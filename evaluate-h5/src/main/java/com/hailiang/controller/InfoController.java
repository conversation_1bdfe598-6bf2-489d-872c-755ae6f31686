package com.hailiang.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.hailiang.annotation.CurrentStaffConvert;
import com.hailiang.annotation.MethodExecuteTime;
import com.hailiang.common.starter.oss.service.HailiangS3;
import com.hailiang.constant.Constant;
import com.hailiang.enums.ModuleNameEnum;
import com.hailiang.enums.SexEnum;
import com.hailiang.enums.StudentInfoTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.job.handle.TaskMsgHandle;
import com.hailiang.model.dto.QueryClassSubjectRelDTO;
import com.hailiang.model.dto.internaldrive.modify.InfoModifyDTO;
import com.hailiang.model.dto.query.GetEvaluateInfoDetailQueryDTO;
import com.hailiang.model.dto.remove.InfoRemoveDTO;
import com.hailiang.model.dto.response.TeachSubjectResponse;
import com.hailiang.model.dto.save.ApprovalHandleDTO;
import com.hailiang.model.dto.save.InfoSaveDTO;
import com.hailiang.model.dto.submit.StudentSubmitDTO;
import com.hailiang.model.entity.SportProjectProvinceGrade;
import com.hailiang.model.entity.TaskPO;
import com.hailiang.model.entity.mongo.Info;
import com.hailiang.model.entity.mongo.SubStudentInfo;
import com.hailiang.model.vo.GetEvaluateInfoDetailVO;
import com.hailiang.model.vo.StudentInfoVO;
import com.hailiang.model.vo.TargetSportProjectConfigVO;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.dto.StudentDTO;
import com.hailiang.service.InfoService;
import com.hailiang.service.SaasSubjectInfoService;
import com.hailiang.service.SportStandardLogicService;
import com.hailiang.service.SubjectEvaluationDimInternalService;
import com.hailiang.service.TargetService;
import com.hailiang.service.TaskOperateLogService;
import com.hailiang.service.TaskService;
import com.hailiang.util.R;
import com.hailiang.util.WebUtil;
import com.yomahub.tlog.core.annotation.TLogAspect;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0
 * 评估任务详情
 * @date 2022/12/26 下午3:08
 */
@RestController
@RequestMapping("/evaluateInfo")
@Slf4j
public class InfoController {

    @Resource
    private SaasSubjectInfoService saasSubjectInfoService;

    @Resource
    private InfoService evaluateInfoService;

    @Resource
    private HailiangS3 hailiangS3;

    @Resource
    private TaskMsgHandle taskMsgHandle;

    @Resource
    private TaskService taskService;

    @Resource
    SubjectEvaluationDimInternalService subjectEvaluationDimInternalService;

    @Resource
    TaskOperateLogService taskOperateLogService;

    @Resource
    private SportStandardLogicService sportStandardLogicService;

    @Resource
    private TargetService targetService;

    @Resource
    private SaasStudentManager saasStudentManager;


    /**
     * 提交评估任务详情(老师端)
     *
     * @param: dto
     * @return: com.hailiang.util.R
     * <AUTHOR>
     * @date: 2022/12/26 下午3:12
     */
    @PostMapping("/save")
    @TLogAspect(convert = CurrentStaffConvert.class)
    public R<Info> saveTeacherEvaluateInfo(@RequestBody InfoSaveDTO dto) {
        log.info("[{}-保存],入参:[{}]", ModuleNameEnum.EVALUATE_TASK.getMessage(), JSONUtil.toJsonStr(dto));
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.start("[评估任务]-[教师端保存]");
        //体测指标校验
        Long targetId = dto.getTargetId();
        Map<Long, Boolean> targetFromSportMap = targetService.checkTargetFromSport(CollUtil.toList(targetId));
        Boolean isSportTarget = targetFromSportMap.get(targetId);
        if (Boolean.TRUE.equals(isSportTarget)) {
            checkSportTarget(targetId, CollUtil.getFirst(dto.getSubmitInfoList()).getSubmitList());
        }

        Info info = evaluateInfoService.saveTeacherValuateInfo(dto, DateUtil.date(), false, isSportTarget);
        if (taskOperateLogService.checkBatchReviewTimesAWeekV2()) {
            info.setMsg("提交成功，为了拉开学生区分度，请尽量减少批量点评哦");
        }

        log.info("[评估任务]-[教师端保存]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]",
                timeInterval.interval("[评估任务]-[教师端保存]"));

        return R.ok(info);
    }


    /**
     * 编辑评估任务详情
     *
     * @param: dto
     * @return: com.hailiang.util.R
     * <AUTHOR>
     * @date: 2022/12/26 下午3:12
     */
    @PostMapping("/update")
    @TLogAspect(convert = CurrentStaffConvert.class)
    public R<Boolean> modifyEvaluateInfo(@RequestBody InfoModifyDTO dto) {
        log.info("[{}-修改],入参:[{}]", ModuleNameEnum.EVALUATE_TASK.getMessage(), JSONUtil.toJsonStr(dto));

        //体测指标校验
        Long targetId = dto.getTargetId();
        Map<Long, Boolean> targetFromSportMap = targetService.checkTargetFromSport(CollUtil.toList(targetId));
        Boolean isSportTarget = targetFromSportMap.get(targetId);
        if (Boolean.TRUE.equals(isSportTarget)) {
            checkSportTarget(targetId, CollUtil.getFirst(dto.getSubmitInfoList()).getSubmitList());
        }

        return R.ok(evaluateInfoService.modifyEvaluateInfo(dto, isSportTarget));
    }

    /**
     * 删除评估任务详情
     *
     * @param: dto
     * @return: com.hailiang.util.R
     * <AUTHOR>
     * @date: 2022/12/26 下午3:12
     */
    @PostMapping("/delete")
    public R<Boolean> removeEvaluateInfo(@RequestBody InfoRemoveDTO dto) {
        log.info("[{}-删除],入参:[{}]", ModuleNameEnum.EVALUATE_TASK.getMessage(), JSONUtil.toJsonStr(dto));
        return R.ok(evaluateInfoService.removeEvaluateInfo(dto));
    }


    /**
     * 提交页面查询填写详情
     *
     * @param: dto
     * @return: com.hailiang.util.R
     * <AUTHOR>
     * @date: 2022/12/26 下午3:12
     */
    @PostMapping("/get")
    @MethodExecuteTime
    public R<GetEvaluateInfoDetailVO> getEvaluateInfoDetail(@RequestBody GetEvaluateInfoDetailQueryDTO dto) {
        return R.ok(evaluateInfoService.getEvaluateInfoDetail(dto));
    }

    /**
     * 获取临时token
     */
    @GetMapping("/ststoken")
    public R ststoken() {
        Map token = hailiangS3.getToken(null);
        return R.ok(token);
    }

    /**
     * 家长提交指标任务(家长端)
     *
     * @param dto dto
     * @return {@link R}<{@link Info}>
     */
    @PostMapping("/parentSave")
    public R<Info> saveParentEvaluateInfo(@RequestBody InfoSaveDTO dto) {
        log.info("[{}-保存],入参:[{}]", ModuleNameEnum.EVALUATE_TASK.getMessage(), JSONUtil.toJsonStr(dto));
        return R.ok(evaluateInfoService.saveParentEvaluateInfo(dto));
    }

    /**
     * 家长重新提交指标任务(家长端)
     *
     * @param dto
     * @return
     */
    @PostMapping("/parentUpdate")
    public R<Boolean> modifyParentEvaluateInfo(@RequestBody InfoModifyDTO dto) {
        return R.ok(evaluateInfoService.modifyParentEvaluateInfo(dto));
    }

    /**
     * 家长删除评估任务详情(家长端)
     *
     * @param dto dto
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/parentDelete")
    public R<Boolean> removeParentEvaluateInfo(@RequestBody InfoRemoveDTO dto) {
        return R.ok(evaluateInfoService.removeParentEvaluateInfo(dto));
    }

    /**
     * 老师审核家长提交的指标(老师端)
     *
     * @param dto dto
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/approvalParentEvaluateInfo")
    public R<Boolean> approvalParentEvaluateInfo(@Valid @RequestBody ApprovalHandleDTO dto) {
        evaluateInfoService.approvalParentEvaluateInfo(dto);
        return R.ok(true);
    }

    /**
     * 根据学生学号和姓名查询学生信息(家长端)
     *
     * @return
     */
    @GetMapping("/getStudentInfoByStudentNoAndName")
    public R<StudentInfoVO> getStudentInfoByStudentNoAndName() {
        return R.ok(evaluateInfoService.getStudentInfoByStudentNoAndName());
    }

    /**
     * 学生提交指标任务(平板端)
     *
     * @param dto dto
     * @return {@link R}<{@link Info}>
     */
    @PostMapping("/studentSave")
    public R<Info> saveStudentEvaluateInfo(@RequestBody InfoSaveDTO dto) {
        log.info("[{}-保存],入参:[{}]", ModuleNameEnum.EVALUATE_TASK.getMessage(), JSONUtil.toJsonStr(dto));
        return R.ok(evaluateInfoService.saveStudentEvaluateInfo(dto));
    }

    /**
     * 老师审核学生提交的指标(老师端)
     *
     * @param dto dto
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/approvalStudentEvaluateInfo")
    public R<Boolean> approvalStudentEvaluateInfo(@Valid @RequestBody ApprovalHandleDTO dto) {
        evaluateInfoService.approvalStudentEvaluateInfo(dto);
        return R.ok(true);
    }

    /**
     * test
     *
     * @return
     */
    @PostMapping("/test")
    public R<Boolean> teacherApprovalHandle() {
        TaskPO taskPO = taskService.getById(1615531720012230657L);
        taskMsgHandle.sendWXMustMsg(taskPO);
        return R.ok();
    }


    //TODO 暂时注释，使用V2版本
//    /**
//     * 获取老师关联学科
//     */
//    @PostMapping("/getRelatedSubject")
//    public R<List<EduStaffTeachClassVO>> getRelatedSubject(@RequestBody @Validated QueryClassSubjectRelDTO dto) {
//        if (CollUtil.isEmpty(dto.getSubmitValue())) return R.ok();
//        return this.getRelSubject(dto);
//    }
//
//
//    private R<List<EduStaffTeachClassVO>> getRelSubject(QueryClassSubjectRelDTO dto) {
//        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
//        eduOrgQueryDTO.setCurrentId(WebUtil.getSchoolIdLong());
//        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SCHOOL.getCode());
//        eduOrgQueryDTO.setEndType(SaasCurrentIdTypeEnum.SECTION.getCode());
//        eduOrgQueryDTO.setIsTree(0);
//
//        Map<Long, EduOrgTreeVO> eduMap = Optional.ofNullable(cacheSaasManager.queryEducationalOrgTree(eduOrgQueryDTO)).orElse(Collections.emptyList())
//                .stream().filter(e -> SaasClassOrgTypeEnum.SECTION.getCode().equals(e.getType())).collect(Collectors.toMap(EduOrgTreeVO::getId, Function.identity()));
//
//        for (SubStudentInfo subStudentInfo : dto.getSubmitValue()) {
//            if (ObjectUtil.isNull(subStudentInfo) || ObjectUtil.isNull(subStudentInfo.getId())) continue;
//            if (!StudentInfoTypeEnum.SECTION.getCode().equals(subStudentInfo.getType())) continue;
//            EduOrgTreeVO eduOrgTreeVO = eduMap.get(Long.valueOf(subStudentInfo.getId()));
//            Assert.notNull(eduOrgTreeVO, () -> new BizException("匹配学段编码异常"));
//            subStudentInfo.setSectionCode(eduOrgTreeVO.getCode());
//        }
//        //过滤校区
//        List<EduStaffTeachClassVO> relatedSubject = evaluateInfoService.getRelatedSubject(dto, WebUtil.getSchoolId(), WebUtil.getStaffId())
//                .stream().filter(e -> WebUtil.getCampusIdLong().equals(e.getCampusId())).collect(Collectors.toList());
//
//        //处理指标和学科关联关系，判断指标是否需要弹窗选择学科
//        subjectEvaluationDimInternalService.fillTargetAndSubject(dto, relatedSubject);
//
//        if (StringUtils.isBlank(dto.getInfoId())) return R.ok(relatedSubject);
//        List<BehaviourRecord> behaviourRecords = behaviourRecordService.getListByInfoId(dto.getInfoId());
//        Map<String, List<BehaviourRecord>> relMap = behaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecord::getClassId));
//
//        for (EduStaffTeachClassVO teachClassVO : relatedSubject) {
//            if (ObjectUtil.isNull(teachClassVO)) continue;
//            if (ObjectUtil.isNull(teachClassVO.getClassId())) continue;
//
//            List<EduStaffSubjectVO> subjects = teachClassVO.getSubjects();
//            if (CollUtil.isEmpty(subjects)) continue;
//
//            List<BehaviourRecord> records = relMap.getOrDefault(teachClassVO.getClassId().toString(), Collections.emptyList());
//            BehaviourRecord behaviourRecord = records.stream().findFirst().orElse(new BehaviourRecord());
//            for (EduStaffSubjectVO subject : subjects) {
//                if (ObjectUtil.isNull(subject) || ObjectUtil.isNull(subject.getSubjectCode())) continue;
//                subject.setIsSelected(subject.getSubjectCode().equals(behaviourRecord.getSubjectCode()));
//            }
//        }
//        return R.ok(relatedSubject);
//    }

    /**
     * 获取老师关联学科
     */
    @PostMapping("/getRelatedSubjectV2")
    public R<TeachSubjectResponse> getRelatedSubjectV2(@RequestBody @Validated QueryClassSubjectRelDTO dto) {
        if (CollUtil.isEmpty(dto.getSubmitValue())) {
            return R.ok();
        }
        return this.getRelSubjectV2(dto);
    }


    private R<TeachSubjectResponse> getRelSubjectV2(QueryClassSubjectRelDTO request) {

        //校验提交选项中学生控件范围是否超过班级
        for (SubStudentInfo subStudentInfo : request.getSubmitValue()) {

            if (ObjectUtil.isNull(subStudentInfo) || ObjectUtil.isNull(subStudentInfo.getId())) {
                continue;
            }

            if (!StudentInfoTypeEnum.isClassScope(subStudentInfo.getType())) {
                log.error("获取对应科目选择时，提交选项中学生控件范围超过班级，subStudentInfo：{}，request：{}", subStudentInfo,request);
                throw new BizException("获取对应科目选择时，提交选项中学生控件范围超过班级");
            }
        }

        TeachSubjectResponse teachSubjectResponse = saasSubjectInfoService.getRelatedSubjectV2(request, WebUtil.getStaffId());

        //处理指标和学科关联关系，判断指标是否需要弹窗选择学科
        subjectEvaluationDimInternalService.fillTargetAndSubjectV2(request, teachSubjectResponse);

        return R.ok(teachSubjectResponse);
    }

    private void checkSportTarget(Long targetId,List<List<LinkedHashMap>> submitList) {

        Assert.notEmpty(submitList, "提交表单内容不能为空！");
        List<StudentSubmitDTO> studentSubmitDTOList = parseSubmitInfo(submitList);
        Assert.notEmpty(studentSubmitDTOList, "学生不能为空！");
        //校验学生是否重复
        List<Long> distinctStudentList = studentSubmitDTOList
                .stream()
                .map(StudentSubmitDTO::getId).distinct().collect(Collectors.toList());

        Assert.isTrue(distinctStudentList.size() == studentSubmitDTOList.size(), "学生不能重复！");

        for (StudentSubmitDTO studentSubmitDTO : studentSubmitDTOList) {
            Integer studentSportDataSex = sportStandardLogicService.getStudentSportDataSex(studentSubmitDTO.getId());
            if (ObjectUtil.isNull(studentSportDataSex)) {
                continue;
            }
            String sex = studentSubmitDTO.getSex();
            String name = studentSubmitDTO.getName();
            String studentNo = studentSubmitDTO.getStudentNo();
            Assert.isTrue(studentSportDataSex.equals(SexEnum.getCodeByMessage(sex)),
                    String.format("%s(%s)", name, studentNo) + "当前所选性别与体测档案不一致！");
        }

        //校验项目是否包含学生所在年级、性别
        List<TargetSportProjectConfigVO> sportProjectRules = sportStandardLogicService.getSportProjectRule(targetId);
        if (CollUtil.isEmpty(sportProjectRules)){
            return;
        }

        TargetSportProjectConfigVO sportProjectRule = CollUtil.getFirst(sportProjectRules);
        List<SportProjectProvinceGrade> gradeSexList = sportProjectRule.getGradeSexList();
        if (CollUtil.isEmpty(gradeSexList)){
            return;
        }

        //根据班级 id 获取学生
        StudentDTO studentDTO = new StudentDTO();
        studentDTO.setStudentIds(distinctStudentList);
        List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOS = saasStudentManager.studentDetail(studentDTO);
        Assert.notEmpty(studentInfoVOS,"学生不存在！");

        studentInfoVOS = studentInfoVOS.stream()
                .filter(s -> Constant.ZERO.equals(Convert.toInt(s.getUpgradeStatus()))
                        && Constant.ZERO.equals(Convert.toInt(s.getStudentStatus())))
                .collect(Collectors.toList());
        Assert.notEmpty(studentInfoVOS,"学生不存在！");

        Map<Long, StudentSubmitDTO> studentSubmitDTOMap = studentSubmitDTOList
                .stream()
                .collect(Collectors.toMap(StudentSubmitDTO::getId, Function.identity()));

        Map<Long, com.hailiang.saas.model.vo.StudentInfoVO> studentMap = studentInfoVOS
                .stream()
                .collect(Collectors.toMap(com.hailiang.saas.model.vo.StudentInfoVO::getStudentId, Function.identity(), (v1, v2) -> v1));


        for (Entry<Long, com.hailiang.saas.model.vo.StudentInfoVO> entry : studentMap.entrySet()) {
            com.hailiang.saas.model.vo.StudentInfoVO studentInfo = entry.getValue();
            String studentName = studentInfo.getStudentName();
            String studentNo = studentInfo.getStudentNo();

            StudentSubmitDTO studentSubmitDTO = studentSubmitDTOMap.get(studentInfo.getStudentId());

            SportProjectProvinceGrade sportProjectProvinceGrade = gradeSexList
                    .stream()
                    .filter(item ->
                            item.getGradeCode().equals(studentInfo.getGradeCode())
                            && item.getSex().equals(SexEnum.getCodeByMessage(studentSubmitDTO.getSex())))
                    .findFirst().orElse(null);

            Assert.notNull(sportProjectProvinceGrade, String.format("%s(%s)所在年级，不支持该体测项目！",studentName,studentNo));
        }
    }
    private List<StudentSubmitDTO> parseSubmitInfo(List<List<LinkedHashMap>> submitList) {
        List<StudentSubmitDTO> studentSubmitDTOList = Lists.newArrayList();

        for (List<LinkedHashMap> linkedHashMaps : submitList) {
            StudentSubmitDTO studentSportTargetDTO = new StudentSubmitDTO();

            for (LinkedHashMap linkedHashMap : linkedHashMaps) {
                String type = (String) linkedHashMap.get("type");
                String name = (String) linkedHashMap.get("name");
                Object submitValueObj = linkedHashMap.get("submitValue");

                //解析学生id
                if ("student".equals(type)) {
                    studentSportTargetDTO = CollUtil.getFirst(JSONUtil.parseArray(submitValueObj)
                            .toList(StudentSubmitDTO.class));
                }
                //解析性别
                if ("radio".equals(type) && "性别".equals(name)) {
                    studentSportTargetDTO.setSex(JSONUtil.parseObj(submitValueObj).getStr("label"));
                }
            }
            studentSubmitDTOList.add(studentSportTargetDTO);
        }

        return studentSubmitDTOList
                .stream()
                .filter(Objects::nonNull).collect(Collectors.toList());
    }


}
