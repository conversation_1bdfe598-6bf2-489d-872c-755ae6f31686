package com.hailiang.controller.speed;

import cn.hutool.core.collection.CollUtil;
import com.hailiang.annotation.CurrentStaffConvert;
import com.hailiang.annotation.MethodExecuteLog;
import com.hailiang.annotation.MethodExecuteTime;
import com.hailiang.constant.Constant;
import com.hailiang.internal.model.request.EvaluateUsualWordRemainRequest;
import com.hailiang.internal.model.request.EvaluateUsualWordRequest;
import com.hailiang.internal.model.request.MessagePlanRequest;
import com.hailiang.internal.model.request.PlanInitialRequest;
import com.hailiang.internal.model.request.PlanListsRequest;
import com.hailiang.internal.model.request.PlanTagListsRequest;
import com.hailiang.internal.model.request.UniqueKeyListRequest;
import com.hailiang.internal.model.response.EvaluateUsualWordResponse;
import com.hailiang.internal.model.response.PlanListsResponse;
import com.hailiang.model.dto.remove.RemoveSpeedRequest;
import com.hailiang.model.dto.request.IdListRequest;
import com.hailiang.model.dto.save.BehaviourRecordHandleDTO;
import com.hailiang.model.dto.save.InfoSpeedRequest;
import com.hailiang.model.response.speed.SpeedGroupDetailResponse;
import com.hailiang.model.response.speed.SpeedSaveResponse;
import com.hailiang.model.vo.GetEvaluateTaskDetailVO;
import com.hailiang.saas.model.dto.FaceDetectRequest;
import com.hailiang.saas.model.vo.StudentInfoVO;
import com.hailiang.service.EvaluateUsualService;
import com.hailiang.service.InfoService;
import com.hailiang.service.SpeedInfoEditService;
import com.hailiang.service.SpeedInfoShowService;
import com.hailiang.service.TaskOperateLogService;
import com.hailiang.util.R;
import com.yomahub.tlog.core.annotation.TLogAspect;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 极速点评
 *
 * @Description: 极速点评
 * @Author: JiangJunLi
 * @Date: Created in 2024-01-26
 * @Version: 1.6.0
 */
@RestController
@RequestMapping("/evaluateSpeedInfo")
public class SpeedInfoController {

    @Resource
    private SpeedInfoEditService speedInfoEditService;

    @Resource
    private SpeedInfoShowService speedInfoShowService;

    @Resource
    private TaskOperateLogService taskOperateLogService;

    @Resource
    private EvaluateUsualService evaluateUsualService;

    @Resource
    private InfoService evaluateInfoService;

    /**
     * 保存极速点评
     */
    @PostMapping("/saveSpeed")
    @TLogAspect(convert = CurrentStaffConvert.class)
    @MethodExecuteLog
    public R<SpeedSaveResponse> speedSaveTeacherEvaluateInfo(@RequestBody @Valid InfoSpeedRequest request) {
        BehaviourRecordHandleDTO behaviourRecordHandleDTO = speedInfoEditService.speedSaveTeacherEvaluateInfoV2(request);
        // 新增行为记录处理争章活动
        evaluateInfoService.handleActivity(behaviourRecordHandleDTO);

        SpeedSaveResponse  speedSaveResponse = new SpeedSaveResponse();
        //是否存在被限制的学生
        if (CollUtil.isNotEmpty(behaviourRecordHandleDTO.getRestrictedStudentList())){
            speedSaveResponse.setOperatedStatus(Constant.TWO);
            speedSaveResponse.setErrorStudents(behaviourRecordHandleDTO.getRestrictedStudentList());
            return R.ok(speedSaveResponse);
        }

        if (taskOperateLogService.checkBatchReviewTimesAWeekV2()) {
            speedSaveResponse.setOperatedStatus(Constant.THREE);
            return R.ok(speedSaveResponse);
        }
        return R.ok(speedSaveResponse);
    }

    /**
     * 极速点评分组及详情
     */
    @PostMapping("/listSpeedTargetGroupDetail")
    public R<List<SpeedGroupDetailResponse>> listSpeedTargetGroupDetail(@RequestBody @Valid PlanTagListsRequest
                                                                                request) {
        return R.ok(speedInfoShowService.listSpeedTargetGroupDetail(request));
    }

    /**
     * 人脸自动识别
     */
    @PostMapping("/faceSearch")
    @MethodExecuteTime
    public R<List<StudentInfoVO>> faceSearch(@RequestBody @Valid FaceDetectRequest request) {
        List<StudentInfoVO> studentInfoVOS = speedInfoEditService.faceSearch(request);
        if (CollUtil.isEmpty(studentInfoVOS)) {
            return R.failed(30001, "人脸识别失败，请重新识别");
        }
        return R.ok(studentInfoVOS);
    }


    /**
     * 积分版列表
     */
    @PostMapping("/listPlans")
    public R<List<PlanListsResponse>> listPlans(@RequestBody @Valid PlanListsRequest request) {
        return R.ok(speedInfoShowService.listPlans(request));
    }

    /**
     * 删除极速点评
     */
    @PostMapping("/delete")
    public R<Boolean> deleteSpeedByIds(@RequestBody @Valid RemoveSpeedRequest removeSpeedRequest) {
        speedInfoEditService.deleteSpeedByIds(removeSpeedRequest);
        return R.ok();
    }

    /**
     * 查询积分板详情
     * @param request
     * @return
     */
    @PostMapping("/listPlansByCampusId")
    public R<List<PlanListsResponse>> listPlansByCampusId(@RequestBody @Valid MessagePlanRequest request){
        return R.ok(speedInfoShowService.listPlansByCampusId(request));
    }

    /**
     * 初始化积分板
     *
     * @param planListsRequest
     * @return
     */
    @PostMapping("/initialPlan")
    public R<PlanListsResponse> initialPlan(@RequestBody @Valid PlanInitialRequest planListsRequest){
        return R.ok(speedInfoShowService.initialPlan(planListsRequest));
    }

    /**
     * 通过targetIds查询必填textarea
     *
     * @param req targetIds集合
     * @return
     */
    @PostMapping("/textareaList")
    public R<List<GetEvaluateTaskDetailVO>> textareaList(@RequestBody @Valid IdListRequest req){
        return R.ok(speedInfoShowService.textareaList(req));
    }


    /**
     * 保存常用语
     *
     * @param req
     * @return
     */
    @PostMapping("/usualSave")
    public R<List<EvaluateUsualWordResponse>> usualSave(@RequestBody @Valid EvaluateUsualWordRequest req){
        return R.ok(evaluateUsualService.usualSave(req));
    }



    /**
     * 保留常用语
     *
     * @param req 保留的id集合
     * @return
     */
    @PostMapping("/usualRemain")
    public R<List<EvaluateUsualWordResponse>> usualRemain(@RequestBody @Valid EvaluateUsualWordRemainRequest req){
        return R.ok(evaluateUsualService.usualRemain(req));
    }




    /**
     * 常用语查询
     *
     * @param req
     * @return
     */
    @PostMapping("/usualList")
    public R<Map<String, List<EvaluateUsualWordResponse>>> usualList(@RequestBody @Valid UniqueKeyListRequest req) {
        return R.ok(evaluateUsualService.usualList(req.getKeyList()));
    }

}
