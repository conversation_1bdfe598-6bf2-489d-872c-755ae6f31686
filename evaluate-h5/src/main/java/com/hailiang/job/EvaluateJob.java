package com.hailiang.job;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hailiang.agg.AggBizTypeEnum;
import com.hailiang.agg.AggDataHandler;
import com.hailiang.annotation.MethodExecuteTime;
import com.hailiang.job.handle.*;
import com.hailiang.model.dto.EvaluateAggDTO;
import com.hailiang.model.dto.JobEightHalfClockDTO;
import com.hailiang.model.dto.JobIssueActivityMedalDTO;
import com.hailiang.model.dto.JobTwoClockDTO;
import com.hailiang.service.PointChangeDetailService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/6 18:57
 */
@Component
@RequiredArgsConstructor
@Slf4j
@RestController
public class EvaluateJob {

    private final MedalHandle medalHandle;
    private final TaskMsgHandle taskMsgHandle;
    private final TaskHandle evaluateTaskHandle;
    private final CheckInfoHandle checkInfoHandle;
    private final TenMinuteHandle tenMinuteHandle;
    private final ReportRuleHandle reportRuleHandle;
    private final InitialScoreHandle initialScoreHandle;
    private final CabinetUpdateHandle cabinetUpdateHandle;
    private final SubjectEvaluationDimArchiveHandle subjectEvaluationDimArchiveHandle;
    private final ReportCompensatorHandle reportCompensatorHandle;
    private final PointChangeDetailService pointChangeDetailService;
    private final ReportCalculateCleanHandle reportCalculateCleanHandle;
    private final SpeedEvaluateReminderHandle speedEvaluateReminderHandle;
    private final ReportBehaviourCalculatedHandle behaviourCalculatedHandle;
    @Resource
    private DataStatisticsHandle dataStatisticsHandle;
    @Resource
    private GenerateReportDataHandler generateReportDataHandler;
    @Resource
    private DailyCommentPushHandle dailyCommentPushHandle;
    private final AggDataHandler aggDataHandler;
    private final RevaluateHistoryDataHandle revaluateHistoryDataHandle;
    @Resource
    private ThreadPoolTaskExecutor evaluateExecutor;
    private final StudentModelCalculateHandle studentModelCalculateHandle;
    private final ReportDataCompensatorHandle reportDataCompensatorHandle;
    private final TranscodeStatusHandle transcodeStatusHandle;
    @Resource
    private ScoreContinuationHandle scoreContinuationHandle;
    @Resource
    private DingdingDailyNoticeHandle dingdingDailyNoticeHandle;

    /**
     * @description: 01:00:00跑
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2022-04-21 10:21
     */
    @XxlJob("oneClockHandler")
    public void oneClock() {
        Date now = DateUtil.date();
        // 生成指标任务
        evaluateTaskHandle.createTasks(now);
    }

    /**
     * @description: 02:00:00跑
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2022-04-21 10:21
     */
    @XxlJob("twoClockHandler")
    public void twoClock() {
        String campusId = null;
        String beginDate = null;
        Boolean isForce = false;
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isNotBlank(param)) {
            List paramList = StrUtil.split(param, StrPool.COMMA);
            campusId = paramList.get(0).toString();
            beginDate = paramList.get(1).toString();
            if (paramList.size() == 3){
                isForce = Convert.toBool(paramList.get(2));
            }
        }
        // 学期积分清零
        pointChangeDetailService.termResetPoint(campusId, beginDate, isForce);
    }

    /**
     * @description: 02:00:00跑
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2022-04-21 10:21
     */
    @XxlJob("termResetPoint1")
    public void termResetPoint1() {
        String campusId = null;
        String beginDate = null;
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isNotBlank(param)) {
            List paramList = StrUtil.split(param, ":");
            campusId = paramList.get(0).toString();
            beginDate = paramList.get(1).toString();
        }
        // 学期积分清零
        if (ObjectUtil.hasEmpty(campusId, beginDate)) {
            log.info("【学期积分清零】-campusId或beginDate为空，直接返回");
            return;
        }
        for (String campus : campusId.split(",")) {
            pointChangeDetailService.termResetPoint1(campus, beginDate);
        }
    }

    /**
     * @description: 发送点评消息
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2022-04-21 10:21
     */
    @XxlJob("evaluateMessageHandler")
    @MethodExecuteTime
    public void evaluateMessage() {
        Date now = DateUtil.date();
        DateTime nowMin = DateUtil.beginOfMinute(now);
        DateTime beginOfDay = DateUtil.beginOfDay(now);
        // 【提醒消息-必填评价任务】
        taskMsgHandle.pushRemindMsgForMust(nowMin, beginOfDay);
        // 【提醒消息-非必填评价任务】
        taskMsgHandle.pushRemindMsgForNotMust(nowMin);
        // 【催办消息】
        taskMsgHandle.pushUrgeMsg(nowMin);
    }

    /**
     * 学生画像报告推送
     */
    @XxlJob("reportRuleHandler")
    @MethodExecuteTime
    public void reportRule() {
        Date now = DateUtil.date();
        DateTime nowMin = DateUtil.beginOfMinute(now);
        // 【报告消息-班主任审核提醒】 此任务执行时间较长 新起线程 避免定时任务阻塞
        evaluateExecutor.execute(() ->  reportRuleHandle.pushRemindToClassMaster(nowMin));
    }

    /**
     * 争章活动状态修改
     */
    @XxlJob("medalHandle")
    @MethodExecuteTime
    public void medalChangeStatus() {
        Date now = DateUtil.date();
        DateTime nowMin = DateUtil.beginOfMinute(now);
        //【争章活动状态转换】
        medalHandle.changeMedalActivityStatus(nowMin);
    }

    /**
     * 每日推送
     */
    @XxlJob("dailyCommentPushHandle")
    @MethodExecuteTime
    public void dailyCommentPush() {
        Date now = DateUtil.date();
        // 【每日点评推送】
        dailyCommentPushHandle.pushDailyComment(now);
    }
    /**
     * 凌晨两点,统计需要发送的数据
     */
    @XxlJob("collectKanbanData")
    public void collectKanbanData() {
        JobTwoClockDTO dto = new JobTwoClockDTO();
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isNotBlank(param)) {
            String[] methodParams = param.split(",");
            dto.setCampusId("null".equals(methodParams[0]) ? null : methodParams[0]);
            dto.setStartTime("null".equals(methodParams[1]) ? null : new DateTime(methodParams[1]));
            dto.setEndTime("null".equals(methodParams[2]) ? null : new DateTime(methodParams[2]));

            List<Long> ids = new ArrayList<>();
            for (int i = 3; i < methodParams.length; i++) {
                ids.add(Convert.toLong(methodParams[i]));

            }
            dto.setIds(ids);
        }
        dataStatisticsHandle.calculatedData(dto);
    }

    /**
     * 早上8:30 发送钉钉通知
     */
    @XxlJob("sendKanbanData")
    public void sendKanbanData() {
        JobEightHalfClockDTO dto = new JobEightHalfClockDTO();
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isNotBlank(param)) {
            String[] methodParams = param.split(",");
            dto.setCampusId("null".equals(methodParams[0]) ? null : methodParams[0]);
            dto.setStartTime("null".equals(methodParams[1]) ? null : new DateTime(methodParams[1]));
            dto.setEndTime("null".equals(methodParams[2]) ? null : new DateTime(methodParams[2]));

            List<Long> ids = new ArrayList<>();
            for (int i = 3; i < methodParams.length; i++) {
                ids.add(Convert.toLong(methodParams[i]));

            }
            dto.setIds(ids);
        }
        dataStatisticsHandle.sendInformMsg(dto);
    }

    /**
     * 早上8点 颁发活动奖章
     */
    @XxlJob("issueActivityMedal")
    public void issueActivityMedal() {
        JobIssueActivityMedalDTO jobIssueActivityMedalDTO = new JobIssueActivityMedalDTO();
        // 校区id
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isNotBlank(param)) {
            String[] methodParams = param.split(",");
            jobIssueActivityMedalDTO.setCampusId("null".equals(methodParams[0]) ? null : methodParams[0]);
            jobIssueActivityMedalDTO.setIssueDate("null".equals(methodParams[1]) ? null : new DateTime(methodParams[1]));
            log.info("[争章活动]-[每日早上8点]-[奖章颁发]-[指定校区]-校区id:{}-[指定颁发日期]-日期:{}", jobIssueActivityMedalDTO.getCampusId(), jobIssueActivityMedalDTO.getIssueDate());
        }
        medalHandle.issueActivityMedal(jobIssueActivityMedalDTO);
    }

    /**
     * 十分钟执行(暂时不执行，后续考虑)
     */
    @XxlJob("tenMinuteHandle")
    public void tenMinuteHandle() {
        tenMinuteHandle.handlePointChangeLog();
    }

    /**
     * 积分柜升级（02:10）
     */
    @XxlJob("cabinetUpdateHandle")
    public void cabinetUpdateHandle() {
        String version = XxlJobHelper.getJobParam();
        if (StrUtil.isBlank(version)) {
            log.info("[积分柜]-[升级]-无参数，直接返回");
            return;
        }
        cabinetUpdateHandle.checkUpdate(version);
    }

    /**
     * 音频文件推送（02:20）
     */
    @XxlJob("cabinetVideoHandle")
    public void cabinetVideoHandle() {
        cabinetUpdateHandle.videoPush();
    }

    /**
     * 每小时执行
     */
    @XxlJob("oneHourHandle")
    public void oneHourHandle() {
        cabinetUpdateHandle.checkCabinetInfoNotice();

        //重试每日点评推送失败任务
        dailyCommentPushHandle.retryPushDailyComment();
    }


    /**
     * 综合成绩配置归档
     * 每天晚上3点执行
     * CRON：00 0 3 * * ?
     */
    @XxlJob("reportArchiveHandle")
    public void reportArchiveHandle() {
        String param = XxlJobHelper.getJobParam();
        log.info("[综合成绩-归档任务触发-校区:{}]", param);
        subjectEvaluationDimArchiveHandle.archive(param);
    }

    /**
     * 点评记录变更触发计算 30分钟
     */
    @XxlJob("reportBehaviourCalculatedHandle")
    public void reportBehaviourCalculatedHandle() {
        String param = XxlJobHelper.getJobParam();
        behaviourCalculatedHandle.behaviourCalculated(param);
    }

    /**
     * 计算结果清除任务
     * param 保留天数
     */
    @XxlJob("reportCalculateCleanHandle")
    public void reportCalculateCleanHandle() {
        String param = XxlJobHelper.getJobParam();
        reportCalculateCleanHandle.clean(Integer.parseInt(param));
    }

    /**
     * 计算补偿器 手动执行
     */
    @XxlJob("reportCompensatorHandle")
    public void reportCompensatorHandle() {
        String param = XxlJobHelper.getJobParam();
        log.info("【综合成绩】-【补偿任务】，入参：{}", param);
        if (StrUtil.isBlank(param)) return;

        String[] methodParams = param.split(",");
        String sectionId = "null".equals(methodParams[0]) ? null : methodParams[0];
        String scopeId = "null".equals(methodParams[1]) ? null : methodParams[1];
        if (StringUtils.isBlank(sectionId)) return;
        reportCompensatorHandle.compensate(sectionId, scopeId);
    }

    /**
     * 新学期初始化分
     * 每天晚上3点执行
     * CRON：00 0 3 * * ?
     */
    @XxlJob("initialScore")
    public void initialScore() {
        log.info("定时任务-每天3点定时任务轮训是否新学期分配初始分");
        initialScoreHandle.initScoreV2();
    }

    /**
     * 每天9.30推送值日检查数据：录入、撤回
     */
    @XxlJob("checkInfoHandle")
    public void checkInfoHandle() {
        String campusId = null, startTime = null, endTime = null;
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isNotBlank(param)) {
            String[] methodParams = param.split(",");
            campusId = "null".equals(methodParams[0]) ? null : methodParams[0];
            startTime = "null".equals(methodParams[1]) ? null : methodParams[1];
            endTime = "null".equals(methodParams[2]) ? null : methodParams[2];
        }

        log.info("定时任务-推送值日检查数据,执行参数:{}", param);
        checkInfoHandle.dutyNotice(campusId, startTime, endTime);
    }

    /**
     * 生成报告单处理器（每天凌晨1点执行一次）
     */
    @XxlJob("generateReportDataHandler")
    public void generateReportDataHandler() {
        String campusId = XxlJobHelper.getJobParam();
        log.info("生成报告单处理器,执行参数:{}", campusId);
        // 生成报告单
        generateReportDataHandler.generateReportData(new Date(), campusId);
    }

    /**
     * 补偿报告单处理器（每天凌晨6点执行一次）
     */
    @XxlJob("remedyGenerateReportDataHandler")
    public void remedyGenerateReportDataHandler() {
        String campusId = XxlJobHelper.getJobParam();
        log.info("补偿报告单处理器,执行参数:{}", campusId);
        // 生成报告单
        generateReportDataHandler.remedyGenerateReportData(new Date(), campusId);
    }

    /**
     * 推送极速点评提醒通知（每分钟执行）
     */
    @XxlJob("speedReminderNotify")
    public void speedReminderNotify() {
        speedEvaluateReminderHandle.reminderNotify();
    }

    /**
     * 学生评价明细聚合任务
     *  param:日期 或者 日期,校区id
     *  eg:"2024-08-25"  或者 "2024-08-25,7225050992433508352"
     */
    @XxlJob("stuBehaviourStatics")
    public void stuBehaviourStatics() {
        String param = XxlJobHelper.getJobParam();
        // 构造参数
        EvaluateAggDTO.EvaluateAggDTOBuilder builder = EvaluateAggDTO.builder();
        // 默认昨天
        Date day = DateUtil.offset(new Date(), DateField.DAY_OF_MONTH, -1);
        log.info("【学生评价聚合任务】,执行参数:{}", param);
        // 参数不为空,说明是手动执行定时任务
        if (StrUtil.isNotBlank(param)) {
            List<String> params = StrUtil.split(param, ",");
            if (params.size() == 1){
                day = DateUtil.parseDate(StrUtil.trim(params.get(0)));
            }
            if (params.size() == 2){
                day = DateUtil.parseDate(StrUtil.trim(params.get(0)));
                builder.campusId(StrUtil.trim(params.get(1)));
            }
            builder.retry(true);
        }
        builder.daily(day);
        builder.dailyStr(DateUtil.formatDate(day));
        EvaluateAggDTO aggDTO = builder.build();

        aggDataHandler.getService(AggBizTypeEnum.BEHAVIOUR_STU).doExecute(aggDTO, null);
    }


    /**
     * 学生评价明细分类聚合任务
     *  param:日期 或者 日期,校区id
     *  eg:"2024-08-25"  或者 "2024-08-25,7225050992433508352"
     */
    @XxlJob("stuBehaviourClassifyStatics")
    public void stuBehaviourClassifyStatics() {
        String param = XxlJobHelper.getJobParam();
        // 构造参数
        EvaluateAggDTO.EvaluateAggDTOBuilder builder = EvaluateAggDTO.builder();
        // 默认昨天
        Date day = DateUtil.offset(new Date(), DateField.DAY_OF_MONTH, -1);
        log.info("【学生评价分类统计聚合任务】,执行参数:{}", param);
        // 参数不为空,说明是手动执行定时任务
        if (StrUtil.isNotBlank(param)) {
            List<String> params = StrUtil.split(param, ",");
            if (params.size() == 1){
                day = DateUtil.parseDate(StrUtil.trim(params.get(0)));
            }
            if (params.size() == 2){
                day = DateUtil.parseDate(StrUtil.trim(params.get(0)));
                builder.campusId(StrUtil.trim(params.get(1)));
            }
            builder.retry(true);
        }
        builder.daily(day);
        builder.dailyStr(DateUtil.formatDate(day));
        EvaluateAggDTO aggDTO = builder.build();

        aggDataHandler.getService(AggBizTypeEnum.BEHAVIOUR_CLASSIFY_STU).doExecute(aggDTO, null);
    }

    /**
     * 教师评价明细聚合任务
     * param:日期 或者 日期,校区id
     * eg:"2024-08-25"  或者 "2024-08-25,7225050992433508352"
     */
    @XxlJob("staffBehaviourStatics")
    public void staffBehaviourStatics() {
        String param = XxlJobHelper.getJobParam();
        // 构造参数
        EvaluateAggDTO.EvaluateAggDTOBuilder builder = EvaluateAggDTO.builder();
        Date day = DateUtil.offset(new Date(), DateField.DAY_OF_MONTH, -1);
        log.info("【教师评价聚合任务】,执行参数:{}", param);
        // 参数不为空,说明是手动执行定时任务
        if (StrUtil.isNotBlank(param)) {
            List<String> params = StrUtil.split(param, ",");
            if (params.size() == 1){
                day = DateUtil.parseDate(StrUtil.trim(params.get(0)));
            }
            if (params.size() == 2){
                day = DateUtil.parseDate(StrUtil.trim(params.get(0)));
                builder.campusId(StrUtil.trim(params.get(1)));
            }
            builder.retry(true);
        }
        builder.daily(day);
        builder.dailyStr(DateUtil.formatDate(day));
        EvaluateAggDTO aggDTO = builder.build();

        aggDataHandler.getService(AggBizTypeEnum.BEHAVIOUR_STAFF).doExecute(aggDTO, null);
    }

    /**
     * 历史数据变更触发重新计算定时任务
     */
    @XxlJob("revaluateHistoryData")
    public void revaluateHistoryData() {
        revaluateHistoryDataHandle.revaluate();
    }

    /**
     * @description: 01:00:00跑
     * @param: 可以手动传参 校区id,是否是测试,是否打印调试日志
     * @return: void
     * <AUTHOR>
     * @date: 2024-12-04 10:21
     */
    @XxlJob("studentModelCalculate")
    public void studentModelCalculate() {
        studentModelCalculateHandle.calculate();
    }
    /**
     * @description: 每10分钟跑一次
     * @param: 报告单补偿
     * @return: void
     * @date: 2024-12-23 14:06
     */
    @XxlJob("reportDataCompensator")
    public void reportDataCompensator() {
        reportDataCompensatorHandle.compensator();
    }

    /**
     * 每30S同步一次视频转码状态
     */
    @XxlJob("syncTranscodeStatusHandler")
    public void syncTranscodeStatus(){
        transcodeStatusHandle.syncTranscodeStatus();
    }

    /**
     * 新学期指标扣分延续,每天早上6点执行
     */
    @XxlJob("scoreContinueHandler")
    public void scoreContinueHandler() {
        String campusId = XxlJobHelper.getJobParam();
        log.info("【定时任务】-【指标扣分延续】-【传参执行:{}】", campusId);
        scoreContinuationHandle.scoreContinuation(campusId);
    }
    /**
     * 钉钉每日推送
     * 每小时轮训
     */
    @XxlJob("dingdingDailyNotice")
    public void dingdingDailyNoticeHandler() {
        log.info("【定时任务】-【钉钉每日点评推送】-定时抓取点评数据");
        dingdingDailyNoticeHandle.push();
    }
}
