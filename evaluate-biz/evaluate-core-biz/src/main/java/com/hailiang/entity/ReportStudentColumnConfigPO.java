package com.hailiang.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 报告单学生栏目配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Getter
@Setter
@TableName("report_student_column_config")
public class ReportStudentColumnConfigPO extends BaseEntity {

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 学校id
     */
    @TableField("school_id")
    private String schoolId;

    /**
     * 校区id
     */
    @TableField("campus_id")
    private String campusId;

    /**
     * 报告编号
     */
    @TableField("report_data_number")
    private Long reportDataNumber;

    /**
     * 学生id
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 栏目code
     */
    @TableField("column_code")
    private String columnCode;

    /**
     * 配置详情 json字符串,每个栏目的配置结构字段信息可能不同
     */
    @TableField("column_content")
    private String columnContent;


    public static final String TENANT_ID = "tenant_id";

    public static final String SCHOOL_ID = "school_id";

    public static final String CAMPUS_ID = "campus_id";

    public static final String REPORT_DATA_NUMBER = "report_data_number";

    public static final String STUDENT_ID = "student_id";

    public static final String COLUMN_CODE = "column_code";

    public static final String COLUMN_CONTENT = "column_content";

}
