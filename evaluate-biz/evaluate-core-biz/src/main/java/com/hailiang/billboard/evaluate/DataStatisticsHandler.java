package com.hailiang.billboard.evaluate;

import static com.hailiang.constant.DataStatisticsConstant.CLASS_CHILDREN_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.CLASS_EVALUATE_CHILDREN_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.CLASS_EVALUATE_SUGGESTION;
import static com.hailiang.constant.DataStatisticsConstant.CLASS_EVALUATE_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.CLASS_PARTICIPATION_SUGGESTION;
import static com.hailiang.constant.DataStatisticsConstant.CLASS_STUDENT_EVALUATE_CHILDREN_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.CLASS_STUDENT_EVALUATE_SUGGESTION;
import static com.hailiang.constant.DataStatisticsConstant.CLASS_STUDENT_EVALUATE_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.CLASS_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.GRADE_CHILDREN_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.GRADE_EVALUATE_CHILDREN_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.GRADE_EVALUATE_SUGGESTION;
import static com.hailiang.constant.DataStatisticsConstant.GRADE_EVALUATE_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.GRADE_PARTICIPATION_SUGGESTION;
import static com.hailiang.constant.DataStatisticsConstant.GRADE_STUDENT_EVALUATE_CHILDREN_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.GRADE_STUDENT_EVALUATE_SUGGESTION;
import static com.hailiang.constant.DataStatisticsConstant.GRADE_STUDENT_EVALUATE_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.GRADE_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.SCHOOL_CHILDREN_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.SCHOOL_EVALUATE_CHILDREN_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.SCHOOL_EVALUATE_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.SCHOOL_STUDENT_EVALUATE_CHILDREN_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.SCHOOL_STUDENT_EVALUATE_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.SCHOOL_TITLE;
import static com.hailiang.constant.DataStatisticsConstant.TEACHER_EXCEL_NAME;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.constant.Constant;
import com.hailiang.convert.BasicConvert;
import com.hailiang.convert.TargetConvert;
import com.hailiang.enums.FrequencyTypeEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.SaasClassOrgTypeEnum;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.exception.BaseErrorCode;
import com.hailiang.exception.BizException;
import com.hailiang.logic.DataStatisticsLogic;
import com.hailiang.manager.BehaviourHandleManager;
import com.hailiang.manager.DorisBehaviourRecordManager;
import com.hailiang.manager.StaffDailyStatisticsManager;
import com.hailiang.manager.StudentDailyStatisticsManager;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.datastatistics.dto.LastSameTimeEvaluateVO;
import com.hailiang.model.datastatistics.dto.SaasCurrentInfoDTO;
import com.hailiang.model.datastatistics.dto.StaffDailyStatisticsVO;
import com.hailiang.model.datastatistics.dto.StudentDailyStatisticsDTO;
import com.hailiang.model.datastatistics.dto.TargetDTO;
import com.hailiang.model.datastatistics.query.DataStatisticsQuery;
import com.hailiang.model.datastatistics.query.TeacherEvaluateDataStatisticsQuery;
import com.hailiang.model.datastatistics.vo.CheckPermissionVo;
import com.hailiang.model.datastatistics.vo.DataStatisticsEvaluateNumVO;
import com.hailiang.model.datastatistics.vo.DataStatisticsStudentEvaluateRateVO;
import com.hailiang.model.datastatistics.vo.DataStatisticsTeacherParticipationRateVO;
import com.hailiang.model.datastatistics.vo.ExcelTargetRankVO;
import com.hailiang.model.datastatistics.vo.FiveEducationDetailVO;
import com.hailiang.model.datastatistics.vo.FiveEducationDistributionVO;
import com.hailiang.model.datastatistics.vo.FiveEducationVO;
import com.hailiang.model.datastatistics.vo.NameAndValueVO;
import com.hailiang.model.datastatistics.vo.PraiseImproveDetailVO;
import com.hailiang.model.datastatistics.vo.PraiseImproveVO;
import com.hailiang.model.datastatistics.vo.TargetCoverageVO;
import com.hailiang.model.datastatistics.vo.TargetFrequencyVO;
import com.hailiang.model.datastatistics.vo.TargetRankVO;
import com.hailiang.model.datastatistics.vo.TeacherEvaluateDataStatisticsVO;
import com.hailiang.model.datastatistics.vo.TeacherVO;
import com.hailiang.model.datastatistics.vo.excel.TeacherEvaluateExcel;
import com.hailiang.model.datastatistics.vo.excel.TeacherEvaluateSheet1;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.staff.StaffSectionOrGradeQueryDTO;
import com.hailiang.remote.saas.vo.educational.EduAuthVO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.remote.saas.vo.staff.ClassFullInfoVO;
import com.hailiang.remote.saas.vo.staff.GradeFullInfoVO;
import com.hailiang.remote.saas.vo.staff.StaffFullInfoVO;
import com.hailiang.saas.SaasSchoolCacheManager;
import com.hailiang.saas.model.dto.school.EduOrgQueryV2DTO;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import com.hailiang.saas.model.vo.school.EduOrgTreeV2VO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.util.EasyPoiUtil;
import com.hailiang.util.WebUtil;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 数据统计，抽象类，子类为当前学年和历史学年
 *
 * @Description: 数据统计，抽象类，子类为当前学年和历史学年
 * @Author: Jovi
 * @Date: Created in 2024/8/20
 * @Version: 2.0.0
 */
@Slf4j
@Component
public abstract class DataStatisticsHandler {
    @Resource
    private SaasSchoolCacheManager saasSchoolCacheManager;

    @Resource
    private BehaviourHandleManager behaviourHandleManager;

    @Resource
    private DorisBehaviourRecordManager dorisBehaviourRecordManager;

    @Resource
    private BasicInfoService basicInfoService;

    @Resource
    private StaffDailyStatisticsManager staffDailyStatisticsManager;

    @Resource
    private StudentDailyStatisticsManager studentDailyStatisticsManager;

    @Autowired
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private DataStatisticsLogic dataStatisticsLogic;
    @Value("${kanbanData.intelligentRatio.praiseImproveDetail}")
    private BigDecimal praiseImproveDetail;
    @Value("${kanbanData.intelligentRatio.fiveEducation}")
    private BigDecimal fiveEducation;

    /**
     * 根据学段/年级/班级查询指标配置过的老师id集合
     *
     * @return 教职工信息集合
     */
    public abstract List<StaffFullInfoVO> listTargetConfigTeacherIds(
            String campusSectionId,
            String schoolYear,
            String gradeId,
            String classId);


    /**
     * 批量获取教职工姓名信息
     *
     * @return TreeMap<staffId有序, 教职工姓名>
     */
    protected abstract TreeMap<String/*staffId*/, String/*教职工名称*/> listStaffTreeMap(Long campusSectionId, String schoolYear, Long gradeId, Long classId);

    /**
     * 批量获取教职工列表
     *
     * @return 教职工信息
     */
    protected abstract List<StaffFullInfoVO> listStaff(Long campusSectionId, String schoolYear, Integer type, Long gradeId, Long classId);

    /**
     * 获取学生总人数
     */
    protected abstract List<SassStudentVO> listTotalSaasStudent(Long campusSectionId, String schoolYear, Long gradeId, Long classId);

    /**
     * 根据学生ids获取学生
     */
    protected abstract List<SassStudentVO> listByStudentIds(Long campusSectionId, String schoolYear, List<Long> studentIds);

    /**
     * 获取总点评次数上一时间段统计信息
     */
    protected abstract LastSameTimeEvaluateVO getLastEvaluateStatistics(TeacherEvaluateDataStatisticsQuery query, Integer total);

    /**
     * 获取老师参与率上一时间段统计信息
     */
    protected abstract LastSameTimeEvaluateVO getLastParticipationRate(TeacherEvaluateDataStatisticsQuery query, List<String/*staffId*/> needEvaluateTeacherIds, BigDecimal rate);

    /**
     * 获取学生点评率上一时间段统计信息（环比）
     *
     * @param query
     * @param studentNum
     * @param totalStudentNum
     * @return
     */
    protected abstract LastSameTimeEvaluateVO getStudentLastParticipationRate(DataStatisticsQuery query, BigDecimal studentNum, BigDecimal totalStudentNum);

    /**
     * 获取校区内当前学年所有启用的指标
     */
    protected abstract List<TargetDTO> listEnableTarget(String schoolId, String campusId, String schoolYear);

    /**
     * 点评看板页面（支持历史学年）
     *
     * @return 老师点评、五育分布、表扬与待改进、指标点评情况
     */
    public TeacherEvaluateDataStatisticsVO getStatistics(TeacherEvaluateDataStatisticsQuery query) {

        TimeInterval timeInterval = DateUtil.timer();

        TeacherEvaluateDataStatisticsVO statistics = new TeacherEvaluateDataStatisticsVO();

        // 时间段内所有的点评数据
        List<StaffDailyStatisticsVO> staffDailyStatisticsList = this.getRecord(
                query.getCampusSectionId(),
                query.getSchoolYear(),
                query.getIsCurrentYear(),
                query.getGradeId(),
                query.getClassId(),
                query.getStartTime(),
                query.getEndTime());
        log.info("【pc端】- 【点评看板】- 【查询点评统计数据】，消耗时间:{}", timeInterval.intervalMs());
        timeInterval.restart();

        // 需点评老师
        List<StaffFullInfoVO> staffBatchVOList = this.listTargetConfigTeacherIds(query.getCampusSectionId(), query.getSchoolYear(), query.getGradeId(), query.getClassId());
        log.info("【pc端】- 【点评看板】- 【查询需点评老师】，消耗时间:{}", timeInterval.intervalMs());
        timeInterval.restart();
        //从saas获取该学校下的教务组织架构树形
        List<EduOrgTreeVO> eduOrgTreeVOS = this.listEduOrgTree(query.getType(), Constant.ONE, query.getCampusSectionId(), query.getSchoolYear(), query.getGradeId(), query.getClassId());
        // saas学段/年级/班级信息
        SaasCurrentInfoDTO saasCurrentInfoDTO = this.listBusinessIdByBusinessType(query);
        //获取校区内所有启用的指标
        List<TargetDTO> targets = this.listEnableTarget(WebUtil.getSchoolId(), WebUtil.getCampusId(), query.getSchoolYear());
        log.info("【pc端】- 【点评看板】- 【教职工点评基础数据获取】，消耗时间:{}", timeInterval.intervalMs());
        timeInterval.restart();

        // 行为记录根据类型(年级/班级/老师)分组
        Map<String, List<StaffDailyStatisticsVO>> groupMap = this.getBehaviourGroupByCurrentIdTyp(saasCurrentInfoDTO.getBusinessMap(), staffDailyStatisticsList, query.getType());
        statistics.setEvaluateNum(this.getEvaluateStatistics(query, staffDailyStatisticsList, staffBatchVOList, eduOrgTreeVOS));
        statistics.setTeacherParticipationRate(this.getTeacherParticipationRate(query, staffDailyStatisticsList, staffBatchVOList, eduOrgTreeVOS));
        statistics.setPraiseImprove(this.getPraiseImproveDetail(query, saasCurrentInfoDTO, staffDailyStatisticsList, groupMap));
        statistics.setFiveEducation(this.getFiveEducation(query, saasCurrentInfoDTO, staffDailyStatisticsList, groupMap));
        statistics.setTargetCoverage(this.getTargetCoverage(query, saasCurrentInfoDTO, staffDailyStatisticsList, targets));
        statistics.setTargetFrequency(this.getTargetFrequency(staffDailyStatisticsList, targets));
        List<TeacherVO> teacherVOList = this.listTeacherEvaluate(query, staffDailyStatisticsList, staffBatchVOList);
        log.info("【pc端】- 【点评看板】- 【教职工点评数据组装】，消耗时间:{}", timeInterval.intervalMs());
        if (CollUtil.isNotEmpty(teacherVOList)) {
            statistics.setListTeacher(teacherVOList.stream().limit(Constant.THREE).collect(Collectors.toList()));
        }
        return statistics;
    }

    /**
     * 老师点评榜（支持历史学年）
     */
    public Page<TeacherVO> pageTeacherEvaluateNew(TeacherEvaluateDataStatisticsQuery query) {
        // 时间段内所有的点评数据
        List<StaffDailyStatisticsVO> staffDailyStatisticsList = this.getRecord(query.getCampusSectionId(), query.getSchoolYear(), query.getIsCurrentYear(), query.getGradeId(), query.getClassId(), query.getStartTime(), query.getEndTime());
        // 需点评老师
        List<StaffFullInfoVO> staffFullInfoList = this.listTargetConfigTeacherIds(query.getCampusSectionId(), query.getSchoolYear(), query.getGradeId(), query.getClassId());
        List<TeacherVO> teacherVOS = this.listTeacherEvaluate(query, staffDailyStatisticsList, staffFullInfoList);
        //分页
        List<TeacherVO> teacherVOList = teacherVOS.stream().skip((long) (query.getPageNum() - 1) * query.getPageSize()).limit(query.getPageSize()).collect(Collectors.toList());
        Page<TeacherVO> page = new Page<>(query.getPageNum(), query.getPageSize());
        page.setRecords(teacherVOList);
        page.setTotal(teacherVOS.size());
        return page;
    }

    /**
     * 老师点评榜下载（支持历史学年）
     */
    public void exportTeacherEvaluateNew(TeacherEvaluateDataStatisticsQuery query, HttpServletResponse response) throws IOException {
        log.info("[老师点评榜下载]-开始，请求参数：{}", JSONUtil.toJsonStr(query));
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();

        //时间段内所有的点评数据
        List<StaffDailyStatisticsVO> staffDailyStatisticsList = this.getRecord(query.getCampusSectionId(), query.getSchoolYear(), query.getIsCurrentYear(), query.getGradeId(), query.getClassId(), query.getStartTime(), query.getEndTime());
        log.info("[老师点评榜下载]-查询点评数据，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        // 需点评老师
        List<StaffFullInfoVO> staffFullInfoList = this.listTargetConfigTeacherIds(query.getCampusSectionId(), query.getSchoolYear(), query.getGradeId(), query.getClassId());

        List<TeacherVO> teacherVOS = this.listTeacherVOS(query, staffDailyStatisticsList, staffFullInfoList);
        if (Constant.ONE.equals(query.getSort())) {
            teacherVOS = teacherVOS.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum)).collect(Collectors.toList());
        } else {
            teacherVOS = teacherVOS.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum).reversed()).collect(Collectors.toList());
        }

        log.info("[老师点评榜下载]-sheet2数据准备，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        // 第二个sheet参数定义
        Map<String, Object> sheet2Map = new HashMap<>();
        ExportParams sheet2Params = new ExportParams();
        sheet2Params.setSheetName("老师个人点评排行榜");
        sheet2Map.put("title", sheet2Params);
        sheet2Map.put("entity", TeacherVO.class);
        sheet2Map.put("data", CollUtil.newArrayList(teacherVOS));

        List<Map<String, Object>> schoolCourseList = new ArrayList<>();
        TeacherEvaluateExcel teacherEvaluateExcel = null;
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(query.getType())
                || SaasCurrentIdTypeEnum.GRADE.getCode().equals(query.getType())) {
            log.info("[老师点评榜下载]-学段/年级维度查询：准备增加新的sheet：年级和班级点评排行榜");
            teacherEvaluateExcel = this.listTeacherClassData(query, staffDailyStatisticsList);
            // 第一个sheet参数定义
            Map<String, Object> sheet1Map = new HashMap<>();
            ExportParams sheet1Params = new ExportParams();
            sheet1Params.setSheetName("年级和班级点评排行榜");
            sheet1Map.put("title", sheet1Params);
            sheet1Map.put("entity", TeacherEvaluateSheet1.class);
            sheet1Map.put("data", CollUtil.isEmpty(teacherEvaluateExcel.getTeacherEvaluateSheet1List()) ? Collections.EMPTY_LIST : teacherEvaluateExcel.getTeacherEvaluateSheet1List());
            schoolCourseList.add(sheet1Map);
        }
        log.info("[老师点评榜下载]-sheet1数据准备，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        schoolCourseList.add(sheet2Map);

        Workbook workbook = ExcelExportUtil.exportExcel(schoolCourseList, ExcelType.XSSF);
        // 处理sheet1-合并单元格
        dataStatisticsLogic.mergeCell(workbook, query.getType(), teacherEvaluateExcel);
        // 处理sheet2-修改标题名称
        dataStatisticsLogic.handleTitle(workbook, query.getType());
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(query.getType())
                || SaasCurrentIdTypeEnum.GRADE.getCode().equals(query.getType())) {
            EasyPoiUtil.adjustStyle(workbook.getSheetAt(1));
        }
        EasyPoiUtil.adjustStyle(workbook.getSheetAt(0));

        String startTime = DateUtil.format(query.getStartTime(), Constant.CHINESE_FORMAT);
        String endTime = DateUtil.format(query.getEndTime(), Constant.CHINESE_FORMAT);
        log.info("[老师点评榜下载]-导出并调整excel，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        EasyPoiUtil.downLoadExcelWithFormat(MessageFormat.format(TEACHER_EXCEL_NAME, startTime, endTime), response, workbook);
    }

    /**
     * 指标排行榜（支持历史学年）
     */
    public Page<TargetRankVO> getTargetRankNew(TeacherEvaluateDataStatisticsQuery query) {
        // 获取指标排行榜的所有数据
        List<TargetRankVO> targetRankList = this.listTargetRank(query);
        Page<TargetRankVO> resultPages = new Page<>(query.getPageNum(), query.getPageSize());
        if (CollUtil.isEmpty(targetRankList)) {
            return resultPages;
        }
        targetRankList = targetRankList.stream().skip((long) (query.getPageNum() - 1) * query.getPageSize()).limit(query.getPageSize()).collect(Collectors.toList());
        resultPages.setTotal(targetRankList.size());
        resultPages.setRecords(targetRankList);
        return resultPages;
    }

    /**
     * 导出指标排行榜excel（支持历史学年）
     */
    @SneakyThrows
    public void exportTargetRankNew(TeacherEvaluateDataStatisticsQuery query, HttpServletResponse response) {
        // 获取指标排行榜的所有数据
        List<TargetRankVO> targetRankList = this.listTargetRank(query);

        List<ExcelTargetRankVO> targetRankVOS = TargetConvert.INSTANCE.toExcelTargetRankVOList(targetRankList);

        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), this.getExcelExportEntities(), targetRankVOS);

        EasyPoiUtil.adjustStyle(workbook.getSheetAt(Constant.ZERO));

        EasyPoiUtil.downLoadExcelWithFormat(getTitle(query.getStartTime(), query.getEndTime()), response, workbook);
    }

    private List<ExcelExportEntity> getExcelExportEntities() {
        List<ExcelExportEntity> entityList = new ArrayList<>();

        ExcelExportEntity typeExportEntity = new ExcelExportEntity("一级分类", "moduleName", 18);
        entityList.add(typeExportEntity);

        ExcelExportEntity groupExportEntity = new ExcelExportEntity("分组", "groupName", 18);
        entityList.add(groupExportEntity);

        ExcelExportEntity targetExportEntity = new ExcelExportEntity("指标", "targetName", 18);
        entityList.add(targetExportEntity);

        ExcelExportEntity countExportEntity = new ExcelExportEntity("次数", "count", 18);
        entityList.add(countExportEntity);
        return entityList;
    }

    /**
     * 获取统计报表标题
     */
    private String getTitle(Date startTime, Date endTime) {
        String start = DateUtil.format(startTime, Constant.CHINESE_FORMAT);
        String end = DateUtil.format(endTime, Constant.CHINESE_FORMAT);
        return MessageFormat.format("{0}至{1}日指标点评榜", start, end);
    }

    private List<TargetRankVO> listTargetRank(TeacherEvaluateDataStatisticsQuery query) {
        // 时间段内所有的点评数据
        List<StaffDailyStatisticsVO> staffDailyStatisticsList = this.getRecord(query.getCampusSectionId(), query.getSchoolYear(), query.getIsCurrentYear(), query.getGradeId(), query.getClassId(), query.getStartTime(), query.getEndTime());
        //获取校区内所有启用的指标
        List<TargetDTO> targets = this.listEnableTarget(WebUtil.getSchoolId(), WebUtil.getCampusId(), query.getSchoolYear());
        // 获取指标排行榜的所有数据
        return this.listAllTargetRank(targets, staffDailyStatisticsList);
    }

    /**
     * 总点评次数
     */
    private DataStatisticsEvaluateNumVO getEvaluateStatistics(TeacherEvaluateDataStatisticsQuery query, List<StaffDailyStatisticsVO> staffDailyStatisticsList, List<StaffFullInfoVO> staffBatchVOList, List<EduOrgTreeVO> eduOrgTreeVOS) {
        DataStatisticsEvaluateNumVO evaluateNumVO = this.evaluateStatisticsFillTitle(query.getType(), query.getName());
        AtomicReference<Integer/*老师总点评次数*/> totalNum = new AtomicReference<>(Constant.ZERO);
        Map<String/*老师staffId*/, List<StaffDailyStatisticsVO>> staffDailyStatisticsMap = new HashMap<>();
        Map<Long/*年级gradeId*/, List<StaffDailyStatisticsVO>> gradeDailyStatisticsMap = new HashMap<>();
        Map<Long/*班级classId*/, List<StaffDailyStatisticsVO>> classDailyStatisticsMap = new HashMap<>();
        staffDailyStatisticsList.forEach(item -> {
            totalNum.updateAndGet(v -> v + item.getAppraisalCount());
            if (staffDailyStatisticsMap.containsKey(item.getAppraisalId())) {
                staffDailyStatisticsMap.get(item.getAppraisalId()).add(item);
            } else {
                staffDailyStatisticsMap.put(item.getAppraisalId(), CollUtil.newArrayList(item));
            }
            // 年级统计
            Long gradeId = Convert.toLong(item.getGradeId());
            if (gradeDailyStatisticsMap.containsKey(gradeId)) {
                gradeDailyStatisticsMap.get(gradeId).add(item);
            } else {
                gradeDailyStatisticsMap.put(gradeId, CollUtil.newArrayList(item));
            }
            // 班级统计
            Long classId = Convert.toLong(item.getClassId());
            if (classDailyStatisticsMap.containsKey(classId)) {
                classDailyStatisticsMap.get(classId).add(item);
            } else {
                classDailyStatisticsMap.put(classId, CollUtil.newArrayList(item));
            }
        });
        // 老师点评总次数
        evaluateNumVO.setTotalNum(totalNum.get());
        if (CollectionUtil.isEmpty(staffBatchVOList)) {
            evaluateNumVO.setEmptyFlag(Constant.ONE);
            return evaluateNumVO;
        }
        Map<String/*staffId*/, List<StaffFullInfoVO>> staffFullInfoVOMap = new HashMap<>();
        //需点评老师
        List<String/*staffId*/> teacherIdList = staffBatchVOList.stream().map(item -> {
            String staffId = Convert.toStr(item.getStaffId());
            if (staffFullInfoVOMap.containsKey(staffId)) {
                staffFullInfoVOMap.get(staffId).add(item);
            } else {
                staffFullInfoVOMap.put(staffId, CollUtil.newArrayList(item));
            }
            return staffId;
        }).collect(Collectors.toList());
        //需点评老师数量
        int teacherNum = teacherIdList.size();
        if (Constant.ZERO == teacherNum) {
            evaluateNumVO.setAverageNum(null);
            evaluateNumVO.setAverageDayNum(null);
        } else {
            BigDecimal averageNum = this.getRate(totalNum.get(), teacherNum, Constant.ONE).stripTrailingZeros();
            evaluateNumVO.setAverageNum(averageNum);
            Map<String, List<StaffDailyStatisticsVO>> totalTeacherNum = staffDailyStatisticsList.stream().collect(Collectors.groupingBy(item -> item.getStatisticsTime() + StrPool.UNDERLINE + item.getAppraisalId()));
            BigDecimal averageDayNum = this.getRate(totalTeacherNum.size(), teacherNum, Constant.ONE).stripTrailingZeros();
            evaluateNumVO.setAverageDayNum(averageDayNum);
        }
        // 获取上一段相同时间
        LastSameTimeEvaluateVO lastTimeEvaluate = this.getLastEvaluateStatistics(query, totalNum.get());
        evaluateNumVO.setPercent(lastTimeEvaluate.getPercent());
        evaluateNumVO.setLastSameTime(lastTimeEvaluate.getLastSameTime());

        ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
        //班级看板
        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(query.getType())) {
            Integer totalEvaluateNum = 0;
            if (CollectionUtil.isEmpty(teacherIdList)) {
                return evaluateNumVO;
            }
            for (String teacherId : teacherIdList) {
                NameAndValueVO nameAndValueVO = new NameAndValueVO();
                if (staffFullInfoVOMap.containsKey(teacherId)) {
                    List<StaffFullInfoVO> staffFullInfoVOS = staffFullInfoVOMap.get(teacherId);
                    if (CollUtil.isNotEmpty(staffFullInfoVOS)) {
                        nameAndValueVO.setName(staffFullInfoVOS.get(Constant.ZERO).getName());
                    }
                }
                //该老师评价次数
                long teacherEvaluateNum = staffDailyStatisticsMap.containsKey(teacherId) ? staffDailyStatisticsMap.get(teacherId).stream().mapToInt(StaffDailyStatisticsVO::getAppraisalCount).sum() : Constant.ZERO;
                nameAndValueVO.setValue(Convert.toStr(teacherEvaluateNum));
                nameAndValueVOS.add(nameAndValueVO);
                totalEvaluateNum += Convert.toInt(teacherEvaluateNum);
            }
            //智能建议
            //平均点评次数
            BigDecimal avgEvaluateNum = this.getRate(totalEvaluateNum, teacherNum, Constant.ONE).stripTrailingZeros();
            evaluateNumVO.setAverage(Convert.toStr(avgEvaluateNum));
            List<String> teacherNames = nameAndValueVOS.stream().filter(s -> BigDecimal.valueOf(Convert.toLong(s.getValue())).compareTo(avgEvaluateNum) == Constant.NEGATIVE_ONE).map(NameAndValueVO::getName).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(teacherNames)) {
                evaluateNumVO.setIntelligentSuggestion(null);
            } else {
                evaluateNumVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, teacherNames) + MessageFormat.format(CLASS_EVALUATE_SUGGESTION, avgEvaluateNum));
            }
            nameAndValueVOS = (ArrayList<NameAndValueVO>) nameAndValueVOS.stream().sorted(Comparator.comparing(NameAndValueVO::getValue, Comparator.comparingDouble(Double::parseDouble)).reversed()).collect(Collectors.toList());
        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(query.getType())) {
            int totalEvaluateNum = 0;
            if (CollUtil.isEmpty(eduOrgTreeVOS) || CollUtil.isEmpty(eduOrgTreeVOS.get(0).getChildren())) {
                return evaluateNumVO;
            }
            //该年级所有班级
            Map<Long, String> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));
            if (CollectionUtil.isEmpty(idAndName)) {
                return evaluateNumVO;
            }
            for (Map.Entry<Long, String> longStringEntry : idAndName.entrySet()) {
                NameAndValueVO nameAndValueVO = new NameAndValueVO();
                //该班级下所有点评数据
                int size = classDailyStatisticsMap.getOrDefault(longStringEntry.getKey(), new ArrayList<>()).stream().mapToInt(StaffDailyStatisticsVO::getAppraisalCount).sum();
                nameAndValueVO.setName(longStringEntry.getValue());
                nameAndValueVO.setValue(Convert.toStr(size));
                nameAndValueVOS.add(nameAndValueVO);
                totalEvaluateNum += size;
            }
            //智能建议
            //平均点评次数
            BigDecimal avgEvaluateNum = this.getRate(totalEvaluateNum, idAndName.keySet().size(), Constant.ONE).stripTrailingZeros();
            evaluateNumVO.setAverage(Convert.toStr(avgEvaluateNum));
            List<String> classNames = nameAndValueVOS.stream().filter(s -> BigDecimal.valueOf(Convert.toLong(s.getValue())).compareTo(avgEvaluateNum) == Constant.NEGATIVE_ONE).map(NameAndValueVO::getName).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(classNames)) {
                evaluateNumVO.setIntelligentSuggestion(null);
            } else {
                evaluateNumVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, classNames) + MessageFormat.format(GRADE_EVALUATE_SUGGESTION, avgEvaluateNum));
            }
        } else if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(query.getType())) {
            int totalEvaluateNum = 0;
            if (CollUtil.isEmpty(eduOrgTreeVOS) || CollUtil.isEmpty(eduOrgTreeVOS.get(0).getChildren())) {
                return evaluateNumVO;
            }
            // 该学段所有年级
            Map<Long, String> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));
            if (CollectionUtil.isEmpty(idAndName)) {
                return evaluateNumVO;
            }
            for (Map.Entry<Long, String> longStringEntry : idAndName.entrySet()) {
                NameAndValueVO nameAndValueVO = new NameAndValueVO();
                //该年级下所有点评数据
                int size = gradeDailyStatisticsMap.containsKey(longStringEntry.getKey()) ? gradeDailyStatisticsMap.get(longStringEntry.getKey()).stream().mapToInt(StaffDailyStatisticsVO::getAppraisalCount).sum() : 0;
                nameAndValueVO.setName(longStringEntry.getValue());
                nameAndValueVO.setValue(Convert.toStr(size));
                nameAndValueVOS.add(nameAndValueVO);
                totalEvaluateNum += size;
            }
            //智能建议
            //平均点评次数
            BigDecimal avgEvaluateNum = this.getRate(totalEvaluateNum, idAndName.keySet().size(), Constant.ONE).stripTrailingZeros();
            evaluateNumVO.setAverage(Convert.toStr(avgEvaluateNum));
            List<String> classNames = nameAndValueVOS.stream().filter(s -> BigDecimal.valueOf(Convert.toLong(s.getValue())).compareTo(avgEvaluateNum) == Constant.NEGATIVE_ONE).map(NameAndValueVO::getName).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(classNames)) {
                evaluateNumVO.setIntelligentSuggestion(null);
            } else {
                evaluateNumVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, classNames) + MessageFormat.format(GRADE_EVALUATE_SUGGESTION, avgEvaluateNum));
            }
        }
        evaluateNumVO.setChildrenTotalNum(nameAndValueVOS);
        if (BeanUtil.isEmpty(evaluateNumVO)) {
            evaluateNumVO.setEmptyFlag(Constant.ONE);
        }
        return evaluateNumVO;
    }

    /**
     * 老师参与率
     */
    private DataStatisticsTeacherParticipationRateVO getTeacherParticipationRate(TeacherEvaluateDataStatisticsQuery query, List<StaffDailyStatisticsVO> staffDailyStatisticsList, List<StaffFullInfoVO> staffBatchVOList, List<EduOrgTreeVO> eduOrgTreeVOS) {
        DataStatisticsTeacherParticipationRateVO rateVO = this.participationRateFillTitle(query.getType(), query.getName());
        // 需点评老师数量
        Map<String/*staffId*/, List<StaffFullInfoVO>> staffIdMap = new HashMap<>();
        List<String/*staffId*/> listTargetConfigTeacherIds = staffBatchVOList.stream().map(item -> {
            String id = Convert.toStr(item.getStaffId());
            if (staffIdMap.containsKey(id)) {
                staffIdMap.get(id).add(item);
            } else {
                staffIdMap.put(id, CollUtil.newArrayList(item));
            }
            return id;
        }).collect(Collectors.toList());
        // 需点评老师
        int teacherNum = listTargetConfigTeacherIds.size();
        // 点评过的老师id集合
        List<String> listTeacherIds = this.listTeacherIds(query.getCampusSectionId(), query.getSchoolYear(), query.getGradeId(), query.getClassId(), query.getStartTime(), query.getEndTime());
        // 参与点评老师数量
        int participationTeacherNum = CollectionUtil.intersection(listTargetConfigTeacherIds, listTeacherIds).size();
        // 老师参与率
        BigDecimal rate = this.getRate(participationTeacherNum, teacherNum, Constant.THREE).stripTrailingZeros();
        rateVO.setTeacherNum(teacherNum);
        rateVO.setParticipationTeacherNum(participationTeacherNum);
        rateVO.setRate(rate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);

        // 获取上一段相同时间
        LastSameTimeEvaluateVO lastTimeEvaluate = this.getLastParticipationRate(query, listTargetConfigTeacherIds, rate);
        rateVO.setPercent(lastTimeEvaluate.getPercent());
        rateVO.setLastSameTime(lastTimeEvaluate.getLastSameTime());

        Map<String/*班级classId*/, List<String>/*老师集合*/> classAppraisalIdMap = new HashMap<>();
        Map<String/*年级gradeId*/, List<String>/*老师集合*/> gradeAppraisalIdMap = new HashMap<>();
        Map<String/*老师ID*/, List<StaffDailyStatisticsVO>> teacherEvaluateNumMap = staffDailyStatisticsList.stream().collect(Collectors.groupingBy(StaffDailyStatisticsVO::getAppraisalId));
        staffDailyStatisticsList.forEach(item -> {
            if (classAppraisalIdMap.containsKey(item.getClassId())) {
                classAppraisalIdMap.get(item.getClassId()).add(item.getAppraisalId());
            } else {
                classAppraisalIdMap.put(item.getClassId(), CollUtil.newArrayList(item.getAppraisalId()));
            }
            if (gradeAppraisalIdMap.containsKey(item.getGradeId())) {
                gradeAppraisalIdMap.get(item.getGradeId()).add(item.getAppraisalId());
            } else {
                gradeAppraisalIdMap.put(item.getGradeId(), CollUtil.newArrayList(item.getAppraisalId()));
            }
        });
        //老师点评的
        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(query.getType())) {
            this.buildClassType(rateVO, listTargetConfigTeacherIds, teacherEvaluateNumMap, staffIdMap);
        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(query.getType())) {
            this.buildGradeType(rateVO, listTargetConfigTeacherIds, eduOrgTreeVOS, classAppraisalIdMap, staffBatchVOList);
        } else if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(query.getType())) {
            this.buildSectionType(rateVO, listTargetConfigTeacherIds, eduOrgTreeVOS, gradeAppraisalIdMap, staffBatchVOList);
        }
        if (BeanUtil.isEmpty(rateVO)) {
            rateVO.setEmptyFlag(Constant.ONE);
        }
        return rateVO;
    }

    /**
     * 表扬与待改进
     */
    private PraiseImproveVO getPraiseImproveDetail(TeacherEvaluateDataStatisticsQuery query, SaasCurrentInfoDTO saasCurrentInfoDTO, List<StaffDailyStatisticsVO> staffDailyStatisticsList, Map<String, List<StaffDailyStatisticsVO>> groupMap) {
        String title = "表扬与待改进";
        String detailTitle = "评分情况";
        FiveEducationVO fillTitle = this.commonFillTitle(query.getType(), title, detailTitle, saasCurrentInfoDTO.getCurrentBusinessName());
        PraiseImproveVO praiseImproveVO = new PraiseImproveVO();
        praiseImproveVO.setEmptyFlag(fillTitle.getEmptyFlag());
        praiseImproveVO.setTitle(fillTitle.getTitle());
        praiseImproveVO.setDetailTitle(fillTitle.getDetailTitle());

        // 遍历一次集合计算多个BigDecimal的字段和
        BigDecimal[] totalScoreSums = this.multiKeySum(staffDailyStatisticsList);
        BigDecimal sumScore = totalScoreSums[0];
        // 如果所有分值相加为0
        if (ObjectUtil.equals(BigDecimal.ZERO, sumScore)) {
            praiseImproveVO.setEmptyFlag(Constant.YES);
            return praiseImproveVO;
        }
        // 表扬总分值
        BigDecimal praise = totalScoreSums[1];
        // 待改进总分值（负值）
        BigDecimal needImprove = totalScoreSums[2];
        // 表扬占比(保留两位小数) = 表扬 / ( 表扬 - (待改进) )
        BigDecimal praiseProportion = praise.divide(praise.subtract(needImprove), 3, RoundingMode.HALF_UP);
        // 待改进占比(保留两位小数)
        BigDecimal needImproveProportion = BigDecimal.ONE.subtract(praiseProportion);
        // 总分值 = 表扬 - (待改进)
        praiseImproveVO.setTotalScore(praise.subtract(needImprove));
        BigDecimal hundred = BigDecimal.valueOf(100);
        praiseImproveVO.setPraiseProportion(praiseProportion.multiply(hundred));
        praiseImproveVO.setNeedImprovedProportion(needImproveProportion.multiply(hundred));

        List<PraiseImproveDetailVO> details = new ArrayList<>();
        for (String businessId : saasCurrentInfoDTO.getSortBusinessKey()) {
            List<StaffDailyStatisticsVO> subStaffDailyStatisticsList = groupMap.get(businessId);
            PraiseImproveDetailVO praiseDetailVO = new PraiseImproveDetailVO();
            praiseDetailVO.setName(saasCurrentInfoDTO.getBusinessMap().getOrDefault(businessId, ""));
            // 无数据情况
            if (CollUtil.isEmpty(subStaffDailyStatisticsList)) {
                details.add(praiseDetailVO);
                continue;
            }
            // 总分
            BigDecimal[] subScoreSums = this.multiKeySum(subStaffDailyStatisticsList);
            BigDecimal subTotalScore = subScoreSums[0];
            if (ObjectUtil.equals(BigDecimal.ZERO, subTotalScore)) {
                log.info("表扬与待改进-业务分组占比-总分为空");
                continue;
            }
            // 表扬总分值
            BigDecimal subDetailPraise = subScoreSums[1];
            // 待改进总分值（负值）
            BigDecimal subDetailImprove = subScoreSums[2];
            // 表扬占比(保留两位小数) = 表扬 / ( 表扬 - （ - 待改进 ） )
            BigDecimal detailPraiseProportion = subDetailPraise.divide((subDetailPraise.subtract(subDetailImprove)), 3, RoundingMode.HALF_UP);
            // 待改进占比(保留两位小数) = 1 - 表扬占比
            BigDecimal detailNeedImproveProportion = BigDecimal.ONE.subtract(detailPraiseProportion);
            praiseDetailVO.setPraiseProportion(detailPraiseProportion.multiply(hundred));
            praiseDetailVO.setNeedImprovedProportion(detailNeedImproveProportion.multiply(hundred));
            praiseDetailVO.setIsNotEmpty(Constant.YES);
            details.add(praiseDetailVO);
        }
        // 班级
        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(query.getType())) {
            details = details.stream().sorted(Comparator.comparing(PraiseImproveDetailVO::getIsNotEmpty, Comparator.nullsFirst(Integer::compareTo).reversed())).collect(Collectors.toList());
        }
        praiseImproveVO.setDetails(details);
        // 智能建议
        List<String> businessNames = details.stream().filter(s -> {
            boolean flag = false;
            if (Objects.nonNull(s.getNeedImprovedProportion()) && s.getNeedImprovedProportion().compareTo(praiseImproveDetail) > 0) {
                flag = Boolean.TRUE;
            }
            return flag;
        }).map(PraiseImproveDetailVO::getName).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(businessNames)) {
            String intelligentSuggestion = StrUtil.join(StrPool.COMMA, businessNames) + "点评的待改进高于" + praiseImproveDetail + "%，希望多鼓励孩子喔";
            praiseImproveVO.setIntelligentSuggestion(intelligentSuggestion);
        }
        return praiseImproveVO;
    }

    /**
     * 五育分布
     */
    private FiveEducationVO getFiveEducation(TeacherEvaluateDataStatisticsQuery query, SaasCurrentInfoDTO saasCurrentInfoDTO, List<StaffDailyStatisticsVO> staffDailyStatisticsList, Map<String, List<StaffDailyStatisticsVO>> groupMap) {
        String title = "五育分布";
        String detailTitle = "维度情况";
        FiveEducationVO fiveEducationVO = this.commonFillTitle(query.getType(), title, detailTitle, saasCurrentInfoDTO.getCurrentBusinessName());
        // 各年级维度情况过滤掉其他育
        staffDailyStatisticsList = staffDailyStatisticsList
                .stream()
                .filter(item -> !Objects.equals(item.getModuleCode(), ModuleEnum.OTHER.getCode()))
                .collect(Collectors.toList());
        // 点评次数map
        Map<Integer/*moduleCode*/, Integer> countModuleMap = staffDailyStatisticsList.stream()
                .collect(Collectors.groupingBy(StaffDailyStatisticsVO::getModuleCode, Collectors.summingInt(item -> {
                    if (item.getAppraisalCount() == null) return 0;
                    return item.getAppraisalCount();
                })));
        // 根据行为记录统计每个五育模块的占比
        List<FiveEducationDistributionVO> distributions = this.encapsulationFiveEducation(staffDailyStatisticsList, null, null, countModuleMap);
        // 五育明细占比
        fiveEducationVO.setDistributions(distributions);
        // 总分
        fiveEducationVO.setTotalScore(staffDailyStatisticsList.stream().map(item -> {
            if (Objects.isNull(item.getPlusTotalScore()) && Objects.isNull(item.getMinusTotalScore())) {
                return BigDecimal.ZERO;
            } else if (Objects.isNull(item.getPlusTotalScore())) {
                return item.getMinusTotalScore().abs();
            } else if (Objects.isNull(item.getMinusTotalScore())) {
                return item.getPlusTotalScore();
            }
            return item.getPlusTotalScore().subtract(item.getMinusTotalScore());
        }).reduce(BigDecimal.ZERO, BigDecimal::add));

        // 所有(年级/班级)的明细详情
        List<FiveEducationDistributionVO> allBusinessDistributions = new ArrayList<>();
        // 各年级(班级)五育分布占比详情数据封装
        List<FiveEducationDetailVO> details = new ArrayList<>();
        for (String businessId : saasCurrentInfoDTO.getSortBusinessKey()) {
            FiveEducationDetailVO fiveEducationDetail = new FiveEducationDetailVO();
            fiveEducationDetail.setName(saasCurrentInfoDTO.getBusinessMap().getOrDefault(businessId, ""));
            List<StaffDailyStatisticsVO> staffDailyStatisticsVOList = groupMap.get(businessId);
            // 如果没有点评数据
            if (CollUtil.isEmpty(staffDailyStatisticsVOList)) {
                details.add(fiveEducationDetail);
                continue;
            }
            // 封装每个模块的占比
            List<FiveEducationDistributionVO> detailDistributions = this.encapsulationFiveEducation(staffDailyStatisticsVOList, businessId, fiveEducationDetail.getName(), null);
            fiveEducationDetail.setDistributions(detailDistributions);
            fiveEducationDetail.setIsNotEmpty(Constant.YES);
            allBusinessDistributions.addAll(detailDistributions);
            details.add(fiveEducationDetail);
        }
        // 班级
        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(query.getType())) {
            details = details.stream().sorted(Comparator.comparing(FiveEducationDetailVO::getIsNotEmpty, Comparator.nullsFirst(Integer::compareTo).reversed())).collect(Collectors.toList());
        }
        fiveEducationVO.setDetails(details);

        // 低点评数据
        List<FiveEducationDistributionVO> lowProportions = allBusinessDistributions.stream().filter(s -> {
            Boolean flag = Boolean.FALSE;
            if (s.getProportion().compareTo(fiveEducation) < 0) {
                flag = Boolean.TRUE;
            }
            return flag;
        }).collect(Collectors.toList());
        Map<Integer, List<FiveEducationDistributionVO>> moduleMap = lowProportions.stream().collect(Collectors.groupingBy(FiveEducationDistributionVO::getModuleCode));

        // 智能建议
        List<String> intelligentSuggestions = new ArrayList<>();
        // 存在未点评的列表
        List<String> emptyDataBusinessNames = details.stream().filter(s -> CollUtil.isEmpty(s.getDistributions())).map(FiveEducationDetailVO::getName).collect(Collectors.toList());
        // 不存在未点评的列表
        if (CollUtil.isEmpty(emptyDataBusinessNames)) {
            for (Integer moduleCode : moduleMap.keySet()) {
                List<FiveEducationDistributionVO> fiveEducationDistributionVOList = moduleMap.get(moduleCode);
                // 筛选出年级
                List<String> businessNames = fiveEducationDistributionVOList.stream().map(FiveEducationDistributionVO::getBusinessName).distinct().collect(Collectors.toList());
                String intelligentSuggestion = ModuleEnum.getModuleName(moduleCode) + ":" + StrUtil.join(StrPool.COMMA, businessNames) + "点评率低于" + fiveEducation + "%，希望进行多点评";
                intelligentSuggestions.add(intelligentSuggestion);
            }
        } else {
            for (Integer moduleCode : ModuleEnum.normalModuleMap.keySet()) {
                if (moduleMap.containsKey(moduleCode)) {
                    List<FiveEducationDistributionVO> fiveEducationDistributionVOList = moduleMap.get(moduleCode);
                    if (CollUtil.isEmpty(fiveEducationDistributionVOList)) continue;
                    // 筛选出年级
                    List<String> businessNames = fiveEducationDistributionVOList.stream().map(FiveEducationDistributionVO::getBusinessName).distinct().collect(Collectors.toList());
                    String intelligentSuggestion = ModuleEnum.getModuleName(moduleCode) + ":" + StrUtil.join(StrPool.COMMA, businessNames) + "," + StrUtil.join(StrPool.COMMA, emptyDataBusinessNames) + "点评率低于" + fiveEducation + "%，希望进行多点评";
                    intelligentSuggestions.add(intelligentSuggestion);
                } else {
                    String intelligentSuggestion = ModuleEnum.getModuleName(moduleCode) + ":" + StrUtil.join(StrPool.COMMA, emptyDataBusinessNames) + "点评率低于" + fiveEducation + "%，希望进行多点评";
                    intelligentSuggestions.add(intelligentSuggestion);
                }
            }
        }
        // 班级看板无智能建议
        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(query.getType())) {
            fiveEducationVO.setIntelligentSuggestion(null);
        } else {
            // 智能建议
            fiveEducationVO.setIntelligentSuggestion(intelligentSuggestions);
        }
        return fiveEducationVO;
    }

    /**
     * 指标覆盖率(只有校级数据,指标配置只有校级维度)
     */
    private TargetCoverageVO getTargetCoverage(TeacherEvaluateDataStatisticsQuery query, SaasCurrentInfoDTO saasCurrentInfoDTO, List<StaffDailyStatisticsVO> staffDailyStatisticsList, List<TargetDTO> targets) {
        String title = "指标覆盖率";
        String detailTitle = "指标覆盖情况";
        // saas学段/年级/班级信息
        TargetCoverageVO targetCoverageVO = this.targetCoverageFillTitle(query.getType(), title, detailTitle, saasCurrentInfoDTO.getCurrentBusinessName());
        if (CollUtil.isEmpty(targets)) {
            targetCoverageVO.setEmptyFlag(Constant.YES);
            return targetCoverageVO;
        }
        List<Long> targetIds = targets.stream().map(TargetDTO::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(targetIds)) {
            targetCoverageVO.setEmptyFlag(Constant.YES);
            return targetCoverageVO;
        }
        //获取点评过的指标个数(需要过滤出启用且未删除的指标)
        Map<Long, Long> commentMap = staffDailyStatisticsList.stream().filter(s -> targetIds.contains(s.getTargetId()))
                .collect(Collectors.groupingBy(StaffDailyStatisticsVO::getTargetId, Collectors.counting()));

        targetCoverageVO.setTotalNum(targetIds.size());
        // 被点评指标个数(点评过的指标个数/总指标个数)
        BigDecimal beReviewedProportion = this.getRate(commentMap.keySet().size(), targetIds.size(), Constant.THREE).stripTrailingZeros();
        // 未被点评指标占比
        BigDecimal notReviewedProportion = BigDecimal.ONE.subtract(beReviewedProportion);
        targetCoverageVO.setBeReviewedProportion(beReviewedProportion.multiply(Convert.toBigDecimal(100)));
        targetCoverageVO.setNotReviewedProportion(notReviewedProportion.multiply(Convert.toBigDecimal(100)));
        // 智能建议
        if (targetCoverageVO.getNotReviewedProportion().compareTo(BigDecimal.ZERO) > 0) {
            targetCoverageVO.setIntelligentSuggestion("有" + targetCoverageVO.getNotReviewedProportion().stripTrailingZeros().toPlainString() + "%的指标未被填写过，可以考虑督促老师填写或删除");
        }
        return targetCoverageVO;
    }

    /**
     * 指标覆盖情况(只有校级数据,指标配置只有校级维度)
     */
    private List<TargetFrequencyVO> getTargetFrequency(List<StaffDailyStatisticsVO> staffDailyStatisticsList, List<TargetDTO> targets) {
        List<TargetRankVO> targetRankList = this.listAllTargetRank(targets, staffDailyStatisticsList);
        List<TargetFrequencyVO> frequencyList = targetRankList.stream().map(s -> {
            TargetFrequencyVO targetFrequency = new TargetFrequencyVO();
            targetFrequency.setTargetName(s.getTargetName());
            targetFrequency.setNum(s.getCount());
            targetFrequency.setCreateTime(s.getCreateTime());
            return targetFrequency;
        }).collect(Collectors.toList());
        if (CollUtil.isEmpty(frequencyList)) {
            return Collections.emptyList();
        }
        List<TargetFrequencyVO> result = new ArrayList<>();
        if (frequencyList.size() <= 10) {
            List<TargetFrequencyVO/*高频*/> heightList = frequencyList.stream().filter(s -> s.getNum() > 0).collect(Collectors.toList());
            heightList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.HEIGHT.getCode()));
            List<TargetFrequencyVO/*低频*/> lowList = frequencyList.stream().filter(s -> s.getNum() == 0).collect(Collectors.toList());
            lowList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.LOW.getCode()));
            result.addAll(heightList);
            result.addAll(lowList);
        }
        if (frequencyList.size() > 10 && frequencyList.size() <= 20) {
            // 大于0的数据
            List<TargetFrequencyVO> noZeroNumList = frequencyList.stream().filter(s -> s.getNum() > 0).collect(Collectors.toList());
            if (noZeroNumList.size() >= 10) {
                List<TargetFrequencyVO/*高频*/> heightList = frequencyList.stream().limit(10).collect(Collectors.toList());
                heightList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.HEIGHT.getCode()));
                // 低频
                frequencyList.removeAll(heightList);
                frequencyList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.LOW.getCode()));
                // 汇总
                result.addAll(heightList);
                result.addAll(frequencyList);
            } else {
                // 高频
                noZeroNumList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.HEIGHT.getCode()));
                // 低频
                frequencyList.removeAll(noZeroNumList);
                frequencyList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.LOW.getCode()));
                // 汇总
                result.addAll(noZeroNumList);
                result.addAll(frequencyList);
            }
        }
        if (frequencyList.size() > 20) {
            List<TargetFrequencyVO/*高频*/> heightList = frequencyList.stream().filter(s -> s.getNum() > 0).sorted(Comparator.comparing(TargetFrequencyVO::getNum).reversed()).limit(10).collect(Collectors.toList());
            heightList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.HEIGHT.getCode()));
            List<TargetFrequencyVO/*低频*/> lowList = frequencyList.stream().sorted(Comparator.comparing(TargetFrequencyVO::getNum).reversed().thenComparing(TargetFrequencyVO::getCreateTime).reversed()).limit(10).collect(Collectors.toList());
            lowList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.LOW.getCode()));
            // 汇总
            result.addAll(heightList);
            result.addAll(lowList);
        }
        return result.stream().sorted(Comparator.comparing(TargetFrequencyVO::getNum).reversed()).collect(Collectors.toList());
    }

    private List<TeacherVO> listTeacherEvaluate(TeacherEvaluateDataStatisticsQuery query, List<StaffDailyStatisticsVO> staffDailyStatisticsList, List<StaffFullInfoVO> staffFullInfoList) {
        //获取老师点评榜
        List<TeacherVO> teacherVOS = this.listTeacherVOS(query, staffDailyStatisticsList, staffFullInfoList);
        if (Constant.ONE.equals(query.getSort())) {
            teacherVOS = teacherVOS.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum)).collect(Collectors.toList());
        } else {
            teacherVOS = teacherVOS.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum).reversed()).collect(Collectors.toList());
        }
        return teacherVOS;
    }

    private TeacherEvaluateExcel listTeacherClassData(TeacherEvaluateDataStatisticsQuery query, List<StaffDailyStatisticsVO> staffDailyStatisticsList) {
        TeacherEvaluateExcel teacherEvaluateExcel = new TeacherEvaluateExcel();
        List<TeacherEvaluateSheet1> teacherEvaluateSheet1s = new ArrayList<>();
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();

        // 业务数据-根据班级-老师分组
        Map<String, List<StaffDailyStatisticsVO>> staffBehaviourMap = new HashMap<>();
        Map<String, List<StaffDailyStatisticsVO>> classBehaviourMap = new HashMap<>();
        Map<String, List<StaffDailyStatisticsVO>> gradeBehaviourMap = new HashMap<>();
        for (StaffDailyStatisticsVO statisticsVO : staffDailyStatisticsList) {
            String classStaffId = statisticsVO.getClassId() + statisticsVO.getAppraisalId();
            String classId = statisticsVO.getClassId();
            String gradeId = statisticsVO.getGradeId();
            staffBehaviourMap.computeIfAbsent(classStaffId, (key) -> new ArrayList<>()).add(statisticsVO);
            classBehaviourMap.computeIfAbsent(classId, (key) -> new ArrayList<>()).add(statisticsVO);
            gradeBehaviourMap.computeIfAbsent(gradeId, (key) -> new ArrayList<>()).add(statisticsVO);
        }
        // 需点评老师
        List<StaffFullInfoVO> staffFullInfoList = this.listTargetConfigTeacherIds(query.getCampusSectionId(), query.getSchoolYear(), query.getGradeId(), query.getClassId());
        if (CollectionUtil.isEmpty(staffFullInfoList)) {
            return teacherEvaluateExcel;
        }
        log.info("[老师点评榜下载]-[sheet1数据准备]-1.2，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        List<Long> targetStaffIds = CollStreamUtil.toList(staffFullInfoList, StaffFullInfoVO::getStaffId);
        //从saas获取学段或者年级或者班级所有老师信息
        StaffSectionOrGradeQueryDTO queryDTO = new StaffSectionOrGradeQueryDTO();

        log.info("[老师点评榜下载]-[sheet1数据准备]-1.3，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        //从saas获取该学段下的教务组织架构列表
        List<EduOrgTreeVO> eduOrgTreeVOS = this.listEduOrgTree(query.getType(), Constant.ZERO, query.getCampusSectionId(), query.getSchoolYear(), query.getGradeId(), query.getClassId());
        Map<Long, String> idNameMap = CollStreamUtil.toMap(eduOrgTreeVOS, EduOrgTreeVO::getId, EduOrgTreeVO::getName);
        log.info("[老师点评榜下载]-[sheet1数据准备]-1.4，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        if (CollectionUtil.isEmpty(eduOrgTreeVOS)) {
            return teacherEvaluateExcel;
        }
        List<StaffFullInfoVO> staffFullInfoVOS;
        for (EduOrgTreeVO eduOrgTreeVO : eduOrgTreeVOS) {
            if (eduOrgTreeVO == null || ObjectUtil.notEqual(eduOrgTreeVO.getType(), SaasClassOrgTypeEnum.CLASS.getCode()))
                continue;
            // 只过滤出当前年级下的班级
            if (ObjectUtil.isNotEmpty(query.getGradeId())
                    && ObjectUtil.notEqual(Convert.toStr(eduOrgTreeVO.getParentId()), query.getGradeId())) continue;
            int haveStaff = 0;
            // 查询班级下的人
            queryDTO.setClassId(eduOrgTreeVO.getId());
            staffFullInfoVOS = basicInfoRemote.queryByClassId(queryDTO);
            // 只统计了谁点评的这个班的人
            String gradeName = idNameMap.get(eduOrgTreeVO.getParentId());
            for (StaffFullInfoVO staffFullInfoVO : staffFullInfoVOS) {
                if (!targetStaffIds.contains(staffFullInfoVO.getStaffId())) {
                    continue;
                }
                teacherEvaluateSheet1s.add(this.getTeacherEvaluateSheet1(eduOrgTreeVO, staffFullInfoVO, gradeName));
                haveStaff++;
            }
            // 如果班级下面没有人 则加一行班级数据，对应的老师是空值
            if (haveStaff == 0) {
                teacherEvaluateSheet1s.add(this.getTeacherEvaluateSheet1(eduOrgTreeVO, null, gradeName));
            }
        }
        log.info("[老师点评榜下载]-[sheet1数据准备]-1.5，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        teacherEvaluateSheet1s.forEach(sheet1 -> {
            // 老师评价次数
            String staffId = Convert.toStr(sheet1.getClassId()) + Convert.toStr(sheet1.getStaffId());
            String classId = Convert.toStr(sheet1.getClassId());
            String gradeId = Convert.toStr(sheet1.getGradeId());
            // 老师评价总数
            sheet1.setStaffEvaluateNum(ObjectUtil.isEmpty(sheet1.getStaffId()) ? null : CollectionUtil.isEmpty(staffBehaviourMap) ? Constant.ZERO : CollectionUtil.isEmpty(staffBehaviourMap.get(staffId)) ? 0 : staffBehaviourMap.get(staffId).stream().mapToInt(StaffDailyStatisticsVO::getAppraisalCount).sum());
            // 老师评价天数
            sheet1.setStaffDayNum(ObjectUtil.isEmpty(sheet1.getStaffId()) ? null : CollectionUtil.isEmpty(staffBehaviourMap) ? Constant.ZERO : CollectionUtil.isEmpty(staffBehaviourMap.get(staffId)) ? 0 : staffBehaviourMap.get(staffId).stream().collect(Collectors.groupingBy(StaffDailyStatisticsVO::getStatisticsTime)).keySet().size());
            // 班级评价总数
            sheet1.setClassEvaluateNum(CollectionUtil.isEmpty(classBehaviourMap) || CollectionUtil.isEmpty(classBehaviourMap.get(classId)) ? Constant.ZERO : classBehaviourMap.get(classId).stream().mapToInt(StaffDailyStatisticsVO::getAppraisalCount).sum());
            // 班级人均点评天数
            sheet1.setClassAverageDayNum(getAverageDayNum(query, classId, classBehaviourMap, SaasCurrentIdTypeEnum.CLASS.getCode(), sheet1));
            // 年级评价总数
            sheet1.setGradeEvaluateNum(CollectionUtil.isEmpty(gradeBehaviourMap) || CollectionUtil.isEmpty(gradeBehaviourMap.get(gradeId)) ? Constant.ZERO : gradeBehaviourMap.get(gradeId).stream().mapToInt(StaffDailyStatisticsVO::getAppraisalCount).sum());
            // 年级人均点评天数
            sheet1.setGradeAverageDayNum(getAverageDayNum(query, gradeId, gradeBehaviourMap, SaasCurrentIdTypeEnum.GRADE.getCode(), sheet1));
        });
        log.info("[老师点评榜下载]-[sheet1数据准备]-1.6，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        log.info("[老师点评榜下载]-原始数据 teacherEvaluateSheet1s {}", JSONUtil.toJsonStr(teacherEvaluateSheet1s));
        List<Long> gradeIds = new ArrayList<>();
        List<Long> classIds = new ArrayList<>();
        // 使用自定义比较器进行排序
//        teacherEvaluateSheet1s = teacherEvaluateSheet1s.stream().sorted(Objects.equals(query.getSort(), Constant.ONE) ? dataStatisticsLogic.getAscComparator() : dataStatisticsLogic.getDescComparator()).collect(Collectors.toList());
        teacherEvaluateSheet1s.sort(Objects.equals(query.getSort(), Constant.ONE) ? dataStatisticsLogic.getAscComparator() : dataStatisticsLogic.getDescComparator());
        teacherEvaluateSheet1s.forEach(item -> {
            classIds.add(item.getClassId());
            gradeIds.add(item.getGradeId());
        });
        Map<Integer, Integer> gradeMergeMap = dataStatisticsLogic.getMergeMap(gradeIds);
        Map<Integer, Integer> classMergeMap = dataStatisticsLogic.getMergeMap(classIds);

        log.info("[老师点评榜下载]-排序后数据 teacherEvaluateSheet1s {}", JSONUtil.toJsonStr(teacherEvaluateSheet1s));
        log.info("[老师点评榜下载]-合并的数据 gradeMergeMap {}，classMergeMap {}", JSONUtil.toJsonStr(gradeMergeMap), JSONUtil.toJsonStr(classMergeMap));
        teacherEvaluateExcel.setTeacherEvaluateSheet1List(teacherEvaluateSheet1s);
        teacherEvaluateExcel.setGradeMergeMap(gradeMergeMap);
        teacherEvaluateExcel.setClassMergeMap(classMergeMap);
        log.info("[老师点评榜下载]-[sheet1数据准备]-1.7，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        return teacherEvaluateExcel;
    }

    private TeacherEvaluateSheet1 getTeacherEvaluateSheet1(EduOrgTreeVO eduOrgTreeVO, StaffFullInfoVO staffFullInfoVO, String gradeName) {
        TeacherEvaluateSheet1 teacherEvaluateSheet1 = new TeacherEvaluateSheet1();
        teacherEvaluateSheet1.setClassName(eduOrgTreeVO.getName());
        teacherEvaluateSheet1.setClassId(eduOrgTreeVO.getId());
        teacherEvaluateSheet1.setGradeName(gradeName);
        teacherEvaluateSheet1.setGradeId(eduOrgTreeVO.getParentId());
        if (staffFullInfoVO != null) {
            teacherEvaluateSheet1.setStaffName(staffFullInfoVO.getName());
            teacherEvaluateSheet1.setStaffId(staffFullInfoVO.getStaffId());
        }
        return teacherEvaluateSheet1;
    }

    private BigDecimal getAverageDayNum(TeacherEvaluateDataStatisticsQuery query, String id, Map<String, List<StaffDailyStatisticsVO>> behaviourMap, Integer idType, TeacherEvaluateSheet1 sheet1) {
        if (ObjectUtil.isEmpty(id) || CollUtil.isEmpty(behaviourMap)) {
            return BigDecimal.ZERO;
        }
        Map<String, Integer> targetTeacherNumMap = new HashMap<>();
        List<StaffDailyStatisticsVO> behaviourRecordDTOS = behaviourMap.get(id);
        if (CollUtil.isEmpty(behaviourRecordDTOS)) {
            return BigDecimal.ZERO;
        }
        // 按照提交时间分组
        Map<String, Long> collect = behaviourRecordDTOS.parallelStream().collect(Collectors.groupingBy(item -> item.getStatisticsTime() + StrPool.UNDERLINE + item.getAppraisalId(), Collectors.counting()));
        int totalTeacherNum = collect.size();

        List<StaffFullInfoVO> staffFullInfoVOList = new ArrayList<>();
        // 需点评老师
        if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(idType)) {
            staffFullInfoVOList = this.listTargetConfigTeacherIds(query.getCampusSectionId(), query.getSchoolYear(), Convert.toStr(sheet1.getGradeId()), null);
        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(idType)) {
            staffFullInfoVOList = this.listTargetConfigTeacherIds(query.getCampusSectionId(), query.getSchoolYear(), Convert.toStr(sheet1.getGradeId()), Convert.toStr(sheet1.getClassId()));
        }
        targetTeacherNumMap.put(id, staffFullInfoVOList.size());
        Integer targetTeacherNum = targetTeacherNumMap.get(id);
        if (Objects.equals(targetTeacherNum, Constant.ZERO)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(new BigDecimal(totalTeacherNum).divide(BigDecimal.valueOf(targetTeacherNum), Constant.ONE, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
    }

    /**
     * 获取老师点评榜
     */
    private List<TeacherVO> listTeacherVOS(TeacherEvaluateDataStatisticsQuery query, List<StaffDailyStatisticsVO> staffDailyStatisticsVOList, List<StaffFullInfoVO> staffFullInfoList) {
        List<TeacherVO> teacherVOS = new ArrayList<>();
        if (CollectionUtil.isEmpty(staffFullInfoList)) {
            return teacherVOS;
        }
        List<Long> targetUserStaffIds = staffFullInfoList.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList());

        //从saas获取学段或者年级或者班级所有老师信息
        List<StaffFullInfoVO> staffFullInfoVOS = this.listStaff(Convert.toLong(query.getCampusSectionId()), query.getSchoolYear(), query.getType(), Convert.toLong(query.getGradeId()), Convert.toLong(query.getClassId()));
        if (CollectionUtil.isEmpty(staffFullInfoVOS)) {
            return new ArrayList<>();
        }
        Map<String/*点评人staffId*/, List<StaffDailyStatisticsVO>> teacherBehaviour = staffDailyStatisticsVOList.stream()
                .collect(Collectors.groupingBy(StaffDailyStatisticsVO::getAppraisalId));
        List<Long> staffIdList = staffFullInfoVOS.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList());
        targetUserStaffIds = (List<Long>) CollectionUtil.intersection(staffIdList, targetUserStaffIds);
        // sonar
        StaffFullInfoVO staffFullInfoVO = new StaffFullInfoVO();
        for (Long staffId : targetUserStaffIds) {
            TeacherVO teacherVO = new TeacherVO();
            teacherVO.setTeacherId(Convert.toStr(staffId));

            List<StaffFullInfoVO> filterStaffList = staffFullInfoVOS.stream().filter(s -> s.getStaffId().equals(staffId)).collect(Collectors.toList());
            ;
            if (CollectionUtil.isNotEmpty(filterStaffList)) {
                staffFullInfoVO = filterStaffList.get(0);
            }
            teacherVO.setTeacherName(staffFullInfoVO.getName());

            int evaluateNum = 0;
            int dayNum = 0;
            if (CollectionUtil.isNotEmpty(teacherBehaviour) && teacherBehaviour.containsKey(Convert.toStr(staffId))) {
                List<StaffDailyStatisticsVO> staffDailyStatisticsVOList1 = teacherBehaviour.get(Convert.toStr(staffId));
                if (CollectionUtil.isNotEmpty(staffDailyStatisticsVOList1)) {
                    evaluateNum = staffDailyStatisticsVOList1.stream().mapToInt(StaffDailyStatisticsVO::getAppraisalCount).sum();
                    dayNum = staffDailyStatisticsVOList1.stream().collect(Collectors.groupingBy(StaffDailyStatisticsVO::getStatisticsTime)).keySet().size();
                }
            }
            teacherVO.setEvaluateNum(evaluateNum);
            teacherVO.setDayNum(dayNum);
            teacherVO.setIconUrl(staffFullInfoVO.getAvatar());
            if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(query.getType())) {
                List<GradeFullInfoVO> gradeList = staffFullInfoVO.getGradeList();
                log.info("获取老师点评榜,staffId:{},gradeList:{}",staffId,JSONUtil.toJsonStr(gradeList));
                if (CollectionUtil.isNotEmpty(gradeList)) {
                    teacherVO.setGradeOrClass(gradeList.stream().filter(Objects::nonNull).map(GradeFullInfoVO::getGradeName).collect(Collectors.joining(Constant.CAESURA_SIGN)));
                }
            } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(query.getType())) {
                List<ClassFullInfoVO> classList = staffFullInfoVO.getClassList();
                log.info("获取老师点评榜,staffId:{},classList:{}",staffId,JSONUtil.toJsonStr(classList));
                if (CollectionUtil.isNotEmpty(classList)) {
                    teacherVO.setGradeOrClass(classList.stream().filter(Objects::nonNull).map(ClassFullInfoVO::getClassName).collect(Collectors.joining(Constant.CAESURA_SIGN)));
                }
            }
            teacherVOS.add(teacherVO);
        }
        return teacherVOS;
    }

    /**
     * 获取指标排行榜的所有数据
     */
    private List<TargetRankVO> listAllTargetRank(List<TargetDTO> targets, List<StaffDailyStatisticsVO> staffDailyStatisticsVOList) {
        if (CollUtil.isEmpty(targets)) {
            return Collections.emptyList();
        }
        Set<Long> targetIds = new HashSet<>();
        Map<Long, TargetDTO> targetInfoMap = targets.stream().peek(item -> {
            targetIds.add(item.getId());
        }).collect(Collectors.toMap(TargetDTO::getId, Function.identity(), (o1, o2) -> o1));

        // 根据指标分组(需要过滤出启用且未删除得指标)
        Map<Long, Integer> targetNumMap = staffDailyStatisticsVOList.stream()
                .filter(item -> targetIds.contains(item.getTargetId()))
                .collect(Collectors.groupingBy(StaffDailyStatisticsVO::getTargetId, Collectors.summingInt(item -> {
                    if (item.getAppraisalCount() == null) return 0;
                    return item.getAppraisalCount();
                })));

        // 数据封装
        List<TargetRankVO> targetRankList = new ArrayList<>();
        for (Long targetId : targetIds) {
            TargetRankVO targetRank = this.getTargetRank(targetInfoMap, targetId, targetNumMap);
            if (Objects.nonNull(targetRank)) {
                targetRankList.add(targetRank);
            }
        }
        return targetRankList.stream().sorted(Comparator
                .comparing(TargetRankVO::getCount, Comparator.nullsFirst(Integer::compareTo)).reversed()
                .thenComparing(TargetRankVO::getCreateTime, Comparator.nullsFirst(Date::compareTo)).reversed().reversed()).collect(Collectors.toList());
    }

    private TargetRankVO getTargetRank(Map<Long, TargetDTO> targetInfoMap, Long targetId, Map<Long, Integer> targetNumMap) {
        TargetRankVO targetRank = new TargetRankVO();
        if (!targetInfoMap.containsKey(targetId)) {
            return null;
        }
        TargetDTO targetDTO = targetInfoMap.get(targetId);
        if (targetDTO == null) {
            return null;
        }
        targetRank.setTargetId(Convert.toStr(targetId));
        targetRank.setTargetName(targetDTO.getTargetName());
        targetRank.setModuleCode(targetDTO.getModuleCode());
        targetRank.setModuleName(ModuleEnum.getModuleName(targetRank.getModuleCode()));
        targetRank.setGroupName(targetDTO.getGroupName());
        targetRank.setCreateTime(targetDTO.getCreateTime());
        targetRank.setCount(targetNumMap.getOrDefault(targetId, 0));
        return targetRank;
    }

    /**
     * 获取对应百分比
     *
     * @param divider
     * @param dividee
     * @param scale
     * @return
     */
    private BigDecimal getRate(long divider, int dividee, int scale) {
        return divider == 0L || dividee == 0 ? BigDecimal.ZERO : new BigDecimal(divider).divide(new BigDecimal(dividee), scale, RoundingMode.HALF_UP);
    }

    private void buildClassType(DataStatisticsTeacherParticipationRateVO rateVO, List<String> listTargetConfigTeacherIds, Map<String, List<StaffDailyStatisticsVO>> teacherEvaluateNumMap, Map<String, List<StaffFullInfoVO>> staffIdMap) {
        //需点评老师
        if (CollectionUtil.isEmpty(listTargetConfigTeacherIds)) return;
        ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
        long totalDay = 0;
        for (String teacherId : listTargetConfigTeacherIds) {
            NameAndValueVO nameAndValueVO = new NameAndValueVO();
            //每个老师的天数
            List<StaffDailyStatisticsVO> staffDailyStatisticsVOList = teacherEvaluateNumMap.getOrDefault(teacherId, new ArrayList<>());
            long size = staffDailyStatisticsVOList.stream().collect(Collectors.groupingBy(StaffDailyStatisticsVO::getStatisticsTime, Collectors.counting())).size();
            totalDay += size;
            if (staffIdMap.containsKey(teacherId)) {
                List<StaffFullInfoVO> staffList = staffIdMap.get(teacherId);
                if (CollUtil.isNotEmpty(staffList)) {
                    nameAndValueVO.setName(staffList.get(Constant.ZERO).getName());
                }
            }
            nameAndValueVO.setValue(Convert.toStr(size));
            nameAndValueVOS.add(nameAndValueVO);
        }
        //智能建议
        //平均天数
        BigDecimal avgDay = this.getRate(totalDay, listTargetConfigTeacherIds.size(), Constant.ONE).stripTrailingZeros();
        rateVO.setAverage(Convert.toStr(avgDay));
        List<String> teacherNames = nameAndValueVOS.stream().filter(s -> BigDecimal.valueOf(Convert.toLong(s.getValue())).compareTo(avgDay) == Constant.NEGATIVE_ONE).map(NameAndValueVO::getName).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(teacherNames)) {
            rateVO.setIntelligentSuggestion(null);
        } else {
            rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, teacherNames) + MessageFormat.format(CLASS_PARTICIPATION_SUGGESTION, avgDay));
        }
        nameAndValueVOS = (ArrayList<NameAndValueVO>) nameAndValueVOS.stream().sorted(Comparator.comparing(NameAndValueVO::getValue, Comparator.comparingDouble(Double::parseDouble)).reversed()).collect(Collectors.toList());
        rateVO.setChildrenRate(nameAndValueVOS);
    }

    private void buildGradeType(DataStatisticsTeacherParticipationRateVO rateVO, List<String> listTargetConfigTeacherIds, List<EduOrgTreeVO> eduOrgTreeVOS, Map<String, List<String>> classAppraisalIdMap, List<StaffFullInfoVO> staffBatchVOList) {
        ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
        BigDecimal totalRate = BigDecimal.ZERO;
        if (CollUtil.isEmpty(eduOrgTreeVOS) || CollUtil.isEmpty(eduOrgTreeVOS.get(0).getChildren())) {
            return;
        }
        //年级下的班级
        Map<Long/*教务组织ID*/, String/*教务组织名称*/> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));
        if (CollectionUtil.isEmpty(idAndName)) {
            return;
        }
        for (Map.Entry<Long, String> next : idAndName.entrySet()) {
            //该班级下的点评数据按老师分组
            List<String> appraisalIdList = new ArrayList<>();
            String classId = Convert.toStr(next.getKey());
            if (classAppraisalIdMap.containsKey(classId)) {
                appraisalIdList = classAppraisalIdMap.get(classId);
            }
            NameAndValueVO nameAndValueVO = new NameAndValueVO();
            nameAndValueVO.setName(next.getValue());
            //该班级配置了点评项的老师
            List<StaffFullInfoVO> staffFullInfoVOS = staffBatchVOList.stream()
                    .filter(s -> CollUtil.isNotEmpty(s.getClassList()) && s.getClassList().stream().map(ClassFullInfoVO::getClassId).collect(Collectors.toList()).contains(next.getKey()))
                    .collect(Collectors.toList());
            List<String> staffIdList = Convert.toList(String.class, staffFullInfoVOS.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList()));
            //该班级点评过的老师
            Collection<String> intersection = CollectionUtil.intersection(appraisalIdList, staffIdList);
            int teacherCount = CollectionUtil.intersection(listTargetConfigTeacherIds, staffIdList).size();
            //老师参与率
            BigDecimal teacherRate = this.getRate(intersection.size(), teacherCount, Constant.THREE).stripTrailingZeros();
            totalRate = totalRate.add(teacherRate);
            nameAndValueVO.setValue(Constant.ZERO.equals(teacherCount) ? Constant.ZERO + Constant.PERCENT : teacherRate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
            nameAndValueVOS.add(nameAndValueVO);
        }
        //智能建议
        //平均参与率
        BigDecimal avgRate = totalRate.divide(BigDecimal.valueOf(idAndName.keySet().size()), Constant.THREE, RoundingMode.HALF_UP);
        rateVO.setAverage(Convert.toStr(avgRate));
        List<String> classNames = nameAndValueVOS.stream().filter(s -> {
            try {
                return new BigDecimal(Convert.toStr(NumberFormat.getPercentInstance().parse(s.getValue()))).compareTo(avgRate) == Constant.NEGATIVE_ONE;
            } catch (ParseException e) {
                log.error("老师参与率转换错误", e);
            }
            return false;
        }).map(NameAndValueVO::getName).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(classNames)) {
            rateVO.setIntelligentSuggestion(null);
        } else {
            NumberFormat percentInstance = NumberFormat.getPercentInstance();
            percentInstance.setMaximumIntegerDigits(Constant.TWO);
            percentInstance.setMaximumFractionDigits(Constant.ONE);
            rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, classNames) + MessageFormat.format(GRADE_PARTICIPATION_SUGGESTION, percentInstance.format(avgRate)));
        }
        rateVO.setChildrenRate(nameAndValueVOS);
    }

    private void buildSectionType(DataStatisticsTeacherParticipationRateVO rateVO, List<String> listTargetConfigTeacherIds, List<EduOrgTreeVO> eduOrgTreeVOS, Map<String, List<String>> gradeAppraisalIdMap, List<StaffFullInfoVO> staffBatchVOList) {
        ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
        BigDecimal totalRate = BigDecimal.ZERO;
        if (CollUtil.isEmpty(eduOrgTreeVOS) || CollUtil.isEmpty(eduOrgTreeVOS.get(0).getChildren())) {
            return;
        }
        //校区下的年级
        Map<Long/*年级ID*/, String/*年级名称*/> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));
        if (CollectionUtil.isEmpty(idAndName)) {
            return;
        }
        for (Map.Entry<Long/*年级ID*/, String/*年级名称*/> next : idAndName.entrySet()) {
            //该学段的点评数据按年级分组
            List<String> appraisalIdList = new ArrayList<>();
            String gradeId = Convert.toStr(next.getKey());
            if (gradeAppraisalIdMap.containsKey(gradeId)) {
                appraisalIdList = gradeAppraisalIdMap.get(gradeId);
            }
            NameAndValueVO nameAndValueVO = new NameAndValueVO();
            nameAndValueVO.setName(next.getValue());
            //该年级配置了点评项的老师
            List<StaffFullInfoVO> staffFullInfoVOS = staffBatchVOList.stream()
                    .filter(s -> CollUtil.isNotEmpty(s.getGradeList()) && s.getGradeList().stream().map(GradeFullInfoVO::getGradeId).collect(Collectors.toList()).contains(next.getKey()))
                    .collect(Collectors.toList());
            List<String> staffIdList = Convert.toList(String.class, staffFullInfoVOS.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList()));
            //该年级点评过的老师
            Collection<String> intersection = CollectionUtil.intersection(appraisalIdList, staffIdList);
            int teacherCount = CollectionUtil.intersection(listTargetConfigTeacherIds, staffIdList).size();
            //老师参与率
            BigDecimal teacherRate = this.getRate(intersection.size(), teacherCount, Constant.THREE).stripTrailingZeros();
            totalRate = totalRate.add(teacherRate);
            nameAndValueVO.setValue(Constant.ZERO.equals(teacherCount) ? Constant.ZERO + Constant.PERCENT : teacherRate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
            nameAndValueVOS.add(nameAndValueVO);
        }
        //智能建议
        //平均参与率
        BigDecimal avgRate = totalRate.divide(BigDecimal.valueOf(idAndName.keySet().size()), Constant.THREE, RoundingMode.HALF_UP);
        rateVO.setAverage(Convert.toStr(avgRate));
        List<String> gradeNames = nameAndValueVOS.stream().filter(s -> {
            try {
                return new BigDecimal(Convert.toStr(NumberFormat.getPercentInstance().parse(s.getValue()).toString())).compareTo(avgRate) == Constant.NEGATIVE_ONE;
            } catch (ParseException e) {
                log.error("老师参与率转换错误", e);
            }
            return false;
        }).map(NameAndValueVO::getName).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(gradeNames)) {
            rateVO.setIntelligentSuggestion(null);
        } else {
            NumberFormat percentInstance = NumberFormat.getPercentInstance();
            percentInstance.setMaximumIntegerDigits(Constant.TWO);
            percentInstance.setMaximumFractionDigits(Constant.ONE);
            rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, gradeNames) + MessageFormat.format(GRADE_PARTICIPATION_SUGGESTION, percentInstance.format(avgRate)));
        }
        rateVO.setChildrenRate(nameAndValueVOS);
    }

    /**
     * 根据行为记录统计每个五育模块的占比
     */
    private List<FiveEducationDistributionVO> encapsulationFiveEducation(List<StaffDailyStatisticsVO> staffDailyStatisticsList, String businessId, String businessName, Map<Integer, Integer> countModuleMap) {
        // 总分
        BigDecimal totalScore = staffDailyStatisticsList.stream().map(item -> {
            BigDecimal plusTotalScore = item.getPlusTotalScore();
            BigDecimal minusTotalScore = item.getMinusTotalScore();
            if (plusTotalScore == null) {
                plusTotalScore = BigDecimal.ZERO;
            }
            if (minusTotalScore == null) {
                minusTotalScore = BigDecimal.ZERO;
            }
            return plusTotalScore.subtract(minusTotalScore);
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 如果所有分值相加为0
        if (ObjectUtil.equals(BigDecimal.ZERO, totalScore)) {
            log.info("五育分布-计算每个模块占比-总分为空");
            return new ArrayList<>();
        }
        // 根据五育分组
        Map<Integer, List<StaffDailyStatisticsVO>> groupByModuleMap = staffDailyStatisticsList.stream().collect(Collectors.groupingBy(StaffDailyStatisticsVO::getModuleCode));

        int num = 1;
        // 最后一个模块的占比计算(为了保证所有占比相加为1)
        BigDecimal lastProportion = BigDecimal.ONE;
        // 五育分布明细
        List<FiveEducationDistributionVO> distributions = new ArrayList<>();
        Map<Integer, String> moduleMap = ModuleEnum.normalModuleMap;
        for (Integer key : moduleMap.keySet()) {
            List<StaffDailyStatisticsVO> staffDailyModuleList = groupByModuleMap.get(key);
            FiveEducationDistributionVO fiveEducationDistributionVO;
            // 如果某个模块明细数据不存在
            if (CollUtil.isEmpty(staffDailyModuleList)) {
                distributions.add(this.singleFiveEducationDistribution(BigDecimal.ZERO, BigDecimal.ZERO, key, countModuleMap, businessId, businessName));
                lastProportion = lastProportion.subtract(BigDecimal.ZERO);
                num++;
                continue;
            }
            // 表扬总分值
            BigDecimal praise = staffDailyModuleList.stream().map(StaffDailyStatisticsVO::getPlusTotalScore).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 待改进总分值（负值）
            BigDecimal needImprove = staffDailyModuleList.stream().map(StaffDailyStatisticsVO::getMinusTotalScore).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 当前五育占比（ 表扬 -（-待改进））/ 总分
            BigDecimal moduleProportion = praise.subtract(needImprove).divide(totalScore.abs(), 3, RoundingMode.HALF_UP);

            fiveEducationDistributionVO = this.singleFiveEducationDistribution(praise, needImprove, key, countModuleMap, businessId, businessName);
            // 如果相等,说明是最后一个模块
            if (moduleMap.size() == num) {
                fiveEducationDistributionVO.setProportion(lastProportion.multiply(Convert.toBigDecimal(100)));
            } else {
                fiveEducationDistributionVO.setProportion(moduleProportion.multiply(Convert.toBigDecimal(100)));
            }
            distributions.add(fiveEducationDistributionVO);
            lastProportion = lastProportion.subtract(moduleProportion);
            num++;
        }
        return distributions;
    }

    private FiveEducationDistributionVO singleFiveEducationDistribution(BigDecimal praise, BigDecimal needImprove, Integer key, Map<Integer, Integer> countModuleMap, String businessId, String businessName) {
        FiveEducationDistributionVO fiveEducationDistributionVO = new FiveEducationDistributionVO();
        fiveEducationDistributionVO.setExtraScore(praise);
        fiveEducationDistributionVO.setSubtractScore(needImprove);
        if (Objects.nonNull(countModuleMap) && countModuleMap.containsKey(key)) {
            fiveEducationDistributionVO.setCommentCount(countModuleMap.get(key));
        }
        fiveEducationDistributionVO.setModuleCode(key);
        fiveEducationDistributionVO.setModuleName(ModuleEnum.getModuleName(key));
        fiveEducationDistributionVO.setBusinessId(StrUtil.isNotBlank(businessId) ? businessId : null);
        fiveEducationDistributionVO.setBusinessName(StrUtil.isNotBlank(businessName) ? businessName : null);
        fiveEducationDistributionVO.setProportion(BigDecimal.ZERO);
        return fiveEducationDistributionVO;
    }

    private TargetCoverageVO targetCoverageFillTitle(Integer type, String title, String detailTitle, String currentBusinessName) {
        // 学段
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
            title = "全校" + title;
            detailTitle = "全校" + detailTitle;
        }
        // 年级
        if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
            title = currentBusinessName + title;
            detailTitle = currentBusinessName + detailTitle;
        }
        // 班级
        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
            title = currentBusinessName + title;
            detailTitle = currentBusinessName + detailTitle;
        }
        // saas学段/年级/班级信息
        TargetCoverageVO targetCoverageVO = new TargetCoverageVO();
        targetCoverageVO.setEmptyFlag(Constant.NO);
        targetCoverageVO.setTitle(title);
        targetCoverageVO.setDetailTitle(detailTitle);
        return targetCoverageVO;
    }

    private FiveEducationVO commonFillTitle(Integer type, String title, String detailTitle, String currentBusinessName) {
        FiveEducationVO fiveEducationVO = new FiveEducationVO();
        // 学段
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
            title = "全校" + title;
            detailTitle = "各年级" + detailTitle;
        }
        // 年级
        if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
            title = currentBusinessName + title;
            detailTitle = "各班级" + detailTitle;
        }
        // 班级
        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
            title = currentBusinessName + title;
            detailTitle = "各老师" + detailTitle;
        }
        fiveEducationVO.setEmptyFlag(Constant.NO);
        fiveEducationVO.setTitle(title);
        fiveEducationVO.setDetailTitle(detailTitle);
        return fiveEducationVO;
    }

    private DataStatisticsEvaluateNumVO evaluateStatisticsFillTitle(Integer type, String name) {
        DataStatisticsEvaluateNumVO evaluateNumVO = new DataStatisticsEvaluateNumVO();
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
            evaluateNumVO.setTitle(SCHOOL_EVALUATE_TITLE);
            evaluateNumVO.setChildrenTitle(SCHOOL_EVALUATE_CHILDREN_TITLE);
        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
            evaluateNumVO.setTitle(MessageFormat.format(GRADE_EVALUATE_TITLE, name));
            evaluateNumVO.setChildrenTitle(GRADE_EVALUATE_CHILDREN_TITLE);
        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
            evaluateNumVO.setTitle(MessageFormat.format(CLASS_EVALUATE_TITLE, name));
            evaluateNumVO.setChildrenTitle(CLASS_EVALUATE_CHILDREN_TITLE);
        }
        return evaluateNumVO;
    }

    private DataStatisticsTeacherParticipationRateVO participationRateFillTitle(Integer type, String name) {
        DataStatisticsTeacherParticipationRateVO rateVO = new DataStatisticsTeacherParticipationRateVO();
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
            rateVO.setTitle(SCHOOL_TITLE);
            rateVO.setChildrenTitle(SCHOOL_CHILDREN_TITLE);
        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
            rateVO.setTitle(MessageFormat.format(GRADE_TITLE, name));
            rateVO.setChildrenTitle(GRADE_CHILDREN_TITLE);
        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
            rateVO.setTitle(MessageFormat.format(CLASS_TITLE, name));
            rateVO.setChildrenTitle(CLASS_CHILDREN_TITLE);
        }
        return rateVO;
    }
    /**
     * 从T+1归档表中获取点评记录（带权限校验）
     *
     * @return 点评记录集合
     */
    protected List<StaffDailyStatisticsVO> getRecordOld(String campusSectionId,
                                                     String schoolYear, Boolean isCurrentYear, String gradeId, String classId,
                                                     Date startTime, Date endTime) {
        CheckPermissionVo permissionVo = basicInfoService.checkPermission(
                Convert.toLong(campusSectionId),
                schoolYear,
                isCurrentYear,
                gradeId,
                classId,
                startTime);

        if (permissionVo == null || !Objects.equals(Boolean.TRUE, permissionVo.getHasPermission())) {
            return new ArrayList<>();
        }
        return staffDailyStatisticsManager.listStaffDailyStatistics(
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                campusSectionId,
                schoolYear,
                permissionVo.getGradeIdList(),
                permissionVo.getClassIdList(),
                startTime,
                endTime);
    }
    /**
     * 从T+1归档表中获取点评记录（带权限校验）
     *
     * @return 点评记录集合
     */
    protected List<StaffDailyStatisticsVO> getRecord(String campusSectionId,
                                                     String schoolYear, Boolean isCurrentYear, String gradeId, String classId,
                                                     Date startTime, Date endTime) {
        /*CheckPermissionVo permissionVo = basicInfoService.checkPermission(
                Convert.toLong(campusSectionId),
                schoolYear,
                isCurrentYear,
                gradeId,
                classId,
                startTime);

        if (permissionVo == null || !Objects.equals(Boolean.TRUE, permissionVo.getHasPermission())) {
            return new ArrayList<>();
        }*/
        List<String> gradeIds = new ArrayList<>();
        List<String> classIds = new ArrayList<>();
        EduAuthVO currentStaffAuth = basicInfoService.getCurrentStaffAuth(WebUtil.getStaffId(), schoolYear, null);
        Assert.isTrue(ObjectUtil.isNotEmpty(currentStaffAuth), () -> new BizException(BaseErrorCode.NO_DATA_AUTH));
        // 普通用户权限过滤
        if (!currentStaffAuth.getIsAdmin()){
            gradeIds = StrUtil.isEmpty(gradeId) ? currentStaffAuth.getGradeIdStrs() : Arrays.asList(gradeId);
            classIds = StrUtil.isEmpty(classId) ? currentStaffAuth.getClassIdStrs() : Arrays.asList(classId);
        }else{
            //管理员根据查询参数过滤
            if(StrUtil.isNotBlank(gradeId)){
                gradeIds = Arrays.asList(gradeId);
            }
            if(StrUtil.isNotBlank(classId)){
                classIds = Arrays.asList(classId);
            }
        }
        return staffDailyStatisticsManager.listStaffDailyStatistics(
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                campusSectionId,
                schoolYear,
                gradeIds,
                classIds,
                startTime,
                endTime);
    }

    /**
     * 根据学段/年级/班级查询点评过的老师id集合
     *
     * @return 教师ID集合
     */
    private List<String> listTeacherIds(String campusSectionId, String schoolYear, String gradeId, String classId, Date startTime, Date endTime) {
        // 历史点评过的老师id集合
        return staffDailyStatisticsManager.listHistoryTeacherIds(WebUtil.getSchoolId(), WebUtil.getCampusId(), campusSectionId, schoolYear, gradeId, classId, startTime, endTime);
    }

    /**
     * 从saas获取该学校下学段及以下的教务组织架构树形（支持当前学年和历史学年）
     *
     * @return
     */
    private List<EduOrgTreeVO> listEduOrgTree(Integer type,
                                              Integer isTree,
                                              String campusSectionId,
                                              String schoolYear,
                                              String gradeId,
                                              String classId) {

        EduOrgQueryV2DTO eduOrgQueryV2DTO = new EduOrgQueryV2DTO();

        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
            eduOrgQueryV2DTO.setCurrentId(Convert.toLong(campusSectionId));
        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
            eduOrgQueryV2DTO.setCurrentId(Convert.toLong(gradeId));
        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
            eduOrgQueryV2DTO.setCurrentId(Convert.toLong(classId));
        }
        eduOrgQueryV2DTO.setCurrentIdType(type);
        eduOrgQueryV2DTO.setSchoolYear(schoolYear);
        eduOrgQueryV2DTO.setIsTree(isTree);
        List<EduOrgTreeV2VO> eduOrgTreeV2VOList = saasSchoolCacheManager.queryHistoryEducationalOrgTree(eduOrgQueryV2DTO);
        return BasicConvert.INSTANCE.toEduOrgTreeVOList(eduOrgTreeV2VOList);
    }

    /**
     * 根据(学段/年级/班级)获取对应的(年级/班级/老师)列表
     */
    private SaasCurrentInfoDTO listBusinessIdByBusinessType(TeacherEvaluateDataStatisticsQuery query) {
        SaasCurrentInfoDTO saasCurrentInfoDTO = new SaasCurrentInfoDTO();
        saasCurrentInfoDTO.setBusinessMap(new HashMap<>());
        saasCurrentInfoDTO.setSortBusinessKey(new ArrayList<>());

        // 根据(学段/年级/班级)获取对应的(年级/班级/老师)列表
        Map<String, String> businessMap = new HashMap<>();
        // 业务类型顺序
        List<String> sortBusinessKey = new ArrayList<>();
        // 当前学段/年级/班级信息
        String currentBusinessName = "";

        List<EduOrgTreeVO> eduOrgTreeVOS = this.listEduOrgTree(query.getType(), Constant.ONE, query.getCampusSectionId(), query.getSchoolYear(), query.getGradeId(), query.getClassId());
        // 如果是学段,查询出学段下有哪些年级;如果是年级,查询出年级下有哪些班级
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(query.getType()) || SaasCurrentIdTypeEnum.GRADE.getCode().equals(query.getType())) {
            if (CollUtil.isNotEmpty(eduOrgTreeVOS)) {
                EduOrgTreeVO eduOrgTreeVO = CollUtil.getFirst(eduOrgTreeVOS);
                if (Objects.nonNull(eduOrgTreeVO.getChildren())) {
                    businessMap = eduOrgTreeVO.getChildren().stream().collect(Collectors.toMap(s -> Convert.toStr(s.getId()), EduOrgTreeVO::getName));
                    sortBusinessKey = eduOrgTreeVO.getChildren().stream().map(s -> Convert.toStr(s.getId())).collect(Collectors.toList());
                }
                currentBusinessName = eduOrgTreeVO.getName();
            }
        }
        // 如果是班级,查询出班级下有哪些老师
        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(query.getType())) {
            TreeMap<String, String> teacherTreeMap = this.listStaffTreeMap(Convert.toLong(query.getCampusSectionId()), query.getSchoolYear(), Convert.toLong(query.getGradeId()), Convert.toLong(query.getClassId()));
            businessMap = teacherTreeMap;
            sortBusinessKey = new ArrayList<>(teacherTreeMap.keySet());
            currentBusinessName = CollUtil.isEmpty(eduOrgTreeVOS) ? StringUtils.EMPTY : eduOrgTreeVOS.get(0).getName();
        }
        saasCurrentInfoDTO.setBusinessMap(businessMap);
        saasCurrentInfoDTO.setSortBusinessKey(sortBusinessKey);
        saasCurrentInfoDTO.setCurrentBusinessName(currentBusinessName);
        return saasCurrentInfoDTO;
    }

    private Map<String, List<StaffDailyStatisticsVO>> getBehaviourGroupByCurrentIdTyp(Map<String, String> businessMap, List<StaffDailyStatisticsVO> staffDailyStatisticsList, Integer type) {
        // 行为记录中的所有的年级,班级,老师都需要在saas中存在
        Map<String, List<StaffDailyStatisticsVO>> groupMap = new HashMap<>();
        // 学段下的年级分组
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
            groupMap = staffDailyStatisticsList.stream().filter(s -> businessMap.containsKey(s.getGradeId())).collect(Collectors.groupingBy(StaffDailyStatisticsVO::getGradeId));
        }
        // 年级下的班级分组
        if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
            groupMap = staffDailyStatisticsList.stream().filter(s -> businessMap.containsKey(s.getClassId())).collect(Collectors.groupingBy(StaffDailyStatisticsVO::getClassId));
        }
        // 班级下的老师分组
        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
            groupMap = staffDailyStatisticsList.stream().filter(s -> businessMap.containsKey(s.getAppraisalId())).collect(Collectors.groupingBy(StaffDailyStatisticsVO::getAppraisalId));
        }
        return groupMap;
    }

    /**
     * 多个BigDecimal字段遍历一次求和
     *
     * @return 集合中三个字段各自和的数组
     */
    private BigDecimal[] multiKeySum(List<StaffDailyStatisticsVO> staffDailyStatisticsList) {
        if (CollUtil.isEmpty(staffDailyStatisticsList)) return initArr();
        // 自定义收集器，遍历一次集合计算多个BigDecimal的字段和
        return staffDailyStatisticsList.stream().collect(Collector.of(this::initArr, (acc, item) -> {
                    if (Objects.isNull(item)) return;
                    acc[0] = acc[0].add(item.getTotalScore() == null ? BigDecimal.ZERO : item.getTotalScore());
                    acc[1] = acc[1].add(item.getPlusTotalScore() == null ? BigDecimal.ZERO : item.getPlusTotalScore());
                    acc[2] = acc[2].add(item.getMinusTotalScore() == null ? BigDecimal.ZERO : item.getMinusTotalScore());
                }, (left, right) -> { // 组合器
                    left[0] = left[0].add(right[0]);
                    left[1] = left[1].add(right[1]);
                    left[2] = left[2].add(right[2]);
                    return left;
                }, acc -> acc)
        );
    }

    private BigDecimal[] initArr() {
        return new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
    }

    /**
     * 学生点评覆盖率
     *
     * @param query
     * @return
     */
    @SneakyThrows
    public DataStatisticsStudentEvaluateRateVO getStudentEvaluateRate(DataStatisticsQuery query) {

        TimeInterval TIME_INTERVAL = DateUtil.timer();


        DataStatisticsStudentEvaluateRateVO result = new DataStatisticsStudentEvaluateRateVO();

        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(query.getType())) {
            result.setTitle(SCHOOL_STUDENT_EVALUATE_TITLE);
            result.setChildrenTitle(SCHOOL_STUDENT_EVALUATE_CHILDREN_TITLE);
        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(query.getType())) {
            result.setTitle(MessageFormat.format(GRADE_STUDENT_EVALUATE_TITLE, query.getName()));
            result.setChildrenTitle(GRADE_STUDENT_EVALUATE_CHILDREN_TITLE);
        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(query.getType())) {
            result.setTitle(MessageFormat.format(CLASS_STUDENT_EVALUATE_TITLE, query.getName()));
            result.setChildrenTitle(CLASS_STUDENT_EVALUATE_CHILDREN_TITLE);
        }
        List<String> gradeIds = new ArrayList<>();
        List<String> classIds = new ArrayList<>();

        EduAuthVO currentStaffAuth = basicInfoService.getCurrentStaffAuth(WebUtil.getStaffId(), query.getSchoolYear(), query.getTermName());
        Assert.isTrue(ObjectUtil.isNotEmpty(currentStaffAuth), () -> new BizException(BaseErrorCode.NO_DATA_AUTH));
        // 普通用户权限过滤
        if (!currentStaffAuth.getIsAdmin()){
            gradeIds = StringUtils.equals("-1", query.getGradeId()) ? currentStaffAuth.getGradeIdStrs() : Arrays.asList(query.getGradeId());
            classIds = StringUtils.equals("-1", query.getClassId()) ? currentStaffAuth.getClassIdStrs() : Arrays.asList(query.getClassId());
        }
        //获取检索时间范围内，学生统计维度表中，学生统计点评数据
        List<StudentDailyStatisticsDTO> studentDailyStatisticsDTOS = studentDailyStatisticsManager.listStudentDailyStatisticsBatchV2(
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                query.getCampusSectionId(),
                query.getSchoolYear(),
                null,
                null,
                gradeIds,
                classIds,
                query.getStartTime(),
                query.getEndTime()
        );

        int times = studentDailyStatisticsDTOS
                .stream()
                .filter(item -> Objects.nonNull(item.getAppraisalCount()))
                .mapToInt(StudentDailyStatisticsDTO::getAppraisalCount)
                .sum();

        //总的被点评的次数 TODO 此处有优化空间，可以在学生统计表中添加统计次数，需要对历史数据做调整，固放在下一个迭代中优化
//        Integer totalEvaluatedTimes = behaviourHandleManager.countBehaviourRecordNew(
//                WebUtil.getSchoolId(),
//                WebUtil.getCampusId(),
//                query.getCampusSectionId(),
//                gradeId,
//                classId,
//                query.getStartTime(),
//                query.getEndTime(), null);


        log.warn("【学生画像】-【学生点评覆盖率】--【时间段内所有的点评数据阶段】-结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();


        List<SassStudentVO> sassStudentVOS = buildBaseInfoAndListSaasStudentVos(query, result, times,studentDailyStatisticsDTOS);

        log.warn("【学生画像】-【学生点评覆盖率】--【从saas获取的状态为正常的学生阶段】-结束，学生数量：【{}】消耗时长：【{}】",
                sassStudentVOS.size(),
                TIME_INTERVAL.intervalMs());

        TIME_INTERVAL.restart();

        List<String> saasStudentIdList;
        if (CollUtil.isEmpty(sassStudentVOS)) {
            return result;
        } else {
            saasStudentIdList = sassStudentVOS
                    .stream()
                    .map(item -> Convert.toStr(item.getStudentId()))
                    .collect(Collectors.toList());
        }

        //从saas获取该学校下的教务组织架构树形
        List<EduOrgTreeVO> eduOrgTreeVOS = listEduOrgTree(
                query.getType(),
                Constant.ONE,
                query.getCampusSectionId(),
                query.getSchoolYear(),
                query.getGradeId(),
                query.getClassId());

        log.warn("【学生画像】-【学生点评覆盖率】--【从saas获取该学校下的教务组织架构树形阶段】-结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();

        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(query.getType())) {

            getDataStatisticsStudentEvaluateRateVOByClass(
                    query,
                    query.getGradeId(),
                    query.getClassId(),
                    result,
                    saasStudentIdList,
                    nameAndValueVOS);
        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(query.getType())) {
            getDataStatisticsStudentEvaluateRateVOByGrade(
                    query,
                    eduOrgTreeVOS,
                    result,
                    studentDailyStatisticsDTOS,
                    nameAndValueVOS,
                    sassStudentVOS);
        } else if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(query.getType())) {

            getDataStatisticsStudentEvaluateRateVOBySection(
                    query,
                    eduOrgTreeVOS,
                    result,
                    studentDailyStatisticsDTOS,
                    nameAndValueVOS,
                    sassStudentVOS);
        }

        result.setChildrenRate(nameAndValueVOS);
        if (BeanUtil.isEmpty(result)) {
            result.setEmptyFlag(Constant.ONE);
        }
        log.warn("【学生画像】-【学生点评覆盖率】--【最后阶段】-结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());

        return result;
    }

    /**
     * 组装基础数据和返回查询条件下的全部学生列表信息
     *
     * @param query
     * @param result
     * @param totalEvaluatedTimes 总的被点评的次数
     * @param studentDailyStatisticsDTOS
     * @return
     */
    private List<SassStudentVO> buildBaseInfoAndListSaasStudentVos(DataStatisticsQuery query,
                                                                   DataStatisticsStudentEvaluateRateVO result,
                                                                   Integer totalEvaluatedTimes,
                                                                   List<StudentDailyStatisticsDTO> studentDailyStatisticsDTOS) {

        TimeInterval TIME_INTERVAL = DateUtil.timer();

        //从saas获取的状态为正常的学生
        List<SassStudentVO> sassStudentVOS = listTotalSaasStudent(
                Convert.toLong(query.getCampusSectionId()),
                query.getSchoolYear(),
                Convert.toLong(query.getGradeId()),
                Convert.toLong(query.getClassId()));


        List<String> saasStudentIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(sassStudentVOS)) {

            saasStudentIdList = sassStudentVOS
                    .stream()
                    .map(item -> Convert.toStr(item.getStudentId()))
                    .collect(Collectors.toList());
        }

        //学生个数
        int totalStudentNum = saasStudentIdList.size();
        if (ObjectUtil.isNull(totalStudentNum) || Constant.ZERO.equals(totalStudentNum)) {
            result.setEmptyFlag(Constant.ONE);
            return sassStudentVOS;
        }

        List<String> evaluatedStudentIds = studentDailyStatisticsDTOS
                .stream()
                .map(item -> Convert.toStr(item.getStudentId()))
                .distinct()
                .collect(Collectors.toList());

        List<String> noEvaluateStudentIds = (List<String>) CollectionUtil.subtract(saasStudentIdList, evaluatedStudentIds);

        //学生平均被点评=总点评次数/学生总数
        BigDecimal averageEvaluateNum =
                this
                        .getRate(totalEvaluatedTimes, totalStudentNum, Constant.ONE)
                        .stripTrailingZeros();

        //学生点评覆盖率=被点评的学生人数/学生总人数*100%
        BigDecimal rate =
                this
                        .getRate(totalStudentNum - noEvaluateStudentIds.size(), totalStudentNum, Constant.THREE)
                        .stripTrailingZeros();


        // 获取上一段相同时间
        LastSameTimeEvaluateVO lastTimeEvaluate = this.getStudentLastParticipationRate(
                query,
                BigDecimal.valueOf(totalEvaluatedTimes),
                BigDecimal.valueOf(totalStudentNum));

        log.warn("【学生画像】-【学生点评覆盖率】--【获取上一段相同时间阶段】-结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();


        result.setTotalStudentNum(totalStudentNum);
        result.setTotalRate(rate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
        result.setAverageEvaluateNum(averageEvaluateNum);
        result.setNoEvaluateStudentNum(noEvaluateStudentIds.size());
        result.setPercent(lastTimeEvaluate.getPercent());
        result.setLastSameTime(lastTimeEvaluate.getLastSameTime());

        return sassStudentVOS;
    }

    /**
     * 获取学生统计信息根据学段
     *
     * @param dto
     * @param eduOrgTreeVOS
     * @param result
     * @param studentDailyStatisticsDTOS
     * @param nameAndValueVOS
     * @param sassStudentVOS
     * @return
     */
    private void getDataStatisticsStudentEvaluateRateVOBySection(
            DataStatisticsQuery dto,
            List<EduOrgTreeVO> eduOrgTreeVOS,
            DataStatisticsStudentEvaluateRateVO result,
            List<StudentDailyStatisticsDTO> studentDailyStatisticsDTOS,
            ArrayList<NameAndValueVO> nameAndValueVOS,
            List<SassStudentVO> sassStudentVOS) {

        BigDecimal totalRate = BigDecimal.ZERO;
        if (CollUtil.isEmpty(eduOrgTreeVOS) || CollUtil.isEmpty(eduOrgTreeVOS.get(0).getChildren())) {
            return;
        }
        //该学段下的年级
        Map<Long, String> idAndName = eduOrgTreeVOS
                .get(0)
                .getChildren()
                .stream()
                .collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));

        if (CollectionUtil.isEmpty(idAndName)) {
            return;
        }

        Iterator<Map.Entry<Long, String>> iterator = idAndName.entrySet().iterator();
        while (iterator.hasNext()) {
            NameAndValueVO nameAndValueVO = new NameAndValueVO();
            Map.Entry<Long, String> next = iterator.next();
            //该年级点评数据按学生分组
//                Map<String, List<BehaviourRecordDTO>> collect = behaviourRecordDTOS.stream().filter(s -> Convert.toStr(next.getKey()).equals(s.getGradeId())).collect(Collectors.groupingBy(BehaviourRecordDTO::getStudentId));
            List<String> collect = studentDailyStatisticsDTOS.parallelStream().filter(s -> Convert.toStr(next.getKey()).equals(s.getGradeId())).map(StudentDailyStatisticsDTO::getStudentId).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect)) {
                nameAndValueVO.setName(next.getValue());
                nameAndValueVO.setValue(Constant.ZERO + Constant.PERCENT);
                nameAndValueVOS.add(nameAndValueVO);
                continue;
            }
            List<SassStudentVO> classBffVOS = sassStudentVOS.stream().filter(s -> next.getKey().equals(s.getGradeId())).collect(Collectors.toList());
            Integer studentNum = classBffVOS.size();
            //Integer studentNum = eduOrgTreeVOS.get(Constant.ZERO).getChildren().stream().filter(s -> next.getKey().equals(s.getId())).collect(Collectors.toList()).get(0).getStudentNum();
            nameAndValueVO.setName(next.getValue());
            if (ObjectUtil.isNull(studentNum) || Constant.ZERO.equals(studentNum)) {
                nameAndValueVO.setValue(Convert.toStr(BigDecimal.ZERO) + Constant.PERCENT);
                nameAndValueVOS.add(nameAndValueVO);
                continue;
            }
            //点评过的学生
            List<SassStudentVO> studentClassBffVOS1 = this.listByStudentIds(Convert.toLong(dto.getCampusSectionId()), dto.getSchoolYear(), Convert.toList(Long.class, collect));
            List<SassStudentVO> studentClassBffVOS = studentClassBffVOS1;
            if (CollUtil.isNotEmpty(studentClassBffVOS1) && StringUtils.isNotEmpty(studentClassBffVOS1.get(0).getUpgradeStatus())) {
                //当前学年需要过滤
                studentClassBffVOS = studentClassBffVOS1.stream().filter(s -> next.getKey().equals(s.getGradeId()) && Constant.ZERO.equals(Convert.toInt(s.getStudentStatus())) && Constant.ZERO.equals(Convert.toInt(s.getUpgradeStatus())) && Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
            }
            //该班级学生点评覆盖率
            BigDecimal studentRate = this.getRate(studentClassBffVOS.size(), studentNum, Constant.THREE).stripTrailingZeros();
            totalRate = totalRate.add(studentRate);

            BigDecimal multiply = studentRate.multiply(BigDecimal.valueOf(Constant.HUNDRED));
            //跟 100比较大小
            if (multiply.compareTo(BigDecimal.valueOf(Constant.HUNDRED)) == Constant.ONE) {
                multiply = BigDecimal.valueOf(Constant.HUNDRED);
            }
            nameAndValueVO.setValue(multiply.stripTrailingZeros().toPlainString() + Constant.PERCENT);
            nameAndValueVOS.add(nameAndValueVO);
        }
        //智能建议
        //平均学生点评覆盖率
        BigDecimal avgRate = totalRate.divide(BigDecimal.valueOf(idAndName.keySet().size()), Constant.THREE, RoundingMode.HALF_UP);
        result.setAverage(Convert.toStr(avgRate));
        List<String> classNames = nameAndValueVOS.stream().filter(s -> {
            try {
                return new BigDecimal(Convert.toStr(NumberFormat.getPercentInstance().parse(s.getValue()))).compareTo(avgRate) == Constant.NEGATIVE_ONE;
            } catch (ParseException e) {
                log.error("老师点评学生覆盖率转换错误", e);
            }
            return false;
        }).map(NameAndValueVO::getName).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(classNames)) {
            result.setIntelligentSuggestion(null);
        } else {
            NumberFormat percentInstance = NumberFormat.getPercentInstance();
            percentInstance.setMaximumIntegerDigits(Constant.TWO);
            percentInstance.setMaximumFractionDigits(Constant.ONE);
            result.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, classNames) + MessageFormat.format(GRADE_STUDENT_EVALUATE_SUGGESTION, percentInstance.format(avgRate)));
        }
    }

    /**
     * 获取学生统计信息根据年级
     *
     * @param dto
     * @param eduOrgTreeVOS
     * @param rateVO
     * @param nameAndValueVOS
     * @param sassStudentVOS
     * @return
     */
    private void getDataStatisticsStudentEvaluateRateVOByGrade(DataStatisticsQuery dto,
                                                               List<EduOrgTreeVO> eduOrgTreeVOS,
                                                               DataStatisticsStudentEvaluateRateVO rateVO,
                                                               List<StudentDailyStatisticsDTO> studentDailyStatisticsDTOS,
                                                               ArrayList<NameAndValueVO> nameAndValueVOS,
                                                               List<SassStudentVO> sassStudentVOS) {
        BigDecimal totalRate = BigDecimal.ZERO;

        if (CollUtil.isEmpty(eduOrgTreeVOS) || CollUtil.isEmpty(eduOrgTreeVOS.get(0).getChildren())) {
            return;
        }

        //该年级下的班级
        Map<Long, String> idAndName = eduOrgTreeVOS
                .get(0)
                .getChildren()
                .stream()
                .collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName,
                        (k1, k2) -> k1, LinkedHashMap::new));


        if (CollectionUtil.isEmpty(idAndName)) {
            return;
        }
        Iterator<Map.Entry<Long, String>> iterator = idAndName.entrySet().iterator();

        while (iterator.hasNext()) {
            NameAndValueVO nameAndValueVO = new NameAndValueVO();
            Map.Entry<Long, String> next = iterator.next();

            //该班级点评数据按学生分组
            List<String> studentIds = studentDailyStatisticsDTOS
                    .parallelStream()
                    .filter(s -> Convert.toStr(next.getKey()).equals(s.getClassId()))
                    .map(StudentDailyStatisticsDTO::getStudentId)
                    .distinct()
                    .collect(Collectors.toList());


            if (CollectionUtil.isEmpty(studentIds)) {
                nameAndValueVO.setName(next.getValue());
                nameAndValueVO.setValue(Constant.ZERO + Constant.PERCENT);
                nameAndValueVOS.add(nameAndValueVO);
                continue;
            }

            List<SassStudentVO> classBffVOS =
                    sassStudentVOS
                            .stream()
                            .filter(s -> next.getKey().equals(s.getClassId()))
                            .collect(Collectors.toList());

            Integer studentNum = classBffVOS.size();
            //  Integer studentNum = eduOrgTreeVOS.get(Constant.ZERO).getChildren().stream().filter(s -> next.getKey().equals(s.getId())).collect(Collectors.toList()).get(0).getStudentNum();
            nameAndValueVO.setName(next.getValue());
            if (ObjectUtil.isNull(studentNum) || Constant.ZERO.equals(studentNum)) {
                nameAndValueVO.setValue(Convert.toStr(BigDecimal.ZERO) + Constant.PERCENT);
                nameAndValueVOS.add(nameAndValueVO);
                continue;
            }

            //Saas对应学生
            List<SassStudentVO> studentClassBffVOS = this.listByStudentIds(
                    Convert.toLong(dto.getCampusSectionId()),
                    dto.getSchoolYear(),
                    Convert.toList(Long.class, studentIds));


            //该班级学生点评覆盖率
            BigDecimal studentRate = this.getRate(studentClassBffVOS.size(), studentNum, Constant.THREE).stripTrailingZeros();

            totalRate = totalRate.add(studentRate);
            BigDecimal multiply = studentRate.multiply(BigDecimal.valueOf(Constant.HUNDRED));
            //跟 100比较大小
            if (multiply.compareTo(BigDecimal.valueOf(Constant.HUNDRED)) == Constant.ONE) {
                multiply = BigDecimal.valueOf(Constant.HUNDRED);
            }
            nameAndValueVO.setValue(multiply.stripTrailingZeros().toPlainString() + Constant.PERCENT);
            nameAndValueVOS.add(nameAndValueVO);
        }
        //智能建议
        //平均学生点评覆盖率
        BigDecimal avgRate = totalRate.divide(BigDecimal.valueOf(idAndName.keySet().size()), Constant.THREE, RoundingMode.HALF_UP);
        rateVO.setAverage(Convert.toStr(avgRate));
        List<String> classNames = nameAndValueVOS
                .stream()
                .filter(s -> {
                    try {
                        return new BigDecimal(Convert.toStr(NumberFormat.getPercentInstance().parse(s.getValue()))).compareTo(avgRate) == Constant.NEGATIVE_ONE;
                    } catch (ParseException e) {
                        log.error("老师点评学生覆盖率转换错误", e);
                    }
                    return false;
                }).map(NameAndValueVO::getName).collect(Collectors.toList());


        if (CollectionUtil.isEmpty(classNames)) {
            rateVO.setIntelligentSuggestion(null);
        } else {
            NumberFormat percentInstance = NumberFormat.getPercentInstance();
            percentInstance.setMaximumIntegerDigits(Constant.TWO);
            percentInstance.setMaximumFractionDigits(Constant.ONE);
            rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, classNames) + MessageFormat.format(GRADE_STUDENT_EVALUATE_SUGGESTION, percentInstance.format(avgRate)));
        }
    }

    /**
     * 获取学生统计信息根据班级
     *
     * @param query
     * @param gradeId
     * @param classId
     * @param rateVO
     * @param saasStudentIdList
     * @param nameAndValueVOS
     */
    private void getDataStatisticsStudentEvaluateRateVOByClass(
            DataStatisticsQuery query,
            String gradeId,
            String classId,
            DataStatisticsStudentEvaluateRateVO rateVO,
            List<String> saasStudentIdList,
            ArrayList<NameAndValueVO> nameAndValueVOS) {

        TimeInterval TIME_INTERVAL = DateUtil.timer();

        Date endTime = query.getEndTime();
        // 判断endTime是否大于等于今天，如果是，将当前的日期减少一天
        if (endTime != null && endTime.getTime() >= DateUtil.beginOfDay(new Date()).getTime()) {
            endTime = DateUtil.offsetDay(new Date(), -1);

            String formattedEndTime = DateUtil.format(endTime, "yyyy-MM-dd");
            query.setEndTime(formattedEndTime);
        }

        //时间段内所有的点评数据
/*        List<BehaviourRecordDTO> behaviourRecordDTOS = behaviourHandleManager.getRecord(
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                query.getCampusSectionId(),
                gradeId,
                classId,
                query.getStartTime(),
                query.getEndTime(),
                null);*/
        List<BehaviourRecordDTO> behaviourRecordDTOS = dorisBehaviourRecordManager.getRecord(
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                query.getCampusSectionId(),
                gradeId,
                classId,
                query.getStartTime(),
                query.getEndTime(),
                null,null,null);


        // 需点评老师
        List<StaffFullInfoVO> listTargetConfigTeacherList = listTargetConfigTeacherIds(
                query.getCampusSectionId(),
                query.getSchoolYear(),
                gradeId,
                classId);

        log.warn("【学生画像】-【学生点评覆盖率】--【按班级维度查看】--【需点评老师获取阶段】-结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        if (CollectionUtil.isEmpty(listTargetConfigTeacherList)) {
            return;
        }
        // 指标正常且配有点评项的老师ids
        List<String> teacherIdList = listTargetConfigTeacherList
                .stream()
                .map(item -> Convert.toStr(item.getStaffId()))
                .distinct()
                .collect(Collectors.toList());

        BigDecimal studentTotalRate = BigDecimal.ZERO;


        for (String teacherId : teacherIdList) {
            NameAndValueVO nameAndValueVO = new NameAndValueVO();
            //该老师点评过的按学生分组
            List<String> evaluatedStudentIds =
                    behaviourRecordDTOS
                            .parallelStream()
                            .filter(s -> teacherId.equals(s.getAppraisalId()))
                            .map(BehaviourRecordDTO::getStudentId)
                            .distinct()
                            .collect(Collectors.toList());

            nameAndValueVO.setName(
                    listTargetConfigTeacherList
                            .stream()
                            .filter(s -> Convert.toStr(s.getStaffId()).equals(teacherId))
                            .collect(Collectors.toList())
                            .get(0)
                            .getName());

            Collection<String> intersection = CollectionUtil.intersection(saasStudentIdList, evaluatedStudentIds);

            BigDecimal studentRate = this.getRate(intersection.size(), saasStudentIdList.size(), Constant.THREE).stripTrailingZeros();
            BigDecimal multiply = studentRate.multiply(BigDecimal.valueOf(Constant.HUNDRED));

            //跟 100比较大小
            if (multiply.compareTo(BigDecimal.valueOf(Constant.HUNDRED)) == Constant.ONE) {
                multiply = BigDecimal.valueOf(Constant.HUNDRED);
            }
            nameAndValueVO.setValue(multiply.stripTrailingZeros().toPlainString() + Constant.PERCENT);
            nameAndValueVOS.add(nameAndValueVO);
            studentTotalRate = studentTotalRate.add(studentRate);
        }

        //智能建议
        //平均点评覆盖率
        BigDecimal avgRate = studentTotalRate.divide(BigDecimal.valueOf(teacherIdList.size()), Constant.THREE, RoundingMode.HALF_UP);
        rateVO.setAverage(Convert.toStr(avgRate));
        List<String> teacherNames = nameAndValueVOS.stream().filter(s -> {
            try {
                return new BigDecimal(Convert.toStr(NumberFormat.getPercentInstance().parse(s.getValue()))).compareTo(avgRate) == Constant.NEGATIVE_ONE;
            } catch (ParseException e) {
                log.error("学生点评覆盖率转换错误", e);
            }
            return false;
        }).map(NameAndValueVO::getName).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(teacherNames)) {
            rateVO.setIntelligentSuggestion(null);
        } else {
            rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, teacherNames) + MessageFormat.format(CLASS_STUDENT_EVALUATE_SUGGESTION, avgRate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT));
        }
        nameAndValueVOS.sort(((o1, o2) -> {
            Double d1 = Double.parseDouble(o1.getValue().split("%")[0]);
            Double d2 = Double.parseDouble(o2.getValue().split("%")[0]);
            //降序
            return d2.compareTo(d1);
        }));
    }

}
