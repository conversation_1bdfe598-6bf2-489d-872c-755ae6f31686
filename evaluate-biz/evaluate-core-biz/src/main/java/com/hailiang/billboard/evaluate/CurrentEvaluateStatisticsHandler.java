package com.hailiang.billboard.evaluate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.hailiang.constant.Constant;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.jxgy.common.utils.StringUtil;
import com.hailiang.manager.BehaviourHandleManager;
import com.hailiang.manager.StudentDailyStatisticsManager;
import com.hailiang.model.datastatistics.dto.LastSameTimeEvaluateVO;
import com.hailiang.model.datastatistics.dto.StaffDailyStatisticsVO;
import com.hailiang.model.datastatistics.dto.StudentDailyStatisticsDTO;
import com.hailiang.model.datastatistics.dto.TargetDTO;
import com.hailiang.model.datastatistics.query.DataStatisticsQuery;
import com.hailiang.model.datastatistics.query.TeacherEvaluateDataStatisticsQuery;
import com.hailiang.model.datastatistics.vo.LastSameTimeVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.staff.StaffSectionOrGradeQueryDTO;
import com.hailiang.remote.saas.vo.staff.StaffBatchQueryDTO;
import com.hailiang.remote.saas.vo.staff.StaffBatchVO;
import com.hailiang.remote.saas.vo.staff.StaffFullInfoVO;
import com.hailiang.saas.SaasHistoryStudentCacheManager;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import com.hailiang.service.DataStatisticsService;
import com.hailiang.util.WebUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 当前学年统计服务
 */
@Service("currentEvaluateStatisticsHandler")
public class CurrentEvaluateStatisticsHandler extends DataStatisticsHandler {
    @Resource
    private BehaviourHandleManager behaviourHandleManager;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private DataStatisticsService dataStatisticsService;
    @Resource
    private SaasHistoryStudentCacheManager saasHistoryStudentCacheManager;
    @Resource
    private StudentDailyStatisticsManager studentDailyStatisticsManager;

    @Override
    public List<StaffFullInfoVO> listTargetConfigTeacherIds(
            String campusSectionId,
            String schoolYear,
            String gradeId,
            String classId) {

        // 指标正常且配有点评项的老师信息
        List<StaffFullInfoVO> staffFullInfoVOS = behaviourHandleManager.listTargetConfigTeacherInfos(
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                campusSectionId,
                gradeId,
                classId);

        if (CollUtil.isEmpty(staffFullInfoVOS)) {
            return new ArrayList<>();
        }

        List<Long> listTargetConfigTeacherIds = staffFullInfoVOS
                .stream()
                .map(StaffFullInfoVO::getStaffId)
                .collect(Collectors.toList());


        StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
        // 查staffIdList集合中所有在职员工
        staffBatchQueryDTO.setState(Constant.ZERO);
        staffBatchQueryDTO.setStaffIdList(listTargetConfigTeacherIds);

        List<StaffBatchVO> onStaffList = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
        if (CollUtil.isEmpty(onStaffList)) {
            return new ArrayList<>();
        }

        List<Long> onStaffIdList = onStaffList
                .parallelStream()
                .map(StaffBatchVO::getId)
                .collect(Collectors.toList());

        return staffFullInfoVOS
                .parallelStream()
                .filter(item -> onStaffIdList.contains(item.getStaffId()))
                .collect(Collectors.toList());
    }

    @Override
    protected TreeMap<String/*staffId*/, String/*教职工名称*/> listStaffTreeMap(Long campusSectionId, String schoolYear, Long gradeId, Long classId) {
        StaffSectionOrGradeQueryDTO staffSectionOrGradeQueryDTO = new StaffSectionOrGradeQueryDTO();
        staffSectionOrGradeQueryDTO.setCampusSectionId(campusSectionId);
        staffSectionOrGradeQueryDTO.setGradeId(gradeId);
        staffSectionOrGradeQueryDTO.setClassId(classId);
        List<StaffFullInfoVO> staffFullInfos = basicInfoRemote.queryByClassId(staffSectionOrGradeQueryDTO);
        if (CollUtil.isEmpty(staffFullInfos)) {
            return new TreeMap<>();
        }
        TreeMap<String, String> staffMap = new TreeMap<>();
        staffFullInfos.parallelStream().forEachOrdered(staffFullInfo -> staffMap.put(Convert.toStr(staffFullInfo.getStaffId()), staffFullInfo.getName()));
        return staffMap;
    }

    @Override
    protected List<StaffFullInfoVO> listStaff(Long campusSectionId, String schoolYear, Integer type, Long gradeId, Long classId) {
        StaffSectionOrGradeQueryDTO queryDTO = new StaffSectionOrGradeQueryDTO();
        List<StaffFullInfoVO> staffFullInfoVOS = new ArrayList<>();
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
            queryDTO.setCampusSectionId(Convert.toLong(campusSectionId));
            staffFullInfoVOS = basicInfoRemote.queryByCampusSectionId(queryDTO);
        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
            if (Objects.isNull(gradeId)) {
                return this.listStaff(campusSectionId, schoolYear, SaasCurrentIdTypeEnum.SECTION.getCode(), null, null);
            }
            queryDTO.setGradeId(Convert.toLong(gradeId));
            staffFullInfoVOS = basicInfoRemote.queryByGradeId(queryDTO);
        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
            if (Objects.isNull(classId)) {
                return this.listStaff(campusSectionId, schoolYear, SaasCurrentIdTypeEnum.GRADE.getCode(), gradeId, null);
            }
            queryDTO.setClassId(Convert.toLong(classId));
            staffFullInfoVOS = basicInfoRemote.queryByClassId(queryDTO);
        }
        return staffFullInfoVOS;
    }

    /**
     * 从saas获取(学段、年级、班级)的状态为正常的学生
     *
     * @param campusSectionId
     * @param schoolYear
     * @param gradeId
     * @param classId
     * @return
     */
    @Override
    protected List<SassStudentVO> listTotalSaasStudent(Long campusSectionId, String schoolYear, Long gradeId, Long classId) {

        List<SassStudentVO> sassStudentVOS = saasHistoryStudentCacheManager.getCurrentStudentVOS(
                Convert.toLong(WebUtil.getSchoolId()),
                WebUtil.getCampusIdLong(),
                campusSectionId,
                gradeId,
                classId,
                schoolYear, "0", "0");

        if (CollUtil.isNotEmpty(sassStudentVOS)) {
            //筛选行政班
            sassStudentVOS = sassStudentVOS.stream().filter(s -> Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
            //过滤学段
            if (StringUtil.isNotEmpty(campusSectionId)) {
                sassStudentVOS = sassStudentVOS.stream().filter(s -> s.getCampusSectionId().equals(campusSectionId)).collect(Collectors.toList());
            }
            //指定年级id
            if (StringUtil.isNotEmpty(gradeId) && -1 != gradeId) {
                sassStudentVOS = sassStudentVOS.stream().filter(s -> s.getGradeId().equals(gradeId)).collect(Collectors.toList());
            }
            //指定班级id
            if (StringUtil.isNotEmpty(classId) && -1 != classId) {
                sassStudentVOS = sassStudentVOS.stream().filter(s -> s.getClassId().equals(classId)).collect(Collectors.toList());
            }
        }
        return sassStudentVOS;
    }

    /**
     * 根据studentIds从saas获取(学段、年级、班级)的状态为正常的学生
     *
     * @param campusSectionId
     * @param schoolYear
     * @return
     */
    @Override
    protected List<SassStudentVO> listByStudentIds(Long campusSectionId, String schoolYear, List<Long> studentIds) {
        List<SassStudentVO> sassStudentVOS = saasHistoryStudentCacheManager.listStudentByStudentIds(Convert.toLong(WebUtil.getSchoolId()), schoolYear, studentIds, true);
        if (CollUtil.isNotEmpty(sassStudentVOS)) {
            //筛选行政班
            sassStudentVOS = sassStudentVOS.stream().filter(s -> Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
        }
        return sassStudentVOS;
    }

    @Override
    protected LastSameTimeEvaluateVO getLastEvaluateStatistics(TeacherEvaluateDataStatisticsQuery query, Integer total) {
        LastSameTimeEvaluateVO lastSameTImeEvaluateVO = new LastSameTimeEvaluateVO();
        // 获取上一段相同时间
        LastSameTimeVO lastSameTime = dataStatisticsService.getLastSameTime(query.getStartTime(), query.getEndTime(), query.getCampusSectionId());
        lastSameTImeEvaluateVO.setLastSameTime(lastSameTime.getTitle());
        if (ObjectUtil.isNull(lastSameTime.getStartTime()) || ObjectUtil.isNull(lastSameTime.getEndTime())) {
            lastSameTImeEvaluateVO.setPercent(Constant.HYPHEN);
        } else {
            // 上一段相同时间段内所有的点评数据
            List<StaffDailyStatisticsVO> lastBehaviourRecordDTOList = super.getRecord(query.getCampusSectionId(), query.getSchoolYear(), query.getIsCurrentYear(), query.getGradeId(), query.getClassId(), lastSameTime.getStartTime(), lastSameTime.getEndTime());
            if (CollectionUtil.isEmpty(lastBehaviourRecordDTOList)) {
                lastSameTImeEvaluateVO.setPercent(Constant.HYPHEN);
            } else {
                int sum = lastBehaviourRecordDTOList.stream().mapToInt(StaffDailyStatisticsVO::getAppraisalCount).sum();
                String percent = BigDecimal.valueOf(total).subtract(BigDecimal.valueOf(sum))
                        .divide(BigDecimal.valueOf(sum), Constant.THREE, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT;
                lastSameTImeEvaluateVO.setPercent(percent);
            }
        }
        return lastSameTImeEvaluateVO;
    }

    @Override
    protected LastSameTimeEvaluateVO getLastParticipationRate(TeacherEvaluateDataStatisticsQuery query, List<String/*staffId*/> needEvaluateTeacherIds, BigDecimal rate) {
        LastSameTimeEvaluateVO lastSameTImeEvaluateVO = new LastSameTimeEvaluateVO();
        // 获取上一段相同时间
        LastSameTimeVO lastSameTime = dataStatisticsService.getLastSameTime(query.getStartTime(), query.getEndTime(), query.getCampusSectionId());
        lastSameTImeEvaluateVO.setLastSameTime(lastSameTime.getTitle());
        if (ObjectUtil.isNull(lastSameTime.getStartTime()) || ObjectUtil.isNull(lastSameTime.getEndTime())) {
            lastSameTImeEvaluateVO.setPercent(Constant.HYPHEN);
        } else {
            //需点评老师数量
            int lastTeacherNum = needEvaluateTeacherIds.size();
            //点评过的老师id集合
            List<StaffDailyStatisticsVO> staffDailyStatisticsVOS = super.getRecord(query.getCampusSectionId(), query.getSchoolYear(), query.getIsCurrentYear(), query.getGradeId(), query.getClassId(), lastSameTime.getStartTime(), lastSameTime.getEndTime());
            List<String> lastTeacherIds = staffDailyStatisticsVOS.stream().map(StaffDailyStatisticsVO::getAppraisalId).collect(Collectors.toList());
            //参与点评老师数量
            int lastParticipationTeacherNum = CollectionUtil.intersection(needEvaluateTeacherIds, lastTeacherIds).size();
            if (CollectionUtil.isEmpty(needEvaluateTeacherIds) || Constant.ZERO == lastTeacherNum || Constant.ZERO == lastParticipationTeacherNum) {
                lastSameTImeEvaluateVO.setPercent(Constant.HYPHEN);
            } else {
                //老师参与率
                BigDecimal lastRate = new BigDecimal(lastParticipationTeacherNum).divide(new BigDecimal(lastTeacherNum), Constant.THREE, RoundingMode.HALF_UP);
                lastSameTImeEvaluateVO.setPercent(rate.subtract(lastRate)
                        .divide(lastRate, Constant.THREE, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
            }
        }
        return lastSameTImeEvaluateVO;
    }

    @Override
    protected LastSameTimeEvaluateVO getStudentLastParticipationRate(DataStatisticsQuery query,
                                                                     BigDecimal currentEvaluatedTimes,
                                                                     BigDecimal totalStudentNum) {

        LastSameTimeEvaluateVO lastSameTImeEvaluateVO = new LastSameTimeEvaluateVO();
        //获取上一段相同时间

        LastSameTimeVO lastSameTime = dataStatisticsService.getLastSameTime(
                query.getStartTime(),
                query.getEndTime(),
                query.getCampusSectionId());

        lastSameTImeEvaluateVO.setLastSameTime(lastSameTime.getTitle());

        if (ObjectUtil.isNull(lastSameTime.getStartTime()) || ObjectUtil.isNull(lastSameTime.getEndTime())) {
            lastSameTImeEvaluateVO.setPercent(Constant.HYPHEN);
        } else {

//            List<StudentDailyStatisticsDTO> studentDailyStatisticsDTOS = studentDailyStatisticsManager.listStudentDailyStatisticsBatch(
//                    WebUtil.getSchoolId(),
//                    WebUtil.getCampusId(),
//                    query.getCampusSectionId(),
//                    query.getSchoolYear(),
//                    null,
//                    null,
//                    query.getGradeId() == null || query.getGradeId().equals("-1") ? null : CollectionUtil.newArrayList(query.getGradeId()),
//                    query.getClassId() == null || query.getClassId().equals("-1") ? null : CollectionUtil.newArrayList(query.getClassId()),
//                    lastSameTime.getStartTime(),
//                    lastSameTime.getEndTime()
//            );

//            Integer historyTotalEvaluatedTimes = behaviourHandleManager.countBehaviourRecordNew(
//                    WebUtil.getSchoolId(),
//                    WebUtil.getCampusId(),
//                    query.getCampusSectionId(),
//                    query.getGradeId() == null || query.getGradeId().equals("-1") ? null : query.getGradeId(),
//                    query.getClassId() == null || query.getClassId().equals("-1") ? null : query.getClassId(),
//                    lastSameTime.getStartTime(),
//                    lastSameTime.getEndTime(), null);

            List<StudentDailyStatisticsDTO> leastStudentDailyStatisticsDTOS = studentDailyStatisticsManager.listStudentDailyStatisticsBatchV2(
                    WebUtil.getSchoolId(),
                    WebUtil.getCampusId(),
                    query.getCampusSectionId(),
                    query.getSchoolYear(),
                    null,
                    null,
                    query.getGradeId() == null || query.getGradeId().equals("-1") ? null : Collections.singletonList(query.getGradeId()),
                    query.getClassId() == null || query.getClassId().equals("-1") ? null : Collections.singletonList(query.getClassId()),
                    lastSameTime.getStartTime(),
                    lastSameTime.getEndTime()
            );
            Integer historyTotalEvaluatedTimes = leastStudentDailyStatisticsDTOS
                    .stream()
                    .filter(item -> Objects.nonNull(item.getAppraisalCount()))
                    .mapToInt(StudentDailyStatisticsDTO::getAppraisalCount)
                    .sum();

//            List<String> lastSameTimeEvaluatedStudentIds = studentDailyStatisticsDTOS
//                    .stream()
//                    .map(item -> Convert.toStr(item.getStudentId()))
//                    .distinct()
//                    .collect(Collectors.toList());

            //（当前时段累计点评次数 —上一相同时段周期内点评次数）/上一相同时段周期内点评数
            BigDecimal compareRate = historyTotalEvaluatedTimes == 0 ?
                    BigDecimal.ZERO :
                    currentEvaluatedTimes.subtract(BigDecimal.valueOf(historyTotalEvaluatedTimes))
                            .divide(BigDecimal.valueOf(historyTotalEvaluatedTimes), Constant.THREE, RoundingMode.HALF_UP);

            lastSameTImeEvaluateVO.setPercent(historyTotalEvaluatedTimes == 0 ?
                    Constant.HYPHEN :
                    compareRate.multiply(
                                    BigDecimal.valueOf(Constant.HUNDRED))
                            .stripTrailingZeros().toPlainString() + Constant.PERCENT);
        }
        return lastSameTImeEvaluateVO;
    }

    @Override
    protected List<TargetDTO> listEnableTarget(String schoolId, String campusId, String schoolYear) {
        return behaviourHandleManager.listEnableTarget(schoolId, campusId);
    }

}
