package com.hailiang.billboard.evaluate;

import com.hailiang.enums.EvaluateBizTypeEnum;
import com.hailiang.exception.BizException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Component
public class DataStatisticsFactory {
    @Resource
    private Map<String, DataStatisticsHandler> handlerMap;

    public DataStatisticsHandler getService(EvaluateBizTypeEnum bizType){
        DataStatisticsHandler handler = handlerMap.get(bizType.getName());
        if (Objects.isNull(handler)){
            throw new BizException("没有处理器类");
        }
        return handler;
    }

}
