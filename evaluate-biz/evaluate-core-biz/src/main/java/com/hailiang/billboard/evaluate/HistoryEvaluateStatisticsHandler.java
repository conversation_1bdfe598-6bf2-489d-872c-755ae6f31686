package com.hailiang.billboard.evaluate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.hailiang.constant.Constant;
import com.hailiang.manager.BehaviourHandleManager;
import com.hailiang.manager.TargetArchivedManager;
import com.hailiang.model.datastatistics.dto.LastSameTimeEvaluateVO;
import com.hailiang.model.datastatistics.dto.TargetDTO;
import com.hailiang.model.datastatistics.query.DataStatisticsQuery;
import com.hailiang.model.datastatistics.query.TeacherEvaluateDataStatisticsQuery;
import com.hailiang.remote.saas.vo.staff.ClassFullInfoVO;
import com.hailiang.remote.saas.vo.staff.GradeFullInfoVO;
import com.hailiang.remote.saas.vo.staff.StaffFullInfoVO;
import com.hailiang.saas.SaasHistoryStudentCacheManager;
import com.hailiang.saas.SaasHistoryTeacherRelationCacheManager;
import com.hailiang.saas.model.vo.HisTeacherRelationVO;
import com.hailiang.saas.model.vo.TeacherClassRelationVO;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 历史学年统计服务
 */
@Slf4j
@Service("historyEvaluateStatisticsHandler")
public class HistoryEvaluateStatisticsHandler extends DataStatisticsHandler {
    @Resource
    private BehaviourHandleManager behaviourHandleManager;
    @Resource
    private SaasHistoryTeacherRelationCacheManager saasHistoryTeacherRelationCacheManager;
    @Resource
    private SaasHistoryStudentCacheManager saasHistoryStudentCacheManager;
    @Resource
    private TargetArchivedManager targetArchivedManager;

    private static final LastSameTimeEvaluateVO LAST_SAME_TIME_EVALUATE = new LastSameTimeEvaluateVO().setLastSameTime(Constant.HYPHEN).setPercent(Constant.HYPHEN);

    @Override
    public List<StaffFullInfoVO> listTargetConfigTeacherIds(String campusSectionId, String schoolYear, String gradeId, String classId) {
        // 历史指标正常且配有点评项的老师
        List<StaffFullInfoVO> staffFullInfoVOS = behaviourHandleManager.historyTargetConfigTeacherInfos(
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                campusSectionId,
                schoolYear,
                gradeId,
                classId);

        if (CollUtil.isEmpty(staffFullInfoVOS)) {
            return new ArrayList<>();
        }
        return staffFullInfoVOS;
    }

    @Override
    protected TreeMap<String/*staffId*/, String/*教职工名称*/> listStaffTreeMap(Long campusSectionId, String schoolYear, Long gradeId, Long classId) {
        Map<Long/*staffId*/, TeacherClassRelationVO> listTeacherClassRelationVOMap = saasHistoryTeacherRelationCacheManager.listAllTeacherClassIdsRelation(WebUtil.getSchoolIdLong(), schoolYear, WebUtil.getCampusIdLong(), campusSectionId, gradeId, classId);
        if (CollUtil.isEmpty(listTeacherClassRelationVOMap) || !listTeacherClassRelationVOMap.containsKey(campusSectionId)) {
            return new TreeMap<>();
        }
        TeacherClassRelationVO yearTeacherClassRelationVO = listTeacherClassRelationVOMap.get(campusSectionId);
        if (yearTeacherClassRelationVO == null || CollUtil.isEmpty(yearTeacherClassRelationVO.getHisClassList())) {
            return new TreeMap<>();
        }
        TreeMap<String, String> staffMap = new TreeMap<>();
        for (HisTeacherRelationVO history : yearTeacherClassRelationVO.getHisClassList()) {
            String teacherStaffId = Convert.toStr(history.getHeadTeacherStaffId());
            staffMap.put(teacherStaffId, history.getHeadTeacherName());
        }
        return staffMap;
    }

    @Override
    protected List<StaffFullInfoVO> listStaff(Long campusSectionId, String schoolYear, Integer type, Long gradeId, Long classId) {
        List<StaffFullInfoVO> hisTeacherRelationVOS = this.listStaff(campusSectionId, schoolYear, gradeId, classId);
        if (CollUtil.isEmpty(hisTeacherRelationVOS)) {
            return new ArrayList<>();
        }
        return hisTeacherRelationVOS;
    }

    @Override
    protected List<SassStudentVO> listTotalSaasStudent(Long campusSectionId, String schoolYear, Long gradeId, Long classId) {
        return saasHistoryStudentCacheManager.listHisStudentByGradeIdAndClassId(Convert.toLong(WebUtil.getSchoolId()),schoolYear,Convert.toLong(WebUtil.getCampusId()),gradeId,classId,campusSectionId);
    }

    @Override
    protected List<SassStudentVO> listByStudentIds(Long campusSectionId, String schoolYear, List<Long> studentIds) {
        return saasHistoryStudentCacheManager.listStudentByStudentIds(Convert.toLong(WebUtil.getSchoolId()), schoolYear, studentIds, false);
    }

    @Override
    protected LastSameTimeEvaluateVO getLastEvaluateStatistics(TeacherEvaluateDataStatisticsQuery query, Integer total) {
        // 无需统计上一时间段数据
        return LAST_SAME_TIME_EVALUATE;
    }

    @Override
    protected LastSameTimeEvaluateVO getLastParticipationRate(TeacherEvaluateDataStatisticsQuery query, List<String> needEvaluateTeacherIds, BigDecimal rate) {
        // 无需统计上一时间段数据
        return LAST_SAME_TIME_EVALUATE;
    }

    @Override
    protected LastSameTimeEvaluateVO getStudentLastParticipationRate(DataStatisticsQuery query,BigDecimal studentNum, BigDecimal totalStudentNum) {
        // 无需统计上一时间段数据
        return LAST_SAME_TIME_EVALUATE;
    }

    @Override
    protected List<TargetDTO> listEnableTarget(String schoolId, String campusId, String schoolYear) {
        List<TargetDTO> targetDTOS = targetArchivedManager.listEnableTarget(schoolId, campusId, schoolYear);
        if (CollUtil.isEmpty(targetDTOS)) {
            return new ArrayList<>();
        }
        return targetDTOS.parallelStream().peek(item -> item.setId(item.getTargetId())).collect(Collectors.toList());
    }

    private List<StaffFullInfoVO> listStaff(Long campusSectionId, String schoolYear, Long gradeId, Long classId) {
        List<HisTeacherRelationVO> hisTeacherRelationVOList = saasHistoryTeacherRelationCacheManager.listTeacherByCampusIdAndGradeIdAndClassId(WebUtil.getSchoolIdLong(), schoolYear, WebUtil.getCampusIdLong(), campusSectionId, gradeId, classId);
        if (CollUtil.isEmpty(hisTeacherRelationVOList)) {
            return new ArrayList<>();
        }
        Set<Long> staffIds = new HashSet<>();
        List<StaffFullInfoVO> staffFullInfoVOList = new ArrayList<>();
        log.info("HistoryEvaluateStatisticsHandler.listStaff 批量获取历史员工信息 campusSectionId【{}】，schoolYear【{}】，gradeId【{}】，classId【{}】，hisTeacherRelationVOList：{}",
                campusSectionId, schoolYear, gradeId, classId, hisTeacherRelationVOList.size());
        hisTeacherRelationVOList.forEach(his -> {
            if (his == null) return;
            String schoolId = Convert.toStr(his.getSchoolId());
            String campusId = Convert.toStr(his.getCampusId());
            Map<Long, TeacherClassRelationVO> longTeacherClassRelationVOMap = saasHistoryTeacherRelationCacheManager.listAllTeacherClassIdsRelation(his.getSchoolId(), schoolYear, his.getCampusId(), campusSectionId, gradeId, classId);
            String campusSectionIdS = Convert.toStr(his.getCampusSectionId());
            if (his.getHeadTeacherStaffId() != null && his.getHeadTeacherStaffId() > 0L && !staffIds.contains(his.getHeadTeacherStaffId())) {
                staffIds.add(his.getHeadTeacherStaffId());
                staffFullInfoVOList.add(this.getStaffFullInfoVO(longTeacherClassRelationVOMap, schoolId, campusId, campusSectionIdS, his.getHeadTeacherStaffId(), his.getHeadTeacherName()));
            }
            if (CollUtil.isEmpty(his.getSubjectList())) return;
            his.getSubjectList().parallelStream()
                    .filter(sub -> CollUtil.isNotEmpty(sub.getTeacherList()))
                    .forEach(sub -> sub.getTeacherList().parallelStream().forEach(teacher -> {
                        if (teacher == null || teacher.getTeacherStaffId() == null
                                || teacher.getTeacherStaffId() == 0L || staffIds.contains(teacher.getTeacherStaffId())) {
                            return;
                        }
                        staffIds.add(teacher.getTeacherStaffId());
                        staffFullInfoVOList.add(this.getStaffFullInfoVO(longTeacherClassRelationVOMap, schoolId, campusId, campusSectionIdS, teacher.getTeacherStaffId(), teacher.getTeacherName()));
                    }));
        });
        return staffFullInfoVOList;
    }

    private StaffFullInfoVO getStaffFullInfoVO(Map<Long, TeacherClassRelationVO> longTeacherClassRelationVOMap,
                                               String schoolId, String campusId, String campusSectionId, Long staffId, String name) {
        StaffFullInfoVO staffFullInfoVO = new StaffFullInfoVO();
        staffFullInfoVO.setSchoolId(schoolId);
        staffFullInfoVO.setCampusId(campusId);
        staffFullInfoVO.setCampusSectionId(campusSectionId);
        staffFullInfoVO.setStaffId(staffId);
        staffFullInfoVO.setName(name);

        this.fillGradeAndClass(longTeacherClassRelationVOMap, staffFullInfoVO);
        return staffFullInfoVO;
    }

    private void fillGradeAndClass(Map<Long, TeacherClassRelationVO> longTeacherClassRelationVOMap, StaffFullInfoVO staffFullInfoVO) {
        Long staffId = staffFullInfoVO.getStaffId();
        if (!longTeacherClassRelationVOMap.containsKey(staffId)) {
            return;
        }
        TeacherClassRelationVO teacherClassRelationVO = longTeacherClassRelationVOMap.get(staffId);
        log.info("获取班级历史任教关系，teacherClassRelationVO:{}", JSONUtil.toJsonStr(teacherClassRelationVO));
        if (teacherClassRelationVO == null || CollUtil.isEmpty(teacherClassRelationVO.getHisClassList())) {
            return;
        }
        Set<Long> gradeIds = new HashSet<>();
        Set<Long> classIds = new HashSet<>();
        List<GradeFullInfoVO> gradeList = new ArrayList<>();
        List<ClassFullInfoVO> classList = teacherClassRelationVO.getHisClassList().parallelStream().map(hisClass -> {
            if (hisClass == null) return null;
            if (!gradeIds.contains(hisClass.getGradeId()))  {
                gradeIds.add(hisClass.getGradeId());
                GradeFullInfoVO gradeFullInfoVO = new GradeFullInfoVO();
                gradeFullInfoVO.setGradeId(hisClass.getGradeId());
                gradeFullInfoVO.setGradeName(hisClass.getGradeName());
                gradeList.add(gradeFullInfoVO);
            }
            if (classIds.contains(hisClass.getClassId())) return null;
            classIds.add(hisClass.getClassId());
            ClassFullInfoVO classFullInfoVO = new ClassFullInfoVO();
            classFullInfoVO.setGradeId(hisClass.getGradeId());
            classFullInfoVO.setClassId(hisClass.getClassId());
            classFullInfoVO.setClassName(hisClass.getClassName());
            return classFullInfoVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        staffFullInfoVO.setGradeList(gradeList);
        staffFullInfoVO.setClassList(classList);
    }

}
