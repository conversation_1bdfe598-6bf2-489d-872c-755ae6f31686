package com.hailiang.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.google.common.collect.Lists;
import com.hailiang.channel.facade.enums.CmMessageTypeEnum;
import com.hailiang.constant.Constant;
import com.hailiang.dto.DateTimeRangeDTO;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.InfoTypeEnum;
import com.hailiang.enums.MessageLogBusinessTypeEnum;
import com.hailiang.enums.MessageLogMessageTypeEnum;
import com.hailiang.enums.MessageLogTerminalTypeEnum;
import com.hailiang.enums.ModuleNameEnum;
import com.hailiang.enums.SaasClassTypeEnum;
import com.hailiang.enums.SaasStaffStateQueryEnum;
import com.hailiang.enums.ScoreTypeEnum;
import com.hailiang.enums.StudentInfoTypeEnum;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.enums.SubmitRateEnum;
import com.hailiang.enums.TaskApprovalEnum;
import com.hailiang.enums.TaskRoleTypeEnum;
import com.hailiang.enums.TaskStatusEnum;
import com.hailiang.enums.TaskTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.logic.TargetGroupLogic;
import com.hailiang.logic.TargetLogic;
import com.hailiang.logic.TaskLogic;
import com.hailiang.manager.SaasGeneralManager;
import com.hailiang.model.datastatistics.dto.ClassGradeInfoDTO;
import com.hailiang.model.dto.internaldrive.modify.InfoModifyDTO;
import com.hailiang.model.dto.internaldrive.modify.SubmitInfoModifyDTO;
import com.hailiang.model.dto.save.BehaviourRecordSaveDTO;
import com.hailiang.model.dto.save.InfoSaveDTO;
import com.hailiang.model.dto.save.SubmitInfoSaveDTO;
import com.hailiang.model.dto.save.TaskSaveDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.entity.MessageLog;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TargetGroup;
import com.hailiang.model.entity.TaskPO;
import com.hailiang.model.entity.mongo.Info;
import com.hailiang.model.entity.mongo.ScoreInfo;
import com.hailiang.model.entity.mongo.StudentInfo;
import com.hailiang.model.entity.mongo.SubStudentInfo;
import com.hailiang.model.entity.mongo.SubmitInfo;
import com.hailiang.msg.BizTypeEnum;
import com.hailiang.msg.ChannelMsgManager;
import com.hailiang.msg.ChannelMsgTemplateProps;
import com.hailiang.remote.ding.utils.DingDingMsgUtil;
import com.hailiang.remote.feishu.domain.FeiMsgReqDTO;
import com.hailiang.remote.feishu.domain.FeiShuResultDTO;
import com.hailiang.remote.feishu.utils.FeiShuMsgUtil;
import com.hailiang.remote.hai.domain.dto.request.MsgContent;
import com.hailiang.remote.hai.domain.dto.request.SendMsgRequestDTO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.staff.StaffBatchQueryDTO;
import com.hailiang.remote.saas.vo.staff.StaffBatchVO;
import com.hailiang.remote.saas.vo.student.StudentVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.remote.wechat.WechatManager;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.dto.StudentClassQueryDTO;
import com.hailiang.saas.model.dto.StudentDTO;
import com.hailiang.saas.model.dto.StudentPageQueryDTO;
import com.hailiang.saas.model.vo.StudentInfo1VO;
import com.hailiang.saas.model.vo.StudentInfoVO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.MessageLogService;
import com.hailiang.service.TaskService;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RefreshScope
public class InfoHelper {
    @Resource
    private TaskService taskService;
    @Resource
    private DingDingMsgUtil dingDingMsgUtil;
    @Resource
    private BasicInfoService basicInfoService;
    @Resource
    private MessageLogService messageLogService;
    @Resource
    private TargetGroupLogic targetGroupLogic;
    @Resource
    private TargetLogic targetLogic;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private TaskLogic taskLogic;
    @Value("${oss.urlPrefix}")
    private String urlPrefix;
    @Value("${third.ding.handledColor}")
    private String handledColor;
    @Value("${third.ding.messageUrl}")
    private String messageUrl;
    @Value("${third.ding.titleColor}")
    private String titleColor;
    @Value("${third.ding.unhandleColor}")
    private String unhandleColor;
    @Resource
    private SaasGeneralManager saasGeneralManager;
    @Resource
    private FeiShuMsgUtil feiShuMsgUtil;
    @Resource
    private WechatManager wechatManager;
    @Resource
    private SaasStudentManager saasStudentManager;
    @Resource
    private ChannelMsgManager channelMsgManager;
    @Resource
    private ChannelMsgHelper channelMsgHelper;
    @Resource
    private ChannelMsgTemplateProps channelMsgTemplateProps;
    @Resource
    private BehaviourRecordManager behaviourRecordManager;

    /**
     * 判断模版格式
     */
//    public void judgeTemplate(List<TemplateInfoSaveDTO> templateInfoSaveDTOList) {
//        // 只能有一个加分控件 1
//        // 有加分控件不允许有明细 1
//        // 加分控件只能在外面 1
//        // 有加分控件不允许有加分的选择题 允许有不加分的选择题 1
//        // 学生控件只能在外面或里面 1
//        // 明细外或一个明细里只会有一个学生控件 1
//        // 一定会有学生控件 1
//
//        // 明细外是否有加分控件
//        boolean hasScore = false;
//        // 明细里是否有加分控件
//        boolean hasDetailScore = false;
//        // 是否有明细控件
//        boolean hasDetail = false;
//        // 明细外是否有加分的选择题
//        boolean hasScoreCheck = false;
//        // 明细外是否有学生控件
//        boolean hasStudent = false;
//        // 明细里是否有学生控件
//        boolean hasDetailStudent = false;
//        for (TemplateInfoSaveDTO saveDTO : templateInfoSaveDTOList) {
//            if (SubmitInfoTypeEnum.SCORE.getText().equals(saveDTO.getType())) {
//                Assert.isTrue(!hasScore, "加分控件只能有一个");
//                hasScore = true;
//            }
//            if (SubmitInfoTypeEnum.DETAIL.getText().equals(saveDTO.getType())) {
//                hasDetail = true;
//                List<LinkedHashMap> list = saveDTO.getList();
//                // 每个明细里是否有学生控件
//                boolean hasSubDetailStudent = false;
//                for (LinkedHashMap map : list) {
//                    String type = String.valueOf(map.get("type"));
//                    if (SubmitInfoTypeEnum.SCORE.getText().equals(type)) {
//                        hasDetailScore = true;
//                    }
//                    if (SubmitInfoTypeEnum.STUDENT.getText().equals(type)) {
//                        Assert.isTrue(!hasSubDetailStudent, "一个明细里只能有一个学生控件");
//                        hasSubDetailStudent = true;
//                        hasDetailStudent = true;
//                    }
//                }
//            }
//            if (SubmitInfoTypeEnum.STUDENT.getText().equals(saveDTO.getType())) {
//                Assert.isTrue(!hasStudent, "指标只能有一个学生控件");
//                hasStudent = true;
//            }
//            // 是否是选择题
//            boolean isCheck = SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(saveDTO.getType()) ||
//                    SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(saveDTO.getType());
//            if (isCheck) {
//                if (saveDTO.getIsScore()) {
//                    hasScoreCheck = true;
//                }
//            }
//        }
//        Assert.isTrue(!hasDetailScore, "加分控件不允许加在明细里");
//        Assert.isTrue(!(hasScore && hasDetail), "明细控件里不允许加加分控件");
//        Assert.isTrue(!(hasScore && hasScoreCheck), "加分控件不允许选择题开启加分");
//        Assert.isTrue(!(hasStudent && hasDetailStudent), "学生控件不允许同时加在指标和明细里");
//        Assert.isTrue(!(!hasStudent && !hasDetailStudent), "没加学生控件");
//    }
    public void judgeTemplate(List<TemplateInfoSaveDTO> templateInfoSaveDTOList) {
        // 只能有一个加分控件
        // 有加分控件不允许有明细
        // 加分控件只能在外面
        // 有加分控件不允许有加分的选择题 允许有不加分的选择题
        // 学生控件只能在外面或里面
        // 明细外或一个明细里只会有一个学生控件
        // 一定会有学生控件

        // 定义二维数组
        // 行是表单的每一条
        // 第一列是 明细外有几个加分控件 j=0
        // 第二列是 明细里有几个加分控件 j=1
        // 第三列是 明细外有几个学生控件 j=2
        // 第四列是 所有明细里是否有学生控件 j=3
        // 第五列是 一个明细里有几个学生控件 j=4
        // 第六列是 明细外有几个加分的选择题 j=5
        // 第七列是 有几个明细控件 j=6
        int size = templateInfoSaveDTOList.size();
        int[][] array = new int[size][7];
        // 先填充第一行
        TemplateInfoSaveDTO firstSaveDTO = templateInfoSaveDTOList.get(0);
        array[0][0] = SubmitInfoTypeEnum.SCORE.getText().equals(firstSaveDTO.getType()) ? 1 : 0;
        array[0][2] = SubmitInfoTypeEnum.STUDENT.getText().equals(firstSaveDTO.getType()) ? 1 : 0;
        array[0][5] = (SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(firstSaveDTO.getType()) ||
                SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(firstSaveDTO.getType())) &&
                firstSaveDTO.getIsScore() ? 1 : 0;
        array[0][6] = SubmitInfoTypeEnum.DETAIL.getText().equals(firstSaveDTO.getType()) ? 1 : 0;
        if (SubmitInfoTypeEnum.DETAIL.getText().equals(firstSaveDTO.getType())) {
            judgeDetail(firstSaveDTO, array, 0);
        }
        // 填充剩余的数组数据
        for (int i = 1; i < size; i++) {
            TemplateInfoSaveDTO saveDTO = templateInfoSaveDTOList.get(i);
            array[i][0] = SubmitInfoTypeEnum.SCORE.getText().equals(saveDTO.getType()) ?
                    array[i - 1][0] + 1 : array[i - 1][0];
            Assert.isTrue(array[i][0] < 2, () -> new BizException(BizExceptionEnum.ONLY_ONE_SCORE.getMessage()));
            array[i][2] = SubmitInfoTypeEnum.STUDENT.getText().equals(saveDTO.getType()) ?
                    array[i - 1][2] + 1 : array[i - 1][2];
            Assert.isTrue(array[i][2] < 2, () -> new BizException(BizExceptionEnum.ONLY_ONE_STUDENT.getMessage()));
            array[i][3] = Math.max(array[i - 1][3], array[i][3]);
            array[i][5] = (SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(saveDTO.getType()) ||
                    SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(saveDTO.getType())) &&
                    saveDTO.getIsScore() ?
                    array[i - 1][5] + 1 : array[i - 1][5];
            array[i][6] = SubmitInfoTypeEnum.DETAIL.getText().equals(saveDTO.getType()) ?
                    array[i - 1][6] + 1 : array[i - 1][6];
            if (SubmitInfoTypeEnum.DETAIL.getText().equals(saveDTO.getType())) {
                judgeDetail(saveDTO, array, i);
            }
        }
        Assert.isTrue(!(array[size - 1][0] > 0 && array[size - 1][6] > 0), () -> new BizException(BizExceptionEnum.NOT_ALLOW_DETAIL.getMessage()));
        Assert.isTrue(!(array[size - 1][0] > 0 && array[size - 1][5] > 0), () -> new BizException(BizExceptionEnum.NOT_ALLOW_SCORE.getMessage()));
        Assert.isTrue(!(array[size - 1][2] > 0 && array[size - 1][3] > 0), () -> new BizException(BizExceptionEnum.NOT_ALLOW_STUDENT.getMessage()));
        Assert.isTrue(array[size - 1][2] > 0 || array[size - 1][3] > 0, () -> new BizException(BizExceptionEnum.NO_STUDENT.getMessage()));
    }

    private void judgeDetail(TemplateInfoSaveDTO saveDTO,
                             int[][] array, int i) {
        List<LinkedHashMap> list = saveDTO.getList();
        Assert.isTrue(CollUtil.isNotEmpty(list), () -> new BizException(BizExceptionEnum.NO_DETAIL.getMessage()));
        Boolean isPhoto = saveDTO.getIsPhoto();

        int scoreNum = 0;
        int studentNum = 0;
        for (LinkedHashMap map : list) {
            String type = String.valueOf(map.get("type"));
            Assert.isTrue(!(isPhoto && SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(type)), () -> new BizException(BizExceptionEnum.DETAIL_NOT_ALLOW_CHECK.getMessage()));
            array[i][1] = SubmitInfoTypeEnum.SCORE.getText().equals(type) ? ++scoreNum : scoreNum;
            Assert.isTrue(array[i][1] < 1, () -> new BizException(BizExceptionEnum.DETAIL_NOT_ALLOW_SCORE.getMessage()));
            array[i][4] = SubmitInfoTypeEnum.STUDENT.getText().equals(type) ? ++studentNum : studentNum;
            Assert.isTrue(array[i][4] < 2, () -> new BizException(BizExceptionEnum.DETAIL_ONE_STUDENT.getMessage()));
            array[i][3] = Math.max(array[i][3], studentNum);
        }
    }

    /**
     * @description: 填充分数
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2023/1/3 下午2:59
     */
    public void fullFillDatas(List<StudentInfo> totalStudentInfoList, List<Map<String, List>> infoList, String taskName, List<SubmitInfoSaveDTO> submitInfoSaveDTOList) {
        // 加分控件集合
        List<ScoreInfo> scoreInfoList = new ArrayList<>();
        // 外层学生集合
        List<SubStudentInfo> studentInfoList = new ArrayList<>();
        // 所有选择题集合
        List<ScoreInfo> checkScoreInfoList = new ArrayList<>();
        // 内层选择题和学生映射集合
        List<Map<String, List>> infoMapList = new ArrayList<>();
        for (SubmitInfoSaveDTO saveDTO : submitInfoSaveDTOList) {
            // 参数判断
            if (judgeFillDatas(saveDTO)) {
                continue;
            }
            fullFillSubDatas(taskName, saveDTO, totalStudentInfoList, scoreInfoList, studentInfoList, checkScoreInfoList, null, null, null);
            // 明细里的分数
            if (SubmitInfoTypeEnum.DETAIL.getText().equals(saveDTO.getType())) {
                fullFillDetailDatas(taskName, saveDTO, totalStudentInfoList, scoreInfoList, studentInfoList, checkScoreInfoList, infoMapList);
            }
        }

        // 有加分控件
        if (CollUtil.isNotEmpty(scoreInfoList)) {
            Map<String, List> infoMap = new HashMap<>();
            infoMap.put("score", scoreInfoList);
            infoMap.put("student", studentInfoList);
            infoList.add(infoMap);
            return;
        }
        // 学生在外面
        if (CollUtil.isNotEmpty(studentInfoList)) {
            Map<String, List> infoMap = new HashMap<>();
            infoList.add(infoMap);
            infoMap.put("student", studentInfoList);
            // 有选择题
            if (CollUtil.isNotEmpty(checkScoreInfoList)) {
                infoMap.put("score", checkScoreInfoList);
                return;
            }
            // 没选择题
            ScoreInfo scoreInfo = new ScoreInfo();
            scoreInfo.setInfoName(taskName);
            scoreInfo.setInfoType(InfoTypeEnum.INFO.getCode());
            scoreInfo.setIsScore(false);
            scoreInfo.setTitleId("");
            scoreInfo.setOptionId("");
            infoMap.put("score", Collections.singletonList(scoreInfo));
            return;
        }
        // 有明细
        infoList.addAll(infoMapList);
    }

    /**
     * 获取点评项和分数、学生间的映射
     *
     * @param submitInfoSaveDTOList
     * @param infoMap
     */
    public void buildInfoMap(List<SubmitInfoSaveDTO> submitInfoSaveDTOList,
                             Map<String, BigDecimal> infoMap,
                             Map<String, List<SubStudentInfo>> optionToStudentMap,
                             Map<String, List<SubStudentInfo>> optionDetailToStudentMap) {
        List<SubStudentInfo> studentInfos = new ArrayList<>();
        for (SubmitInfoSaveDTO saveDTO : submitInfoSaveDTOList) {
            if (SubmitInfoTypeEnum.STUDENT.getText().equals(saveDTO.getType())) {
                studentInfos = this.fillStudentInfo(saveDTO);
            }
        }
        for (SubmitInfoSaveDTO saveDTO : submitInfoSaveDTOList) {
            if (CollUtil.isNotEmpty(studentInfos)) {
                this.fillMap(saveDTO, infoMap, optionToStudentMap, studentInfos);
            }
            // 明细里的分数
            if (SubmitInfoTypeEnum.DETAIL.getText().equals(saveDTO.getType())) {
                List<SubStudentInfo> detailStudentInfos = new ArrayList<>();
                boolean outStudent = false;
                if (CollUtil.isNotEmpty(studentInfos)) {
                    detailStudentInfos = studentInfos;
                    outStudent = true;
                }
                this.fillDetailMap(saveDTO, infoMap, optionDetailToStudentMap, detailStudentInfos, outStudent);
            }
        }
    }

    // 图片视频存相对路径
    public void transferImgForSave(InfoSaveDTO dto) {
        List<SubmitInfoSaveDTO> submitInfoList = dto.getSubmitInfoList();
        if (CollUtil.isEmpty(submitInfoList)) {
            return;
        }
        for (SubmitInfoSaveDTO saveDTO : submitInfoList) {
            // 明细里的图片
            if (SubmitInfoTypeEnum.DETAIL.getText().equals(saveDTO.getType())) {
                List<List<LinkedHashMap>> list = saveDTO.getSubmitList();
                for (List<LinkedHashMap> subList : list) {
                    for (LinkedHashMap subMap : subList) {
                        String type = String.valueOf(subMap.get("type"));
                        if (SubmitInfoTypeEnum.MEDIA.getText().equals(type)) {
                            List<String> submitValue = (List<String>) subMap.get("submitValue");
                            List<String> submitValueNew = new ArrayList<>();
                            for (String imgStr : submitValue) {
                                String[] strs = imgStr.split("\\/");
                                submitValueNew.add(strs[strs.length - 1]);
                            }
                            subMap.put("submitValue", submitValueNew);
                        }
                    }
                }
                continue;
            }
            if (SubmitInfoTypeEnum.MEDIA.getText().equals(saveDTO.getType())) {
                List<String> submitValue = (List<String>) saveDTO.getSubmitValue();
                List<String> submitValueNew = new ArrayList<>();
                for (String imgStr : submitValue) {
                    String[] strs = imgStr.split("\\/");
                    submitValueNew.add(strs[strs.length - 1]);
                }
                saveDTO.setSubmitValue(submitValueNew);
            }
        }
    }

    public void transferImgForUpdate(List<SubmitInfoModifyDTO> submitInfoList) {
        if (CollUtil.isEmpty(submitInfoList)) {
            return;
        }
        for (SubmitInfoModifyDTO modifyDTO : submitInfoList) {
            // 明细里的图片
            if (SubmitInfoTypeEnum.DETAIL.getText().equals(modifyDTO.getType())) {
                List<List<LinkedHashMap>> list = modifyDTO.getSubmitList();
                for (List<LinkedHashMap> subList : list) {
                    for (LinkedHashMap subMap : subList) {
                        String type = String.valueOf(subMap.get("type"));
                        if (SubmitInfoTypeEnum.MEDIA.getText().equals(type)) {
                            List<String> submitValue = (List<String>) subMap.get("submitValue");
                            List<String> submitValueNew = new ArrayList<>();
                            for (String imgStr : submitValue) {
                                String[] strs = imgStr.split("\\/");
                                submitValueNew.add(strs[strs.length - 1]);
                            }
                            subMap.put("submitValue", submitValueNew);
                        }
                    }
                }
                continue;
            }
            if (SubmitInfoTypeEnum.MEDIA.getText().equals(modifyDTO.getType())) {
                List<String> submitValue = (List<String>) modifyDTO.getSubmitValue();
                List<String> submitValueNew = new ArrayList<>();
                for (String imgStr : submitValue) {
                    String[] strs = imgStr.split("\\/");
                    submitValueNew.add(strs[strs.length - 1]);
                }
                modifyDTO.setSubmitValue(submitValueNew);
            }
        }
    }

    // 拼接图片url
    public void jointImg(Info evaluateInfo) {
        List<SubmitInfo> submitInfoList = evaluateInfo.getSubmitInfoList();
        if (CollUtil.isEmpty(submitInfoList)) {
            return;
        }
        for (SubmitInfo submitInfo : submitInfoList) {
            // 明细里的图片
            if (SubmitInfoTypeEnum.DETAIL.getText().equals(submitInfo.getType())) {
                List<List<LinkedHashMap>> list = submitInfo.getSubmitList();
                for (List<LinkedHashMap> subList : list) {
                    for (LinkedHashMap subMap : subList) {
                        String type = String.valueOf(subMap.get("type"));
                        if (SubmitInfoTypeEnum.MEDIA.getText().equals(type)) {
                            List<String> submitValue = (List<String>) subMap.get("submitValue");
                            List<String> submitValueNew = new ArrayList<>();
                            for (String imgStr : submitValue) {
                                imgStr = urlPrefix + imgStr;
                                submitValueNew.add(imgStr);
                            }
                            subMap.put("submitValue", submitValueNew);
                        }
                    }
                }
                continue;
            }
            if (SubmitInfoTypeEnum.MEDIA.getText().equals(submitInfo.getType())) {
                List<String> submitValue = (List<String>) submitInfo.getSubmitValue();
                List<String> submitValueNew = new ArrayList<>();
                for (String imgStr : submitValue) {
                    imgStr = urlPrefix + imgStr;
                    submitValueNew.add(imgStr);
                }
                submitInfo.setSubmitValue(submitValueNew);
            }
        }
    }

    private boolean judgeFillDatas(SubmitInfoSaveDTO saveDTO) {
        // 必填是否没填
        boolean requireFlag = !(SubmitInfoTypeEnum.DETAIL.getText().equals(saveDTO.getType()) ||
                SubmitInfoTypeEnum.DESCRIPTION.getText().equals(saveDTO.getType()) ||
                SubmitInfoTypeEnum.SCORE.getText().equals(saveDTO.getType())) &&
                saveDTO.getRequired() != null &&
                saveDTO.getRequired()
                && ObjectUtil.isEmpty(saveDTO.getSubmitValue());
        Assert.isTrue(!requireFlag, () -> new BizException(BizExceptionEnum.NOT_WRITE.getMessage()));

        // 不是明细或加分、没填不统计
        boolean flag = ObjectUtil.isEmpty(saveDTO.getSubmitValue()) &&
                !(SubmitInfoTypeEnum.DETAIL.getText().equals(saveDTO.getType()) ||
                        SubmitInfoTypeEnum.SCORE.getText().equals(saveDTO.getType()) ||
                        SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(saveDTO.getType()) ||
                        SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(saveDTO.getType()));
        return flag;
    }

    private void fullFillDetailDatas(String taskName,
                                     SubmitInfoSaveDTO saveDTO,
                                     List<StudentInfo> totalStudentInfoList,
                                     List<ScoreInfo> scoreInfoList,
                                     List<SubStudentInfo> studentInfoList,
                                     List<ScoreInfo> checkScoreInfoList,
                                     List<Map<String, List>> infoMapList) {
        List<List<LinkedHashMap>> list = saveDTO.getSubmitList();

        for (List<LinkedHashMap> subList : list) {
            List<ScoreInfo> subCheckScoreInfoList = new ArrayList<>();
            List<SubStudentInfo> subStudentInfoList = new ArrayList<>();
            for (LinkedHashMap subMap : subList) {
                SubmitInfoSaveDTO subSaveDTO = new SubmitInfoSaveDTO()
                        .setType(String.valueOf(subMap.get("type")))
                        .setName(String.valueOf(subMap.get("name")))
                        .setKey(String.valueOf(subMap.get("key")));
                if (subMap.get("submitValue") != null) {
                    subSaveDTO.setSubmitValue(subMap.get("submitValue"));
                }
                if (subMap.get("score") != null) {
                    subSaveDTO.setScore(new BigDecimal(String.valueOf(subMap.get("score"))));
                }
                if (subMap.get("isScore") != null) {
                    subSaveDTO.setIsScore(Boolean.parseBoolean(String.valueOf(subMap.get("isScore"))));
                }
                if (subMap.get("scoreType") != null) {
                    subSaveDTO.setScoreType(Integer.parseInt(String.valueOf(subMap.get("scoreType"))));
                }
                if (subMap.get("required") != null) {
                    subSaveDTO.setRequired(Boolean.parseBoolean(String.valueOf(subMap.get("required"))));
                }
                // 参数判断
                if (judgeFillDatas(subSaveDTO)) {
                    continue;
                }
                fullFillSubDatas(taskName, subSaveDTO, totalStudentInfoList,
                        scoreInfoList, studentInfoList, checkScoreInfoList,
                        saveDTO.getKey(), subCheckScoreInfoList, subStudentInfoList);
            }
            Map<String, List> infoMap = new HashMap<>();
            infoMap.put("score", subCheckScoreInfoList);
            infoMap.put("student", subStudentInfoList);
            infoMapList.add(infoMap);
        }
    }


    private void fillDetailMap(SubmitInfoSaveDTO saveDTO,
                               Map<String, BigDecimal> fillDetailmap,
                               Map<String, List<SubStudentInfo>> submitStudentsMap,
                               List<SubStudentInfo> detailStudentInfos,
                               boolean outStudent) {
        List<List<LinkedHashMap>> list = saveDTO.getSubmitList();
        for (List<LinkedHashMap> subList : list) {
            List<SubStudentInfo> studentInfos = this.checkDetailStudentInfo(subList);
            if(CollUtil.isNotEmpty(studentInfos)){
                detailStudentInfos = studentInfos;
            }

            for (LinkedHashMap subMap : subList) {
                SubmitInfoSaveDTO subSaveDTO = new SubmitInfoSaveDTO()
                        .setType(String.valueOf(subMap.get("type")))
                        .setName(String.valueOf(subMap.get("name")))
                        .setKey(String.valueOf(subMap.get("key")));
                if (subMap.get("submitValue") != null) {
                    subSaveDTO.setSubmitValue(subMap.get("submitValue"));
                }
                if (subMap.get("score") != null) {
                    subSaveDTO.setScore(new BigDecimal(String.valueOf(subMap.get("score"))));
                }
                if (subMap.get("isScore") != null) {
                    subSaveDTO.setIsScore(Boolean.parseBoolean(String.valueOf(subMap.get("isScore"))));
                }
                if (subMap.get("scoreType") != null) {
                    subSaveDTO.setScoreType(Integer.parseInt(String.valueOf(subMap.get("scoreType"))));
                }
                if (subMap.get("required") != null) {
                    subSaveDTO.setRequired(Boolean.parseBoolean(String.valueOf(subMap.get("required"))));
                }
                // 参数判断
                if (judgeFillDatas(subSaveDTO)) {
                    continue;
                }
//                if (SubmitInfoTypeEnum.STUDENT.getText().equals(subSaveDTO.getType())) {
//                    detailStudentInfos = this.fillStudentInfo(subSaveDTO);
//                }
                if(CollUtil.isNotEmpty(studentInfos) || (CollUtil.isEmpty(studentInfos) && outStudent)){
                    this.fillMap(subSaveDTO, fillDetailmap, submitStudentsMap, detailStudentInfos);
                }
            }
        }
    }

    public List<SubStudentInfo> checkDetailStudentInfo(List<LinkedHashMap> subList) {
        for (LinkedHashMap subMap : subList) {
            SubmitInfoSaveDTO subSaveDTO = new SubmitInfoSaveDTO()
                    .setType(String.valueOf(subMap.get("type")))
                    .setName(String.valueOf(subMap.get("name")))
                    .setKey(String.valueOf(subMap.get("key")));
            if (subMap.get("submitValue") != null) {
                subSaveDTO.setSubmitValue(subMap.get("submitValue"));
            }
            if (subMap.get("score") != null) {
                subSaveDTO.setScore(new BigDecimal(String.valueOf(subMap.get("score"))));
            }
            if (subMap.get("isScore") != null) {
                subSaveDTO.setIsScore(Boolean.parseBoolean(String.valueOf(subMap.get("isScore"))));
            }
            if (subMap.get("scoreType") != null) {
                subSaveDTO.setScoreType(Integer.parseInt(String.valueOf(subMap.get("scoreType"))));
            }
            if (subMap.get("required") != null) {
                subSaveDTO.setRequired(Boolean.parseBoolean(String.valueOf(subMap.get("required"))));
            }
            // 参数判断
            if (judgeFillDatas(subSaveDTO)) {
                continue;
            }
            if (SubmitInfoTypeEnum.STUDENT.getText().equals(subSaveDTO.getType())) {
                List<SubStudentInfo> detailStudentInfos = this.fillStudentInfo(subSaveDTO);
                return detailStudentInfos;
            }
        }
        return Collections.emptyList();
    }

    /**
     * @description: 填充分数-子类
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2023/1/3 下午2:59
     */
    private void fullFillSubDatas(String taskName,
                                  SubmitInfoSaveDTO saveDTO,
                                  List<StudentInfo> totalStudentInfoList,
                                  List<ScoreInfo> scoreInfoList,
                                  List<SubStudentInfo> studentInfoList,
                                  List<ScoreInfo> checkScoreInfoList,
                                  String detailId,
                                  List<ScoreInfo> subCheckScoreInfoList,
                                  List<SubStudentInfo> subStudentInfoList) {
        Assert.isTrue(!(SubmitInfoTypeEnum.SINGLE_TEXT.getText().equals(saveDTO.getType()) && String.valueOf(saveDTO.getSubmitValue()).length() > 25), () -> new BizException(BizExceptionEnum.CHECK_TXT.getMessage()));
        Assert.isTrue(!(SubmitInfoTypeEnum.MULTI_TEXT.getText().equals(saveDTO.getType())
                && String.valueOf(saveDTO.getSubmitValue()).length() > 150), () -> new BizException(BizExceptionEnum.MULTI_CHECK_TXT.getMessage()));
        // 分数控件
        if (SubmitInfoTypeEnum.SCORE.getText().equals(saveDTO.getType())) {
            Assert.isTrue(saveDTO.getScore().compareTo(BigDecimal.ZERO) >= 0, () -> new BizException("分数不能小于0"));
            ScoreInfo scoreInfo = new ScoreInfo();
            scoreInfo.setInfoName(taskName);
            scoreInfo.setInfoType(InfoTypeEnum.INFO.getCode());
            scoreInfo.setIsScore(true);
            scoreInfo.setScoreType(saveDTO.getScoreType());
            scoreInfo.setTitleId("");
            scoreInfo.setOptionId("");
            scoreInfo.setScoreValue(saveDTO.getScore());
            if (ScoreTypeEnum.PLUS.getCode().equals(saveDTO.getScoreType())) {
                scoreInfo.setScore(saveDTO.getScore());
            } else {
                scoreInfo.setScore(saveDTO.getScore().negate());
            }
            scoreInfoList.add(scoreInfo);
            return;
        }
        // 学生
        if (SubmitInfoTypeEnum.STUDENT.getText().equals(saveDTO.getType())) {
            // 学生控件数据
            List<LinkedHashMap> students = (List<LinkedHashMap>) saveDTO.getSubmitValue();
            // 学生列表
            for (LinkedHashMap student : students) {
                Assert.isTrue(StrUtil.isNotBlank(String.valueOf(student.get("classId"))), () -> new BizException("学生班级id不能为空"));
                SubStudentInfo subStudentInfo = new SubStudentInfo();
                subStudentInfo.setId(String.valueOf(student.get("id")));
                subStudentInfo.setName(String.valueOf(student.get("name")));
                subStudentInfo.setType(Integer.parseInt(String.valueOf(student.get("type"))));
                subStudentInfo.setClassId(String.valueOf(student.get("classId")));
                subStudentInfo.setSchoolId(String.valueOf(student.get("schoolId")));
                if (detailId == null) {
                    studentInfoList.add(subStudentInfo);
                } else {
                    subStudentInfoList.add(subStudentInfo);
                }
            }
            StudentInfo studentInfo = new StudentInfo()
                    .setTitleId(saveDTO.getKey())
                    .setDetailId(detailId);
            if (detailId == null) {
                studentInfo.setStudentInfos(studentInfoList);
            } else {
                studentInfo.setStudentInfos(subStudentInfoList);
            }
            totalStudentInfoList.add(studentInfo);
            return;
        }
        // 单选
        if (SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(saveDTO.getType()) &&
                ObjectUtil.isNotEmpty(saveDTO.getSubmitValue())) {
            LinkedHashMap submitValue = (LinkedHashMap) saveDTO.getSubmitValue();
            BigDecimal score = new BigDecimal(String.valueOf(submitValue.get("value")));
            ScoreInfo scoreInfo = new ScoreInfo();
            scoreInfo.setInfoName(String.valueOf(submitValue.get("label")));
            scoreInfo.setInfoType(InfoTypeEnum.OPTION.getCode());
            scoreInfo.setIsScore(saveDTO.getIsScore());
            scoreInfo.setScoreType(saveDTO.getIsScore() ? score.doubleValue() >= 0 ? ScoreTypeEnum.PLUS.getCode() : ScoreTypeEnum.REDUCE.getCode() : null);
            scoreInfo.setScore(saveDTO.getIsScore() ? score : null);
            scoreInfo.setScoreValue(saveDTO.getIsScore() ? new BigDecimal(String.valueOf(submitValue.get("value"))).abs() : null);
            scoreInfo.setTitleId(saveDTO.getKey());
            scoreInfo.setOptionId(submitValue.get("key") == null ? "" : String.valueOf(submitValue.get("key")));
            checkScoreInfoList.add(scoreInfo);
            if (detailId != null) {
                subCheckScoreInfoList.add(scoreInfo);
            }
            return;
        }
        // 多选
        if (SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(saveDTO.getType()) &&
                ObjectUtil.isNotEmpty(saveDTO.getSubmitValue())) {
            List<LinkedHashMap> submitValues = (List<LinkedHashMap>) saveDTO.getSubmitValue();
            for (LinkedHashMap submitValue : submitValues) {
                BigDecimal score = new BigDecimal(String.valueOf(submitValue.get("value")));
                ScoreInfo scoreInfo = new ScoreInfo();
                scoreInfo.setInfoName(String.valueOf(submitValue.get("label")));
                scoreInfo.setInfoType(InfoTypeEnum.OPTION.getCode());
                scoreInfo.setIsScore(saveDTO.getIsScore());
                scoreInfo.setScoreType(saveDTO.getIsScore() ? score.doubleValue() >= 0 ? ScoreTypeEnum.PLUS.getCode() : ScoreTypeEnum.REDUCE.getCode() : null);
                scoreInfo.setScore(saveDTO.getIsScore() ? score : null);
                scoreInfo.setScoreValue(saveDTO.getIsScore() ? new BigDecimal(String.valueOf(submitValue.get("value"))).abs() : null);
                scoreInfo.setTitleId(saveDTO.getKey());
                scoreInfo.setOptionId(submitValue.get("key") == null ? "" : String.valueOf(submitValue.get("key")));
                checkScoreInfoList.add(scoreInfo);
                if (detailId != null) {
                    subCheckScoreInfoList.add(scoreInfo);
                }
            }
        }
    }

    private void fillMap(SubmitInfoSaveDTO saveDTO,
                         Map<String, BigDecimal> submitInfoMap,
                         Map<String, List<SubStudentInfo>> submitStudentsMap,
                         List<SubStudentInfo> studentInfos) {
        // 分数控件
        if (SubmitInfoTypeEnum.SCORE.getText().equals(saveDTO.getType())) {
            String key = saveDTO.getKey();
            String name = saveDTO.getName();
            String optionKey = key + "-" + name;
            if (submitStudentsMap.containsKey(optionKey)) {
                submitStudentsMap.get(optionKey).addAll(studentInfos);
            } else {
                submitStudentsMap.put(optionKey, studentInfos);
            }
            if (ScoreTypeEnum.PLUS.getCode().equals(saveDTO.getScoreType())) {
                submitInfoMap.put(key + "-" + name, saveDTO.getScore());
            } else {
                submitInfoMap.put(key + "-" + name, saveDTO.getScore().negate());
            }
        }
        // 单选
        if (SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(saveDTO.getType()) &&
                ObjectUtil.isNotEmpty(saveDTO.getSubmitValue())) {
            LinkedHashMap submitValue = (LinkedHashMap) saveDTO.getSubmitValue();
            String label = String.valueOf(submitValue.get("label"));
            submitInfoMap.put(submitValue.get("key") == null ? "key" + "-" + label : submitValue.get("key") + "-" + label, new BigDecimal(Convert.toStr(submitValue.get("value"))));
            List<SubStudentInfo> studentInfoList = new ArrayList<>();
            if (submitStudentsMap.containsKey(submitValue.get("key") + "-" + label)) {
                List<SubStudentInfo> studentInfos1 = submitStudentsMap.get(submitValue.get("key") + "-" + label);
                studentInfoList.addAll(studentInfos1);
                studentInfoList.addAll(studentInfos);
            } else {
                studentInfoList.addAll(studentInfos);
            }
            submitStudentsMap.put(submitValue.get("key") == null ? "key" + "-" + label : submitValue.get("key") + "-" + label, studentInfoList);
        }
        // 多选
        if (SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(saveDTO.getType()) &&
                ObjectUtil.isNotEmpty(saveDTO.getSubmitValue())) {
            List<LinkedHashMap> submitValues = (List<LinkedHashMap>) saveDTO.getSubmitValue();
            for (LinkedHashMap submitValue : submitValues) {
                String label = String.valueOf(submitValue.get("label"));
                submitInfoMap.put(submitValue.get("key") == null ? "key" + "-" + label : submitValue.get("key") + "-" + label, new BigDecimal(Convert.toStr(submitValue.get("value"))));
                List<SubStudentInfo> studentInfoList = new ArrayList<>();
                if (submitStudentsMap.containsKey(submitValue.get("key") + "-" + label)) {
                    List<SubStudentInfo> studentInfos1 = submitStudentsMap.get(submitValue.get("key") + "-" + label);
                    studentInfoList.addAll(studentInfos1);
                    studentInfoList.addAll(studentInfos);
                } else {
                    studentInfoList.addAll(studentInfos);
                }
                submitStudentsMap.put(submitValue.get("key") == null ? "key" + "-" + label : submitValue.get("key") + "-" + label, studentInfoList);
            }
        }
    }

    private List<SubStudentInfo> fillStudentInfo(SubmitInfoSaveDTO saveDTO) {
        List<SubStudentInfo> studentInfos = new ArrayList<>();
        // 学生
        if (SubmitInfoTypeEnum.STUDENT.getText().equals(saveDTO.getType())) {
            // 学生控件数据
            List<LinkedHashMap> students = (List<LinkedHashMap>) saveDTO.getSubmitValue();
            // 学生列表
            for (LinkedHashMap student : students) {
                Assert.isTrue(StrUtil.isNotBlank(String.valueOf(student.get("classId"))), () -> new BizException("学生班级id不能为空"));
                SubStudentInfo subStudentInfo = new SubStudentInfo();
                if (!Objects.equals(Integer.parseInt(String.valueOf(student.get("type"))), 6)) {
                    continue;
                }
                subStudentInfo.setId(String.valueOf(student.get("id")));
                subStudentInfo.setName(String.valueOf(student.get("name")));
                subStudentInfo.setType(Integer.parseInt(String.valueOf(student.get("type"))));
                subStudentInfo.setClassId(String.valueOf(student.get("classId")));
                subStudentInfo.setSchoolId(String.valueOf(student.get("schoolId")));
                studentInfos.add(subStudentInfo);
            }
        }
        return studentInfos;
    }

    /**
     * 完成任务(老师)
     *
     * @param dto
     * @param now
     * @return
     */
    public Long teacherFinishTask(InfoSaveDTO dto,
                                  Date now) {
        // 完成任务
        Long taskId = dto.getTaskId();
        TaskPO taskPO = taskService.getById(taskId);
        if (taskPO == null) {
            String staffId = WebUtil.getStaffId();
            String mobile = "";
            StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO()
                    .setStaffIdList(Collections.singletonList(Long.parseLong(staffId)))
                    .setState(SaasStaffStateQueryEnum.NORMAL.getCode());
            List<StaffBatchVO> staffBatchVOS = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
            if (CollUtil.isNotEmpty(staffBatchVOS)) {
                mobile = CollUtil.getFirst(staffBatchVOS).getMobile();
            }
            // 没有任务新增并完成
            TaskSaveDTO saveDTO = new TaskSaveDTO();
            saveDTO.setTenantId(WebUtil.getTenantId());
            saveDTO.setSchoolId(WebUtil.getSchoolId());
            saveDTO.setCampusId(WebUtil.getCampusId());
            saveDTO.setTargetId(dto.getTargetId());
            saveDTO.setTaskName(dto.getTaskName());
            saveDTO.setTaskDate(now);
            saveDTO.setSubmitTime(now);
            saveDTO.setSubmitStaffId(staffId);
            saveDTO.setRoleType(TaskRoleTypeEnum.TEACHER.getCode());
            saveDTO.setTaskStatus(TaskStatusEnum.SUBMITTED.getCode());
            saveDTO.setTaskType(TaskTypeEnum.STAFF.getCode());
            saveDTO.setMobile(mobile);
            taskId = taskService.saveFinishedEvaluateTasks(saveDTO);
            return taskId;
        } else {
            taskPO.setSubmitTime(new Date());
            taskPO.setTaskStatus(TaskStatusEnum.SUBMITTED.getCode());
            taskService.updateById(taskPO);
        }

        // 完成待办
        taskService.finishTask(taskId);
        // 更新钉钉消息状态为已处理
        dingDingMsgUtil.updateMessageStatusToHandle(taskId);
        // 更新飞书消息状态为已处理
        feiShuMsgUtil.updateFeiShuMessageStatusToHandle(taskId);
        // 更新教师移动端消息状态为已处理
        channelMsgHelper.updateTeacherMobileMessageToDone(taskId);

        return taskId;
    }


    /**
     * 完成任务(家长)
     *
     * @param dto
     * @param now
     * @param targetId
     * @return
     */
    public Long parentFinishTask(InfoSaveDTO dto, Date now, Long targetId, Integer submitRoleType) {
        // 完成任务
        Long taskId = dto.getTaskId();
        TaskPO taskPO = taskService.getById(taskId);
        // 查询指标信息
        Target target = targetLogic.getById(targetId);
        Assert.notNull(target, () -> new BizException("指标不存在"));
        // 检测家长提交是否超过提交次数
        this.checkParentSubmitTimes(target, dto.getSubmitStaffId());

        if (taskPO == null) {

            // 没有任务新增并完成
            TaskSaveDTO saveDTO = new TaskSaveDTO();
            saveDTO.setTenantId(target.getTenantId());
            saveDTO.setSchoolId(target.getSchoolId());
            saveDTO.setCampusId(target.getCampusId());

            // 设置提醒时间
            saveDTO.setRemindTime(new Date());
            // 设置催办时间
            Date urgeTime = DateUtil.offsetHour(new Date(), target.getUrgeTime());
            saveDTO.setUrgeTime(urgeTime);
            saveDTO.setTargetId(dto.getTargetId());
            saveDTO.setTaskName(dto.getTaskName());
            saveDTO.setTaskDate(now);
            saveDTO.setSubmitTime(now);
            saveDTO.setSubmitStaffId(dto.getSubmitStaffId());
            saveDTO.setRoleType(submitRoleType);
            saveDTO.setTaskStatus(TaskStatusEnum.SUBMITTED.getCode());
            saveDTO.setApprovalStatus(TaskApprovalEnum.APPROVAL_ING.getCode());
            saveDTO.setTaskType(TaskTypeEnum.STAFF.getCode());
            saveDTO.setCreateBy(WebUtil.getStudentIdStr());
            taskId = taskService.saveFinishedEvaluateTasks(saveDTO);
            return taskId;
        } else {
            taskPO.setSubmitTime(new Date());
            taskPO.setTaskStatus(TaskStatusEnum.SUBMITTED.getCode());
            taskPO.setApprovalStatus(TaskApprovalEnum.APPROVAL_ING.getCode());
            taskPO.setUpdateBy(WebUtil.getStudentIdStr());
            taskPO.setUpdateTime(new Date());
            taskService.updateById(taskPO);
        }

        // 完成待办
        taskService.finishTask(taskId);

//        //发送老师审核钉钉通知 todo
//        sendDingDingMsg(task, infoId);

        return taskId;
    }

    /**
     * 检测家长提交是否超过提交次数
     */
    public void checkParentSubmitTimes(Target target, String submitStaffId) {
        // 检查指标是否存在
        Assert.notNull(target, () -> new BizException("指标不存在"));

        // 获取限制标志和频率类型
        Integer parentSubmitLimitFlag = target.getParentSubmitLimitFlag();
        Integer submitType = target.getSubmitType();

        // 如果不限制次数或频率类型为无限制，直接返回
        if (Constant.NO.equals(parentSubmitLimitFlag) || SubmitRateEnum.NO_RATE.getSubmitType().equals(submitType)) {
            log.warn("【检测家长提交是否超过提交次数】,该指标无需校验，指标id：{}", target.getId());
            return;
        }

        // 获取频率类型的枚举对象
        SubmitRateEnum rateEnum = SubmitRateEnum.getEnumByType(submitType);
        if (rateEnum == null) {
            log.warn("不支持的提交类型，submitType：{}", submitType);
            return;
        }

        // 获取学生信息
        StudentInfoVO studentInfoVO = getStudentInfo(submitStaffId);

        // 获取时间范围
        DateTimeRangeDTO timeRange = getTimeRange(rateEnum, studentInfoVO);

        // 查询提交次数
        Long limitCount = behaviourRecordManager.countByCondition(
                studentInfoVO.getStudentId(),
                studentInfoVO.getClassId(),
                target.getId(),
                timeRange.getBeginTime(),
                timeRange.getEndTime()
        );

        // 获取提交频率名称
        String submitRateName = getSubmitRateName(rateEnum);

        // 获取次数限制
        Integer parentSubmitLimitTimes = target.getParentSubmitLimitTimes();
        Integer times = ObjectUtil.isNull(parentSubmitLimitTimes) ? Constant.ONE : parentSubmitLimitTimes;

        // 校验次数限制逻辑
        if (times <= limitCount) {
            log.warn("家长{}提交的次数超过限制次数{},学生id：{},指标 id：{}", submitRateName, times, submitStaffId, target.getId());
            throw new BizException(String.format("%s只能提交%d次", submitRateName, times));
        }
    }

    /**
     * 获取学生信息
     *
     * @param submitStaffId 提交员工 ID（实际为学生 ID）
     * @return 学生信息对象
     */
    private StudentInfoVO getStudentInfo(String submitStaffId) {
        Long studentId = Convert.toLong(submitStaffId);
        StudentDTO studentDTO = new StudentDTO();
        studentDTO.setStudentIds(CollUtil.toList(studentId));
        return saasStudentManager.studentDetail(studentDTO)
                .stream()
                .filter(s -> Constant.ZERO.equals(Convert.toInt(s.getUpgradeStatus()))
                                && Constant.ZERO.equals(Convert.toInt(s.getStudentStatus()))
                                && Constant.ZERO.equals(Convert.toInt(s.getClassType())))
                .findFirst()
                .orElseThrow(() -> new BizException("学生不存在！"));
    }

    /**
     * 获取时间范围
     *
     * @param rateEnum    提交频率枚举
     * @param studentInfoVO 学生信息对象
     * @return 时间范围对象
     */
    private DateTimeRangeDTO getTimeRange(SubmitRateEnum rateEnum, StudentInfoVO studentInfoVO) {
        DateTime beginTime;
        DateTime endTime;
        if (SubmitRateEnum.TERM == rateEnum) {
            TermVo currentTermVo = getCurrentTermVo(studentInfoVO);
            beginTime = DateUtil.beginOfDay(DateUtil.parseDate(currentTermVo.getStartTime()));
            endTime = DateUtil.endOfDay(DateUtil.parseDate(currentTermVo.getEndTime()));
        } else {
            DateTime today = DateUtil.date();
            beginTime = rateEnum.getBeginTime(today);
            endTime = rateEnum.getEndTime(today);
        }
        return new DateTimeRangeDTO(beginTime, endTime);
    }

    /**
     * 获取当前学期信息
     *
     * @param studentInfoVO 学生信息对象
     * @return 当前学期信息对象
     */
    private TermVo getCurrentTermVo(StudentInfoVO studentInfoVO) {
        TermQuery termQuery = new TermQuery();
        termQuery.setCampusSectionId(Convert.toStr(studentInfoVO.getCampusSectionId()));
        termQuery.setCampusId(studentInfoVO.getCampusId());
        termQuery.setSchoolId(studentInfoVO.getSchoolId());
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        return termVos.stream()
                .filter(TermVo::isCurrentTerm)
                .findFirst()
                .orElseThrow(() -> new BizException("当前学期不存在！"));
    }

    /**
     * 获取提交频率名称
     *
     * @param rateEnum 提交频率枚举
     * @return 提交频率名称
     */
    private String getSubmitRateName(SubmitRateEnum rateEnum) {
        if (SubmitRateEnum.WORK_DAY.equals(rateEnum)) {
            return "每天";
        } else if (SubmitRateEnum.TERM.equals(rateEnum)) {
            return "每学期";
        }
        return SubmitRateEnum.getSubmitRateName(rateEnum.getSubmitType());
    }


    /**
     * 发送老师审核钉钉通知
     *
     * @param taskPO
     */
    public void sendDingDingMsg(TaskPO taskPO, String infoId, Long reviewTeacherId, String reviewTeacherMobile,
                                StudentVO studentVO) {
        if (Objects.nonNull(taskPO) && StrUtil.isNotBlank(infoId)) {
            OapiMessageCorpconversationAsyncsendV2Request.OA oa = new OapiMessageCorpconversationAsyncsendV2Request.OA();
            OapiMessageCorpconversationAsyncsendV2Request.Body body = new OapiMessageCorpconversationAsyncsendV2Request.Body();
            OapiMessageCorpconversationAsyncsendV2Request.Head head = new OapiMessageCorpconversationAsyncsendV2Request.Head();
            OapiMessageCorpconversationAsyncsendV2Request.StatusBar statusBar = new OapiMessageCorpconversationAsyncsendV2Request.StatusBar();
            oa.setHead(head);
            head.setText("综合素质评估");
            head.setBgcolor(titleColor);
            oa.setBody(body);
            body.setTitle(taskPO.getTaskName() + "待审批");
            body.setContent(
                    "老师您好，" + studentVO.getStudentName() + "的家长提交了一份" + taskPO.getTaskName() + "待你审批");
            oa.setStatusBar(statusBar);
            statusBar.setStatusValue("待处理");
            statusBar.setStatusBg("0x" + unhandleColor);
            // 学生提交审核跳转链接
            String approvalUrl = "/home/<USER>";
            if (TaskRoleTypeEnum.STUDENT.getCode().equals(taskPO.getRoleType())){
                approvalUrl = "/home/<USER>/evaluate-student-approval?";
            }
            oa.setMessageUrl(messageUrl + approvalUrl +
                    "&tenantId=" + taskPO.getTenantId() +
                    "&schoolId=" + taskPO.getSchoolId() +
                    "&campusId=" + taskPO.getCampusId() +
                    "&infoId=" + infoId +
                    "&studentId=" + Convert.toStr(studentVO.getStudentId()) +
                    "&staffId=" + reviewTeacherId +
                    "&fromMsg=1" +
                    "&roleType=" + taskPO.getRoleType());
            Long ddTaskId = 0L;
            try {
                ddTaskId = dingDingMsgUtil.sendDingOaMessageNew(oa, reviewTeacherMobile, taskPO.getTenantId());
            } catch (Exception e) {
                log.warn("[{}-提交提醒-必填任务]，sendDingOaMessage发生异常", ModuleNameEnum.DINGDING.getMessage(), e);
            }

            MessageLog messageLog = new MessageLog();
            messageLog.setMobile(reviewTeacherMobile);
            messageLog.setContent(oa.getBody().getContent());
            messageLog.setBusinessId(taskPO.getId());
            messageLog.setBusinessType(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getCode());
            messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getMessage());
            messageLog.setMessageId(ddTaskId);
            messageLog.setMessageType(MessageLogMessageTypeEnum.MUST_REMIND.getCode());
            messageLog.setTermintalType(MessageLogTerminalTypeEnum.DINGDING.getCode());
            messageLog.setSchoolId(taskPO.getSchoolId());
            messageLog.setCampusId(taskPO.getCampusId());
            messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
            messageLog.setUserId(Long.parseLong(taskPO.getSubmitStaffId()));
            messageLog.setUserType(TaskRoleTypeEnum.TEACHER.getCode());
            messageLog.setTenantId(taskPO.getTenantId());
            messageLogService.save(messageLog);
        }
    }

    /**
     * 发送老师审核微信通知
     *
     * @param taskPO "班级通知
     *             班级：{{keyword1.DATA}}
     *             通知人：学生处
     *             时间：{{keyword3.DATA}}
     *             通知内容：「学生」的家长提交了一份「评价任务名称」待你审批"""
     */
    public void sendWechatMsg(TaskPO taskPO, String infoId, StudentVO studentVO,
                              Long reviewTeacherId, String reviewTeacherMobile) {
        if (Objects.isNull(taskPO) || CharSequenceUtil.isBlank(infoId)) return;
        MsgContent content = new MsgContent();
        content.setKeyword1(studentVO.getClassName());
        content.setKeyword2("学生处");
        content.setKeyword3(DateUtil.formatDateTime(DateUtil.date()));
        content.setKeyword4(studentVO.getStudentName() + "的家长提交了一份" + taskPO.getTaskName() + "待你审批");

        SendMsgRequestDTO request = new SendMsgRequestDTO();
        request.setMobile(reviewTeacherMobile);
        request.setContent(content);
        request.setUrlType(1);
        // 学生提交审核跳转链接
        String approvalUrl = "/home/<USER>";
        if (TaskRoleTypeEnum.STUDENT.getCode().equals(taskPO.getRoleType())){
            approvalUrl = "/home/<USER>/evaluate-student-approval?";
        }
        request.setUrl(messageUrl + approvalUrl +
                "&tenantId=" + taskPO.getTenantId() +
                "&schoolId=" + taskPO.getSchoolId() +
                "&campusId=" + taskPO.getCampusId() +
                "&infoId=" + infoId +
                "&studentId=" + Convert.toStr(studentVO.getStudentId()) +
                "&staffId=" + reviewTeacherId +
                "&fromMsg=1" +
                "&roleType=" + taskPO.getRoleType());
        try {
            wechatManager.sendCommonMsg(request);
        } catch (Exception e) {
            log.warn("[{}-提交提醒-必填任务]，sendWechatMsg发生异常", ModuleNameEnum.WECHAT.getMessage(), e);
        }

        MessageLog messageLog = new MessageLog();
        messageLog.setMobile(reviewTeacherMobile);
        messageLog.setContent(JSONUtil.toJsonStr(content));
        messageLog.setBusinessId(taskPO.getId());
        messageLog.setBusinessType(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getCode());
        messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getMessage());
//        messageLog.setMessageId(IdUtil.getSnowflakeNextId());
        messageLog.setMessageId(SnowFlakeIdUtil.nextId());
        messageLog.setMessageType(MessageLogMessageTypeEnum.MUST_REMIND.getCode());
        messageLog.setTermintalType(MessageLogTerminalTypeEnum.WECHAT.getCode());
        messageLog.setSchoolId(taskPO.getSchoolId());
        messageLog.setCampusId(taskPO.getCampusId());
        messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
        messageLog.setUserId(Long.parseLong(taskPO.getSubmitStaffId()));
        messageLog.setUserType(TaskRoleTypeEnum.TEACHER.getCode());
        messageLog.setTenantId(taskPO.getTenantId());
        messageLogService.save(messageLog);
    }

    /**
     * 发送老师重新添加审核钉钉通知
     *
     * @param taskPO
     */
    public void sendResubmitDingDingMsg(TaskPO taskPO, String infoId, Long reviewTeacherId, String reviewTeacherMobile,
                                        StudentVO studentVO) {
        if (Objects.nonNull(taskPO) && StrUtil.isNotBlank(infoId)) {
            OapiMessageCorpconversationAsyncsendV2Request.OA oa = new OapiMessageCorpconversationAsyncsendV2Request.OA();
            OapiMessageCorpconversationAsyncsendV2Request.Body body = new OapiMessageCorpconversationAsyncsendV2Request.Body();
            OapiMessageCorpconversationAsyncsendV2Request.Head head = new OapiMessageCorpconversationAsyncsendV2Request.Head();
            OapiMessageCorpconversationAsyncsendV2Request.StatusBar statusBar = new OapiMessageCorpconversationAsyncsendV2Request.StatusBar();
            oa.setHead(head);
            head.setText("综合素质评估");
            head.setBgcolor(titleColor);
            oa.setBody(body);
            body.setTitle(taskPO.getTaskName() + "待审批");
            body.setContent("老师您好，" + studentVO.getStudentName() + "的家长重新提交了" + taskPO.getTaskName() + "待你审批");
            oa.setStatusBar(statusBar);
            statusBar.setStatusValue("待处理");
            statusBar.setStatusBg("0x" + unhandleColor);
            oa.setMessageUrl(messageUrl + "/home/<USER>" +
                    "&tenantId=" + taskPO.getTenantId() +
                    "&schoolId=" + taskPO.getSchoolId() +
                    "&campusId=" + taskPO.getCampusId() +
                    "&infoId=" + infoId +
                    "&studentId=" + Convert.toStr(studentVO.getStudentId()) +
                    "&staffId=" + reviewTeacherId +
                    "&fromMsg=1");
            Long ddTaskId = 0L;
            try {
                ddTaskId = dingDingMsgUtil.sendDingOaMessageNew(oa, reviewTeacherMobile, taskPO.getTenantId());
            } catch (Exception e) {
                log.warn("[{}-提交提醒-必填任务]，sendDingOaMessage发生异常", ModuleNameEnum.DINGDING.getMessage(), e);
            }

            MessageLog messageLog = new MessageLog();
            messageLog.setMobile(reviewTeacherMobile);
            messageLog.setContent(oa.getBody().getContent());
            messageLog.setBusinessId(taskPO.getId());
            messageLog.setBusinessType(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getCode());
            messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getMessage());
            messageLog.setMessageId(ddTaskId);
            messageLog.setMessageType(MessageLogMessageTypeEnum.MUST_REMIND.getCode());
            messageLog.setTermintalType(MessageLogTerminalTypeEnum.DINGDING.getCode());
            messageLog.setSchoolId(taskPO.getSchoolId());
            messageLog.setCampusId(taskPO.getCampusId());
            messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
            messageLog.setUserId(Long.parseLong(taskPO.getSubmitStaffId()));
            messageLog.setUserType(TaskRoleTypeEnum.TEACHER.getCode());
            messageLog.setTenantId(taskPO.getTenantId());
            messageLogService.save(messageLog);
        }
    }

    /**
     * 发送老师重新添加审核微信通知
     *
     * @param taskPO "班级通知
     *             班级：{{keyword1.DATA}}
     *             通知人：学生处
     *             时间：{{keyword3.DATA}}
     *             通知内容：「学生」的家长的家长重新提交了「评价任务名称」待你审批"
     */
    public void sendResubmitWechatMsg(TaskPO taskPO, String infoId, StudentVO studentVO,
                                      Long reviewTeacherId, String reviewTeacherMobile) {
        if (Objects.isNull(taskPO) || CharSequenceUtil.isBlank(infoId)) return;
        MsgContent content = new MsgContent();
        content.setKeyword1(studentVO.getClassName());
        content.setKeyword2("学生处");
        content.setKeyword3(DateUtil.formatDateTime(DateUtil.date()));
        content.setKeyword4(studentVO.getStudentName() + "的家长重新提交了" + taskPO.getTaskName() + "待你审批");

        SendMsgRequestDTO request = new SendMsgRequestDTO();
        request.setMobile(reviewTeacherMobile);
        request.setContent(content);
        request.setUrlType(1);
        request.setUrl(messageUrl + "/home/<USER>" +
                "&tenantId=" + taskPO.getTenantId() +
                "&schoolId=" + taskPO.getSchoolId() +
                "&campusId=" + taskPO.getCampusId() +
                "&infoId=" + infoId +
                "&studentId=" + Convert.toStr(studentVO.getStudentId()) +
                "&staffId=" + reviewTeacherId +
                "&fromMsg=1");
        try {
            wechatManager.sendCommonMsg(request);
        } catch (Exception e) {
            log.warn("[{}-提交提醒-必填任务]，sendWechatMsg发生异常", ModuleNameEnum.WECHAT.getMessage(), e);
        }

        MessageLog messageLog = new MessageLog();
        messageLog.setMobile(reviewTeacherMobile);
        messageLog.setContent(JSONUtil.toJsonStr(content));
        messageLog.setBusinessId(taskPO.getId());
        messageLog.setBusinessType(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getCode());
        messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getMessage());
//        messageLog.setMessageId(IdUtil.getSnowflakeNextId());
        messageLog.setMessageId(SnowFlakeIdUtil.nextId());
        messageLog.setMessageType(MessageLogMessageTypeEnum.MUST_REMIND.getCode());
        messageLog.setTermintalType(MessageLogTerminalTypeEnum.WECHAT.getCode());
        messageLog.setSchoolId(taskPO.getSchoolId());
        messageLog.setCampusId(taskPO.getCampusId());
        messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
        messageLog.setUserId(Long.parseLong(taskPO.getSubmitStaffId()));
        messageLog.setUserType(TaskRoleTypeEnum.TEACHER.getCode());
        messageLog.setTenantId(taskPO.getTenantId());
        messageLogService.save(messageLog);
    }

    /**
     * @description: 填充统计数据
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2023/1/3 下午2:59
     */
    public List<BehaviourRecordSaveDTO> generateRecords(List<Map<String, List>> infoList,
                                                        Date now,
                                                        Long taskId,
                                                        String evaluateInfoId,
                                                        Long targetId) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();
        // 参数判断
        Target target = targetLogic.getByIdWithOutDeleted(targetId);
        Assert.isTrue(target != null, () -> new BizException(BizExceptionEnum.TARGET_NOT_NULL.getMessage()));
        TargetGroup group = targetGroupLogic.getByIdWithOutDeleted(target.getGroupId());
        Assert.isTrue(group != null, () -> new BizException(BizExceptionEnum.TARGET_GROUP_NOT_NULL.getMessage()));
        List<BehaviourRecordSaveDTO> behaviourRecordSaveDTOs = new ArrayList<>(512);

        log.info("填充统计数据：{}", JSONUtil.toJsonStr(infoList));

        List<SubStudentInfo> uniqueStudentInfos = new ArrayList<>();
        Map<String, Boolean> uniqueMap = new HashMap<>();
        for (Map<String, List> map : infoList) {
            List<SubStudentInfo> studentInfos = map.get("student");
            // 没学生不统计
            if (studentInfos == null) {
                continue;
            }
            // 学生去重
            uniqueStudentInfos.clear();
            uniqueMap.clear();
            for (SubStudentInfo studentInfo : studentInfos) {
                if (uniqueMap.get(studentInfo.getId()) != null) {
                    continue;
                }
                if (StudentInfoTypeEnum.STUDENT.getCode().equals(studentInfo.getType())) {
                    uniqueStudentInfos.add(studentInfo);
                    uniqueMap.put(studentInfo.getId(), true);
                } else if (StudentInfoTypeEnum.CLASS.getCode().equals(studentInfo.getType()) || StudentInfoTypeEnum.GRADE.getCode().equals(studentInfo.getType()) || StudentInfoTypeEnum.SECTION.getCode().equals(studentInfo.getType())) {
                    EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
                    eduStudentPageQueryDTO.setSchoolId(Long.parseLong(studentInfo.getSchoolId()));
                    eduStudentPageQueryDTO.setClassId(StudentInfoTypeEnum.CLASS.getCode().equals(studentInfo.getType()) ? Long.parseLong(studentInfo.getId()) : null);
                    eduStudentPageQueryDTO.setGradeId(StudentInfoTypeEnum.GRADE.getCode().equals(studentInfo.getType()) ? Long.parseLong(studentInfo.getId()) : null);
                    eduStudentPageQueryDTO.setCampusSectionId(StudentInfoTypeEnum.SECTION.getCode().equals(studentInfo.getType()) ? Long.parseLong(studentInfo.getId()) : null);
                    eduStudentPageQueryDTO.setPageSize(Integer.MAX_VALUE);
                    List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoService.queryStudentPage(eduStudentPageQueryDTO);
                    if (CollUtil.isEmpty(eduStudentInfoVOS)) {
                        log.warn("统计行为获取学生为空");
                        continue;
                    }
                    for (EduStudentInfoVO vo : eduStudentInfoVOS) {
                        if (uniqueMap.get(String.valueOf(vo.getId())) != null) {
                            continue;
                        }
                        SubStudentInfo student = new SubStudentInfo();
                        student.setId(String.valueOf(vo.getId()));
                        student.setName(vo.getStudentName());
                        student.setType(StudentInfoTypeEnum.STUDENT.getCode());
                        student.setSchoolId(studentInfo.getSchoolId());
                        student.setClassId(String.valueOf(vo.getClassId()));
                        uniqueStudentInfos.add(student);
                        uniqueMap.put(student.getId(), true);
                    }
                }
            }

            List<ScoreInfo> scoreInfos = map.get("score");
            log.warn("[评估任务-教师端保存]-[step.1.7.1]-[获取学生数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
            TIME_INTERVAL.restart();
            for (SubStudentInfo studentInfo : uniqueStudentInfos) {
                ClassGradeInfoDTO classGradeInfo = saasGeneralManager.getClassGradeInfo(studentInfo.getClassId());
                if (ObjectUtil.isEmpty(classGradeInfo)) {
                    log.warn("[评估任务-教师端保存],classGradeInfo为空，直接忽略，执行下个学生，当前学生信息：{}", JSONUtil.toJsonStr(classGradeInfo));
                    continue;
                }
                for (ScoreInfo scoreInfo : scoreInfos) {
                    BehaviourRecordSaveDTO saveDTO = new BehaviourRecordSaveDTO();
                    if (Objects.nonNull(classGradeInfo)) {
                        saveDTO.setGradeId(classGradeInfo.getGradeId());
                        saveDTO.setGradeCode(classGradeInfo.getGradeCode());
                        saveDTO.setCampusSectionId(classGradeInfo.getCampusSectionId());
                        saveDTO.setCampusSectionCode(classGradeInfo.getCampusSectionCode());
                    }

                    saveDTO.setTenantId(WebUtil.getTenantId());
                    saveDTO.setTemplateId(target.getTemplateId());
                    saveDTO.setSchoolId(WebUtil.getSchoolId());
                    saveDTO.setCampusId(WebUtil.getCampusId());
                    saveDTO.setClassId(studentInfo.getClassId());
                    saveDTO.setStudentId(studentInfo.getId());
                    saveDTO.setModuleCode(group.getModuleCode());
                    saveDTO.setTargetId(targetId);
                    saveDTO.setNotPartCount(target.getTargetNotPartCount());
                    saveDTO.setTargetName(target.getTargetName());
                    saveDTO.setTitleId(scoreInfo.getTitleId());
                    saveDTO.setOptionId(scoreInfo.getOptionId());
                    saveDTO.setTaskId(taskId);
                    saveDTO.setInfoId(evaluateInfoId);
                    saveDTO.setInfoType(scoreInfo.getInfoType());
                    saveDTO.setInfoName(scoreInfo.getInfoName());
                    saveDTO.setScoreType(scoreInfo.getScoreType());
                    saveDTO.setIsScore(scoreInfo.getIsScore());
                    saveDTO.setScore(scoreInfo.getScore());
                    saveDTO.setScoreValue(scoreInfo.getScoreValue());
                    saveDTO.setDataSource(DataSourceEnum.EVALUATE_IMAGE_TEXT.getCode());
                    saveDTO.setSubmitTime(now);
                    behaviourRecordSaveDTOs.add(saveDTO);
                }
            }
            log.warn("[评估任务-教师端保存]-[step.1.7.2]-[获取学生数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
            TIME_INTERVAL.restart();
        }
        return behaviourRecordSaveDTOs;
    }

    /**
     * @description: 填充统计数据
     * @param:
     * @return: void
     * <AUTHOR>
     * @date: 2023/1/3 下午2:59
     */
    public List<BehaviourRecordSaveDTO> generateRecordsV2(List<Map<String, List>> infoList,
                                                          Date now,
                                                          Long taskId,
                                                          String evaluateInfoId,
                                                          Long targetId) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();
        // 参数判断
        Target target = targetLogic.getByIdWithOutDeleted(targetId);
        Assert.isTrue(target != null, () -> new BizException(BizExceptionEnum.TARGET_NOT_NULL.getMessage()));
        TargetGroup group = targetGroupLogic.getByIdWithOutDeleted(target.getGroupId());
        Assert.isTrue(group != null, () -> new BizException(BizExceptionEnum.TARGET_GROUP_NOT_NULL.getMessage()));
        List<BehaviourRecordSaveDTO> behaviourRecordSaveDTOs = new ArrayList<>(512);

        log.info("填充统计数据：{}", JSONUtil.toJsonStr(infoList));
        List<SubStudentInfo> uniqueStudentInfos = new ArrayList<>();
        Map<String, Boolean> uniqueMap = new HashMap<>();
        for (Map<String, List> map : infoList) {
            List<SubStudentInfo> studentInfos = map.get("student");
            // 没学生不统计
            if (studentInfos == null) {
                continue;
            }
            // 学生
            uniqueStudentInfos.clear();
            uniqueMap.clear();
            for (SubStudentInfo studentInfo : studentInfos) {

                if (StudentInfoTypeEnum.STUDENT.getCode().equals(studentInfo.getType())) {
                    if (uniqueMap.containsKey(studentInfo.getId() + studentInfo.getClassId())) {
                        continue;
                    }
                    uniqueStudentInfos.add(studentInfo);
                    uniqueMap.put(studentInfo.getId() + studentInfo.getClassId(), true);
                } else if (StudentInfoTypeEnum.GRADE.getCode().equals(studentInfo.getType()) || StudentInfoTypeEnum.SECTION.getCode().equals(studentInfo.getType())) {
                    EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
                    eduStudentPageQueryDTO.setSchoolId(Long.parseLong(studentInfo.getSchoolId()));
                    eduStudentPageQueryDTO.setClassId(StudentInfoTypeEnum.CLASS.getCode().equals(studentInfo.getType()) ? Long.parseLong(studentInfo.getId()) : null);
                    eduStudentPageQueryDTO.setGradeId(StudentInfoTypeEnum.GRADE.getCode().equals(studentInfo.getType()) ? Long.parseLong(studentInfo.getId()) : null);
                    eduStudentPageQueryDTO.setCampusSectionId(StudentInfoTypeEnum.SECTION.getCode().equals(studentInfo.getType()) ? Long.parseLong(studentInfo.getId()) : null);
                    eduStudentPageQueryDTO.setPageSize(Integer.MAX_VALUE);
                    List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoService.queryStudentPageAll(eduStudentPageQueryDTO);
                    if (CollUtil.isEmpty(eduStudentInfoVOS)) {
                        log.warn("统计行为获取学生为空");
                        continue;
                    }
                    for (EduStudentInfoVO vo : eduStudentInfoVOS) {
                        if (uniqueMap.containsKey(Convert.toStr(vo.getId()) + Convert.toStr(vo.getClassId()))) {
                            continue;
                        }
                        SubStudentInfo student = new SubStudentInfo();
                        student.setId(Convert.toStr(vo.getId()));
                        student.setName(vo.getStudentName());
                        student.setType(StudentInfoTypeEnum.STUDENT.getCode());
                        student.setSchoolId(studentInfo.getSchoolId());
                        student.setClassId(Convert.toStr(vo.getClassId()));
                        uniqueStudentInfos.add(student);
                        uniqueMap.put(Convert.toStr(vo.getId()) + Convert.toStr(vo.getClassId()), true);
                    }
                } else if (StudentInfoTypeEnum.CLASS.getCode().equals(studentInfo.getType())) {
                    //根据班级 id 获取学生
                    StudentPageQueryDTO eduStudentPageQueryDTO = new StudentPageQueryDTO();
                    eduStudentPageQueryDTO.setSchoolId(Convert.toLong(studentInfo.getSchoolId()));
                    eduStudentPageQueryDTO.setClassId(Convert.toLong(studentInfo.getId()));
                    eduStudentPageQueryDTO.setPageSize(Integer.MAX_VALUE);
                    List<StudentInfo1VO> eduStudentInfoVOList = saasStudentManager.queryStudentPage(eduStudentPageQueryDTO);
                    if (eduStudentInfoVOList.isEmpty()) {
                        log.info("统计行为获取班级为空,班级 id：【{}】", studentInfo.getId());
                        continue;
                    }
                    for (StudentInfo1VO vo : eduStudentInfoVOList) {
                        if (uniqueMap.containsKey(Convert.toStr(vo.getId()) + studentInfo.getId())) {
                            continue;
                        }
                        SubStudentInfo student = new SubStudentInfo();
                        student.setId(Convert.toStr(vo.getId()));
                        student.setName(vo.getStudentName());
                        student.setType(StudentInfoTypeEnum.STUDENT.getCode());
                        student.setSchoolId(studentInfo.getSchoolId());
                        student.setClassId(studentInfo.getId());
                        uniqueStudentInfos.add(student);
                        uniqueMap.put(Convert.toStr(vo.getId()) + studentInfo.getId(), true);
                    }
                }
            }

            List<ScoreInfo> scoreInfos = map.get("score");
            log.warn("[评估任务-教师端保存]-[step.1.7.1]-[获取学生数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
            TIME_INTERVAL.restart();
            for (SubStudentInfo studentInfo : uniqueStudentInfos) {
                ClassGradeInfoDTO classGradeInfo = saasGeneralManager.getClassGradeInfo(studentInfo.getClassId());
                if (ObjectUtil.isEmpty(classGradeInfo)) {
                    log.warn("[评估任务-教师端保存],classGradeInfo为空，直接忽略，执行下个学生，当前学生信息：{}", JSONUtil.toJsonStr(classGradeInfo));
                    continue;
                }
                for (ScoreInfo scoreInfo : scoreInfos) {
                    BehaviourRecordSaveDTO saveDTO = new BehaviourRecordSaveDTO();
                    if (Objects.nonNull(classGradeInfo)) {
                        saveDTO.setGradeId(classGradeInfo.getGradeId());
                        saveDTO.setGradeCode(classGradeInfo.getGradeCode());
                        saveDTO.setCampusSectionId(classGradeInfo.getCampusSectionId());
                        saveDTO.setCampusSectionCode(classGradeInfo.getCampusSectionCode());
                    }

                    saveDTO.setTenantId(WebUtil.getTenantId());
                    saveDTO.setTemplateId(target.getTemplateId());
                    saveDTO.setSchoolId(WebUtil.getSchoolId());
                    saveDTO.setCampusId(WebUtil.getCampusId());
                    saveDTO.setClassId(studentInfo.getClassId());
                    saveDTO.setStudentId(studentInfo.getId());
                    saveDTO.setModuleCode(group.getModuleCode());
                    saveDTO.setTargetId(targetId);
                    saveDTO.setNotPartCount(target.getTargetNotPartCount());
                    saveDTO.setTitleId(scoreInfo.getTitleId());
                    saveDTO.setOptionId(scoreInfo.getOptionId());
                    saveDTO.setTaskId(taskId);
                    saveDTO.setInfoId(evaluateInfoId);
                    saveDTO.setInfoType(scoreInfo.getInfoType());
                    saveDTO.setInfoName(scoreInfo.getInfoName());
                    saveDTO.setScoreType(scoreInfo.getScoreType());
                    saveDTO.setIsScore(scoreInfo.getIsScore());
                    saveDTO.setScore(scoreInfo.getScore());
                    saveDTO.setScoreValue(scoreInfo.getScoreValue());
                    saveDTO.setDataSource(DataSourceEnum.EVALUATE_IMAGE_TEXT.getCode());
                    saveDTO.setSubmitTime(now);
                    //新增指标名称
                    saveDTO.setTargetName(target.getTargetName());
                    behaviourRecordSaveDTOs.add(saveDTO);
                }
            }
            log.warn("[评估任务-教师端保存]-[step.1.7.2]-[获取学生数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
            TIME_INTERVAL.restart();
        }
        return behaviourRecordSaveDTOs;
    }


    private static StudentClassQueryDTO getEduStudentClassQueryDTO(List<Long> studentIdList) {
        StudentClassQueryDTO eduStudentClassQueryDTO = new StudentClassQueryDTO();
        List<String> classTypes = new ArrayList<>();
        classTypes.add(SaasClassTypeEnum.XINGZHENG.getCode());
        classTypes.add(SaasClassTypeEnum.XUANKAO.getCode());
        classTypes.add(SaasClassTypeEnum.XUEKAO.getCode());
        classTypes.add(SaasClassTypeEnum.XUANXIU.getCode());
        classTypes.add(SaasClassTypeEnum.XINGQU.getCode());
        eduStudentClassQueryDTO.setClassTypes(classTypes);
        eduStudentClassQueryDTO.setStudentIds(studentIdList);
        eduStudentClassQueryDTO.setGraduationStatus("0");
        return eduStudentClassQueryDTO;
    }

    /**
     * 发送老师审核飞书通知
     *
     * @param taskPO
     */
    public void sendFeiShuMsg(TaskPO taskPO, String infoId, Long reviewTeacherId, String reviewTeacherMobile,
                              StudentVO studentVO) {
        if (Objects.isNull(taskPO) || StrUtil.isBlank(infoId)) {
            log.info("[发送老师审核飞书通知]参数为空，入参task:{}，infoId:{}", JSONUtil.toJsonStr(taskPO), infoId);
            return;
        }
        String callBackId = SnowFlakeIdUtil.nextIdStr();
        // 发送飞书消息
        FeiMsgReqDTO feiMsgReqDTO = new FeiMsgReqDTO();
        feiMsgReqDTO.setTitle(taskPO.getTaskName() + "待审批");
        // 学生提交审核跳转链接
        String approvalUrl = "/home/<USER>";
        if (TaskRoleTypeEnum.STUDENT.getCode().equals(taskPO.getRoleType())){
            approvalUrl = "/home/<USER>/evaluate-student-approval?";
        }
        feiMsgReqDTO.setMsgUrl(messageUrl + approvalUrl +
                "&tenantId=" + taskPO.getTenantId() +
                "&schoolId=" + taskPO.getSchoolId() +
                "&campusId=" + taskPO.getCampusId() +
                "&infoId=" + infoId +
                "&studentId=" + Convert.toStr(studentVO.getStudentId()) +
                "&staffId=" + reviewTeacherId +
                "&fromMsg=1" +
                "&roleType=" + taskPO.getRoleType());
        feiMsgReqDTO.setContext(
                "老师您好，" + studentVO.getStudentName() + "的家长提交了一份" + taskPO.getTaskName() + "待你审批");
        feiMsgReqDTO.setMobile(reviewTeacherMobile);
        feiMsgReqDTO.setCallBackId(callBackId);
        FeiShuResultDTO feiShuResultDTO = new FeiShuResultDTO();
        try {
            feiShuResultDTO = feiShuMsgUtil.sendHandleStatusMessage(feiMsgReqDTO);
        } catch (Exception e) {
            log.warn("[{}-提交提醒-必填任务]，sendHandleStatusMessage发生异常", ModuleNameEnum.FEISHU.getMessage(), e);
        }

        MessageLog messageLog = new MessageLog();
        messageLog.setMobile(reviewTeacherMobile);
        messageLog.setContent(feiShuResultDTO.getContext());
        messageLog.setBusinessId(taskPO.getId());
        messageLog.setBusinessType(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getCode());
        messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getMessage());
        messageLog.setMessageId(Convert.toLong(callBackId));
        messageLog.setMessageType(MessageLogMessageTypeEnum.MUST_REMIND.getCode());
        messageLog.setTermintalType(MessageLogTerminalTypeEnum.FEISHU.getCode());
        messageLog.setSchoolId(taskPO.getSchoolId());
        messageLog.setCampusId(taskPO.getCampusId());
        messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
        messageLog.setUserId(Long.parseLong(taskPO.getSubmitStaffId()));
        messageLog.setUserType(TaskRoleTypeEnum.TEACHER.getCode());
        messageLog.setTenantId(taskPO.getTenantId());
        messageLogService.save(messageLog);
    }

    /**
     * 发送老师重新添加审核飞书通知
     *
     * @param taskPO
     */
    public void sendResubmitFeiShuMsg(TaskPO taskPO, String infoId, Long reviewTeacherId, String reviewTeacherMobile,StudentVO studentVO) {
        if (Objects.nonNull(taskPO) && StrUtil.isNotBlank(infoId)) {
            String callBackId = SnowFlakeIdUtil.nextIdStr();
            // 发送飞书消息
            FeiMsgReqDTO feiMsgReqDTO = new FeiMsgReqDTO();
            feiMsgReqDTO.setTitle(taskPO.getTaskName() + "待审批");
            feiMsgReqDTO.setMsgUrl(messageUrl + "/home/<USER>" +
                    "&tenantId=" + taskPO.getTenantId() +
                    "&schoolId=" + taskPO.getSchoolId() +
                    "&campusId=" + taskPO.getCampusId() +
                    "&infoId=" + infoId +
                    "&studentId=" + Convert.toStr(studentVO.getStudentId()) +
                    "&staffId=" + reviewTeacherId +
                    "&fromMsg=1");
            feiMsgReqDTO.setContext("老师您好，" + studentVO.getStudentName() + "的家长重新提交了" + taskPO.getTaskName() + "待你审批");
            feiMsgReqDTO.setMobile(reviewTeacherMobile);
            feiMsgReqDTO.setCallBackId(callBackId);
            FeiShuResultDTO feiShuResultDTO = new FeiShuResultDTO();
            try {
                feiShuResultDTO = feiShuMsgUtil.sendHandleStatusMessage(feiMsgReqDTO);
            } catch (Exception e) {
                log.warn("[{}-提交提醒-必填任务]，sendApprovalStatusMessage发生异常", ModuleNameEnum.FEISHU.getMessage(), e);
            }
            MessageLog messageLog = new MessageLog();
            messageLog.setMobile(reviewTeacherMobile);
            messageLog.setContent(feiShuResultDTO.getContext());
            messageLog.setBusinessId(taskPO.getId());
            messageLog.setBusinessType(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getCode());
            messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getMessage());
            messageLog.setMessageId(Convert.toLong(callBackId));
            messageLog.setMessageType(MessageLogMessageTypeEnum.MUST_REMIND.getCode());
            messageLog.setTermintalType(MessageLogTerminalTypeEnum.FEISHU.getCode());
            messageLog.setSchoolId(taskPO.getSchoolId());
            messageLog.setCampusId(taskPO.getCampusId());
            messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
            messageLog.setUserId(Long.parseLong(taskPO.getSubmitStaffId()));
            messageLog.setUserType(TaskRoleTypeEnum.TEACHER.getCode());
            messageLog.setTenantId(taskPO.getTenantId());
            messageLogService.save(messageLog);
        }
    }

    public void judgeAdjustScore(List<TemplateInfoSaveDTO> templateInfoList) {
        //校验单选、多选、分值
        List<TemplateInfoSaveDTO> scoreList = templateInfoList.stream().filter(e -> SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(e.getType()) || SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(e.getType()) || SubmitInfoTypeEnum.SCORE.getText().equals(e.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(scoreList)) {
            this.checkAdjustScore(scoreList);
        }

        //校验明细中的单选 、多选
        List<TemplateInfoSaveDTO> detailList = templateInfoList.stream().filter(e -> SubmitInfoTypeEnum.DETAIL.getText().equals(e.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(detailList)) {
            List<TemplateInfoSaveDTO> detailScoreList = Lists.newArrayList();

            for (TemplateInfoSaveDTO detail : detailList) {
                if (ObjectUtil.isNull(detail) || CollUtil.isEmpty(detail.getList())) continue;

                for (LinkedHashMap linkedHashMap : detail.getList()) {
                    if (!Lists.newArrayList(SubmitInfoTypeEnum.SINGLE_CHECK.getText(), SubmitInfoTypeEnum.MULTI_CHECK.getText()).contains(Convert.toStr(linkedHashMap.get("type")))) {
                        continue;
                    }
                    TemplateInfoSaveDTO templateInfoSaveDTO = new TemplateInfoSaveDTO();
                    templateInfoSaveDTO.setType(Convert.toStr(linkedHashMap.get("type")));
                    templateInfoSaveDTO.setIsScore((Boolean) linkedHashMap.get("isScore"));
                    templateInfoSaveDTO.setIsAdjustScore((Boolean) linkedHashMap.get("isAdjustScore"));
                    templateInfoSaveDTO.setAdjustScore(Convert.toBigDecimal(linkedHashMap.get("adjustScore")));

                    Object innerOption = linkedHashMap.get("options");
                    String jsonStr = JSON.toJSONString(innerOption);
                    TemplateInfoSaveDTO.InnerSubmitOptionInfoSave submitOptionInfoSave = JSONObject.parseObject(jsonStr, TemplateInfoSaveDTO.InnerSubmitOptionInfoSave.class);
                    templateInfoSaveDTO.setOptions(submitOptionInfoSave);
                    detailScoreList.add(templateInfoSaveDTO);
                }
            }
            this.checkAdjustScore(detailScoreList);
        }


    }

    /**
     * 校验手动调整分值
     */
    private void checkAdjustScore(List<TemplateInfoSaveDTO> templateInfoList) {
        if (CollUtil.isEmpty(templateInfoList)) {
            return;
        }

        for (TemplateInfoSaveDTO info : templateInfoList) {
            if (ObjectUtil.isNull(info.getIsAdjustScore()) || !info.getIsAdjustScore()) {
                log.info("[校验手动调整分值]-[开关未设置或者未开启]-[无需校验]");
                continue;
            }

            BigDecimal adjustScore = info.getAdjustScore();
            Assert.isFalse(ObjectUtil.isNull(adjustScore), () -> new BizException("手动调整分值不能为空"));
            Assert.isFalse(adjustScore.compareTo(BigDecimal.ZERO) < 0, () -> new BizException("手动调整分值不能小于0"));

            //加分控件
            if (info.getType().equals(SubmitInfoTypeEnum.SCORE.getText())) {
                BigDecimal score = ObjectUtil.isNull(info.getScore()) ? BigDecimal.ZERO : info.getScore().abs();
                Assert.isFalse(adjustScore.compareTo(score) > 0, () -> new BizException("手动调整分值不能大于指标分值"));
            }

            //单选多选
            if ((info.getType().equals(SubmitInfoTypeEnum.SINGLE_CHECK.getText()) || info.getType().equals(SubmitInfoTypeEnum.MULTI_CHECK.getText())) && info.getIsScore()) {
                List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> options = info.getOptions().getOptions();
                List<BigDecimal> values = options.stream().map(TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave::getValue).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());

                if (CollUtil.isEmpty(values)) {
                    log.info("[校验手动调整分值]-[加分选项分值为空]-[无需校验]");
                    return;
                }

                List<BigDecimal> nonNegativeValues = values.stream().map(BigDecimal::abs).collect(Collectors.toList());
                Assert.isFalse(adjustScore.compareTo(Collections.max(nonNegativeValues)) > 0, () -> new BizException("手动调整分值不能大于指标分值"));
                Assert.isFalse(adjustScore.compareTo(Collections.min(nonNegativeValues)) < 0, () -> new BizException("手动调整分值不能小于指标分值"));
            }
        }
    }

    /**
     * evaluateMsg_16
     * @param task
     * @param infoId
     * @param studentVO
     */
    public void sendTeacherMobileMsg(TaskPO task, String infoId, StudentVO studentVO,
                                     Long reviewTeacherId, String reviewTeacherMobile) {
        log.info("执行【老师审核-发送老师移动端通知】");
        if (Objects.isNull(task) || StrUtil.isBlank(infoId)) {
            log.info("【发送老师审核-教师移动端通知】参数为空，入参task:{}，infoId:{}", JSONUtil.toJsonStr(task), infoId);
            return;
        }
        String callBackId = SnowFlakeIdUtil.nextIdStr();
        String msgTitle = StrUtil.format("【评价】{}（家长提交审核）", task.getTaskName());
        String msgAbstract = StrUtil.format("{}{}{}", studentVO.getClassName(), Constant.TEACHER_MOBILE_BLANK, studentVO.getStudentName());
        String wxMsg = StrUtil.format("{}家长提交{}待你审批", studentVO.getStudentName(), task.getTaskName());
        String tenantId = task.getTenantId();
        String schoolId = task.getSchoolId();
        String cumpusId = task.getCampusId();
        // 学生提交审核跳转链接
        String approvalUrl = "/home/<USER>";
        if (TaskRoleTypeEnum.STUDENT.getCode().equals(task.getRoleType())){
            approvalUrl = "/home/<USER>/evaluate-student-approval?";
        }
        String path = approvalUrl +
                "&tenantId=" + task.getTenantId() +
                "&schoolId=" + task.getSchoolId() +
                "&campusId=" + task.getCampusId() +
                "&infoId=" + infoId +
                "&studentId=" + Convert.toStr(studentVO.getStudentId()) +
                "&staffId=" + reviewTeacherId +
                "&fromMsg=1" +
                "&roleType=" + task.getRoleType();
        String jumpUrl = messageUrl + "/transfer-teacher?redirect=" + URLEncodeUtil.encodeAll(path);

        // 发送【教师移动端】消息
        String sendResult = channelMsgManager.sendMsgToTeacherWithCheck(Convert.toLong(task.getSchoolId())
                , Convert.toLong(task.getCampusId())
                , channelMsgTemplateProps.getPendingOrderRemind()
                , CmMessageTypeEnum.TO_DO_MESSAGE.getCode()
                , BizTypeEnum.APPROVAL.getCode()
                , msgTitle
                , msgAbstract
                , channelMsgHelper.buildWxParamsForPendingOrderRemind(tenantId, schoolId, cumpusId, jumpUrl, "学生评价", wxMsg)
                , reviewTeacherId
                , callBackId
                , jumpUrl
                , "去审批"
        );

        MessageLog messageLog = new MessageLog();
        messageLog.setMobile(reviewTeacherMobile);
        messageLog.setContent(msgTitle + msgAbstract);
        messageLog.setBusinessId(task.getId());
        messageLog.setBusinessType(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getCode());
        messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getMessage());
        messageLog.setMessageId(Convert.toLong(callBackId));
        messageLog.setMessageType(MessageLogMessageTypeEnum.MUST_REMIND.getCode());
        messageLog.setTermintalType(MessageLogTerminalTypeEnum.TEACHER_MOBILE.getCode());
        messageLog.setSchoolId(task.getSchoolId());
        messageLog.setCampusId(task.getCampusId());
        messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
        messageLog.setUserId(Long.parseLong(task.getSubmitStaffId()));
        messageLog.setUserType(TaskRoleTypeEnum.TEACHER.getCode());
        messageLog.setTenantId(task.getTenantId());
        messageLog.setSendResult(sendResult);

        messageLogService.save(messageLog);
    }

    /**
     * evaluateMsg_17
     * @param task
     * @param infoId
     * @param studentVO
     */
    public void sendResubmitTeacherMobileMsg(TaskPO task, String infoId, StudentVO studentVO,
                                             Long reviewTeacherId, String reviewTeacherMobile) {
        if (Objects.isNull(task) || StrUtil.isBlank(infoId)) {
            log.info("[发送老师审核-教师移动端通知]参数为空，入参task:{}，infoId:{}", JSONUtil.toJsonStr(task), infoId);
            return;
        }
        String callBackId = SnowFlakeIdUtil.nextIdStr();
        String msgTitle = StrUtil.format("【评价】{}（家长重新提交审核）", task.getTaskName());
        String msgAbstract = StrUtil.format("{}{}{}", studentVO.getClassName(), Constant.TEACHER_MOBILE_BLANK, studentVO.getStudentName());
        String wxMsg = StrUtil.format("{}家长重新提交{}待你审批", studentVO.getStudentName(), task.getTaskName());
        String tenantId = task.getTenantId();
        String schoolId = task.getSchoolId();
        String cumpusId = task.getCampusId();
        String path = "/home/<USER>" +
                "&tenantId=" + task.getTenantId() +
                "&schoolId=" + task.getSchoolId() +
                "&campusId=" + task.getCampusId() +
                "&infoId=" + infoId +
                "&studentId=" + Convert.toStr(studentVO.getStudentId()) +
                "&staffId=" + reviewTeacherId +
                "&fromMsg=1";
        String jumpUrl = messageUrl + "/transfer-teacher?redirect=" + URLEncodeUtil.encodeAll(path);

        // 发送【教师移动端】消息
        String sendResult = channelMsgManager.sendMsgToTeacherWithCheck(Convert.toLong(task.getSchoolId())
                , Convert.toLong(task.getCampusId())
                , channelMsgTemplateProps.getPendingOrderRemind()
                , CmMessageTypeEnum.TO_DO_MESSAGE.getCode()
                , BizTypeEnum.APPROVAL.getCode()
                , msgTitle
                , msgAbstract
                , channelMsgHelper.buildWxParamsForPendingOrderRemind(tenantId, schoolId, cumpusId, jumpUrl, "学生评价", wxMsg)
                , reviewTeacherId
                , callBackId
                , jumpUrl
                , "去审批"
        );

        MessageLog messageLog = new MessageLog();
        messageLog.setMobile(reviewTeacherMobile);
        messageLog.setContent(msgTitle + msgAbstract);
        messageLog.setBusinessId(task.getId());
        messageLog.setBusinessType(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getCode());
        messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.TEACHER_APPROVAL.getMessage());
        messageLog.setMessageId(Convert.toLong(callBackId));
        messageLog.setMessageType(MessageLogMessageTypeEnum.MUST_REMIND.getCode());
        messageLog.setTermintalType(MessageLogTerminalTypeEnum.TEACHER_MOBILE.getCode());
        messageLog.setSchoolId(task.getSchoolId());
        messageLog.setCampusId(task.getCampusId());
        messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
        messageLog.setUserId(Long.parseLong(task.getSubmitStaffId()));
        messageLog.setUserType(TaskRoleTypeEnum.TEACHER.getCode());
        messageLog.setTenantId(task.getTenantId());
        messageLog.setSendResult(sendResult);

        messageLogService.save(messageLog);
    }


}
