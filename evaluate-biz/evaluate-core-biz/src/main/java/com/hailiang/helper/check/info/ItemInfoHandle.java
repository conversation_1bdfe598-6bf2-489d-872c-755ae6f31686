package com.hailiang.helper.check.info;

import com.hailiang.model.dto.check.CheckFormInfo;

import java.util.Date;

/**
 * 检查项提交表单处理规则
 *
 * <AUTHOR>
 * @date 2023/7/20 16:12
 */
public interface ItemInfoHandle {

    /**
     * 匹配策略 班级:class  寝室:dormitory  学生:student
     *
     * @param type
     * @return
     */
    Boolean equalsStrategy(String type);

    /**
     * 检查对象的处理策略
     *
     * @param checkFormInfo 表单信息
     * @return
     */
    Boolean handle(CheckFormInfo checkFormInfo);

    Boolean handleV2(CheckFormInfo checkFormInfo, Date date);
}
