package com.hailiang.helper;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.saas.SaasHistoryStudentCacheManager;
import com.hailiang.saas.SaasSchoolManager;
import com.hailiang.saas.model.dto.educational.EduOrgQueryDTO;
import com.hailiang.saas.model.vo.educational.EduOrgTreeVO;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class StudentModelHelper {
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    SaasSchoolManager saasSchoolManager;
    @Autowired
    SaasHistoryStudentCacheManager saasHistoryStudentCacheManager;
    /**
     * 获取每个学年班级或年级下的学生人数
     * @param schoolYear
     * @param id
     * @return
     */
    public int getStudentNumById(String schoolYear, Boolean isCurrentSchoolYear, Long schoolId, Long campusId, String id){
        String key = StrUtil.format(RedisKeyConstants.STUDENT_NUM, schoolYear, id);
        // 先从缓存找一下
        Object redisValue = redisUtil.get(key);
        if (ObjectUtil.isNotEmpty(redisValue)){
            return Convert.toInt(redisValue);
        }
        // 查一下当前时间到第二天0点的秒数
        long cacheTime = DateUtil.between(DateUtil.date(), DateUtil.offsetDay(DateUtil.date(), 1), DateUnit.SECOND);
        // 没有缓存在查一下
        initStudentNumCache(schoolYear, isCurrentSchoolYear, schoolId, campusId, cacheTime);
        // 在尝试找一次 如果没有往缓存放0 避免缓存击穿
        Object redisValueNew = redisUtil.get(key);
        if (ObjectUtil.isNotEmpty(redisValueNew)){
            return Convert.toInt(redisValueNew);
        }
        redisUtil.set(key, 0, cacheTime);
        return 0;
    }

    private void initStudentNumCache(String schoolYear, Boolean isCurrentSchoolYear, Long schoolId, Long campusId, long cacheTime) {
        log.info("【初始化学生人数缓存】-【开始】-【当前学校id:{}】,【当前校区id:{}】,【当前学年:{}】,【是否是当前学年:{}】,【缓存时间:{}】", schoolId, campusId, schoolYear, isCurrentSchoolYear, cacheTime);
        if (isCurrentSchoolYear) {
            EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
            eduOrgQueryDTO.setCurrentId(campusId);
            eduOrgQueryDTO.setCurrentIdType(2);
            eduOrgQueryDTO.setIsTree(0);
            eduOrgQueryDTO.setEndType(5);
            List<EduOrgTreeVO> eduOrgTreeVOS = saasSchoolManager.queryEducationalOrgTree(eduOrgQueryDTO);
            for (EduOrgTreeVO eduOrgTreeVO : eduOrgTreeVOS) {
                // 班级或年级
                if (eduOrgTreeVO.getType() == 4 || eduOrgTreeVO.getType() == 5) {
                    String key = StrUtil.format(RedisKeyConstants.STUDENT_NUM, schoolYear, eduOrgTreeVO.getId());
                    redisUtil.set(key, eduOrgTreeVO.getStudentNum(), cacheTime);
                    log.info("【初始化学生人数缓存】-【班级或年级】-【班级或年级id:{}】,【学生人数:{}】", eduOrgTreeVO.getId(), eduOrgTreeVO.getStudentNum());
                }
            }
        } else {
            // 查一下这个校区下的学生数据
            List<SassStudentVO> sassStudentVOS = saasHistoryStudentCacheManager.getHisStudentVOS(schoolId, schoolYear, campusId, null, null);
            // 根据sassStudentVOS中的classId分组 目的是想查出每个班级的学生人数
            Map<Long, List<SassStudentVO>> classStudentMap = CollStreamUtil.groupByKey(sassStudentVOS, SassStudentVO::getClassId);
            classStudentMap.forEach((classId, studentList) -> {
                String key = StrUtil.format(RedisKeyConstants.STUDENT_NUM, schoolYear, classId);
                int studentNum = studentList.size();
                redisUtil.set(key, studentNum, cacheTime);
                log.info("【初始化学生人数缓存】-【班级或年级】-【班级或年级id:{}】,【学生人数:{}】", key, studentNum);
            });

            Map<Long, List<SassStudentVO>> gradeStudentMap = CollStreamUtil.groupByKey(sassStudentVOS, SassStudentVO::getGradeId);
            gradeStudentMap.forEach((gradeId, studentList) -> {
                String key = StrUtil.format(RedisKeyConstants.STUDENT_NUM, schoolYear, gradeId);
                int studentNum = studentList.size();
                redisUtil.set(key, studentNum, cacheTime);
                log.info("【初始化学生人数缓存】-【班级或年级】-【班级或年级id:{}】,【学生人数:{}】", key, studentNum);
            });

        }
    }
}
