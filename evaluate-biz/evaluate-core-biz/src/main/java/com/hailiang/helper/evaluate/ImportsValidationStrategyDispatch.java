package com.hailiang.helper.evaluate;

import com.hailiang.enums.ImportsValidationEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * 分发器，根据不同类型校验
 *
 * @Description: 分发器，根据不同类型校验
 * @Author: Jovi
 * @Date: Created in 2024/12/2
 * @Version: 2.0.0
 */
@Component
public class ImportsValidationStrategyDispatch implements ApplicationContextAware {

    private ApplicationContext applicationContext;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    public <T> ValidationStrategy<T> getSpecificStrategyByTypeClass(String validCode, Class<T> type) {

        Map<String, ValidationStrategy> validationStrategyMap = this
                .applicationContext
                .getBeansOfType(ValidationStrategy.class);

        // 根据 validCode 获取对应的策略值
        String strategyValue = ImportsValidationEnum.getStrategyByValidCode(validCode);

        // 从策略映射中获取对应的 ValidationStrategy 实例
        ValidationStrategy validationStrategy = validationStrategyMap.get(strategyValue);

        // 获取 ValidationStrategy 类的泛型接口
        Type[] genericInterfaces = validationStrategy.getClass().getGenericInterfaces();

        // 如果没有泛型接口，则返回 null
        if (genericInterfaces.length == 0) {
            return null;
        }

        // 获取第一个泛型接口
        Type specificGenericInterface = genericInterfaces[0];

        // 如果第一个泛型接口是 ParameterizedType 类型
        if (specificGenericInterface instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) specificGenericInterface;

            // 获取泛型接口的实际类型参数
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

            // 如果没有实际类型参数，则返回 null
            if (actualTypeArguments.length == 0) {
                return null;
            }

            // 获取第一个实际类型参数
            Type actualTypeArgument = actualTypeArguments[0];

            // 如果实际类型参数与传入的类型相同，则强制转换为 ValidationStrategy<T> 类型并返回
            if (type.equals(actualTypeArgument)) {
                return (ValidationStrategy<T>) validationStrategy;
            }
        }

        return null;
    }
}
