package com.hailiang.helper;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.logic.TaskLogic;
import com.hailiang.model.dto.save.TaskSaveDTO;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TaskPO;
import com.hailiang.model.entity.mongo.Info;
import com.hailiang.model.entity.mongo.SubmitInfo;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.vo.GetEvaluateTaskDetailVO;
import com.hailiang.model.vo.ListEvaluateTaskHistorySubVO;
import com.hailiang.service.TargetService;
import com.hailiang.service.TargetTemplateService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TaskHelper {

    @Resource
    private TaskLogic taskLogic;
    @Resource
    private TargetTemplateService templateService;
    @Resource
    private TargetService targetService;

    public void judgeSaveTask(TaskSaveDTO dto) {
        Assert.notNull(dto.getTenantId(), () -> new BizException("租户id不能为空"));
        Assert.notNull(dto.getTargetId(), () -> new BizException("指标模版id不能为空"));
        Assert.notNull(dto.getTaskName(), () -> new BizException("评价任务名称不能为空"));
        Assert.notNull(dto.getTaskDate(), () -> new BizException("推送时间不能为空"));
        Assert.notNull(dto.getUrgeTime(), () -> new BizException("催办时间不能为空"));
        Assert.notNull(dto.getRemindTime(), () -> new BizException("提醒时间不能为空"));
        Assert.notBlank(dto.getSubmitStaffId(), () -> new BizException("提交人id不能为空"));
        Assert.notNull(dto.getRoleType(), () -> new BizException("角色类型不能为空"));
        Assert.notNull(dto.getTaskType(), () -> new BizException("任务类型不能为空"));
        Assert.notNull(dto.getTaskStatus(), () -> new BizException("任务状态不能为空"));
    }

    public void judgeFinishedSaveTask(TaskSaveDTO dto) {
        Assert.notNull(dto.getTenantId(), () -> new BizException("租户id不能为空"));
        Assert.notNull(dto.getCampusId(), () -> new BizException("校区id不能为空"));
        Assert.notNull(dto.getTargetId(), () -> new BizException("指标模版id不能为空"));
        Assert.notNull(dto.getTaskName(), () -> new BizException("评价任务名称不能为空"));
        Assert.notNull(dto.getTaskDate(), () -> new BizException("推送时间不能为空"));
        Assert.notNull(dto.getSubmitTime(), () -> new BizException("提交时间不能为空"));
        Assert.notBlank(dto.getSubmitStaffId(), () -> new BizException("提交人id不能为空"));
        Assert.notNull(dto.getRoleType(), () -> new BizException("角色类型不能为空"));
        Assert.notNull(dto.getTaskType(), () -> new BizException("任务类型不能为空"));
        Assert.notNull(dto.getTaskStatus(), () -> new BizException("任务状态不能为空"));
    }

    // 获取任务对应的模版
    public GetEvaluateTaskDetailVO getTaskDetail(Long taskId) {
        Assert.isTrue(taskId != null, () -> new BizException("任务id不能为空"));
        GetEvaluateTaskDetailVO vo = new GetEvaluateTaskDetailVO();
        TaskPO taskPO = taskLogic.getById(taskId);
        Assert.isTrue(taskPO != null, () -> new BizException("评价内容已被删除"));
        // Assert.isTrue(!TaskStatusEnum.SUBMITTED.getCode().equals(task.getTaskStatus()), () -> new BizException("评估任务已提交"));
        TargetTemplate template = templateService.getByTargetId(taskPO.getTargetId());
        Assert.isTrue(template != null, () -> new BizException("表单模版不存在"));
        vo.setTaskId(String.valueOf(taskPO.getId()))
                .setTargetName(taskPO.getTaskName())
                .setRemindTime(Objects.nonNull(taskPO.getRemindTime()) ? DateUtil.endOfDay(taskPO.getRemindTime()) : null)
                .setTemplateInfo(template.getTemplateInfoList());
        return vo;
    }

    // 获取非必填指标的模版
    public GetEvaluateTaskDetailVO getTaskTemplate(Long targetId) {
        Assert.isTrue(targetId != null, () -> new BizException("指标id不能为空"));
        GetEvaluateTaskDetailVO vo = new GetEvaluateTaskDetailVO();
        // 没任务进新的提交页面
        Target target = targetService.getById(targetId);
        Assert.isTrue(target != null, () -> new BizException("该指标已被删除"));
        TargetTemplate template = templateService.getByTargetId(targetId);
        Assert.isTrue(template != null, () -> new BizException("表单模版不存在"));
        vo.setTargetName(target.getTargetName()).setTemplateInfo(template.getTemplateInfoList());
        return vo;
    }

    public void fullFillSubVO(List<SubmitInfo> submitInfoList,
                              List<ListEvaluateTaskHistorySubVO> submitInfoSubList,
                              Info evaluateInfo,
                              Boolean flag) {
        int i = 0;
        for (SubmitInfo info : submitInfoList) {
            Object submitValue = info.getSubmitValue();
            // 详情只展示名称
            boolean isDetail = SubmitInfoTypeEnum.DETAIL.getText().equals(info.getType());
            boolean isScore = SubmitInfoTypeEnum.SCORE.getText().equals(info.getType());
            // 没填答案且不是详情的跳过
            if (submitValue == null && (!isDetail && !isScore)) {
                continue;
            }
            // 其他
            boolean isdirect = SubmitInfoTypeEnum.SINGLE_TEXT.getText().equals(info.getType()) ||
                    SubmitInfoTypeEnum.MULTI_TEXT.getText().equals(info.getType()) ||
                    SubmitInfoTypeEnum.DATE.getText().equals(info.getType()) ||
                    SubmitInfoTypeEnum.DESCRIPTION.getText().equals(info.getType());
            boolean isCheck = SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(info.getType()) ||
                    SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(info.getType());

            // 图片和视频只展示个数
            boolean isDedia = SubmitInfoTypeEnum.MEDIA.getText().equals(info.getType());
            // 学生展示被评价的名字
            boolean isStudent = SubmitInfoTypeEnum.STUDENT.getText().equals(info.getType());
            // 数字展示数字+单位
            boolean isNumber = SubmitInfoTypeEnum.NUMBER.getText().equals(info.getType());
            ListEvaluateTaskHistorySubVO subVO = new ListEvaluateTaskHistorySubVO()
                    .setType(info.getType())
                    .setName(info.getName());
            submitInfoSubList.add(subVO);
            if (isScore) {
                subVO.setIsAdjustScore(info.getIsAdjustScore());
                subVO.setAdjustScore(info.getAdjustScore());
                subVO.setAdjustValue(info.getAdjustValue());
                subVO.setTypeName(info.getTypeName()).setScoreType(info.getScoreType()).setScore(info.getScore());
                subVO.setScoreList(Collections.singletonList(info.getScore()));
            } else if (isdirect) {
                subVO.setSubmitValue(submitValue).setTypeName(info.getTypeName());
            } else if (isCheck) {
                subVO.setSubmitValue(submitValue).setTypeName(info.getTypeName()).setIsScore(info.getIsScore());
                subVO.setScoreList(this.getScoreList(info));
            } else if (isDetail) {
                subVO.setSubmitValue(evaluateInfo.getStudentInfos().stream()
                        .filter(s -> Objects.nonNull(s.getDetailId()) && s.getDetailId().equals(info.getKey()))
                        .collect(Collectors.toList())).setTypeName(info.getTypeName());
                subVO.setDetailScoreList(this.getDetailScoreList(info));
            } else if (isDedia) {
                subVO.setSubmitValue(((List) submitValue).size()).setTypeName(info.getTypeName());
            } else if (isStudent) {
                subVO.setSubmitValue(evaluateInfo.getStudentInfos()).setTypeName(info.getTypeName());
            } else if (isNumber) {
                subVO.setSubmitValue(submitValue).setUnit(info.getUnit()).setTypeName(info.getTypeName());
            }
            // 详情只展示前两项
            if (flag && ++i == 2) {
                break;
            }
        }
    }

    /**
     * 解析明细中的分数
     * @param info
     * @return
     */
    private List<BigDecimal> getDetailScoreList(SubmitInfo info){
        List<BigDecimal> scoreList = new ArrayList<>();
        List<List<LinkedHashMap>> list = info.getSubmitList();
        for (List<LinkedHashMap> subList : list) {
            for (LinkedHashMap subMap : subList) {
                String type = String.valueOf(subMap.get("type"));
                if (SubmitInfoTypeEnum.SCORE.getText().equals(type) || SubmitInfoTypeEnum.SINGLE_CHECK.getText()
                        .equals(type) || SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(type)) {
                    Object value = subMap.get("submitValue");
                    if(value instanceof LinkedHashMap){
                        LinkedHashMap linkedHashMap = (LinkedHashMap) value;
                        BigDecimal scoreValue = Convert.toBigDecimal(linkedHashMap.get("value"));
//                        BigDecimal scoreValue = new BigDecimal((Integer) linkedHashMap.get("value"));
                        scoreList.add(scoreValue);
                    }
                    if(value instanceof List){
                        List<LinkedHashMap> submitValue = (List<LinkedHashMap>) value;
                        for (LinkedHashMap score : submitValue) {
                            Object val = score.get("value");
                            scoreList.add(Convert.toBigDecimal(val));
                        }
                    }
                }
            }
        }
        return scoreList;
    }

    /**
     * 获取单选多选分数
     * @param submitInfo
     * @return
     */
    private List<BigDecimal> getScoreList(SubmitInfo submitInfo){
        List<BigDecimal> scoreList = new ArrayList<>();
        Object value = submitInfo.getSubmitValue();
        if(value instanceof LinkedHashMap){
            LinkedHashMap linkedHashMap = (LinkedHashMap) value;
            BigDecimal scoreValue =  Convert.toBigDecimal(linkedHashMap.get("value"));
            scoreList.add(scoreValue);
        }
        if(value instanceof List){
            List<LinkedHashMap> submitValue = (List<LinkedHashMap>) value;
            for (LinkedHashMap score : submitValue) {
                Object val = score.get("value");
                scoreList.add(Convert.toBigDecimal(val));
            }
        }
        return scoreList;
    }
}
