package com.hailiang.helper.check.info;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.model.dto.check.CheckEntryDetailDTO;
import com.hailiang.model.dto.check.CheckFormInfo;
import com.hailiang.model.dto.check.CheckItemEntryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 检查项为学生的处理规则
 *
 * <AUTHOR>
 * @date 2023/7/20 16:14
 */
@Slf4j
@Component
public class CheckStudentHandle implements ItemInfoHandle {

    @Resource
    private ItemDataHandle itemDataHandle;

    @Override
    public Boolean equalsStrategy(String type) {
        boolean flag = ObjectUtil.equals(SubmitInfoTypeEnum.STUDENT.getText(), type);
        if (flag) {
            log.info("[表单提交]-[策略命中]-[检查项为学生的处理逻辑]");
        }
        return flag;
    }

    @Override
    public Boolean handle(CheckFormInfo checkFormInfo) {

        if (Objects.isNull(checkFormInfo.getCheckItemEntryDTO())) {
            log.error("[检查项为学生的表单提交]-[填写对象信息信息为空]-相关信息:[{}]", checkFormInfo);
            return Boolean.FALSE;
        }
        // 表单填写对象信息
        CheckItemEntryDTO checkItemEntryDTO = checkFormInfo.getCheckItemEntryDTO();
        if (CollUtil.isEmpty(checkItemEntryDTO.getStudentInfos())) {
            log.error("[检查项为学生的表单提交]-[学生信息为空]-相关信息:[{}]", checkFormInfo);
            return Boolean.FALSE;
        }

        // 需要扣分的班级
        List<CheckEntryDetailDTO> studentInfos = checkItemEntryDTO.getStudentInfos();
        List<String> classInfoIds = studentInfos.stream().map(CheckEntryDetailDTO::getClassId).distinct().collect(Collectors.toList());
        // 根据班级分组,维护班级与学生的关联信息
        Map<String, List<CheckEntryDetailDTO>> classRelationGroup = checkItemEntryDTO.getStudentInfos().stream().collect(Collectors.groupingBy(CheckEntryDetailDTO::getClassId));

        // 存储数据
        itemDataHandle.saveDataHandle(
                classInfoIds,
                classRelationGroup,
                checkFormInfo.getCheckItemBasicInfo(),
                checkFormInfo.getCheckUserRoleDTO(),
                checkFormInfo.getCheckClassInfoMongodb(),
                checkFormInfo.getCheckOptionDetails(),
                checkFormInfo.getCheckScoreControl());
        return Boolean.TRUE;
    }

    @Override
    public Boolean handleV2(CheckFormInfo checkFormInfo, Date date) {
        if (Objects.isNull(checkFormInfo.getCheckItemEntryDTO())) {
            log.error("[检查项为学生的表单提交]-[填写对象信息信息为空]-相关信息:[{}]", checkFormInfo);
            return Boolean.FALSE;
        }
        // 表单填写对象信息
        CheckItemEntryDTO checkItemEntryDTO = checkFormInfo.getCheckItemEntryDTO();
        if (CollUtil.isEmpty(checkItemEntryDTO.getStudentInfos())) {
            log.error("[检查项为学生的表单提交]-[学生信息为空]-相关信息:[{}]", checkFormInfo);
            return Boolean.FALSE;
        }

        // 需要扣分的班级
        List<CheckEntryDetailDTO> studentInfos = checkItemEntryDTO.getStudentInfos();
        List<String> classInfoIds = studentInfos.stream().map(CheckEntryDetailDTO::getClassId).distinct().collect(Collectors.toList());
        // 根据班级分组,维护班级与学生的关联信息
        Map<String, List<CheckEntryDetailDTO>> classRelationGroup = checkItemEntryDTO.getStudentInfos().stream().collect(Collectors.groupingBy(CheckEntryDetailDTO::getClassId));

        // 存储数据
        itemDataHandle.saveDataHandleV2(
                classInfoIds,
                classRelationGroup,
                checkFormInfo.getCheckItemBasicInfo(),
                checkFormInfo.getCheckUserRoleDTO(),
                checkFormInfo.getCheckClassInfoMongodb(),
                checkFormInfo.getCheckOptionDetails(),
                checkFormInfo.getCheckScoreControl(),
                date);
        return Boolean.TRUE;
    }
}
