package com.hailiang.annotation;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hailiang.util.WebUtil;
import com.yomahub.tlog.core.convert.AspectLogConvert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/10/14 14:35
 * 打印当前用户信息(只适用于教职工角色，登录用户为学生、家长需要单独在写一份)
 */
@Slf4j
public class CurrentStaffConvert implements AspectLogConvert {

    @Override
    public String convert(Object[] args) {
        String currentSchoolId = null;
        String currentCampusId = null;
        String currentStaffId = null;
        String currentStaffName = null;
        String source = null;
        String sourcePlatform = null;
        try {
            currentSchoolId = WebUtil.getSchoolId();
            currentCampusId = WebUtil.getCampusId();
            currentStaffId = WebUtil.getStaffId();
            currentStaffName = DesensitizedUtil.chineseName(WebUtil.getStaffName());
            source = ObjectUtil.isEmpty(WebUtil.getSource()) ? StrUtil.EMPTY : Convert.toStr(WebUtil.getSource());
            sourcePlatform = ObjectUtil.isEmpty(WebUtil.getSourcePlatform()) ? StrUtil.EMPTY : Convert.toStr(WebUtil.getSourcePlatform());
            return StrUtil.format("{}-{}-{}-{}-{}-{}", source, sourcePlatform, currentSchoolId, currentCampusId, currentStaffId, currentStaffName);
        } catch (Exception e) {
            log.warn("获取CurrentUser发生异常", e);
            return "CurrentUserInfo:[异常]";
        }
    }
}
