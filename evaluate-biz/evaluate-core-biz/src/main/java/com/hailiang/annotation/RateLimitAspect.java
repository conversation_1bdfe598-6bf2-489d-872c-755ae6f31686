package com.hailiang.annotation;

import com.hailiang.exception.BizException;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


/**
 * 频率限制
 *
 * @Description: 频率限制
 * @Author: JJl
 * @Date: Created in 2024-08-08
 * @Version: 1.9.1
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class RateLimitAspect {
    private final RedissonClient redissonClient;

    @Around("@annotation(rateLimit)")
    public Object rateLimitAspect(ProceedingJoinPoint joinPoint, RateLimit rateLimit) throws Throwable {
        String key = buildKey(rateLimit.key());
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
        rateLimiter.trySetRate(rateLimit.mode(), rateLimit.rate(), rateLimit.rateInterval(), rateLimit.rateIntervalUnit());
        redissonClient.getBucket(key).expire(60, TimeUnit.SECONDS);

        if (!rateLimiter.tryAcquire()) {
            throw new BizException("请求频率过高，请稍后再试");
        }


        // 执行原始方法
        Object result = joinPoint.proceed();

        Signature signature = joinPoint.getSignature();
        //类名
        String declaringTypeName = signature.getDeclaringTypeName();
        //方法名
        String methodName = signature.getName(); // 方法
        //入参
        Object[] methodArgs = joinPoint.getArgs();
        // 打印日志或进行其他处理
        log.info(">>>>>>方法频率限制, 方法名称:【{}#{}】, 入参:【{}】", declaringTypeName, methodName, arrayToString(methodArgs));

        return result;
    }


    private String arrayToString(Object[] array) {
        StringBuilder result = new StringBuilder("[");
        for (Object item : array) {
            result.append(item).append(", ");
        }
        if (array.length > 0) {
            result.setLength(result.length() - 2); // Remove the trailing comma and space
        }
        result.append("]");
        return result.toString();
    }

    private String buildKey(String baseKey) {
        return baseKey.concat(WebUtil.getStaffId());
    }

}
