package com.hailiang.annotation;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class MethodExecuteLogAspect {
    @Around("@annotation(methodExecuteLog)")
    public Object logMethodExecutionTime(ProceedingJoinPoint joinPoint, MethodExecuteLog methodExecuteLog) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 获取方法入参
        Object[] methodArgs = joinPoint.getArgs();

        // 执行原始方法
        Object result = joinPoint.proceed();

        long endTime = System.currentTimeMillis();

        // 计算执行时长
        long duration = endTime - startTime;

        Signature signature = joinPoint.getSignature();
        String methodName = signature.getName(); // 方法名
        String declaringTypeName = signature.getDeclaringTypeName(); // 包含包名的类名

        // 打印日志或进行其他处理
        log.info(">>>>>>方法执行情况统计, 方法名称:【{}#{}】, 耗时:【{}ms】, 入参:【{}】, 出参:【{}】 ",
                declaringTypeName, methodName, duration, JSONUtil.toJsonStr(methodArgs) , JSONUtil.toJsonStr(result));

        return result;
    }

    private String arrayToString(Object[] array) {
        StringBuilder result = new StringBuilder("[");
        for (Object item : array) {
            result.append(item).append(", ");
        }
        if (array.length > 0) {
            result.setLength(result.length() - 2); // Remove the trailing comma and space
        }
        result.append("]");
        return result.toString();
    }
}
