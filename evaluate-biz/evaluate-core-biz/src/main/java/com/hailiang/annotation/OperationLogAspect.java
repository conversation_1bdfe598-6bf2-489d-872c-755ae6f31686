package com.hailiang.annotation;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.enums.MessageLogUserTypeEnum;
import com.hailiang.model.dto.save.OperateLogSaveDTO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.vo.staff.StaffBatchQueryDTO;
import com.hailiang.remote.saas.vo.staff.StaffBatchVO;
import com.hailiang.service.OperateLogService;
import com.hailiang.util.IpInfoUtil;
import com.hailiang.util.RequestUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Aspect
@Component
@Slf4j
public class OperationLogAspect {
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private IpInfoUtil ipInfoUtil;
    @Resource
    private ThreadPoolTaskExecutor evaluateExecutor;
    @Resource
    private OperateLogService operateLogService;
    /**
     * 此处的切点是注解的方式，也可以用包名的方式达到相同的效果
     * '@Pointcut("execution(* com.wwj.springboot.service.impl.*.*(..))")'
     */
    @Pointcut("@annotation(com.hailiang.annotation.OperationLog)")
    public void operationLog(){}


    /**
     * 环绕增强，相当于MethodInterceptor
     */
    @Around("@annotation(operationLog)")
    public Object doAround(ProceedingJoinPoint joinPoint, OperationLog operationLog) throws Throwable {
        Object response = null;
        long time = System.currentTimeMillis();
        try {
            response =  joinPoint.proceed();
            time = System.currentTimeMillis() - time;
            return response;
        } finally {
            try {
                //方法执行完成后增加日志
                addOperationLog(joinPoint, response, time, operationLog.operationType().getKey());
            }catch (Exception e){
                log.warn("操作日志 operationLog 操作失败：异常信息：", e);
            }
        }
    }

    private void addOperationLog(JoinPoint joinPoint, Object response, long time, int operateType){
//            MethodSignature signature = (MethodSignature)joinPoint.getSignature();
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest(); // NOSONAR

        OperateLogSaveDTO operateLog = new OperateLogSaveDTO();
        // 学校id
        operateLog.setSchoolId(WebUtil.getSchoolId());
        // 校区id
        operateLog.setCampusId(WebUtil.getCampusId());
        // 租户id
        operateLog.setTenantId(WebUtil.getTenantId());
        // 用户Id
        operateLog.setUserId(WebUtil.getStaffId());
        // 用户类型
        operateLog.setUserType(MessageLogUserTypeEnum.STAFF.getCode());
        // 手机号
        operateLog.setMobile(getMobile());
        // 设置IP
        operateLog.setIp(ipInfoUtil.getIpAddr(request));
        // 请求URL
        operateLog.setRequestUrl(request.getRequestURI());
        // 请求参数
        operateLog.setRequestParam(JSONUtil.toJsonStr(RequestUtil.getParam()));
        JSONObject responseJson = JSONUtil.parseObj(response);
        // 返回的状态码
        operateLog.setResponseStatus(responseJson.getInt("status"));
        // 返回数据
        operateLog.setResponseParam(responseJson.toString());
        operateLog.setCreateBy(WebUtil.getStaffId());
        operateLog.setCreateTime(new Date());
        operateLog.setSource(1);
        operateLog.setOperateType(operateType);
        // 调用线程保存到数据库
        evaluateExecutor.execute(() -> operateLogService.saveOperateLog(operateLog));
    }

    private String getMobile() {
        String mobile = null;
        // 部分接口没有staffId 比如积分柜升级等
        if (CharSequenceUtil.isNotBlank(WebUtil.getStaffId())){
            String staffId = redisUtil.getStr(RedisKeyConstants.OPERATE_LOG_STAFFID + WebUtil.getStaffId());
            if (CharSequenceUtil.isNotEmpty(staffId)) {
                mobile = staffId;
            } else {
                StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
                List<Long> staffIds = new ArrayList<>();
                staffIds.add(WebUtil.getStaffIdLong());
                staffBatchQueryDTO.setStaffIdList(staffIds);
                List<StaffBatchVO> staffBatchVOS = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
                if (!staffBatchVOS.isEmpty()) {
                    mobile = staffBatchVOS.get(0).getMobile();
                }
            }
        }
        return mobile;
    }

    @Before("operationLog()")
    public void doBeforeAdvice(JoinPoint joinPoint){
//        log.info("进入方法前执行.....");

    }

    /**
     * 处理完请求，返回内容
     * @param ret
     */
    @AfterReturning(returning = "ret", pointcut = "operationLog()")
    public void doAfterReturning(Object ret) {
//        log.info("方法的返回值 : " + ret);
    }

    /**
     * 后置异常通知
     */
    @AfterThrowing("operationLog()")
    public void throwss(JoinPoint jp){
//        log.info("方法异常时执行.....");
    }


    /**
     * 后置最终通知,final增强，不管是抛出异常或者正常退出都会执行
     */
    @After("operationLog()")
    public void after(JoinPoint jp){
//        log.info("方法最后执行.....");
    }
}
