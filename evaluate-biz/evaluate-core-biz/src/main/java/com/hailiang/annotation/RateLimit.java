package com.hailiang.annotation;

import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 频率限制注解
 *
 * @Description: 频率限制注解
 * @Author: JJl
 * @Date: Created in 2024-08-08
 * @Version: 1.9.1
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    String key() default "";
    RateType mode() default RateType.PER_CLIENT;
    long rate() default 10;
    long rateInterval() default 10;
    RateIntervalUnit rateIntervalUnit() default RateIntervalUnit.SECONDS;
}