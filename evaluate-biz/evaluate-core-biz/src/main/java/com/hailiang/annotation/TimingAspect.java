package com.hailiang.annotation;

import com.hailiang.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * 时间统计切面
 *
 * <AUTHOR>
 * @date 2023年08月26日 14:14
 */
@Aspect
@Component
@Slf4j
public class TimingAspect {

    /**
     * 切面上下文
     */
    private static final ThreadLocal<Entity> CONTEXT = new ThreadLocal<>();

    /**
     * 获取计时器
     *
     * @return StopWatch
     */
    private static StopWatch getStopWatch() {
        return Optional.ofNullable(CONTEXT.get())
                .map(Entity::getStopWatch)
                .orElse(null);
    }

    /**
     * 停止任务
     */
    public static StopWatch stop() {
        StopWatch stopWatch = getStopWatch();
        Optional.ofNullable(stopWatch)
                .filter(StopWatch::isRunning)
                .ifPresent(StopWatch::stop);
        return stopWatch;
    }

    /**
     * 开始任务
     *
     * @param taskName 任务名称
     */
    public static void start(String taskName) {
        Optional.ofNullable(stop())
                .ifPresent(stopWatch -> stopWatch.start(taskName));
    }

    /**
     * 环绕方法切面
     *
     * @param joinPoint 切面方法
     * @param timing    时间统计注解
     * @return Object
     */
    @Around("@annotation(timing)")
    public Object aroundMethod(ProceedingJoinPoint joinPoint, Timing timing) throws Throwable {
        String taskName = getTaskName(joinPoint, timing);
        initContext(taskName);
        Object result;
        try {
            start(taskName);
            result = joinPoint.proceed();
            stop();
            prettyPrintAndRemoveContext(taskName);
        } catch (Throwable t) {
            CONTEXT.remove();
            throw t;
        }
        return result;
    }

    /**
     * 获取任务名称
     *
     * @param joinPoint 切入方法
     * @param timing    时间统计注解
     * @return String
     */
    private String getTaskName(ProceedingJoinPoint joinPoint, Timing timing) {
        String methodName = joinPoint.getSignature().toShortString();
        return StringUtils.isEmpty(timing.value()) ?
                methodName :
                timing.value();
    }

    /**
     * 初始化上线文
     * 只在线程入口处初始一次
     *
     * @param taskName 任务名称
     */
    private void initContext(String taskName) {
        if (Objects.isNull(CONTEXT.get())) {
            //计时器名字和第一个任务名称相同
            CONTEXT.set(new Entity(taskName, new StopWatch(taskName)));
        }
    }

    /**
     * 优雅打印时间统计信息，并清除上下文
     * 任务名称和计时器名称相同，表示整个链路结束，才打印时间统计日志，否则继续统计下一个任务
     *
     * @param taskName 任务名称
     */
    private void prettyPrintAndRemoveContext(String taskName) {
        if (Objects.equals(taskName, getWatchName())) {
            log.info(prettyPrint());
            CONTEXT.remove();
        }
    }

    /**
     * 打印时间统计信息
     *
     * @return String
     */
    private String prettyPrint() {
        return Optional.ofNullable(getStopWatch())
                .map(StopWatch::prettyPrint)
                .orElseThrow(() -> new BizException("统计方法时长-StopWatch不能为空"));
    }

    /**
     * 获取计时器名称
     *
     * @return String
     */
    private String getWatchName() {
        return CONTEXT.get().getWatchName();
    }

    /**
     * 上下文实体
     */
    @Data
    @AllArgsConstructor
    private static class Entity {
        /**
         * 计时器名字
         */
        private String watchName;
        /**
         * @see StopWatch
         */
        private StopWatch stopWatch;
    }
}
