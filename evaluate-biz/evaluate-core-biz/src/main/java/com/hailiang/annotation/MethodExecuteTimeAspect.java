package com.hailiang.annotation;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class MethodExecuteTimeAspect {
    @Around("@annotation(methodExecuteTime)")
    public Object logMethodExecutionTime(ProceedingJoinPoint joinPoint, MethodExecuteTime methodExecuteTime) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 执行原始方法
        Object result = joinPoint.proceed();

        long endTime = System.currentTimeMillis();

        // 计算执行时长
        long duration = endTime - startTime;

        Signature signature = joinPoint.getSignature();
        String methodName = signature.getName(); // 方法名
        String declaringTypeName = signature.getDeclaringTypeName(); // 包含包名的类名

        // 打印日志或进行其他处理
        log.info(">>>>>>方法执行情况统计, 方法名称:【{}#{}】, 耗时:【{}ms】", declaringTypeName, methodName, duration);

        return result;
    }
}
