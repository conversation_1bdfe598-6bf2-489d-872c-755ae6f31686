package com.hailiang.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.hailiang.entity.EvaluateBehaviourImportsRecordDetailPO;
import com.hailiang.entity.EvaluateBehaviourImportsRecordPO;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.manager.EvaluateBehaviourImportsRecordDetailManager;
import com.hailiang.manager.EvaluateBehaviourImportsRecordManager;
import com.hailiang.model.dto.ImportsBehaviourRecordMsgRowDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.service.ImportsEvaluateRecordDetailService;
import com.hailiang.util.SnowFlakeIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.hailiang.enums.DataSourceEnum.BATCH_IMPORTS;

/**
 * 批量导入详情处理服务实现
 *
 * @Description: 批量导入详情处理服务实现
 * @Author: Jovi
 * @Date: Created in 2024-12-09
 * @Version: 2.0.0
 */
@Service
@Slf4j
public class ImportsEvaluateRecordDetailServiceImpl implements ImportsEvaluateRecordDetailService {


    @Resource
    private EvaluateBehaviourImportsRecordManager importsRecordManager;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private BehaviourRecordManager behaviourRecordManager;

    @Resource
    private EvaluateBehaviourImportsRecordDetailManager importsRecordDetailManager;

    @Override
    public Boolean batchDealBehaviourRecordDTOList(Long batchSubmitId,
                                                   EvaluateBehaviourImportsRecordPO evaluateBehaviourImportsRecordPO,
                                                   List<ImportsBehaviourRecordMsgRowDTO> importsBehaviourRecordMsgRowDTOList,
                                                   List<BehaviourRecord> behaviourRecordPOList) {


        List<EvaluateBehaviourImportsRecordDetailPO> importsRecordDetailPOS = new ArrayList<>();

        TimeInterval timeInterval = DateUtil.timer();


        int i = 0;
        for (ImportsBehaviourRecordMsgRowDTO importsBehaviourRecordMsgRowDTO : importsBehaviourRecordMsgRowDTOList) {

            log.info("【点评下游消息处理】-【导入批次：{}】，第{}条数据开始处理...", batchSubmitId, ++i);

            buildBehaviourRecordPOs(importsBehaviourRecordMsgRowDTO, behaviourRecordPOList);

            buildImportsBehaviourRecordPOs(batchSubmitId, importsBehaviourRecordMsgRowDTO, importsRecordDetailPOS);
        }

        log.info("【点评下游消息处理】-【导入批次：{}】，详情处理耗时：{}，条数：{}", batchSubmitId,
                timeInterval.intervalPretty(),
                importsBehaviourRecordMsgRowDTOList.size());
        timeInterval.restart();

        evaluateBehaviourImportsRecordPO.setDealFlag(1);

        boolean save = save(evaluateBehaviourImportsRecordPO, behaviourRecordPOList, importsRecordDetailPOS);

        log.info("【点评下游消息处理】-【导入批次：{}】，保存数据库耗时：{}，行为记录条数：{}", batchSubmitId, save,behaviourRecordPOList.size());

        return save;
    }

    private boolean save(EvaluateBehaviourImportsRecordPO evaluateBehaviourImportsRecordPO,
                         List<BehaviourRecord> behaviourRecordPOList,
                         List<EvaluateBehaviourImportsRecordDetailPO> importsRecordDetailPOS) {


        return transactionTemplate.execute(status -> {

                    boolean importsRecordFlag = false;

                    try {

                        boolean batchBehaviourRecordFlag = behaviourRecordManager.saveBatchByCustomerSql(behaviourRecordPOList, 1000);

                        boolean batchImportsRecordDetailFlag = importsRecordDetailManager.saveBatchByCustomerSql(importsRecordDetailPOS, 1000);

                        if (batchBehaviourRecordFlag && batchImportsRecordDetailFlag) {
                            importsRecordFlag = importsRecordManager.updateById(evaluateBehaviourImportsRecordPO);
                        }

                    } catch (Exception e) {
                        status.setRollbackOnly();
                        log.error("批量导入消息接收处理失败：「{}」", e.getMessage(), e);
                        return false;
                    }

                    return importsRecordFlag;
                }
        );
    }

    /**
     * 组装导入详情记录
     *
     * @param importsBehaviourRecordMsgRowDTO
     * @param importsRecordDetailPOS
     */
    private void buildImportsBehaviourRecordPOs(Long batchSubmitId,
                                                ImportsBehaviourRecordMsgRowDTO importsBehaviourRecordMsgRowDTO,
                                                List<EvaluateBehaviourImportsRecordDetailPO> importsRecordDetailPOS) {

        importsBehaviourRecordMsgRowDTO.getDetailList().forEach(detail -> {

            EvaluateBehaviourImportsRecordDetailPO importsRecordDetailPO = new EvaluateBehaviourImportsRecordDetailPO();
            importsRecordDetailPO.setTenantId(Convert.toLong(importsBehaviourRecordMsgRowDTO.getTenantId()));
            importsRecordDetailPO.setSchoolId(Convert.toLong(importsBehaviourRecordMsgRowDTO.getSchoolId()));
            importsRecordDetailPO.setCampusId(Convert.toLong(importsBehaviourRecordMsgRowDTO.getSchoolId()));
            importsRecordDetailPO.setCampusSectionId(Convert.toLong(importsBehaviourRecordMsgRowDTO.getCampusSectionId()));
            importsRecordDetailPO.setCampusSectionCode(importsBehaviourRecordMsgRowDTO.getCampusSectionCode());
            importsRecordDetailPO.setGradeId(Convert.toLong(importsBehaviourRecordMsgRowDTO.getGradeId()));
            importsRecordDetailPO.setGradeCode(importsBehaviourRecordMsgRowDTO.getGradeCode());
            importsRecordDetailPO.setClassId(Convert.toLong(importsBehaviourRecordMsgRowDTO.getClassId()));
            importsRecordDetailPO.setStudentId(Convert.toLong(importsBehaviourRecordMsgRowDTO.getStudentId()));
            importsRecordDetailPO.setStudentName(importsBehaviourRecordMsgRowDTO.getStudentName());
            importsRecordDetailPO.setTargetId(importsBehaviourRecordMsgRowDTO.getTargetId());
            importsRecordDetailPO.setControlId(detail.getControlId());
            importsRecordDetailPO.setControlType(detail.getControlType());
            importsRecordDetailPO.setControlName(detail.getControlName());
            importsRecordDetailPO.setControlValue(detail.getControlValue());
            importsRecordDetailPO.setControlIsScore(detail.getIsScore() == false ? 0 : 1);
            importsRecordDetailPO.setOptionId(detail.getOptionId());
            importsRecordDetailPO.setOptionValue(detail.getOptionValue());
            importsRecordDetailPO.setOptionScore(detail.getScore());
            //相同的导入记录行ID
            importsRecordDetailPO.setImportRecordId(Convert.toLong(importsBehaviourRecordMsgRowDTO.getInfoId()));
            importsRecordDetailPO.setSubmitId(batchSubmitId);
            importsRecordDetailPO.setAppraisalId(Convert.toLong(importsBehaviourRecordMsgRowDTO.getAppraisalId()));
            importsRecordDetailPO.setCreateBy(importsBehaviourRecordMsgRowDTO.getAppraisalId());
            importsRecordDetailPO.setAppraisalName(importsBehaviourRecordMsgRowDTO.getAppraisalName());
            importsRecordDetailPO.setAppraisalType(importsBehaviourRecordMsgRowDTO.getAppraisalType());

            log.info("导入详情数据:{}", importsRecordDetailPO);

            importsRecordDetailPOS.add(importsRecordDetailPO);

        });
    }

    /**
     * 组装行为记录表
     *
     * @param importsBehaviourRecordMsgRowDTO
     * @param behaviourRecordPOList
     */
    private void buildBehaviourRecordPOs(ImportsBehaviourRecordMsgRowDTO importsBehaviourRecordMsgRowDTO, List<BehaviourRecord> behaviourRecordPOList) {

        if (importsBehaviourRecordMsgRowDTO.getIsSplit()) {

            importsBehaviourRecordMsgRowDTO.getDetailList().forEach(detail -> {

                if (SubmitInfoTypeEnum.isCheckSubmitInfo(detail.getControlType())) {
                    BehaviourRecord behaviourRecordPO = BuildBehaviourRecordPOBaseCommonInfo(importsBehaviourRecordMsgRowDTO);
                    behaviourRecordPO.setOptionId(detail.getOptionId());
                    behaviourRecordPO.setInfoId(importsBehaviourRecordMsgRowDTO.getInfoId());
                    behaviourRecordPO.setInfoType(importsBehaviourRecordMsgRowDTO.getInfoType());
                    behaviourRecordPO.setInfoName(detail.getInfoName());
                    behaviourRecordPO.setScoreType(detail.getScoreType());
                    behaviourRecordPO.setIsScore(detail.getIsScore());
                    behaviourRecordPO.setScore(detail.getScore());
                    behaviourRecordPO.setScoreValue(detail.getScoreValue());

                    log.info("拆分导入行为记录数据:{}", behaviourRecordPO);

                    behaviourRecordPOList.add(behaviourRecordPO);
                }
            });
        } else {

            BehaviourRecord behaviourRecordPO = BuildBehaviourRecordPOBaseCommonInfo(importsBehaviourRecordMsgRowDTO);


            behaviourRecordPO.setOptionId(Convert.toStr(importsBehaviourRecordMsgRowDTO.getTargetId()));
            behaviourRecordPO.setInfoId(importsBehaviourRecordMsgRowDTO.getInfoId());
            behaviourRecordPO.setInfoType(importsBehaviourRecordMsgRowDTO.getInfoType());
            behaviourRecordPO.setInfoName(importsBehaviourRecordMsgRowDTO.getTargetName());


            importsBehaviourRecordMsgRowDTO.getDetailList().forEach(detail -> {
                if (SubmitInfoTypeEnum.SCORE.getText().equals(detail.getControlType())) {
                    behaviourRecordPO.setScoreType(detail.getScoreType());
                    behaviourRecordPO.setIsScore(detail.getIsScore());
                    behaviourRecordPO.setScore(detail.getScore());
                    behaviourRecordPO.setScoreValue(detail.getScoreValue());
                }
            });


            log.info("不拆分导入行为记录数据:{}", behaviourRecordPO);

            behaviourRecordPOList.add(behaviourRecordPO);
        }


    }

    private BehaviourRecord BuildBehaviourRecordPOBaseCommonInfo(ImportsBehaviourRecordMsgRowDTO importsBehaviourRecordMsgRowDTO) {
        BehaviourRecord behaviourRecordPO = new BehaviourRecord();

        behaviourRecordPO.setTenantId(importsBehaviourRecordMsgRowDTO.getTenantId());
        behaviourRecordPO.setSchoolId(importsBehaviourRecordMsgRowDTO.getSchoolId());
        behaviourRecordPO.setCampusId(importsBehaviourRecordMsgRowDTO.getCampusId());
        behaviourRecordPO.setCampusSectionId(importsBehaviourRecordMsgRowDTO.getCampusSectionId());
        behaviourRecordPO.setCampusSectionCode(importsBehaviourRecordMsgRowDTO.getCampusSectionCode());
        behaviourRecordPO.setGradeId(importsBehaviourRecordMsgRowDTO.getGradeId());
        behaviourRecordPO.setGradeCode(importsBehaviourRecordMsgRowDTO.getGradeCode());
        behaviourRecordPO.setClassId(importsBehaviourRecordMsgRowDTO.getClassId());
        behaviourRecordPO.setStudentId(importsBehaviourRecordMsgRowDTO.getStudentId());
        behaviourRecordPO.setModuleCode(importsBehaviourRecordMsgRowDTO.getModuleCode());
        behaviourRecordPO.setTargetId(importsBehaviourRecordMsgRowDTO.getTargetId());
        behaviourRecordPO.setTargetName(importsBehaviourRecordMsgRowDTO.getTargetName());
        behaviourRecordPO.setDataSource(BATCH_IMPORTS.getCode());
        behaviourRecordPO.setSubmitTime(importsBehaviourRecordMsgRowDTO.getSubmitDate());
        behaviourRecordPO.setAppraisalType(importsBehaviourRecordMsgRowDTO.getAppraisalType());
        behaviourRecordPO.setAppraisalId(importsBehaviourRecordMsgRowDTO.getAppraisalId());
        behaviourRecordPO.setCreateBy(importsBehaviourRecordMsgRowDTO.getAppraisalId());
        behaviourRecordPO.setAppraisalName(importsBehaviourRecordMsgRowDTO.getAppraisalName());
        behaviourRecordPO.setSubjectCode(importsBehaviourRecordMsgRowDTO.getSubjectCode());
        behaviourRecordPO.setNotPartCount(importsBehaviourRecordMsgRowDTO.getNotPartCount());
        behaviourRecordPO.setId(SnowFlakeIdUtil.nextId());

        return behaviourRecordPO;
    }
}
