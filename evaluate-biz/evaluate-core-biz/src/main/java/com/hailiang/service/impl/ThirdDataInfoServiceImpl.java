package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.mapper.ThirdDataInfoMapper;
import com.hailiang.model.dto.ThirdDataInfoDTO;
import com.hailiang.model.entity.ThirdDataInfoPO;
import com.hailiang.service.ThirdDataInfoService;
import java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 业务实现
 *
 * <AUTHOR> 2023-02-17 17:40:34
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ThirdDataInfoServiceImpl extends ServiceImpl<ThirdDataInfoMapper, ThirdDataInfoPO> implements ThirdDataInfoService {

    private final ThirdDataInfoMapper thirdDataInfoMapper;

    @Override
    public Page pageThirdDataInfo(Page page, ThirdDataInfoDTO thirdDataInfoDTO) {
        return this.page(page);
    }

    @Override
    public boolean saveThirdDataInfo(ThirdDataInfoDTO thirdDataInfoDTO) {
        ThirdDataInfoPO thirdDataInfoPO = new ThirdDataInfoPO();

        BeanUtil.copyProperties(thirdDataInfoDTO, thirdDataInfoPO);

        return this.save(thirdDataInfoPO);
    }

    @Override
    public boolean updateThirdDataInfo(ThirdDataInfoDTO thirdDataInfoDTO) {
        ThirdDataInfoPO thirdDataInfoPO = new ThirdDataInfoPO();

//        BeanUtils.copyProperties(thirdDataInfoDTO, thirdDataInfo);

        return this.updateById(thirdDataInfoPO);
    }

    @Override
    public boolean deleteThirdDataInfo(ThirdDataInfoDTO thirdDataInfoDTO) {
        return this.removeById(thirdDataInfoDTO.getId());
    }

    @Override
    public Integer updateFail(Long id, String remark) {
        return thirdDataInfoMapper.updateFail(id, remark);
    }

    @Override
    public Integer updateSuccess(Long id, Long localRecordId, String campusId) {
        return thirdDataInfoMapper.updateSuccess(id, localRecordId, campusId);
    }

    @Override
    public Integer batchUpdateFail(List<ThirdDataInfoDTO> thirdDataInfoDTOS) {
        return thirdDataInfoMapper.batchUpdateFail(thirdDataInfoDTOS);
    }

    @Override
    public Integer batchUpdateSuccess(List<ThirdDataInfoDTO> thirdDataInfoDTOS) {
        return thirdDataInfoMapper.batchUpdateSuccess(thirdDataInfoDTOS);
    }

    @Override
    public Map<String, Long> mapByThirdRecordIds(List<String> thirdRecordIdList) {

        List<ThirdDataInfoPO> list = list(new LambdaQueryWrapper<ThirdDataInfoPO>()
                .eq(ThirdDataInfoPO::getOperateFlag, 1)
                .in(ThirdDataInfoPO::getThirdRecordId, thirdRecordIdList)
                .isNotNull(ThirdDataInfoPO::getLocalRecordId)
        );

        Map<String, Long> thirdRecordMapByThirdRecordId = new HashMap<>();

        if (CollUtil.isNotEmpty(list)) {
            thirdRecordMapByThirdRecordId = list.stream()
                    .collect(Collectors.toMap(
                            ThirdDataInfoPO::getThirdRecordId, ThirdDataInfoPO::getLocalRecordId));

        }

        return thirdRecordMapByThirdRecordId;
    }

    @Override
    public Map<Long, String> mapByLocalRecordIds(List<Long> localRecordIdList) {
        List<ThirdDataInfoPO> list = list(new LambdaQueryWrapper<ThirdDataInfoPO>()
                .eq(ThirdDataInfoPO::getOperateFlag, 1)
                .ne(ThirdDataInfoPO::getDataType, 22)
                .in(ThirdDataInfoPO::getLocalRecordId, localRecordIdList));

        Map<Long, String> thirdRecordMapByThirdRecordId = new HashMap<>();

        if (CollUtil.isNotEmpty(list)) {
            thirdRecordMapByThirdRecordId = list.stream()
                    .collect(Collectors.toMap(
                            ThirdDataInfoPO::getLocalRecordId, ThirdDataInfoPO::getThirdRecordId));

        }

        return thirdRecordMapByThirdRecordId;
    }

    @Override
    public List<ThirdDataInfoPO> listByThirdRecordIds(List<Long> businessIds) {
        if (CollUtil.isNotEmpty(businessIds)) {
            return this.lambdaQuery()
                    .in(ThirdDataInfoPO::getThirdRecordId, Convert.toList(String.class, businessIds))
                    .list();
        }
        return Collections.emptyList();
    }


}
