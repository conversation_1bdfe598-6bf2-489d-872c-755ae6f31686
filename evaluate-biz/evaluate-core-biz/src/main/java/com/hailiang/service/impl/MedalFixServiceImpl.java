package com.hailiang.service.impl;

import cn.hutool.core.convert.Convert;
import com.hailiang.convert.TaskRuleConvert;
import com.hailiang.manager.*;
import com.hailiang.mapper.BehaviourRecordMapper;
import com.hailiang.mapper.MedalInfoMapper;
import com.hailiang.model.entity.MedalUserAcquireRecord;
import com.hailiang.remote.saas.dto.student.StudentByIdQuery;
import com.hailiang.remote.saas.vo.student.StudentVO;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/1 14:02
 */
@Slf4j
@Service
public class MedalFixServiceImpl implements MedalFixService {

    @Resource
    private MedalUserAcquireRecordManager medalUserAcquireRecordManager;

    @Resource
    private BasicInfoService basicInfoService;



    @Override
    @Transactional(timeout = 600)
    public Boolean fixData() {
        List<MedalUserAcquireRecord> medalUserAcquireRecords = medalUserAcquireRecordManager.list();
        for (MedalUserAcquireRecord medalUserAcquireRecord : medalUserAcquireRecords) {
            StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(medalUserAcquireRecord.getSchoolId()), Convert.toLong(medalUserAcquireRecord.getStudentId()));
            medalUserAcquireRecord.setClassName(studentVO .getClassName());
            medalUserAcquireRecordManager.updateById(medalUserAcquireRecord);
        }
//        List<Long> studentIds = medalUserAcquireRecords.stream().map(s->Convert.toLong(s.getStudentId())).distinct().collect(Collectors.toList());
//        StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
//        studentByIdQuery.setStudentIds(studentIds);
//        List<UcStudentClassBffVO> ucStudentClassBffVOS = basicInfoRemote.listByStudentIds(studentByIdQuery);
//        Map<String, String> classIdMap = ucStudentClassBffVOS.stream().collect(Collectors.toMap(s->Convert.toStr(s.getStudentId()), s->Convert.toStr(s.getClassId())));
//
//        List<MedalUserAcquireRecord> list =new ArrayList<>();
//
//        for (MedalUserAcquireRecord medalUserAcquireRecord : medalUserAcquireRecords) {
//            medalUserAcquireRecord.setClassId(Convert.toStr(classIdMap.get(medalUserAcquireRecord.getStudentId())));
//            list.add(medalUserAcquireRecord);
//        }
//
//        medalUserAcquireRecordManager.updateBatchById(medalUserAcquireRecords);
        return null;
    }
}
