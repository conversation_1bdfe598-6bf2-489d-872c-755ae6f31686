/**
 * Hailiang.com Inc. Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.hailiang.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.hailiang.convert.EvaluateUsualWordConvert;
import com.hailiang.exception.BizException;
import com.hailiang.internal.model.request.EvaluateUsualWordRemainRequest;
import com.hailiang.internal.model.request.EvaluateUsualWordRequest;
import com.hailiang.internal.model.response.EvaluateUsualWordResponse;
import com.hailiang.manager.EvaluateUsualWordManager;
import com.hailiang.model.entity.EvaluateUsualWordPO;
import com.hailiang.service.EvaluateUsualService;
import com.hailiang.util.WebUtil;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * <AUTHOR>
 * @version : EvaluateUsualServiceImpl.java, v 0.1 2025年07月29日 14:08  chenbw Exp $
 */
@Service
@Slf4j
public class EvaluateUsualServiceImpl implements EvaluateUsualService {

    @Resource
    private EvaluateUsualWordManager evaluateUsualWordManager;
    public static final long MAX_USUAL_WORD_SIZE = 20L;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<EvaluateUsualWordResponse> usualSave(EvaluateUsualWordRequest req) {
        Long staffId = WebUtil.getStaffIdLong();
        Long schoolId = WebUtil.getSchoolIdLong();
        Long tenantId = WebUtil.getTenantIdLong();
        Long campusId = WebUtil.getCampusIdLong();
        String uniqueKey = req.getUniqueKey();
        List<EvaluateUsualWordPO> list = evaluateUsualWordManager.byStaffAndKey(staffId, uniqueKey);
        if (list.size() >= MAX_USUAL_WORD_SIZE) {
            throw new BizException("最多添加20个!");
        }
        // 常用词不能重复
        List<String> userWords = CollStreamUtil.toList(list, EvaluateUsualWordPO::getUsualWords);
        if (userWords.contains(req.getUsualWords())) {
            throw new BizException("常用词不能重复");
        }
        EvaluateUsualWordPO usualWordPO = new EvaluateUsualWordPO();
        usualWordPO.setTenantId(tenantId);
        usualWordPO.setSchoolId(schoolId);
        usualWordPO.setCampusId(campusId);
        usualWordPO.setUsualWords(req.getUsualWords());
        usualWordPO.setUniqueKey(uniqueKey);
        usualWordPO.setStaffId(staffId);
        evaluateUsualWordManager.save(usualWordPO);

        List<EvaluateUsualWordResponse> res = EvaluateUsualWordConvert.IN.po2res(list);
        res.add(EvaluateUsualWordConvert.IN.po2res(usualWordPO));
        return res;
    }

    @Override
    public List<EvaluateUsualWordResponse> usualRemain(EvaluateUsualWordRemainRequest req) {
        Long staffId = WebUtil.getStaffIdLong();
        String uniqueKey = req.getUniqueKey();
        List<Long> remainIds = req.getIds();
        // 历史数据不存在直接返回
        List<EvaluateUsualWordPO> list = evaluateUsualWordManager.byStaffAndKey(staffId, uniqueKey);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<EvaluateUsualWordPO> remainList = list.stream().filter(item -> remainIds.contains(item.getId()))
                .collect(Collectors.toList());
        // 删除不在ids中的数据
        List<Long> delIds = list.stream().map(EvaluateUsualWordPO::getId).collect(Collectors.toList());
        delIds.removeAll(remainIds);
        if (CollUtil.isNotEmpty(delIds)) {
            evaluateUsualWordManager.removeByIds(delIds);
        }
        return EvaluateUsualWordConvert.IN.po2res(remainList);
    }

    @Override
    public Map<String, List<EvaluateUsualWordResponse>> usualList(List<String> keyList) {
        Long staffId = WebUtil.getStaffIdLong();
        List<EvaluateUsualWordPO> list = evaluateUsualWordManager.byStaffAndKeys(staffId, keyList);
        List<EvaluateUsualWordResponse> resList = EvaluateUsualWordConvert.IN.po2res(list);
        return CollStreamUtil.groupByKey(resList, EvaluateUsualWordResponse::getUniqueKey);
    }
}
