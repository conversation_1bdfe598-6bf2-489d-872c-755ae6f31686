package com.hailiang.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.exception.BizException;
import com.hailiang.feign.CheetahMessageService;
import com.hailiang.helper.CheetahSmsHelper;
import com.hailiang.helper.CommonHelper;
import com.hailiang.model.dto.save.LoginAuthDTO;
import com.hailiang.model.dto.save.VerificationCodeGetDTO;
import com.hailiang.service.LoginAuthService;
import com.hailiang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class LoginAuthServiceImpl implements LoginAuthService {

    @Value("${cheetah.message.appId}")
    private String cheetahMessageAppId;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private CheetahMessageService cheetahMessageService;

    @Override
    public String verificationCodeGet(VerificationCodeGetDTO verificationCodeGetDTO) {
        // 1. 参数校验
        Assert.notNull(verificationCodeGetDTO, "请求参数不能为空");
        verificationCodeGetDTO.valid();

        // 2. 对短信验证码频繁获取时进行拦截
        String mobileTrim = verificationCodeGetDTO.getMobile().trim();
        String limitVerificationCodeKey = MessageFormat.format("{0}_{1}_{2}",
            Constant.LIMIT_VERIFICATION_CODE_KEY,
            Constant.FAMILY_KEY,
            mobileTrim);
        Object object = redisUtil.get(limitVerificationCodeKey);
        if(ObjectUtil.isNotNull(object)){
            throw new BizException("操作过于频繁，请稍后重试！");
        }

        // 3. 发送短信验证码，此处不论该验证码是否被谁用，只要能再次发送短信验证码都按照最新的来，且过期时间重置为当前新验证码生成的时间完后开始计时
        String verificationCodeKey = MessageFormat.format("{0}_{1}_{2}",
            Constant.VERIFICATION_CODE_KEY,
            Constant.FAMILY_KEY,
            mobileTrim);
        String generateCode = CommonHelper.generateCode();
        this.redisUtil.deleteKey(verificationCodeKey);
        this.redisUtil.setIfAbsentForMinute(verificationCodeKey, generateCode, Constant.MESSAGE_CODE_EXPIRE_TIME);
        this.redisUtil.setIfAbsent(limitVerificationCodeKey, "limit_send_message",60);
        JSONObject jsonObject = new JSONObject();
        jsonObject.set(Constant.VERIFICATION_CODE, generateCode);
            this.cheetahMessageService.send(CheetahSmsHelper.buildParam("evaluate", this.cheetahMessageAppId, "code1", jsonObject.toString(), mobileTrim));
        return Constant.SUCCESS;
    }

    @Override
    public String verificationAuth(LoginAuthDTO loginAuthDTO) {
        // 1. 参数校验
        Assert.notNull(loginAuthDTO, "请求参数不能为空");
        loginAuthDTO.valid();

        // 2. 校验短信验证码+生成token，token作用域仅限于获取报告单相关接口
        String mobileTrim = loginAuthDTO.getMobile();
        String limitVerificationCodeKey = MessageFormat.format("{0}_{1}_{2}",
            Constant.LIMIT_VERIFICATION_CODE_KEY,
            Constant.FAMILY_KEY,
            mobileTrim);
        String verificationCodeKey = MessageFormat.format("{0}_{1}_{2}",
            Constant.VERIFICATION_CODE_KEY,
            Constant.FAMILY_KEY,
            mobileTrim);
        Object obj = this.redisUtil.get(verificationCodeKey);
        Assert.notNull(obj, "验证码不存在");
        String messageCodeInRedis = obj.toString();
        Assert.isTrue(StringUtils.equals(messageCodeInRedis, loginAuthDTO.getVerificationCode()), "验证码错误");

        // 3. 生成token，放入redis，并设置该token过期时间，有效期为当前生成日期开始往后偏移60分钟
        Map<String, Object> playLoadMap = new HashMap<>();
        playLoadMap.put(JWTPayload.EXPIRES_AT, DateUtil.offsetMinute(DateUtil.date(), 60));
        playLoadMap.put("mobile", mobileTrim);
        String token = JWTUtil.createToken(playLoadMap, StrUtil.bytes(this.cheetahMessageAppId));

        // 4. 删除短信验证码和60s内重新获取拦截
        this.redisUtil.deleteKey(verificationCodeKey);
        this.redisUtil.deleteKey(limitVerificationCodeKey);
        return token;
    }
}
