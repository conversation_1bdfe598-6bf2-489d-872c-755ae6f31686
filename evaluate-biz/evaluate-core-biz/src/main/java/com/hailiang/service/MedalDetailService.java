package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.dto.activity.detail.*;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/21 13:51
 */
public interface MedalDetailService {

    /**
     * 颁章明细查询
     *
     * @param query 查询条件
     * @return
     */
    Page<MedalDetailVO> listMedalDetail(MedalDetailPage query);

    /**
     * 导出颁章明细
     *
     * @param query    查询条件
     * @param response 响应体
     */
    void exporterMedalDetail(MedalDetailQuery query, HttpServletResponse response);

    /**
     * 查询颁章明细操作记录
     *
     * @param dto 查询参数
     * @return
     */
    Page<MedalOperationRecordVO> listOperationRecord(MedalBusinessIdDTO dto);

    /**
     * 查询个人争章任务进度
     *
     * @param query 查询参数
     * @return
     */

    /**
     * 手动颁发奖章
     *
     * @param dto 请求参数
     * @return
     */
    Boolean artificialIssueMedal(IssueMedalDTO dto);

    /**
     * 手动撤回奖章
     *
     * @param dto 请求参数
     * @return
     */
    Boolean artificialWithdrawMedal(MedalBusinessIdDTO dto);

    /**
     * 根据活动id获取活动下的奖章信息
     *
     * @param dto 请求参数
     * @return
     */
    List<MedalActivityInfoDTO> listActivityMedalInfo(MedalActivityIdDTO dto);

    /**
     * 获取活动列表
     *
     * @param dto 请求参数
     * @return
     */
    List<MedalActivityVO> listActivity(MedalActivityDTO dto);

    /**
     * 查询活动下的年级班级树形结构
     *
     * @param dto 请求参数
     * @return
     */
    List<EduOrgTreeVO> listMedalOrgTree(MedalActivityIdDTO dto);

    /**
     * 通过班级id查询学生信息
     *
     * @param dto 请求参数
     * @return
     */
    List<MedalStudentVO> listStudentInfo(MedalStudentDTO dto);

    /**
     * 获取奖章列表
     *
     * @return
     */
    List<MedalCampusInfoDTO> listMedalInfo();

}
