package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.MongoPage;
import com.hailiang.entity.EvaluateBehaviourRecordExtPO;
import com.hailiang.model.dto.query.*;
import com.hailiang.model.dto.save.TaskSaveDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.TaskPO;
import com.hailiang.model.response.speed.SpeedRecordResponse;
import com.hailiang.model.response.speed.SpeedRecordStudentResponse;
import com.hailiang.model.vo.*;
import com.hailiang.saas.model.vo.StudentInfoVO;

import java.util.List;
import java.util.Map;

public interface TaskService extends IService<TaskPO> {

    /**
     * 生成评估任务（定时器)
     *
     * @param dtos
     * @return
     */
    List<TaskSaveVO> saveEvaluateTasks(List<TaskSaveDTO> dtos);

    /**
     * 新增完成任务
     *
     * @param dto
     * @return
     */
    Long saveFinishedEvaluateTasks(TaskSaveDTO dto);

    /**
     * 完成任务
     *
     * @param taskId
     * @return
     */
    boolean finishTask(Long taskId);


    /**
     * 查询当前评估
     *
     * @return
     */
    List<ListTeacherCurrentEvaluateTaskVO> listTeacherCurrentEvaluateTask();

    /**
     * 查询全部评估指标
     *
     * @return
     */
    List<ListAllEvaluateTargetVOModule> listTeacherAllEvaluateTarget();

    /**
     * 查询（搜索）填报历史
     *
     * @param dto
     * @return
     */
    MongoPage<PageEvaluateTaskHistoryVO> pageEvaluateTaskHistory(PageEvaluateTaskHistoryTeacherQueryDTO dto);

    /**
     * 查询表单提交页面的表单
     *
     * @param dto
     * @return
     */
    GetEvaluateTaskDetailVO getEvaluateTaskDetail(GetEvaluateTaskDetailQueryDTO dto);

    /**
     * 钉钉待办消息进哪个页面
     *
     * @param dto
     * @return
     */
    GetTaskDetailForDDVO getTaskDetailForDD(GetTaskDetailForDDDTO dto);

    /**
     * 查询家长当前填报指标
     *
     * @return
     */
    List<ListCurrentEvaluateTaskVO> listParentCurrentEvaluateTask();

    /**
     * 查询家长的所有填报指标
     *
     * @return
     */
    List<ListAllEvaluateTargetVOModule> listParentAllEvaluateTarget();

    /**
     * 查询（搜索）填报历史(家长端)
     * @param dto
     * @param roleType 角色类型 1:老师 2:家长 3:学生
     * @return
     */
    MongoPage<PageEvaluateTaskHistoryVO> pageParentEvaluateTaskHistory(PageEvaluateTaskHistoryQueryDTO dto, Integer roleType);

    /**
     * 查询（搜索）填报历史(学生端)
     * @param dto
     * @return
     */
    MongoPage<PageEvaluateTaskHistoryVO> pageStudentEvaluateTaskHistory(PageEvaluateTaskHistoryQueryDTO dto);

    Page<TeacherApprovalHistoryVO> pageTeacherApprovalHistory(PageEvaluateTaskHistoryTeacherQueryDTO dto);

    NodeDetailVo getParentTaskNextNode(ParentTaskNodeDTO dto);

    /**
     * 获取时间枚举 1:近两周 2：近一月 3：近两月 4：近四月 5：本学期
     *
     * @return
     */
    List<MapVO> getTimeEnum();


    void fillSchoolRecordDetail(List<BehaviourRecord> studentIdToRecordEntryValue,
                                List<SpeedRecordResponse> speedRecordResponses,
                                Map<Long, EvaluateBehaviourRecordExtPO> longEvaluateBehaviourRecordClassifyPOMap,
            Map<Long, List<String>> optExtPOS);

    /**
     * 构建极速点评学生详情
     */
    List<SpeedRecordResponse> listSpeedRecordResponses(Map.Entry<String, List<BehaviourRecord>> studentIdToRecordEntry,
                                                       List<SpeedRecordStudentResponse> speedRecordStudentResponseList,
                                                       List<StudentInfoVO> studentInfoVOS);

    /**
     * 填充校标记录详情
     */
//    void fillSchoolRecordDetail(List<BehaviourRecord> studentIdToRecordEntryValue,
//                                List<SpeedTargetGroupPO> finalSpeedTargetGroupPOS,
//                                List<SpeedRecordResponse> speedRecordResponses,
//                                Map<Long, EvaluateBehaviourRecordExtPO> longEvaluateBehaviourRecordClassifyPOMap);

    /**
     * 填充师标记录详情
     */
    void fillGeneralRecordDetail(List<BehaviourRecord> studentIdToRecordEntryValue,
                                 List<SpeedRecordResponse> speedRecordResponses,
                                 Map<Long, EvaluateBehaviourRecordExtPO> longEvaluateBehaviourRecordClassifyPOMap);

    List<ListAllEvaluateTargetVOModule> listStudentEvaluateTarget();

}
