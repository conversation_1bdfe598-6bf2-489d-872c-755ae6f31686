package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hailiang.base.BaseService;
import com.hailiang.common.lock.annotation.RLock;
import com.hailiang.convert.CheckConvert;
import com.hailiang.enums.DimTemplateEnum;
import com.hailiang.enums.SourceTypeEnum;
import com.hailiang.manager.CheckDimManager;
import com.hailiang.model.dto.query.ListCheckDimBaseInfoDTO;
import com.hailiang.model.entity.CheckDim;
import com.hailiang.model.entity.CheckItemPO;
import com.hailiang.model.vo.CheckDimBaseInfoVO;
import com.hailiang.model.vo.CheckItemBaseInfoVO;
import com.hailiang.request.HeaderRequest;
import com.hailiang.saas.uc.api.util.AssertUtil;
import com.hailiang.service.CheckDimService;
import com.hailiang.service.CheckItemService;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/19 15:51
 */
@Service
@RequiredArgsConstructor
public class CheckDimServiceImpl extends BaseService implements CheckDimService {
    private static final String SOURCE_TYPE = SourceTypeEnum.LDHQ.getCode();
    private final CheckDimManager dimManager;
    private final CheckConvert checkConvert;
    private final CheckItemService itemService;

    @Override
    @RLock(name = "createDefaultTemplates", keys = {"#tenantId", "#schoolId", "#campusId"})
    public List<CheckDim> createDefaultTemplates(String tenantId, String schoolId, String campusId) {

        List<CheckDim> dims = dimManager.list(new LambdaQueryWrapper<CheckDim>()
                .select(CheckDim::getId, CheckDim::getName, CheckDim::getIsDefault)
                .eq(CheckDim::getTenantId, tenantId).eq(CheckDim::getSchoolId, schoolId)
                .eq(CheckDim::getCampusId, campusId).eq(CheckDim::getSourceType, SOURCE_TYPE)
                .eq(CheckDim::getIsDefault, Boolean.TRUE));
        if (CollectionUtils.isNotEmpty(dims)) return dims;

        List<CheckDim> initDims = this.initDimTemplates(tenantId, schoolId, campusId);
        dimManager.saveBatchDiy(initDims);
        return initDims;
    }

    @Override
    public List<CheckDim> queryDims() {
        HeaderRequest headerRequest = super.checkRequestHeader();
        String tenantId = headerRequest.getTenantId();
        String schoolId = headerRequest.getSchoolId();
        String campusId = headerRequest.getCampusId();

        return dimManager.list(new LambdaQueryWrapper<CheckDim>()
                .select(CheckDim::getId, CheckDim::getName, CheckDim::getIsDefault)
                .eq(CheckDim::getTenantId, tenantId).eq(CheckDim::getSchoolId, schoolId)
                .eq(CheckDim::getCampusId, campusId).eq(CheckDim::getSourceType, SOURCE_TYPE));
    }

    private List<CheckDim> initDimTemplates(String tenantId, String schoolId, String campusId) {
        DimTemplateEnum[] dimDefaultTemplate = DimTemplateEnum.values();
        String staffId = WebUtil.getStaffId();

        return Arrays.stream(dimDefaultTemplate).map(e -> {
            CheckDim checkDim = new CheckDim();
            long id = IdWorker.getId(checkDim);
            checkDim.setId(id);
            checkDim.setName(e.getName());
            checkDim.setIsDefault(e.getIsDefault());
            checkDim.setSourceType(SOURCE_TYPE);
            checkDim.setTenantId(tenantId);
            checkDim.setSchoolId(schoolId);
            checkDim.setCampusId(campusId);
            checkDim.setCreateBy(staffId);
            checkDim.setUpdateBy(staffId);
            return checkDim;
        }).collect(Collectors.toList());
    }

    /**
     * 根据校区id与来源查检查维度map key：维度id value：维度名称
     * @param campusId
     * @param sourceType
     * @return
     */
    @Override
    public Map<Long, String> queryAllCheckDimIdNameMap(String campusId, String sourceType){
        return dimManager.queryAllCheckDimIdNameMap(campusId, sourceType);
    }

    /**
     * 查检查维度--检查项列表
     * @return
     */
    @Override
    public List<CheckDimBaseInfoVO> queryCheckDimBaseInfoList(ListCheckDimBaseInfoDTO query){
        List<CheckDim> checkDimList = dimManager.queryCheckDimList(query.getCampusId(), query.getSourceType(), query.getIsDefault());
        AssertUtil.checkNotEmpty(checkDimList, "当前学校下无检查维度，请联系管理员进行配置");
        // 排序
        List<CheckDim> checkDimSortList = checkDimList.stream().sorted(Comparator.comparing(CheckDim::getId)).collect(Collectors.toList());
        List<CheckDimBaseInfoVO> resultList = checkConvert.toCheckDimBaseInfoVOList(checkDimSortList);

        if (query.getContainsItemFlag()){
            // 补充检查项信息
            this.appendCheckItemBaseInfo(resultList, query.getCampusId(), query.getSourceType());
        }

        return resultList;
    }

    /**
     * 补充检查项信息
     * @param checkDimBaseInfoVOList
     * @param campusId
     * @param sourceType
     */
    private void appendCheckItemBaseInfo(List<CheckDimBaseInfoVO> checkDimBaseInfoVOList, String campusId, String sourceType){
        List<CheckItemPO> checkItemPOList = itemService.queryCheckItemList(campusId, sourceType);
        if (CollUtil.isEmpty(checkItemPOList)){
            return ;
        }
        // 排序分组
        Map<String, List<CheckItemPO>> checkDimIdCheckItemListMap = checkItemPOList.stream().sorted(Comparator.comparing(CheckItemPO::getSortIndex)).collect(Collectors.groupingBy(CheckItemPO::getDimId));
        for (CheckDimBaseInfoVO item : checkDimBaseInfoVOList) {
            List<CheckItemPO> list = checkDimIdCheckItemListMap.get(Convert.toStr(item.getCheckDimId()));
            if (CollUtil.isEmpty(list)){
                continue;
            }

            List<CheckItemBaseInfoVO> convertList = checkConvert.toCheckItemBaseInfoVOList(list);
            item.setCheckItemList(convertList);
        }
    }
}
