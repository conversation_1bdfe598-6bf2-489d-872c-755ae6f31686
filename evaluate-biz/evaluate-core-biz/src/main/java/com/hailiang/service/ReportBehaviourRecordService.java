package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.report.ReportBehaviourRecordDiffDTO;
import com.hailiang.model.entity.ReportBehaviourRecord;

import java.util.List;

/**
 * 审核行为记录表(ReportBehaviourRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-13 11:51:01
 */
public interface ReportBehaviourRecordService extends IService<ReportBehaviourRecord> {
    List<Long> listNoSendRecordByDetail(String detailId);

    List<ReportBehaviourRecord> listNoSendRecordByDetailList(Long detailId);

    /**
     * 批量获取无需推送的行为记录
     * @param detailId
     * @return
     */
    List<ReportBehaviourRecord> listNoSendRecordByDetailIdList(List<Long> detailId);

    List<ReportBehaviourRecord> listSendRecordByDetailList(Long detailId);

    /**
     * 查询已经发送的行为记录
     * @param detailId
     * @return
     */
    ReportBehaviourRecordDiffDTO listSendRecordDIffByDetailList(Long detailId);
}

