package com.hailiang.service;

import com.hailiang.model.dto.SaveCheckGroupDTO;
import com.hailiang.model.dto.UpdateCheckGroupDTO;
import com.hailiang.model.entity.CheckGroupPO;

import java.util.List;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/19 16:33
 */
public interface CheckGroupService {

    void convertAndCreateGroup(SaveCheckGroupDTO dto);

    void editGroup(UpdateCheckGroupDTO dto);

    List<CheckGroupPO> fillSortIndex(List<String> ids);

    List<CheckGroupPO> queryOrderedGroups(String dimId);

    void verifyGroupExists(Long id);
}
