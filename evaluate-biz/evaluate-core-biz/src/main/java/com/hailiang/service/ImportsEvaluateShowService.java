package com.hailiang.service;

import com.hailiang.model.dto.response.ModuleCommonResponse;
import com.hailiang.model.dto.response.SubjectCommonResponse;
import com.hailiang.model.dto.response.evaluate.imports.ImportsEvaluateTableResponse;
import java.util.List;

/**
 * 点评导入核心服务
 *
 * @Description: 点评导入核心服务
 * @Author: Jovi
 * @Date: Created in 2024-12-02
 * @Version: 2.0.0
 */
public interface ImportsEvaluateShowService {


    /**
     * 根据指标ID获取表格列信息
     *
     * @param targetId
     * @return
     */
    List<ImportsEvaluateTableResponse> listTableColumnByTargetId(Long targetId);

    /**
     * 获取五育模块列表
     *
     * @return
     */
    List<ModuleCommonResponse> listModules();

    /**
     * 获取学科列表
     *
     * @param classIds
     * @return
     */
    List<SubjectCommonResponse> listSubject(String sectionCode,
                                            String campusSectionId,
                                            String gradeId,
                                            List<Long> classIds);
}
