package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hailiang.manager.PointChangeLogManager;
import com.hailiang.model.dto.BehaviourRecordMqDTO;
import com.hailiang.model.dto.motivate.BehaviourPointExchangeDTO;
import com.hailiang.model.dto.motivate.BehaviourPointExchangeMessageDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.PointChangeLog;
import com.hailiang.model.point.dto.PointInitialMsgConvertDTO;
import com.hailiang.service.ConvertInitialService;
import com.hailiang.util.SnowFlakeIdUtil;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 转积分服务类
 *
 * @Description: 转积分服务类
 * @Author: Jovi
 * @Date: Created in 2024-11-08
 * @Version: 2.0.0
 */
@Service
@Slf4j
public class ConvertInitialServiceImpl implements ConvertInitialService {

    @Resource
    RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.topic.behaviourRecord}")
    private String behaviourRecordTopic;

    @Value("${rocketmq.topic.behaviorPointExchange}")
    private String behaviorPointExchangeTopic;

    @Resource
    private PointChangeLogManager pointChangeLogManager;

    /**
     * 发送MQ转积分
     *
     * @param businessType       参考 {@link com.hailiang.enums.PointMqBusinessTypeEnum}
     * @param dataOperateType    参考 {@link com.hailiang.enums.PointMqDataOperateTypeEnum}
     * @param records
     * @param oldBehaviourRecord
     * @return
     */
    @Override
    public Boolean sendMQ(Integer businessType,
                          Integer dataOperateType,
                          List<BehaviourRecord> records,
                          BehaviourRecord oldBehaviourRecord) {

        log.info("[行为记录]-[发送mq]-要发送的数据:{}", JSONUtil.toJsonStr(records));
        if (CollUtil.isEmpty(records)) {
            log.warn("[行为记录]-[发送mq]-[mq消息发送异常] records为空");
            return false;
        }
        BehaviourRecordMqDTO behaviourRecordMqDTO = new BehaviourRecordMqDTO();
        behaviourRecordMqDTO.setBusinessType(businessType);
        behaviourRecordMqDTO.setDataOperateType(dataOperateType);
        behaviourRecordMqDTO.setRecords(records);
        behaviourRecordMqDTO.setOldBehaviourRecord(oldBehaviourRecord);

        BehaviourRecord record = records.get(0);
        // 插入积分变动日志
        PointChangeLog pointChangeLog = new PointChangeLog();
        pointChangeLog.setId(SnowFlakeIdUtil.nextId());
        pointChangeLog.setTenantId(record.getTenantId());
        pointChangeLog.setSchoolId(record.getSchoolId());
        pointChangeLog.setCampusId(record.getCampusId());
        pointChangeLog.setSourceDataInfo(JSONUtil.toJsonStr(behaviourRecordMqDTO));
        pointChangeLog.setOperateType(dataOperateType);
        pointChangeLog.setDataType(1);
        pointChangeLog.setHandleStatus(1);
        pointChangeLog.setCreateBy(StrUtil.isBlank(record.getCreateBy()) ? record.getAppraisalId() :
                record.getCreateBy());

        pointChangeLogManager.save(pointChangeLog);

        behaviourRecordMqDTO.setLogParentId(pointChangeLog.getId());

        log.info("[行为记录]-[发送mq]-要发送的数据:{}", JSONUtil.toJsonStr(behaviourRecordMqDTO));
        rocketMQTemplate.asyncSendOrderly(behaviourRecordTopic, behaviourRecordMqDTO, "point", new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("[行为记录]-[新增]-[mq消息发送成功]，pointChangeLogId:{}",
                        behaviourRecordMqDTO.getLogParentId());
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("[行为记录]-[新增]-[mq消息发送异常] 数据:{},异常信息：{}",
                        JSONUtil.toJsonStr(behaviourRecordMqDTO),
                        throwable.getMessage());
            }
        });

        return true;
    }

    /**
     * @param businessType    1：添加  3：删除(触发时方式的业务操作，业务可能是修改， 但是对应到数据变动类型上可能是1条删除 1条添加)
     * @param dataOperateType 1：添加  3：删除
     * @return
     */
    @Override
    public Boolean initialScoreSendMQ(Integer businessType,
                                      Integer dataOperateType,
                                      Date submitTime,
                                      PointInitialMsgConvertDTO pointInitialMsgConvertDTO) {

        log.info("【初始分模块】-【发送消息转积分】-【消息内容】:{}", pointInitialMsgConvertDTO);

        BehaviourRecordMqDTO behaviourRecordMqDTO = new BehaviourRecordMqDTO();
        behaviourRecordMqDTO.setMsgType(1);
        behaviourRecordMqDTO.setBusinessType(businessType);
        behaviourRecordMqDTO.setDataOperateType(dataOperateType);
        behaviourRecordMqDTO.setPointInitialMsgConvertDTO(pointInitialMsgConvertDTO);

        // 插入积分变动日志
        PointChangeLog pointChangeLog = new PointChangeLog();
        pointChangeLog.setTenantId(pointInitialMsgConvertDTO.getTenantId());
        pointChangeLog.setSchoolId(pointInitialMsgConvertDTO.getSchoolId());
        pointChangeLog.setCampusId(pointInitialMsgConvertDTO.getCampusId());
        pointChangeLog.setSourceDataInfo(JSONUtil.toJsonStr(behaviourRecordMqDTO));
        pointChangeLog.setOperateType(dataOperateType);
        pointChangeLog.setDataType(1);
        pointChangeLog.setHandleStatus(1);
        pointChangeLog.setCreateBy(pointInitialMsgConvertDTO.getCreateBy());
        pointChangeLog.setCreateTime(submitTime);

        pointChangeLogManager.save(pointChangeLog);

        behaviourRecordMqDTO.setLogParentId(pointChangeLog.getId());

        log.info("【初始分模块】-【发送消息转积分】-【要发送的数据:{}】", JSONUtil.toJsonStr(behaviourRecordMqDTO));
        rocketMQTemplate.asyncSendOrderly(behaviourRecordTopic, behaviourRecordMqDTO, "initialPoint",
                new SendCallback() {
                    @Override
                    public void onSuccess(SendResult sendResult) {
                        log.info("【初始分模块】-【发送消息转积分】-【发送成功】，pointChangeLogId:{}",
                                behaviourRecordMqDTO.getLogParentId());
                    }

                    @Override
                    public void onException(Throwable throwable) {
                        log.error("【初始分模块】-【发送消息转积分】-【发送失败:{}】,异常信息：{}",
                                JSONUtil.toJsonStr(behaviourRecordMqDTO),
                                throwable.getMessage());
                    }
                });

        return true;
    }

    @Override
    public Boolean sendBehaviourExchangeMq(List<BehaviourPointExchangeDTO> behaviourPointExchanges) {
        if (CollUtil.isEmpty(behaviourPointExchanges)) {
            return false;
        }

        BehaviourPointExchangeMessageDTO behaviourPointExchangeMessageDTO = new BehaviourPointExchangeMessageDTO();
        behaviourPointExchangeMessageDTO.setBehaviourPointExchanges(behaviourPointExchanges);

        this.sendPointExchangeMq(behaviourPointExchangeMessageDTO);
        return true;
    }

    private void sendPointExchangeMq(BehaviourPointExchangeMessageDTO behaviourPointExchangeMessageDTO) {

        rocketMQTemplate.asyncSendOrderly(behaviorPointExchangeTopic, behaviourPointExchangeMessageDTO,
                "pointExchange", new SendCallback() {
                    @Override
                    public void onSuccess(SendResult sendResult) {
                        log.info("[行为记录转换金币]-[新增]-[mq消息发送成功]，消息体:{}",
                                JSONUtil.toJsonStr(behaviourPointExchangeMessageDTO));
                    }

                    @Override
                    public void onException(Throwable throwable) {
                        log.error("[行为记录转换金币]-[新增]-[mq消息发送异常] 数据:{},异常信息：{}",
                                JSONUtil.toJsonStr(behaviourPointExchangeMessageDTO),
                                throwable.getMessage());
                    }
                });

    }

}
