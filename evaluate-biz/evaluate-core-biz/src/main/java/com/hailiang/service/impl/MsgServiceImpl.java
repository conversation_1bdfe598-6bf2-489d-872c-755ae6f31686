package com.hailiang.service.impl;

import com.hailiang.feign.CheetahMessageService;
import com.hailiang.helper.CheetahFeiShuHelper;
import com.hailiang.model.dto.CheetahSendRequest;
import com.hailiang.remote.feishu.domain.FeiMsgReqDTO;
import com.hailiang.service.MsgService;
import com.hailiang.util.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> hljy
 * @date 2023/8/10 16:16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MsgServiceImpl implements MsgService {

    @Value("${cheetah.message.appId}")
    private String appId;

    final CheetahMessageService cheetahMessageService;

    @Override
    public void sendFeiShuMsg(FeiMsgReqDTO msg) {

        String msgContext = this.textMsgContextTemplate(msg.getTitle(), msg.getContext(), "查看详情", msg.getMsgUrl());

        CheetahSendRequest request = CheetahFeiShuHelper.buildParamWithInteractive("evaluate", appId, msgContext, msg.getMobile(), msg.getCallBackId());
        log.info("[飞书消息发送]-[发送纯文本消息]-[请求内容:{}]", request);
        try {
            R response = cheetahMessageService.send(request);
            log.info("[飞书消息发送]-[发送纯文本消息]-[发送结果状态码:{}]-[响应内容:{}]", response.getStatus(), response);
        } catch (Exception e) {
            log.error("[飞书消息发送]-[发送纯文本消息]-[发送异常]-[请求内容:{}]", request, e);
        }

    }

    @Override
    public void sendDingDingMsg() {

    }


    /**
     * 飞书卡片消息内容模板(纯文本消息)
     *
     * @param title          通知标题
     * @param context        通知内容
     * @param jumpButtonName 跳转按钮名称
     * @param msgUrl         消息跳转url
     * @return
     */
    public String textMsgContextTemplate(String title, String context, String jumpButtonName, String msgUrl) {
        return "{\n" +
                "  \"config\": {\n" +
                "    \"wide_screen_mode\": true\n" +
                "  },\n" +
                "  \"header\": {\n" +
                "    \"template\": \"blue\",\n" +
                "    \"title\": {\n" +
                "      \"tag\": \"plain_text\",\n" +
                "      \"i18n\": {\n" +
                "        \"zh_cn\": \"" + title + "\"\n" +
                "      }\n" +
                "    }\n" +
                "  },\n" +
                "  \"i18n_elements\": {\n" +
                "    \"zh_cn\": [\n" +
                "      {\n" +
                "        \"tag\": \"div\",\n" +
                "        \"text\": {\n" +
                "          \"content\": \"" + context + "\",\n" +
                "          \"tag\": \"plain_text\"\n" +
                "        }\n" +
                "      },\n" +
                "      {\n" +
                "        \"tag\": \"action\",\n" +
                "        \"actions\": [\n" +
                "          {\n" +
                "            \"tag\": \"button\",\n" +
                "            \"text\": {\n" +
                "              \"tag\": \"plain_text\",\n" +
                "              \"content\": \"" + jumpButtonName + "\"\n" +
                "            },\n" +
                "            \"url\": \"" + msgUrl + "\",\n" +
                "            \"type\": \"primary\"\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";
    }

}
