package com.hailiang.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.datastatistics.query.DataStatisticsQuery;
import com.hailiang.model.datastatistics.query.TeacherEvaluateDataStatisticsQuery;
import com.hailiang.model.datastatistics.vo.*;
import com.hailiang.remote.saas.vo.staff.StaffFullInfoVO;
import com.hailiang.remote.saas.vo.term.TermVo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DataStatisticsService {
    /**
     * 获取学期时间
     *
     * @return
     */
    TermVo getTime(String campusSectionId);

    /**
     * 获取当前筛选时间上一个相同时间段
     *
     * @param startTime
     * @param endTime
     * @return
     */
    LastSameTimeVO getLastSameTime(Date startTime, Date endTime, String campusSectionId);

//    /**
//     * 老师参与率
//     *
//     * @param dto
//     * @return
//     */
//    DataStatisticsTeacherParticipationRateVO getTeacherParticipationRate(DataStatisticsQuery dto);
//
//    /**
//     * 点评总次数
//     *
//     * @param dto
//     * @return
//     */
//    DataStatisticsEvaluateNumVO getEvaluateNum(DataStatisticsQuery dto);

    /**
     * 老师点评统计
     * @param query
     * @return
     */
    TeacherEvaluateDataStatisticsVO getTeacherEvaluateStatistics(TeacherEvaluateDataStatisticsQuery query);

    /**
     * 老师点评榜（支持历史学年）
     * @param query
     * @return
     */
    Page<TeacherVO> pageTeacherEvaluateNew(TeacherEvaluateDataStatisticsQuery query);

    /**
     * 老师点评榜下载（支持历史学年）
     * @param query
     * @param response
     */
    void exportTeacherEvaluateNew(TeacherEvaluateDataStatisticsQuery query, HttpServletResponse response);

    /**
     * 指标排行榜（支持历史学年）
     * @param query
     * @return
     */
    Page<TargetRankVO> getTargetRankNew(TeacherEvaluateDataStatisticsQuery query);

    /**
     * 指标排行榜下载（支持历史学年）
     * @param query
     * @param response
     */
    void exportTargetRankNew(TeacherEvaluateDataStatisticsQuery query, HttpServletResponse response);

//    /**
//     * 表扬与待改进
//     *
//     * @param dto
//     * @return
//     */
//    PraiseImproveVO getPraiseImproveDetail(DataStatisticsQuery dto);
//
//    /**
//     * 五育分布
//     *
//     * @param dto
//     * @return
//     */
//    FiveEducationVO getFiveEducation(DataStatisticsQuery dto);
//
//    /**
//     * 指标覆盖率
//     *
//     * @param dto
//     * @return
//     */
//    TargetCoverageVO getTargetCoverage(DataStatisticsQuery dto);
//
//    /**
//     * 指标覆盖情况
//     *
//     * @param dto
//     * @return
//     */
//    List<TargetFrequencyVO> getTargetFrequency(DataStatisticsQuery dto);
//
//    /**
//     * 指标排行榜
//     *
//     * @param dto
//     * @return
//     */
//    Page<TargetRankVO> getTargetRank(DataStatisticsQuery dto);
//
//    /**
//     * 导出指标排行榜excel
//     *
//     * @param response
//     * @param dto
//     */
//    void exportTargetRank(HttpServletResponse response, DataStatisticsQuery dto);
//
//    /**
//     * 学生点评覆盖率
//     *
//     * @param dto
//     * @return
//     */
//    DataStatisticsStudentEvaluateRateVO getStudentEvaluateRate(DataStatisticsQuery dto);
//
//    /**
//     * 老师点评榜
//     *
//     * @param dto
//     * @return
//     */
//    Page<TeacherVO> pageTeacherEvaluate(DataStatisticsQuery dto);
//
//    /**
//     * 老师点评榜下载
//     *
//     * @param dto
//     * @return
//     */
//    void exportTeacherEvaluate(DataStatisticsQuery dto, HttpServletResponse response) throws IOException;

    /**
     * 年级分组（校级看板未被点评学生名单）
     *
     * @param dto
     * @return
     */
    List<GradeInfoVO> listGrade(DataStatisticsQuery dto);

    /**
     * 未被点评学生名单
     *
     * @param dto
     * @return
     */
    Page<StudentVO> pageNoEvaluateStudent(DataStatisticsQuery dto);

    /**
     * 未被点评学生名单下载
     *
     * @param dto
     * @return
     */
    void exportNoEvaluateStudent(DataStatisticsQuery dto, HttpServletResponse response) throws IOException;

    /**
     * 获取教职工的权限范围（3：校级，4：年级，5：班级）
     *
     * @param
     * @return
     */
    Integer getRoleType(String staffId);

    /**
     * 根据校区获取需发生通知老师的信息
     *
     * @param campusId
     * @param startTime
     * @param endTime
     * @return
     */
    ArrayList<StaffFullInfoVO> listTeacherInfo(String schoolId, String campusId, String tenantId, Date startTime, Date endTime);

    /**
     * 根据老师获取老师需要发送的通知内容
     *
     * @param staffFullInfoVOS
     * @param startTime
     * @param endTime
     * @param msgType
     * @return
     */
    List<StaffFullInfoVO> listTeacherInform(String schoolId, String campusId, String tenantId, ArrayList<StaffFullInfoVO> staffFullInfoVOS, Date startTime, Date endTime, Integer msgType);

    List<StaffFullInfoVO> listTeacherInformV2(String schoolId, String campusId, String tenantId, List<StaffFullInfoVO> staffFullInfoVOS, Date startTime, Date endTime, Integer msgType, List<BehaviourRecordDTO> behaviourRecordDTOList);

    /**
     * 学生点评覆盖率（新）
     *
     * @param dto
     * @return
     */
    DataStatisticsStudentEvaluateRateVO getStudentEvaluateStatistics(DataStatisticsQuery dto);

    List<StaffFullInfoVO> listTeacherInfoV2(String schoolId, String campusId, String tenantId);
}
