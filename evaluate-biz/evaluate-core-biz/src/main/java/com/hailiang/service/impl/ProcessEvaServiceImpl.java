package com.hailiang.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.constant.Constant;
import com.hailiang.entity.EvaluateBehaviourRecordOptExtPO;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.OperateTypeEnum;
import com.hailiang.enums.TaskRoleTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.manager.BehaviourHandleManager;
import com.hailiang.manager.DorisBehaviourRecordManager;
import com.hailiang.manager.EvaluateBehaviourRecordOptExtManager;
import com.hailiang.mapper.SubjectInfoMapper;
import com.hailiang.model.dto.query.GetEvaluateInfoDetailQueryDTO;
import com.hailiang.model.dto.query.QueryStudentListWithClassDTO;
import com.hailiang.model.dto.remove.RemoveSpeedRequest;
import com.hailiang.model.dto.target.TargetGroupInfoResponse;
import com.hailiang.model.processeva.entity.BehaviourIdInfoIdEntity;
import com.hailiang.model.processeva.entity.BehaviourRecordEntity;
import com.hailiang.model.processeva.entity.PageProcessRecordParam;
import com.hailiang.model.processeva.request.MoralSportDetailRequest;
import com.hailiang.model.processeva.request.ProcessEvaRecordIdsRequest;
import com.hailiang.model.processeva.request.ProcessEvaRecordRequest;
import com.hailiang.model.processeva.request.SpeedInfoDetailRequest;
import com.hailiang.model.processeva.response.ProcessEvaRecordResponse;
import com.hailiang.model.report.entity.SubjectInfoPO;
import com.hailiang.model.response.speed.SpeedRecordResponse;
import com.hailiang.model.response.speed.SpeedRecordStudentDetailResponse;
import com.hailiang.model.response.speed.SpeedRecordStudentResponse;
import com.hailiang.model.vo.GetEvaluateInfoDetailVO;
import com.hailiang.model.vo.ModifyHistoryVO;
import com.hailiang.model.vo.StudentWithClassInfoListVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.enums.OrgTypeEnum;
import com.hailiang.remote.saas.model.student.param.SaasStudentIdsParam;
import com.hailiang.remote.saas.model.student.vo.SaasStudentInfoVO;
import com.hailiang.remote.saas.vo.educational.EduClassInfoLinkVO;
import com.hailiang.remote.saas.vo.educational.GlobalEduOrgTreeVO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.InfoService;
import com.hailiang.service.ProcessEvaService;
import com.hailiang.service.SpeedInfoEditService;
import com.hailiang.service.TargetGroupService;
import com.hailiang.service.TargetService;
import com.hailiang.util.AssertUtil;
import com.hailiang.util.EasyExcelUtil;
import com.hailiang.util.PageUtils;
import com.hailiang.util.WebUtil;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

/**
 * @Description: 过程性评价
 * @Author: huyouting
 * @Date: Created in 2024-12-02
 * @Version: v2.1.0
 */
@Slf4j
@Service
public class ProcessEvaServiceImpl implements ProcessEvaService {

    @Resource
    private BehaviourHandleManager behaviourHandleManager;
    @Resource
    private DorisBehaviourRecordManager dorisBehaviourRecordManager;
    @Resource
    private BasicInfoService basicInfoService;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private TargetGroupService targetGroupService;
    @Resource
    private SpeedInfoEditService speedInfoEditService;
    @Resource
    private InfoService infoService;
    @Resource
    private TargetService targetService;
    @Resource
    private SubjectInfoMapper subjectInfoMapper;
    @Resource
    private EvaluateBehaviourRecordOptExtManager evaluateBehaviourRecordOptExtManager;

    /**
     * 分页查询过程性评价明细记录
     *
     * @param request
     * @return
     */
    @Override
    public Page<ProcessEvaRecordResponse> pageProcessEvaRecord(ProcessEvaRecordRequest request) {
        // 查询记录
        Page<BehaviourRecordEntity> behaviourRecordPage = this.commonPageProcessEvaRecordCondition(request);
        // 组装VO
        List<ProcessEvaRecordResponse> reportEvaluateRecords = this.assemblyResponse(behaviourRecordPage.getRecords());
        // 分页返回
        return PageUtils.copyPage(behaviourRecordPage, reportEvaluateRecords);
    }

    /**
     * 导出过程性评价明细记录
     *
     * @param data
     * @param response
     */
    @Override
    public void exportProcessEvaRecord(Page<ProcessEvaRecordResponse> data, HttpServletResponse response) {
        AssertUtil.checkIsTrue(data.getRecords().size() <= 50000, "单次最大支持导出50000条，请调整筛选分批次导出");
        // 导出excel
        this.exportProcessEvaRecordExcel(response, data.getRecords());
    }

    /**
     * 批量删除过程性评价明细记录
     *
     * @param request
     */
    @Override
    public void batchDeletedProcessEvaRecord(ProcessEvaRecordIdsRequest request) {
        AssertUtil.checkNotEmpty(request.getBehaviourRecordIds(), "删除id集合不能为空");
        List<BehaviourIdInfoIdEntity> behaviourIdInfos = behaviourHandleManager.listInfoIdByRecordIds(request.getBehaviourRecordIds());
        if (CollUtil.isEmpty(behaviourIdInfos)) {
            log.warn("【批量删除过程性评价明细记录】-【没有查询到对应的记录】-请求参数{}", JSONUtil.toJsonStr(request));
            return;
        }
        //体测项目指标校验
        List<Long> targetIdList = behaviourIdInfos
                .stream()
                .map(BehaviourIdInfoIdEntity::getTargetId).distinct().collect(Collectors.toList());
        Map<Long, Boolean> targetFromSportMap = targetService.checkTargetFromSport(targetIdList);

        if (behaviourIdInfos.size() == 1) {
            BehaviourIdInfoIdEntity behaviourIdInfo = CollUtil.getFirst(behaviourIdInfos);
            if (DataSourceEnum.SPORT.getCode().equals(behaviourIdInfo.getDataSource())){
                throw new BizException("体测数据不能删除");
            }
             if (Boolean.TRUE.equals(targetFromSportMap.get(behaviourIdInfo.getTargetId()))){
                 throw new BizException("体测数据不能删除");
             }
        }

        // 批量删除过滤提测数据
        List<BehaviourIdInfoIdEntity> collectResult = behaviourIdInfos
                .stream()
                .filter(s -> !DataSourceEnum.SPORT.getCode().equals(s.getDataSource()))
                .filter(item-> !Boolean.TRUE.equals(targetFromSportMap.get(item.getTargetId())))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(collectResult)) {
            log.warn("【批量删除过程性评价明细记录】-【没有查询到对应的记录】-请求参数{}", JSONUtil.toJsonStr(request));
            return;
        }
        // 删除德育活动、体测以外的行为记录
        this.deletedByInfoId(collectResult);
        // 删除德育活动、体测行为记录
        this.deletedMoralSportByIds(collectResult);

    }

    /**
     * 删除德育活动、体测行为记录
     *
     * @param behaviourIdInfos
     */
    private void deletedMoralSportByIds(List<BehaviourIdInfoIdEntity> behaviourIdInfos) {
        // 德育活动、体测没有infoId
        List<Long> moralSportIds = behaviourIdInfos.stream().filter(s -> CharSequenceUtil.isBlank(s.getInfoId())).map(BehaviourIdInfoIdEntity::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(moralSportIds)) {
            log.warn("【批量删除过程性评价明细记录】-【没有查询到对应的德育活动、体测记录】-请求参数{}", JSONUtil.toJsonStr(behaviourIdInfos));
            return;
        }
        RemoveSpeedRequest removeSpeedRequest = new RemoveSpeedRequest();
        removeSpeedRequest.setIds(moralSportIds);
        removeSpeedRequest.setInfoId("-1");
        // 删除记录
        speedInfoEditService.deleteSpeedByIds(removeSpeedRequest);
        log.info("【批量删除过程性评价明细记录】-【删除德育活动、体测行为记录】-请求参数{}", JSONUtil.toJsonStr(moralSportIds));
    }

    /**
     * 获取极速点评详情(单个学生)
     *
     * @param request
     * @return
     */
    @Override
    public GetEvaluateInfoDetailVO getSpeedInfoDetail(SpeedInfoDetailRequest request) {
        AssertUtil.checkNotBlank(request.getInfoId(), "infoId不能为空");
        AssertUtil.checkNotBlank(request.getStudentId(), "学生id不能为空");
        // 根据infoId查询极速点评详情
        GetEvaluateInfoDetailVO evaluateInfoDetail = infoService.getEvaluateInfoDetail(new GetEvaluateInfoDetailQueryDTO().setInfoId(request.getInfoId()).setDetailType(
                request.getDetailType()).setId(request.getId()));
        AssertUtil.checkNotNull(evaluateInfoDetail, "获取极速点评详情失败");
        // 过滤出符合查询条件的学生
        this.filterStudentInfo(evaluateInfoDetail, request.getStudentId());
        return evaluateInfoDetail;
    }

    /**
     * 获取德育活动、体测详情
     *
     * @param request
     * @return
     */
    @Override
    public GetEvaluateInfoDetailVO getMoralSportInfoDetail(MoralSportDetailRequest request) {
        AssertUtil.checkNotNull(request.getId(), "id不能为空");
        List<BehaviourRecordEntity> behaviourIdInfos = behaviourHandleManager.listBehaviourRecordByRecordIds(CollUtil.newArrayList(request.getId()));
        if (CollUtil.isEmpty(behaviourIdInfos)) {
            behaviourIdInfos = dorisBehaviourRecordManager.listBehaviourRecordByRecordIds(CollUtil.newArrayList(request.getId()));
        }
        AssertUtil.checkNotEmpty(behaviourIdInfos, "获取详情失败");
        BehaviourRecordEntity behaviourRecordEntity = CollUtil.getFirst(behaviourIdInfos);

        List<SaasStudentInfoVO> saasStudentInfos = basicInfoRemote.listSaasStudentInfoByStudentIds(new SaasStudentIdsParam().setStudentIds(CollUtil.newArrayList(Convert.toLong(behaviourRecordEntity.getStudentId()))));
        AssertUtil.checkNotEmpty(saasStudentInfos, "获取学生信息失败");
        SaasStudentInfoVO saasStudentInfoVO = CollUtil.getFirst(saasStudentInfos);

        GetEvaluateInfoDetailVO evaluateInfoDetailVO = new GetEvaluateInfoDetailVO();
        // 操作日志
        ModifyHistoryVO historyVO = new ModifyHistoryVO().setOperateDate(behaviourRecordEntity.getSubmitTime());
        historyVO.setOperateType(OperateTypeEnum.SUBMIT.getCode());
        historyVO.setRoleType(TaskRoleTypeEnum.TEACHER.getCode());
        historyVO.setOperatorName(behaviourRecordEntity.getAppraisalName());
        evaluateInfoDetailVO.setModifyHistory(CollUtil.newArrayList(historyVO));
        // 详情
        SpeedRecordStudentResponse speedRecordStudentResponse = new SpeedRecordStudentResponse();
        SpeedRecordResponse speedRecordResponse = new SpeedRecordResponse();
        SpeedRecordStudentDetailResponse speedRecordStudentDetailResponse = new SpeedRecordStudentDetailResponse();
        speedRecordStudentDetailResponse.setEvaluateId(behaviourRecordEntity.getId());
        speedRecordStudentDetailResponse.setOptionId(behaviourRecordEntity.getOptionId());
        speedRecordStudentDetailResponse.setInfoName(behaviourRecordEntity.getInfoName());
        speedRecordStudentDetailResponse.setInfoType(behaviourRecordEntity.getInfoType());
        speedRecordStudentDetailResponse.setScoreType(behaviourRecordEntity.getScoreType());
        speedRecordStudentDetailResponse.setScore(behaviourRecordEntity.getScore());
        speedRecordStudentDetailResponse.setDataSource(behaviourRecordEntity.getDataSource());
        speedRecordStudentDetailResponse.setSubmitTime(behaviourRecordEntity.getSubmitTime());
        speedRecordResponse.setGroupName("其他");
        speedRecordResponse.setSpeedRecordStudentDetailResponses(CollUtil.newArrayList(speedRecordStudentDetailResponse));
        speedRecordStudentResponse.setStudentId(behaviourRecordEntity.getStudentId());
        speedRecordStudentResponse.setStudentName(saasStudentInfoVO.getStudentBaseInfo().getStudentName());
        speedRecordStudentResponse.setSpeedRecordResponses(CollUtil.newArrayList(speedRecordResponse));
        evaluateInfoDetailVO.setSpeedRecordStudentResponseList(CollUtil.newArrayList(speedRecordStudentResponse));
        return evaluateInfoDetailVO;
    }

    /**
     * 获取极速点评详情(单个学生)
     *
     * @param evaluateInfoDetail
     * @param studentId
     */
    private void filterStudentInfo(GetEvaluateInfoDetailVO evaluateInfoDetail, String studentId) {
        if (CollUtil.isNotEmpty(evaluateInfoDetail.getSpeedRecordStudentResponseList())) {
            evaluateInfoDetail.setSpeedRecordStudentResponseList(evaluateInfoDetail.getSpeedRecordStudentResponseList().stream().filter(item -> item.getStudentId().equals(studentId)).collect(Collectors.toList()));
        }
    }

    /**
     * 删除德育活动、体测以外的行为记录
     *
     * @param behaviourIdInfos
     */
    private void deletedByInfoId(List<BehaviourIdInfoIdEntity> behaviourIdInfos) {
        AssertUtil.checkNotEmpty(behaviourIdInfos, "删除集合不能为空");
        Map<String, List<Long>> infoGroup = behaviourIdInfos.stream().filter(s -> CharSequenceUtil.isNotBlank(s.getInfoId())).collect(Collectors.groupingBy(BehaviourIdInfoIdEntity::getInfoId, Collectors.mapping(BehaviourIdInfoIdEntity::getId, Collectors.toList())));
        for (Map.Entry<String, List<Long>> entry : infoGroup.entrySet()) {
            RemoveSpeedRequest removeSpeedRequest = new RemoveSpeedRequest();
            removeSpeedRequest.setIds(entry.getValue());
            removeSpeedRequest.setInfoId(entry.getKey());
            // 删除记录
            speedInfoEditService.deleteSpeedByIds(removeSpeedRequest);
            log.info("【删除德育活动、体测以外的行为记录】,infoId:{},ids:{}", entry.getKey(), entry.getValue());
        }
    }

    /**
     * 公共查询条件
     *
     * @param request
     * @return
     */
    private Page<BehaviourRecordEntity> commonPageProcessEvaRecordCondition(ProcessEvaRecordRequest request) {
        // 查询记录
        return behaviourHandleManager.pageProcessEvaRecord(this.assemblyParam(request));
    }


    /**
     * 组装查询参数
     *
     * @param request
     * @return
     */
    private PageProcessRecordParam assemblyParam(ProcessEvaRecordRequest request) {
        AssertUtil.checkNotBlank(request.getSchoolYear(), "学校学年不能为空");
        AssertUtil.checkNotBlank(request.getTermName(), "学年学期不能为空");
        AssertUtil.checkNotBlank(request.getCampusSectionId(), "学段不能为空");
        AssertUtil.checkNotNull(request.getStartTime(), "开始时间不能为空");
        AssertUtil.checkNotNull(request.getEndTime(), "结束时间不能为空");
        request.setClassId("-1".equals(request.getClassId()) ? "" : request.getClassId());
        request.setGradeId("-1".equals(request.getGradeId()) ? "" : request.getGradeId());
        PageProcessRecordParam pageProcessRecord = new PageProcessRecordParam();
        // 非管理员角色时,查询任教班级权限(和列表数据权限保持一致)
        if (Boolean.FALSE.equals(basicInfoService.judgeCurrentUserAdminAuth())) {
            // 非管理员查询当前查询用户点评数据
            pageProcessRecord.setCurrentUserId(WebUtil.getStaffId());
            // 如果没有班级权限默认赋值-1
            pageProcessRecord.setDataClassIds(CollUtil.isNotEmpty(this.getClassAuth(request)) ? this.getClassAuth(request) : CollUtil.newArrayList("-1"));
        }
        // 当前校区下的数据权限
        pageProcessRecord.setCampusId(WebUtil.getCampusId());
        // 根据学生名称模糊查询学生
        pageProcessRecord.setStudentIds(this.likeStudentName(request));
        pageProcessRecord.setPageNum(request.getPageNum());
        pageProcessRecord.setPageSize(request.getPageSize());
        pageProcessRecord.setCampusSectionId(request.getCampusSectionId());
        pageProcessRecord.setGradeId(request.getGradeId());
        pageProcessRecord.setClassId(request.getClassId());
        pageProcessRecord.setStartTime(DateUtil.beginOfDay(request.getStartTime()));
        pageProcessRecord.setEndTime(DateUtil.endOfDay(request.getEndTime()));
        pageProcessRecord.setModuleCodes(request.getModuleCodes());
        pageProcessRecord.setTargetIds(request.getTargetIds());
        if(Objects.equals(request.getQueryType(), 2)){
            pageProcessRecord.setDataSources(DataSourceEnum.getCodesCardEvaluate());
        }
        if(Objects.equals(request.getQueryType(), 3)){
            pageProcessRecord.setDataSources(DataSourceEnum.getCodesNotCardEvaluate());
        }
        return pageProcessRecord;
    }

    /**
     * 根据学生姓名查询学生id集合
     *
     * @param request
     * @return
     */
    private List<String> likeStudentName(ProcessEvaRecordRequest request) {
        if (CharSequenceUtil.isBlank(request.getStudentName())) {
            return Collections.emptyList();
        }
        QueryStudentListWithClassDTO param = new QueryStudentListWithClassDTO();
        param.setCurrentId(request.getCampusSectionId());
        param.setStudentName(request.getStudentName());
        param.setCurrentIdType(OrgTypeEnum.SECTION.getSaasCode());
        List<StudentWithClassInfoListVO> sassStudentInfos = basicInfoService.queryStudentListWithClassInfo(param);
        List<String> studentIds = sassStudentInfos.stream().map(s -> Convert.toStr(s.getStudentId())).collect(Collectors.toList());
        // 如果查询不到学生，则返回-1
        if (CollUtil.isEmpty(studentIds)) {
            studentIds.add("-1");
        }
        return studentIds;
    }

    /**
     * 非管理员角色时,查询任教班级权限(和列表数据权限保持一致)
     *
     * @param request
     * @return
     */
    private List<String> getClassAuth(ProcessEvaRecordRequest request) {
        AssertUtil.checkNotBlank(request.getSchoolYear(), "学校学年不能为空");
        AssertUtil.checkNotBlank(request.getTermName(), "学年学期不能为空");
        AssertUtil.checkNotBlank(request.getCampusSectionId(), "学段不能为空");
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setSchoolYear(request.getSchoolYear());
        eduOrgQueryDTO.setTermName(request.getTermName());
        eduOrgQueryDTO.setCurrentId(Convert.toLong(request.getCampusSectionId()));
        eduOrgQueryDTO.setCurrentIdType(OrgTypeEnum.SECTION.getSaasCode());
        eduOrgQueryDTO.setEndType(OrgTypeEnum.CLASS.getSaasCode());
        eduOrgQueryDTO.setIsTree(Constant.NO);
        List<GlobalEduOrgTreeVO> globalEduOrgTreeInfos = basicInfoService.queryGlobalEducationalOrgTreeV2(eduOrgQueryDTO);
        if (CollUtil.isEmpty(globalEduOrgTreeInfos)) {
            log.warn("【过程性评价】-【获取组织信息失败】-【请求参数:{}】", JSONUtil.toJsonStr(eduOrgQueryDTO));
            return Collections.emptyList();
        }
        List<String> classIds = globalEduOrgTreeInfos.stream().filter(s -> Objects.equals(s.getType(), OrgTypeEnum.CLASS.getSaasCode())).map(s -> Convert.toStr(s.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(classIds)) {
            log.warn("【过程性评价】-【获取年级信息失败】-【请求参数:{}】", JSONUtil.toJsonStr(eduOrgQueryDTO));
            return Collections.emptyList();
        }
        return classIds;
    }

    /**
     * 组装返回参数
     *
     * @param records
     * @return
     */
    private List<ProcessEvaRecordResponse> assemblyResponse(List<BehaviourRecordEntity> records) {
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyList();
        }
        // 获取班级名称信息
        Map<Long, String> classNameMap = this.getClassNameMap(records);
        // 获取学生姓名信息
        Map<Long, String> studentNameMap = this.getStudentNameMap(records);
        // 获取指标分组名称信息
        Map<Long, String> targetGroupNameMap = this.getTargetGroupNameMap(records);
        //获取学科名称信息
        Map<String, String> subjectNameMap = this.getSubjectNameMap(records);
        //获取极速点评必填文本信息
        Map<Long, String> optExtMap = this.getOptExtMap(records);


        List<Long> targetIds = records
                .stream()
                .map(BehaviourRecordEntity::getTargetId).distinct()
                .collect(Collectors.toList());
        Map<Long, Boolean> targetFromSportMap = targetService.checkTargetFromSport(targetIds);

        // 德育活动
        List<Integer> moralEnums = CollUtil.newArrayList(DataSourceEnum.MORAL_CAMPUS.getCode(), DataSourceEnum.MORAL_OFF_CAMPUS.getCode());
        // 极速点评
        List<Integer> speedEnums = CollUtil.newArrayList(DataSourceEnum.EVALUATE_SPEED.getCode(), DataSourceEnum.INTERNAL_DRIVE_SPEED.getCode(), DataSourceEnum.EVALUATE_SPEED_FACE.getCode());

        // 组装返回参数
        return records.stream().map(record -> {
            ProcessEvaRecordResponse response = new ProcessEvaRecordResponse();
            response.setInfoId(record.getInfoId());
            response.setIsSportTarget(Boolean.TRUE.equals(targetFromSportMap.get(record.getTargetId())));
            Long behaviourRecordId = record.getId();
            response.setId(Convert.toStr(behaviourRecordId));
            response.setClassName(classNameMap.getOrDefault(Convert.toLong(record.getClassId()), "-"));
            response.setStudentId(record.getStudentId());
            response.setStudentName(studentNameMap.getOrDefault(Convert.toLong(record.getStudentId()), "-"));
            response.setTargetName(CharSequenceUtil.isNotBlank(record.getTargetName()) ? record.getTargetName() : "-");
            if (CharSequenceUtil.isBlank(record.getTargetName()) && DataSourceEnum.SPORT.getCode().equals(record.getDataSource())) {
                response.setTargetName("体测");
            }
            response.setOptionName(CharSequenceUtil.isNotBlank(record.getInfoName()) ? record.getInfoName() : "-");
            if (Boolean.FALSE.equals(record.getIsScore()) || Objects.isNull(record.getIsScore())) {
                response.setOptionName("-");
            }
            response.setScore(Objects.nonNull(record.getScore()) ? record.getScore().toPlainString() : "-");
            response.setTargetGroupName(targetGroupNameMap.getOrDefault(record.getTargetId(), "-"));
            response.setModuleCodeName(ModuleEnum.getModuleName(record.getModuleCode()));
            response.setSubmitTime(DateUtil.format(record.getSubmitTime(), DatePattern.NORM_DATE_PATTERN));
            response.setExcelSubmitTime(DateUtil.format(record.getSubmitTime(), DatePattern.NORM_DATETIME_PATTERN));
            response.setAppraisalName(CharSequenceUtil.isNotBlank(record.getAppraisalName()) ? record.getAppraisalName() : "-");
            response.setSpeedEvaFlag(speedEnums.contains(record.getDataSource()));
            response.setSubjectCode(record.getSubjectCode());
            response.setTextarea(optExtMap.getOrDefault(record.getId(),StrUtil.EMPTY));
            //学科编码
            if ("0".equals(record.getSubjectCode())){
                response.setSubjectName(Constant.HYPHEN);
            }else {
                List<String> subjectNameList = Arrays.stream(record.getSubjectCode().split(Constant.ENGLISH_COMMA))
                        .map(subjectNameMap::get)
                        .filter(Objects::nonNull).collect(Collectors.toList());
              String subjectName = CollUtil.isEmpty(subjectNameList)?Constant.HYPHEN:String.join(Constant.ENGLISH_COMMA,subjectNameList);
              response.setSubjectName(subjectName);
            }
            // 德育活动
            if (moralEnums.contains(record.getDataSource())) {
                response.setTargetGroupName("其他");
                response.setDetailType(1);
            }
            // 极速点评
            if (speedEnums.contains(record.getDataSource())) {
                response.setDetailType(2);
            }
            // 体测
            if (DataSourceEnum.SPORT.getCode().equals(record.getDataSource())) {
                response.setDetailType(3);
            }
            // 批量导入
            if (DataSourceEnum.BATCH_IMPORTS.getCode().equals(record.getDataSource())) {
                response.setDetailType(4);
            }
            // 积分卡点评
            if (DataSourceEnum.EVALUATE_POINT_CARD_RECYCLABLE.getCode().equals(record.getDataSource())
                    || DataSourceEnum.EVALUATE_POINT_CARD_DISPOSABLE.getCode().equals(record.getDataSource())) {
                response.setDetailType(5);
            }
            return response;
        }).collect(Collectors.toList());
    }

    private Map<Long, String> getOptExtMap(List<BehaviourRecordEntity> records) {
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyMap();
        }
        List<Long> speedIds = records.stream()
                .filter(item -> DataSourceEnum.getCodesSpeed().contains(item.getDataSource()))
                .map(BehaviourRecordEntity::getId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(speedIds)) {
            return Collections.emptyMap();
        }
        return CollStreamUtil.toMap(evaluateBehaviourRecordOptExtManager.byBehaviourRecordIds(speedIds),
                EvaluateBehaviourRecordOptExtPO::getBehaviourRecordId,
                item -> StrUtil.isEmpty(item.getTextarea()) ? StrUtil.EMPTY
                        : StrUtil.join("；",  JSON.parseArray(item.getTextarea(), String.class)));
    }

    private Map<String, String> getSubjectNameMap(List<BehaviourRecordEntity> records) {
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyMap();
        }

        List<String> subjectCodes = loopParseSubjectCodes(records);
        if (CollUtil.isEmpty(subjectCodes)) {
            return Collections.emptyMap();
        }

        BehaviourRecordEntity first = CollUtil.getFirst(records);

        List<SubjectInfoPO> subjectInfoPOS = subjectInfoMapper.listWithOutDeleted(first.getCampusId(),subjectCodes);
        if (CollUtil.isEmpty(subjectInfoPOS)){
            return Collections.emptyMap();
        }

        return subjectInfoPOS.stream()
                .collect(
                        Collectors.toMap(SubjectInfoPO::getSubjectCode, SubjectInfoPO::getSubjectName, (v1, v2) -> v1));

    }

    private static List<String> loopParseSubjectCodes(List<BehaviourRecordEntity> records) {
        List<String> subjectCodes = Lists.newArrayList();
        for (BehaviourRecordEntity record : records) {
            String subjectCode = record.getSubjectCode();
            if (StrUtil.isBlank(subjectCode) || "0".equals(subjectCode)) {
                continue;
            }
            subjectCodes.addAll(Arrays.asList(subjectCode.split(Constant.ENGLISH_COMMA)));
        }
        return subjectCodes;
    }

    /**
     * 查询海思谷班级名称
     *
     * @param records
     * @return
     */
    private Map<Long, String> getClassNameMap(List<BehaviourRecordEntity> records) {
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyMap();
        }
        List<Long> classIds = records.stream().map(s -> Convert.toLong(s.getClassId())).distinct().collect(Collectors.toList());
        List<EduClassInfoLinkVO> saasClassInfos = basicInfoRemote.listUnderByClassIds(classIds);
        return saasClassInfos.stream().collect(Collectors.toMap(EduClassInfoLinkVO::getId, EduClassInfoLinkVO::getClassName));
    }

    /**
     * 查询海思谷学生名称
     *
     * @param records
     * @return
     */
    private Map<Long, String> getStudentNameMap(List<BehaviourRecordEntity> records) {
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyMap();
        }
        Map<Long, String> studentNameMap = new HashMap<>();
        List<Long> studentIds = records.stream().map(s -> Convert.toLong(s.getStudentId())).distinct().collect(Collectors.toList());
        List<SaasStudentInfoVO> saasStudentInfos = basicInfoRemote.listSaasStudentInfoByStudentIds(new SaasStudentIdsParam().setStudentIds(studentIds));
        for (SaasStudentInfoVO saasStudentInfo : saasStudentInfos) {
            if (Objects.isNull(saasStudentInfo.getStudentBaseInfo())) {
                log.warn("【海思谷学生基础信息为空】-【信息:{}】", JSONUtil.toJsonStr(saasStudentInfo));
                continue;
            }
            if (Objects.isNull(saasStudentInfo.getStudentSchoolInfo())) {
                log.warn("【海思谷学生学校信息为空】-【信息:{}】", JSONUtil.toJsonStr(saasStudentInfo));
                continue;
            }
            if (Objects.isNull(saasStudentInfo.getStudentSchoolInfo().getStudentId())) {
                log.warn("【海思谷学生学校信息-学生id为空】-【信息:{}】", JSONUtil.toJsonStr(saasStudentInfo));
                continue;
            }
            studentNameMap.put(saasStudentInfo.getStudentSchoolInfo().getStudentId(), saasStudentInfo.getStudentBaseInfo().getStudentName());
        }
        return studentNameMap;
    }

    /**
     * 查询指标组名称
     *
     * @param records
     * @return
     */
    private Map<Long, String> getTargetGroupNameMap(List<BehaviourRecordEntity> records) {
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyMap();
        }
        List<Long> targetIds = records.stream().map(BehaviourRecordEntity::getTargetId).distinct().collect(Collectors.toList());
        List<TargetGroupInfoResponse> targetResponses = targetGroupService.listTargetGroupInfoByTargetIds(targetIds);
        log.info("根据指标id查询指标以及指标分组信息，入参:【{}】返回结果：【{}】", JSONUtil.toJsonStr(targetIds), JSONUtil.toJsonStr(targetResponses));
        if (CollUtil.isEmpty(targetResponses)) {
            log.warn("根据指标id查询指标以及指标分组信息，返回结果为空，入参：【{}】", JSONUtil.toJsonStr(targetIds));
            return Collections.emptyMap();
        }
        return targetResponses.stream().filter(s -> Objects.nonNull(s.getTargetId()) && Objects.nonNull(s.getGroupName())).collect(Collectors.toMap(TargetGroupInfoResponse::getTargetId, TargetGroupInfoResponse::getGroupName));
    }

    /**
     * 处理excel导出
     *
     * @param response
     * @param reportEvaluateRecords
     */
    private void exportProcessEvaRecordExcel(HttpServletResponse response, List<ProcessEvaRecordResponse> reportEvaluateRecords) {
        String fileName = "过程性评价明细" + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        try {
            EasyExcelUtil.writeExcel(response, reportEvaluateRecords, fileName, "sheet", ProcessEvaRecordResponse.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
