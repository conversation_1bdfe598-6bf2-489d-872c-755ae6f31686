package com.hailiang.service;

import com.hailiang.model.dto.request.evaluate.imports.ImportsEvaluateRequest;
import com.hailiang.model.dto.response.evaluate.imports.ImportsEvaluateResponse;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;

import java.util.List;

/**
 * 点评导入核心服务
 *
 * @Description: 点评导入核心服务
 * @Author: Jovi
 * @Date: Created in 2024-12-02
 * @Version: 2.0.0
 */
public interface ImportsEvaluateService {

    /**
     * 点评导入
     *
     * @param importsEvaluateRequest
     * @return
     */
    ImportsEvaluateResponse importsEvaluate(ImportsEvaluateRequest importsEvaluateRequest);

    /**
     * 根据指标ID获取指标信息，并过滤出常规类型的指标项
     *
     * @param targetId
     * @return
     */
    List<TemplateInfoSaveDTO> listNormalSubmitInfoByTargetId(Long targetId);
}
