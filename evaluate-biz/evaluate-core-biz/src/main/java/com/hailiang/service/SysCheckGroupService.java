package com.hailiang.service;

import com.hailiang.dto.DeleteDTO;
import com.hailiang.dto.SortDTO;
import com.hailiang.model.dto.CheckGroupDTO;
import com.hailiang.model.dto.SaveCheckGroupDTO;
import com.hailiang.model.dto.UpdateCheckGroupDTO;
import com.hailiang.model.vo.CheckDimVO;
import com.hailiang.model.vo.CheckGroupVO;

import java.util.List;

/**
 * 运营后台检查项分组
 *
 * @Description: 运营后台检查项分组
 * @Author: TanJian
 * @Date: Created in 2024-04-18
 * @Version: 1.6.0
 */
public interface SysCheckGroupService {

    /**
     * 查询检查维度
     * @return
     */
    List<CheckDimVO> queryDim();

    /**
     * 创建分组
     * @param dto
     */
    void createGroup(SaveCheckGroupDTO dto);

    /**
     * 编辑分组
     * @param dto
     */
    void editGroup(UpdateCheckGroupDTO dto);

    /**
     * 删除分组
     * @param dto
     */
    void deleteGroupIfItemDoesNotExist(DeleteDTO dto);

    /**
     * 查询分组列表
     * @param dto
     * @return
     */
    void sortGroup(SortDTO dto);

    /**
     * 根据检查维度查分组列表
     * @param dto
     * @return
     */
    List<CheckGroupVO> queryGroupsByDim(CheckGroupDTO dto);
}
