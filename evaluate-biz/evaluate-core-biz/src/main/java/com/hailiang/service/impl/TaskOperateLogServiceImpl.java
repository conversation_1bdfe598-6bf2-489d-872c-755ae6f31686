package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.constant.Constant;
import com.hailiang.convert.TaskOperateLogConvert;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.enums.TaskRoleTypeEnum;
import com.hailiang.logic.TaskOperateLogManager;
import com.hailiang.mapper.TaskOperateLogMapper;
import com.hailiang.mapper.mongo.InfoDao;
import com.hailiang.model.dto.save.TaskOperateLogSaveDTO;
import com.hailiang.model.entity.TaskOperateLog;
import com.hailiang.model.entity.mongo.Info;
import com.hailiang.model.entity.mongo.SubmitInfo;
import com.hailiang.service.TaskOperateLogService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TaskOperateLogServiceImpl extends ServiceImpl<TaskOperateLogMapper, TaskOperateLog> implements TaskOperateLogService {

    @Resource
    private TaskOperateLogMapper mapper;
    @Resource
    private TaskOperateLogManager logLogic;
    @Resource
    private TaskOperateLogConvert convert;
    @Resource
    private InfoDao infoDao;
    @Resource
    private TaskOperateLogManager taskOperateLogManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEvaluateTaskOperateLog(TaskOperateLogSaveDTO dto) {
        TaskOperateLog log = convert.toLog(dto);
        return logLogic.save(log);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEvaluateTaskOperateLog_Playback(TaskOperateLogSaveDTO dto,Date date) {
        TaskOperateLog log = convert.toLog(dto);
        log.setCreateTime(date);
        return logLogic.save(log);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEvaluateTaskOperateLogs(List<TaskOperateLogSaveDTO> dtos) {
        List<TaskOperateLog> logs = convert.toLogs(dtos);
        return logLogic.saveBatch(logs);
    }

    @Override
    public boolean checkBatchReviewTimesAWeek() {
        LambdaQueryWrapper<TaskOperateLog> wrapper = new LambdaQueryWrapper<>();
        DateTime now = DateUtil.date();
        // 获取一周前的时间
        DateTime lastWeek = DateUtil.lastWeek();
        wrapper.eq(TaskOperateLog::getOperatorId, WebUtil.getStaffId());
        wrapper.gt(TaskOperateLog::getOperateTime, lastWeek);
        wrapper.lt(TaskOperateLog::getOperateTime, now);
        wrapper.eq(TaskOperateLog::getRoleType, TaskRoleTypeEnum.TEACHER.getCode());
        wrapper.eq(TaskOperateLog::getCampusId, WebUtil.getCampusId());
        wrapper.orderByDesc(TaskOperateLog::getOperateTime);
        List<TaskOperateLog> taskOperateLogs = this.list(wrapper);
        if (CollUtil.isEmpty(taskOperateLogs)) {
            return false;
        }

        TaskOperateLog taskOperateLog = taskOperateLogs.get(0);
        Map<String, TaskOperateLog> infoMap = taskOperateLogs.stream().collect(Collectors.toMap(TaskOperateLog::getInfoId, Function.identity(), (k1, k2) -> k1));
        Map<Date, List<TaskOperateLog>> operateTimeMap = taskOperateLogs.stream().collect(Collectors.groupingBy(TaskOperateLog::getOperateTime));
        // 本次提交不是批量提交或前3次提交则不用判断区分度
        if (Constant.ZERO.equals(taskOperateLog.getIsBatchFlag()) || operateTimeMap.keySet().size() <= 3) {
            return false;
        }

        // 查询指标评价提交模版mongo表id关联表单数据
        List<String> infoIds = taskOperateLogs.stream().map(TaskOperateLog::getInfoId).collect(Collectors.toList());
        Query query = new Query();
        query.addCriteria(Criteria.where("id").in(infoIds)).addCriteria(Criteria.where("deleted").is(0));
        List<Info> infos = infoDao.find(query);

        int count = 0;
        int round = 1;
        try {
            // 组装点评内容对应的map
            Map<Date, List<String>> timeToOptionMap = new HashMap<>();
            this.buildOptionMap(infos, timeToOptionMap, infoMap);
            log.info("【点评时判断点评区分度】timeToOptionMap:{}", JSONUtil.toJsonStr(timeToOptionMap));
            if (CollUtil.isEmpty(timeToOptionMap)) {
                return false;
            }

            // 本次点评是批量点评
            count++;
            int total = operateTimeMap.size();
            round = Math.round((float) total / 2);
            List<String> originList = timeToOptionMap.get(taskOperateLog.getOperateTime());
            if (CollUtil.isEmpty(originList)) {
                return false;
            }
            originList = originList.stream().distinct().collect(Collectors.toList());
            CollUtil.sort(originList, String::compareTo);

            // 判断近一周批量提交相同点评项次数是否大于总点评次数的50%
            for (Map.Entry<Date, List<String>> entry : timeToOptionMap.entrySet()) {
                List<TaskOperateLog> operateLogs = operateTimeMap.get(entry.getKey());
                if (!Objects.equals(taskOperateLog.getOperateTime(), entry.getKey()) && CollUtil.isNotEmpty(operateLogs)) {
                    TaskOperateLog taskOperateLog1 = operateLogs.stream().filter(operateLog -> Objects.equals(Constant.ZERO, operateLog.getIsBatchFlag())).findFirst().orElse(null);

                    List<String> targetList = timeToOptionMap.get(entry.getKey());
                    if (CollUtil.isEmpty(targetList)) {
                        continue;
                    }

                    targetList = targetList.stream().distinct().collect(Collectors.toList());
                    CollUtil.sort(targetList, String::compareTo);
                    if (CollUtil.isEqualList(targetList, originList) && Objects.isNull(taskOperateLog1)) {
                        count++;
                    }
                }
            }
        } catch (Exception e) {
            log.error("提交点评时判断点评区分度出错，报错信息:{}", e.getMessage(), e);
        }

        return count >= round;
    }

    @Override
    public boolean checkBatchReviewTimesAWeekV2() {

        int count = 0;
        int round = 1;

        try {
            DateTime now = DateUtil.date();
            // 获取一周前的时间
            DateTime lastWeek = DateUtil.lastWeek();
            String staffId = WebUtil.getStaffId();
            Integer roleType = TaskRoleTypeEnum.TEACHER.getCode();
            String campusId = WebUtil.getCampusId();
            List<TaskOperateLog> taskOperateLogs = taskOperateLogManager.listTaskOperateLogByCampusIdAndStartTimeAndEndTimeAndOperateId(
                    campusId,
                    lastWeek,
                    now,
                    staffId,
                    roleType
            );
            if (CollUtil.isEmpty(taskOperateLogs)) {
                return false;
            }

            TaskOperateLog taskOperateLog = taskOperateLogs.get(0);
            Map<Date, List<TaskOperateLog>> operateTimeMap = taskOperateLogs.stream().collect(Collectors.groupingBy(TaskOperateLog::getOperateTime));
            // 本次提交不是批量提交或前3次提交则不用判断区分度
            if (Constant.ZERO.equals(taskOperateLog.getIsBatchFlag()) || operateTimeMap.keySet().size() <= 3) {
                return false;
            }

            // 判断批量提交次数
            round = Math.round((float) operateTimeMap.size() / 2);
            for (Map.Entry<Date, List<TaskOperateLog>> dateListEntry : operateTimeMap.entrySet()) {
                List<TaskOperateLog> value = dateListEntry.getValue();
                List<TaskOperateLog> operateLogs = value.stream().filter(item -> Objects.equals(item.getIsBatchFlag(), Constant.ZERO)).collect(Collectors.toList());
                if (CollUtil.isEmpty(operateLogs)) {
                    count++;
                }
            }

        } catch (Exception e) {
            log.error("提交点评时判断点评区分度出错，报错信息:{}", e.getMessage(), e);
        }
        return count >= round;
    }

    /**
     * 获取点评关联内容
     *
     * @param infos
     * @param timeToOptionMap
     * @param infoMap
     */
    private void buildOptionMap(List<Info> infos, Map<Date, List<String>> timeToOptionMap, Map<String, TaskOperateLog> infoMap) {
        log.info("构建点评关联内容,buildOptionMap,infoMap:{},infos:{}", JSONUtil.toJsonStr(infoMap), JSONUtil.toJsonStr(infos));
        // 获取操作记录对应的选项
        infos.forEach(item -> {
            List<SubmitInfo> submitInfoList = new ArrayList<>(item.getSubmitInfoList());
            TaskOperateLog operateLog = infoMap.get(item.getId());
            if (CollUtil.isNotEmpty(submitInfoList) && Objects.nonNull(operateLog)) {
                for (SubmitInfo ele : submitInfoList) {
                    if (Objects.equals(ele.getType(), SubmitInfoTypeEnum.SINGLE_CHECK.getText())
                            || Objects.equals(ele.getType(), SubmitInfoTypeEnum.MULTI_CHECK.getText())
                            || Objects.equals(ele.getType(), SubmitInfoTypeEnum.DETAIL.getText())) {
                        // 明细
                        if (CollUtil.isNotEmpty(ele.getSubmitList())) {
                            List<List<LinkedHashMap>> submitList = ele.getSubmitList();
                            submitList.forEach(submit -> submit.forEach(sub -> {
                                Object object = sub.get("submitValue");
                                if (object instanceof List) {
                                    List<Object> list = (List<Object>) object;
                                    list.forEach(e -> {
                                        if (e instanceof Map) {
                                            JSONObject jsonObject = JSONUtil.parseObj(e);
                                            String key = (String) jsonObject.get("key");
                                            if (CharSequenceUtil.isNotBlank(key)) {
                                                timeToOptionMap.put(operateLog.getOperateTime(), timeToOptionMap.getOrDefault(operateLog.getOperateTime(), new ArrayList<>()));
                                                timeToOptionMap.get(operateLog.getOperateTime()).add(key);
                                            }
                                        }
                                    });
                                } else if (object instanceof Map) {
                                    JSONObject jsonObject = JSONUtil.parseObj(object);
                                    String key = (String) jsonObject.get("key");
                                    if (CharSequenceUtil.isNotBlank(key)) {
                                        timeToOptionMap.put(operateLog.getOperateTime(), timeToOptionMap.getOrDefault(operateLog.getOperateTime(), new ArrayList<>()));
                                        timeToOptionMap.get(operateLog.getOperateTime()).add(key);
                                    }
                                }
                            }));
                            continue;
                        }
                        Object submitValue = ele.getSubmitValue();
                        if (Objects.isNull(submitValue)) {
                            continue;
                        }
                        if (submitValue instanceof List) {
                            JSONArray jsonArray = JSONUtil.parseArray(submitValue);
                            for (Object object : jsonArray) {
                                JSONObject jsonObject = JSONUtil.parseObj(object);
                                String key = (String) jsonObject.get("key");
                                if (CharSequenceUtil.isNotBlank(key)) {
                                    timeToOptionMap.put(operateLog.getOperateTime(), timeToOptionMap.getOrDefault(operateLog.getOperateTime(), new ArrayList<>()));
                                    timeToOptionMap.get(operateLog.getOperateTime()).add(key);
                                }
                            }
                        } else if (submitValue instanceof Map) {
                            JSONObject jsonObject = JSONUtil.parseObj(submitValue);
                            String key = (String) jsonObject.get("key");
                            if (CharSequenceUtil.isNotBlank(key)) {
                                timeToOptionMap.put(operateLog.getOperateTime(), timeToOptionMap.getOrDefault(operateLog.getOperateTime(), new ArrayList<>()));
                                timeToOptionMap.get(operateLog.getOperateTime()).add(key);
                            }
                        }
                        // 分值类型
                    } else if (Objects.equals(ele.getType(), SubmitInfoTypeEnum.SCORE.getText())) {
                        String key = ele.getKey();
                        timeToOptionMap.put(operateLog.getOperateTime(), timeToOptionMap.getOrDefault(operateLog.getOperateTime(), new ArrayList<>()));
                        timeToOptionMap.get(operateLog.getOperateTime()).add(key);
                    }
                }
            }
        });
    }
}
