package com.hailiang.service.impl;

import static com.hailiang.enums.DataSourceEnum.EVALUATE_SPEED;
import static com.hailiang.enums.DataSourceEnum.EVALUATE_SPEED_FACE;
import static com.hailiang.enums.InfoTypeEnum.SPEED_OPTION_SCHOOL_SCHOOL;
import static com.hailiang.enums.InfoTypeEnum.SPEED_OPTION_SCHOOL_TEACHER;
import static com.hailiang.enums.InfoTypeEnum.SPEED_OPTION_TEACHER_TEACHER;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hailiang.annotation.RateLimit;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.entity.EvaluateBehaviourRecordExtPO;
import com.hailiang.entity.EvaluateBehaviourRecordOptExtPO;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.EventCodeEnum;
import com.hailiang.enums.FaceImageTypeEnum;
import com.hailiang.enums.RevaluateBusinessTypeEnum;
import com.hailiang.enums.SaasClassTypeEnum;
import com.hailiang.enums.StudentInfoTypeEnum;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.enums.SubmitRateEnum;
import com.hailiang.enums.SysSwitchConfigEnum;
import com.hailiang.enums.TaskRoleTypeEnum;
import com.hailiang.enums.motivate.CoinExchangeOperateTypeEnum;
import com.hailiang.enums.motivate.CoinExchangeStrategyEnum;
import com.hailiang.enums.speed.SpeedApplyLevelEnum;
import com.hailiang.exception.BizException;
import com.hailiang.helper.EvaluateReminderHandle;
import com.hailiang.helper.SaasClassHelper;
import com.hailiang.internal.InternalDriveRemote;
import com.hailiang.internal.model.request.ComputeRecordCommentRequest;
import com.hailiang.internal.model.request.ComputeRecordRequest;
import com.hailiang.internal.model.request.ComputeRecordStudentRequest;
import com.hailiang.internal.model.request.PlanListsRequest;
import com.hailiang.internal.model.response.InternalDrivePointRecordResponse;
import com.hailiang.internal.model.response.PlanListsResponse;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.logic.PointsSendLogic;
import com.hailiang.logic.TargetLogic;
import com.hailiang.logic.TaskOperateLogManager;
import com.hailiang.logic.TermLogic;
import com.hailiang.manager.DorisBehaviourRecordManager;
import com.hailiang.manager.EvaluateBehaviourRecordExtManager;
import com.hailiang.manager.SysSwitchConfigManager;
import com.hailiang.manager.TargetManager;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.dto.QueryClassSubjectRelDTO;
import com.hailiang.model.dto.internaldrive.merge.ThirdToXdlRecordMessageDTO;
import com.hailiang.model.dto.remove.RemoveSpeedRequest;
import com.hailiang.model.dto.save.BehaviourRecordHandleDTO;
import com.hailiang.model.dto.save.InfoSpeedBasicDTO;
import com.hailiang.model.dto.save.InfoSpeedOptionRequest;
import com.hailiang.model.dto.save.InfoSpeedRequest;
import com.hailiang.model.dto.save.InfoSpeedStudentRequest;
import com.hailiang.model.dto.save.SubjectRelDTO;
import com.hailiang.model.dto.save.XxbInfoSpeedDeleteRequest;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.EvaluateHelpBehaviourRecordPO;
import com.hailiang.model.entity.SysSwitchConfigPO;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TaskOperateLog;
import com.hailiang.model.entity.ThirdDataInfoPO;
import com.hailiang.model.entity.mongo.SubStudentInfo;
import com.hailiang.remote.internaldrive.EventSendHelper;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.dto.educational.EduStudentClassQueryDTO;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.pojo.educational.EduClassBaseInfoPojo;
import com.hailiang.remote.saas.vo.educational.EduStaffSubjectVO;
import com.hailiang.remote.saas.vo.educational.EduStaffTeachClassVO;
import com.hailiang.remote.saas.vo.educational.EduStudentClassVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.dto.FaceDetectRequest;
import com.hailiang.saas.model.dto.FaceV2DTO;
import com.hailiang.saas.model.dto.StudentDTO;
import com.hailiang.saas.model.vo.StudentInfoVO;
import com.hailiang.saas.model.vo.face.FaceDetailVO;
import com.hailiang.saas.model.vo.face.FaceDetectResultVO;
import com.hailiang.saas.model.vo.face.StudentFaceResultItemVO;
import com.hailiang.saas.model.vo.face.StudentFaceResultVO;
import com.hailiang.service.BehaviourRecordService;
import com.hailiang.service.ConvertInitialService;
import com.hailiang.service.InfoService;
import com.hailiang.service.SpeedInfoEditService;
import com.hailiang.service.SpeedInfoSaveService;
import com.hailiang.service.TargetGroupService;
import com.hailiang.service.ThirdDataInfoService;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

/**
 * 极速点评编辑服务
 *
 * @Description: 极速点评编辑服务
 * @Author: JiangJunLi
 * @Date: Created in 2024-01-26
 * @Version: 1.6.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SpeedInfoEditServiceImpl implements SpeedInfoEditService {

    private final InfoService evaluateInfoService;
    private final BehaviourRecordService behaviourRecordService;
    private final InternalDriveRemote internalDriveRemote;
    private final TaskOperateLogManager taskOperateLogManager;
    private final ConvertInitialService convertInitialService;
    private final RedisUtil redisUtil;
    private final CacheSaasManager cacheSaasManager;
    private final EventSendHelper eventSendHelper;
    private final SaasStudentManager saasStudentManager;
    private final SysSwitchConfigManager sysSwitchConfigManager;
    private final EvaluateBehaviourRecordExtManager evaluateBehaviourRecordExtManager;
    private final ThirdDataInfoService thirdDataInfoService;
    private final ThreadPoolTaskExecutor evaluateExecutor;
    private final InfoService infoService;
    private final TermLogic termLogic;
    private final TargetGroupService targetGroupService;
    private final SpeedInfoSaveService speedInfoSaveService;
    private final BehaviourRecordManager behaviourRecordManager;
    private final SaasClassHelper saasClassHelper;
    private final EvaluateReminderHandle evaluateReminderHandle;
    private final TargetLogic targetLogic;
    private final TargetManager targetManager;
    private final DorisBehaviourRecordManager dorisBehaviourRecordManager;
    private final BasicInfoRemote basicInfoRemote;
    private final PointsSendLogic pointsSendLogic;

    private static final Integer BATCH_SIZE = 400;



    @Value("${third.sendEvent.xwlAddRecord}")
    private String xwlAddRecord;
    @Value("${third.sendEvent.xwlDelRecord}")
    private String xwlDelRecord;


    @Override
    public BehaviourRecordHandleDTO speedSaveTeacherEvaluateInfoV2(InfoSpeedRequest request) {

        if (Objects.isNull(request)) {
            log.warn("极速点评入参为空");
            throw new BizException("极速点评参数错误，请稍后重试");
        }

        log.info("保存极速点评,request:{}", JSONUtil.toJsonStr(request));

        Date submitDate = new Date();

        BehaviourRecordHandleDTO behaviourRecordHandleDTO = new BehaviourRecordHandleDTO();

        InfoSpeedBasicDTO speedBasicDTO = buildSpeedBaiscDTO(request);

        //1、校验入参【星动力】-积分板正确性
        dealXdlPlan(request);

        //2、极速点评点击一次提交产生的多条行为记录，根据此字段归属为同一批次提交
        String infoId = SnowFlakeIdUtil.nextIdStr();

        // 点评分类记录
        List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordExtPOS = new ArrayList<>();
        // 点评必填多文本框
        List<EvaluateBehaviourRecordOptExtPO> behaviourRecordOptExtPOS = new ArrayList<>();

        List<BehaviourRecord> behaviourRecordPOList = new ArrayList<>();
        // 校验指标提交频次 如果有提交过 就过滤
        handleRestrictedStudents(request, behaviourRecordHandleDTO);
        if (CollUtil.isEmpty(request.getInfoSpeedStudentRequests())){
            return behaviourRecordHandleDTO;
        }

        // 第三方映射表（目前仅供星学伴数据处理）
        List<ThirdDataInfoPO> thirdDataInfoPOList = new ArrayList<>();
        
        //3、组装行为记录实体
        this.buildSpeedBehaviorRecords(
                infoId,
                submitDate,
                speedBasicDTO,
                behaviourRecordPOList,
                request,
                evaluateBehaviourRecordExtPOS,
                behaviourRecordOptExtPOS,
                thirdDataInfoPOList);


        if (CollUtil.isEmpty(behaviourRecordPOList)) {
            log.warn("组装极速点评行为记录实体为空，直接返回");
            return null;
        }

        //4、点评关联学科
        this.fillSubjectCode(
                behaviourRecordPOList,
                request.getClassId(),
                speedBasicDTO,
                request.getSubjectRelRequest());


        //5、师徒帮扶数据
        List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS = new ArrayList<>();

        if (CharSequenceUtil.isNotBlank(request.getPlanId())) {

            if (Objects.equals(request.getPlanType(), 3)) {
                // 师徒帮扶
                try {
                    this.buildComputeRecord(
                            speedBasicDTO,
                            request,
                            behaviourRecordPOList,
                            evaluateBehaviourRecordExtPOS,
                            helpBehaviourRecordPOS);
                } catch (Exception e) {
                    log.error("请求星动力，获取点评师徒帮扶数据出错，报错信息：【{}】", e.getMessage(), e);
                    throw new BizException("点评获取帮扶分数出错");
                }
            }

            sendMqToXDL(request, behaviourRecordPOList, evaluateBehaviourRecordExtPOS, helpBehaviourRecordPOS);
        }


        // 处理点评记录，过滤掉既在行政班又在走班的走班数据，并将只在走班的班级 id 转为行政班班级 id
        this.filterRecords(behaviourRecordPOList);

        String classId = request.getClassId();
        if (CollUtil.isNotEmpty(helpBehaviourRecordPOS)) {
            helpBehaviourRecordPOS.forEach(item -> item.setClassId(classId));
        }

        if (CollUtil.isNotEmpty(behaviourRecordPOList) && behaviourRecordPOList.stream().anyMatch(item -> Objects.isNull(item.getCampusSectionId()))) {
            log.error("组转极速点评数据获取学段id异常");
            throw new BizException("系统异常。请稍后重试");
        }

        


        TimeInterval TIME_INTERVAL = DateUtil.timer();

        saasClassHelper.checkAndHandleClassForRecord(classId, behaviourRecordPOList);
        saasClassHelper.checkAndHandleClassForHelpRecord(classId, helpBehaviourRecordPOS);

        log.info("提交极速点评, behaviourRecords:{}", JSONUtil.toJsonStr(behaviourRecordPOList));
        log.info("提交极速点评, helpBehaviourRecordPOS:{}", JSONUtil.toJsonStr(helpBehaviourRecordPOS));
        log.info("提交极速点评, evaluateBehaviourRecordClassifyPOS:{}", JSONUtil.toJsonStr(evaluateBehaviourRecordExtPOS));

        // 记录点评日志
        TaskOperateLog taskOperateLog = this.buildTaskOperateLog(request);
        taskOperateLog.setInfoId(infoId);

//        evaluateExecutor.execute(() -> speedInfoSaveService.saveSpeed(behaviourRecordPOList, helpBehaviourRecordPOS, evaluateBehaviourRecordExtPOS, taskOperateLog));
        speedInfoSaveService.saveSpeed(behaviourRecordPOList, helpBehaviourRecordPOS, evaluateBehaviourRecordExtPOS,
                taskOperateLog, behaviourRecordOptExtPOS, thirdDataInfoPOList);

        // 发送到积分系统
        pointsSendLogic.convertAndSendPoints(behaviourRecordPOList);

        log.warn("【H5】-【极速点评】-【保存点评记录、帮扶记录、分类记录、日志】，【耗时：{}】", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 新增点评项发送mq
        Integer pointSwitch = targetGroupService.listPointSwitch(WebUtil.getCampusId());
        if (Objects.equals(pointSwitch, 1)) {

            List<List<BehaviourRecord>> recodeBatchList = Lists.partition(behaviourRecordPOList, BATCH_SIZE);
            for (List<BehaviourRecord> behaviourRecords : recodeBatchList) {
                convertInitialService.sendMQ(1, 1, behaviourRecords, null);
                // 发送积分金币转换mq
                convertInitialService.sendBehaviourExchangeMq(
                        infoService.assemblyPointExchangeMq(behaviourRecords, Convert.toLong(request.getPlanId()),
                                CoinExchangeOperateTypeEnum.CREATE.getCode(), CoinExchangeStrategyEnum.SPEED_COMMENT.getCode()));
            }

        } else {
            List<BehaviourRecord> toPointRecords = behaviourRecordPOList
                    .stream()
                    .filter(item -> Objects.equals(item.getInfoType(), SPEED_OPTION_SCHOOL_SCHOOL.getCode()))
                    .collect(Collectors.toList());

            List<List<BehaviourRecord>> toPointBatchRecordList = Lists.partition(toPointRecords, BATCH_SIZE);
            for (List<BehaviourRecord> toPointRecordList : toPointBatchRecordList) {
                convertInitialService.sendMQ(1, 1, toPointRecordList, null);
                // 发送积分金币转换mq
                convertInitialService.sendBehaviourExchangeMq(
                        infoService.assemblyPointExchangeMq(toPointRecordList, Convert.toLong(request.getPlanId()),
                                CoinExchangeOperateTypeEnum.CREATE.getCode(), CoinExchangeStrategyEnum.SPEED_COMMENT.getCode()));
            }
        }


        // 点评项增删改打标
        Set<String> sectionSet = new HashSet<>();
        // 新增的年级
        if (CollUtil.isNotEmpty(behaviourRecordPOList)) {
            this.addGradeRedis(behaviourRecordPOList, sectionSet);
        }
        // 新增和删除的学段
        if (CollUtil.isNotEmpty(sectionSet)) {
            this.addSectionRedis(sectionSet);
        }

        // 新增的行为记录

        List<Long> behaviourIds = behaviourRecordPOList
                .stream()
                .map(BehaviourRecord::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        behaviourRecordHandleDTO.setAddBehaviourRecordIds(behaviourIds);


        saveOptionsOrTargetToRedis(submitDate, speedBasicDTO, behaviourRecordPOList);

        // 异步发送消息 班主任提醒消息
        if (CollUtil.isNotEmpty(behaviourRecordPOList)) {
            Set<Long> targetIds = CollStreamUtil.toSet(behaviourRecordPOList, BehaviourRecord::getTargetId);

            Map<Long, Target> targetMap = CollStreamUtil.toIdentityMap(
                    targetLogic.listByIds(new ArrayList<>(targetIds)), Target::getId);
            CollStreamUtil.groupByKey(behaviourRecordPOList, BehaviourRecord::getTargetId)
                    .forEach((targetId, records) -> {
                        Target target = targetMap.get(targetId);
                        if (Objects.isNull(target)) {
                            log.warn("【极速点评】-【保存点评剩余流程】-【获取target为空】targetId:{}", targetId);
                            return;
                        }
                        evaluateReminderHandle.teacherNoticeSend(target, records, classId,
                                speedBasicDTO.getStaffName(), "/evaluate-fast-detail");
                    });

        }

        log.warn("【H5】-【极速点评】-【保存点评剩余流程】，【耗时：{}】", TIME_INTERVAL.intervalMs());

        return behaviourRecordHandleDTO;
    }

    private void handleRestrictedStudents(InfoSpeedRequest request, BehaviourRecordHandleDTO behaviourRecordHandleDTO) {
        List<BehaviourRecordHandleDTO.StudentDTO> restrictedStudentList = checkTargetSubmissionFrequency(request);
        behaviourRecordHandleDTO.setRestrictedStudentList(restrictedStudentList);

        List<String> restrictedStudentIdList = restrictedStudentList
                .stream()
                .map(BehaviourRecordHandleDTO.StudentDTO::getStudentId)
                .distinct()
                .collect(Collectors.toList());

        //过滤掉异常的学生
        request.setInfoSpeedStudentRequests(request.getInfoSpeedStudentRequests()
                .stream()
                .filter(item -> !restrictedStudentIdList.contains(item.getStudentId()))
                .collect(Collectors.toList()));
    }

    private List<BehaviourRecordHandleDTO.StudentDTO> checkTargetSubmissionFrequency(InfoSpeedRequest request) {
        boolean needCheck = false;
        //回填指标提交类型
        fillTargetSubmitType(request);
        // 先看下 有没有按照学期去过滤的 没有就提前返回 不要查学期、doris了
        for (InfoSpeedStudentRequest infoSpeedStudentRequest : request.getInfoSpeedStudentRequests()) {
            List<InfoSpeedOptionRequest> needLimitedOptionRequest = infoSpeedStudentRequest.getSpeedOptions()
                    .stream()
                    .filter(item -> SubmitRateEnum.TERM.getSubmitType().equals(item.getTargetSubmitType()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(needLimitedOptionRequest)) {
                needCheck = true;
            }
        }
        if (!needCheck) {
            return Collections.emptyList();
        }
        //查询学期信息
        TermVo currentTermVo = getCurrentTermVo(request);

        //查询学生当前学期的提交记录（数据来自Doris）
        Map<String, List<BehaviourRecordDTO>> studentBehaviourRecordMap = listBehaviourRecord(request, currentTermVo);
        //比对出已经提交过的指标关联的学生
        return diffSubmittedTarget(request, studentBehaviourRecordMap);
    }

    private List<BehaviourRecordHandleDTO.StudentDTO> diffSubmittedTarget(InfoSpeedRequest request,
                                                                          Map<String, List<BehaviourRecordDTO>> studentBehaviourRecordMap) {
        String classId = request.getClassId();

        List<BehaviourRecordHandleDTO.StudentDTO> restrictedStudentList = new ArrayList<>();

        for (InfoSpeedStudentRequest infoSpeedStudentRequest : request.getInfoSpeedStudentRequests()) {
            String studentId = infoSpeedStudentRequest.getStudentId();
            String studentName = infoSpeedStudentRequest.getName();

            BehaviourRecordHandleDTO.StudentDTO studentDTO = new BehaviourRecordHandleDTO.StudentDTO();
            studentDTO.setStudentId(studentId);
            studentDTO.setStudentName(studentName);

            List<InfoSpeedOptionRequest> needLimitedOptionRequest = infoSpeedStudentRequest.getSpeedOptions()
                    .stream()
                    .filter(item -> SubmitRateEnum.TERM.getSubmitType().equals(item.getTargetSubmitType()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(needLimitedOptionRequest)) {
                continue;
            }

            List<Long> distinctTargetIdList = needLimitedOptionRequest
                    .stream()
                    .map(InfoSpeedOptionRequest::getTargetId).distinct().collect(Collectors.toList());

            List<BehaviourRecordDTO> studentBehaviourRecordList = studentBehaviourRecordMap.get(studentId);
            if (CollUtil.isEmpty(studentBehaviourRecordList)) {
                continue;
            }

            Map<Long, List<BehaviourRecordDTO>> targetRecordMap = studentBehaviourRecordList
                    .stream()
                    .filter(item -> Objects.equals(item.getClassId(), classId))
                    .collect(Collectors.groupingBy(BehaviourRecordDTO::getTargetId));

            for (Long targetId : distinctTargetIdList) {
                //已经提交过当前指标
                List<BehaviourRecordDTO> behaviourRecordDTOS = targetRecordMap.get(targetId);
                if (CollUtil.isNotEmpty(behaviourRecordDTOS)) {
                    restrictedStudentList.add(studentDTO);
                    break;
                }
            }
        }
        return restrictedStudentList;
    }

    private Map<String, List<BehaviourRecordDTO>> listBehaviourRecord(InfoSpeedRequest request, TermVo currentTermVo) {
        List<BehaviourRecordDTO> behaviourRecordList = dorisBehaviourRecordManager.getRecord(
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                null,
                null,
                null,
                DateUtil.beginOfDay(DateUtil.parseDate(currentTermVo.getStartTime())),
                DateUtil.endOfDay(DateUtil.parseDate(currentTermVo.getEndTime())),
                null,
                null,
                request.getInfoSpeedStudentRequests()
                        .stream()
                        .map(InfoSpeedStudentRequest::getStudentId)
                        .distinct()
                        .collect(Collectors.toList()));
        if (CollUtil.isEmpty(behaviourRecordList)) {
            behaviourRecordList = Lists.newArrayList();
        }

        return behaviourRecordList
                .stream()
                .collect(Collectors.groupingBy(BehaviourRecordDTO::getStudentId));
    }

    private TermVo getCurrentTermVo(InfoSpeedRequest request) {
        TermQuery termQuery = new TermQuery();
        termQuery.setCampusSectionId(request.getCampusSectionId());
        termQuery.setCampusId(WebUtil.getCampusIdLong());
        termQuery.setSchoolId(WebUtil.getSchoolIdLong());
        List<TermVo> termVoList = basicInfoRemote.queryTermList(termQuery);
        Assert.notEmpty(termVoList, "海思谷学期信息为空!");
        TermVo currentTermVo = termVoList
                .stream()
                .filter(TermVo::isCurrentTerm)
                .findFirst().orElse(null);
        Assert.notNull(currentTermVo, "当前学期为空!");
        return currentTermVo;
    }

    private void fillTargetSubmitType(InfoSpeedRequest request) {
        //过滤掉星动力的指标
        List<Long> evaluateTargetIds = request.getInfoSpeedStudentRequests()
                .stream()
                .map(InfoSpeedStudentRequest::getSpeedOptions)
                .flatMap(Collection::stream)
                .filter(item -> SpeedApplyLevelEnum.SCHOOL.getCode().equals(item.getApplyLevel()))
                .map(InfoSpeedOptionRequest::getTargetId)
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(evaluateTargetIds)){
            return;
        }

        //查询指标类型
        List<Target> targets = targetManager.listByIds(evaluateTargetIds);
        Assert.notEmpty(targets, "指标不存在！");

        Map<Long, Target> targetMap = targets
                .stream()
                .collect(Collectors.toMap(Target::getId, Function.identity()));
        //回填指标提交限制类型
        for (InfoSpeedStudentRequest infoSpeedStudentRequest : request.getInfoSpeedStudentRequests()) {
            for (InfoSpeedOptionRequest speedOption : infoSpeedStudentRequest.getSpeedOptions()) {
                Long targetId = speedOption.getTargetId();
                Target target = targetMap.get(targetId);
                if (ObjectUtil.isNull(target)) {
                    continue;
                }
                speedOption.setTargetSubmitType(target.getSubmitType());
            }
        }
    }

    /**
     * 保存此次点评提交的点评项和指标到Redis，供给后续的查看老师经常点评使用
     *
     * @param submitDate
     * @param speedBasicDTO
     * @param behaviourRecordPOList
     */
    private void saveOptionsOrTargetToRedis(Date submitDate, InfoSpeedBasicDTO speedBasicDTO, List<BehaviourRecord> behaviourRecordPOList) {
        String submitDateStr = DateUtil.formatDateTime(submitDate);
        String staffOftenEvaluateOptionsKey = StrUtil.format(RedisKeyConstants.STAFF_OFTEN_EVALUTAE_OPTIONS, speedBasicDTO.getStaffId());
        behaviourRecordPOList.forEach(item -> {
            if (StringUtils.isNotBlank(item.getOptionId())) {
                redisUtil.put(staffOftenEvaluateOptionsKey, item.getOptionId(), submitDateStr);
            } else {
                redisUtil.put(staffOftenEvaluateOptionsKey, Convert.toStr(item.getTargetId()), submitDateStr);
            }
        });
    }

    /**
     * 处理需要发送给星动力的数据
     *
     * @param request
     * @param behaviourRecordPOList
     * @param evaluateBehaviourRecordExtPOS
     * @param helpBehaviourRecordPOS
     */
    private void sendMqToXDL(InfoSpeedRequest request, List<BehaviourRecord> behaviourRecordPOList, List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordExtPOS, List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS) {
        List<BehaviourRecord> behaviourRecords1 = behaviourRecordPOList
                .stream()
                .filter(item -> Objects.nonNull(item.getScore()))
                .collect(Collectors.toList());


        Map<Long, EvaluateBehaviourRecordExtPO> longEvaluateBehaviourRecordClassifyPOMap = evaluateBehaviourRecordExtPOS
                .stream()
                .collect(Collectors.toMap(EvaluateBehaviourRecordExtPO::getBehaviourRecordId, Function.identity()));

        if (CollUtil.isNotEmpty(behaviourRecords1)) {
            List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS = this.convertThirdToXdlRecordMessageDTO(behaviourRecords1,
                    Convert.toLong(request.getPlanId()),
                    longEvaluateBehaviourRecordClassifyPOMap);
            // 师徒帮扶
            this.convertHelpThirdToXdlRecordMessageDTO(Convert.toLong(request.getPlanId()),
                    thirdToXdlRecordMessageDTOS,
                    helpBehaviourRecordPOS
            );

            log.info("同步星动力消息内容,thirdToXdlRecordMessageDTOS:{}", JSONUtil.toJsonStr(thirdToXdlRecordMessageDTOS));

            eventSendHelper.sendEventMQ(thirdToXdlRecordMessageDTOS, xwlAddRecord);
        }
    }

    /**
     * 构建极速点评基本信息
     *
     * @param request
     * @return
     */
    private InfoSpeedBasicDTO buildSpeedBaiscDTO(InfoSpeedRequest request) {
        InfoSpeedBasicDTO basicInfo = new InfoSpeedBasicDTO();
        basicInfo.setStaffId(WebUtil.getStaffId());
        basicInfo.setStaffName(WebUtil.getStaffName());
        basicInfo.setTenantId(WebUtil.getTenantId());
        basicInfo.setCampusId(WebUtil.getCampusId());
        basicInfo.setSchoolId(WebUtil.getSchoolId());
        basicInfo.setCampusSectionId(request.getCampusSectionId());
        basicInfo.setCampusSectionCode(request.getCampusSectionCode());
        basicInfo.setGradeId(request.getGradeId());
        basicInfo.setGradeCode(request.getGradeCode());
        return basicInfo;
    }

    /**
     * 处理星动力积分板相关信息
     *
     * @param request
     */
    private void dealXdlPlan(InfoSpeedRequest request) {

        if (StringUtils.isBlank(request.getPlanId())) {
            log.warn("当前积分板ID不存在，不需要特殊处理积分板相关信息");
            return;
        }

        TimeInterval TIME_INTERVAL = DateUtil.timer();

        PlanListsRequest planTagListsRequest = new PlanListsRequest();
        planTagListsRequest.setStaffId(WebUtil.getStaffIdLong());
        planTagListsRequest.setSaasClassIds(Collections.singletonList(Convert.toLong(request.getClassId())));
        planTagListsRequest.setSaasSchoolId(WebUtil.getSchoolIdLong());
        List<PlanListsResponse> planListsResponses = internalDriveRemote.queryPlanLists(planTagListsRequest);

        List<PlanListsResponse> filterPlanList = planListsResponses
                .stream()
                .filter(item -> Objects.equals(item.getPlanId().toString(), request.getPlanId()))
                .collect(Collectors.toList());

        log.warn("【H5】-【极速点评】-【调用星动力接口获取积分板信息】，【耗时：{}】", TIME_INTERVAL.intervalMs());

        if (CollUtil.isEmpty(filterPlanList)) {
            log.warn("当前老师所选积分板不存在");
            throw new BizException("当前积分板不存在，请重新选择积分板");
        }

    }


    /**
     * 组装极速点评行为记录
     *
     * @Description:
     * @Author: Jovi
     * @Date: Created in 2024/10/18
     * @Version: 2.0.0
     */
    private void buildSpeedBehaviorRecords(String infoId,
            Date submitDate,
            InfoSpeedBasicDTO basicInfo,
            List<BehaviourRecord> behaviourRecordPOs,
            InfoSpeedRequest request,
            List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordExtPOS,
            List<EvaluateBehaviourRecordOptExtPO> behaviourRecordOptExtPOS,
            List<ThirdDataInfoPO> thirdDataInfoPOList) {

        TimeInterval TIME_INTERVAL = DateUtil.timer();


        // 学生相关信息入参
        List<InfoSpeedStudentRequest> infoSpeedStudentRequests = request.getInfoSpeedStudentRequests();
        if (CollUtil.isEmpty(infoSpeedStudentRequests)) {
            return;
        }

        // 入参：班级ID
        String classId = request.getClassId();
        // 入参：是否人脸识别
        Integer faceFlag = request.getFaceFlag();

        for (InfoSpeedStudentRequest infoSpeedStudentRequest : infoSpeedStudentRequests) {


            List<InfoSpeedOptionRequest> speedOptions = infoSpeedStudentRequest.getSpeedOptions();

            for (InfoSpeedOptionRequest speedOption : speedOptions) {
                dealSingleOption(
                        infoId,
                        submitDate,
                        basicInfo,
                        behaviourRecordPOs,
                        evaluateBehaviourRecordExtPOS,
                        infoSpeedStudentRequest,
                        speedOption,
                        faceFlag,
                        classId,
                        behaviourRecordOptExtPOS,
                        thirdDataInfoPOList);
            }
        }


        behaviourRecordPOs.forEach(behaviourRecord -> {
            if (ObjectUtil.isNull(behaviourRecord.getModuleCode())) {
                behaviourRecord.setModuleCode(0);
            }
        });

        log.warn("【H5】-【极速点评】-【组装极速点评行为记录】，【耗时：{}】", TIME_INTERVAL.intervalMs());
    }

    /**
     * 处理单个点评项逻辑
     *
     * @param infoId
     * @param behaviourRecordPOs
     * @param evaluateBehaviourRecordExtPOS
     * @param infoSpeedStudentRequest
     * @param speedOption
     * @param faceFlag
     * @param classId
     * @param behaviourRecordOptExtPOS
     */
    private void dealSingleOption(String infoId,
            Date submitDate,
            InfoSpeedBasicDTO basicInfo,
            List<BehaviourRecord> behaviourRecordPOs,
            List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordExtPOS,
            InfoSpeedStudentRequest infoSpeedStudentRequest,
            InfoSpeedOptionRequest speedOption,
            Integer faceFlag,
            String classId,
            List<EvaluateBehaviourRecordOptExtPO> behaviourRecordOptExtPOS,
            List<ThirdDataInfoPO> thirdDataInfoPOList) {

        //分值控件
        if (Objects.equals(speedOption.getType(), SubmitInfoTypeEnum.SCORE.getText())) {

            BehaviourRecord behaviourRecord = this.buildBehaviourRecordScore(
                    speedOption,
                    submitDate,
                    basicInfo,
                    faceFlag);

            evaluateBehaviourRecordExtPOS.add(
                    this.buildEvaluateBehaviourRecordExtPO(speedOption, behaviourRecord)
            );
            behaviourRecord.setStudentId(infoSpeedStudentRequest.getStudentId());
            behaviourRecord.setClassId(classId);
            behaviourRecord.setInfoId(infoId);
            behaviourRecordPOs.add(behaviourRecord);
            behaviourRecordOptExtPOS.add(this.buildBehaviourRecordOptExtPO(speedOption, behaviourRecord.getId()));
            return;
        }

        //多选或者是师级点评项
        if (Objects.equals(speedOption.getType(), SubmitInfoTypeEnum.MULTI_CHECK.getText())
                || Objects.equals(speedOption.getApplyLevel(), SpeedApplyLevelEnum.GENERAL.getCode())) {


            List<LinkedHashMap> submitValues = (List<LinkedHashMap>) speedOption.getSubmitValue();

            for (LinkedHashMap submitValue : submitValues) {
                buildSingleSubmitValue(
                        infoId,
                        submitDate,
                        basicInfo,
                        behaviourRecordPOs,
                        evaluateBehaviourRecordExtPOS,
                        infoSpeedStudentRequest,
                        speedOption,
                        faceFlag,
                        classId,
                        submitValue,
                        behaviourRecordOptExtPOS ,
                        thirdDataInfoPOList);
            }

            return;
        }

        //单选或者是校级点评项
        LinkedHashMap submitValue = (LinkedHashMap) speedOption.getSubmitValue();

        buildSingleSubmitValue(
                infoId,
                submitDate,
                basicInfo,
                behaviourRecordPOs,
                evaluateBehaviourRecordExtPOS,
                infoSpeedStudentRequest,
                speedOption,
                faceFlag,
                classId,
                submitValue,
                behaviourRecordOptExtPOS,
                thirdDataInfoPOList);
    }

    /**
     * 构建行为记录选项扩展表数据
     * @param speedOption
     * @param behaviourRecordId
     * @return
     */
    private EvaluateBehaviourRecordOptExtPO buildBehaviourRecordOptExtPO(InfoSpeedOptionRequest speedOption,
            Long behaviourRecordId) {
        if (CollUtil.isEmpty(speedOption.getTextareaList())) {
            return null;
        }
        EvaluateBehaviourRecordOptExtPO optExtPO = new EvaluateBehaviourRecordOptExtPO();
        optExtPO.setBehaviourRecordId(behaviourRecordId);
        optExtPO.setTextarea(JSON.toJSONString(speedOption.getTextareaList()));
        return optExtPO;
    }


    /**
     * 构建单个提交值
     *
     * @param infoId
     * @param submitDate
     * @param basicInfo
     * @param behaviourRecordPOs
     * @param evaluateBehaviourRecordExtPOS
     * @param infoSpeedStudentRequest
     * @param speedOption
     * @param faceFlag
     * @param classId
     * @param submitValue
     * @param behaviourRecordOptExtPOS
     */
    private void buildSingleSubmitValue(String infoId,
            Date submitDate,
            InfoSpeedBasicDTO basicInfo,
            List<BehaviourRecord> behaviourRecordPOs,
            List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordExtPOS,
            InfoSpeedStudentRequest infoSpeedStudentRequest,
            InfoSpeedOptionRequest speedOption,
            Integer faceFlag,
            String classId,
            LinkedHashMap submitValue,
            List<EvaluateBehaviourRecordOptExtPO> behaviourRecordOptExtPOS,
            List<ThirdDataInfoPO> thirdDataInfoPOList) {
        BehaviourRecord behaviourRecord = this.buildBehaviourRecord(
                speedOption,
                basicInfo,
                submitDate,
                submitValue,
                faceFlag);

        if(Objects.nonNull(speedOption.getThirdDataId())){
            ThirdDataInfoPO thirdDataInfoPO = new ThirdDataInfoPO();
            thirdDataInfoPO.setId(SnowFlakeIdUtil.nextId());
            thirdDataInfoPO.setSourceDataInfo(JSONUtil.toJsonStr(speedOption));
            thirdDataInfoPO.setOperateFlag(1);
            thirdDataInfoPO.setDataSource(DataSourceEnum.XXB_EVALUATE_RECORD_ADD.getCode());
            thirdDataInfoPO.setDisposeFlag(1);
            thirdDataInfoPO.setDataType(EventCodeEnum.XXB_BEHAVIOUR_ADD_RECORD.getDataType());
            thirdDataInfoPO.setThirdRecordId(speedOption.getThirdDataId().toString());
            thirdDataInfoPO.setCreateBy("system");
            thirdDataInfoPO.setLocalRecordId(behaviourRecord.getId());
            thirdDataInfoPOList.add(thirdDataInfoPO);
        }

        evaluateBehaviourRecordExtPOS.add(buildEvaluateBehaviourRecordExtPO(speedOption, behaviourRecord));

        behaviourRecord.setStudentId(infoSpeedStudentRequest.getStudentId());
        behaviourRecord.setClassId(classId);
        behaviourRecord.setInfoId(infoId);
        behaviourRecordPOs.add(behaviourRecord);
        behaviourRecordOptExtPOS.add(this.buildBehaviourRecordOptExtPO(speedOption, behaviourRecord.getId()));
    }

    /**
     * 点评关联学科
     */
    private void fillSubjectCode(List<BehaviourRecord> behaviourRecordPOs,
                                 String classId,
                                 InfoSpeedBasicDTO speedBasicDTO,
                                 SubjectRelDTO subjectRelRequest) {

        TimeInterval TIME_INTERVAL = DateUtil.timer();


        //获取关联学科
        QueryClassSubjectRelDTO query = new QueryClassSubjectRelDTO();

        SubStudentInfo subStudentInfo = new SubStudentInfo();
        subStudentInfo.setId(classId);
        subStudentInfo.setClassId(classId);
        subStudentInfo.setType(StudentInfoTypeEnum.CLASS.getCode());

        query.setSubmitValue(Collections.singletonList(subStudentInfo));


        List<EduStaffTeachClassVO> relatedSubjectFromSaasList = evaluateInfoService.getRelatedSubject(query,
                speedBasicDTO.getSchoolId(),
                speedBasicDTO.getStaffId());


        //没有关联学科，赋值默认学科直接返回 END
        if (CollUtil.isEmpty(relatedSubjectFromSaasList)) {

            for (BehaviourRecord record : behaviourRecordPOs) {
                if (Objects.nonNull(subjectRelRequest)) {
                    String defaultSubjectCode = subjectRelRequest.getDefaultSubjectCode();
                    record.setSubjectCode(CharSequenceUtil.isBlank(defaultSubjectCode) ? "0" : defaultSubjectCode);
                }
            }
            return;
        }


        Map<Long/*classId*/, EduStaffTeachClassVO> saasSubjectMapByClassIdLong = relatedSubjectFromSaasList
                .stream()
                .collect(Collectors.toMap(EduStaffTeachClassVO::getClassId, Function.identity()));


        dealBehaviourRecordSubjectLogic(behaviourRecordPOs, classId, saasSubjectMapByClassIdLong, subjectRelRequest);

        log.warn("【H5】-【极速点评】-【回填学科信息】，【耗时：{}】", TIME_INTERVAL.intervalMs());
    }

    /**
     * 处理极速点评行为记录的学科信息
     *
     * @param behaviourRecordPOs
     * @param classId
     * @param saasSubjectRelMapByClassId
     */
    private void dealBehaviourRecordSubjectLogic(List<BehaviourRecord> behaviourRecordPOs,
                                                 String classId,
                                                 Map<Long, EduStaffTeachClassVO> saasSubjectRelMapByClassId,
                                                 SubjectRelDTO subjectRelRequest) {

        for (BehaviourRecord record : behaviourRecordPOs) {
            dealSingleBehaviourRecordSubjectLogic(classId, saasSubjectRelMapByClassId, subjectRelRequest, record);
        }

    }

    /**
     * 处理单个行为记录的学科信息
     *
     * @param classId
     * @param saasSubjectRelMapByClassIdLong
     * @param subjectRelRequest
     * @param record
     */
    private void dealSingleBehaviourRecordSubjectLogic(String classId,
                                                       Map<Long, EduStaffTeachClassVO> saasSubjectRelMapByClassIdLong,
                                                       SubjectRelDTO subjectRelRequest,
                                                       BehaviourRecord record) {

        Long classIdLong = Long.valueOf(classId);

        EduStaffTeachClassVO teachClassVO = saasSubjectRelMapByClassIdLong.get(classIdLong);
        if (ObjectUtil.isNull(teachClassVO)) {
            return;
        }

        List<EduStaffSubjectVO> subjects = teachClassVO.getSubjects();
        if (CollUtil.isEmpty(subjects)) {
            return;
        }

        EduStaffSubjectVO eduSubjectVO = subjects.stream().findFirst().orElse(null);
        if (ObjectUtil.isNull(eduSubjectVO)) {
            return;
        }

        //对应班级只任教一门学科
        if (subjects.size() == 1) {
            record.setSubjectCode(eduSubjectVO.getSubjectCode()); // NOSONAR
        } else {

            //对应班级任教多门学科需要用户前端选择

            if (ObjectUtil.isNull(subjectRelRequest) ||
                    (ObjectUtil.isNull(subjectRelRequest.getDefaultSubjectCode()) && ObjectUtil.isNull(subjectRelRequest.getSubjectCode()))) {
                throw new BizException("该班级未选择学科，请重新尝试选择学科");
            }

            //需要选择对应指标包含该点评行为记录指标，则直接赋值选择的学科
            List<String> needChooseTargets = subjectRelRequest.getNeedChooseTargets();
            if (CollUtil.isNotEmpty(needChooseTargets) && needChooseTargets.contains(Convert.toStr(record.getTargetId()))) {
                record.setSubjectCode(subjectRelRequest.getSubjectCode());
            }

            //无需选择对应指标包含该点评行为记录指标，则直接赋值默认学科
            List<String> noNeedChooseTargets = subjectRelRequest.getNoNeedChooseTargets();
            if (CollUtil.isNotEmpty(noNeedChooseTargets) && noNeedChooseTargets.contains(Convert.toStr(record.getTargetId()))) {
                if (StringUtils.isNotBlank(subjectRelRequest.getSubjectCode())) {
                    record.setSubjectCode(subjectRelRequest.getSubjectCode());
                } else {
                    record.setSubjectCode(subjectRelRequest.getDefaultSubjectCode());
                }
            }

            //点评的点评项如果全部都是师级点评项，则直接赋值默认学科，改V2版本后，按理说不应该再有都为空的时候了
            if (CollUtil.isEmpty(noNeedChooseTargets) && CollUtil.isEmpty(needChooseTargets)) {
                record.setSubjectCode(subjectRelRequest.getDefaultSubjectCode());
            }
        }
    }

    /**
     * 处理点评记录，过滤掉既在行政班又在走班的走班数据，并将只在走班的班级 id 转为行政班班级 id
     *
     * @param records
     */
    private void filterRecords(List<BehaviourRecord> records) {

        TimeInterval TIME_INTERVAL = DateUtil.timer();


        // 根据学生获取行政班级
        List<String> studentIds = records.stream().map(BehaviourRecord::getStudentId).distinct().collect(Collectors.toList());
        List<EduStudentClassVO> eduStudentClassVOList = listEduStudentClassVOS(studentIds);
        if (CollUtil.isNotEmpty(eduStudentClassVOList)) {
            //班级
            List<EduClassBaseInfoPojo> eduClassList = eduStudentClassVOList.stream().map(EduStudentClassVO::getClassBaseInfos).collect(Collectors.toList()).stream().flatMap(Collection::stream).collect(Collectors.toList());
            //行政班 id
            List<Long> classIds = eduClassList.stream().filter(item -> SaasClassTypeEnum.XINGZHENG.getCode().equals(item.getClassType())).map(EduClassBaseInfoPojo::getId).collect(Collectors.toList());
            //过滤既在行政班又在走班的走班数据，并将只在走班的班级 id 转为行政班班级 id
            fillRecords(records, classIds, eduStudentClassVOList);
        }

        log.warn("【H5】-【极速点评】-【处理点评数据-走班转行政班】，【耗时：{}】", TIME_INTERVAL.intervalMs());
    }

    private List<EduStudentClassVO> listEduStudentClassVOS(List<String> studentIdList) {
        if (CollUtil.isEmpty(studentIdList)) {
            return Collections.emptyList();
        }
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        List<String> classTypes = new ArrayList<>();
        classTypes.add(SaasClassTypeEnum.XINGZHENG.getCode());
        eduStudentClassQueryDTO.setClassTypes(classTypes);
        eduStudentClassQueryDTO.setStudentIds(Convert.toList(Long.class, studentIdList));
        eduStudentClassQueryDTO.setGraduationStatus("0");
        List<EduStudentClassVO> eduStudentClassVOList = cacheSaasManager.queryClassListByStudentIds(eduStudentClassQueryDTO);
        return eduStudentClassVOList;
    }

    /**
     * 过滤既在行政班又在走班的走班数据，并将只在走班的班级 id 转为行政班班级 id
     *
     * @param records
     * @param classIds
     * @param eduStudentClassVOList
     */
    private void fillRecords(List<BehaviourRecord> records, List<Long> classIds, List<EduStudentClassVO> eduStudentClassVOList) {
        //按学生 id 分组
        List<List<BehaviourRecord>> repeatBehaviourRecordListByStudentId = records.stream()
                .collect(Collectors.groupingBy(BehaviourRecord::getStudentId))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        //按学生 id 和班级 id 分组,筛选出有行政班和走班的行为记录
        List<List<BehaviourRecord>> repeatBehaviourRecordList = new ArrayList<>();
        for (List<BehaviourRecord> behaviourRecords : repeatBehaviourRecordListByStudentId) {
            int size = behaviourRecords.stream()
                    .collect(Collectors.groupingBy(p -> Pair.of(p.getStudentId(), p.getClassId())))
                    .size();
            if (Constant.ONE < size) {
                repeatBehaviourRecordList.addAll(behaviourRecords.stream()
                        .collect(Collectors.groupingBy(p -> Pair.of(p.getStudentId(), p.getClassId())))
                        .entrySet().stream()
                        .map(Map.Entry::getValue)
                        .collect(Collectors.toList()));
            }
        }

        ArrayList<SubStudentInfo> studentInfos = new ArrayList<>();
        repeatBehaviourRecordList.forEach(s -> {
            if (CollUtil.isNotEmpty(s)) {
                SubStudentInfo student = new SubStudentInfo();
                student.setId(s.get(0).getStudentId());
                student.setClassId(s.get(0).getClassId());
                studentInfos.add(student);
            }
        });
        //既在行政班又在走班的学生
        List<SubStudentInfo> repeatStudentInfos = studentInfos.stream()
                .collect(Collectors.groupingBy(SubStudentInfo::getId))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getValue)
                .collect(Collectors.toList()).stream().flatMap(Collection::stream).collect(Collectors.toList());
        //既在行政班又在走班的学生过滤出走班学生
        List<SubStudentInfo> zouBanStudents = repeatStudentInfos.stream().filter(entry -> !classIds.contains(Convert.toLong(entry.getClassId()))).collect(Collectors.toList());

        List<BehaviourRecord> zouBanRecords = new ArrayList<>();
        //既在行政班又在走班的学生的走班行为数据
        zouBanStudents.forEach(s -> {
            zouBanRecords.addAll(records.stream().filter(item -> item.getStudentId().equals(s.getId()) && item.getClassId().equals(s.getClassId())).collect(Collectors.toList()));
        });
        //过滤既在行政班又在走班的学生的走班行为数据
        records.removeAll(zouBanRecords);

        //将走班 id 转成行政班 id
        records.forEach(item -> {
            Optional<EduStudentClassVO> first = eduStudentClassVOList.stream().filter(s -> item.getStudentId().equals(Convert.toStr(s.getStudentId()))).findFirst();
            first.ifPresent(eduStudentClassVO -> item.setClassId(Convert.toStr(eduStudentClassVO.getClassBaseInfos().get(0).getId())));
        });
    }

    /**
     * 组装极速点评（单选、多选）
     *
     * @param speedOption
     * @param submitValue
     * @param faceFlag
     * @return
     */
    private BehaviourRecord buildBehaviourRecord(InfoSpeedOptionRequest speedOption,
                                                 InfoSpeedBasicDTO basicInfo,
                                                 Date submitDate,
                                                 LinkedHashMap submitValue,
                                                 Integer faceFlag) {
        if (Objects.isNull(speedOption) || Objects.isNull(speedOption.getSubmitValue())) {
            return new BehaviourRecord();
        }

        Object value = submitValue.get("value");
        BigDecimal score = Objects.isNull(value) ? null : new BigDecimal(value.toString());
        BehaviourRecord behaviourRecordPO = buildBehaviourRecordBasicCommonInfo(basicInfo, score);


        // 师标
        if (Objects.equals(speedOption.getApplyLevel(), SpeedApplyLevelEnum.GENERAL.getCode())) {
            behaviourRecordPO.setTargetId(Convert.toLong(speedOption.getSpeedGroupId()));
            behaviourRecordPO.setTargetName(speedOption.getSpeedGroupName());
            // 师标默认参与统计
            behaviourRecordPO.setNotPartCount(0);
        } else {
            behaviourRecordPO.setTargetId(speedOption.getTargetId());
            behaviourRecordPO.setTargetName(speedOption.getTargetName());
            behaviourRecordPO.setNotPartCount(speedOption.getTargetNotPartCount());
        }

        behaviourRecordPO.setOptionId(Objects.isNull(submitValue.get("key")) ? "" : submitValue.get("key").toString());
        behaviourRecordPO.setIsScore(true);
        Integer scoreType = Objects.isNull(score) ? null : score.compareTo(new BigDecimal(0)) >= 0 ? 1 : 2;
        behaviourRecordPO.setScoreType(scoreType);

        Integer infoType = Objects.equals(speedOption.getApplyLevelGroup(),
                SpeedApplyLevelEnum.GENERAL.getCode()) ?
                SPEED_OPTION_TEACHER_TEACHER.getCode() : Objects.equals(speedOption.getApplyLevel(), SpeedApplyLevelEnum.GENERAL.getCode()) ?
                SPEED_OPTION_SCHOOL_TEACHER.getCode() : SPEED_OPTION_SCHOOL_SCHOOL.getCode();

        behaviourRecordPO.setInfoType(infoType);
        behaviourRecordPO.setInfoName(Objects.isNull(submitValue.get("label")) ? "" : Convert.toStr(submitValue.get("label")));
        behaviourRecordPO.setSubmitTime(submitDate);
        behaviourRecordPO.setTaskId(null);
        behaviourRecordPO.setTitleId(speedOption.getKey());
        behaviourRecordPO.setModuleCode(speedOption.getModuleCode());
        behaviourRecordPO.setDataSource(Constant.YES.equals(faceFlag) ? EVALUATE_SPEED_FACE.getCode() : EVALUATE_SPEED.getCode());

        return behaviourRecordPO;
    }

    /**
     * 组装极速点评公共信息（分值、多选、单选）
     *
     * @param basicInfo
     * @param score
     * @return
     */
    private BehaviourRecord buildBehaviourRecordBasicCommonInfo(InfoSpeedBasicDTO basicInfo, BigDecimal score) {

        BehaviourRecord behaviourRecordPO = new BehaviourRecord();
        behaviourRecordPO.setScore(score);
        behaviourRecordPO.setScoreValue(score == null ? null : score.abs());
        behaviourRecordPO.setAppraisalId(basicInfo.getStaffId());
        behaviourRecordPO.setAppraisalName(basicInfo.getStaffName());
        behaviourRecordPO.setCreateBy(basicInfo.getStaffId());
        behaviourRecordPO.setAppraisalType(TaskRoleTypeEnum.TEACHER.getCode());
        behaviourRecordPO.setTemplateId("");
        behaviourRecordPO.setTenantId(basicInfo.getTenantId());
        behaviourRecordPO.setSchoolId(basicInfo.getSchoolId());
        behaviourRecordPO.setCampusId(basicInfo.getCampusId());
        behaviourRecordPO.setCampusSectionId(basicInfo.getCampusSectionId());
        behaviourRecordPO.setCampusSectionCode(basicInfo.getCampusSectionCode());
        behaviourRecordPO.setGradeId(basicInfo.getGradeId());
        behaviourRecordPO.setGradeCode(basicInfo.getGradeCode());
        behaviourRecordPO.setId(SnowFlakeIdUtil.nextId());


        return behaviourRecordPO;
    }


    /**
     * 分值控件特殊处理
     *
     * @param speedOption
     * @param submitDate
     * @param basicInfo
     * @param faceFlag
     * @return
     */
    private BehaviourRecord buildBehaviourRecordScore(InfoSpeedOptionRequest speedOption,
                                                      Date submitDate,
                                                      InfoSpeedBasicDTO basicInfo,
                                                      Integer faceFlag) {
        if (Objects.isNull(speedOption)) {
            return new BehaviourRecord();
        }


        BigDecimal value = speedOption.getScoreValue();
        BigDecimal score = Objects.isNull(value) ? null : new BigDecimal(value.toString());
        BigDecimal adjustValue = speedOption.getAdjustValue();
        if (score != null && adjustValue != null) {
            score = score.add(adjustValue);
        }

        BehaviourRecord behaviourRecordPO = buildBehaviourRecordBasicCommonInfo(basicInfo, score);

        behaviourRecordPO.setTargetId(speedOption.getTargetId());
        behaviourRecordPO.setTargetName(speedOption.getTargetName());
        behaviourRecordPO.setNotPartCount(speedOption.getTargetNotPartCount());
        behaviourRecordPO.setOptionId(Convert.toStr(speedOption.getTargetId()));
        behaviourRecordPO.setIsScore(true);

        Integer scoreType = Objects.isNull(score) ? null : score.compareTo(new BigDecimal(0)) >= 0 ? 1 : 2;
        behaviourRecordPO.setScoreType(scoreType);

        //分值指标只能是校级分类-校级点评项
        behaviourRecordPO.setInfoType(SPEED_OPTION_SCHOOL_SCHOOL.getCode());

        behaviourRecordPO.setInfoName(speedOption.getScoreName() == null ? "" : speedOption.getScoreName());
        behaviourRecordPO.setSubmitTime(submitDate);
        behaviourRecordPO.setTaskId(null);
        behaviourRecordPO.setTitleId(speedOption.getKey());
        behaviourRecordPO.setModuleCode(speedOption.getModuleCode());
        behaviourRecordPO.setDataSource(Constant.YES.equals(faceFlag) ? EVALUATE_SPEED_FACE.getCode() : EVALUATE_SPEED.getCode());


        return behaviourRecordPO;
    }

    /**
     * 组装极速点评分类
     *
     * @param speedOption
     * @param behaviourRecord
     * @return
     */
    private EvaluateBehaviourRecordExtPO buildEvaluateBehaviourRecordExtPO(InfoSpeedOptionRequest speedOption,
                                                                           BehaviourRecord behaviourRecord) {
        EvaluateBehaviourRecordExtPO evaluateBehaviourRecordExtPO = new EvaluateBehaviourRecordExtPO();
        evaluateBehaviourRecordExtPO.setClassifyId(Convert.toLong(speedOption.getSpeedGroupId()));
        evaluateBehaviourRecordExtPO.setClassifyName(speedOption.getSpeedGroupName());
        evaluateBehaviourRecordExtPO.setBehaviourRecordId(behaviourRecord.getId());
        return evaluateBehaviourRecordExtPO;
    }

    private TaskOperateLog buildTaskOperateLog(InfoSpeedRequest infoSpeedRequest) {
        TaskOperateLog taskOperateLog = new TaskOperateLog();
        taskOperateLog.setOperatorId(WebUtil.getStaffId());
        taskOperateLog.setOperateTime(new Date());
        taskOperateLog.setTenantId(WebUtil.getTenantId());
        taskOperateLog.setSchoolId(WebUtil.getSchoolId());
        taskOperateLog.setCampusId(WebUtil.getCampusId());
        taskOperateLog.setRoleType(TaskRoleTypeEnum.TEACHER.getCode());
        taskOperateLog.setIsBatchFlag(infoSpeedRequest.getIsBatchFlag());
        taskOperateLog.setOperateType(Constant.ONE);
        return taskOperateLog;
    }

    /**
     * 构建发送星动力事件消息体
     *
     * @param behaviourRecords
     * @param planId
     * @return
     */
    private List<ThirdToXdlRecordMessageDTO> convertThirdToXdlRecordMessageDTO(List<BehaviourRecord> behaviourRecords,
                                                                               Long planId,
                                                                               Map<Long, EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordClassifyPOMap) {
        List<ThirdToXdlRecordMessageDTO> records = new ArrayList<>();
        for (BehaviourRecord behaviourRecord : behaviourRecords) {
            ThirdToXdlRecordMessageDTO thirdToXdlRecordMessageDTO = new ThirdToXdlRecordMessageDTO();
            thirdToXdlRecordMessageDTO.setPlanId(planId);
            thirdToXdlRecordMessageDTO.setScore(behaviourRecord.getScore());
            thirdToXdlRecordMessageDTO.setScoreType(behaviourRecord.getScoreType());
            thirdToXdlRecordMessageDTO.setGradeId(behaviourRecord.getGradeId());
            thirdToXdlRecordMessageDTO.setGradeCode(behaviourRecord.getGradeCode());
            thirdToXdlRecordMessageDTO.setTenantId(behaviourRecord.getTenantId());
            thirdToXdlRecordMessageDTO.setSchoolId(behaviourRecord.getSchoolId());
            thirdToXdlRecordMessageDTO.setCampusId(behaviourRecord.getCampusId());
            thirdToXdlRecordMessageDTO.setCampusSectionId(behaviourRecord.getCampusSectionId());
            thirdToXdlRecordMessageDTO.setCampusSectionCode(behaviourRecord.getCampusSectionCode());
            thirdToXdlRecordMessageDTO.setClassId(behaviourRecord.getClassId());
            if (CharSequenceUtil.isBlank(behaviourRecord.getSubjectCode())) {
                thirdToXdlRecordMessageDTO.setSubjectCode("0");
            } else {
                thirdToXdlRecordMessageDTO.setSubjectCode(behaviourRecord.getSubjectCode());
            }
            thirdToXdlRecordMessageDTO.setThirdRecordId(behaviourRecord.getId().toString());
            thirdToXdlRecordMessageDTO.setStudentId(behaviourRecord.getStudentId());
            thirdToXdlRecordMessageDTO.setModuleCode(behaviourRecord.getModuleCode());
            thirdToXdlRecordMessageDTO.setThirdTargetType(1);
            Integer thirdOperationType = Objects.equals(SPEED_OPTION_SCHOOL_SCHOOL.getCode(), behaviourRecord.getInfoType()) ?
                    1 : 2;
            EvaluateBehaviourRecordExtPO evaluateBehaviourRecordExtPO = evaluateBehaviourRecordClassifyPOMap.get(behaviourRecord.getId());
            if (CollUtil.isNotEmpty(evaluateBehaviourRecordClassifyPOMap) && Objects.isNull(evaluateBehaviourRecordExtPO)) {
                log.error("根据行为记录Id获取点评分类出错,behaviourRecord;{},evaluateBehaviourRecordClassifyPOMap:{}", JSONUtil.toJsonStr(behaviourRecord), JSONUtil.toJsonStr(evaluateBehaviourRecordClassifyPOMap));
                throw new BizException("根据行为记录Id获取点评分类出错");
            }
            thirdToXdlRecordMessageDTO.setThirdTargetName(evaluateBehaviourRecordExtPO == null ? "" : evaluateBehaviourRecordExtPO.getClassifyName());
            thirdToXdlRecordMessageDTO.setThirdTargetId(evaluateBehaviourRecordExtPO == null ? "" : Convert.toStr(evaluateBehaviourRecordExtPO.getClassifyId()));
            thirdToXdlRecordMessageDTO.setThirdOperationType(thirdOperationType);
            thirdToXdlRecordMessageDTO.setThirdOptionId(behaviourRecord.getOptionId());
            thirdToXdlRecordMessageDTO.setThirdOptionName(behaviourRecord.getInfoName());
            thirdToXdlRecordMessageDTO.setThirdDataSource(2);
            thirdToXdlRecordMessageDTO.setCreateBy(behaviourRecord.getCreateBy());
            thirdToXdlRecordMessageDTO.setCreateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            thirdToXdlRecordMessageDTO.setCreateByName(behaviourRecord.getAppraisalName());
            thirdToXdlRecordMessageDTO.setScene("personal");
            thirdToXdlRecordMessageDTO.setContent(behaviourRecord.getInfoName());
            records.add(thirdToXdlRecordMessageDTO);
        }
        return records;
    }

    private void convertHelpThirdToXdlRecordMessageDTO(Long planId,
                                                       List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS,
                                                       List<EvaluateHelpBehaviourRecordPO> evaluateHelpBehaviourRecordPOS) {

        if (CollUtil.isEmpty(evaluateHelpBehaviourRecordPOS)) {
            return;
        }
        for (EvaluateHelpBehaviourRecordPO evaluateHelpBehaviourRecordPO : evaluateHelpBehaviourRecordPOS) {
            ThirdToXdlRecordMessageDTO thirdToXdlRecordMessageDTO = new ThirdToXdlRecordMessageDTO();
            thirdToXdlRecordMessageDTO.setPlanId(planId);
            thirdToXdlRecordMessageDTO.setScore(evaluateHelpBehaviourRecordPO.getScore());
            thirdToXdlRecordMessageDTO.setScoreType(evaluateHelpBehaviourRecordPO.getScoreType());
            thirdToXdlRecordMessageDTO.setGradeId(evaluateHelpBehaviourRecordPO.getGradeId());
            thirdToXdlRecordMessageDTO.setGradeCode(evaluateHelpBehaviourRecordPO.getGradeCode());
            thirdToXdlRecordMessageDTO.setTenantId(evaluateHelpBehaviourRecordPO.getTenantId());
            thirdToXdlRecordMessageDTO.setSchoolId(evaluateHelpBehaviourRecordPO.getSchoolId());
            thirdToXdlRecordMessageDTO.setCampusId(evaluateHelpBehaviourRecordPO.getCampusId());
            thirdToXdlRecordMessageDTO.setCampusSectionId(evaluateHelpBehaviourRecordPO.getCampusSectionId());
            thirdToXdlRecordMessageDTO.setCampusSectionCode(evaluateHelpBehaviourRecordPO.getCampusSectionCode());
            thirdToXdlRecordMessageDTO.setClassId(evaluateHelpBehaviourRecordPO.getClassId());
            thirdToXdlRecordMessageDTO.setSubjectCode("0");
            thirdToXdlRecordMessageDTO.setThirdRecordId(evaluateHelpBehaviourRecordPO.getId().toString());
            thirdToXdlRecordMessageDTO.setStudentId(evaluateHelpBehaviourRecordPO.getStudentId());
            thirdToXdlRecordMessageDTO.setModuleCode(evaluateHelpBehaviourRecordPO.getModuleCode());
            thirdToXdlRecordMessageDTO.setThirdTargetType(1);
            Integer thirdOperationType = Objects.equals(SPEED_OPTION_SCHOOL_SCHOOL.getCode(), evaluateHelpBehaviourRecordPO.getDataType()) ?
                    1 : 2;
            thirdToXdlRecordMessageDTO.setThirdTargetName(evaluateHelpBehaviourRecordPO.getClassifyName());
            thirdToXdlRecordMessageDTO.setThirdTargetId(Convert.toStr(evaluateHelpBehaviourRecordPO.getClassifyId()));
            thirdToXdlRecordMessageDTO.setThirdOperationType(thirdOperationType);
            thirdToXdlRecordMessageDTO.setThirdOptionId(evaluateHelpBehaviourRecordPO.getOptionId());
            thirdToXdlRecordMessageDTO.setThirdOptionName(evaluateHelpBehaviourRecordPO.getOptionName());
            thirdToXdlRecordMessageDTO.setThirdDataSource(2);
            thirdToXdlRecordMessageDTO.setCreateBy(evaluateHelpBehaviourRecordPO.getCreateBy());
            thirdToXdlRecordMessageDTO.setCreateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            thirdToXdlRecordMessageDTO.setCreateByName(evaluateHelpBehaviourRecordPO.getAppraisalName());
            thirdToXdlRecordMessageDTO.setScene("help");
            thirdToXdlRecordMessageDTO.setContent(evaluateHelpBehaviourRecordPO.getHelpDesc());
            thirdToXdlRecordMessageDTOS.add(thirdToXdlRecordMessageDTO);
        }
    }

    /**
     * 师徒帮扶积分板获取积分
     *
     * @param request
     */
    private void buildComputeRecord(InfoSpeedBasicDTO speedBasicDTO,
                                    InfoSpeedRequest request,
                                    List<BehaviourRecord> behaviourRecordPOList,
                                    List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordExtPOS,
                                    List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS) {

        //TODO @Valid 校验是否有效
        if (CollUtil.isEmpty(request.getInfoSpeedStudentRequests()) || CollUtil.isEmpty(behaviourRecordPOList)) {
            return;
        }

        String planId = request.getPlanId();
        String classId = request.getClassId();

        ComputeRecordRequest computeRecordRequest = new ComputeRecordRequest();
        //TODO 此参数无用，暂时传值默认0
        computeRecordRequest.setSubjectCode("0");
        computeRecordRequest.setPlanId(Convert.toLong(planId));
        computeRecordRequest.setSaasClassId(classId);
        computeRecordRequest.setSaasCampusId(speedBasicDTO.getCampusId());
        computeRecordRequest.setSaasSchoolId(speedBasicDTO.getSchoolId());

        // 过滤无分值和分值为0的行为记录并根据选项id+分值分组
        Map<String, List<BehaviourRecord>> recordMap = behaviourRecordPOList
                .stream()
                .filter(item -> Objects.nonNull(item.getScore()) && item.getScore().compareTo(new BigDecimal(0)) != 0)
                .collect(Collectors.groupingBy(behaviourRecord ->
                        behaviourRecord.getTargetId() + "-" + behaviourRecord.getOptionId() + "-" + behaviourRecord.getScore()));

        this.requestComputeRecord(recordMap, computeRecordRequest, evaluateBehaviourRecordExtPOS, helpBehaviourRecordPOS);
    }


    /**
     * 获取师徒帮扶记录
     */
    private void requestComputeRecord(Map<String, List<BehaviourRecord>> recordMap,
                                      ComputeRecordRequest computeRecordRequest,
                                      List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordExtPOS,
                                      List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS) {

        Map<Long, EvaluateBehaviourRecordExtPO> behaviourRecordClassifyPOMap = evaluateBehaviourRecordExtPOS
                .stream()
                .collect(Collectors.toMap(EvaluateBehaviourRecordExtPO::getBehaviourRecordId, Function.identity(), (k1, k2) -> k1));

        Map<Long, EvaluateBehaviourRecordExtPO> classifyPOMap = evaluateBehaviourRecordExtPOS
                .stream()
                .collect(Collectors.toMap(EvaluateBehaviourRecordExtPO::getClassifyId, Function.identity(), (k1, k2) -> k1));

        recordMap.forEach((k, v) -> {
            ComputeRecordCommentRequest computeRecordCommentRequest = new ComputeRecordCommentRequest();

            List<ComputeRecordStudentRequest> studentRequests = new ArrayList<>();

            for (BehaviourRecord behaviourRecord : v) {
                BigDecimal score = behaviourRecord.getScore();

                if (Objects.isNull(score)) {
                    continue;
                }

                if (score.compareTo(new BigDecimal(0L)) == 0) {
                    continue;
                }

                ComputeRecordStudentRequest computeRecordStudentRequest = new ComputeRecordStudentRequest();
                computeRecordCommentRequest.setCommentId(CharSequenceUtil.isBlank(behaviourRecord.getOptionId()) ? Convert.toStr(behaviourRecord.getTargetId()) : behaviourRecord.getOptionId());
                computeRecordCommentRequest.setCommentName(behaviourRecord.getInfoName());
                computeRecordCommentRequest.setTagId(behaviourRecordClassifyPOMap.get(behaviourRecord.getId()).getClassifyId());
                computeRecordCommentRequest.setScore(behaviourRecord.getScore());
                computeRecordCommentRequest.setModuleCode(behaviourRecord.getModuleCode());
                computeRecordStudentRequest.setStudentId(Convert.toLong(behaviourRecord.getStudentId()));
                studentRequests.add(computeRecordStudentRequest);
            }


            if (CollUtil.isNotEmpty(studentRequests)) {

                computeRecordCommentRequest.setStudentList(studentRequests);

                computeRecordRequest.setCommentList(Collections.singletonList(computeRecordCommentRequest));

                List<InternalDrivePointRecordResponse> internalDrivePointRecordResponses = internalDriveRemote.queryComputeRecord(computeRecordRequest);

                for (InternalDrivePointRecordResponse internalDrivePointRecordResponse : internalDrivePointRecordResponses) {

                    if (Objects.equals(internalDrivePointRecordResponse.getScene(), "personal")) {
                        continue;
                    }

                    BehaviourRecord internalDriveRecord = v.get(0);
                    EvaluateHelpBehaviourRecordPO evaluateHelpBehaviourRecordPO = this.buildEvaluateHelpBehaviourRecordPO(internalDriveRecord,
                            internalDrivePointRecordResponse,
                            classifyPOMap);

                    helpBehaviourRecordPOS.add(evaluateHelpBehaviourRecordPO);
                }
            }


        });
    }

    private EvaluateHelpBehaviourRecordPO buildEvaluateHelpBehaviourRecordPO(BehaviourRecord internalDriveRecord,
                                                                             InternalDrivePointRecordResponse internalDrivePointRecordResponse,
                                                                             Map<Long, EvaluateBehaviourRecordExtPO> classifyPOMap) {
        EvaluateHelpBehaviourRecordPO evaluateHelpBehaviourRecordPO = new EvaluateHelpBehaviourRecordPO();
        evaluateHelpBehaviourRecordPO.setId(SnowFlakeIdUtil.nextId());
        BigDecimal score = BigDecimal.valueOf(internalDrivePointRecordResponse.getScore());
        evaluateHelpBehaviourRecordPO.setScore(score);
        evaluateHelpBehaviourRecordPO.setScoreValue(score.abs());
        evaluateHelpBehaviourRecordPO.setScoreType(internalDriveRecord.getScoreType());
        evaluateHelpBehaviourRecordPO.setTenantId(internalDriveRecord.getTenantId());
        evaluateHelpBehaviourRecordPO.setSchoolId(internalDriveRecord.getSchoolId());
        evaluateHelpBehaviourRecordPO.setCampusId(internalDriveRecord.getCampusId());
        evaluateHelpBehaviourRecordPO.setCampusSectionId(internalDriveRecord.getCampusSectionId());
        evaluateHelpBehaviourRecordPO.setCampusSectionCode(internalDriveRecord.getCampusSectionCode());
        evaluateHelpBehaviourRecordPO.setGradeId(internalDriveRecord.getGradeId());
        evaluateHelpBehaviourRecordPO.setGradeCode(internalDriveRecord.getGradeCode());
        evaluateHelpBehaviourRecordPO.setClassId(internalDriveRecord.getClassId());
        evaluateHelpBehaviourRecordPO.setStudentId(Convert.toStr(internalDrivePointRecordResponse.getStudentId()));
        evaluateHelpBehaviourRecordPO.setStudentName(internalDrivePointRecordResponse.getStudentName());
        evaluateHelpBehaviourRecordPO.setModuleCode(internalDriveRecord.getModuleCode());
        evaluateHelpBehaviourRecordPO.setTargetId(internalDriveRecord.getTargetId());
        evaluateHelpBehaviourRecordPO.setTargetName(internalDriveRecord.getTargetName());
        evaluateHelpBehaviourRecordPO.setNotPartCount(internalDriveRecord.getNotPartCount());
        EvaluateBehaviourRecordExtPO evaluateBehaviourRecordExtPO = classifyPOMap.get(internalDrivePointRecordResponse.getPlanTagId());
        evaluateHelpBehaviourRecordPO.setClassifyId(evaluateBehaviourRecordExtPO.getClassifyId());
        evaluateHelpBehaviourRecordPO.setClassifyName(evaluateBehaviourRecordExtPO.getClassifyName());
        evaluateHelpBehaviourRecordPO.setOptionId(internalDriveRecord.getOptionId());
        evaluateHelpBehaviourRecordPO.setOptionName(internalDriveRecord.getInfoName());
        evaluateHelpBehaviourRecordPO.setHelpDesc(internalDrivePointRecordResponse.getContent());
        evaluateHelpBehaviourRecordPO.setDataType(internalDriveRecord.getInfoType());
        evaluateHelpBehaviourRecordPO.setDataChannel(1);
        evaluateHelpBehaviourRecordPO.setSubmitTime(internalDriveRecord.getSubmitTime());
        evaluateHelpBehaviourRecordPO.setAppraisalId(internalDriveRecord.getAppraisalId());
        evaluateHelpBehaviourRecordPO.setAppraisalName(internalDriveRecord.getAppraisalName());
        evaluateHelpBehaviourRecordPO.setCreateBy(internalDriveRecord.getCreateBy());
        return evaluateHelpBehaviourRecordPO;
    }


    /**
     * 获取新增的年级
     *
     * @param behaviourRecords
     * @param sectionSet
     */
    private void addGradeRedis(List<BehaviourRecord> behaviourRecords, Set<String> sectionSet) {
        Map<String, List<BehaviourRecord>> deleteSectionIdMap = behaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecord::getCampusSectionId));
        deleteSectionIdMap.forEach((key, value) -> {
            Set<String> gradeIdSet = value.stream().map(BehaviourRecord::getGradeId).collect(Collectors.toSet());
            gradeIdSet.forEach(s -> {
                //年级
                redisUtil.sSet(RedisKeyConstants.EVALUATE_INFO_SECTION_ID + key,s );
                log.info("【点评项变动，通知计算，学段 id：【{}】, 年级 id：【{}】", key, s);
            });

        });
        sectionSet.addAll(deleteSectionIdMap.keySet());
    }

    /**
     * 获取新增的学段
     *
     * @param sectionSet
     */
    private void addSectionRedis(Set<String> sectionSet) {
        sectionSet.forEach(s -> {
            redisUtil.sSet(RedisKeyConstants.EVALUATE_INFO_MARK, s);
            log.info("【点评项变动，通知计算，学段 id：【{}】", s);
        });

    }

    @Override
    public void deleteSpeedByIds(RemoveSpeedRequest removeSpeedRequest) {
        log.info("删除极速点评，request:{}", JSONUtil.toJsonStr(removeSpeedRequest));
        if (CollUtil.isEmpty(removeSpeedRequest.getIds())) {
            return;
        }

        Date updateTime = new Date();
        List<Long> ids = removeSpeedRequest.getIds();

        Map<Long, String> mapByLocalRecordIds = thirdDataInfoService.mapByLocalRecordIds(ids);
        List<BehaviourRecord> records = behaviourRecordService.listByIds(ids);

        TaskOperateLog taskOperateLog = new TaskOperateLog();
        taskOperateLog.setOperatorId(WebUtil.getStaffId());

        taskOperateLog.setOperateTime(updateTime);
        taskOperateLog.setTenantId(WebUtil.getTenantId());
        taskOperateLog.setSchoolId(WebUtil.getSchoolId());
        taskOperateLog.setCampusId(WebUtil.getCampusId());
        taskOperateLog.setRoleType(TaskRoleTypeEnum.TEACHER.getCode());
        taskOperateLog.setIsBatchFlag(0);
        taskOperateLog.setOperateType(Constant.ONE);
        taskOperateLog.setInfoId(removeSpeedRequest.getInfoId());

        taskOperateLogManager.save(taskOperateLog);
        // 转积分
//        behaviourHandleManager.sendMQ(3, 3, records, null);
        BehaviourRecordHandleDTO behaviourRecordHandleDTO = new BehaviourRecordHandleDTO();
        behaviourRecordHandleDTO.setDeletedBehaviourRecordIds(ids);

        // 新增行为记录处理争章活动
        evaluateInfoService.handleActivity(behaviourRecordHandleDTO);

        // 删除行为记录和点评分类
        evaluateBehaviourRecordExtManager.removeByEvaluateIds(ids, WebUtil.getStaffId(), updateTime);
        behaviourRecordManager.removeBatchByIds(ids, WebUtil.getStaffId(), updateTime);

        // 同步星动力
        List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS = this.convertThirdToXdlRecordMessageDTO(records, null, new HashMap<>());
        if (CollUtil.isNotEmpty(mapByLocalRecordIds)) {
            for (ThirdToXdlRecordMessageDTO thirdToXdlRecordMessageDTO : thirdToXdlRecordMessageDTOS) {
                String thirdId = mapByLocalRecordIds.get(Convert.toLong(thirdToXdlRecordMessageDTO.getThirdRecordId()));
                if (CharSequenceUtil.isNotBlank(thirdId)) {
                    thirdToXdlRecordMessageDTO.setThirdRecordId(thirdId);
                    thirdToXdlRecordMessageDTO.setThirdDataSource(1);
                }
            }
        }
        // 新增点评项发送mq
        Integer pointSwitch = targetGroupService.listPointSwitch(WebUtil.getCampusId());
        if (Objects.equals(pointSwitch, 1)) {
            convertInitialService.sendMQ(3, 3, records, null);
            // 发送积分金币转换mq
            convertInitialService.sendBehaviourExchangeMq(
                    infoService.assemblyPointExchangeMq(records,null,
                            CoinExchangeOperateTypeEnum.DELETE.getCode(), CoinExchangeStrategyEnum.SPEED_COMMENT.getCode()));
        } else {
            List<BehaviourRecord> toPointRecords = records
                    .stream()
                    .filter(item -> !Objects.equals(item.getInfoType(), SPEED_OPTION_SCHOOL_TEACHER.getCode())
                            && !Objects.equals(item.getInfoType(), SPEED_OPTION_TEACHER_TEACHER.getCode()))
                    .collect(Collectors.toList());
            convertInitialService.sendMQ(3, 3, toPointRecords, null);
            // 发送积分金币转换mq
            convertInitialService.sendBehaviourExchangeMq(
                    infoService.assemblyPointExchangeMq(records,null,
                            CoinExchangeOperateTypeEnum.DELETE.getCode(), CoinExchangeStrategyEnum.SPEED_COMMENT.getCode()));
        }
        eventSendHelper.sendEventMQ(thirdToXdlRecordMessageDTOS, xwlDelRecord);


        //点评项增删改打标
        Set<String> sectionSet = new HashSet<>();
        //删除的年级
        addGradeRedis(records, sectionSet);
        //新增和删除的学段
        if (CollUtil.isNotEmpty(sectionSet)) {
            addSectionRedis(sectionSet);
        }

        evaluateExecutor.execute(() -> {
            // 历史学年点评记录变动触发重新计算
            if (CollUtil.isEmpty(records)) return;
            BehaviourRecord behaviourRecord = records.get(Constant.ZERO);
            TermVo termVo = termLogic.getTermVoByCampusIdCache(WebUtil.getSchoolIdLong(), WebUtil.getCampusIdLong(), Convert.toLong(behaviourRecord.getCampusSectionId()), behaviourRecord.getSubmitTime());
            // 如果是历史学期 直接触发综合成绩计算
            if (!termVo.getIsCurrentTerm()) {
                // 综合成绩
                infoService.revaluate(behaviourRecord.getCampusSectionId(),behaviourRecord.getGradeId(), termVo.getSchoolYear(), termVo.getTermName(), behaviourRecord.getSubmitTime(), RevaluateBusinessTypeEnum.REPORT_EXAM.getCode());
            }
            // 如果修改的不是当天的提交记录 则触发今日之前的T+1数据变更重复重新计算
            if (!DateUtil.isSameDay(DateUtil.date(), behaviourRecord.getSubmitTime())) {
                // 今日之前的T+1数据变更重复重新计算
                infoService.revaluate(behaviourRecord.getCampusSectionId(),behaviourRecord.getGradeId(), termVo.getSchoolYear(), termVo.getTermName(), behaviourRecord.getSubmitTime(), RevaluateBusinessTypeEnum.BEHAVIOUR_RECORD.getCode());
            }
        });
    }

    @Override
    @RateLimit(key = RedisKeyConstants.EVALUATE_LIMIT_FACE_SEARCH,
            mode = RateType.PER_CLIENT,
            rate = 10,
            rateInterval = 10,
            rateIntervalUnit = RateIntervalUnit.SECONDS)
    public List<StudentInfoVO> faceSearch(FaceDetectRequest request) {

        Assert.notBlank(request.getImage(), "人脸图片不能为空");
        Assert.notBlank(request.getImageType(), "图片类型不能为空");

        // 判断是否可以人脸识别以及校验
        SysSwitchConfigPO sysSwitchConfig = sysSwitchConfigManager
                .getBySchoolId(WebUtil.getSchoolId(), SysSwitchConfigEnum.XWL_SCHOOL_FACE.getCode());
        if (BeanUtil.isEmpty(sysSwitchConfig) || Constant.NO.equals(sysSwitchConfig.getStatus())) {
            throw new BizException("该学校未开通人脸识别");
        }

        //人脸检测
        FaceDetectResultVO faceDetectResultVO = saasStudentManager.studentFaceDetect(request);

        //根据人脸识别获取学生id
        Set<Long> studentIdList = this.listStudentIds(faceDetectResultVO);

        //查询学生信息
        return this.listStudentInfoVOS(studentIdList);

    }

    @Override
    public void deleteSpeedByBusinessIds(XxbInfoSpeedDeleteRequest request) {
        if(null == request || CollUtil.isEmpty(request.getBusinessIds())){
            log.warn("【删除星学伴极速点评记录】，入参为空");
            return;
        }
        List<ThirdDataInfoPO> thirdDataInfoPOS = thirdDataInfoService.listByThirdRecordIds(request.getBusinessIds());
        if(CollUtil.isEmpty(thirdDataInfoPOS)){
            log.warn("【删除星学伴极速点评记录】，未查询到对应记录，businessId:{}", request.getBusinessIds());
            return;
        }
        List<Long> evaluateIds = thirdDataInfoPOS.stream()
                .map(ThirdDataInfoPO::getLocalRecordId)
                .collect(Collectors.toList());
        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.listByIds(evaluateIds);
        if(CollUtil.isEmpty(behaviourRecords)){
            log.warn("根据id查询学生行为记录明细为空，evaluateIds:{}", JSON.toJSONString(evaluateIds));
            return;
        }
        Map<String, List<BehaviourRecord>> infoIdListMap = CollStreamUtil.groupByKey(behaviourRecords,
                BehaviourRecord::getInfoId);
        infoIdListMap.forEach((infoId, behaviourRecordList) -> {
            RemoveSpeedRequest removeSpeedRequest = new RemoveSpeedRequest();
            removeSpeedRequest.setInfoId(infoId);
            removeSpeedRequest.setIds(CollUtil.getFieldValues(behaviourRecordList, "id", Long.class));
            this.deleteSpeedByIds(removeSpeedRequest);
        });
    }

    /**
     * 查询学生信息
     *
     * @param studentIdList 学生 id
     */
    private List<StudentInfoVO> listStudentInfoVOS(Set<Long> studentIdList) {
        if (CollUtil.isEmpty(studentIdList)) {
            log.warn("人脸识别-查询学生信息，入参为空直接返回");
            return Collections.emptyList();
        }

        StudentDTO studentByIdQuery = new StudentDTO();
        studentByIdQuery.setStudentIds(Convert.toList(Long.class, studentIdList));
        List<StudentInfoVO> studentInfoVOList = saasStudentManager.studentDetailV2(studentByIdQuery);

        //过滤毕业班和兴趣班
        studentInfoVOList = studentInfoVOList
                .stream()
                .filter(d -> "0".equals(d.getUpgradeStatus()))
                .filter(d -> "0".equals(d.getClassType()))
                .collect(Collectors.toList());

        Assert.notEmpty(studentInfoVOList, () -> new BizException(30002, "人脸识别失败，可能学生人脸未入库或者人脸录入不正确"));

        //过滤校区
        studentInfoVOList = studentInfoVOList
                .stream()
                .filter(studentInfoVO -> WebUtil.getCampusIdLong().equals(studentInfoVO.getCampusId()))
                .collect(Collectors.toList());
        Assert.notEmpty(studentInfoVOList, () -> new BizException(30003, "你没有权限评价该学生"));

        return studentInfoVOList;
    }


    /**
     * 根据人脸识别获取学生id
     *
     * @param faceDetectResultVO 人脸检测结果
     */
    private Set<Long> listStudentIds(FaceDetectResultVO faceDetectResultVO) {
        Set<Long> studentIdList = new HashSet<>();
        if (BeanUtil.isEmpty(faceDetectResultVO) || CollUtil.isEmpty(faceDetectResultVO.getFaceList())) {
            log.warn("扫脸点评-人脸自动识别-未识别人脸，入参：{}", JSONUtil.toJsonStr(faceDetectResultVO));
            return studentIdList;
        }

        List<String> faceTokens = faceDetectResultVO.getFaceList()
                .stream().map(FaceDetailVO::getFaceToken).collect(Collectors.toList());

        for (String faceToken : faceTokens) {
            FaceV2DTO faceDTO = new FaceV2DTO();
            faceDTO.setFaceImage(faceToken);
            faceDTO.setSchoolId(WebUtil.getSchoolIdLong());
            faceDTO.setImageType(FaceImageTypeEnum.FACE_TOKEN.getCode());
            StudentFaceResultVO studentFaceResultVO;
            //人脸识别
            if (Constant.ONE.equals(faceTokens.size())) {
                studentFaceResultVO = saasStudentManager.studentFaceQueryV3(faceDTO);
            } else {
                studentFaceResultVO = saasStudentManager.studentFaceQueryV2(faceDTO);
            }

            if (BeanUtil.isEmpty(studentFaceResultVO) || CollUtil.isEmpty(studentFaceResultVO.getStudentList())) {
                log.warn("扫脸点评-人脸自动识别-未发现人脸，入参：{}", JSONUtil.toJsonStr(faceDTO));
                continue;
            }
            studentIdList.addAll(studentFaceResultVO.getStudentList()
                    .stream()
                    .map(StudentFaceResultItemVO::getStudentId)
                    .collect(Collectors.toSet())
            );
        }
        Assert.notEmpty(studentIdList, () -> new BizException(30002, "人脸识别失败，可能学生人脸未入库或者人脸录入不正确"));
        return studentIdList;
    }

}
