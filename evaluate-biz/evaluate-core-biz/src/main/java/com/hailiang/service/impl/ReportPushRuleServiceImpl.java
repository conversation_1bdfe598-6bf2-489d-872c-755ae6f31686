package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.constant.Constant;
import com.hailiang.exception.BizException;
import com.hailiang.mapper.ReportPushRuleMapper;
import com.hailiang.model.dto.save.PushRuleDailyCommentDTO;
import com.hailiang.model.dto.save.PushRuleRangeDTO;
import com.hailiang.model.dto.save.ReportPushRuleSaveDTO;
import com.hailiang.model.entity.DailyNoticePushRule;
import com.hailiang.model.entity.ReportPushRule;
import com.hailiang.model.entity.ReportPushRuleRange;
import com.hailiang.model.vo.ReportPushRuleVO;
import com.hailiang.mp.commonsource.core.exception.BaseRuntimeException;
import com.hailiang.service.DailyNoticePushRuleService;
import com.hailiang.service.ReportPushRuleRangeService;
import com.hailiang.service.ReportPushRuleService;
import com.hailiang.util.DateHelper;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告推送规则表(ReportPushRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-13 11:30:31
 */
@Slf4j
@Service("reportPushRuleService")
public class ReportPushRuleServiceImpl extends ServiceImpl<ReportPushRuleMapper, ReportPushRule> implements ReportPushRuleService {

    @Resource
    private ReportPushRuleRangeService reportPushRuleRangeService;

    @Resource
    private DailyNoticePushRuleService dailyNoticePushRuleService;

    /**
     * 保存报告推送规则
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRule(ReportPushRuleSaveDTO reportPushRuleSaveDTO) {
        log.info("saving rule start for campusId: {} and schoolId: {} and reportPushRuleSaveDTO: {}",
                WebUtil.getCampusId(), WebUtil.getSchoolId(),reportPushRuleSaveDTO);

        //规则参数校验
        this.validateRule(reportPushRuleSaveDTO);

        //保存每日画像推送规则
        this.saveReportPushRule(reportPushRuleSaveDTO);

        //保存每日点评推送规则 (自动加入当前事务中)
        dailyNoticePushRuleService.saveDailyPushRule(reportPushRuleSaveDTO);

        log.info("saved rule end for campusId: {} and schoolId: {} and reportPushRuleSaveDTO: {}",
                WebUtil.getCampusId(), WebUtil.getSchoolId(),reportPushRuleSaveDTO);
        return Boolean.TRUE;
    }

    /**
     * 规则参数校验
     * @param reportPushRuleSaveDTO 保存参数
     */
    private void validateRule(ReportPushRuleSaveDTO reportPushRuleSaveDTO) {
        Assert.isFalse(Objects.isNull(WebUtil.getTenantId()), () -> new BaseRuntimeException("租户id不存在，请检查请求头！"));
        Assert.isFalse(Objects.isNull(WebUtil.getSchoolId()), () -> new BaseRuntimeException("学校id不存在，请检查请求头！"));
        Assert.isFalse(Objects.isNull(WebUtil.getCampusId()), () -> new BaseRuntimeException("校区id不存在，请检查请求头！"));
        List<ReportPushRule> reportPushRuleList = super.list(new LambdaQueryWrapper<ReportPushRule>()
                .eq(ReportPushRule::getCampusId, WebUtil.getCampusId()));
        Assert.isFalse(Objects.isNull(reportPushRuleSaveDTO.getId()) && !reportPushRuleList.isEmpty(),
                () -> new BaseRuntimeException("该校区下已配置报告推送规则，请关闭弹窗并重新打开！"));
        Assert.isFalse(Objects.nonNull(reportPushRuleSaveDTO.getId()) && CollUtil.isNotEmpty(reportPushRuleList)
                        && (!reportPushRuleSaveDTO.getId().equals(CollUtil.getFirst(reportPushRuleList).getId()))
                , () -> new BaseRuntimeException("规则id在该校区下不存在，请检查参数！"));
    }

    /**
     * 保存每日画像推送规则
     * @param reportPushRuleSaveDTO 保存参数
     */
    private void saveReportPushRule(ReportPushRuleSaveDTO reportPushRuleSaveDTO) {

        //获取每日点评推送规则实体
        ReportPushRule reportPushRule = getReportPushRule(reportPushRuleSaveDTO);

        //保存每日画像推送规则
        super.saveOrUpdate(reportPushRule);

        //保存每日画像推送学校范围
        this.savePushSchoolRange(reportPushRuleSaveDTO, reportPushRule);
    }

    /**
     * 获取每日点评推送规则实体
     * @param reportPushRuleSaveDTO reportPushRuleSaveDTO
     * @return ReportPushRule
     */
    private ReportPushRule getReportPushRule(ReportPushRuleSaveDTO reportPushRuleSaveDTO) {
        ReportPushRule reportPushRule = new ReportPushRule();
        if (Constant.YES.equals(reportPushRuleSaveDTO.getParentPushFlag())) {
            BeanUtils.copyProperties(reportPushRuleSaveDTO, reportPushRule);
            reportPushRule.setRuleTime(DateHelper.getFirstSendTime(DateHelper.getPushTime(new Date(), reportPushRuleSaveDTO.getPushTime()), reportPushRuleSaveDTO.getPushWeek()));
        } else {
            reportPushRule.setId(reportPushRuleSaveDTO.getId());
            reportPushRule.setParentPushFlag(reportPushRuleSaveDTO.getParentPushFlag());
        }
        if (Objects.nonNull(reportPushRuleSaveDTO.getPushWay())) {
            reportPushRule.setPushWay(reportPushRuleSaveDTO.getPushWay());
        }
        reportPushRule.setShowAppraisalFlag(reportPushRuleSaveDTO.getShowAppraisalFlag());
        reportPushRule.setTenantId(WebUtil.getTenantId());
        reportPushRule.setSchoolId(WebUtil.getSchoolId());
        reportPushRule.setCampusId(WebUtil.getCampusId());
        return reportPushRule;
    }

    /**
     * 保存每日画像推送学校范围
     * @param reportPushRuleSaveDTO 保存参数
     * @param reportPushRule 保存参数
     */
    private void savePushSchoolRange(ReportPushRuleSaveDTO reportPushRuleSaveDTO, ReportPushRule reportPushRule) {
        List<ReportPushRuleRange> rangeList = new ArrayList<>();

        if (CollUtil.isNotEmpty(reportPushRuleSaveDTO.getPushRanges())){
            for (PushRuleRangeDTO pushRange : reportPushRuleSaveDTO.getPushRanges()) {
                ReportPushRuleRange reportPushRuleRange = new ReportPushRuleRange();
                reportPushRuleRange.setCurrentId(Convert.toStr(pushRange.getCurrentId()));
                reportPushRuleRange.setCurrentType(pushRange.getCurrentType());
                reportPushRuleRange.setName(Convert.toStr(pushRange.getName()));
                reportPushRuleRange.setTenantId(WebUtil.getTenantId());
                reportPushRuleRange.setSchoolId(WebUtil.getSchoolId());
                reportPushRuleRange.setCampusId(WebUtil.getCampusId());
                reportPushRuleRange.setPushRuleId(reportPushRule.getId());
                rangeList.add(reportPushRuleRange);
            }
        }
        updatePushRange(rangeList, reportPushRule.getId());
    }

    /**
     * 查询报告推送规则详情
     */
    @Override
    public ReportPushRuleVO detailRule() {
        Assert.isFalse(Objects.isNull(WebUtil.getCampusId()), () -> new BizException("校区id不存在，请检查请求头！"));
        ReportPushRuleVO reportPushRuleVO = new ReportPushRuleVO();
        List<ReportPushRule> reportPushRuleList = list(new LambdaQueryWrapper<ReportPushRule>().eq(ReportPushRule::getCampusId, WebUtil.getCampusId()));
        Assert.isFalse(reportPushRuleList.size() > 1, () -> new BizException("该校区下配置了多个规则，请检查！"));
        if (CollUtil.isNotEmpty(reportPushRuleList)) {
            BeanUtils.copyProperties(reportPushRuleList.get(0), reportPushRuleVO);
        }
        if (Objects.nonNull(reportPushRuleVO.getId())) {

            List<ReportPushRuleRange> existPushRuleRangeList = reportPushRuleRangeService.list(new LambdaQueryWrapper<ReportPushRuleRange>().eq(ReportPushRuleRange::getPushRuleId, reportPushRuleVO.getId()));

            List<PushRuleRangeDTO> rangeDTOList = new ArrayList<>();
            for (ReportPushRuleRange pushRange : existPushRuleRangeList) {
                PushRuleRangeDTO pushRuleRangeDTO = new PushRuleRangeDTO();
                pushRuleRangeDTO.setCurrentId(Convert.toLong(pushRange.getCurrentId()));
                pushRuleRangeDTO.setCurrentType(pushRange.getCurrentType());
                pushRuleRangeDTO.setName(Convert.toStr(pushRange.getName()));
                rangeDTOList.add(pushRuleRangeDTO);
            }

            reportPushRuleVO.setPushRanges(rangeDTOList);
        }
        //获取每日点评推送规则
        this.getDailyPushRuleVO(reportPushRuleVO);

        log.info("detailRule result: {}", reportPushRuleVO);
        return reportPushRuleVO;
    }

    @Override
    public List<ReportPushRule> listByCampusIds(List<String> campusIds) {
       if(CollUtil.isEmpty(campusIds)){
           log.info("[listByCampusIds] campusIds is empty");
           return Collections.emptyList();
       }
       return this.list(new LambdaQueryWrapper<ReportPushRule>()
                .in(ReportPushRule::getCampusId, campusIds));
    }

    @Override
    public Boolean getShowAppraisalFlag(String campusId) {
        Assert.notBlank(campusId, () -> new BizException("校区id不存在"));
        // 默认显示
        Boolean showAppraisalFlag = true;
        // 是否显示点评人
        ReportPushRule reportPushRule = this.getOne(new LambdaQueryWrapper<ReportPushRule>()
                .select(ReportPushRule::getShowAppraisalFlag)
                .eq(ReportPushRule::getCampusId, WebUtil.getCampusId())
                .last("limit 1"));
        if (Objects.nonNull(reportPushRule)) {
            showAppraisalFlag = Convert.toBool(reportPushRule.getShowAppraisalFlag());
        }
        return showAppraisalFlag;
    }

    private void getDailyPushRuleVO(ReportPushRuleVO reportPushRuleVO) {
        List<DailyNoticePushRule> noticePushRuleList = dailyNoticePushRuleService.list(new LambdaQueryWrapper<DailyNoticePushRule>()
                .eq(DailyNoticePushRule::getCampusId, WebUtil.getCampusId())
                .eq(DailyNoticePushRule::getDeleted, Constant.NO));
        if (CollUtil.isNotEmpty(noticePushRuleList)) {
            PushRuleDailyCommentDTO dailyCommentDTO = new PushRuleDailyCommentDTO();
            reportPushRuleVO.setCommentParentPushFlag(noticePushRuleList.get(0).getParentPushFlag());
            dailyCommentDTO.setPushDay(noticePushRuleList.get(0).getPushDay());
            dailyCommentDTO.setPushTime(noticePushRuleList.get(0).getPushTime());
            reportPushRuleVO.setDailyCommentDTO(dailyCommentDTO);
            reportPushRuleVO.setPushWay(noticePushRuleList.get(0).getPushWay());
        }
    }

    /**
     * 更新推送范围
     */
    private void updatePushRange(List<ReportPushRuleRange> pushRuleRangeList, Long ruleId) {
        List<ReportPushRuleRange> existPushRuleRangeList = reportPushRuleRangeService.list(new LambdaQueryWrapper<ReportPushRuleRange>().eq(ReportPushRuleRange::getPushRuleId, ruleId));

        List<ReportPushRuleRange> insertList = pushRuleRangeList.stream().filter(item -> !existPushRuleRangeList.stream().map(ReportPushRuleRange::getCurrentId).collect(Collectors.toList()).contains(item.getCurrentId()))
                .collect(Collectors.toList());
        List<Long> removeIds = existPushRuleRangeList.stream().filter(item -> !pushRuleRangeList.stream().map(ReportPushRuleRange::getCurrentId).collect(Collectors.toList()).contains(item.getCurrentId()))
                .map(ReportPushRuleRange::getId).distinct().collect(Collectors.toList());

        if (!insertList.isEmpty()) {
            reportPushRuleRangeService.saveBatch(insertList);
        }
        if (!removeIds.isEmpty()) {
            reportPushRuleRangeService.removeBatchByIds(removeIds);
        }

    }
}

