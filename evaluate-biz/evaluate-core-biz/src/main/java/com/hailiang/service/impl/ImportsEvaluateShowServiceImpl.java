package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.SaasOrgQueryEnum;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.logic.RoleLogic;
import com.hailiang.manager.ReportCategoryConfigManager;
import com.hailiang.manager.ReportSubjectConfigManager;
import com.hailiang.manager.ReportTargetBusinessMergeManager;
import com.hailiang.manager.SubjectEvaluationDimConfigManager;
import com.hailiang.manager.SubjectInfoManager;
import com.hailiang.model.dto.response.ModuleCommonResponse;
import com.hailiang.model.dto.response.SubjectCommonResponse;
import com.hailiang.model.dto.response.evaluate.imports.ImportsEvaluateTableResponse;
import com.hailiang.model.dto.response.evaluate.imports.ImportsEvaluateTemplateOptionResponse;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.dto.target.TargetGroupInfoResponse;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.report.entity.SubjectEvaluationDimConfigPO;
import com.hailiang.model.report.entity.SubjectEvaluationDimSubjectConfigPO;
import com.hailiang.model.report.entity.SubjectEvaluationDimTargetBusinessMergePO;
import com.hailiang.model.report.entity.SubjectInfoPO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.role.ResRoleQueryDTO;
import com.hailiang.remote.saas.pojo.role.ResRoleInfoPojo;
import com.hailiang.remote.saas.vo.role.ResStaffRoleVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasClassManager;
import com.hailiang.saas.model.vo.ClassSubjectDetailVO;
import com.hailiang.saas.model.vo.staff.ClassTeacherInfoVO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.ImportsEvaluateService;
import com.hailiang.service.ImportsEvaluateShowService;
import com.hailiang.service.TargetGroupService;
import com.hailiang.service.TargetTemplateService;
import com.hailiang.util.WebUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 点评导入菜单显示功能服务
 *
 * @Description: 点评导入菜单显示功能服务
 * @Author: Jovi
 * @Date: Created in 2024-12-02
 * @Version: 2.0.0
 */
@Service
@Slf4j
public class ImportsEvaluateShowServiceImpl implements ImportsEvaluateShowService {

    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private RoleLogic roleLogic;

    @Resource
    private SaasClassManager saasClassManager;

    @Resource
    private SubjectInfoManager subjectInfoManager;

    @Resource
    private ImportsEvaluateService importsEvaluateService;

    @Resource
    private SubjectEvaluationDimConfigManager subjectEvaluationDimConfigManager;

    @Resource
    private ReportCategoryConfigManager reportCategoryConfigManager;

    @Resource
    private ReportTargetBusinessMergeManager reportTargetBusinessMergeManager;

    @Resource
    private TargetGroupService targetGroupService;

    @Resource
    private ReportSubjectConfigManager reportSubjectConfigManager;

    @Resource
    private TargetTemplateService targetTemplateService;

    @Resource
    private BasicInfoService basicInfoService;

    @Override
    public List<ImportsEvaluateTableResponse> listTableColumnByTargetId(Long targetId) {

        List<ImportsEvaluateTableResponse> responsesList = new ArrayList<>();

        List<TemplateInfoSaveDTO> filteredNormalTemplateInfoSaveDTOList = importsEvaluateService.listNormalSubmitInfoByTargetId(
                targetId);

        for (TemplateInfoSaveDTO templateInfoSaveDTO : filteredNormalTemplateInfoSaveDTOList) {

            ImportsEvaluateTableResponse response = new ImportsEvaluateTableResponse();
            response.setControlId(templateInfoSaveDTO.getKey());
            response.setControlName(templateInfoSaveDTO.getName());
            String type = templateInfoSaveDTO.getType();

            response.setControlType(type);

            response.setIsRequired(templateInfoSaveDTO.getRequired());
            if (SubmitInfoTypeEnum.SCORE.getText().equals(type)) {
                response.setIsRequired(Boolean.TRUE);
            }
            response.setIsScore(templateInfoSaveDTO.getIsScore());
            response.setScore(templateInfoSaveDTO.getScore());

            if (!SubmitInfoTypeEnum.isCheckSubmitInfo(type)) {
                responsesList.add(response);
                continue;
            }

            dealCheckControl(templateInfoSaveDTO, response, responsesList);
        }

        return responsesList;

    }

    /**
     * 处理单选或者多选类型控件
     *
     * @param templateInfoSaveDTO
     * @param response
     * @param responses
     */
    private void dealCheckControl(TemplateInfoSaveDTO templateInfoSaveDTO,
                                  ImportsEvaluateTableResponse response,
                                  List<ImportsEvaluateTableResponse> responses) {

        List<ImportsEvaluateTemplateOptionResponse> optionsResponses = new ArrayList<>();

        TemplateInfoSaveDTO.InnerSubmitOptionInfoSave optionInfoSave = templateInfoSaveDTO.getOptions();

        optionInfoSave.getOptions().forEach(option -> {
            ImportsEvaluateTemplateOptionResponse optionResponse = new ImportsEvaluateTemplateOptionResponse();
            optionResponse.setOptionId(option.getKey());
            optionResponse.setOptionName(option.getLabel());

            if (templateInfoSaveDTO.getIsScore() != null && templateInfoSaveDTO.getIsScore()) {
                optionResponse.setScore(option.getValue());
            }

            optionsResponses.add(optionResponse);
        });

        response.setOptions(optionsResponses);

        responses.add(response);
    }


    @Override
    public List<ModuleCommonResponse> listModules() {

        List<ModuleCommonResponse> responses = new ArrayList<>();

        ModuleEnum.moduleMap.forEach((moduleCode, moduleName) ->
                responses.add(new ModuleCommonResponse(moduleCode, moduleName)));

        return responses;
    }

    @Override
    public List<SubjectCommonResponse> listSubject(String sectionCode,
                                                   String campusSectionId,
                                                   String gradeId,
                                                   List<Long> classIds) {

        Long staffIdLong = WebUtil.getStaffIdLong();

        List<String> rolesCodeList = getRolesCodeListByStaffId(staffIdLong);

        List<SubjectCommonResponse> subjectCommonResponses = new ArrayList<>();

        // 如果是管理员角色，则直接查学段下所有学科信息
        if (roleLogic.isInMasterRole(rolesCodeList)) {

            List<SubjectInfoPO> subjectInfoPOS = subjectInfoManager.listCourseSubjectBySectionCodeAndCampus(
                    WebUtil.getTenantId(),
                    WebUtil.getSchoolId(),
                    WebUtil.getCampusId(), sectionCode);
            if (CollUtil.isEmpty(subjectInfoPOS)) {
                log.warn("该学段下没有任何学科信息！campusId:【{}】,sectionCode:【{}】", WebUtil.getCampusId(), sectionCode);
                return subjectCommonResponses;
            }

            for (SubjectInfoPO subjectInfoPO : subjectInfoPOS) {

                SubjectCommonResponse subjectCommonResponse = SubjectCommonResponse
                        .builder()
                        .subjectId(Convert.toLong(subjectInfoPO.getSubjectId()))
                        .subjectCode(subjectInfoPO.getSubjectCode())
                        .subjectName(subjectInfoPO.getSubjectName())
                        .build();
                subjectCommonResponses.add(subjectCommonResponse);
            }
        }
        //如果普通角色，则需要根据班级ID集合 和 教职工ID 获取所有任教课程的交集
        else {
            subjectCommonResponses = filterSubjectByClassIdAndSubjectAndTargetId(classIds, staffIdLong, sectionCode,
                    campusSectionId, gradeId);
        }

        return subjectCommonResponses;
    }


    /**
     * 根据班级ID集合 和 教职工ID 获取所有任教课程的交集
     *
     * @param classIds
     * @param staffIdLong
     * @return
     */
    private List<SubjectCommonResponse> filterSubjectByClassIdAndSubjectAndTargetId(List<Long> classIds,
                                                                                    Long staffIdLong,
                                                                                    String sectionCode,
                                                                                    String campusSectionId,
                                                                                    String gradeId) {

        // 查询班级老师信息
        List<ClassTeacherInfoVO> classTeacherInfoVOS = saasClassManager.queryClassTeacherInfo(classIds);

        if (CollUtil.isEmpty(classTeacherInfoVOS)) {
            throw new BizException("所选班级下无任教信息");
        }

        Map<Long, SubjectCommonResponse> subjectCommonResponseMap = new HashMap<>();

        // 1、遍历查询到的所有班级老师信息，并计算所有班级任教课程的交集
        Set<Long> intersectionSubjectIds = singleClass(staffIdLong,
                classTeacherInfoVOS,
                subjectCommonResponseMap);

        List<SubjectCommonResponse> subjectCommonResponses = new ArrayList<>();

        if (CollUtil.isEmpty(intersectionSubjectIds)) {
            log.warn("该登录教职工在所选班级中任教没有交集");
            return subjectCommonResponses;
        }

        // 2、指标在该学段下配置的课程
        Set<Long> subjectIds = this.filterSubjectWithDimConfigSubject(sectionCode, campusSectionId, gradeId);
        intersectionSubjectIds.retainAll(subjectIds);

        for (Long intersectionSubjectId : intersectionSubjectIds) {
            subjectCommonResponses.add(subjectCommonResponseMap.get(intersectionSubjectId));
        }

        return subjectCommonResponses;
    }

    /**
     * @param staffIdLong
     * @param classTeacherInfoVOS
     * @param subjectCommonResponseMap
     * @return
     */
    private Set<Long> singleClass(Long staffIdLong,
                                  List<ClassTeacherInfoVO> classTeacherInfoVOS,
                                  Map<Long, SubjectCommonResponse> subjectCommonResponseMap) {

        //交集课程ID集合
        Set<Long> intersectionSubjectIds = new HashSet<>();

        //1、遍历所有班级
        for (ClassTeacherInfoVO classTeacherInfoVO : classTeacherInfoVOS) {

            List<ClassTeacherInfoVO.ClassTeacher> teacherList = classTeacherInfoVO.getTeacherList();

            if (CollUtil.isEmpty(teacherList)) {
                log.warn("所选班级下无任教课程信息，班级信息：【{}】", classTeacherInfoVO);
                continue;
            }

            Set<Long> singleClassSubjectIds = new HashSet<>();

            singleTeacher(staffIdLong, subjectCommonResponseMap, teacherList, singleClassSubjectIds);

            //每个班级处理完成后，如果单个班级没有该学科，则跳过该班级，有就判断是否存在交集
            if (CollUtil.isEmpty(singleClassSubjectIds)) {
                continue;
            }

            if (CollUtil.isEmpty(intersectionSubjectIds)) {
                intersectionSubjectIds = new HashSet<>(singleClassSubjectIds);
            } else {
                intersectionSubjectIds.retainAll(singleClassSubjectIds);
            }

        }

        return intersectionSubjectIds;
    }

    /**
     * 2、遍历该班级下所有任教老师，由于一个课程只能一个老师任教，所以只要有一个老师任教该学科，就算该班级有该学科了
     *
     * @param staffIdLong
     * @param subjectCommonResponseMap
     * @param teacherList
     * @param singleClassSubjectIds
     */
    private void singleTeacher(Long staffIdLong,
                               Map<Long, SubjectCommonResponse> subjectCommonResponseMap,
                               List<ClassTeacherInfoVO.ClassTeacher> teacherList,
                               Set<Long> singleClassSubjectIds) {

        for (ClassTeacherInfoVO.ClassTeacher classTeacher : teacherList) {

            List<ClassSubjectDetailVO> subjectList = classTeacher.getSubjectList();

            if (!Objects.equals(classTeacher.getStaffId(), staffIdLong)) {
                log.warn("所选班级下无任教课程信息，无该登录教职工任教信息：【{}】，staffIdLong:【{}】", classTeacher,
                        staffIdLong);
                continue;
            }

            if (CollUtil.isEmpty(subjectList)) {
                log.warn("所选班级下无任教课程信息，班级任教信息：【{}】", classTeacher);
                continue;
            }

            singleSubject(subjectCommonResponseMap,
                    singleClassSubjectIds,
                    subjectList);
        }
    }

    /**
     * 3、遍历该班级下任教老师的所有科目
     *
     * @param subjectCommonResponseMap
     * @param singleClassSubjectIds
     * @param subjectList
     */
    private void singleSubject(Map<Long, SubjectCommonResponse> subjectCommonResponseMap,
                               Set<Long> singleClassSubjectIds,
                               List<ClassSubjectDetailVO> subjectList) {

        for (ClassSubjectDetailVO classSubjectDetailVO : subjectList) {
            Long subjectId = classSubjectDetailVO.getSubjectId();
            singleClassSubjectIds.add(subjectId);

            SubjectCommonResponse subjectCommonResponse = new SubjectCommonResponse(subjectId,
                    classSubjectDetailVO.getSubjectCode(),
                    classSubjectDetailVO.getSubjectName());

            subjectCommonResponseMap.put(subjectId, subjectCommonResponse);
        }
    }

    /**
     * 课程id过滤
     *
     * @param sectionCode
     * @return
     */
    private Set<Long> filterSubjectWithDimConfigSubject(String sectionCode,
                                                        String campusSectionId,
                                                        String gradeId) {

        // 校区下该学段的科目课程信息
        List<SubjectInfoPO> subjectInfoPOList = subjectInfoManager.listSubjectBySectionCode(sectionCode);

        if (CollUtil.isEmpty(subjectInfoPOList)) {
            log.warn("【批量导入点评数据】-【当前学段课程科目信息为空】");
            return new HashSet<>();
        }

        //获取学期
        List<TermVo> termVos = basicInfoService.queryTermList(campusSectionId);

        Optional<TermVo> currentSemesterOpt = termVos
                .stream()
                .filter(TermVo::isCurrentTerm)
                .findFirst();

        TermVo termVo;

        if (currentSemesterOpt.isPresent()) {
            termVo = currentSemesterOpt.get();
        } else {
            log.warn("【批量导入点评数据】-【当前学年信息为空】");
            return new HashSet<>();
        }

        // 维度配置
        SubjectEvaluationDimConfigPO subjectEvaluationDimConfigPO = subjectEvaluationDimConfigManager.getSubjectEvaluationDimConfig(
                WebUtil.getTenantId(),
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                termVo.getSchoolYear(),
                termVo.getTermName(),
                campusSectionId,
                gradeId);

        if (Objects.isNull(subjectEvaluationDimConfigPO)) {
            log.warn("【批量导入点评数据】-【当前学段维度配置为空】");
            return new HashSet<>();
        }
        Long dimId = subjectEvaluationDimConfigPO.getId();

        // 科目课程配置
        List<SubjectEvaluationDimSubjectConfigPO> subjectEvaluationDimSubjectConfigPOS = reportSubjectConfigManager.queryByDimId(dimId);
        if (CollUtil.isEmpty(subjectEvaluationDimSubjectConfigPOS)){
            log.warn("【批量导入点评数据】-【当前学段维度配置关联学科为空】");
            return new HashSet<>();
        }

        return subjectEvaluationDimSubjectConfigPOS
                .stream()
                .map(item -> Convert.toLong(item.getSubjectId())).collect(Collectors.toSet());
        //V2.5.8迭代作废以下代码，不在根据指标过滤

//        // 过滤出阶段性评价的配置
//        List<SubjectEvaluationDimCategoryConfigPO> subjectEvaluationDimCategoryConfigPOS = reportCategoryConfigManager.queryByDimId(
//                dimId);
//        subjectEvaluationDimCategoryConfigPOS = subjectEvaluationDimCategoryConfigPOS
//                .stream()
//                .filter(item -> Objects.equals(item.getCategoryType(), 3))
//                .collect(Collectors.toList());
//        // 按照科目课程id分组
//        Map<Long, Long> categoryCinfigMap = subjectEvaluationDimCategoryConfigPOS
//                .stream()
//                .collect(Collectors.toMap(SubjectEvaluationDimCategoryConfigPO::getId,
//                        SubjectEvaluationDimCategoryConfigPO::getSubjectId, (k1, k2) -> k1));
//        if (CollUtil.isEmpty(subjectEvaluationDimSubjectConfigPOS) || CollUtil.isEmpty(
//                subjectEvaluationDimCategoryConfigPOS)) {
//            log.warn("【批量导入点评数据】-【当前学段学科维度配置为空】");
//            return new HashSet<>();
//        }
//
//        // 获取指标对应分组五育信息
//        List<TargetGroupInfoResponse> targetGroupInfoResponses = targetGroupService.listTargetGroupInfoByTargetIds(
//                Collections.singletonList(targetId));
//        if (CollUtil.isEmpty(targetGroupInfoResponses)) {
//            log.warn("【批量导入点评数据】-【根据指标id找不到分组】");
//            return new HashSet<>();
//        }
//        TargetGroupInfoResponse targetGroupInfoResponse = targetGroupInfoResponses.get(0);
//
//        // 指标配置关联数据
//        List<SubjectEvaluationDimTargetBusinessMergePO> subjectEvaluationDimTargetBusinessMergePOS = reportTargetBusinessMergeManager.queryByDimId(
//                dimId);
//        if (CollUtil.isEmpty(subjectEvaluationDimTargetBusinessMergePOS)) {
//            log.warn("【批量导入点评数据】-【学科维度配置未配置指标选项】");
//            return new HashSet<>();
//        }
//
//        // 阶段性评价id对应指标选项配置
//        Map<Long, List<SubjectEvaluationDimTargetBusinessMergePO>> reportTargetMap = subjectEvaluationDimTargetBusinessMergePOS
//                .stream()
//                .collect(Collectors.groupingBy(SubjectEvaluationDimTargetBusinessMergePO::getBusinessId));
//
//        TargetTemplate targetTemplate = targetTemplateService.getByTargetId(targetId);
//        // 学科维度配置配置了对应指标的科目课程
//        Set<Long> subjectIds = new HashSet<>();
//        // 组装科目课程id列表
//        this.buildSubjectIds(reportTargetMap,
//                targetGroupInfoResponse,
//                categoryCinfigMap,
//                subjectConfigMap,
//                subjectInfoPOList,
//                targetTemplate,
//                subjectIds);
//
//        // 过滤结果
//        return subjectIds;
    }

    /**
     * 组装科目课程id列表
     *
     * @param reportTargetMap
     * @param categoryCinfigMap
     * @param subjectConfigMap
     * @param subjectInfoPOList
     * @param subjectIds
     */
    private void buildSubjectIds(Map<Long, List<SubjectEvaluationDimTargetBusinessMergePO>> reportTargetMap,
                                 TargetGroupInfoResponse targetGroupInfoResponse,
                                 Map<Long, Long> categoryCinfigMap,
                                 Map<Long, SubjectEvaluationDimSubjectConfigPO> subjectConfigMap,
                                 List<SubjectInfoPO> subjectInfoPOList,
                                 TargetTemplate targetTemplate,
                                 Set<Long> subjectIds) {
        reportTargetMap.forEach((k, v) -> {
            for (SubjectEvaluationDimTargetBusinessMergePO subjectEvaluationDimTargetBusinessMergePO : v) {
                boolean checkTarget = this.checkTarget(targetGroupInfoResponse,
                        subjectEvaluationDimTargetBusinessMergePO.getSubmitType(),
                        subjectEvaluationDimTargetBusinessMergePO.getSubmitId(),
                        targetTemplate);
                if (checkTarget) {
                    Long id = categoryCinfigMap.get(k);
                    SubjectEvaluationDimSubjectConfigPO subjectEvaluationDimSubjectConfigPO = subjectConfigMap.get(id);
                    if (Objects.isNull(subjectEvaluationDimSubjectConfigPO)) {
                        continue;
                    }
                    // 学科维度配置的科目
                    if (Objects.equals(subjectEvaluationDimSubjectConfigPO.getSubjectType(), 2)) {
                        String subjectId = subjectEvaluationDimSubjectConfigPO.getSubjectId();
                        // 科目对应课程列表
                        List<Long> reportSubjectInfos = subjectInfoPOList.stream()
                                .filter(item -> Objects.equals(item.getParentDisciplineId(), subjectId)
                                        && Objects.equals(item.getSubjectType(), 1))
                                .map(SubjectInfoPO::getSubjectId)
                                .map(Convert::toLong)
                                .distinct()
                                .collect(Collectors.toList());
                        subjectIds.addAll(reportSubjectInfos);
                    } else {
                        subjectIds.add(Convert.toLong(subjectEvaluationDimSubjectConfigPO.getSubjectId()));
                    }
                }
            }
        });
    }

    /**
     * 校验五育，分组，指标，点评项是否能匹配上
     *
     * @param submitType
     * @param submitId
     * @param targetTemplate
     * @return
     */
    private boolean checkTarget(TargetGroupInfoResponse targetGroupInfoResponse,
                                Integer submitType,
                                String submitId,
                                TargetTemplate targetTemplate) {
        // 五育
        if (Objects.equals(submitType, 1)) {
            return Objects.equals(Convert.toStr(targetGroupInfoResponse.getModuleCode()), submitId);
        }
        // 分组
        if (Objects.equals(submitType, 2)) {
            return Objects.equals(Convert.toStr(targetGroupInfoResponse.getGroupId()), submitId);
        }
        // 指标
        if (Objects.equals(submitType, 3)) {
            return Objects.equals(Convert.toStr(targetGroupInfoResponse.getTargetId()), submitId);
        }
        // 选项
        if (Objects.equals(submitType, 4)) {
            for (TemplateInfoSaveDTO templateInfoSaveDTO : targetTemplate.getTemplateInfoList()) {
                if (Objects.equals(templateInfoSaveDTO.getType(), SubmitInfoTypeEnum.MULTI_CHECK.getText())
                        || Objects.equals(templateInfoSaveDTO.getType(), SubmitInfoTypeEnum.SINGLE_CHECK.getText())) {
                    for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave option : templateInfoSaveDTO.getOptions()
                            .getOptions()) {
                        if (Objects.equals(option.getKey(), submitId)) {
                            return true;
                        }
                    }
                } else if (Objects.equals(templateInfoSaveDTO.getType(), SubmitInfoTypeEnum.DETAIL.getText())) {
                    // 明细
                    for (LinkedHashMap linkedHashMap : templateInfoSaveDTO.getList()) {
                        if (Objects.isNull(linkedHashMap.get("options"))) {
                            continue;
                        }
                        if (!Lists.newArrayList(SubmitInfoTypeEnum.SINGLE_CHECK.getText(),
                                        SubmitInfoTypeEnum.MULTI_CHECK.getText())
                                .contains(Convert.toStr(linkedHashMap.get("type")))) {
                            continue;
                        }
                        HashMap innerOption = (HashMap) linkedHashMap.get("options");
                        List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> innerOptions = JSONUtil.parseArray(
                                        innerOption.get("options"))
                                .toList(TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave.class);
                        if (CollUtil.isNotEmpty(innerOptions)) {
                            for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave option : innerOptions) {
                                if (Objects.equals(option.getKey(), submitId)) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * 根据教职工ID查询角色信息
     *
     * @param staffIdLong
     * @return
     */
    private List<String> getRolesCodeListByStaffId(Long staffIdLong) {
        ResRoleQueryDTO resRoleQueryDTO = new ResRoleQueryDTO();
        resRoleQueryDTO.setStaffIds(CollUtil.newArrayList(staffIdLong));
        resRoleQueryDTO.setId(WebUtil.getSchoolIdLong());
        resRoleQueryDTO.setType(SaasOrgQueryEnum.SCHOOL_ID.getCode());
        List<ResStaffRoleVO> resStaffRoleVOList = basicInfoRemote.queryStaffRoleList(resRoleQueryDTO);

        if (CollUtil.isEmpty(resStaffRoleVOList)) {
            log.warn("根据当前教职工ID，查询角色失败，入参信息：【{}】", resRoleQueryDTO);
            throw new BizException("根据当前教职工ID，查询角色失败");
        }

        List<ResRoleInfoPojo> roles = resStaffRoleVOList.get(0).getRoles();

        if (CollUtil.isEmpty(roles)) {
            log.warn("根据当前教职工ID，查询角色失败，入参信息：【{}】", resRoleQueryDTO);
            throw new BizException("根据当前教职工ID，查询角色失败");
        }

        List<String> rolesCodeList = roles
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getRoleCode()))
                .map(ResRoleInfoPojo::getRoleCode)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(rolesCodeList)) {
            log.warn("根据当前教职工ID，查询角色失败，入参信息：【{}】", resRoleQueryDTO);
            throw new BizException("根据当前教职工ID，查询角色失败");
        }

        return rolesCodeList;
    }


}
