package com.hailiang.service;

import com.hailiang.model.dto.QueryClassSubjectRelDTO;
import com.hailiang.model.dto.internaldrive.modify.InfoModifyDTO;
import com.hailiang.model.dto.motivate.BehaviourPointExchangeDTO;
import com.hailiang.model.dto.motivate.BehaviourPointExchangeMessageDTO;
import com.hailiang.model.dto.query.GetEvaluateInfoDetailQueryDTO;
import com.hailiang.model.dto.remove.InfoRemoveDTO;
import com.hailiang.model.dto.save.ApprovalHandleDTO;
import com.hailiang.model.dto.save.BehaviourRecordHandleDTO;
import com.hailiang.model.dto.save.InfoSaveDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.mongo.Info;
import com.hailiang.model.vo.GetEvaluateInfoDetailVO;
import com.hailiang.model.vo.StudentInfoVO;
import com.hailiang.remote.saas.vo.educational.EduStaffTeachClassVO;

import java.util.Date;
import java.util.List;

public interface InfoService {


    // 提交评估任务详情

    Info saveTeacherValuateInfo(InfoSaveDTO dto, Date operateTime, Boolean isSpeedEvaluate,Boolean isSportTarget);

    // 编辑评估任务详情
    Boolean modifyEvaluateInfo(InfoModifyDTO dto, Boolean isSportTarget);

    void revaluate(String campusSectionId,String gradeId, String schoolYear, String termName, Date submitTime, Integer businessType);

    // 删除评估任务详情
    Boolean removeEvaluateInfo(InfoRemoveDTO dto);

    // 老师查询详情
    GetEvaluateInfoDetailVO getEvaluateInfoDetail(GetEvaluateInfoDetailQueryDTO dto);

    // 学生查询详情
    GetEvaluateInfoDetailVO getStudentEvaluateDetail(GetEvaluateInfoDetailQueryDTO dto);

    Boolean checkAuth(String studentId, Date submitTime);

    Info saveParentEvaluateInfo(InfoSaveDTO dto);

    Boolean modifyParentEvaluateInfo(InfoModifyDTO dto);

    Boolean removeParentEvaluateInfo(InfoRemoveDTO dto);

    void approvalParentEvaluateInfo(ApprovalHandleDTO dto);

    StudentInfoVO getStudentInfoByStudentNoAndName();

    Info saveStudentEvaluateInfo(InfoSaveDTO dto);

    void approvalStudentEvaluateInfo(ApprovalHandleDTO dto);

    List<EduStaffTeachClassVO> getRelatedSubject(QueryClassSubjectRelDTO dto, String schoolId, String staffId);

    /**
     * 行为记录新增/修改 影响活动任务
     *
     * @param behaviourRecordHandleDTO
     * @return
     */
    Boolean handleActivity(BehaviourRecordHandleDTO behaviourRecordHandleDTO);

    /**
     * 组装积分金币转换mq
     *
     * @param records
     * @param planId
     * @param operateType
     * @return
     */
    List<BehaviourPointExchangeDTO> assemblyPointExchangeMq(List<BehaviourRecord> records,
                                                             Long planId,
                                                             Integer operateType,
                                                             Integer businessType);

    /**
     * 组装积分金币转换mq(需要subjectCode)
     *
     * @param records
     * @param planId
     * @param subjectCode
     * @param operateType
     * @return
     */
    List<BehaviourPointExchangeDTO> assemblyPointExchangeAllMq(List<BehaviourRecord> records,
                                                                Long planId,
                                                                String subjectCode,
                                                                Integer operateType,
                                                                Integer businessType);

//    void fixEvaluateData();
}