package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.query.GetNextPushTimeQueryDTO;
import com.hailiang.model.dto.save.ReportPushRuleSaveDTO;
import com.hailiang.model.entity.ReportPushRule;
import com.hailiang.model.vo.ReportPushRuleVO;

import java.util.List;

/**
 * 报告推送规则表(ReportPushRule)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-13 11:30:31
 */
public interface ReportPushRuleService extends IService<ReportPushRule> {
    /**
     * 保存报告推送规则
     */
    Boolean saveRule(ReportPushRuleSaveDTO reportPushRuleSaveDTO);

    /**
     * 查询报告推送规则详情
     */
    ReportPushRuleVO detailRule();

    List<ReportPushRule> listByCampusIds(List<String> campusIds);

    /**
     * 获取当前校区下点评记录是否需要显示点评人
     * @return
     */
    Boolean getShowAppraisalFlag(String campusId);
}

