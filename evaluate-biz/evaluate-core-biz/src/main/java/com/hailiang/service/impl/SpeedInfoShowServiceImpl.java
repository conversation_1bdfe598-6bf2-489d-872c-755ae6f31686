package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.enums.*;
import com.hailiang.enums.speed.AppTypeEnum;
import com.hailiang.enums.speed.SpeedApplyLevelEnum;
import com.hailiang.internal.InternalDriveRemote;
import com.hailiang.internal.model.request.MessagePlanRequest;
import com.hailiang.internal.model.request.PlanInitialRequest;
import com.hailiang.internal.model.request.PlanListsRequest;
import com.hailiang.internal.model.request.PlanTagListsRequest;
import com.hailiang.internal.model.response.PlanCommentListsResponse;
import com.hailiang.internal.model.response.PlanListsResponse;
import com.hailiang.internal.model.response.PlanTagListsResponse;
import com.hailiang.manager.SpeedConfigManager;
import com.hailiang.manager.SpeedTargetGroupRTargetManager;
import com.hailiang.mapper.mongo.TargetTemplateDao;
import com.hailiang.model.dto.request.IdListRequest;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.entity.SpeedConfigPO;
import com.hailiang.model.entity.SpeedTargetGroupRTargetPO;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.response.speed.SpeedConfigResponse;
import com.hailiang.model.response.speed.SpeedGroupDetailResponse;
import com.hailiang.model.response.speed.SpeedGroupTargetResponse;
import com.hailiang.model.vo.GetEvaluateTaskDetailVO;
import com.hailiang.model.vo.ListAllEvaluateTargetVOInnerTarget;
import com.hailiang.model.vo.ListAllEvaluateTargetVOModule;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduClassQueryDTO;
import com.hailiang.remote.saas.vo.educational.EduClassInfoVO;
import com.hailiang.response.GroupResponse;
import com.hailiang.saas.SaasClassManager;
import com.hailiang.saas.SaasSchoolCacheManager;
import com.hailiang.saas.model.vo.educational.TchClassOpenV2VO;
import com.hailiang.service.*;
import com.hailiang.util.WebUtil;
import java.util.Map.Entry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 极速点评展示服务
 *
 * @Description: 极速点评展示服务
 * @Author: JiangJunLi
 * @Date: Created in 2024-01-26
 * @Version: 1.6.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
@RefreshScope
public class SpeedInfoShowServiceImpl implements SpeedInfoShowService {
    private final TargetService targetService;
    private final SpeedTargetGroupRTargetManager speedTargetGroupRTTargetManager;
    private final TargetTemplateDao targetTemplateDao;
    private final TargetGroupService targetGroupService;
    private final SpeedConfigManager speedConfigManager;
    private final BehaviourRecordService behaviourRecordService;
    private final SpeedConfigService speedConfigService;
    private final InternalDriveRemote internalDriveRemote;
    private final SpeedConfigSetUpShowService speedConfigSetUpShowService;
    private final SaasClassManager saasClassManager;
    private final BasicInfoRemote basicInfoRemote;
    private final SaasSchoolCacheManager saasSchoolCacheManager;

    @Resource
    private RedisUtil redisUtil;

    @Value("${third.xdl.saasAppId}")
    private Long xdlSaasAppId;
    /**
     * 极速点评分组及详情
     *
     * @return
     */
    @Override
    public List<SpeedGroupDetailResponse> listSpeedTargetGroupDetail(PlanTagListsRequest request) {
        List<SpeedGroupDetailResponse> speedGroupDetailResponses = new ArrayList<>();

        String campusId = WebUtil.getCampusId();
        String staffId = WebUtil.getStaffId();
        //极速点评分组
        SpeedConfigResponse speedConfigResponse = speedConfigService.getOneByCampusIdAndStaffId(campusId, staffId);


        List<GroupResponse> groupResponses = speedConfigResponse.getGroupResponses();
        if (CollUtil.isEmpty(groupResponses)) {
            log.warn("【极速点评分组及详情】-【分组为空】,直接返回,校区 id：【{}】", WebUtil.getCampusId());
            //积分板下的分组及点评项
            this.fillResponsesWithPlanTags(request, speedGroupDetailResponses);
            return speedGroupDetailResponses;
        }
        //分组 id
        List<Long> speedTargetGroupIds =
                groupResponses.stream().map(GroupResponse::getGroupId).distinct().collect(Collectors.toList());
        //分组下的点评项
        List<SpeedTargetGroupRTargetPO> speedTargetGroupRTargetPOS =
                speedTargetGroupRTTargetManager.listByGroupIds(speedTargetGroupIds, AppTypeEnum.MOBILE.getCode());
        if (CollUtil.isEmpty(speedTargetGroupRTargetPOS)) {
            log.warn("【极速点评分组及详情】-【点评项为空】,直接返回分组,speedTargetGroupIds:【{}】", speedTargetGroupIds);
            //积分板下的分组及点评项
            this.fillResponsesWithPlanTags(request, speedGroupDetailResponses);
            this.listResponse(groupResponses, speedGroupDetailResponses);
            return speedGroupDetailResponses;
        }

        Map<Long, List<SpeedTargetGroupRTargetPO>> groupIdListMap = CollStreamUtil.groupByKey(speedTargetGroupRTargetPOS
                , SpeedTargetGroupRTargetPO::getSpeedTargetGroupId);

        //封装数据
        this.listSpeedGroupDetailResponses(speedTargetGroupRTargetPOS, groupResponses, speedGroupDetailResponses,
                groupIdListMap, request);

        //排序
        this.sortSpeedGroupDetailResponses(speedGroupDetailResponses, staffId);
        speedGroupDetailResponses.forEach(speedGroupDetailResponse -> speedGroupDetailResponse
                .setApplyLevel(SpeedApplyLevelEnum.SCHOOL.getCode()));

        //积分板下的分组及点评项
        this.fillResponsesWithPlanTags(request, speedGroupDetailResponses);

        //处理分值控件
        this.handleScore(speedGroupDetailResponses);

        if (!CollUtil.isEmpty(speedGroupDetailResponses)) {
            for (SpeedGroupDetailResponse speedGroupDetailResponse : speedGroupDetailResponses) {
                List<SpeedGroupTargetResponse> speedGroupTargetResponses = speedGroupDetailResponse.getSpeedGroupTargetResponses();

                Set<String> applyLevelSet = new HashSet<>();
                if (!CollUtil.isEmpty(speedGroupTargetResponses)) {
                    for (SpeedGroupTargetResponse speedGroupTargetResponse : speedGroupTargetResponses) {
                        if (!applyLevelSet.contains(speedGroupTargetResponse.getApplyLevel())) {
                            applyLevelSet.add(speedGroupTargetResponse.getApplyLevel());
                        }
                    }
                }

                if (applyLevelSet.size() != 0 && applyLevelSet.size() > 1) {
                    speedGroupDetailResponse.setIsShowTargetApplyLevel(true);
                } else {
                    speedGroupDetailResponse.setIsShowTargetApplyLevel(false);
                }
            }
        }


        return speedGroupDetailResponses;
    }

    /**
     * 处理分值控件
     *
     * @param speedGroupDetailResponses
     */
    private void handleScore(List<SpeedGroupDetailResponse> speedGroupDetailResponses) {
        for (SpeedGroupDetailResponse speedGroupDetailResponse : speedGroupDetailResponses) {
            if (CollUtil.isEmpty(speedGroupDetailResponse.getSpeedGroupTargetResponses())) {
                continue;
            }
            for (SpeedGroupTargetResponse speedGroupTargetResponse : speedGroupDetailResponse.getSpeedGroupTargetResponses()) {
                if (Objects.equals(speedGroupTargetResponse.getType(), SubmitInfoTypeEnum.SCORE.getText())) {
                    speedGroupTargetResponse.setScoreValue(speedGroupTargetResponse.getScore());
                    speedGroupTargetResponse.setScoreName(speedGroupTargetResponse.getTargetName());
                }
            }
        }
    }

    /**
     * 积分板下的分组及点评项
     */
    private void fillResponsesWithPlanTags(PlanTagListsRequest request,
                                           List<SpeedGroupDetailResponse> speedGroupDetailResponses) {
        Long schoolId = WebUtil.getSchoolIdLong();
        Boolean openFlag = saasSchoolCacheManager.checkAppIsOpen(schoolId, xdlSaasAppId);
        if (!openFlag) {
            log.warn("【当前学校未开通星动力，不填充积分板】，schoolId：{}", schoolId);
            return;
        }
        if (ObjectUtil.isNull(request.getPlanId())) {
            log.warn("【极速点评分组及详情】【积分板下的分组及点评项】【planId为空】, 直接返回, request：【{}】"
                    , JSONUtil.toJsonStr(request));
            return;
        }

        //积分板下的分组及点评项
        List<PlanTagListsResponse> planTagListsResponses = internalDriveRemote.queryPlanTagLists(request);
        if (CollUtil.isEmpty(planTagListsResponses)) {
            log.warn("【极速点评分组及详情】【积分板下的分组及点评项为空】, 直接返回, request：【{}】"
                    , JSONUtil.toJsonStr(request));
            return;
        }

        for (PlanTagListsResponse planTagListsResponse : planTagListsResponses) {
            //师级
            if (this.buildGroupDetailWithPlanTag(speedGroupDetailResponses, planTagListsResponse)) {
                continue;
            }
            //校级
            this.fillGroupDetailWithPlanTag(speedGroupDetailResponses, planTagListsResponse);
        }
    }

    /**
     * 校级分组下加入师级点评
     */
    private void fillGroupDetailWithPlanTag(List<SpeedGroupDetailResponse> speedGroupDetailResponses,
                                            PlanTagListsResponse planTagListsResponse) {
        if (BeanUtil.isEmpty(planTagListsResponse) || CollUtil.isEmpty(planTagListsResponse.getPlanCommentList())) {
            log.warn("【极速点评分组及详情】-【校级分组下加入师级点评】，入参为空直接返回,planTagListsResponse:{},speedGroupDetailResponses：{}"
                    , JSONUtil.toJsonStr(planTagListsResponse), JSONUtil.toJsonStr(speedGroupDetailResponses));
            return;
        }

        for (SpeedGroupDetailResponse speedGroupDetailResponse : speedGroupDetailResponses) {

            if (Convert.toLong(speedGroupDetailResponse.getSpeedGroupId()).equals(planTagListsResponse.getPlanTagId())) {

                for (PlanCommentListsResponse planComment : planTagListsResponse.getPlanCommentList()) {

                    SpeedGroupTargetResponse speedGroupTargetResponse = buildSpeedGroupTargetResponse(planComment,planTagListsResponse);
                    speedGroupDetailResponse.getSpeedGroupTargetResponses().add(speedGroupTargetResponse);
                }
            }
        }
    }

    /**
     * 加入师级分组点评和详情
     */
    private boolean buildGroupDetailWithPlanTag(List<SpeedGroupDetailResponse> speedGroupDetailResponses,
                                                PlanTagListsResponse planTagListsResponse) {

        List<PlanCommentListsResponse> planCommentList = planTagListsResponse.getPlanCommentList();

        if (BeanUtil.isEmpty(planTagListsResponse) || CollUtil.isEmpty(planCommentList)) {
            log.warn("【极速点评分组及详情】-【积分板下的分组及点评项】-【加入师级分组点评和详情】,直接返回,planTagListsResponse：【{}】"
                    , JSONUtil.toJsonStr(planTagListsResponse));
            return true;
        }

        if (SpeedApplyLevelEnum.GENERAL.getCode().equals(planTagListsResponse.getApplyLevel())) {

            SpeedGroupDetailResponse speedGroupDetailResponse = new SpeedGroupDetailResponse();

            List<SpeedGroupTargetResponse> speedGroupTargetResponses = new ArrayList<>();

            for (PlanCommentListsResponse planCommentListsResponse : planCommentList) {
                speedGroupTargetResponses.add(buildSpeedGroupTargetResponse(planCommentListsResponse,planTagListsResponse));
            }

            speedGroupDetailResponse.setSpeedGroupId(Convert.toStr(planTagListsResponse.getPlanTagId()));
            speedGroupDetailResponse.setSpeedGroupName(planTagListsResponse.getPlanTagName());
            speedGroupDetailResponse.setApplyLevel(planTagListsResponse.getApplyLevel());
            speedGroupDetailResponse.setSpeedGroupTargetResponses(speedGroupTargetResponses);
            speedGroupDetailResponses.add(speedGroupDetailResponse);

            return true;
        }
        return false;
    }

    private SpeedGroupTargetResponse buildSpeedGroupTargetResponse(PlanCommentListsResponse planCommentListsResponse,PlanTagListsResponse planTagListsResponse) {
        SpeedGroupTargetResponse speedGroupTargetResponse = new SpeedGroupTargetResponse();
        if (BeanUtil.isEmpty(planCommentListsResponse)) {
            return speedGroupTargetResponse;
        }
        speedGroupTargetResponse.setApplyLevel(planCommentListsResponse.getApplyLevel());
        speedGroupTargetResponse.setOptionId(planCommentListsResponse.getPlanCommentId());
        speedGroupTargetResponse.setOptionName(planCommentListsResponse.getContent());
        speedGroupTargetResponse.setScore(planCommentListsResponse.getScore());
        speedGroupTargetResponse.setModuleCode(planCommentListsResponse.getModuleCode());
        speedGroupTargetResponse.setTargetId(Convert.toStr(planTagListsResponse.getPlanTagId()));
        speedGroupTargetResponse.setTargetName(planTagListsResponse.getPlanTagName());
        speedGroupTargetResponse.setTargetSource(Constant.TWO);

        // 前端提交师标时作为多选类型提交
        speedGroupTargetResponse.setType("general");
        speedGroupTargetResponse.setTypeName("general");
        return speedGroupTargetResponse;
    }

    /**
     * 积分版列表
     */
    @Override
    public List<PlanListsResponse> listPlans(PlanListsRequest request) {
        Assert.notEmpty(request.getSaasClassIds(), "班级id不能为空");
        request.setStaffId(WebUtil.getStaffIdLong());
        request.setSaasSchoolId(WebUtil.getSchoolIdLong());
        List<PlanListsResponse> planListsResponses = internalDriveRemote.queryPlanLists(request);
        if (CollUtil.isEmpty(planListsResponses)) {
            log.warn("【积分版列表为空】, 直接返回, request：【{}】"
                    , JSONUtil.toJsonStr(request));
            return Collections.emptyList();
        }
        List<Long> classIds = planListsResponses
                .stream().map(PlanListsResponse::getSaasClassId).distinct().collect(Collectors.toList());

        List<TchClassOpenV2VO> classOpenV2VOS = saasClassManager.queryClassByIds(Convert.toList(String.class, classIds));
        planListsResponses.forEach(s -> {
            TchClassOpenV2VO tchClassOpenV2VO = classOpenV2VOS.stream()
                    .filter(t -> t.getId().equals(s.getSaasClassId()))
                    .findFirst().orElse(null);
            if (BeanUtil.isNotEmpty(tchClassOpenV2VO)) {
                s.setSaasClassName(tchClassOpenV2VO.getClassName());
                s.setGradeName(tchClassOpenV2VO.getGradeName());
                s.setCampusSectionName(SectionInfoEnum.getNameByCode(tchClassOpenV2VO.getSectionCode()));
            }
        });
        return planListsResponses;
    }

    @Override
    public PlanListsResponse initialPlan(PlanInitialRequest planListsRequest) {

        planListsRequest.setSaasCampusId(WebUtil.getCampusIdLong());
        planListsRequest.setTenantId(WebUtil.getTenantIdLong());
        planListsRequest.setSaasSchoolId(WebUtil.getSchoolIdLong());
        planListsRequest.setStaffId(WebUtil.getStaffIdLong());

        return internalDriveRemote.initialPlan(planListsRequest);
    }

    @Override
    public List<PlanListsResponse> listPlansByCampusId(MessagePlanRequest request) {
        Long staffId = request.getStaffId();
        Long campusId = request.getCampusId();
        Long schoolId = request.getSchoolId();
        EduClassQueryDTO dto = new EduClassQueryDTO();
        dto.setClassTypes(Arrays.stream(SaasClassTypeEnum.values())
                .map(SaasClassTypeEnum::getCode)
                .filter(item -> !Objects.equals(item, SaasClassTypeEnum.ALL.getCode()))
                .map(Convert::toStr).collect(Collectors.toList()));
        dto.setCampusId(campusId);
        dto.setSchoolId(schoolId);
        dto.setGraduationStatus(Constant.ZERO.toString());
        List<EduClassInfoVO> eduClassInfoVOS = basicInfoRemote.queryClassInfoList(dto);
        List<Long> classIds = eduClassInfoVOS.stream().map(EduClassInfoVO::getId).distinct().collect(Collectors.toList());

        PlanListsRequest planListsRequest = new PlanListsRequest();
        planListsRequest.setSaasClassIds(classIds);
        planListsRequest.setStaffId(staffId);
        planListsRequest.setSaasSchoolId(request.getSchoolId());
        List<PlanListsResponse> planListsResponses = internalDriveRemote.queryPlanLists(planListsRequest);
//        List<TchClassOpenV2VO> classOpenV2VOS = saasClassManager.queryClassByIdsAll(Convert.toList(String.class, classIds));
//        planListsResponses.forEach(s -> {
//            TchClassOpenV2VO tchClassOpenV2VO = classOpenV2VOS.stream()
//                    .filter(t -> t.getId().equals(s.getSaasClassId()))
//                    .findFirst().orElse(null);
//            if (BeanUtil.isNotEmpty(tchClassOpenV2VO)) {
//                s.setSaasClassName(tchClassOpenV2VO.getClassName());
//                s.setGradeName(tchClassOpenV2VO.getGradeName());
//                s.setCampusSectionName(SectionInfoEnum.getNameByCode(tchClassOpenV2VO.getSectionCode()));
//            }
//        });
//        return planListsResponses;
        List<PlanListsResponse> responses = new ArrayList<>();
        this.sortByGradeCodeAndClassNum(eduClassInfoVOS);
        eduClassInfoVOS.forEach(s -> {
            List<PlanListsResponse> listsResponses = planListsResponses
                    .stream()
                    .filter(p -> s.getId().equals(p.getSaasClassId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(listsResponses)) {
                listsResponses.forEach(planListsResponse -> {
                    planListsResponse.setSaasClassName(s.getClassName());
                    planListsResponse.setGradeName(s.getGradeName());
                    planListsResponse.setCampusSectionName(SectionInfoEnum.getNameByCode(s.getSectionCode()));
                    responses.add(planListsResponse);
                });
            }
        });
        return responses;
    }

    private void sortByGradeCodeAndClassNum(List<EduClassInfoVO> eduClassInfoVOS) {
        eduClassInfoVOS.sort((o1, o2) -> {
            int gradeComparison = o1.getGradeCode().compareTo(o2.getGradeCode());
            if (gradeComparison != 0) {
                return gradeComparison;
            } else {
                boolean isO1ClassZero = o1.getClassNum() == 0;
                boolean isO2ClassZero = o2.getClassNum() == 0;
                if (isO1ClassZero && !isO2ClassZero) {
                    return 1; // o1 with classnum 0 should come after o2
                } else if (!isO1ClassZero && isO2ClassZero) {
                    return -1; // o1 with classnum != 0 should come before o2
                } else {
                    return Integer.compare(o1.getClassNum(), o2.getClassNum()); // normal comparison for classnum
                }
            }
        });
    }


    /**
     * 排序
     *
     * @param speedGroupDetailResponses
     * @return
     */
    private List<SpeedGroupDetailResponse> sortSpeedGroupDetailResponses(List<SpeedGroupDetailResponse> speedGroupDetailResponses, String staffId) {
        boolean frequentReviews = false;
        final List<String> optionIds = new ArrayList<>();
        //选项排序
        SpeedConfigPO speedConfigPO = speedConfigManager.getOneByStaffIdAndCampusId(staffId,
                WebUtil.getCampusId());

        if (BeanUtil.isNotEmpty(speedConfigPO)
                && ObjectUtil.isNotNull(speedConfigPO.getIsFrequentReviews())
                && speedConfigPO.getIsFrequentReviews()) {

            String staffOftenEvaluateOptionsKey = StrUtil.format(RedisKeyConstants.STAFF_OFTEN_EVALUTAE_OPTIONS, staffId);

            try {
                Map<Object/*options*/, Object/*datetime*/> optionsMap = redisUtil.getHashEntries(staffOftenEvaluateOptionsKey);
                if (CollUtil.isEmpty(optionsMap)) {
                    return speedGroupDetailResponses;
                }

                //统计老师十四天之内的点评项
                optionsMap.forEach((k, v) -> {
                    if (v instanceof String) {
                        DateTime endTime = DateUtil.date();
                        DateTime beginTime = DateUtil.offsetDay(endTime, -14);
                        DateTime parse = DateUtil.parse(v.toString());
                        //在14天范围期间的加到后续判断点评项集合中
                        if (parse.isIn(beginTime, endTime)) {
                            optionIds.add(k.toString());
                        }
                        //超过14天的则删除对应值
                        else {
                            redisUtil.delete(staffOftenEvaluateOptionsKey, k.toString());
                        }
                    }
                });
            } catch (Exception e) {
                //此处只是打印错误日志，不影响正常主流程，所以无需抛出异常
                log.error("从Redis获取对应常点评的点评项缓存异常，不影响主流程业务，需排查具体原因：【{}】", e.getMessage(), e);
                return speedGroupDetailResponses;
            }


            frequentReviews = true;

            //TODO 已优化成上面的从Redis中获取，这里暂时保留
//            List<BehaviourRecord> behaviourRecords = behaviourRecordService.countTeacherRecord();
//            if (CollUtil.isNotEmpty(behaviourRecords)) {
//                behaviourRecords.stream()
//                        .filter(record -> StrUtil.isBlank(record.getOptionId()))
//                        .forEach(record -> record.setOptionId(Convert.toStr(record.getTargetId())));
//
//                optionIds = behaviourRecords.stream()
//                        .map(BehaviourRecord::getOptionId)
//                        .filter(StrUtil::isNotBlank)
//                        .collect(Collectors.toList());
//            }
        }
        if (!frequentReviews || CollUtil.isEmpty(optionIds)) {
            return speedGroupDetailResponses;
        }
        for (SpeedGroupDetailResponse speedGroupDetailResponse : speedGroupDetailResponses) {
            List<SpeedGroupTargetResponse> speedGroupTargetResponses =
                    speedGroupDetailResponse.getSpeedGroupTargetResponses();
            if (CollUtil.isEmpty(speedGroupTargetResponses)) {
                continue;
            }
            List<String> finalOptionIds = optionIds;
            speedGroupTargetResponses.sort((s1, s2) -> {
                String id1 = s1.getOptionId() != null ? s1.getOptionId() : s1.getTargetId();
                String id2 = s2.getOptionId() != null ? s2.getOptionId() : s2.getTargetId();
                int indexOfId1 = finalOptionIds.indexOf(id1);
                int indexOfId2 = finalOptionIds.indexOf(id2);
                if (indexOfId1 == -1 && indexOfId2 == -1) {
                    // Both ids are not in optionIds, return 0 to keep their relative order
                    return 0;
                } else if (indexOfId1 == -1) {
                    // id1 is not in optionIds, put it at the end
                    return 1;
                } else if (indexOfId2 == -1) {
                    // id2 is not in optionIds, put it at the end
                    return -1;
                } else {
                    // Both ids are in optionIds, compare their positions
                    return indexOfId1 - indexOfId2;
                }
            });
        }
        return speedGroupDetailResponses;
    }

    /**
     * 封装数据
     *
     * @param speedTargetGroupRTargetPOS
     * @param groupResponses
     * @param speedGroupDetailResponses
     * @param groupIdListMap
     * @return
     */
    private List<SpeedGroupDetailResponse> listSpeedGroupDetailResponses(List<SpeedTargetGroupRTargetPO> speedTargetGroupRTargetPOS,
                                                                         List<GroupResponse> groupResponses,
                                                                         List<SpeedGroupDetailResponse> speedGroupDetailResponses,
                                                                         Map<Long, List<SpeedTargetGroupRTargetPO>> groupIdListMap,
                                                                         PlanTagListsRequest request) {
        //分组下的指标 id
        List<Long> targetIds = speedTargetGroupRTargetPOS.stream()
                .map(SpeedTargetGroupRTargetPO::getTargetId)
                .distinct()
                .collect(Collectors.toList());

        //该老师能点评的指标
        List<ListAllEvaluateTargetVOModule> listAllEvaluateTargetVOModules =
                targetGroupService.queryStaffInfoModules(false);

        //该老师能点评的指标 id
        List<String> teacherTargetIds = this.listTeacherTargetIds(listAllEvaluateTargetVOModules);

        if (CollUtil.isEmpty(teacherTargetIds)) {
            log.warn("【极速点评分组及详情】-【该老师能点评的指标为空】,直接返回分组,staffId:[{}]", WebUtil.getStaffId());
            this.listResponse(groupResponses, speedGroupDetailResponses);
            return speedGroupDetailResponses;
        }

        //分组下老师可点评的指标 ids
        List<Long> finalTargetIds = new ArrayList<>(CollUtil.intersection(targetIds, Convert.toList(Long.class,
                teacherTargetIds)));

        if (CollUtil.isEmpty(finalTargetIds)) {
            log.warn("【极速点评分组及详情】-【该老师能点评的指标为空】,直接返回分组,staffId:[{}]", WebUtil.getStaffId());
            this.listResponse(groupResponses, speedGroupDetailResponses);
            return speedGroupDetailResponses;
        }

        //指标信息
        List<Target> targets = targetService.listByTargetIds(finalTargetIds);

        //指标模板信息
        List<TargetTemplate> targetTemplates = targetTemplateDao.listByTargetIds(finalTargetIds);
        if (CollUtil.isEmpty(targets) || CollUtil.isEmpty(targetTemplates)) {
            log.info("[极速点评分组及详情]-[指标或者模板谢谢为空],直接返回分组,targetIds:[{}]", finalTargetIds);
            this.listResponse(groupResponses, speedGroupDetailResponses);
            return speedGroupDetailResponses;
        }
        Map<Long, List<TargetTemplate>> targetIdListMap = CollStreamUtil.groupByKey(targetTemplates,
                TargetTemplate::getTargetId);

        //组装选项和指标信息
        this.fillGroupDetailResponse(groupResponses, speedGroupDetailResponses, groupIdListMap, finalTargetIds,
                targetIdListMap, targets);

        return speedGroupDetailResponses;
    }

    /**
     * 组装选项和指标信息
     *
     * @param groupResponses
     * @param speedGroupDetailResponses
     * @param groupIdListMap
     * @param finalTargetIds
     * @param targetIdListMap
     * @param targets
     */
    private void fillGroupDetailResponse(List<GroupResponse> groupResponses,
                                         List<SpeedGroupDetailResponse> speedGroupDetailResponses,
                                         Map<Long, List<SpeedTargetGroupRTargetPO>> groupIdListMap,
                                         List<Long> finalTargetIds,
                                         Map<Long, List<TargetTemplate>> targetIdListMap,
                                         List<Target> targets) {
        // 多行文本是否必须
        Map<Long, Boolean> needTextareaMap = CollStreamUtil.toMap(targetIdListMap.entrySet(),
                Entry::getKey, entry -> {
                    List<TargetTemplate> templateList = entry.getValue();
                    if (CollUtil.isEmpty(templateList)) {
                        return false;
                    }
                    List<TemplateInfoSaveDTO> infoList = templateList.get(0).getTemplateInfoList();
                    if (CollUtil.isEmpty(infoList)) {
                        return false;
                    }
                    return infoList.stream().anyMatch(t -> SubmitInfoTypeEnum.MULTI_TEXT.getText().equals(t.getType())
                            && Boolean.TRUE.equals(t.getRequired()));
                });
        for (GroupResponse s : groupResponses) {
            //分组下的点评项
            List<SpeedTargetGroupRTargetPO> targetGroupRTargetPOS = groupIdListMap.get(s.getGroupId());
            if (CollUtil.isEmpty(targetGroupRTargetPOS)) {
                this.fillGroup(speedGroupDetailResponses, s);
                continue;
            }
            //该老师有的点评项
            List<SpeedTargetGroupRTargetPO> groupRTargetPOS = targetGroupRTargetPOS.stream()
                    .filter(t -> finalTargetIds.contains(t.getTargetId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(groupRTargetPOS)) {
                this.fillGroup(speedGroupDetailResponses, s);
                continue;
            }

            SpeedGroupDetailResponse response = new SpeedGroupDetailResponse();
            List<SpeedGroupTargetResponse> speedGroupTargetResponses = new ArrayList<>();
            response.setSpeedGroupId(Convert.toStr(s.getGroupId()));
            response.setSpeedGroupName(s.getGroupName());
            response.setSpeedGroupTargetResponses(speedGroupTargetResponses);
            //填充分组下指标,选项详情
            this.fillGroupTarget(groupRTargetPOS, targetIdListMap, targets, speedGroupTargetResponses,needTextareaMap);
            speedGroupDetailResponses.add(response);
        }
    }

    /**
     * 填充分组信息
     *
     * @param speedGroupDetailResponses
     * @param s
     */
    private void fillGroup(List<SpeedGroupDetailResponse> speedGroupDetailResponses, GroupResponse s) {
        SpeedGroupDetailResponse response = new SpeedGroupDetailResponse();
        response.setSpeedGroupId(Convert.toStr(s.getGroupId()));
        response.setSpeedGroupName(s.getGroupName());
        List<SpeedGroupTargetResponse> speedGroupTargetResponses = new ArrayList<>();
        response.setSpeedGroupTargetResponses(speedGroupTargetResponses);
        speedGroupDetailResponses.add(response);
    }

    /**
     * 填充分组下指标,选项详情
     *
     * @param groupRTargetPOS
     * @param targetIdListMap
     * @param targets
     * @param speedGroupTargetResponses
     * @param needTextareaMap
     */
    private void fillGroupTarget(List<SpeedTargetGroupRTargetPO> groupRTargetPOS,
            Map<Long, List<TargetTemplate>> targetIdListMap,
            List<Target> targets,
            List<SpeedGroupTargetResponse> speedGroupTargetResponses,
            Map<Long, Boolean> needTextareaMap) {

        List<Long> targetIds = groupRTargetPOS.stream().map(SpeedTargetGroupRTargetPO::getTargetId)
                .distinct().collect(Collectors.toList());
        Map<Long, Integer> targetIdToModuleCode = speedConfigSetUpShowService.buildTargetIdtoModuleMap(targetIds);

        for (SpeedTargetGroupRTargetPO t : groupRTargetPOS) {
            //指标对应的表单模板t
            List<TargetTemplate> templates = targetIdListMap.get(t.getTargetId());
            if (CollUtil.isEmpty(templates)) {
                continue;
            }
            SpeedGroupTargetResponse speedGroupTargetResponse = new SpeedGroupTargetResponse();
            speedGroupTargetResponse.setTargetId(Convert.toStr(t.getTargetId()));
            Target target = this.getTargetName(t, targets);
            if(Objects.nonNull(target)){
                speedGroupTargetResponse.setTargetName(target.getTargetName());
                speedGroupTargetResponse.setTargetNotPartCount(target.getTargetNotPartCount());
            }
            Integer moduleCode = targetIdToModuleCode.getOrDefault(t.getTargetId(), 0);
            speedGroupTargetResponse.setModuleCode(moduleCode);

            //分数统计类型 1：指标（分值） 2：选项
            Integer infoType = t.getInfoType();
            if (InfoTypeEnum.OPTION.getCode().equals(infoType)) {
                //明细，单选，多选
                this.fillOption(t, speedGroupTargetResponse, templates, speedGroupTargetResponses, needTextareaMap);
            } else {
                //指标（分值）
                this.fillDetail(t, speedGroupTargetResponse, templates, speedGroupTargetResponses, needTextareaMap);
            }
        }
    }

    /**
     * 指标（分值）
     *
     * @param t
     * @param speedGroupTargetResponse
     * @param templates
     * @param speedGroupTargetResponses
     * @param needTextareaMap
     */
    private void fillDetail(SpeedTargetGroupRTargetPO t,
            SpeedGroupTargetResponse speedGroupTargetResponse,
            List<TargetTemplate> templates,
            List<SpeedGroupTargetResponse> speedGroupTargetResponses,
            Map<Long, Boolean> needTextareaMap) {

        speedGroupTargetResponse.setType(SubmitInfoTypeEnum.SCORE.getText());
        speedGroupTargetResponse.setTypeName("分值");
        List<TemplateInfoSaveDTO> templateInfoList = templates.get(0).getTemplateInfoList();
        List<TemplateInfoSaveDTO> infoSaveDTOS = templateInfoList.stream()
                .filter(o -> SubmitInfoTypeEnum.SCORE.getText().equals(o.getType())).collect(Collectors.toList());
        if (CollUtil.isEmpty(infoSaveDTOS)) {
            return;
        }
        speedGroupTargetResponse.setOptionId(infoSaveDTOS.get(0).getKey());
        speedGroupTargetResponse.setKey(infoSaveDTOS.get(0).getKey());
        speedGroupTargetResponse.setScoreType(infoSaveDTOS.get(0).getScoreType());
        if (ScoreTypeEnum.PLUS.getCode().equals(infoSaveDTOS.get(0).getScoreType())) {
            speedGroupTargetResponse.setScore(infoSaveDTOS.get(0).getScore());
        } else if (ScoreTypeEnum.REDUCE.getCode().equals(infoSaveDTOS.get(0).getScoreType())) {
            speedGroupTargetResponse.setScore(infoSaveDTOS.get(0).getScore().negate());
        }
        speedGroupTargetResponse.setIsAdjustScore(infoSaveDTOS.get(0).getIsAdjustScore());
        speedGroupTargetResponse.setAdjustScore(infoSaveDTOS.get(0).getAdjustScore());
        speedGroupTargetResponse.setSortIndex(t.getSortIndex());
        speedGroupTargetResponse.setNeedTextarea(needTextareaMap.getOrDefault(t.getTargetId(), false));
        speedGroupTargetResponses.add(speedGroupTargetResponse);
    }

    /**
     * 明细，单选，多选
     *
     * @param t
     * @param speedGroupTargetResponse
     * @param templates
     * @param speedGroupTargetResponses
     * @param needTextareaMap
     */
    private void fillOption(SpeedTargetGroupRTargetPO t,
                            SpeedGroupTargetResponse speedGroupTargetResponse,
                            List<TargetTemplate> templates,
                            List<SpeedGroupTargetResponse> speedGroupTargetResponses,
            Map<Long, Boolean> needTextareaMap) {
        speedGroupTargetResponse.setOptionId(t.getOptionId());
        //该指标对应的控件
        List<TemplateInfoSaveDTO> templateInfoList1 = templates.get(0).getTemplateInfoList();
        for (TemplateInfoSaveDTO templateInfoSaveDTO : templateInfoList1) {
            String type = templateInfoSaveDTO.getType();
            //明细
            if (SubmitInfoTypeEnum.DETAIL.getText().equals(type)) {
                List<LinkedHashMap> list = templateInfoSaveDTO.getList();
                if (CollUtil.isEmpty(list)) {
                    continue;
                }
                List<TemplateInfoSaveDTO> saveDTOS = Convert.toList(TemplateInfoSaveDTO.class, list);
                for (TemplateInfoSaveDTO saveDTO : saveDTOS) {
                    //填充数据
                    this.fillKey(t, saveDTO, speedGroupTargetResponse, speedGroupTargetResponses,needTextareaMap);
                }
                //单选多选
            } else if (SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(type)
                    || SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(type)) {
                //填充数据
                this.fillKey(t, templateInfoSaveDTO, speedGroupTargetResponse, speedGroupTargetResponses,needTextareaMap);
            }
        }
    }

    /**
     * 填充选项所属控件信息
     *
     * @param t
     * @param saveDTO
     * @param speedGroupTargetResponse
     * @param speedGroupTargetResponses
     * @param needTextareaMap
     */
    private void fillKey(SpeedTargetGroupRTargetPO t,
                         TemplateInfoSaveDTO saveDTO,
                         SpeedGroupTargetResponse speedGroupTargetResponse,
                         List<SpeedGroupTargetResponse> speedGroupTargetResponses, Map<Long, Boolean> needTextareaMap) {
        TemplateInfoSaveDTO.InnerSubmitOptionInfoSave options = saveDTO.getOptions();
        Long targetId = t.getTargetId();
        if (BeanUtil.isEmpty(options)) {
            return;
        }
        List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> infoSubSaves = options.getOptions();
        if (CollUtil.isEmpty(infoSubSaves)) {
            return;
        }
        Optional<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> first = infoSubSaves.stream()
                .filter(b -> t.getOptionId().equals(b.getKey())).findFirst();
        if (first.isPresent()) {
            speedGroupTargetResponse.setOptionName(first.get().getLabel());
            speedGroupTargetResponse.setScore(first.get().getValue());
            speedGroupTargetResponse.setScoreType(saveDTO.getScoreType());
            speedGroupTargetResponse.setKey(saveDTO.getKey());
            speedGroupTargetResponse.setType(saveDTO.getType());
            speedGroupTargetResponse.setTypeName(saveDTO.getTypeName());
            speedGroupTargetResponse.setIsAdjustScore(saveDTO.getIsAdjustScore());
            speedGroupTargetResponse.setAdjustScore(saveDTO.getAdjustScore());
            speedGroupTargetResponse.setSortIndex(t.getSortIndex());
            speedGroupTargetResponse.setNeedTextarea(needTextareaMap.getOrDefault(targetId, false));
            speedGroupTargetResponses.add(speedGroupTargetResponse);
        }
    }

    private List<String> listTeacherTargetIds(List<ListAllEvaluateTargetVOModule> listAllEvaluateTargetVOModules) {
        return listAllEvaluateTargetVOModules.stream()
                .flatMap(module -> Optional.ofNullable(module)
                        .map(m -> m.getTargetGroupList().stream())
                        .orElse(Stream.empty()))
                .flatMap(group -> Optional.ofNullable(group)
                        .map(g -> g.getTargetList().stream())
                        .orElse(Stream.empty()))
                .filter(BeanUtil::isNotEmpty)
                .map(ListAllEvaluateTargetVOInnerTarget::getTargetId)
                .collect(Collectors.toList());
    }

    /**
     * 获取指标名称
     *
     * @param t
     * @param targets
     * @return
     */
    private Target getTargetName(SpeedTargetGroupRTargetPO t, List<Target> targets) {
        return targets.stream()
                .filter(target -> t.getTargetId().equals(target.getId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 返回分组信息
     *
     * @param groupResponses
     * @param speedGroupDetailResponses
     */
    private void listResponse(List<GroupResponse> groupResponses,
                              List<SpeedGroupDetailResponse> speedGroupDetailResponses) {
        groupResponses.forEach(s -> {
            SpeedGroupDetailResponse response = new SpeedGroupDetailResponse();
            response.setSpeedGroupId(Convert.toStr(s.getGroupId()));
            response.setSpeedGroupName(s.getGroupName());
            List<SpeedGroupTargetResponse> speedGroupTargetResponses = new ArrayList<>();
            response.setSpeedGroupTargetResponses(speedGroupTargetResponses);
            speedGroupDetailResponses.add(response);
        });
    }

    @Override
    public List<GetEvaluateTaskDetailVO> textareaList(IdListRequest req) {
        List<Long> targetIds = req.getIds();
        if (CollUtil.isEmpty(targetIds)) {
            return new ArrayList<>();
        }
        List<Target> targets = targetService.listByIds(targetIds);
        List<TargetTemplate> targetTemplates = targetTemplateDao.listByTargetIds(targetIds);
        Map<Long, List<TemplateInfoSaveDTO>> textareaMap = CollStreamUtil.toMap(targetTemplates,
                TargetTemplate::getTargetId,targetTemplate -> {
                    List<TemplateInfoSaveDTO> templateInfoList = targetTemplate.getTemplateInfoList();
                    return CollUtil.filter(templateInfoList,
                            t -> SubmitInfoTypeEnum.MULTI_TEXT.getText().equals(t.getType())
                                    && Boolean.TRUE.equals(t.getRequired()));
                });
        return CollStreamUtil.toList(targets, target -> {
            GetEvaluateTaskDetailVO vo = new GetEvaluateTaskDetailVO();
            Long id = target.getId();
            vo.setTargetId(id);
            vo.setTargetName(target.getTargetName());
            List<TemplateInfoSaveDTO> templateInfoList = textareaMap.getOrDefault(id, new ArrayList<>());
            templateInfoList.forEach(item->{
            });
            vo.setTemplateInfo(templateInfoList);
            return vo;
        });
    }
}
