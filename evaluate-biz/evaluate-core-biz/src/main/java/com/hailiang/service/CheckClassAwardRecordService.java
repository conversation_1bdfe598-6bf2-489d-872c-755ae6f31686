package com.hailiang.service;

import com.hailiang.model.entity.CheckClassAwardRecord;
import com.hailiang.model.vo.CheckClassAwardRecordVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gaoxin
 * @create 2023/8/3 14:56
 */
public interface CheckClassAwardRecordService {

    String formatNames(List<String> classNames);

    void saveBatch(List<CheckClassAwardRecord> records);

    List<String> queryAwardIdsByClassNameAndIsAward(String className);

    Map<String, List<CheckClassAwardRecord>> queryAndGroupByAwardIdAndIsAward(List<String> awardIds);

    List<CheckClassAwardRecord> filtereNeedNotifyClassList(List<CheckClassAwardRecord> records);

    Map<String, String> formatClassNameAndSortGroupByType(Integer awardType, List<CheckClassAwardRecord> classRecords);

    Map<String, List<CheckClassAwardRecordVO>> sortGroupByType(Integer awardType, List<CheckClassAwardRecordVO> classRecords);
}
