package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.check.CheckAuthConfigDTO;
import com.hailiang.model.entity.CheckAuthConfig;
import com.hailiang.model.request.check.CheckAuthConfigSaveRequest;
import com.hailiang.model.response.check.CheckAuthConfigSaveResponse;


/**
 * 接口
 *
 * <AUTHOR> 2024-04-07 16:05:23
 */
public interface CheckAuthConfigService extends IService<CheckAuthConfig>{

    boolean saveOrUpdateCheckAuthConfig(CheckAuthConfigSaveRequest checkAuthConfigSaveRequest);

    CheckAuthConfigSaveResponse detail();

}