package com.hailiang.service.impl;

import cn.hutool.core.util.IdUtil;
import com.google.common.collect.Maps;
import com.hailiang.enums.AwardTypeEnum;
import com.hailiang.manager.CheckClassAwardRecordManager;
import com.hailiang.model.entity.CheckClassAwardRecord;
import com.hailiang.model.vo.CheckClassAwardRecordVO;
import com.hailiang.service.CheckClassAwardRecordService;
import com.hailiang.util.SnowFlakeIdUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gaoxin
 * @create 2023/8/3 14:56
 */
@Service
@RequiredArgsConstructor
public class CheckClassAwardRecordServiceImpl implements CheckClassAwardRecordService {

    private static final String SEPARATOR = "、";
    private static final String DEFAULT_KEY = "all";
    final CheckClassAwardRecordManager classRecordManager;

    @Override
    public String formatNames(List<String> classNames) {
        if (CollectionUtils.isEmpty(classNames)) return null;
        return String.join(SEPARATOR, classNames);
    }

    @Override
    public void saveBatch(List<CheckClassAwardRecord> records) {
        records.forEach(record -> {
            record.setId(SnowFlakeIdUtil.nextId());
        });
        classRecordManager.saveBatch(records);
    }

    @Override
    public List<String> queryAwardIdsByClassNameAndIsAward(String className) {
        if (StringUtils.isBlank(className)) return null;
        return classRecordManager.queryByClassNameAndIsAward(className).stream().map(CheckClassAwardRecord::getAwardId).distinct().collect(Collectors.toList());
    }

    @Override
    public Map<String, List<CheckClassAwardRecord>> queryAndGroupByAwardIdAndIsAward(List<String> awardIds) {
        if (CollectionUtils.isEmpty(awardIds)) return null;
        return classRecordManager.queryByAwardIdsAndIsAward(awardIds).stream().collect(Collectors.groupingBy(CheckClassAwardRecord::getAwardId));
    }

    @Override
    public List<CheckClassAwardRecord> filtereNeedNotifyClassList(List<CheckClassAwardRecord> records) {
        return records.stream().filter(classRecord -> Boolean.TRUE.equals(classRecord.getIsAward())).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> formatClassNameAndSortGroupByType(Integer awardType, List<CheckClassAwardRecord> classRecords) {
        if (null == awardType) return null;

        Map<String, String> result = Maps.newLinkedHashMap();

        if (AwardTypeEnum.BY_CLASS.getCode().equals(awardType)) {
            result.put(DEFAULT_KEY, this.formatNames(classRecords.stream().map(CheckClassAwardRecord::getClassName).collect(Collectors.toList())));
        }

        if (AwardTypeEnum.BY_HIERARCHY.getCode().equals(awardType)) {
            result.putAll(this.sortAndGroup(classRecords, CheckClassAwardRecord::getLevelName, CheckClassAwardRecord::getLevelId,
                    Collectors.collectingAndThen(
                            Collectors.mapping(CheckClassAwardRecord::getClassName, Collectors.toList()),
                            classNames -> String.join(SEPARATOR, classNames)
                    )));
        }

        if (AwardTypeEnum.BY_GRADE.getCode().equals(awardType)) {
            result.putAll(this.sortAndGroup(classRecords, CheckClassAwardRecord::getGradeName, CheckClassAwardRecord::getGradeCode,
                    Collectors.collectingAndThen(
                            Collectors.mapping(CheckClassAwardRecord::getClassName, Collectors.toList()),
                            classNames -> String.join(SEPARATOR, classNames)
                    )));
        }

        return result;
    }

    @Override
    public Map<String, List<CheckClassAwardRecordVO>> sortGroupByType(Integer awardType, List<CheckClassAwardRecordVO> classRecords) {
        if (null == awardType) return null;

        Map<String, List<CheckClassAwardRecordVO>> result = Maps.newLinkedHashMap();

        if (AwardTypeEnum.BY_CLASS.getCode().equals(awardType)) {
            result.put(DEFAULT_KEY, classRecords);
        }

        if (AwardTypeEnum.BY_HIERARCHY.getCode().equals(awardType)) {
            result.putAll(this.sortAndGroup(classRecords, CheckClassAwardRecordVO::getLevelName, CheckClassAwardRecordVO::getLevelId, Collectors.toList()));

        }

        if (AwardTypeEnum.BY_GRADE.getCode().equals(awardType)) {
            result.putAll(this.sortAndGroup(classRecords, CheckClassAwardRecordVO::getGradeName, CheckClassAwardRecordVO::getGradeCode, Collectors.toList()));
        }

        return result;
    }

    private <T, R> Map<String, R> sortAndGroup(List<T> records, Function<T, String> groupFunction, Function<T, String> sortFunction, Collector<T, ?, R> collector) {
        return records.stream()
                .sorted(Comparator.comparing(sortFunction))
                .collect(Collectors.groupingBy(groupFunction, LinkedHashMap::new, collector));
    }
}
