package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.hailiang.enums.TargetUserSubmitTypeEnum;
import com.hailiang.manager.TargetUserManager;
import com.hailiang.model.entity.TargetUserPO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.staff.OrgStaffQueryDTO;
import com.hailiang.remote.saas.dto.staff.StaffRoleQueryDTO;
import com.hailiang.remote.saas.vo.staff.StaffVO;
import com.hailiang.service.TargetUserService;
import jline.internal.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 指标用户服务
 *
 * @Description: 指标用户服务
 * @Author: Jovi
 * @Date: Created in 2024-12-03
 * @Version: 2.0.0
 */
@Service
@Slf4j
public class TargetUserServiceImpl implements TargetUserService {

    @Resource
    private TargetUserManager targetUserManager;

    @Resource
    private BasicInfoRemote basicInfoRemote;


    @Override
    public List<Long> filterValidTargetIds(Long staffId, List<Long> targetIds) {

        List<Long> filterTargetIds = new ArrayList<>();

        for (Long targetId : targetIds) {
            if(isValidByStaffIdAndTargetId(staffId, targetId)){
                filterTargetIds.add(targetId);
            }
        }

        return filterTargetIds;
    }

    @Override
    public Boolean isValidByStaffIdAndTargetId(Long staffId, Long targetId) {


        List<TargetUserPO> targetUserPOS = targetUserManager.listByTargetId(targetId);
        log.info("指标用户PO列表：{},staffId:{}, targetId:{}", targetUserPOS, staffId, targetId);

        if (CollUtil.isEmpty(targetUserPOS)) {
            return false;
        }

        return validStaffAuthority(staffId, targetUserPOS);
    }


    /**
     * 循环校验该指标下所有用户的权限，
     * 如果有权限则返回true，否则返回false
     *
     * @param staffId
     * @param targetUserPOS
     * @return
     */
    private Boolean validStaffAuthority(Long staffId, List<TargetUserPO> targetUserPOS) {
        for (TargetUserPO targetUserPO : targetUserPOS) {

            Integer submitType = targetUserPO.getSubmitType();
            Long submitValue = targetUserPO.getSubmitValue();

            if (!TargetUserSubmitTypeEnum.isInStaffAuthority(submitType)) {
                continue;
            }

            //提交类型为教职工分类，则直接验证教职工ID是否相等
            if (TargetUserSubmitTypeEnum.STAFF.getCode().equals(submitType) &&
                    submitValue.equals(staffId)) {

                return true;
            }

            List<StaffVO> staffVOList = new ArrayList<>();

            //提交类型为组织架构，则查询组织架构下教职工，然后验证该组织架构下的教职工ID是否包含当前登录的教职工ID

            if (TargetUserSubmitTypeEnum.ORG.getCode().equals(submitType)) {
                OrgStaffQueryDTO orgStaffQueryDTO = new OrgStaffQueryDTO();
                orgStaffQueryDTO.setOrgId(submitValue);
                orgStaffQueryDTO.setType(2);
                staffVOList.addAll(basicInfoRemote.queryStaffListByOrg(orgStaffQueryDTO));
            }

            //提交类型为角色，则查询角色下教职工，然后验证该角色下的教职工ID是否包含当前登录的教职工ID
            if (TargetUserSubmitTypeEnum.ROLE.getCode()
                    .equals(submitType)) {
                StaffRoleQueryDTO staffRoleQueryDTO = new StaffRoleQueryDTO();
                staffRoleQueryDTO.setRoleId(submitValue);
                staffVOList.addAll(basicInfoRemote.queryStaffListByRole(staffRoleQueryDTO));
            }

            if (CollUtil.isNotEmpty(staffVOList)) {
                List<Long> staffIds = staffVOList
                        .stream()
                        .map(StaffVO::getStaffId)
                        .distinct()
                        .collect(Collectors.toList());

                if (staffIds.contains(staffId)) {
                    return true;
                }
            }

        }

        return false;
    }

}
