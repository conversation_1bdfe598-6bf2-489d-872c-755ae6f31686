package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.EvaluateAggDTO;
import com.hailiang.model.entity.StudentAbilityModel;
import com.hailiang.model.request.studentmodel.StudentAbilityModelRequest;
import com.hailiang.model.response.studentmodel.StudentAbilityModelResponse;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;

import java.util.List;


/**
 * 接口
 *
 * <AUTHOR> 2024-04-19 15:29:38
 */
public interface StudentDailyStatisticsService {

    void saveBatch(EvaluateAggDTO agg, List<StudentDailyStatisticsPO> studentDailyStatisticPOS);
}