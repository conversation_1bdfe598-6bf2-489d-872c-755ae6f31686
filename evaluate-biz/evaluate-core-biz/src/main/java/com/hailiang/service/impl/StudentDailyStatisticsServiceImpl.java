package com.hailiang.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.hailiang.constant.Constant;
import com.hailiang.manager.EvaluateStatisticalArchivedLogManager;
import com.hailiang.manager.StudentDailyStatisticsManager;
import com.hailiang.model.dto.EvaluateAggDTO;
import com.hailiang.portrait.entity.EvaluateStatisticalArchivedLogPO;
import com.hailiang.portrait.entity.StaffDailyStatisticsPO;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.service.StudentDailyStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 业务实现
 *
 * <AUTHOR> 2024-04-19 15:29:38
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StudentDailyStatisticsServiceImpl implements StudentDailyStatisticsService {
    final StudentDailyStatisticsManager studentDailyStatisticsManager;
    final EvaluateStatisticalArchivedLogManager evaluateStatisticalArchivedLogManager;
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 600)
    public void saveBatch(EvaluateAggDTO agg, List<StudentDailyStatisticsPO> studentDailyStatisticPOS) {
        log.info("【学生评价指标明细聚合任务】准备进行归档数据保存：参数：【{}】，学生归档总条数：{}", JSONUtil.toJsonStr(agg), studentDailyStatisticPOS.size());
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        Date startTime = DateUtil.beginOfDay(agg.getDaily());
        Date endTime = DateUtil.endOfDay(agg.getDaily());
        //重试&重算，逻辑删历史数据
        if (Objects.equals(Boolean.TRUE, agg.getRetry())) {
            studentDailyStatisticsManager.deleteByTime(startTime, endTime, agg.getStudentIdList(), agg.getCampusId());
            log.info("【学生评价指标明细聚合任务】旧数据已删除，消耗时长：【{}】", TIME_INTERVAL.intervalMs());
            TIME_INTERVAL.restart();
        }

        //2024-09-26 分页批量新增
        //解决mybatisplus批量插入数据时事务超时和速度慢的问题
        List<List<StudentDailyStatisticsPO>> partition = Lists.partition(studentDailyStatisticPOS, Constant.BATCH_INSERT_SIZE);
        for (List<StudentDailyStatisticsPO> staffDailyStatisticsPOS : partition) {
            studentDailyStatisticsManager.insertBatch(staffDailyStatisticsPOS);
            log.info("【教师评价指标明细聚合任务】数据保存成功，当前批次：{}", staffDailyStatisticsPOS.size());
        }
        log.info("【学生评价指标明细聚合任务】数据批量保存成功，消耗时长：【{}】", TIME_INTERVAL.intervalMs());
    }
}
