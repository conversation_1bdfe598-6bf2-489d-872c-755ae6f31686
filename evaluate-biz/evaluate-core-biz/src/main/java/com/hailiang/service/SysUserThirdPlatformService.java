package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.entity.SysUserThirdPlatform;
import com.hailiang.remote.hai.domain.dto.request.SentMsgTokenRequest;


/**
 * 接口
 *
 * <AUTHOR> 2024-02-26 14:02:30
 */
public interface SysUserThirdPlatformService extends IService<SysUserThirdPlatform>{

    /**
     * 绑定hai家校微信公众号
     *
     * @param code   code
     * @param mobile 手机号
     * @return boolean
     */
    boolean bindHaiWechat(String code, String mobile);

    /**
     * 通过手机获取openId
     *
     * @param mobile 手机号
     * @return {@link String}
     */
    String getOpenIdByMobile(String mobile);


    String getOpenIdByMobileFromHai(SentMsgTokenRequest sentMsgTokenRequest);
}