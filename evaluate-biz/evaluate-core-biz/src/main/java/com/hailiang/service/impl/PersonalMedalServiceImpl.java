package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hailiang.constant.Constant;
import com.hailiang.convert.MedalDetailConvert;
import com.hailiang.enums.TaskStatusEnum;
import com.hailiang.enums.medal.*;
import com.hailiang.manager.*;
import com.hailiang.mapper.MedalActivityGradeMapper;
import com.hailiang.mapper.MedalRuleMatchRecordMapper;
import com.hailiang.mapper.MedalUserOperationRecordMapper;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.dto.activity.detail.*;
import com.hailiang.model.dto.activity.rule.save.OuterLayerRuleDTO;
import com.hailiang.model.dto.activity.rule.save.TargetLevelDTO;
import com.hailiang.model.entity.*;
import com.hailiang.model.medal.vo.MedalInfoVO;
import com.hailiang.model.vo.SubListCurrentEvaluateTaskVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.dto.student.StudentByIdQuery;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.service.ActivityRuleService;
import com.hailiang.service.BasicInfoService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/6 9:55
 */
@Service
@Slf4j
public class PersonalMedalServiceImpl implements PersonalMedalService {


    @Value("${oss.urlPrefix}")
    private String urlPrefix;

    @Resource
    private MedalUserAcquireRecordManager medalUserAcquireRecordManager;

    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private MedalActivityGradeMapper medalActivityGradeMapper;

    @Resource
    private MedalTaskCompletionManager medalTaskCompletionManager;

    @Resource
    private MedalActivityManager medalActivityManager;


    @Resource
    private MedalTaskManager medalTaskManager;

    @Resource
    private MedalInfoManager medalInfoManager;


    @Resource
    private MedalTaskRuleManager medalTaskRuleManager;

    @Resource
    private MedalTaskRuleTargetManager medalTaskRuleTargetManager;

    @Resource
    private MedalRuleMatchRecordMapper medalRuleMatchRecordMapper;

    @Resource
    private ActivityRuleService activityRuleService;


    /**
     * 查询个人争章任务进度
     *
     * @param query 查询参数
     * @return
     */
    @Override
    public List<MedalTaskProgressVO> listPersonalTaskProgress(MedalTaskProgressQuery query) {
        if (CharSequenceUtil.isBlank(query.getStudentId())) {
            log.warn("查询个人争章任务进度，学生 id为空，直接返回，入参：【{}】", JSONUtil.toJsonStr(query));
            return Collections.emptyList();
        }

        StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
        studentByIdQuery.setStudentIds(CollUtil.newArrayList(Convert.toLong(query.getStudentId())));
        List<UcStudentClassBffVO> ucStudents = basicInfoRemote.listByStudentIds(studentByIdQuery);
        if (CollUtil.isEmpty(ucStudents)) {
            return Collections.emptyList();
        }
        // 查询学生的年级信息
        UcStudentClassBffVO studentInfoVO = CollUtil.getFirst(ucStudents);

        MedalTaskProgressMapDTO medalTaskProgressMap = new MedalTaskProgressMapDTO();
        Boolean flag = encapsulationBasicMao(medalTaskProgressMap, studentInfoVO.getGradeId(), query.getStudentId());
        if (CollUtil.isNotEmpty(medalTaskProgressMap.getActivityTasks()) && CollUtil.isEmpty(medalTaskProgressMap.getCompletions())) {
            // 如果活动不为空,但是二级任务完成为空,需要为每个任务生成一个进行中的二级任务
            return emptyCompletionHandle(medalTaskProgressMap.getActivityTasks(), medalTaskProgressMap.getTaskRules(), medalTaskProgressMap.getMedalInfoMap(), medalTaskProgressMap.getActivityInfoMap(), medalTaskProgressMap.getRuleTargetMap(), query.getStudentId());
        }
        if (!flag) {
            return Collections.emptyList();
        }

        // 活动任务封装
        List<MedalTaskProgressVO> result = new ArrayList<>();
        for (MedalTask medalTask : medalTaskProgressMap.getActivityTasks()) {
            // 获取该任务下的二级任务完成情况
            List<MedalTaskCompletion> completions = medalTaskProgressMap.getCompletions().stream().filter(s -> query.getStudentId().equals(s.getStudentId())).filter(s -> medalTask.getId().equals(s.getMedalTaskId())).sorted(Comparator.comparing(MedalTaskCompletion::getId)).collect(Collectors.toList());
            // 获取该任务下有几种一级规则
            List<MedalTaskRule> subRules = medalTaskProgressMap.getTaskRules().stream().filter(s -> medalTask.getId().equals(s.getMedalTaskId())).filter(s -> MedalRuleLevelEnum.SUB_LEVEL.getCode().equals(s.getLevel())).collect(Collectors.toList());
            // 不同规则的行为记录信息
            HashMap<Long, List<BehaviourRecordDTO>> behaviourRecordMap = new HashMap<>();
            for (MedalTaskRule subRule : subRules) {
                // 获取规则下匹配的行为记录
                List<BehaviourRecordDTO> behaviourRecords = medalRuleMatchRecordMapper.listMatchRuleBehaviour(subRule.getId(), query.getStudentId());
                behaviourRecordMap.put(subRule.getId(), behaviourRecords);
            }
            // 封装任务
            List<MedalTaskProgressVO> record = encapsulationTask(completions, medalTaskProgressMap.getTaskRules(), subRules, medalTask, behaviourRecordMap, medalTaskProgressMap.getMedalInfoMap(), medalTaskProgressMap.getActivityInfoMap(), medalTaskProgressMap.getRuleTargetMap(), query.getStudentId(), medalTaskProgressMap.getAwardStatusMap());
            result.addAll(record);
        }

        List<MedalTaskProgressVO> collect = result.stream().filter(s -> {
            Boolean resultFlag = Boolean.TRUE;
            // 奖章删除，已完成的任务需展示，进行中的任务需隐藏（奖章彻底删除，不管已完成还是进行中的任务都需隐藏）

            // 进行中的任务,判断任务是否失效,任务失效不展示
            if (PersonalProgressStatusEnum.IS_PROCEED.getCode().equals(s.getStatus())) {
                Map<Long, Integer> medalTaskStatusMap = medalTaskProgressMap.getMedalTaskStatusMap();
                // 如果任务不存在,被删除了,则不展示
                if (Objects.isNull(medalTaskStatusMap.get(s.getMedalTaskId()))) {
                    resultFlag = Boolean.FALSE;
                }
                // 如果任务存在,但任务失效,也不展示
                if (Objects.nonNull(medalTaskStatusMap.get(s.getMedalTaskId())) && Constant.YES.equals(medalTaskStatusMap.get(s.getMedalTaskId()))) {
                    resultFlag = Boolean.FALSE;
                }
            }

            // 已完成的任务,判断奖章明细是否被删除
            if (PersonalProgressStatusEnum.IS_COMPLETION.getCode().equals(s.getStatus())) {
                Map<Long, Integer> awardStatusMap = medalTaskProgressMap.getAwardStatusMap();
                if (Objects.nonNull(s.getCompletionId()) && Objects.isNull(awardStatusMap.get(s.getCompletionId()))) {
                    resultFlag = Boolean.FALSE;
                }
            }

            // 判断如果活动已结束,没有进行中的任务
            Map<Long, MedalActivityInfoVO> activityInfoMap = medalTaskProgressMap.getActivityInfoMap();
            if (Objects.nonNull(s.getActivityId()) && Objects.nonNull(activityInfoMap.get(s.getActivityId()))) {
                MedalActivityInfoVO medalActivityInfoVO = activityInfoMap.get(s.getActivityId());
                // 活动已结束,且是进行中的任务
                if (Convert.toInt(4).equals(medalActivityInfoVO.getStatus()) && PersonalProgressStatusEnum.IS_PROCEED.getCode().equals(s.getStatus())) {
                    resultFlag = Boolean.FALSE;
                }
            }

            return resultFlag;
        }).sorted(Comparator.comparing(MedalTaskProgressVO::getSortTime, Comparator.nullsLast(Date::compareTo)).thenComparing(MedalTaskProgressVO::getCompletionId, Comparator.nullsLast(Long::compareTo)).reversed()).collect(Collectors.toList());

        return collect;
    }

    /**
     * @param completions        一个任务下的二级任务完成信息
     * @param taskRules          规则信息(包含一二级规则)
     * @param subRules           一级任务信息
     * @param medalTask          活动任务
     * @param behaviourRecordMap 规则下匹配的行为记录map
     * @param medalInfoMap       奖章信息map
     * @param activityInfoMap    活动信息map
     * @param ruleTargetMap      规则详情map
     * @param studentId          学生id
     * @param awardStatusMap     颁章状态map
     * @return
     */
    private List<MedalTaskProgressVO> encapsulationTask(List<MedalTaskCompletion> completions, List<MedalTaskRule> taskRules, List<MedalTaskRule> subRules, MedalTask medalTask, HashMap<Long, List<BehaviourRecordDTO>> behaviourRecordMap, Map<Long, MedalInfoVO> medalInfoMap, Map<Long, MedalActivityInfoVO> activityInfoMap, Map<Long, MedalTaskRuleTarget> ruleTargetMap, String studentId, Map<Long, Integer> awardStatusMap) {

        // 如果未完成过任务,则生成一条进行中的任务
        if (CollUtil.isEmpty(completions)) {
            addNewCompletion(completions, studentId, medalTask.getId());
        }
        // 如果不存在未完成的任务,且任务数小于循环张的个数,则生成一条进行中的任务
        List<MedalTaskCompletion> isNoCompletions = completions.stream().filter(s -> MedalTaskCompleteStatusEnum.NO_COMPLETION.getCode().equals(s.getStatus())).collect(Collectors.toList());
        if (CollUtil.isEmpty(isNoCompletions) && (completions.size() + 1) <= medalTask.getCycleNum()) {
            addNewCompletion(completions, studentId, medalTask.getId());
        }

        // 活动信息
        MedalActivityInfoVO medalActivityInfoVO = new MedalActivityInfoVO();
        if (Objects.nonNull(activityInfoMap.get(medalTask.getMedalActivityId()))) {
            medalActivityInfoVO = activityInfoMap.get(medalTask.getMedalActivityId());
        }
        // 奖章信息
        MedalInfoVO medalInfoVO = new MedalInfoVO();
        if (Objects.nonNull(medalInfoMap.get(medalTask.getMedalInfoId()))) {
            medalInfoVO = medalInfoMap.get(medalTask.getMedalInfoId());
        }
        // 二级任务规则信息
        OuterLayerRuleDTO outerLayerRuleDTO = new OuterLayerRuleDTO();
        List<MedalTaskRule> topRules = taskRules.stream().filter(s -> MedalRuleLevelEnum.TOP_LEVEL.getCode().equals(s.getLevel())).filter(s -> medalTask.getId().equals(s.getMedalTaskId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(topRules)) {
            MedalTaskRule medalTaskRule = CollUtil.getFirst(topRules);
            outerLayerRuleDTO = JSONUtil.toBean(medalTaskRule.getContent(), OuterLayerRuleDTO.class);
        }
        // 规则的完成次数map封装
        HashMap<Long, Integer> ruleCompletionNumMap = new HashMap<>();
        for (MedalTaskRule submitRule : subRules) {
            ruleCompletionNumMap.put(submitRule.getId(), 1);
        }

        // 封装任务
        List<MedalTaskProgressVO> list = new ArrayList<>();
        for (MedalTaskCompletion completion : completions) {

            // 任务基本信息封装
            MedalTaskProgressVO medalTaskProgressVO = new MedalTaskProgressVO();
            medalTaskProgressVO.setCompletionStatus(completion.getStatus());
            medalTaskProgressVO.setCompletionId(completion.getId());
            medalTaskProgressVO.setMedalTaskId(completion.getMedalTaskId());
            // 设置奖章信息
            medalTaskProgressVO.setMedalName(medalInfoVO.getName());
            medalTaskProgressVO.setLogoUrl(urlPrefix + medalInfoVO.getLogoUrl());
            // 设置活动信息
            medalTaskProgressVO.setActivityId(medalActivityInfoVO.getActivityId());
            medalTaskProgressVO.setActivityName(medalActivityInfoVO.getActivityName());
            medalTaskProgressVO.setStartTime(medalActivityInfoVO.getStartTime());
            medalTaskProgressVO.setEndTime(medalActivityInfoVO.getEndTime());
            // 设置排序时间(取最新的任务完成时间)
            medalTaskProgressVO.setSortTime(completion.getCreateTime());
            if (Objects.nonNull(completion.getUpdateTime())) {
                medalTaskProgressVO.setSortTime(completion.getUpdateTime());
            }
            // 设置获章规则
            medalTaskProgressVO.setAcquireRule("任意完成以下" + outerLayerRuleDTO.getDetailTaskNum() + "个任务，即可获章");
            // 设置任务状态
            if (CollUtil.newArrayList(MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode(), MedalTaskCompleteStatusEnum.IS_FROZEN.getCode(), MedalTaskCompleteStatusEnum.ARTIFICIAL_WITHDRAW.getCode()).contains(completion.getStatus())) {
                medalTaskProgressVO.setStatus(2);
            }
            if (MedalTaskCompleteStatusEnum.NO_COMPLETION.getCode().equals(completion.getStatus())) {
                medalTaskProgressVO.setStatus(1);
            }
            // 设置颁发状态
            if (Objects.nonNull(awardStatusMap.get(completion.getId()))) {
                Integer awardStatus = awardStatusMap.get(completion.getId());
                if (MedalAwardStatusEnum.IS_ISSUE.getCode().equals(awardStatus)) {
                    medalTaskProgressVO.setIssueStatus(1);
                }
                if (MedalAwardStatusEnum.ARTIFICIAL_WITHDRAW.getCode().equals(awardStatus)) {
                    medalTaskProgressVO.setIssueStatus(2);
                }
                if (MedalAwardStatusEnum.IS_COMPLETION.getCode().equals(awardStatus)) {
                    medalTaskProgressVO.setIssueStatus(3);
                }
                if (medalTaskProgressVO.getStatus().equals(1)) {
                    medalTaskProgressVO.setIssueStatus(null);
                }
            }

            //封装规则完成情况
            List<TaskProgressDetailVO> taskDetails = new ArrayList<>();
            for (MedalTaskRule subRule : subRules) {
                // 计算任务进度
                calculateTaskProgress(taskDetails, subRule, ruleTargetMap, behaviourRecordMap, ruleCompletionNumMap, completion);
            }
            medalTaskProgressVO.setTaskDetails(taskDetails);
            list.add(medalTaskProgressVO);
        }
        return list;
    }

    /**
     * 封装map
     *
     * @param medalTaskProgressMap 封装实体类
     * @param gradeId              年级id
     * @param studentId            学生id
     * @return
     */
    private Boolean encapsulationBasicMao(MedalTaskProgressMapDTO medalTaskProgressMap, Long gradeId, String studentId) {
        // 根据年级获取活动id
        List<Long> activityIds = medalActivityGradeMapper.listPersonalActivityByGradeId(WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId(), Convert.toStr(gradeId));
        if (CollUtil.isEmpty(activityIds)) {
            return Boolean.FALSE;
        }
        List<MedalActivityInfoVO> medalActivityInfos = medalActivityManager.listMedalActivityInfo(activityIds);
        if (CollUtil.isEmpty(medalActivityInfos)) {
            return Boolean.FALSE;
        }
        Map<Long, MedalActivityInfoVO> activityInfoMap = medalActivityInfos.stream().collect(Collectors.toMap(MedalActivityInfoVO::getActivityId, Function.identity()));
        medalTaskProgressMap.setActivityInfoMap(activityInfoMap);

        // 获取任务id(未失效的任务)
        List<MedalTask> activityTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>().in(MedalTask::getMedalActivityId, activityIds).orderByAsc(MedalTask::getId));
        List<Long> taskIds = activityTasks.stream().map(MedalTask::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(taskIds)) {
            return Boolean.FALSE;
        }
        medalTaskProgressMap.setActivityTasks(activityTasks);
        // 任务状态map
        Map<Long, Integer> medalTaskStatusMap = activityTasks.stream().collect(Collectors.toMap(MedalTask::getId, MedalTask::getStatus));
        medalTaskProgressMap.setMedalTaskStatusMap(medalTaskStatusMap);

        // 奖章信息
        List<Long> medalInfoIds = activityTasks.stream().map(MedalTask::getMedalInfoId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(medalInfoIds)) {
            return Boolean.FALSE;
        }
        List<MedalInfoVO> medalInfos = medalInfoManager.listInfoByIds(medalInfoIds);
        if (CollUtil.isEmpty(medalInfos)) {
            return Boolean.FALSE;
        }
        Map<Long, MedalInfoVO> medalInfoMap = medalInfos.stream().collect(Collectors.toMap(MedalInfoVO::getId, Function.identity()));
        medalTaskProgressMap.setMedalInfoMap(medalInfoMap);

        // 获取规则(包含一二级规则)
        List<MedalTaskRule> taskRules = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>().in(MedalTaskRule::getMedalTaskId, taskIds).eq(MedalTaskRule::getStatus, Constant.NO));
        if (CollUtil.isEmpty(taskRules)) {
            return Boolean.FALSE;
        }
        medalTaskProgressMap.setTaskRules(taskRules);

        // 规则详情
        List<Long> ruleIds = taskRules.stream().map(MedalTaskRule::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(ruleIds)) {
            return Boolean.FALSE;
        }
        List<MedalTaskRuleTarget> medalTaskRuleTargets = medalTaskRuleTargetManager.list(new LambdaQueryWrapper<MedalTaskRuleTarget>().in(MedalTaskRuleTarget::getMedalTaskRuleId, ruleIds));
        if (CollUtil.isEmpty(medalTaskRuleTargets)) {
            return Boolean.FALSE;
        }
        // 二级规则map
        Map<Long, MedalTaskRuleTarget> ruleTargetMap = medalTaskRuleTargets.stream().collect(Collectors.toMap(MedalTaskRuleTarget::getMedalTaskRuleId, Function.identity()));
        medalTaskProgressMap.setRuleTargetMap(ruleTargetMap);

        // 获取二级任务信息
        List<MedalTaskCompletion> medalTaskCompletions = medalTaskCompletionManager.list(new LambdaQueryWrapper<MedalTaskCompletion>()
                .in(MedalTaskCompletion::getMedalTaskId, taskIds)
                .eq(MedalTaskCompletion::getLevel, MedalRuleLevelEnum.TOP_LEVEL.getCode())
                .eq(MedalTaskCompletion::getStudentId, studentId)
                .orderByAsc(MedalTaskCompletion::getId));
        List<Long> completionIds = medalTaskCompletions.stream().map(MedalTaskCompletion::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(completionIds)) {
            return Boolean.FALSE;
        }
        medalTaskProgressMap.setCompletions(medalTaskCompletions);

        // 颁章明细
        List<MedalUserAcquireRecord> medalUserAcquireRecords = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>().in(MedalUserAcquireRecord::getCompletionId, completionIds));
        if (CollUtil.isEmpty(medalUserAcquireRecords)) {
            return Boolean.FALSE;
        }
        // 获取颁章明细状态map
        Map<Long, Integer> awardStatusMap = medalUserAcquireRecords.stream().collect(Collectors.toMap(MedalUserAcquireRecord::getCompletionId, MedalUserAcquireRecord::getAwardStatus));
        medalTaskProgressMap.setAwardStatusMap(awardStatusMap);

        return Boolean.TRUE;
    }

    /**
     * @param activityTasks   活动任务
     * @param taskRules       规则信息(包含一二级规则)
     * @param medalInfoMap    奖章信息map
     * @param activityInfoMap 活动信息map
     * @param ruleTargetMap   规则详情map
     * @param studentId       学生id
     * @return
     */
    private List<MedalTaskProgressVO> emptyCompletionHandle(List<MedalTask> activityTasks, List<MedalTaskRule> taskRules, Map<Long, MedalInfoVO> medalInfoMap, Map<Long, MedalActivityInfoVO> activityInfoMap, Map<Long, MedalTaskRuleTarget> ruleTargetMap, String studentId) {
        // 活动任务封装
        List<MedalTaskProgressVO> result = new ArrayList<>();
        HashMap<Long, List<BehaviourRecordDTO>> behaviourRecordMap = new HashMap<>();
        for (MedalTask activityTask : activityTasks) {

            MedalTaskProgressVO medalTaskProgressVO = new MedalTaskProgressVO();
            medalTaskProgressVO.setMedalTaskId(activityTask.getId());
            medalTaskProgressVO.setActivityId(activityTask.getMedalActivityId());
            medalTaskProgressVO.setStatus(1);
            // 设置奖章信息
            MedalInfoVO medalInfoVO = medalInfoMap.get(activityTask.getMedalInfoId());
            if (Objects.nonNull(medalInfoVO)) {
                medalTaskProgressVO.setMedalName(medalInfoVO.getName());
                medalTaskProgressVO.setLogoUrl(urlPrefix + medalInfoVO.getLogoUrl());
            }
            if (Objects.nonNull(activityInfoMap.get(activityTask.getMedalActivityId()))) {
                MedalActivityInfoVO medalActivityInfoVO = activityInfoMap.get(activityTask.getMedalActivityId());
                // 设置活动信息
                medalTaskProgressVO.setActivityName(medalActivityInfoVO.getActivityName());
                medalTaskProgressVO.setStartTime(medalActivityInfoVO.getStartTime());
                medalTaskProgressVO.setEndTime(medalActivityInfoVO.getEndTime());
            }
            Integer detailTaskNum = 0;
            List<MedalTaskRule> topRules = taskRules.stream().filter(s -> MedalRuleLevelEnum.TOP_LEVEL.getCode().equals(s.getLevel())).filter(s -> activityTask.getId().equals(s.getMedalTaskId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(topRules)) {
                MedalTaskRule medalTaskRule = CollUtil.getFirst(topRules);
                OuterLayerRuleDTO outerLayerRuleDTO = JSONUtil.toBean(medalTaskRule.getContent(), OuterLayerRuleDTO.class);
                detailTaskNum = outerLayerRuleDTO.getDetailTaskNum();
            }
            // 设置获章规则
            medalTaskProgressVO.setAcquireRule("任意完成以下" + detailTaskNum + "个任务，即可获章");

            // 获取该任务下有几种一级规则
            List<MedalTaskRule> subRules = taskRules.stream().filter(s -> activityTask.getId().equals(s.getMedalTaskId())).filter(s -> MedalRuleLevelEnum.SUB_LEVEL.getCode().equals(s.getLevel())).collect(Collectors.toList());

            behaviourRecordMap.clear();
            for (MedalTaskRule subRule : subRules) {
                // 获取规则下匹配的行为记录
                List<BehaviourRecordDTO> behaviourRecords = medalRuleMatchRecordMapper.listMatchRuleBehaviour(subRule.getId(), studentId);
                behaviourRecordMap.put(subRule.getId(), behaviourRecords);
            }

            List<TaskProgressDetailVO> taskDetails = new ArrayList<>();
            for (MedalTaskRule subRule : subRules) {
                TaskProgressDetailVO taskProgressDetailVO = new TaskProgressDetailVO();
                if (Objects.nonNull(ruleTargetMap.get(subRule.getId()))) {
                    MedalTaskRuleTarget medalTaskRuleTarget = ruleTargetMap.get(subRule.getId());

                    TargetLevelDTO targetLevelDTO = activityRuleService.encapsulationLevel(medalTaskRuleTarget.getSubmitId(), medalTaskRuleTarget.getSubmitType(), medalTaskRuleTarget.getTargetId());
                    if (StrUtil.isNotBlank(targetLevelDTO.getModuleName())) {
                        taskProgressDetailVO.setRuleName(targetLevelDTO.getModuleName());
                    }
                    if (StrUtil.isNotBlank(targetLevelDTO.getGroupName())) {
                        taskProgressDetailVO.setRuleName(targetLevelDTO.getGroupName());
                    }
                    if (StrUtil.isNotBlank(targetLevelDTO.getTargetName())) {
                        taskProgressDetailVO.setRuleName(targetLevelDTO.getTargetName());
                    }
                    if (StrUtil.isNotBlank(targetLevelDTO.getOptionName())) {
                        taskProgressDetailVO.setRuleName(targetLevelDTO.getOptionName());
                    }

                    taskProgressDetailVO.setTaskName(medalTaskRuleTarget.getName());
                    taskProgressDetailVO.setTaskRuleId(medalTaskRuleTarget.getMedalTaskRuleId());
                    // 获取一级任务详情
                    if (Objects.nonNull(ruleTargetMap.get(subRule.getId()))) {
                        MedalTaskRuleTarget ruleTarget = ruleTargetMap.get(subRule.getId());
                        if (MedalRuleTypeEnum.COMMENT_NUM.getCode().equals(subRule.getType())) {
                            taskProgressDetailVO.setRuleDemand("完成" + ruleTarget.getTargetValue() + "次");
                        }
                        if (MedalRuleTypeEnum.COMMENT_TOTAL.getCode().equals(subRule.getType())) {
                            taskProgressDetailVO.setRuleDemand("达到" + ruleTarget.getTargetValue() + "分");
                        }
                    }

                    List<BehaviourRecordDTO> behaviourRecords = behaviourRecordMap.get(subRule.getId());
                    // 根据点评次数计算任务完成数
                    if (MedalRuleTypeEnum.COMMENT_NUM.getCode().equals(subRule.getType())) {
                        // 计算总次数
                        int totalNum = behaviourRecords.size();
                        if (totalNum < Convert.toInt(medalTaskRuleTarget.getTargetValue())) {
                            taskProgressDetailVO.setCurrentProgress(totalNum + "/" + medalTaskRuleTarget.getTargetValue() + "次");
                        } else {
                            taskProgressDetailVO.setCurrentProgress(medalTaskRuleTarget.getTargetValue() + "/" + medalTaskRuleTarget.getTargetValue() + "次");
                        }
                    }

                    // 根据点评分数计算任务完成数
                    if (MedalRuleTypeEnum.COMMENT_TOTAL.getCode().equals(subRule.getType())) {
                        // 计算总分
                        BigDecimal totalScore = behaviourRecords.stream().map(BehaviourRecordDTO::getScore).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (totalScore.compareTo(medalTaskRuleTarget.getTargetValue()) == -1) {
                            taskProgressDetailVO.setCurrentProgress(totalScore + "/" + medalTaskRuleTarget.getTargetValue() + "分");
                        } else {
                            taskProgressDetailVO.setCurrentProgress(medalTaskRuleTarget.getTargetValue() + "/" + medalTaskRuleTarget.getTargetValue() + "分");
                        }

                    }
                    taskDetails.add(taskProgressDetailVO);
                }
            }

            medalTaskProgressVO.setTaskDetails(taskDetails);
            result.add(medalTaskProgressVO);

        }
        return result;
    }


    /**
     * // 计算任务进度
     *
     * @param taskDetails          封装的结构体
     * @param subRule              一级任务
     * @param ruleTargetMap        详情任务
     * @param behaviourRecordMap   行为记录
     * @param ruleCompletionNumMap 规则下匹配的行为记录map
     * @param completion           二级任务信息
     */
    private void calculateTaskProgress(List<TaskProgressDetailVO> taskDetails, MedalTaskRule subRule, Map<Long, MedalTaskRuleTarget> ruleTargetMap, HashMap<Long, List<BehaviourRecordDTO>> behaviourRecordMap, HashMap<Long, Integer> ruleCompletionNumMap, MedalTaskCompletion completion) {
        TaskProgressDetailVO taskProgressDetailVO = new TaskProgressDetailVO();
        if (Objects.nonNull(ruleTargetMap.get(subRule.getId()))) {
            MedalTaskRuleTarget medalTaskRuleTarget = ruleTargetMap.get(subRule.getId());

            TargetLevelDTO targetLevelDTO = activityRuleService.encapsulationLevel(medalTaskRuleTarget.getSubmitId(), medalTaskRuleTarget.getSubmitType(), medalTaskRuleTarget.getTargetId());
            if (StrUtil.isNotBlank(targetLevelDTO.getModuleName())) {
                taskProgressDetailVO.setRuleName(targetLevelDTO.getModuleName());
            }
            if (StrUtil.isNotBlank(targetLevelDTO.getGroupName())) {
                taskProgressDetailVO.setRuleName(targetLevelDTO.getGroupName());
            }
            if (StrUtil.isNotBlank(targetLevelDTO.getTargetName())) {
                taskProgressDetailVO.setRuleName(targetLevelDTO.getTargetName());
            }
            if (StrUtil.isNotBlank(targetLevelDTO.getOptionName())) {
                taskProgressDetailVO.setRuleName(targetLevelDTO.getOptionName());
            }

            taskProgressDetailVO.setTaskName(medalTaskRuleTarget.getName());
            taskProgressDetailVO.setTaskRuleId(medalTaskRuleTarget.getMedalTaskRuleId());
            // 获取一级任务详情
            if (Objects.nonNull(ruleTargetMap.get(subRule.getId()))) {
                MedalTaskRuleTarget ruleTarget = ruleTargetMap.get(subRule.getId());
                if (MedalRuleTypeEnum.COMMENT_NUM.getCode().equals(subRule.getType())) {
                    taskProgressDetailVO.setRuleDemand("完成" + ruleTarget.getTargetValue() + "次");
                }
                if (MedalRuleTypeEnum.COMMENT_TOTAL.getCode().equals(subRule.getType())) {
                    taskProgressDetailVO.setRuleDemand("达到" + ruleTarget.getTargetValue() + "分");
                }
            }


            // 计算进度
            List<BehaviourRecordDTO> behaviourRecords = behaviourRecordMap.get(subRule.getId());
            // 根据点评次数计算任务完成数
            if (MedalRuleTypeEnum.COMMENT_NUM.getCode().equals(subRule.getType())) {
                // 当前规则数
                Integer currentNum = ruleCompletionNumMap.get(subRule.getId());

                if (MedalAwardStatusEnum.ARTIFICIAL_WITHDRAW.getCode().equals(completion.getStatus())) {
                    taskProgressDetailVO.setCurrentProgress(medalTaskRuleTarget.getTargetValue() + "/" + medalTaskRuleTarget.getTargetValue() + "次");
                } else {
                    // 计算总次数
                    int totalNum = behaviourRecords.size();
                    // 计算任务可以完成数
                    int completeNum = totalNum / Convert.toInt(medalTaskRuleTarget.getTargetValue());

                    // 说明当前规则是完成的
                    if (currentNum <= completeNum) {
                        taskProgressDetailVO.setCurrentProgress(medalTaskRuleTarget.getTargetValue() + "/" + medalTaskRuleTarget.getTargetValue() + "次");
                    } else {
                        int preValue = totalNum - (currentNum - 1) * Convert.toInt(medalTaskRuleTarget.getTargetValue());

                        if (preValue > 0) {
                            // 说明当前是进行中的任务
                            taskProgressDetailVO.setCurrentProgress(preValue + "/" + medalTaskRuleTarget.getTargetValue() + "次");
                        } else {
                            // 说明一点也不完整该任务的要求
                            taskProgressDetailVO.setCurrentProgress("0/" + medalTaskRuleTarget.getTargetValue() + "次");
                        }
                    }
                }
                currentNum++;
                ruleCompletionNumMap.put(subRule.getId(), currentNum);
            }

            // 根据点评分数计算任务完成数
            if (MedalRuleTypeEnum.COMMENT_TOTAL.getCode().equals(subRule.getType())) {
                // 当前规则数
                Integer currentNum = ruleCompletionNumMap.get(subRule.getId());

                if (MedalAwardStatusEnum.ARTIFICIAL_WITHDRAW.getCode().equals(completion.getStatus())) {
                    taskProgressDetailVO.setCurrentProgress(medalTaskRuleTarget.getTargetValue() + "/" + medalTaskRuleTarget.getTargetValue() + "分");
                } else {
                    // 计算任务可以完成数
                    Integer completeNum;
                    // 计算总分
                    BigDecimal totalScore = behaviourRecords.stream().map(BehaviourRecordDTO::getScore).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 如果总分小于0
                    if (totalScore.compareTo(BigDecimal.ZERO) == -1) {
                        completeNum = 0;
                    } else {
                        // 计算任务完成数
                        completeNum = Convert.toInt(totalScore.divideToIntegralValue(medalTaskRuleTarget.getTargetValue()));
                    }

                    // 说明当前规则是完成的
                    if (currentNum <= completeNum) {
                        taskProgressDetailVO.setCurrentProgress(medalTaskRuleTarget.getTargetValue() + "/" + medalTaskRuleTarget.getTargetValue() + "分");
                    } else {
                        BigDecimal preValue = totalScore.subtract(medalTaskRuleTarget.getTargetValue().multiply(Convert.toBigDecimal(currentNum - 1)));
                        if (preValue.compareTo(BigDecimal.ZERO) > 0) {
                            // 说明当前是进行中的任务
                            taskProgressDetailVO.setCurrentProgress(preValue + "/" + medalTaskRuleTarget.getTargetValue() + "分");
                        } else {
                            // 说明一点也不完整该任务的要求
                            taskProgressDetailVO.setCurrentProgress("0/" + medalTaskRuleTarget.getTargetValue() + "分");
                        }
                    }
                }
                currentNum++;
                ruleCompletionNumMap.put(subRule.getId(), currentNum);
            }
            taskDetails.add(taskProgressDetailVO);
        }
    }

    /**
     * 添加进行中的任务
     *
     * @param completions 一个任务下的二级任务完成信息
     * @param studentId   学生id
     * @param medalTaskId 任务id
     */
    private void addNewCompletion(List<MedalTaskCompletion> completions, String studentId, Long medalTaskId) {
        MedalTaskCompletion medalTaskCompletion = new MedalTaskCompletion();
        medalTaskCompletion.setTenantId(WebUtil.getTenantId());
        medalTaskCompletion.setStudentId(WebUtil.getSchoolId());
        medalTaskCompletion.setStudentId(studentId);
        medalTaskCompletion.setMedalTaskId(medalTaskId);
        medalTaskCompletion.setStatus(MedalTaskCompleteStatusEnum.NO_COMPLETION.getCode());
        medalTaskCompletion.setLevel(MedalRuleLevelEnum.TOP_LEVEL.getCode());
        completions.add(medalTaskCompletion);
    }

}
