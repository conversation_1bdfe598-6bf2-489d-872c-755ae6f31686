package com.hailiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.entity.ThirdDataInfoPO;
import com.hailiang.model.dto.ThirdDataInfoDTO;

import java.util.List;
import java.util.Map;


/**
 * 接口
 *
 * <AUTHOR> 2023-02-17 17:40:34
 */
public interface ThirdDataInfoService extends IService<ThirdDataInfoPO>{

    IPage pageThirdDataInfo(Page page, ThirdDataInfoDTO thirdDataInfoDTO);

    boolean saveThirdDataInfo(ThirdDataInfoDTO thirdDataInfoDTO);

    boolean updateThirdDataInfo(ThirdDataInfoDTO thirdDataInfoDTO);

    boolean deleteThirdDataInfo(ThirdDataInfoDTO thirdDataInfoDTO);

    Integer updateFail(Long id, String remark);

    Integer updateSuccess(Long id, Long localRecordId, String campusId);

    Integer batchUpdateFail(List<ThirdDataInfoDTO> thirdDataInfoDTOS);

    Integer batchUpdateSuccess(List<ThirdDataInfoDTO> thirdDataInfoDTOS);

    /**
     * 根据第三方记录ID筛选出「新增」操作的记录
     * @param thirdRecordIdList
     * @return
     */
    Map<String, Long> mapByThirdRecordIds(List<String> thirdRecordIdList);

    Map<Long, String> mapByLocalRecordIds(List<Long> localRecordIdList);

    /**
     * 根据第三方记录ID筛选出「新增」操作的记录
     */
    List<ThirdDataInfoPO> listByThirdRecordIds(List<Long> businessIds);
}