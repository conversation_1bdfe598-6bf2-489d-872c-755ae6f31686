package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.dto.query.BehaviourRecordQueryDTO;
import com.hailiang.model.report.vo.ReportTermInfoVO;
import com.hailiang.model.response.studentmodel.CommonStudentAbilityModelResponse;
import com.hailiang.model.response.studentmodel.StudentAbilityModelDataResponse;
import com.hailiang.model.vo.ListCommonModuleDetailVOBehaviour;
import com.hailiang.model.vo.ListModuleDetailVOBehaviour;
import com.hailiang.model.vo.ListModuleDetailVODateNewInfo;
import com.hailiang.portrait.query.CommonStuPortraitPageQuery;
import com.hailiang.portrait.query.CommonStuPortraitQuery;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.portrait.vo.StuBehaviorInfoVO;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import com.hailiang.portrait.vo.StuPortraitInfoVO;

/**
 * 学生画像公共接口
 * @description:
 * @author: panjian
 * @create: 2024/9/9 17:44
 * @Version 1.0
 */
public interface CommonPortraitService {

    /**
     * 获取当前学期
     * @return
     */
    ReportTermInfoVO getCurrentTermVo(String classId);

    /**
     * 根据班级学生id获取当前学期
     * @param classId
     * @param studentId
     * @return
     */
    ReportTermInfoVO getCurrentTermVoByClassIdAndStudentId(String classId, String studentId);

    /**
     * 查询学生画像信息
     */
    StuPortraitInfoVO getSimpleStuPortraitInfo(CommonStuPortraitQuery dto);

    /**
     * 查询学生行为信息
     */
    StuBehaviorInfoVO getSimpleStuBehaviorInfo(CommonStuPortraitQuery dto);

    /**
     * 构建学生能力模型参数
     * @param behaviourRecordQueryDTO
     * @return
     */
    BehaviourRecordQueryDTO checkAndFillBehaviourRecordQueryDTO(BehaviourRecordQueryDTO behaviourRecordQueryDTO);

    /**
     *  分页获取五育得分明细
     * @param pageQuery
     * @return
     */
    Page<ListCommonModuleDetailVOBehaviour> getModuleDetailPage(CommonStuPortraitPageQuery pageQuery);

    /**
     * 获取学生能力模型
     * @param behaviourRecordQueryDTO
     * @return
     */
    CommonStudentAbilityModelResponse getStudentModel(BehaviourRecordQueryDTO behaviourRecordQueryDTO);

    /**
     * 检查并生成StuPortraitQuery
     *
     * @param dto
     */
    StuPortraitQuery checkAndBuildStuPortraitQuery(CommonStuPortraitQuery dto);
}