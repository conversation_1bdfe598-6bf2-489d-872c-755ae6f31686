package com.hailiang.service;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.medal.dto.MedalActivityEditDTO;
import com.hailiang.model.medal.dto.MedalActivityQueryDTO;
import com.hailiang.model.medal.dto.MedalActivitySaveDTO;
import com.hailiang.model.medal.vo.MedalActivityVO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;

import java.util.List;

/**
 * @Author: JJL
 * @Date: 2023/5/31 14:29
 */
public interface MedalActivityService {
    /**
     * 新增争章活动
     *
     * @param dto
     * @return
     */
    Long saveMedalActivity(MedalActivitySaveDTO dto, Integer copyFlag);

    /**
     * 修改争章活动
     *
     * @param dto
     * @return
     */
    void updateMedalActivity(MedalActivityEditDTO dto);

    /**
     * 查询争章活动(分页)
     * @param dto
     * @return
     */
    Page<MedalActivityVO> pageMedalActivity(MedalActivityQueryDTO dto);

    /**
     * 争章活动详情
     * @param id
     * @return
     */
    MedalActivityVO getMedalActivity(Long id);

    /**
     * 删除争章活动
     * @param id
     * @return
     */
    void deleteMedalActivity(Long id);

    /**
     * 发布争章活动
     * @param id
     * @return
     */
    void publishMedalActivity(Long id);
    /**
     * 停用争章活动
     * @param id
     * @return
     */
    void disableMedalActivity(Long id);

    /**
     * 复制争章活动
     * @param id
     * @return
     */
    void copyMedalActivity(Long id);

    /**
     * 获取该校区下所有年级（除去幼儿园）
     * @return
     */
    List<EduOrgTreeVO> listGradeByCampusId();

    /**
     * 争章活动状态转换
     * @param jobStartTime
     */
    void changeMedalActivityStatus(DateTime jobStartTime);
}
