package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import com.hailiang.constant.Constant;
import com.hailiang.exception.BizException;
import com.hailiang.manager.TargetManager;
import com.hailiang.manager.TargetUserManager;
import com.hailiang.model.dto.request.QueryTargetRequest;
import com.hailiang.model.dto.response.TargetCommonResponse;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TargetUserPO;
import com.hailiang.service.TargetNewService;
import com.hailiang.service.TargetUserService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 指标新服务
 *
 * @Description: 指标新服务
 * @Author: Jovi
 * @Date: Created in 2024-12-02
 * @Version: 2.0.0
 */
@Service
@Slf4j
public class TargetNewServiceImpl implements TargetNewService {

    @Resource
    private TargetManager targetManager;

    @Resource
    private TargetUserService targetUserService;

    @Resource
    private TargetUserManager targetUserManager;

    @Override
    public List<TargetCommonResponse> listTargetByGroupId(QueryTargetRequest request) {

        Long groupId = request.getTargetGroupId();

        Set<Long> groupIds = new HashSet<>();
        groupIds.add(groupId);

        List<Target> targetPOs = targetManager.listTargetIdsByCampusIdAndGroupIds(WebUtil.getCampusId(), groupIds)
                .stream()
                .filter(item -> Constant.YES.equals(item.getTargetStatus()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(targetPOs)) {
            log.warn("该分组下不存在指标，groupId={}", groupId);
            throw new BizException("该分组下不存在指标");
        }

        Map<Long/*targetId*/, Target> targetMap = targetPOs
                .stream()
                .collect(Collectors.toMap(Target::getId, t -> t));

        List<Long> targetIds = new ArrayList<>(targetMap.keySet());

        Long staffIdLong = WebUtil.getStaffIdLong();
        List<Long> filterValidTargetIds = targetUserService.filterValidTargetIds(staffIdLong, targetIds);

        if (CollectionUtils.isEmpty(filterValidTargetIds)) {
            log.warn("该用户下不存在有效的指标，staffId={}", staffIdLong);
            throw new BizException("当前用户下不存在有效的指标");
        }

        List<Target> filterTargetPOs = new ArrayList<>();

        for (Long filterValidTargetId : filterValidTargetIds) {
            filterTargetPOs.add(targetMap.get(filterValidTargetId));
        }

        List<TargetCommonResponse> result = filterTargetPOs
                .stream()
                .map(target -> TargetCommonResponse.builder()
                        .targetName(target.getTargetName())
                        .targetId(target.getId())
                        .sortIndex(target.getSortIndex())
                        .build()).sorted(Comparator.comparing(TargetCommonResponse::getSortIndex))
                .collect(Collectors.toList());

        if (CharSequenceUtil.isBlank(request.getSubjectId())) {
            return result;
        }

        /**
         * 填报类型编码： 用于唯一确定各层级id、家长id或教师id，数字枚举
         *  1 组织架构id  2.角色id 3:教职工staffId 4:家长id 10:课程id
         */
        List<TargetUserPO> targetUserPOS = targetUserManager.listBySchoolIdAndSubjectIdAndType(WebUtil.getSchoolId(),
                Convert.toLong(request.getSubjectId()), 10);
        // 找不到课程配置的指标则返回全部指标
        if(CollUtil.isEmpty(targetUserPOS)){
            return result;
        }

        // 课程配置了指标则返回课程关联的指标列表
        List<Long> targetIdList = targetUserPOS.stream().map(TargetUserPO::getTargetId).distinct()
                .collect(Collectors.toList());
        return result.stream().filter(item -> targetIdList.contains(item.getTargetId())).collect(Collectors.toList());
    }


}
