package com.hailiang.service;

import com.hailiang.model.dto.query.CampusParamSaveDTO;
import com.hailiang.model.vo.CampusParamBaseInfoVO;

/**
* <AUTHOR>
* @description 针对表【campus_param(校区参数表)】的数据库操作Service
* @createDate 2023-07-21 10:28:40
*/
public interface CampusParamService {

    CampusParamBaseInfoVO getCampusParam(String campusId);

    Integer getCampusInitScore(String campusId);

    boolean saveCampusParam(CampusParamSaveDTO param);
}
