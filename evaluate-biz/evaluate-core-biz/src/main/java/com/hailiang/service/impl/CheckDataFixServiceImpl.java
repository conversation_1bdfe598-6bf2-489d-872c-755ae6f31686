package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hailiang.exception.BizException;
import com.hailiang.manager.CheckClassInfoManager;
import com.hailiang.manager.CheckClassOptionInfoManager;
import com.hailiang.manager.CheckClassRelationManager;
import com.hailiang.model.dto.check.CheckDataFixDTO;
import com.hailiang.model.entity.CheckClassInfo;
import com.hailiang.model.entity.CheckClassOptionInfo;
import com.hailiang.model.entity.CheckClassRelation;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.service.CheckDataFixService;
import com.hailiang.service.CheckInfoService;
import com.hailiang.util.R;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/12 14:03
 */
@Service
public class CheckDataFixServiceImpl implements CheckDataFixService {

    @Resource
    private CheckClassInfoManager checkClassInfoManager;

    @Resource
    private CheckClassOptionInfoManager checkClassOptionInfoManager;

    @Resource
    private CheckClassRelationManager checkClassRelationManager;

    @Resource
    private BasicInfoRemote basicInfoRemote;

    /**
     * 检查项学生提交创建人异常数据修复
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 600)
    public void checkClassInfoData(CheckDataFixDTO dto) {

        List<CheckClassInfo> classInfos = new ArrayList<>();
        if (dto.getType().equals(1)) {
            classInfos = checkClassInfoManager.list(new LambdaQueryWrapper<CheckClassInfo>()
                    .eq(CheckClassInfo::getCreateBy, "123"));
        }
        if (dto.getType().equals(2)) {
            Assert.notEmpty(dto.getCheckClassInfoIds(), () -> new BizException("id不能为空"));
            classInfos = checkClassInfoManager.list(new LambdaQueryWrapper<CheckClassInfo>()
                    .eq(CheckClassInfo::getCreateBy, "123")
                    .in(CheckClassInfo::getId, dto.getCheckClassInfoIds()));
        }


        if (CollUtil.isEmpty(classInfos)) {
            return;
        }


        List<Long> clasInfoIds = classInfos.stream().map(CheckClassInfo::getId).distinct().collect(Collectors.toList());


        List<CheckClassOptionInfo> checkClassOptionInfos = checkClassOptionInfoManager.list(new LambdaQueryWrapper<CheckClassOptionInfo>().in(CheckClassOptionInfo::getCheckClassInfoId, clasInfoIds));
        Map<Long, List<CheckClassOptionInfo>> optionInfoGroup = checkClassOptionInfos.stream().collect(Collectors.groupingBy(CheckClassOptionInfo::getCheckClassInfoId));

        List<CheckClassRelation> checkClassRelations = checkClassRelationManager.list(new LambdaQueryWrapper<CheckClassRelation>().in(CheckClassRelation::getCheckClassInfoId, clasInfoIds));
        Map<Long, List<CheckClassRelation>> relationGroup = checkClassRelations.stream().collect(Collectors.groupingBy(CheckClassRelation::getCheckClassInfoId));

        List<CheckClassInfo> classInfoList = new ArrayList<>();
        List<CheckClassOptionInfo> checkClassOptionInfoList = new ArrayList<>();
        List<CheckClassRelation> checkClassRelationList = new ArrayList<>();
        for (CheckClassInfo classInfo : classInfos) {
            EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
            eduStudentPageQueryDTO.setSchoolId(Convert.toLong(classInfo.getSchoolId()));
            eduStudentPageQueryDTO.setCampusId(Convert.toLong(classInfo.getCampusId()));
            //eduStudentPageQueryDTO.setClassId(Convert.toLong(classInfo.getClassId()));
            eduStudentPageQueryDTO.setNameLike(classInfo.getCheckUserName());
            List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
            if (CollUtil.isNotEmpty(eduStudentInfoVOS)) {
                EduStudentInfoVO studentInfoVO = CollUtil.getFirst(eduStudentInfoVOS);
                classInfo.setCreateBy(Convert.toStr(studentInfoVO.getId()));
                classInfoList.add(classInfo);


                List<CheckClassOptionInfo> optionInfos = optionInfoGroup.get(classInfo.getId());
                if (CollUtil.isNotEmpty(optionInfos)) {
                    for (CheckClassOptionInfo optionInfo : optionInfos) {
                        optionInfo.setCreateBy(Convert.toStr(studentInfoVO.getId()));
                        checkClassOptionInfoList.add(optionInfo);
                    }
                }


                List<CheckClassRelation> relations = relationGroup.get(classInfo.getId());
                if (CollUtil.isNotEmpty(relations)) {
                    for (CheckClassRelation relation : relations) {
                        relation.setCreateBy(Convert.toStr(studentInfoVO.getId()));
                        checkClassRelationList.add(relation);
                    }
                }
            }
        }
        checkClassInfoManager.updateBatchById(classInfoList);
        checkClassOptionInfoManager.updateBatchById(checkClassOptionInfoList);
        checkClassRelationManager.updateBatchById(checkClassRelationList);
    }
}
