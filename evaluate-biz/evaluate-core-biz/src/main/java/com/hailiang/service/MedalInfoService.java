package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.medal.dto.MedalInfoDeleteDTO;
import com.hailiang.model.medal.dto.MedalInfoEditDTO;
import com.hailiang.model.medal.dto.MedalInfoQueryDTO;
import com.hailiang.model.medal.dto.MedalInfoSaveDTO;
import com.hailiang.model.medal.vo.MedalCatalogueInfoVO;
import com.hailiang.model.medal.vo.MedalInfoVO;

import java.util.List;

/**
 * @Author: JJL
 * @Date: 2023/5/31 14:31
 */
public interface MedalInfoService {
    /**
     * 新增奖章
     *
     * @param dto
     * @return
     */
    void saveMedalInfo(MedalInfoSaveDTO dto);

    /**
     * 删除奖章
     *
     * @param dto
     * @return
     */
    void deleteMedalInfo(MedalInfoDeleteDTO dto);

    /**
     * 修改奖章
     *
     * @param dto
     * @return
     */
    void updateMedalInfo(MedalInfoEditDTO dto);

    /**
     * 查询奖章(分页)
     * @param dto
     * @return
     */
    Page<MedalInfoVO> pageMedalInfo(MedalInfoQueryDTO dto);

    /**
     * 奖章详情
     * @param id
     * @return
     */
    MedalInfoVO getMedalInfo(Long id);

    /**
     * 查询奖章(不分页)
     * @return
     */
    List<MedalInfoVO> listMedalDetails();
    /**
     * 查询奖章(不分页)
     * @return
     */
    List<MedalInfoVO> listMedalDetailsBySchoolId(String schoolId);

    /**
     * 查询奖章(二级含章目)
     * @return
     */
    List<MedalCatalogueInfoVO> listMedalCatalogueInfos();
}
