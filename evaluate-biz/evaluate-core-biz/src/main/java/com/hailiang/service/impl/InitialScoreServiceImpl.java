package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.PointMqDataOperateTypeEnum;
import com.hailiang.enums.motivate.CoinExchangeOperateTypeEnum;
import com.hailiang.enums.motivate.CoinExchangeStrategyEnum;
import com.hailiang.enums.point.DataOperateTypeEnum;
import com.hailiang.manager.EvaluateInitialScoreLogManager;
import com.hailiang.manager.InitialScoreManager;
import com.hailiang.model.dto.motivate.BehaviourPointExchangeDTO;
import com.hailiang.model.dto.query.InitScoreSaveRequest;
import com.hailiang.model.dto.query.InitialScoreInnerDTO;
import com.hailiang.model.entity.EvaluateInitialScoreLogPO;
import com.hailiang.model.entity.InitialScorePO;
import com.hailiang.model.point.dto.PointInitialMsgConvertDTO;
import com.hailiang.model.vo.InitScoreVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.service.ConvertInitialService;
import com.hailiang.service.InitialScoreService;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.hailiang.common.cache.constant.RedisKeyConstants.INIT_MANUAL_KEY;

/**
 * 初始分业务处理类
 *
 * @Description: 初始分业务处理类
 * @Author: Jovi
 * @Date: Created in 2024/11/8
 * @Version: 2.0.0
 */
@Service
@Slf4j
public class InitialScoreServiceImpl implements InitialScoreService {


    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private InitialScoreManager initialScoreManager;

    @Resource
    private EvaluateInitialScoreLogManager evaluateInitialScoreLogManager;

    @Resource
    private ConvertInitialService convertInitialService;

    @Resource
    private RedisUtil redisUtil;


    /**
     * 查询初始分
     *
     * @return
     */
    @Override
    public InitScoreVO getInitScore() {

        InitScoreVO initScoreVO = new InitScoreVO();

        String tenantId = WebUtil.getTenantId();
        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();

        TermVo earlyTerm = getTermVo(schoolId, campusId);
        if (earlyTerm == null) {
            log.warn("【手动分配初始分】-【获取当前学期为空】-【不分配初始分】,tenantId：【{}】，schoolId：【{}】，campusId：【{}】", tenantId, schoolId, campusId);
            return null;
        }

        List<InitialScorePO> existOldInitialScorePOList = initialScoreManager.listByBaseInfoOrderByAllocationTimeDesc(
                tenantId,
                schoolId,
                campusId,
                earlyTerm.getSchoolYear(),
                earlyTerm.getTermName());

        if (CollUtil.isEmpty(existOldInitialScorePOList)) {
            log.warn("应校区初始分不存在，tenantId：【{}】，schoolId：【{}】，campusId：【{}】", tenantId, schoolId, campusId);
            return initScoreVO;
        }


        InitialScorePO initialScorePO = existOldInitialScorePOList.get(0);
        initScoreVO.setApportionFlag(initialScorePO.getApportionFlag());
        initScoreVO.setExchangeFlag(initialScorePO.getExchangeFlag());
        initScoreVO.setSplitFlag(initialScorePO.getSplitFlag());


        List<InitialScoreInnerDTO> initialScoreList = new ArrayList<>();
        for (InitialScorePO existOldInitialScorePO : existOldInitialScorePOList) {
            initialScoreList.add(new InitialScoreInnerDTO(existOldInitialScorePO.getInitialScore(), existOldInitialScorePO.getInitialScoreType()));
        }

        initScoreVO.setInitialScoreList(initialScoreList);

        List<EvaluateInitialScoreLogPO> evaluateInitialScoreLogPOS = evaluateInitialScoreLogManager.listByBaseInfo(
                tenantId,
                schoolId,
                campusId,
                initialScorePO.getSchoolYear(),
                initialScorePO.getTermName());


        if (CollUtil.isEmpty(evaluateInitialScoreLogPOS)) {
            log.warn("初始分调整记录不存在，tenantId：【{}】，schoolId：【{}】，campusId：【{}】", tenantId, schoolId, campusId);
            return initScoreVO;
        }

        List<String> details = buildInitialLog(evaluateInitialScoreLogPOS);

        initScoreVO.setDetails(details);

        return initScoreVO;
    }

    /**
     * 构造初始分日志记录返回出参
     *
     * @param evaluateInitialScoreLogPOS
     * @return
     */
    private List<String> buildInitialLog(List<EvaluateInitialScoreLogPO> evaluateInitialScoreLogPOS) {

        Map<Long/*allocationId*/, List<EvaluateInitialScoreLogPO>> batchMap = evaluateInitialScoreLogPOS
                .stream()
                .collect(Collectors.groupingBy(
                        EvaluateInitialScoreLogPO::getAllocationId,
                        LinkedHashMap::new,
                        Collectors.collectingAndThen(Collectors.toList(), list -> {
                            list.sort(Comparator.comparing(EvaluateInitialScoreLogPO::getAllocationTime).reversed());
                            return list;
                        })
                ));


        List<String> details = new ArrayList<>();

        batchMap.forEach((allocationId, evaluateInitialScoreLogPOList) -> {

            String submitTimeStr = DateUtil.formatDateTime(evaluateInitialScoreLogPOList.get(0).getAllocationTime());

            StringBuilder sb = new StringBuilder();

            sb.append(submitTimeStr).append("，");

            evaluateInitialScoreLogPOList.sort(Comparator.comparing(EvaluateInitialScoreLogPO::getInitialScoreType));

            for (EvaluateInitialScoreLogPO evaluateInitialScoreLogPO : evaluateInitialScoreLogPOList) {

                String detail;
                if (evaluateInitialScoreLogPO.getBeforeAllocationScore() != null) {
                    detail = String.format("【%s】由【%s】调整为【%s】",
                            ModuleEnum.getModuleNameV2ForInitial(evaluateInitialScoreLogPO.getInitialScoreType()),
                            evaluateInitialScoreLogPO.getBeforeAllocationScore(),
                            evaluateInitialScoreLogPO.getAllocationScore());
                } else {
                    detail = String.format("【%s】已初始化为【%s】",
                            ModuleEnum.getModuleNameV2ForInitial(evaluateInitialScoreLogPO.getInitialScoreType()),
                            evaluateInitialScoreLogPO.getAllocationScore());
                }

                sb.append(detail).append("，");


            }

            if (sb.length() > 0) {
                sb.setLength(sb.length() - 1); // 移除最后一个逗号
            }

            details.add(sb.toString());

        });
        return details;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveInitScoreJob(String schoolId, String campusId, TermVo newTerm) {

        //同一批次ID
        Long submitBatchId = SnowFlakeIdUtil.nextId();
        Date submitDate = new Date();


        List<InitialScorePO> existOldInitialScorePOList = initialScoreManager
                .listBySchoolIdAndCampusIdOrderByAllocationTimeDesc(schoolId, campusId);

        //此处无需判空，因为在前面已经是按照相关条件搜索出的结果，且此处取的是最近一次分配的数据
        InitialScorePO existOldInitialScorePO = existOldInitialScorePOList.get(0);
        String tenantId = existOldInitialScorePO.getTenantId();

        if (existOldInitialScorePO.getApportionFlag() == 0) {
            log.warn("【定时任务】-【分配初始分】-【分配初始分标识为空】-【直接跳过】-【初始分配置信息:{}】", existOldInitialScorePO);
            return true;
        }

        if (existOldInitialScorePO.getSchoolYear().equals(newTerm.getSchoolYear())
                && existOldInitialScorePO.getTermName().equals(newTerm.getTermName())) {
            log.warn("该校区【{}】初始分已设置，定时任务不再重新设置", campusId);
            return true;
        }

        List<InitialScorePO> lastTermInitialScorePO = existOldInitialScorePOList
                .stream()
                .filter(item -> DateUtil.isSameTime(item.getAllocationTime(), existOldInitialScorePO.getAllocationTime()))
                .collect(Collectors.toList());


        List<InitialScorePO> newInitialScorePOList = new ArrayList<>();

        Map<Integer/*initialScoreType*/, BigDecimal/*initialScore*/> lastTermInitialScoreMap =
                lastTermInitialScorePO
                        .stream()
                        .collect(Collectors.toMap(InitialScorePO::getInitialScoreType, InitialScorePO::getInitialScore));


        boolean baseFlag = saveJobBaseInfo(
                schoolId,
                campusId,
                newTerm,
                lastTermInitialScoreMap,
                tenantId,
                submitDate,
                existOldInitialScorePO,
                submitBatchId,
                newInitialScorePOList);


        boolean logSaveFlag = saveJobLog(
                schoolId,
                campusId,
                newInitialScorePOList,
                submitDate,
                tenantId,
                submitBatchId);


        if (baseFlag && logSaveFlag) {

            //开启转积分，需要新增一条初始分转积分消息操作
            if (existOldInitialScorePO.getExchangeFlag() == 0) {
                return true;
            }

            PointInitialMsgConvertDTO pointInitialMsgConvertDTO = new PointInitialMsgConvertDTO();
            pointInitialMsgConvertDTO.setDataOperateType(DataOperateTypeEnum.CREATE.getCode());
            pointInitialMsgConvertDTO.setTenantId(tenantId);
            pointInitialMsgConvertDTO.setSchoolId(schoolId);
            pointInitialMsgConvertDTO.setCampusId(campusId);
            BigDecimal newScore = lastTermInitialScoreMap.get(ModuleEnum.OTHER.getCode());

            pointInitialMsgConvertDTO.setScoreType(newScore.compareTo(BigDecimal.ZERO) >= 0 ? 1 : 2);
            pointInitialMsgConvertDTO.setScore(newScore);
            pointInitialMsgConvertDTO.setScoreValue(newScore.abs());
            pointInitialMsgConvertDTO.setCreateBy("Job");
            pointInitialMsgConvertDTO.setCreateTime(submitDate);
            pointInitialMsgConvertDTO.setBusinessSource(3);
            pointInitialMsgConvertDTO.setOperateType(1);

            // 发送积分金币转换mq(新)
            BehaviourPointExchangeDTO behaviourPointExchangeDTO = new BehaviourPointExchangeDTO();
            behaviourPointExchangeDTO.setBusinessSource(CoinExchangeStrategyEnum.TERM_INIT.getCode());
            behaviourPointExchangeDTO.setPointInitialMsgConvertDTO(pointInitialMsgConvertDTO);
            convertInitialService.sendBehaviourExchangeMq(CollUtil.newArrayList(behaviourPointExchangeDTO));

            return convertInitialService.initialScoreSendMQ(1, 1, submitDate, pointInitialMsgConvertDTO);

        }

        return false;
    }

    /**
     * 定时任务保存初始分操作记录信息
     *
     * @param schoolId
     * @param campusId
     * @param newInitialScorePOList
     * @param submitDate
     * @param tenantId
     * @param submitBatchId
     * @return
     */
    private boolean saveJobLog(String schoolId,
                               String campusId,
                               List<InitialScorePO> newInitialScorePOList,
                               Date submitDate,
                               String tenantId,
                               Long submitBatchId) {

        //操作记录始终是添加，所以每次都是新增List
        List<EvaluateInitialScoreLogPO> newEvaluateInitialScoreLogPOS = new ArrayList<>();


        for (InitialScorePO scorePO : newInitialScorePOList) {
            EvaluateInitialScoreLogPO evaluateInitialScoreLogPO = new EvaluateInitialScoreLogPO();

            evaluateInitialScoreLogPO.setAllocationScore(scorePO.getInitialScore());
            evaluateInitialScoreLogPO.setScoreValueAbs(scorePO.getInitialScore().abs());
            evaluateInitialScoreLogPO.setScoreType(scorePO.getInitialScore().compareTo(BigDecimal.ZERO) >= 0 ? 1 : 2);
            evaluateInitialScoreLogPO.setAllocationTime(submitDate);
            evaluateInitialScoreLogPO.setInitialScoreType(scorePO.getInitialScoreType());

            evaluateInitialScoreLogPO.setTenantId(tenantId);
            evaluateInitialScoreLogPO.setSchoolId(schoolId);
            evaluateInitialScoreLogPO.setCampusId(campusId);
            evaluateInitialScoreLogPO.setAllocationId(submitBatchId);
            evaluateInitialScoreLogPO.setSchoolYear(scorePO.getSchoolYear());
            evaluateInitialScoreLogPO.setTermName(scorePO.getTermName());
            evaluateInitialScoreLogPO.setCreateBy("Job");
            evaluateInitialScoreLogPO.setCreateTime(submitDate);

            newEvaluateInitialScoreLogPOS.add(evaluateInitialScoreLogPO);
        }

        return evaluateInitialScoreLogManager.saveBatch(newEvaluateInitialScoreLogPOS);
    }

    /**
     * 定时任务保存初始分基础信息
     *
     * @param schoolId
     * @param campusId
     * @param newTerm
     * @param existOldEvaluateInitialScoreLogPOSMap
     * @param tenantId
     * @param submitDate
     * @param existOldInitialScorePO
     * @param submitBatchId
     * @param newInitialScorePOList
     * @return
     */
    private boolean saveJobBaseInfo(String schoolId,
                                    String campusId,
                                    TermVo newTerm,
                                    Map<Integer/*initialScoreType*/, BigDecimal/*initialScore*/> existOldEvaluateInitialScoreLogPOSMap,
                                    String tenantId,
                                    Date submitDate,
                                    InitialScorePO existOldInitialScorePO,
                                    Long submitBatchId,
                                    List<InitialScorePO> newInitialScorePOList) {


        ModuleEnum.moduleMap.forEach((moduleEnum, moduleName) -> {

            InitialScorePO newInitialScorePO = new InitialScorePO();

            newInitialScorePO.setTenantId(tenantId);
            newInitialScorePO.setSchoolId(schoolId);
            newInitialScorePO.setCampusId(campusId);
            newInitialScorePO.setCreateBy("Job");
            newInitialScorePO.setCreateTime(submitDate);
            newInitialScorePO.setAllocationTime(submitDate);
            newInitialScorePO.setSchoolYear(newTerm.getSchoolYear());
            newInitialScorePO.setTermName(newTerm.getTermName());
            newInitialScorePO.setInitialScoreType(moduleEnum);
            newInitialScorePO.setApportionFlag(existOldInitialScorePO.getApportionFlag());
            newInitialScorePO.setExchangeFlag(existOldInitialScorePO.getExchangeFlag());
            newInitialScorePO.setSplitFlag(existOldInitialScorePO.getSplitFlag());
            newInitialScorePO.setAllocationId(submitBatchId);

            BigDecimal initialScore = existOldEvaluateInitialScoreLogPOSMap.get(moduleEnum);

            if (initialScore != null) {
                //总分
                if (moduleEnum == ModuleEnum.OTHER.getCode()) {
                    newInitialScorePO.setInitialScore(initialScore);
                }
                //五育初始分，且拆分五育标识为1
                else if (existOldInitialScorePO.getSplitFlag() == 1) {
                    newInitialScorePO.setInitialScore(initialScore);
                } else {
                    newInitialScorePO.setInitialScore(BigDecimal.ZERO);
                }
            } else {
                newInitialScorePO.setInitialScore(BigDecimal.ZERO);
            }

            newInitialScorePOList.add(newInitialScorePO);
        });

        return initialScoreManager.saveBatch(newInitialScorePOList);
    }

    /**
     * 提交初始分
     *
     * @param initScoreSaveRequest
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveInitScore(InitScoreSaveRequest initScoreSaveRequest) {

        log.info("【手动分配初始分】-【保存初始分】-【开始】,入参:{}", initScoreSaveRequest);

        //同一批次ID
        Long submitBatchId = SnowFlakeIdUtil.nextId();
        Date submitDate = new Date();

        String tenantId = WebUtil.getTenantId();
        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();
        String staffId = WebUtil.getStaffId();

        String key = StrUtil.format(INIT_MANUAL_KEY, tenantId, schoolId, campusId);

        if (redisUtil.get(key) != null) {
            return "校区初始分计算中，请勿频繁操作，稍后重试";
        }

        String errorMsg = validateParamOrInitScore(initScoreSaveRequest);
        if (errorMsg != null) {
            return errorMsg;
        }

        TermVo earlyTerm = getTermVo(schoolId, campusId);
        if (earlyTerm == null) {
            log.warn("【手动分配初始分】-【获取当前学期为空】-【不分配初始分】,入参:{}", JSONUtil.toJsonStr(initScoreSaveRequest));
            return "该校区当前学期设置为空，不予分配初始分";
        }

        //根据租户ID、学校ID、校区ID、学年、学期获取当前初始分信息
        List<InitialScorePO> beforeInitialScorePOList = initialScoreManager.listByBaseInfoOrderByAllocationTimeDesc(
                tenantId,
                schoolId,
                campusId,
                earlyTerm.getSchoolYear(),
                earlyTerm.getTermName());

        //true 有分值变动 false 无分值变动
        List<InitialScoreInnerDTO> initialScoreList = initScoreSaveRequest.getInitialScoreList();

        boolean initialScoreChangedFlag = isInitialScoreChanged(initialScoreList, beforeInitialScorePOList);

        if (!isChanged(initScoreSaveRequest, beforeInitialScorePOList) && !initialScoreChangedFlag) {
            return "初始分设置无变动";
        }


        Map<Integer/*initialScoreType*/, BigDecimal/*initialScore*/> requestScoreMap = initialScoreList
                .stream()
                .collect(Collectors.toMap(InitialScoreInnerDTO::getInitialScoreType, InitialScoreInnerDTO::getInitialScore));

        Map<Integer/*initialScoreType*/, BigDecimal/*initialScore*/> beforeInitialScorePOListScoreMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(beforeInitialScorePOList)) {
            beforeInitialScorePOListScoreMap = beforeInitialScorePOList.
                    stream()
                    .collect(Collectors.toMap(InitialScorePO::getInitialScoreType, InitialScorePO::getInitialScore));

        }


        Boolean baseFlag = saveInitialScorePO(
                initScoreSaveRequest,
                requestScoreMap,
                beforeInitialScorePOList,
                submitDate,
                earlyTerm,
                submitBatchId,
                staffId,
                tenantId,
                schoolId,
                campusId);

        Boolean logSaveFlag = true;

        if (initialScoreChangedFlag) {
            logSaveFlag = saveInitialScoreLog(
                    beforeInitialScorePOListScoreMap,
                    initScoreSaveRequest,
                    tenantId,
                    schoolId,
                    campusId,
                    staffId,
                    submitDate,
                    submitBatchId, earlyTerm);
        }


        if (baseFlag && logSaveFlag) {
            sendMq(initScoreSaveRequest,
                    tenantId,
                    schoolId,
                    campusId,
                    requestScoreMap,
                    staffId,
                    submitDate,
                    beforeInitialScorePOList,
                    beforeInitialScorePOListScoreMap);
        }

        //同一校区缓存10分钟
        redisUtil.set(key, initScoreSaveRequest, CacheConstants.TEN_MINUTE);

        if (baseFlag && logSaveFlag) {
            return "初始分设置成功";
        }
        return "初始分设置失败，请联系管理员";
    }

    /**
     * 手动分配初始分发送转积分消息
     *
     * @param initScoreSaveRequest
     * @param tenantId
     * @param schoolId
     * @param campusId
     * @param requestScoreMap
     * @param staffId
     * @param submitDate
     * @param beforeInitialScorePOList
     * @param beforeInitialScorePOListScoreMap
     */
    private void sendMq(InitScoreSaveRequest initScoreSaveRequest,
                        String tenantId,
                        String schoolId,
                        String campusId,
                        Map<Integer, BigDecimal> requestScoreMap,
                        String staffId,
                        Date submitDate,
                        List<InitialScorePO> beforeInitialScorePOList,
                        Map<Integer, BigDecimal> beforeInitialScorePOListScoreMap) {

        //开启转积分，需要新增一条初始分转积分消息操作
        if (initScoreSaveRequest.getExchangeFlag() == 1) {
            PointInitialMsgConvertDTO pointInitialMsgConvertDTO = new PointInitialMsgConvertDTO();
            pointInitialMsgConvertDTO.setDataOperateType(DataOperateTypeEnum.CREATE.getCode());
            pointInitialMsgConvertDTO.setTenantId(tenantId);
            pointInitialMsgConvertDTO.setSchoolId(schoolId);
            pointInitialMsgConvertDTO.setCampusId(campusId);
            BigDecimal newScore = requestScoreMap.get(ModuleEnum.OTHER.getCode());

            pointInitialMsgConvertDTO.setScoreType(newScore.compareTo(BigDecimal.ZERO) >= 0 ? 1 : 2);
            pointInitialMsgConvertDTO.setScore(newScore);
            pointInitialMsgConvertDTO.setScoreValue(newScore.abs());
            pointInitialMsgConvertDTO.setCreateBy(staffId);
            pointInitialMsgConvertDTO.setCreateTime(submitDate);

            // 发送积分金币转换mq(新)
            BehaviourPointExchangeDTO behaviourPointExchangeDTO = new BehaviourPointExchangeDTO();
            behaviourPointExchangeDTO.setPointInitialMsgConvertDTO(pointInitialMsgConvertDTO);
            behaviourPointExchangeDTO.setBusinessSource(CoinExchangeStrategyEnum.MANUAL_ALLOCATION_INIT.getCode());
            behaviourPointExchangeDTO.setOperateType(CoinExchangeOperateTypeEnum.CREATE.getCode());
            convertInitialService.sendBehaviourExchangeMq(CollUtil.newArrayList(behaviourPointExchangeDTO));

            convertInitialService.initialScoreSendMQ(1, 1, submitDate, pointInitialMsgConvertDTO);

        }

        //不开启转积分，或者开启转积分的情况下，只要有原始记录且原始记录是开启转积分的，则需要删除（撤回）原来的初始分转积分的记录
        if (!CollectionUtils.isEmpty(beforeInitialScorePOList)
                && beforeInitialScorePOList.get(0).getExchangeFlag() == 1) {

            PointInitialMsgConvertDTO pointInitialMsgConvertDTO = new PointInitialMsgConvertDTO();
            pointInitialMsgConvertDTO.setDataOperateType(DataOperateTypeEnum.DELETE.getCode());
            pointInitialMsgConvertDTO.setTenantId(tenantId);
            pointInitialMsgConvertDTO.setSchoolId(schoolId);
            pointInitialMsgConvertDTO.setCampusId(campusId);
            BigDecimal oldScore = beforeInitialScorePOListScoreMap.get(ModuleEnum.OTHER.getCode());

            pointInitialMsgConvertDTO.setScoreType(oldScore.compareTo(BigDecimal.ZERO) >= 0 ? 1 : 2);
            pointInitialMsgConvertDTO.setScore(oldScore);
            pointInitialMsgConvertDTO.setScoreValue(oldScore.abs());
            pointInitialMsgConvertDTO.setCreateBy(staffId);
            pointInitialMsgConvertDTO.setCreateTime(submitDate);

            // 发送积分金币转换mq(新)
            BehaviourPointExchangeDTO behaviourPointExchangeDTO = new BehaviourPointExchangeDTO();
            behaviourPointExchangeDTO.setPointInitialMsgConvertDTO(pointInitialMsgConvertDTO);
            behaviourPointExchangeDTO.setBusinessSource(CoinExchangeStrategyEnum.MANUAL_ALLOCATION_INIT.getCode());
            behaviourPointExchangeDTO.setOperateType(CoinExchangeOperateTypeEnum.DELETE.getCode());
            convertInitialService.sendBehaviourExchangeMq(CollUtil.newArrayList(behaviourPointExchangeDTO));

            convertInitialService.initialScoreSendMQ(3, 3, submitDate, pointInitialMsgConvertDTO);
        }
    }

    /**
     * 保存初始分日志记录详情
     *
     * @param initScoreSaveRequest
     * @param tenantId
     * @param schoolId
     * @param campusId
     * @param staffId
     * @param submitDate
     * @param submitBatchId
     * @param earlyTerm
     */
    private Boolean saveInitialScoreLog(Map<Integer, BigDecimal> beforeInitialScorePOListScoreMap,
                                        InitScoreSaveRequest initScoreSaveRequest,
                                        String tenantId,
                                        String schoolId,
                                        String campusId,
                                        String staffId,
                                        Date submitDate,
                                        Long submitBatchId,
                                        TermVo earlyTerm) {

        //操作记录始终是添加，所以每次都是新增List
        List<EvaluateInitialScoreLogPO> newEvaluateInitialScoreLogPOS = new ArrayList<>();


        for (InitialScoreInnerDTO initialScoreInnerDTO : initScoreSaveRequest.getInitialScoreList()) {

            EvaluateInitialScoreLogPO evaluateInitialScoreLogPO = new EvaluateInitialScoreLogPO();

            evaluateInitialScoreLogPO.setBeforeAllocationScore(beforeInitialScorePOListScoreMap
                    .get(initialScoreInnerDTO.getInitialScoreType()));


            evaluateInitialScoreLogPO.setAllocationScore(initialScoreInnerDTO.getInitialScore());
            evaluateInitialScoreLogPO.setScoreValueAbs(initialScoreInnerDTO.getInitialScore().abs());
            evaluateInitialScoreLogPO.setScoreType(initialScoreInnerDTO.getInitialScore().compareTo(BigDecimal.ZERO) >= 0 ? 1 : 2);
            evaluateInitialScoreLogPO.setAllocationTime(submitDate);
            evaluateInitialScoreLogPO.setInitialScoreType(initialScoreInnerDTO.getInitialScoreType());

            evaluateInitialScoreLogPO.setTenantId(tenantId);
            evaluateInitialScoreLogPO.setSchoolId(schoolId);
            evaluateInitialScoreLogPO.setCampusId(campusId);
            evaluateInitialScoreLogPO.setAllocationId(submitBatchId);
            evaluateInitialScoreLogPO.setSchoolYear(earlyTerm.getSchoolYear());
            evaluateInitialScoreLogPO.setTermName(earlyTerm.getTermName());
            evaluateInitialScoreLogPO.setCreateBy(staffId);
            evaluateInitialScoreLogPO.setCreateTime(submitDate);

            /**
             * 如果修改前初始分整体为空，说明是该校区初始化，则全部需要保存操作记录，
             * 如果修改前初始分不是全部整体为空，则说明是修改，需判断前后分值不相等的初始分类型，才需要保存
             */
            if (CollUtil.isEmpty(beforeInitialScorePOListScoreMap) ||
                    (evaluateInitialScoreLogPO.getBeforeAllocationScore() != null &&
                            evaluateInitialScoreLogPO.getBeforeAllocationScore()
                                    .compareTo(evaluateInitialScoreLogPO.getAllocationScore()) != 0)) {
                newEvaluateInitialScoreLogPOS.add(evaluateInitialScoreLogPO);
            }

        }


        if (!CollectionUtils.isEmpty(newEvaluateInitialScoreLogPOS)) {
            return evaluateInitialScoreLogManager.saveBatch(newEvaluateInitialScoreLogPOS);
        }

        return Boolean.TRUE;
    }


    /**
     * 验证入参 总分是否与其他五育分总和相等，如果没有分配分值，则全部默认归零
     *
     * @param initScoreSaveRequest
     * @return
     */
    private String validateParamOrInitScore(InitScoreSaveRequest initScoreSaveRequest) {
        List<InitialScoreInnerDTO> initialScoreInnerRequestList = initScoreSaveRequest.getInitialScoreList();


        BigDecimal totalScore = BigDecimal.ZERO;
        BigDecimal fiveScore = BigDecimal.ZERO;


        if (initScoreSaveRequest.getApportionFlag() == 0) {
            if (initScoreSaveRequest.getSplitFlag() == 1 || initScoreSaveRequest.getExchangeFlag() == 1) {
                log.warn("初始分设置了不分配总开关，但是子开关是开启状态，默认将子开关关闭：【{}】", initScoreSaveRequest);
                initScoreSaveRequest.setSplitFlag(0);
                initScoreSaveRequest.setExchangeFlag(0);
            }
        }

        //如果没有初始分值，则默认归零
        if (CollectionUtils.isEmpty(initialScoreInnerRequestList)) {

            if (initScoreSaveRequest.getApportionFlag() == 1) {
                log.warn("初始分设置了分配，但是没有分值：【{}】", initScoreSaveRequest);
                return "初始分设置了分配，但是没有分值，请稍后重试";
            }

            initialScoreInnerRequestList = new ArrayList<>();
            initialScoreInnerRequestList.add(buildInitScoreRequest(BigDecimal.ZERO, ModuleEnum.OTHER.getCode()));
            initialFiveScoreZero(initialScoreInnerRequestList);
            initScoreSaveRequest.setInitialScoreList(initialScoreInnerRequestList);

        } else {

            for (InitialScoreInnerDTO initialScoreInnerDTO : initialScoreInnerRequestList) {
                if (initialScoreInnerDTO.getInitialScoreType() == ModuleEnum.OTHER.getCode()) {
                    totalScore = initialScoreInnerDTO.getInitialScore();
                } else {
                    fiveScore = initialScoreInnerDTO.getInitialScore().add(fiveScore);
                }
            }

            if (initScoreSaveRequest.getSplitFlag() == 1 && totalScore.compareTo(fiveScore) != 0) {
                log.warn("五育总分与其他分总和不相等，入参：【{}】", initScoreSaveRequest);
                return "五育总分与其他分总和不相等，请稍后重试";
            }

            if (initScoreSaveRequest.getApportionFlag() == 0) {
                log.warn("初始分设置了不分配，但仍有分值：【{}】", initScoreSaveRequest);
                return "传参错误，初始分设置了不分配，但仍有分值";
            }

            //如果没有分配五育，则五育归零
            if (initScoreSaveRequest.getSplitFlag() == 0) {
                log.warn("初始分设置了不分配五育，所以要给五育归零操作：【{}】", initScoreSaveRequest);
                initScoreSaveRequest.setInitialScoreList(initialScoreZero(initialScoreInnerRequestList));
            }

        }

        return null;
    }


    /**
     * 初始化五育的初始分，归零操作
     */
    private List<InitialScoreInnerDTO> initialScoreZero(List<InitialScoreInnerDTO> initialScoreList) {

        List<InitialScoreInnerDTO> initialZeroScoreList = new ArrayList<>();

        for (InitialScoreInnerDTO initialScoreInnerDTO : initialScoreList) {
            if (initialScoreInnerDTO.getInitialScoreType() == ModuleEnum.OTHER.getCode()) {
                initialZeroScoreList.add(
                        buildInitScoreRequest(initialScoreInnerDTO.getInitialScore(), ModuleEnum.OTHER.getCode()));
            }
        }

        initialFiveScoreZero(initialZeroScoreList);
        return initialZeroScoreList;
    }


    /**
     * 初始化归零五育操作
     *
     * @param initialScoreList
     */
    private void initialFiveScoreZero(List<InitialScoreInnerDTO> initialScoreList) {
        initialScoreList.add(buildInitScoreRequest(BigDecimal.ZERO, ModuleEnum.MORAL.getCode()));
        initialScoreList.add(buildInitScoreRequest(BigDecimal.ZERO, ModuleEnum.WISDOM.getCode()));
        initialScoreList.add(buildInitScoreRequest(BigDecimal.ZERO, ModuleEnum.SPORT.getCode()));
        initialScoreList.add(buildInitScoreRequest(BigDecimal.ZERO, ModuleEnum.PRETTY.getCode()));
        initialScoreList.add(buildInitScoreRequest(BigDecimal.ZERO, ModuleEnum.WORK.getCode()));
    }

    /**
     * 设置初始分分值和类型
     *
     * @param score
     * @param type
     * @return
     */
    private InitialScoreInnerDTO buildInitScoreRequest(BigDecimal score, int type) {
        InitialScoreInnerDTO request = new InitialScoreInnerDTO();
        request.setInitialScore(score);
        request.setInitialScoreType(type);
        return request;
    }

    /**
     * 保存初始分基础信息
     *
     * @param initScoreSaveRequest
     * @param existOldInitialScorePOList
     * @param submitDate
     * @param earlyTerm
     * @param staffId
     * @param tenantId
     * @param schoolId
     * @param campusId
     */
    private Boolean saveInitialScorePO(InitScoreSaveRequest initScoreSaveRequest,
                                       Map<Integer, BigDecimal> requestScoreMap,
                                       List<InitialScorePO> existOldInitialScorePOList,
                                       Date submitDate,
                                       TermVo earlyTerm,
                                       Long submitBatchId,
                                       String staffId,
                                       String tenantId,
                                       String schoolId,
                                       String campusId) {

        List<InitialScorePO> newInitialScorePOList = new ArrayList<>();

        // 如果存在旧的初始分，则更新，否则新增
        if (!CollectionUtils.isEmpty(existOldInitialScorePOList)) {


            for (InitialScorePO existOldInitialScorePO : existOldInitialScorePOList) {

                InitialScorePO newInitialScorePO = new InitialScorePO();
                BeanUtils.copyProperties(existOldInitialScorePO, newInitialScorePO);

                newInitialScorePO.setInitialScore(requestScoreMap.get(existOldInitialScorePO.getInitialScoreType()));
                newInitialScorePO.setAllocationTime(submitDate);
                newInitialScorePO.setApportionFlag(initScoreSaveRequest.getApportionFlag());
                newInitialScorePO.setExchangeFlag(initScoreSaveRequest.getExchangeFlag());
                newInitialScorePO.setSplitFlag(initScoreSaveRequest.getSplitFlag());
                newInitialScorePO.setSchoolYear(earlyTerm.getSchoolYear());
                newInitialScorePO.setTermName(earlyTerm.getTermName());
                newInitialScorePO.setAllocationId(submitBatchId);
                newInitialScorePO.setUpdateBy(staffId);
                newInitialScorePO.setUpdateTime(submitDate);
                // 如果存在旧的初始分，则更新
                newInitialScorePOList.add(newInitialScorePO);
            }


            // 处理历史数据，存在部分类型初始分，但是不存在全部类型初始分，需要补充
            List<Integer> existInitialScoreType = existOldInitialScorePOList
                    .stream()
                    .map(InitialScorePO::getInitialScoreType)
                    .collect(Collectors.toList());

            for (Integer key : requestScoreMap.keySet()) {
                if (!existInitialScoreType.contains(key)) {
                    buildNewInitialScorePO(tenantId,
                            schoolId,
                            campusId,
                            staffId,
                            submitDate,
                            earlyTerm,
                            requestScoreMap.get(key),
                            key,
                            initScoreSaveRequest,
                            submitBatchId,
                            newInitialScorePOList);
                }
            }

        } else {

            // 如果不存在旧的初始分，则新增
            List<InitialScoreInnerDTO> initialScoreList = initScoreSaveRequest.getInitialScoreList();
            for (InitialScoreInnerDTO initialScoreInnerDTO : initialScoreList) {

                buildNewInitialScorePO(tenantId,
                        schoolId,
                        campusId,
                        staffId,
                        submitDate,
                        earlyTerm,
                        initialScoreInnerDTO.getInitialScore(),
                        initialScoreInnerDTO.getInitialScoreType(),
                        initScoreSaveRequest,
                        submitBatchId,
                        newInitialScorePOList);
            }
        }


        return initialScoreManager.saveOrUpdateBatch(newInitialScorePOList);
    }

    private void buildNewInitialScorePO(String tenantId,
                                        String schoolId,
                                        String campusId,
                                        String staffId,
                                        Date submitDate,
                                        TermVo earlyTerm,
                                        BigDecimal initialScore,
                                        Integer initialScoreType,
                                        InitScoreSaveRequest initScoreSaveRequest,
                                        Long submitBatchId,
                                        List<InitialScorePO> newInitialScorePOList) {

        InitialScorePO newInitialScorePO = new InitialScorePO();
        newInitialScorePO.setTenantId(tenantId);
        newInitialScorePO.setSchoolId(schoolId);
        newInitialScorePO.setCampusId(campusId);
        newInitialScorePO.setCreateBy(staffId);
        newInitialScorePO.setCreateTime(submitDate);
        newInitialScorePO.setAllocationTime(submitDate);
        newInitialScorePO.setSchoolYear(earlyTerm.getSchoolYear());
        newInitialScorePO.setTermName(earlyTerm.getTermName());
        newInitialScorePO.setInitialScore(initialScore);
        newInitialScorePO.setInitialScoreType(initialScoreType);
        newInitialScorePO.setApportionFlag(initScoreSaveRequest.getApportionFlag());
        newInitialScorePO.setExchangeFlag(initScoreSaveRequest.getExchangeFlag());
        newInitialScorePO.setSplitFlag(initScoreSaveRequest.getSplitFlag());
        newInitialScorePO.setAllocationId(submitBatchId);
        newInitialScorePOList.add(newInitialScorePO);
    }

    /**
     * 根据 schoolId 和 campusId 获取当前学期时间
     *
     * @param schoolId
     * @param campusId
     * @return
     */
    private TermVo getTermVo(String schoolId, String campusId) {
        // 获取学期
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(Convert.toLong(schoolId));
        termQuery.setCampusId(Convert.toLong(campusId));
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        // 获取多个学段下最早的的学期开始时间
        TermVo earlyTerm = termVos
                .stream()
                .filter(s -> Boolean.TRUE.equals(s.getIsCurrentTerm()))
                .min(Comparator.comparing(TermVo::getStartTime))
                .orElse(null);

        return earlyTerm;
    }

    /**
     * 判断入参中标识位是否和原始已存在的初始分配置相同
     *
     * @param saveRequest
     * @param beforeInitialScorePOList
     * @return true 有变动 false 无变动
     */
    private boolean isChanged(InitScoreSaveRequest saveRequest, List<InitialScorePO> beforeInitialScorePOList) {

        if (CollectionUtils.isEmpty(beforeInitialScorePOList)) {
            return true;
        }

        // 比较标志位
        if (!isFlagSame(saveRequest, beforeInitialScorePOList.get(0))) {
            return true;
        }

        return false;
    }

    /**
     * 比较分值是否有变动
     *
     * @param newList
     * @param beforeInitialScorePOList
     * @return true 有分值变动 false 无分值变动
     */
    private boolean isInitialScoreChanged(List<InitialScoreInnerDTO> newList, List<InitialScorePO> beforeInitialScorePOList) {

        if (CollectionUtils.isEmpty(beforeInitialScorePOList)) {
            return true;
        }

        Map<Integer, BigDecimal> existingScoresMap = beforeInitialScorePOList.stream()
                .collect(Collectors.toMap(InitialScorePO::getInitialScoreType, InitialScorePO::getInitialScore));

        if (existingScoresMap.size() != newList.size()) {
            return true;
        }

        for (InitialScoreInnerDTO newItem : newList) {
            BigDecimal existingScore = existingScoresMap.get(newItem.getInitialScoreType());
            if (existingScore == null || existingScore.compareTo(newItem.getInitialScore()) != 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 比较标志位是否有变动
     *
     * @param request
     * @param existing
     * @return true 无变动 false 有变动
     */
    private boolean isFlagSame(InitScoreSaveRequest request, InitialScorePO existing) {
        return Objects.equals(request.getApportionFlag(), existing.getApportionFlag()) &&
                Objects.equals(request.getSplitFlag(), existing.getSplitFlag()) &&
                Objects.equals(request.getExchangeFlag(), existing.getExchangeFlag());
    }

}
