package com.hailiang.service;

import java.util.List;

/**
 * 指标用户服务接口
 *
 * @Description: 指标用户服务接口
 * @Author: Jovi
 * @Date: Created in 2024-12-03
 * @Version: 2.0.0
 */
public interface TargetUserService {

    /**
     * 根据教职工ID和指标ID列表过滤出有效的指标ID列表
     * @param staffId
     * @param targetIds
     * @return
     */
    List<Long> filterValidTargetIds(Long staffId, List<Long> targetIds);

    /**
     * 根据教职工ID和指标ID判断该教职工是否有该指标权限
     *
     * @param staffId
     * @param targetId
     * @return
     */
    Boolean isValidByStaffIdAndTargetId(Long staffId, Long targetId);
}
