package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.mapper.OperateLogMapper;
import com.hailiang.model.dto.save.OperateLogSaveDTO;
import com.hailiang.model.entity.OperateLog;
import com.hailiang.service.OperateLogService;
import com.hailiang.util.SnowFlakeIdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Date;
import java.util.List;

/**
 * 业务实现
 *
 * <AUTHOR> 2022-03-21 10:50:57
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OperateLogServiceImpl extends ServiceImpl<OperateLogMapper, OperateLog> implements OperateLogService {

    @Override
    public boolean saveOperateLog(@RequestBody OperateLogSaveDTO logDTO) {
        OperateLog operateLog = new OperateLog();

        BeanUtil.copyProperties(logDTO, operateLog);

        operateLog.setId(SnowFlakeIdUtil.nextId());

        return this.save(operateLog);
    }


    @Override
    public List<OperateLog> listOperateLogSpeed(List<String> staffIds, Integer operateType, Date startTime, Date endTime) {

        return list(new LambdaQueryWrapper<OperateLog>()
                .in(OperateLog::getUserId, staffIds)
                .in(OperateLog::getRequestUrl,"//evaluate-h5/evaluateSpeedInfo/saveSpeed","/evaluate-h5/evaluateSpeedInfo/saveSpeed")
                .eq(OperateLog::getResponseStatus, 200)
                .eq(OperateLog::getOperateType, operateType)
                .between(OperateLog::getCreateTime, startTime, endTime)
        );
    }

    @Override
    public List<OperateLog> listOperateLogPic(List<String> staffIds, Integer operateType, Date startTime, Date endTime) {

        return list(new LambdaQueryWrapper<OperateLog>()
                .in(OperateLog::getUserId, staffIds)
                .in(OperateLog::getRequestUrl,"//evaluate-h5/evaluateInfo/save","/evaluate-h5/evaluateInfo/save")
                .eq(OperateLog::getResponseStatus, 200)
                .eq(OperateLog::getOperateType, operateType)
                .between(OperateLog::getCreateTime, startTime, endTime)
        );
    }
}
