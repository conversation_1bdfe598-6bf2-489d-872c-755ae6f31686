package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.mapper.CheckAuthConfigMapper;
import com.hailiang.mapper.mongo.InfoDao;
import com.hailiang.model.dto.check.CheckAuthConfigDTO;
import com.hailiang.model.entity.CheckAuthConfig;
import com.hailiang.model.request.check.CheckAuthConfigSaveRequest;
import com.hailiang.model.response.check.CheckAuthConfigSaveResponse;
import com.hailiang.service.CheckAuthConfigService;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 业务实现
 *
 * <AUTHOR> 2024-04-07 16:05:23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CheckAuthConfigServiceImpl extends ServiceImpl<CheckAuthConfigMapper, CheckAuthConfig> implements CheckAuthConfigService {

    private final CheckAuthConfigMapper checkAuthConfigMapper;
    /**
     * 保存或更新认证配置信息。
     *
     * @param checkAuthConfigSaveRequest 包含认证配置信息的请求对象。其中，如果ID为空，则视为新增；否则，视为更新。
     * @return 如果保存或更新成功，返回true；否则，返回false。
     */
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 600)
    public boolean saveOrUpdateCheckAuthConfig(CheckAuthConfigSaveRequest checkAuthConfigSaveRequest) {
//        Long id = checkAuthConfigSaveRequest.getId();
        // 查一下当前校区下有没有数据
        CheckAuthConfig checkAuthConfig = this.getOne(new LambdaQueryWrapper<CheckAuthConfig>().eq(CheckAuthConfig::getCampusId, WebUtil.getCampusId()));
        // 新增
        if (ObjectUtil.isEmpty(checkAuthConfig)){
//            Assert.isTrue(ObjectUtil.isEmpty(checkAuthConfig), "当前校区下已有数据，不允许新增");
            checkAuthConfig = new CheckAuthConfig();
            checkAuthConfig.setAuthConfig(JSONUtil.toJsonStr(checkAuthConfigSaveRequest));
            return this.save(checkAuthConfig);
        }
        // 修改
        Assert.isTrue(ObjectUtil.isNotEmpty(checkAuthConfig), "当前校区下没有数据，不允许修改");

        return this.update(new LambdaUpdateWrapper<CheckAuthConfig>()
                .set(CheckAuthConfig::getAuthConfig, JSONUtil.toJsonStr(checkAuthConfigSaveRequest))
                .set(CheckAuthConfig::getUpdateBy, WebUtil.getStaffId())
                .set(CheckAuthConfig::getUpdateTime, DateUtil.now())
                .eq(CheckAuthConfig::getCampusId, WebUtil.getCampusId()));
    }

    @Override
    public CheckAuthConfigSaveResponse detail() {
        String campusId = WebUtil.getCampusId();
        Assert.isTrue(ObjectUtil.isNotEmpty(campusId), "校区ID不能为空");

        CheckAuthConfigSaveResponse checkAuthConfigSaveResponse = new CheckAuthConfigSaveResponse();

        CheckAuthConfig checkAuthConfig = this.getOne(new LambdaQueryWrapper<CheckAuthConfig>().eq(CheckAuthConfig::getCampusId, WebUtil.getCampusId()));
        if (ObjectUtil.isEmpty(checkAuthConfig)){
            // 默认
            checkAuthConfigSaveResponse.setDutyStudentRecallMinuteLimit(10080);
            checkAuthConfigSaveResponse.setDutyStudentRecallSource("7,0,0");
            checkAuthConfigSaveResponse.setDutyStudentRecallFlag(1);
            checkAuthConfigSaveResponse.setDutyTeacherRecallMinuteLimit(10080);
            checkAuthConfigSaveResponse.setDutyTeacherRecallSource("7,0,0");
            checkAuthConfigSaveResponse.setDutyTeacherRecallFlag(1);
            checkAuthConfigSaveResponse.setDutyLeaderAppealMinuteLimit(10080);
            checkAuthConfigSaveResponse.setDutyLeaderAppealSource("7,0,0");
            checkAuthConfigSaveResponse.setDutyLeaderAppealFlag(1);
            log.info("当前校区没有权限配置，准备返回默认设置,默认配置信息{}", JSONUtil.toJsonStr(checkAuthConfigSaveResponse));

            return checkAuthConfigSaveResponse;
        }
        checkAuthConfigSaveResponse = JSONUtil.toBean(checkAuthConfig.getAuthConfig(), CheckAuthConfigSaveResponse.class);
        log.info("当前校区配置信息：{}", JSONUtil.toJsonStr(checkAuthConfigSaveResponse));

        return checkAuthConfigSaveResponse;
    }
}
