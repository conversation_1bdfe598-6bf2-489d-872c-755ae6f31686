package com.hailiang.service;

import com.hailiang.model.dto.request.speed.remote.SpeedGroupDetailRemoteRequest;
import com.hailiang.model.dto.request.speed.remote.SpeedGroupDetailRequest;
import com.hailiang.model.dto.response.speed.SpeedConfigGroupResponse;
import com.hailiang.model.dto.response.speed.SpeedGroupOptionsResponse;
import com.hailiang.model.dto.response.speed.remote.SpeedTargetGroupDetailResponse;

import java.util.List;
import java.util.Map;

/**
 * 极速点评配置后台设置
 *
 * @Description: 极速点评配置后台设置
 * @Author: Jovi
 * @Date: Created in 2024-01-29
 * @Version: 1.6.0
 */
public interface SpeedConfigSetUpShowService {

    /**
     * 根据校区展示极速点评分组
     *
     * @return
     */
    List<SpeedConfigGroupResponse> listGroups(Boolean isTemplate);


    /**
     * 根据分组ID展示当前分组下配置树
     *
     * @param speedGroupId
     * @return
     */
    SpeedGroupOptionsResponse tree(Long speedGroupId,Boolean isTemplate);

    /**
     * 极速点评分组及详情
     */
    List<SpeedTargetGroupDetailResponse> listGroupsAndDetails(SpeedGroupDetailRequest request);

    /**
     * 根据指标id和点评项id查询详情
     * @param request
     * @return
     */
    List<SpeedTargetGroupDetailResponse> listGroupsAndDetailsByTargetId(SpeedGroupDetailRemoteRequest request);

    /**
     * 获取指标-五育映射关系
     *
     * @param finalTargetIds 指标 id
     */
    Map<Long, Integer> buildTargetIdtoModuleMap(List<Long> finalTargetIds);
}
