package com.hailiang.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.billboard.evaluate.DataStatisticsFactory;
import com.hailiang.billboard.evaluate.DataStatisticsHandler;
import com.hailiang.constant.Constant;
import com.hailiang.enums.EvaluateBizTypeEnum;
import com.hailiang.enums.MsgTypeEnum;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.enums.TeacherRoleEnum;
import com.hailiang.exception.BizException;
import com.hailiang.manager.BehaviourHandleManager;
import com.hailiang.manager.StudentDailyStatisticsManager;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.datastatistics.query.DataStatisticsQuery;
import com.hailiang.model.datastatistics.query.TeacherEvaluateDataStatisticsQuery;
import com.hailiang.model.datastatistics.vo.*;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.role.ResRoleQueryDTO;
import com.hailiang.remote.saas.dto.school.SchoolQueryDTO;
import com.hailiang.remote.saas.dto.staff.GradeByIdQuery;
import com.hailiang.remote.saas.dto.staff.StaffSectionOrGradeQueryDTO;
import com.hailiang.remote.saas.dto.staff.TeacherInfoQuery;
import com.hailiang.remote.saas.dto.student.UcStudentClassQuery;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.pojo.role.ResRoleInfoPojo;
import com.hailiang.remote.saas.vo.educational.EduClassInfoLinkVO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.remote.saas.vo.role.ResStaffRoleVO;
import com.hailiang.remote.saas.vo.staff.*;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasClassManager;
import com.hailiang.saas.SaasHistoryStudentCacheManager;
import com.hailiang.saas.SaasStaffManager;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import com.hailiang.saas.model.vo.staff.ClassTeacherInfoVO;
import com.hailiang.service.DataStatisticsService;
import com.hailiang.util.EasyPoiUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataStatisticsServiceImpl implements DataStatisticsService {
    /**
     * 筛选日期为一天
     */
    private final static String ONE_DAY_TITLE = "较{0}日";
    /**
     * 筛选日期为一个区间
     */
    private final static String MANY_DAY_TITLE = "较{0}至{1}日";
//    /**
//     * 校级老师参与率标题
//     */
//    private final static String SCHOOL_TITLE = "全校老师参与率";
//    /**
//     * 年级老师参与率标题
//     */
//    private final static String GRADE_TITLE = "{0}老师参与率";
//    /**
//     * 班级老师参与率标题
//     */
//    private final static String CLASS_TITLE = "{0}老师参与率";
//    /**
//     * 各年级老师参与率
//     */
//    private final static String SCHOOL_CHILDREN_TITLE = "各年级老师参与率";
//    /**
//     * 各班级老师参与率
//     */
//    private final static String GRADE_CHILDREN_TITLE = "各班级老师参与率";
//    /**
//     * 各老师参与点评天数
//     */
//    private final static String CLASS_CHILDREN_TITLE = "各老师参与点评天数";
//    /**
//     * 校级老师参与率标题
//     */
//    private final static String SCHOOL_EVALUATE_TITLE = "全校老师点评总次数";
//    /**
//     * 年级老师参与率标题
//     */
//    private final static String GRADE_EVALUATE_TITLE = "{0}点评总次数";
//    /**
//     * 班级老师参与率标题
//     */
//    private final static String CLASS_EVALUATE_TITLE = "{0}点评总次数";
//    /**
//     * 各年级老师参与率
//     */
//    private final static String SCHOOL_EVALUATE_CHILDREN_TITLE = "各年级老师点评次数";
//    /**
//     * 各班级老师参与率
//     */
//    private final static String GRADE_EVALUATE_CHILDREN_TITLE = "各班级老师点评次数";
//    /**
//     * 各老师参与点评天数
//     */
//    private final static String CLASS_EVALUATE_CHILDREN_TITLE = "各老师点评次数";
//    /**
//     * 全校学生点评覆盖率
//     */
//    private final static String SCHOOL_STUDENT_EVALUATE_TITLE = "全校学生点评覆盖率";
//    /**
//     * 年级学生点评覆盖率
//     */
//    private final static String GRADE_STUDENT_EVALUATE_TITLE = "{0}学生点评覆盖率";
//    /**
//     * 班级学生点评点评覆盖率
//     */
//    private final static String CLASS_STUDENT_EVALUATE_TITLE = "{0}学生点评覆盖率";
//    /**
//     * 各年级学生点评覆盖率
//     */
//    private final static String SCHOOL_STUDENT_EVALUATE_CHILDREN_TITLE = "各年级学生点评覆盖率";
//    /**
//     * 各班级学生点评覆盖率
//     */
//    private final static String GRADE_STUDENT_EVALUATE_CHILDREN_TITLE = "各班级学生点评覆盖率";
//    /**
//     * 各老师学生点评覆盖率
//     */
//    private final static String CLASS_STUDENT_EVALUATE_CHILDREN_TITLE = "各老师学生点评覆盖率";
//    /**
//     * 老师参与率班级看板智能建议
//     */
//    private final static String CLASS_PARTICIPATION_SUGGESTION = "老师参与评价天数少于人均评价{0}天";
//    /**
//     * 老师参与率年级看板智能建议
//     */
//    private final static String GRADE_PARTICIPATION_SUGGESTION = "参与率低于平均参与率{0}，建议提升老师的点评参与率";
//    /**
//     * 各老师点评次数智能建议
//     */
//    private final static String CLASS_EVALUATE_SUGGESTION = "老师点评次数低于平均值{0}次，建议引导老师更多点评";
//    /**
//     * 各班级点评次数智能建议
//     */
//    private final static String GRADE_EVALUATE_SUGGESTION = "点评次数低于平均值{0}次，建议引导老师更多点评";
//    /**
//     * 各老师点评学生覆盖率智能建议
//     */
//    private final static String CLASS_STUDENT_EVALUATE_SUGGESTION = "老师点评学生覆盖率低于平均值{0}，建议引导老师更多点评";
//    /**
//     * 各班级点评学生覆盖率智能建议
//     */
//    private final static String GRADE_STUDENT_EVALUATE_SUGGESTION = "点评学生覆盖率低于平均值{0}，建议引导老师更多点评";
//    /**
//     * 老师点评榜下载名称
//     */
//    private final static String TEACHER_EXCEL_NAME = "{0}至{1}老师点评榜单";
    /**
     * 学生未点评名单下载
     */
    private final static String STUDENT_EXCEL_NAME = "{0}至{1}{2}名学生未被点评";
    /**
     * 全部学生被点评钉钉通知模板
     */
    private final static String NORMAL_TEACHER_ALL_DINGDING_INFORM = "\t" + "老师您好，你上周一共点评了{0}人次，点评了{1}个学生，{2}，本周继续加油！" + "\n" +
            "上周所在班级的全部学生都已被点评到，请继续加油!";
    /**
     * 部分学生未被点评钉钉通知模板
     */
    private final static String NORMAL_TEACHER_DINGDING_INFORM = "\t" + "老师您好，你上周一共点评了{0}人次，点评了{1}个学生，{2}，本周继续加油！" + "\n" +
            "上周未被点评的学生如下，本周记得关注一下以下同学喔: " + "\n" +
            "{3}";
    /**
     * 老师排名模板
     */
    private final static String NORMAL_TEACHER_DINGDING_TEACHER_INFORM = "在{0}{1}个老师中排名{2}";
    private final static String NORMAL_TEACHER_WECHAT_INFORM = "上周共点评{0}次({1}学生),排名第{2}";

    /**
     * 全部学生被点评班主任钉钉通知模板
     */
    private final static String CLASS_TEACHER_ALL_DINGDING_INFORM = "\t" + "老师您好，{0}{1}位老师上周一共点评了{2}人次，点评了{3}个学生，在{4}{5}个班中排名第{6}；" + "\n" +
            "每个老师点评情况如下：{7}；" + "\n" +
            "上周{8}的全部学生都已被点评到，请继续加油";
    /**
     * 部分学生被点评班主任钉钉通知模板
     */
    private final static String CLASS_TEACHER_DINGDING_INFORM = "\t" + "老师您好，{0}{1}位老师上周一共点评了{2}人次，点评了{3}个学生，在{4}{5}个班中排名第{6}；" + "\n" +
            "每个老师点评情况如下：{7}；" + "\n" +
            "上周{8}未被点评的学生如下，本周记得关注一下以下同学喔: " + "\n" +
            "{9}";
    private final static String CLASS_TEACHER_ALL_WECHAT_INFORM = "{0}{1}位老师上周共点评了{2}人次({3}学生),排名第{4}";

    /**
     * 年级组长钉钉通知模板
     */
    private final static String GRADE_TEACHER_DINGDING_INFORM = "\t" + "老师您好，{0}{1}位老师上周一共点评了{2}人次，点评了{3}个学生，在{4}{5}个年级排名第{6}；" + "\n" +
            "每个班级点评情况如下：{7}。";
    private final static String GRADE_TEACHER_WECHAT_INFORM = "{0}{1}位老师上周共点评了{2}人次({3}学生),排名第{4}";

    /**
     * 学生处主任钉钉通知模板
     */
    private final static String SCHOOL_TEACHER_DINGDING_INFORM = "\t" + "老师您好，{0}上周一共点评了{1}人次，点评了{2}个学生；" + "\n" +
            "每个年级点评情况如下：{3}。";
    private final static String SCHOOL_TEACHER_WECHAT_INFORM = "{0}上周一共点评了{1}人次,点评了{2}个学生";

    /**
     * 评价统计通知
     */
    private final static String EVALUATE_STATISTICS_INFORM = "评价统计通知";
    /**
     * xx班评价统计通知
     */
    private final static String CLASS_EVALUATE_STATISTICS_INFORM = "{0}评价统计通知";
    /**
     * xx年级评价统计通知
     */
    private final static String GRADE_EVALUATE_STATISTICS_INFORM = "{0}评价统计通知";
    /**
     * xx学校评价统计通知
     */
    private final static String SCHOOL_EVALUATE_STATISTICS_INFORM = "{0}评价统计通知";

    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private BehaviourHandleManager behaviourHandleManager;

    @Value("${third.ding.messageUrl}")
    private String messageUrl;
    @Resource
    private DataStatisticsFactory dataStatisticsFactory;

    @Resource
    private SaasHistoryStudentCacheManager saasHistoryStudentCacheManager;

    @Resource
    private StudentDailyStatisticsManager studentDailyStatisticsManager;

    @Resource
    private SaasClassManager saasClassManager;

    /**
     * 获取学期时间
     *
     * @return
     */
    @Override
    public TermVo getTime(String campusSectionId) {
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
        termQuery.setCampusId(Convert.toLong(WebUtil.getCampusId()));
        termQuery.setCampusSectionId(campusSectionId);
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        if (CollectionUtil.isEmpty(termVos)) {
            log.info("[从saas获取学期时间] 返回结果空：[{}]", JSONUtil.toJsonStr(termVos));
            return null;
        }
        //有当前学期返回当前学期
        List<TermVo> currentTerm = termVos.stream().filter(s -> s.isCurrentTerm()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(currentTerm)) {
            return currentTerm.get(Constant.ZERO);
        }
        //无当前学期，返回上一个最近的学期 + 当前日期
        List<TermVo> termVoList = termVos.stream().filter(s -> DateUtil.parse(s.getEndTime()).before(DateUtil.date())).collect(Collectors.toList());
        TermVo termVo = termVoList.stream().max(Comparator.comparing(s -> DateUtil.parse(s.getEndTime()))).get();
        termVo.setEndTime(DateUtil.today());
        return termVo;
    }

    /**
     * 获取当前筛选时间上一个相同时间段
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public LastSameTimeVO getLastSameTime(Date startTime, Date endTime, String campusSectionId) {
        TermVo termVo = getTime(campusSectionId);
        //时间筛选只能在学期范围内
//        Assert.isFalse(startTime.before(DateUtil.beginOfDay(DateUtil.parse(termVo.getStartTime()))) || endTime.after(DateUtil.endOfDay(DateUtil.parse(termVo.getEndTime()))), () -> new BizException(BizExceptionEnum.TIME_OUT_OF_RANGE_ERROR.getMessage()));
        LastSameTimeVO lastSameTimeVO = new LastSameTimeVO();
        //筛选开始时间，则没有和上一个相同时间段的对比
        if (termVo == null || DateUtil.isSameDay(startTime, DateUtil.parseDate(termVo.getStartTime()))) {
            lastSameTimeVO.setTitle(Constant.HYPHEN);
            return lastSameTimeVO;
        }
        //单日
        if (DateUtil.isSameDay(startTime, endTime)) {
            DateTime start = DateUtil.beginOfDay(DateUtil.offsetDay(startTime, Constant.NEGATIVE_ONE));
            DateTime end = DateUtil.endOfDay(DateUtil.offsetDay(endTime, Constant.NEGATIVE_ONE));
            lastSameTimeVO.setStartTime(start);
            lastSameTimeVO.setEndTime(end);
            if (DateUtil.year(start) == DateUtil.year(startTime)) {
                lastSameTimeVO.setTitle(MessageFormat.format(ONE_DAY_TITLE, DateUtil.format(DateUtil.offsetDay(startTime, Constant.NEGATIVE_ONE), Constant.MONTH_DAY_FORMAT)));
            } else {
                lastSameTimeVO.setTitle(MessageFormat.format(ONE_DAY_TITLE, DateUtil.format(DateUtil.offsetDay(startTime, Constant.NEGATIVE_ONE), Constant.YEAR_MONTH_DAY_FORMAT)));
            }
            return lastSameTimeVO;
        }
        //筛选日期相差的天数
        long betweenDay = DateUtil.betweenDay(startTime, endTime, true);
        DateTime dateTime = DateUtil.endOfDay(DateUtil.offsetDay(startTime, Constant.NEGATIVE_ONE));
        lastSameTimeVO.setEndTime(dateTime);
        lastSameTimeVO.setStartTime(DateUtil.beginOfDay(DateUtil.offsetDay(dateTime, -Convert.toInt(betweenDay))).before(DateUtil.parse(termVo.getStartTime())) ? DateUtil.parse(termVo.getStartTime()) : DateUtil.beginOfDay(DateUtil.offsetDay(dateTime, -Convert.toInt(betweenDay))));
        //跨年要带上年份
        if (DateUtil.year(DateUtil.beginOfDay(DateUtil.offsetDay(dateTime, -Convert.toInt(betweenDay)))) == DateUtil.year(dateTime) && DateUtil.year(startTime) == DateUtil.year(endTime)) {
            //上一个相同开始时间在开学之前，取开学时间
            lastSameTimeVO.setTitle(MessageFormat.format(MANY_DAY_TITLE, DateUtil.beginOfDay(DateUtil.offsetDay(dateTime, -Convert.toInt(betweenDay))).before(DateUtil.parse(termVo.getStartTime()))
                    ? DateUtil.format(DateUtil.parse(termVo.getStartTime()), Constant.MONTH_DAY_FORMAT)
                    : DateUtil.format(DateUtil.beginOfDay(DateUtil.offsetDay(dateTime, -Convert.toInt(betweenDay))), Constant.MONTH_DAY_FORMAT), DateUtil.format(dateTime, Constant.MONTH_DAY_FORMAT)));
        } else {
            lastSameTimeVO.setTitle(MessageFormat.format(MANY_DAY_TITLE, DateUtil.beginOfDay(DateUtil.offsetDay(dateTime, -Convert.toInt(betweenDay))).before(DateUtil.parse(termVo.getStartTime()))
                    ? DateUtil.format(DateUtil.parse(termVo.getStartTime()), DateUtil.year(DateUtil.parse(termVo.getStartTime())) == DateUtil.year(dateTime) ? Constant.MONTH_DAY_FORMAT : Constant.YEAR_MONTH_DAY_FORMAT)
                    : DateUtil.format(DateUtil.beginOfDay(DateUtil.offsetDay(dateTime, -Convert.toInt(betweenDay))), Constant.YEAR_MONTH_DAY_FORMAT), DateUtil.format(dateTime, DateUtil.year(DateUtil.parse(termVo.getStartTime())) == DateUtil.year(dateTime) ? Constant.MONTH_DAY_FORMAT : Constant.YEAR_MONTH_DAY_FORMAT)));
        }
        return lastSameTimeVO;
    }

//    /**
//     * 老师参与率
//     *
//     * @param dto
//     * @return
//     */
//    @Override
//    public DataStatisticsTeacherParticipationRateVO getTeacherParticipationRate(DataStatisticsQuery dto) {
//        DataStatisticsTeacherParticipationRateVO rateVO = new DataStatisticsTeacherParticipationRateVO();
//        //从saas获取该学校下的教务组织架构树形
//        List<EduOrgTreeVO> eduOrgTreeVOS = listEduOrgTree(dto.getType(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId());
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            rateVO.setTitle(SCHOOL_TITLE);
//            rateVO.setChildrenTitle(SCHOOL_CHILDREN_TITLE);
//        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            rateVO.setTitle(MessageFormat.format(GRADE_TITLE, dto.getName()));
//            rateVO.setChildrenTitle(GRADE_CHILDREN_TITLE);
//        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            rateVO.setTitle(MessageFormat.format(CLASS_TITLE, dto.getName()));
//            rateVO.setChildrenTitle(CLASS_CHILDREN_TITLE);
//        }
//        //该学段配置了点评项的老师
//        List<StaffFullInfoVO> listClassTargetConfigTeacherIds = behaviourHandleManager.listTargetConfigTeacherInfos(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime());
//        List<Long> longs = listClassTargetConfigTeacherIds.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList());
//        //指标正常且配有点评项的老师ids
//        List<String> listTargetConfigTeacherIds = Convert.toList(String.class, longs);
//        if (CollectionUtil.isEmpty(listTargetConfigTeacherIds)) {
//            rateVO.setEmptyFlag(Constant.ONE);
//            return rateVO;
//        }
//        StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
//        staffBatchQueryDTO.setState(Constant.ZERO);
//        staffBatchQueryDTO.setStaffIdList(Convert.toList(Long.class, listTargetConfigTeacherIds));
//        //需点评老师
//        List<StaffBatchVO> staffBatchVOList = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
//        if (CollectionUtil.isEmpty(staffBatchVOList)) {
//            rateVO.setEmptyFlag(Constant.ONE);
//            return rateVO;
//        }
//        List<Long> staffIds = staffBatchVOList.stream().map(StaffBatchVO::getId).collect(Collectors.toList());
//        List<String> teacherIdList = Convert.toList(String.class, staffIds);
//
//        //需点评老师数量
//        int teacherNum = teacherIdList.size();
//        //点评过的老师id集合
//        List<String> teacherIds = behaviourHandleManager.listTeacherIds(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime());
//        //参与点评老师数量
//        int participationTeacherNum = CollectionUtil.intersection(teacherIdList, teacherIds).size();
//        //老师参与率
//        BigDecimal rate = new BigDecimal(participationTeacherNum).divide(new BigDecimal(teacherNum), Constant.THREE, RoundingMode.HALF_UP);
//        rateVO.setTeacherNum(teacherNum);
//        rateVO.setParticipationTeacherNum(participationTeacherNum);
//        rateVO.setRate(rate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
//
//        //获取上一段相同时间
//        LastSameTimeVO lastSameTime = getLastSameTime(dto.getStartTime(), dto.getEndTime(), dto.getCampusSectionId());
//        rateVO.setLastSameTime(lastSameTime.getTitle());
//        if (ObjectUtil.isNull(lastSameTime.getStartTime()) || ObjectUtil.isNull(lastSameTime.getEndTime())) {
//            rateVO.setPercent(Constant.HYPHEN);
//        } else {
//            //需点评老师数量
//            int lastTeacherNum = teacherIdList.size();
//            //点评过的老师id集合
//            List<String> lastTeacherIds = behaviourHandleManager.listTeacherIds(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), lastSameTime.getStartTime(), lastSameTime.getEndTime());
//            //参与点评老师数量
//            int lastParticipationTeacherNum = CollectionUtil.intersection(teacherIdList, lastTeacherIds).size();
//            if (CollectionUtil.isEmpty(listTargetConfigTeacherIds) || Constant.ZERO == lastTeacherNum || Constant.ZERO == lastParticipationTeacherNum) {
//                rateVO.setPercent(Constant.HYPHEN);
//            } else {
//                //老师参与率
//                BigDecimal lastRate = new BigDecimal(lastParticipationTeacherNum).divide(new BigDecimal(lastTeacherNum), Constant.THREE, RoundingMode.HALF_UP);
//                rateVO.setPercent(rate.subtract(lastRate).divide(lastRate, Constant.THREE, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
//            }
//        }
//        ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
//        //时间段内所有的点评数据
////        List<BehaviourRecordDTO> behaviourRecordDTOS = behaviourHandleManager.listBehaviourRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime());
//        List<BehaviourRecordDTO> behaviourRecordDTOList = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime(),Constant.ONE);
//        //老师点评的
////        List<BehaviourRecordDTO> behaviourRecordDTOList = behaviourRecordDTOS.stream().filter(s -> Constant.ONE.equals(s.getAppraisalType())).collect(Collectors.toList());
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            Integer totalDay = 0;
//            //需点评老师
//            if (CollectionUtil.isEmpty(teacherIdList)) {
//                return rateVO;
//            }
//            for (String teacherId : teacherIdList) {
//                NameAndValueVO nameAndValueVO = new NameAndValueVO();
//                //将每个老师的数据按时间分组
//                long count = behaviourRecordDTOList.parallelStream()
//                        .filter(s -> teacherId.equals(s.getAppraisalId()))
//                        .map(BehaviourRecordDTO::getSubmitTime).distinct().count();
//                //每个老师的天数
//                int size = Convert.toInt(count);
//                totalDay += size;
//                nameAndValueVO.setName(staffBatchVOList.stream()
//                        .filter(s -> Convert.toStr(s.getId())
//                                .equals(teacherId)).collect(Collectors.toList()).get(0).getName());
//                nameAndValueVO.setValue(Convert.toStr(size));
//                nameAndValueVOS.add(nameAndValueVO);
//            }
//            //智能建议
//            //平均天数
//            BigDecimal avgDay = BigDecimal.valueOf(totalDay).divide(BigDecimal.valueOf(teacherNum), Constant.ONE, RoundingMode.HALF_UP);
//            rateVO.setAverage(Convert.toStr(avgDay));
//            List<String> teacherNames = nameAndValueVOS.stream().filter(s -> BigDecimal.valueOf(Convert.toLong(s.getValue())).compareTo(avgDay) == Constant.NEGATIVE_ONE).map(NameAndValueVO::getName).collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(teacherNames)) {
//                rateVO.setIntelligentSuggestion(null);
//            } else {
//                rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, teacherNames) + MessageFormat.format(CLASS_PARTICIPATION_SUGGESTION, avgDay));
//            }
//            nameAndValueVOS = (ArrayList<NameAndValueVO>) nameAndValueVOS.stream().sorted(Comparator.comparing(NameAndValueVO::getValue, Comparator.comparingDouble(Double::parseDouble)).reversed()).collect(Collectors.toList());
//
//        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            BigDecimal totalRate = BigDecimal.ZERO;
//            //年级下的班级
//            Map<Long, String> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));
//            if (CollectionUtil.isEmpty(idAndName)) {
//                return rateVO;
//            }
//            Iterator<Map.Entry<Long, String>> iterator = idAndName.entrySet().iterator();
//            while (iterator.hasNext()) {
//                Map.Entry<Long, String> next = iterator.next();
//                //该班级下的点评数据按老师分组
//                List<String> collect = behaviourRecordDTOList.parallelStream().filter(s -> Convert.toStr(next.getKey()).equals(s.getClassId())).map(BehaviourRecordDTO::getAppraisalId).distinct().collect(Collectors.toList());
//                NameAndValueVO nameAndValueVO = new NameAndValueVO();
//                nameAndValueVO.setName(next.getValue());
//                //该班级配置了点评项的老师
//                List<StaffFullInfoVO> staffFullInfoVOS = listClassTargetConfigTeacherIds.stream().filter(s -> s.getClassList().stream().map(ClassFullInfoVO::getClassId).collect(Collectors.toList()).contains(next.getKey())).collect(Collectors.toList());
//                List<String> staffIdList = Convert.toList(String.class, staffFullInfoVOS.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList()));
//                //该班级点评过的老师
//                Collection<String> intersection = CollectionUtil.intersection(collect, staffIdList);
//                Integer teacherCount = CollectionUtil.intersection(teacherIdList, staffIdList).size();
//                //老师参与率
//                BigDecimal teacherRate = Constant.ZERO.equals(teacherCount) ? BigDecimal.ZERO : new BigDecimal(intersection.size()).divide(new BigDecimal(teacherCount), Constant.THREE, RoundingMode.HALF_UP);
//                totalRate = totalRate.add(teacherRate);
//                nameAndValueVO.setValue(Constant.ZERO.equals(teacherCount) ? Constant.ZERO + Constant.PERCENT : teacherRate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
//                nameAndValueVOS.add(nameAndValueVO);
//            }
//            //智能建议
//            //平均参与率
//            BigDecimal avgRate = totalRate.divide(BigDecimal.valueOf(idAndName.keySet().size()), Constant.THREE, RoundingMode.HALF_UP);
//            rateVO.setAverage(Convert.toStr(avgRate));
//            List<String> classNames = nameAndValueVOS.stream().filter(s -> {
//                try {
//                    return new BigDecimal(Convert.toStr(NumberFormat.getPercentInstance().parse(s.getValue()))).compareTo(avgRate) == Constant.NEGATIVE_ONE;
//                } catch (ParseException e) {
//                    log.error("老师参与率转换错误", e);
//                }
//                return false;
//            }).map(NameAndValueVO::getName).collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(classNames)) {
//                rateVO.setIntelligentSuggestion(null);
//            } else {
//                NumberFormat percentInstance = NumberFormat.getPercentInstance();
//                percentInstance.setMaximumIntegerDigits(Constant.TWO);
//                percentInstance.setMaximumFractionDigits(Constant.ONE);
//                rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, classNames) + MessageFormat.format(GRADE_PARTICIPATION_SUGGESTION, percentInstance.format(avgRate)));
//            }
//        } else if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            BigDecimal totalRate = BigDecimal.ZERO;
//            //校区下的年级
//            Map<Long, String> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));
//            if (CollectionUtil.isEmpty(idAndName)) {
//                return rateVO;
//            }
//            Iterator<Map.Entry<Long, String>> iterator = idAndName.entrySet().iterator();
//            while (iterator.hasNext()) {
//                Map.Entry<Long, String> next = iterator.next();
//                //该学段的点评数据按年级分组
////                Map<String, List<BehaviourRecordDTO>> collect = behaviourRecordDTOList.stream().filter(s -> Convert.toStr(next.getKey()).equals(s.getGradeId())).collect(Collectors.groupingBy(BehaviourRecordDTO::getAppraisalId));
//                List<String> collect = behaviourRecordDTOList.parallelStream().filter(s -> Convert.toStr(next.getKey()).equals(s.getGradeId())).map(BehaviourRecordDTO::getAppraisalId).distinct().collect(Collectors.toList());
//                NameAndValueVO nameAndValueVO = new NameAndValueVO();
//                nameAndValueVO.setName(next.getValue());
//                //该年级配置了点评项的老师
//                List<StaffFullInfoVO> staffFullInfoVOS = listClassTargetConfigTeacherIds.stream().filter(s -> s.getGradeList().stream().map(GradeFullInfoVO::getGradeId).collect(Collectors.toList()).contains(next.getKey())).collect(Collectors.toList());
//                List<String> staffIdList = Convert.toList(String.class, staffFullInfoVOS.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList()));
//                //该年级点评过的老师
//                Collection<String> intersection = CollectionUtil.intersection(collect, staffIdList);
//                Integer teacherCount = CollectionUtil.intersection(teacherIdList, staffIdList).size();
//                //老师参与率
//                BigDecimal teacherRate = Constant.ZERO.equals(teacherCount) ? BigDecimal.ZERO : new BigDecimal(intersection.size()).divide(new BigDecimal(teacherCount), Constant.THREE, RoundingMode.HALF_UP);
//                totalRate = totalRate.add(teacherRate);
//                nameAndValueVO.setValue(Constant.ZERO.equals(teacherCount) ? Constant.ZERO + Constant.PERCENT : teacherRate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
//                nameAndValueVOS.add(nameAndValueVO);
//            }
//
//            //智能建议
//            //平均参与率
//            BigDecimal avgRate = totalRate.divide(BigDecimal.valueOf(idAndName.keySet().size()), Constant.THREE, RoundingMode.HALF_UP);
//            rateVO.setAverage(Convert.toStr(avgRate));
//            List<String> gradeNames = nameAndValueVOS.stream().filter(s -> {
//                try {
//                    return new BigDecimal(Convert.toStr(NumberFormat.getPercentInstance().parse(s.getValue()).toString())).compareTo(avgRate) == Constant.NEGATIVE_ONE;
//                } catch (ParseException e) {
//                    log.error("老师参与率转换错误", e);
//                }
//                return false;
//            }).map(NameAndValueVO::getName).collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(gradeNames)) {
//                rateVO.setIntelligentSuggestion(null);
//            } else {
//                NumberFormat percentInstance = NumberFormat.getPercentInstance();
//                percentInstance.setMaximumIntegerDigits(Constant.TWO);
//                percentInstance.setMaximumFractionDigits(Constant.ONE);
//                rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, gradeNames) + MessageFormat.format(GRADE_PARTICIPATION_SUGGESTION, percentInstance.format(avgRate)));
//            }
//
//        }
//        rateVO.setChildrenRate(nameAndValueVOS);
//        if (BeanUtil.isEmpty(rateVO)) {
//            rateVO.setEmptyFlag(Constant.ONE);
//        }
//        return rateVO;
//    }
//
//    /**
//     * 点评总次数
//     *
//     * @param dto
//     * @return
//     */
//    @Override
//    public DataStatisticsEvaluateNumVO getEvaluateNum(DataStatisticsQuery dto) {
//        DataStatisticsEvaluateNumVO evaluateNumVO = new DataStatisticsEvaluateNumVO();
//        //时间段内所有的点评数据
//        List<BehaviourRecordDTO> behaviourRecordDTOList = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime(),Constant.ONE);
//        //老师点评的
//        evaluateNumVO.setTotalNum(behaviourRecordDTOList.size());
//        Map<String, Long> collect = behaviourRecordDTOList.parallelStream().collect(Collectors.groupingBy(o -> o.getSubmitTime() + StrPool.UNDERLINE + o.getAppraisalId(), Collectors.counting()));
//        Integer totalTeacherNum = collect.size();
//
//        //指标正常且配有点评项的老师ids
//        List<String> listTargetConfigTeacherIds = behaviourHandleManager.listTargetConfigTeacherIds(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime());
//        StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
//        staffBatchQueryDTO.setState(Constant.ZERO);
//        staffBatchQueryDTO.setStaffIdList(Convert.toList(Long.class, listTargetConfigTeacherIds));
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            evaluateNumVO.setTitle(SCHOOL_EVALUATE_TITLE);
//            evaluateNumVO.setChildrenTitle(SCHOOL_EVALUATE_CHILDREN_TITLE);
//        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            evaluateNumVO.setTitle(MessageFormat.format(GRADE_EVALUATE_TITLE, dto.getName()));
//            evaluateNumVO.setChildrenTitle(GRADE_EVALUATE_CHILDREN_TITLE);
//        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            evaluateNumVO.setTitle(MessageFormat.format(CLASS_EVALUATE_TITLE, dto.getName()));
//            evaluateNumVO.setChildrenTitle(CLASS_EVALUATE_CHILDREN_TITLE);
//        }
//        //需点评老师
//        List<StaffBatchVO> staffBatchVOList = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
//        if (CollectionUtil.isEmpty(staffBatchVOList)) {
//            evaluateNumVO.setEmptyFlag(Constant.ONE);
//            return evaluateNumVO;
//        }
//        List<Long> staffIds = staffBatchVOList.stream().map(StaffBatchVO::getId).collect(Collectors.toList());
//        //需点评老师
//        List<String> teacherIdList = Convert.toList(String.class, staffIds);
//        //需点评老师数量
//        int teacherNum = teacherIdList.size();
//        if (Constant.ZERO == teacherNum) {
//            evaluateNumVO.setAverageNum(null);
//            evaluateNumVO.setAverageDayNum(null);
//        } else {
//            BigDecimal bigDecimal = new BigDecimal(behaviourRecordDTOList.size()).divide(BigDecimal.valueOf(teacherNum), Constant.ONE, RoundingMode.HALF_UP).stripTrailingZeros();
//            evaluateNumVO.setAverageNum(bigDecimal);
//            BigDecimal bigDecimalNum = new BigDecimal(new BigDecimal(totalTeacherNum).divide(BigDecimal.valueOf(teacherNum), Constant.ONE, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
//            evaluateNumVO.setAverageDayNum(bigDecimalNum);
//        }
//        //获取上一段相同时间
//        LastSameTimeVO lastSameTime = getLastSameTime(dto.getStartTime(), dto.getEndTime(), dto.getCampusSectionId());
//        evaluateNumVO.setLastSameTime(lastSameTime.getTitle());
//        if (ObjectUtil.isNull(lastSameTime.getStartTime()) || ObjectUtil.isNull(lastSameTime.getEndTime())) {
//            evaluateNumVO.setPercent(Constant.HYPHEN);
//        } else {
//            //上一段相同时间段内所有的点评数据
//            List<BehaviourRecordDTO> lastBehaviourRecordDTOList = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), lastSameTime.getStartTime(), lastSameTime.getEndTime(),Constant.ONE);
//            //老师点评的
////            List<BehaviourRecordDTO> lastBehaviourRecordDTOList = lastBehaviourRecordDTOS.stream().filter(s -> Constant.ONE.equals(s.getAppraisalType())).collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(lastBehaviourRecordDTOList)) {
//                evaluateNumVO.setPercent(Constant.HYPHEN);
//            } else {
//                evaluateNumVO.setPercent(BigDecimal.valueOf(behaviourRecordDTOList.size()).subtract(BigDecimal.valueOf(lastBehaviourRecordDTOList.size())).divide(BigDecimal.valueOf(lastBehaviourRecordDTOList.size()), Constant.THREE, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
//            }
//        }
//        ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
//        //从saas获取该学校下的教务组织架构树形
//        List<EduOrgTreeVO> eduOrgTreeVOS = listEduOrgTree(dto.getType(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId());
//        //班级看板
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            Integer totalEvaluateNum = 0;
//            if (CollectionUtil.isEmpty(teacherIdList)) {
//                return evaluateNumVO;
//            }
//            for (String teacherId : teacherIdList) {
//                NameAndValueVO nameAndValueVO = new NameAndValueVO();
//                nameAndValueVO.setName(staffBatchVOList.stream().filter(s -> Convert.toStr(s.getId()).equals(teacherId)).collect(Collectors.toList()).get(0).getName());
//                //该老师评价次数
//                long teacherEvaluateNum = behaviourRecordDTOList.parallelStream().filter(s -> s.getAppraisalId().equals(teacherId)).count();
//                nameAndValueVO.setValue(Convert.toStr(teacherEvaluateNum));
//                nameAndValueVOS.add(nameAndValueVO);
//                totalEvaluateNum += Convert.toInt(teacherEvaluateNum);
//            }
//            //智能建议
//            //平均点评次数
//            BigDecimal avgEvaluateNum = BigDecimal.valueOf(totalEvaluateNum).divide(BigDecimal.valueOf(teacherNum), Constant.ONE, RoundingMode.HALF_UP);
//            evaluateNumVO.setAverage(Convert.toStr(avgEvaluateNum));
//            List<String> teacherNames = nameAndValueVOS.stream().filter(s -> BigDecimal.valueOf(Convert.toLong(s.getValue())).compareTo(avgEvaluateNum) == Constant.NEGATIVE_ONE).map(NameAndValueVO::getName).collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(teacherNames)) {
//                evaluateNumVO.setIntelligentSuggestion(null);
//            } else {
//                evaluateNumVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, teacherNames) + MessageFormat.format(CLASS_EVALUATE_SUGGESTION, avgEvaluateNum));
//            }
//            nameAndValueVOS = (ArrayList<NameAndValueVO>) nameAndValueVOS.stream().sorted(Comparator.comparing(NameAndValueVO::getValue, Comparator.comparingDouble(Double::parseDouble)).reversed()).collect(Collectors.toList());
//        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//
//            Integer totalEvaluateNum = 0;
//            //该年级所有班级
//            Map<Long, String> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));
//            if (CollectionUtil.isEmpty(idAndName)) {
//                return evaluateNumVO;
//            }
//            Iterator<Map.Entry<Long, String>> idAndNameMap = idAndName.entrySet().iterator();
//            while (idAndNameMap.hasNext()) {
//                NameAndValueVO nameAndValueVO = new NameAndValueVO();
//                Map.Entry<Long, String> next = idAndNameMap.next();
//                //该班级下所有点评数据
//                long size = behaviourRecordDTOList.parallelStream().filter(s -> Convert.toStr(next.getKey()).equals(s.getClassId())).count();
//                nameAndValueVO.setName(next.getValue());
//                nameAndValueVO.setValue(Convert.toStr(size));
//                nameAndValueVOS.add(nameAndValueVO);
//                totalEvaluateNum += Convert.toInt(size);
//            }
//            //智能建议
//            //平均点评次数
//            BigDecimal avgEvaluateNum = BigDecimal.valueOf(totalEvaluateNum).divide(BigDecimal.valueOf(idAndName.keySet().size()), Constant.ONE, RoundingMode.HALF_UP);
//            evaluateNumVO.setAverage(Convert.toStr(avgEvaluateNum));
//            List<String> classNames = nameAndValueVOS.stream().filter(s -> BigDecimal.valueOf(Convert.toLong(s.getValue())).compareTo(avgEvaluateNum) == Constant.NEGATIVE_ONE).map(NameAndValueVO::getName).collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(classNames)) {
//                evaluateNumVO.setIntelligentSuggestion(null);
//            } else {
//                evaluateNumVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, classNames) + MessageFormat.format(GRADE_EVALUATE_SUGGESTION, avgEvaluateNum));
//            }
//        } else if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            Integer totalEvaluateNum = 0;
//            // 该学段所有年级
//            Map<Long, String> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));
//            if (CollectionUtil.isEmpty(idAndName)) {
//                return evaluateNumVO;
//            }
//            Iterator<Map.Entry<Long, String>> idAndNameMap = idAndName.entrySet().iterator();
//            while (idAndNameMap.hasNext()) {
//                NameAndValueVO nameAndValueVO = new NameAndValueVO();
//                Map.Entry<Long, String> next = idAndNameMap.next();
//                //该年级下所有点评数据
////                int size = behaviourRecordDTOList.parallelStream().filter(s -> Convert.toStr(next.getKey()).equals(s.getGradeId())).collect(Collectors.toList()).size();
//                long size = behaviourRecordDTOList.parallelStream().filter(s -> Convert.toStr(next.getKey()).equals(s.getGradeId())).count();
//                nameAndValueVO.setName(next.getValue());
//                nameAndValueVO.setValue(Convert.toStr(size));
//                nameAndValueVOS.add(nameAndValueVO);
//                totalEvaluateNum += Convert.toInt(size);
//            }
//            //智能建议
//            //平均点评次数
//            BigDecimal avgEvaluateNum = BigDecimal.valueOf(totalEvaluateNum).divide(BigDecimal.valueOf(idAndName.keySet().size()), Constant.ONE, RoundingMode.HALF_UP);
//            evaluateNumVO.setAverage(Convert.toStr(avgEvaluateNum));
//            List<String> classNames = nameAndValueVOS.stream().filter(s -> BigDecimal.valueOf(Convert.toLong(s.getValue())).compareTo(avgEvaluateNum) == Constant.NEGATIVE_ONE).map(NameAndValueVO::getName).collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(classNames)) {
//                evaluateNumVO.setIntelligentSuggestion(null);
//            } else {
//                evaluateNumVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, classNames) + MessageFormat.format(GRADE_EVALUATE_SUGGESTION, avgEvaluateNum));
//            }
//        }
//        evaluateNumVO.setChildrenTotalNum(nameAndValueVOS);
//        if (BeanUtil.isEmpty(evaluateNumVO)) {
//            evaluateNumVO.setEmptyFlag(Constant.ONE);
//        }
//        return evaluateNumVO;
//    }

    private DataStatisticsHandler getService(TeacherEvaluateDataStatisticsQuery query) {
        // 时间段内所有的点评数据
        if (Objects.equals(Boolean.FALSE, query.getIsCurrentYear())) {
            return dataStatisticsFactory.getService(EvaluateBizTypeEnum.HISTORY);
        } else {
            // 默认走当前学年
            return dataStatisticsFactory.getService(EvaluateBizTypeEnum.CURRENT);
        }
    }

    @Override
    public TeacherEvaluateDataStatisticsVO getTeacherEvaluateStatistics(TeacherEvaluateDataStatisticsQuery query) {
        return this.getService(query).getStatistics(query);
    }

    @Override
    public Page<TeacherVO> pageTeacherEvaluateNew(TeacherEvaluateDataStatisticsQuery query) {
        return this.getService(query).pageTeacherEvaluateNew(query);
    }

    @Override
    public void exportTeacherEvaluateNew(TeacherEvaluateDataStatisticsQuery query, HttpServletResponse response) {
        try {
            this.getService(query).exportTeacherEvaluateNew(query, response);
        } catch (IOException e) {
            log.error("老师点评榜下载失败 query {}", JSON.toJSONString(query), e);
            throw new BizException("老师点评榜下载失败");
        }
    }

    @Override
    public Page<TargetRankVO> getTargetRankNew(TeacherEvaluateDataStatisticsQuery query) {
        return this.getService(query).getTargetRankNew(query);
    }

    @Override
    public void exportTargetRankNew(TeacherEvaluateDataStatisticsQuery query, HttpServletResponse response) {
        this.getService(query).exportTargetRankNew(query, response);
    }

//    /**
//     * 学生点评覆盖率
//     *
//     * @param dto
//     * @return
//     */
//    @SneakyThrows
//    @Override
//    public DataStatisticsStudentEvaluateRateVO getStudentEvaluateRate(DataStatisticsQuery dto) {
//        long start = System.currentTimeMillis();
//        log.info("------学生点评覆盖率开始：{}------,执行线程；{}", start, Thread.currentThread().getName());
//        //从saas获取该学校下的教务组织架构树形
//        List<EduOrgTreeVO> eduOrgTreeVOS = listEduOrgTree(dto.getType(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId());
//        //指标正常且配有点评项的老师ids
//        List<String> listTargetConfigTeacherIds = behaviourHandleManager.listTargetConfigTeacherIds(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime());
//        DataStatisticsStudentEvaluateRateVO rateVO = new DataStatisticsStudentEvaluateRateVO();
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            rateVO.setTitle(SCHOOL_STUDENT_EVALUATE_TITLE);
//            rateVO.setChildrenTitle(SCHOOL_STUDENT_EVALUATE_CHILDREN_TITLE);
//        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            rateVO.setTitle(MessageFormat.format(GRADE_STUDENT_EVALUATE_TITLE, dto.getName()));
//            rateVO.setChildrenTitle(GRADE_STUDENT_EVALUATE_CHILDREN_TITLE);
//        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            rateVO.setTitle(MessageFormat.format(CLASS_STUDENT_EVALUATE_TITLE, dto.getName()));
//            rateVO.setChildrenTitle(CLASS_STUDENT_EVALUATE_CHILDREN_TITLE);
//        }
//        //从saas获取的状态为正常的学生
//        List<UcStudentClassBffVO> ucStudentClassBffVOS = listUcStudentClassBffVOS(dto);
//        List<Long> studentIds = ucStudentClassBffVOS.stream().map(UcStudentClassBffVO::getStudentId).collect(Collectors.toList());
//        List<String> studentIdList = Convert.toList(String.class, studentIds);
////        //学生个数
//        Integer totalStudentNum = studentIdList.size();
//        if (ObjectUtil.isNull(totalStudentNum) || Constant.ZERO.equals(totalStudentNum)) {
//            rateVO.setEmptyFlag(Constant.ONE);
//            return rateVO;
//        }
//        rateVO.setTotalStudentNum(totalStudentNum);
//
//        //查询点评过的学生id集合
//        List<String> studentIdsList = behaviourHandleManager.listStudentIds(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime());
//        List<String> noEvaluateStudents = (List<String>) CollectionUtil.subtract(studentIdList, studentIdsList);
//        rateVO.setNoEvaluateStudentNum(noEvaluateStudents.size());
//        //时间段内所有的点评数据
//        List<BehaviourRecordDTO> behaviourRecordDTOS = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime(),null);
//        BigDecimal averageEvaluateNum = new BigDecimal(behaviourRecordDTOS.size()).divide(BigDecimal.valueOf(totalStudentNum), Constant.ONE, BigDecimal.ROUND_HALF_UP).stripTrailingZeros();
//
//        rateVO.setAverageEvaluateNum(averageEvaluateNum);
//        BigDecimal rate = BigDecimal.valueOf(totalStudentNum - noEvaluateStudents.size()).divide(BigDecimal.valueOf(totalStudentNum), Constant.THREE, RoundingMode.HALF_UP);
//        rateVO.setTotalRate(rate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
//
//        ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
//        //获取上一段相同时间
//        LastSameTimeVO lastSameTime = getLastSameTime(dto.getStartTime(), dto.getEndTime(), dto.getCampusSectionId());
//        rateVO.setLastSameTime(lastSameTime.getTitle());
//        if (ObjectUtil.isNull(lastSameTime.getStartTime()) || ObjectUtil.isNull(lastSameTime.getEndTime())) {
//            rateVO.setPercent(Constant.HYPHEN);
//        } else {
//            //上一个相同时间段点评过的学生id集合
//            List<String> lastStudentIds = behaviourHandleManager.listStudentIds(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), lastSameTime.getStartTime(), lastSameTime.getEndTime());
//            BigDecimal compareRate = CollectionUtil.isEmpty(lastStudentIds) ? BigDecimal.ZERO : BigDecimal.valueOf(studentIdsList.size()).subtract(BigDecimal.valueOf(lastStudentIds.size())).divide(BigDecimal.valueOf(lastStudentIds.size()), Constant.THREE, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(totalStudentNum), Constant.THREE, RoundingMode.HALF_UP);
//            rateVO.setPercent(CollectionUtil.isEmpty(lastStudentIds) ? Constant.HYPHEN : compareRate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
//        }
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            BigDecimal studentTotalRate = BigDecimal.ZERO;
//            StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
//            staffBatchQueryDTO.setState(Constant.ZERO);
//            staffBatchQueryDTO.setStaffIdList(Convert.toList(Long.class, listTargetConfigTeacherIds));
//            //需点评老师
//            List<StaffBatchVO> staffBatchVOList = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
//            if (CollectionUtil.isEmpty(staffBatchVOList)) {
//                return rateVO;
//            }
//            List<Long> staffIds = staffBatchVOList.stream().map(StaffBatchVO::getId).collect(Collectors.toList());
//            //需点评老师
//            List<String> teacherIdList = Convert.toList(String.class, staffIds);
//            if (CollectionUtil.isEmpty(teacherIdList)) {
//                return rateVO;
//            }
//            for (String teacherId : teacherIdList) {
//                NameAndValueVO nameAndValueVO = new NameAndValueVO();
//                //该老师点评过的按学生分组
////                Map<String, List<BehaviourRecordDTO>> listMap = behaviourRecordDTOS.stream().filter(s -> teacherId.equals(s.getAppraisalId())).collect(Collectors.groupingBy(BehaviourRecordDTO::getStudentId));
//                List<String> strings = behaviourRecordDTOS.parallelStream().filter(s -> teacherId.equals(s.getAppraisalId())).map(BehaviourRecordDTO::getStudentId).distinct().collect(Collectors.toList());
//                nameAndValueVO.setName(staffBatchVOList.stream().filter(s -> Convert.toStr(s.getId()).equals(teacherId)).collect(Collectors.toList()).get(0).getName());
////                Set<String> strings = listMap.keySet();
//                Collection<String> intersection = CollectionUtil.intersection(studentIdList, strings);
//                BigDecimal studentRate = BigDecimal.valueOf(intersection.size()).divide(BigDecimal.valueOf(totalStudentNum), Constant.THREE, RoundingMode.HALF_UP);
//                nameAndValueVO.setValue(studentRate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
//                nameAndValueVOS.add(nameAndValueVO);
//                studentTotalRate = studentTotalRate.add(studentRate);
//            }
//            //智能建议
//            //平均点评覆盖率
//            BigDecimal avgRate = studentTotalRate.divide(BigDecimal.valueOf(teacherIdList.size()), Constant.THREE, RoundingMode.HALF_UP);
//            rateVO.setAverage(Convert.toStr(avgRate));
//            List<String> teacherNames = nameAndValueVOS.stream().filter(s -> {
//                try {
//                    return new BigDecimal(Convert.toStr(NumberFormat.getPercentInstance().parse(s.getValue()))).compareTo(avgRate) == Constant.NEGATIVE_ONE;
//                } catch (ParseException e) {
//                    log.error("学生点评覆盖率转换错误", e);
//                }
//                return false;
//            }).map(NameAndValueVO::getName).collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(teacherNames)) {
//                rateVO.setIntelligentSuggestion(null);
//            } else {
//                rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, teacherNames) + MessageFormat.format(CLASS_STUDENT_EVALUATE_SUGGESTION, avgRate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT));
//            }
//            nameAndValueVOS.sort(((o1, o2) -> {
//                Double d1 = Double.parseDouble(o1.getValue().split("%")[0]);
//                Double d2 = Double.parseDouble(o2.getValue().split("%")[0]);
//                //降序
//                return d2.compareTo(d1);
//            }));
//
//        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            BigDecimal totalRate = BigDecimal.ZERO;
//            //该年级下的班级
//            Map<Long, String> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));
//            if (CollectionUtil.isEmpty(idAndName)) {
//                return rateVO;
//            }
//            Iterator<Map.Entry<Long, String>> iterator = idAndName.entrySet().iterator();
//            while (iterator.hasNext()) {
//                NameAndValueVO nameAndValueVO = new NameAndValueVO();
//                Map.Entry<Long, String> next = iterator.next();
//                //该班级点评数据按学生分组
////                Map<String, List<BehaviourRecordDTO>> collect = behaviourRecordDTOS.stream().filter(s -> Convert.toStr(next.getKey()).equals(s.getClassId())).collect(Collectors.groupingBy(BehaviourRecordDTO::getStudentId));
//                List<String> collect = behaviourRecordDTOS.parallelStream().filter(s -> Convert.toStr(next.getKey()).equals(s.getClassId())).map(BehaviourRecordDTO::getStudentId).distinct().collect(Collectors.toList());
//                if (CollectionUtil.isEmpty(collect)) {
//                    nameAndValueVO.setName(next.getValue());
//                    nameAndValueVO.setValue(Constant.ZERO + Constant.PERCENT);
//                    nameAndValueVOS.add(nameAndValueVO);
//                    continue;
//                }
//                List<UcStudentClassBffVO> classBffVOS = ucStudentClassBffVOS.stream().filter(s -> next.getKey().equals(s.getClassId())).collect(Collectors.toList());
//                Integer studentNum = classBffVOS.size();
//                //  Integer studentNum = eduOrgTreeVOS.get(Constant.ZERO).getChildren().stream().filter(s -> next.getKey().equals(s.getId())).collect(Collectors.toList()).get(0).getStudentNum();
//                nameAndValueVO.setName(next.getValue());
//                if (ObjectUtil.isNull(studentNum) || Constant.ZERO.equals(studentNum)) {
//                    nameAndValueVO.setValue(Convert.toStr(BigDecimal.ZERO) + Constant.PERCENT);
//                    nameAndValueVOS.add(nameAndValueVO);
//                    continue;
//                }
//
//                //点评过的学生
//                StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
//                studentByIdQuery.setStudentIds(Convert.toList(Long.class, collect));
//                List<UcStudentClassBffVO> ucStudentClassBffVOS1 = basicInfoRemote.listByStudentIds(studentByIdQuery);
//                List<UcStudentClassBffVO> studentClassBffVOS = ucStudentClassBffVOS1.stream().filter(s -> next.getKey().equals(s.getClassId()) && Constant.ZERO.equals(Convert.toInt(s.getStudentStatus())) && Constant.ZERO.equals(Convert.toInt(s.getUpgradeStatus())) && Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
//                //该班级学生点评覆盖率
//                BigDecimal studentRate = new BigDecimal(studentClassBffVOS.size()).divide(new BigDecimal(studentNum), Constant.THREE, RoundingMode.HALF_UP);
//                totalRate = totalRate.add(studentRate);
//                nameAndValueVO.setValue(studentRate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
//                nameAndValueVOS.add(nameAndValueVO);
//            }
//            //智能建议
//            //平均学生点评覆盖率
//            BigDecimal avgRate = totalRate.divide(BigDecimal.valueOf(idAndName.keySet().size()), Constant.THREE, RoundingMode.HALF_UP);
//            rateVO.setAverage(Convert.toStr(avgRate));
//            List<String> classNames = nameAndValueVOS.stream().filter(s -> {
//                try {
//                    return new BigDecimal(Convert.toStr(NumberFormat.getPercentInstance().parse(s.getValue()))).compareTo(avgRate) == Constant.NEGATIVE_ONE;
//                } catch (ParseException e) {
//                    log.error("老师点评学生覆盖率转换错误", e);
//                }
//                return false;
//            }).map(NameAndValueVO::getName).collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(classNames)) {
//                rateVO.setIntelligentSuggestion(null);
//            } else {
//                NumberFormat percentInstance = NumberFormat.getPercentInstance();
//                percentInstance.setMaximumIntegerDigits(Constant.TWO);
//                percentInstance.setMaximumFractionDigits(Constant.ONE);
//                rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, classNames) + MessageFormat.format(GRADE_STUDENT_EVALUATE_SUGGESTION, percentInstance.format(avgRate)));
//            }
//        } else if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            BigDecimal totalRate = BigDecimal.ZERO;
//            //该学段下的年级
//            Map<Long, String> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName, (k1, k2) -> k1, LinkedHashMap::new));
//            if (CollectionUtil.isEmpty(idAndName)) {
//                return rateVO;
//            }
//            Iterator<Map.Entry<Long, String>> iterator = idAndName.entrySet().iterator();
//            while (iterator.hasNext()) {
//                NameAndValueVO nameAndValueVO = new NameAndValueVO();
//                Map.Entry<Long, String> next = iterator.next();
//                //该年级点评数据按学生分组
////                Map<String, List<BehaviourRecordDTO>> collect = behaviourRecordDTOS.stream().filter(s -> Convert.toStr(next.getKey()).equals(s.getGradeId())).collect(Collectors.groupingBy(BehaviourRecordDTO::getStudentId));
//                List<String> collect = behaviourRecordDTOS.parallelStream().filter(s -> Convert.toStr(next.getKey()).equals(s.getGradeId())).map(BehaviourRecordDTO::getStudentId).distinct().collect(Collectors.toList());
//                if (CollectionUtil.isEmpty(collect)) {
//                    nameAndValueVO.setName(next.getValue());
//                    nameAndValueVO.setValue(Constant.ZERO + Constant.PERCENT);
//                    nameAndValueVOS.add(nameAndValueVO);
//                    continue;
//                }
//                List<UcStudentClassBffVO> classBffVOS = ucStudentClassBffVOS.stream().filter(s -> next.getKey().equals(s.getGradeId())).collect(Collectors.toList());
//                Integer studentNum = classBffVOS.size();
//                //Integer studentNum = eduOrgTreeVOS.get(Constant.ZERO).getChildren().stream().filter(s -> next.getKey().equals(s.getId())).collect(Collectors.toList()).get(0).getStudentNum();
//                nameAndValueVO.setName(next.getValue());
//                if (ObjectUtil.isNull(studentNum) || Constant.ZERO.equals(studentNum)) {
//                    nameAndValueVO.setValue(Convert.toStr(BigDecimal.ZERO) + Constant.PERCENT);
//                    nameAndValueVOS.add(nameAndValueVO);
//                    continue;
//                }
//                //点评过的学生
//                StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
//                studentByIdQuery.setStudentIds(Convert.toList(Long.class, collect));
//                List<UcStudentClassBffVO> ucStudentClassBffVOS1 = basicInfoRemote.listByStudentIds(studentByIdQuery);
//                List<UcStudentClassBffVO> studentClassBffVOS = ucStudentClassBffVOS1.stream().filter(s -> next.getKey().equals(s.getGradeId()) && Constant.ZERO.equals(Convert.toInt(s.getStudentStatus())) && Constant.ZERO.equals(Convert.toInt(s.getUpgradeStatus())) && Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
//
//                //该班级学生点评覆盖率
//                BigDecimal studentRate = new BigDecimal(studentClassBffVOS.size()).divide(new BigDecimal(studentNum), Constant.THREE, RoundingMode.HALF_UP);
//                totalRate = totalRate.add(studentRate);
//                nameAndValueVO.setValue(studentRate.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros().toPlainString() + Constant.PERCENT);
//                nameAndValueVOS.add(nameAndValueVO);
//            }
//            //智能建议
//            //平均学生点评覆盖率
//            BigDecimal avgRate = totalRate.divide(BigDecimal.valueOf(idAndName.keySet().size()), Constant.THREE, RoundingMode.HALF_UP);
//            rateVO.setAverage(Convert.toStr(avgRate));
//            List<String> classNames = nameAndValueVOS.stream().filter(s -> {
//                try {
//                    return new BigDecimal(Convert.toStr(NumberFormat.getPercentInstance().parse(s.getValue()))).compareTo(avgRate) == Constant.NEGATIVE_ONE;
//                } catch (ParseException e) {
//                    log.error("老师点评学生覆盖率转换错误", e);
//                }
//                return false;
//            }).map(NameAndValueVO::getName).collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(classNames)) {
//                rateVO.setIntelligentSuggestion(null);
//            } else {
//                NumberFormat percentInstance = NumberFormat.getPercentInstance();
//                percentInstance.setMaximumIntegerDigits(Constant.TWO);
//                percentInstance.setMaximumFractionDigits(Constant.ONE);
//                rateVO.setIntelligentSuggestion(String.join(Constant.CAESURA_SIGN, classNames) + MessageFormat.format(GRADE_STUDENT_EVALUATE_SUGGESTION, percentInstance.format(avgRate)));
//            }
//        }
//        rateVO.setChildrenRate(nameAndValueVOS);
//        if (BeanUtil.isEmpty(rateVO)) {
//            rateVO.setEmptyFlag(Constant.ONE);
//        }
//        long end = System.currentTimeMillis();
//        log.info("------学生点评覆盖率结束：{}------,总耗时：{},执行线程：{}", end, end-start, Thread.currentThread().getName());
//        return rateVO;
//    }

//    /**
//     * 从saas获取(学段、年级、班级)的状态为正常的学生
//     *
//     * @param dto
//     * @return
//     */
//    private List<UcStudentClassBffVO> listUcStudentClassBffVOS(DataStatisticsQuery dto) {
//        UcStudentClassQuery ucStudentClassQuery = new UcStudentClassQuery();
//        ucStudentClassQuery.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
//        ucStudentClassQuery.setCampusSectionId(Convert.toLong(dto.getCampusSectionId()));
//        ucStudentClassQuery.setGradeId(Convert.toLong(dto.getGradeId()));
//        ucStudentClassQuery.setClassId(Convert.toLong(dto.getClassId()));
//        ucStudentClassQuery.setStudentStatus(Convert.toStr(Constant.ZERO));
//        ucStudentClassQuery.setUpgradeStatus(Convert.toStr(Constant.ZERO));
//        ucStudentClassQuery.setPageSize(5000);
//        List<UcStudentClassBffVO> ucStudentClassBffVOS = basicInfoRemote.queryClassStudentPageByCondition(ucStudentClassQuery).getList();
//        List<UcStudentClassBffVO> ucStudentClassBffVOList = JSONUtil.toList(JSONUtil.toJsonStr(ucStudentClassBffVOS), UcStudentClassBffVO.class);
//        ucStudentClassBffVOList = ucStudentClassBffVOList.stream().filter(s -> Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
//        return ucStudentClassBffVOList;
//    }

    /**
     * 从saas获取班级的状态为正常的学生
     *
     * @param classId
     * @return
     */
    private List<UcStudentClassBffVO> listUcStudentClassBffVO(Long schoolId, Long classId) {
        UcStudentClassQuery ucStudentClassQuery = new UcStudentClassQuery();
        ucStudentClassQuery.setSchoolId(schoolId);
        ucStudentClassQuery.setClassId(classId);
        ucStudentClassQuery.setStudentStatus(Convert.toStr(Constant.ZERO));
        ucStudentClassQuery.setUpgradeStatus(Convert.toStr(Constant.ZERO));
        ucStudentClassQuery.setPageSize(5000);
        List<UcStudentClassBffVO> ucStudentClassBffVOS = basicInfoRemote.queryClassStudentPageByCondition(ucStudentClassQuery).getList();
        List<UcStudentClassBffVO> ucStudentClassBffVOList = JSONUtil.toList(JSONUtil.toJsonStr(ucStudentClassBffVOS), UcStudentClassBffVO.class);
        ucStudentClassBffVOList = ucStudentClassBffVOList.stream().filter(s -> Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
        return ucStudentClassBffVOList;
    }

//    /**
//     * 老师点评榜
//     *
//     * @param dto
//     * @return
//     */
//    @Override
//    public Page<TeacherVO> pageTeacherEvaluate(DataStatisticsQuery dto) {
//
//        //时间段内所有的点评数据
//        List<BehaviourRecordDTO> behaviourRecordDTOList = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime(),Constant.ONE);
//        //老师点评的
////        List<BehaviourRecordDTO> behaviourRecordDTOList = behaviourRecordDTOS.stream().filter(s -> Constant.ONE.equals(s.getAppraisalType())).collect(Collectors.toList());
//        //获取老师点评榜
//        ArrayList<TeacherVO> teacherVOS = listTeacherVOS(dto, behaviourRecordDTOList);
//        if (Constant.ONE.equals(dto.getSort())) {
//            teacherVOS = (ArrayList<TeacherVO>) teacherVOS.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum)).collect(Collectors.toList());
//        } else {
//            teacherVOS = (ArrayList<TeacherVO>) teacherVOS.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum).reversed()).collect(Collectors.toList());
//        }
//        //分页
//        List<TeacherVO> teacherVOList = teacherVOS.stream().skip((long) (dto.getPageNum() - 1) * dto.getPageSize()).limit(dto.getPageSize()).collect(Collectors.toList());
//        Page<TeacherVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
//        page.setRecords(teacherVOList);
//        page.setTotal(teacherVOS.size());
//        return page;
//    }
//
////    @Override
//    public void exportTeacherEvaluateOld(DataStatisticsQuery dto, HttpServletResponse response) throws IOException {
//        //时间段内所有的点评数据
//        List<BehaviourRecordDTO> behaviourRecordDTOList = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime(),Constant.ONE);
//        if (CollectionUtil.isEmpty(behaviourRecordDTOList)) {
//            return;
//        }
//        ArrayList<TeacherVO> teacherVOS = listTeacherVOS(dto, behaviourRecordDTOList);
//        if (Constant.ONE.equals(dto.getSort())) {
//            teacherVOS = (ArrayList<TeacherVO>) teacherVOS.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum)).collect(Collectors.toList());
//        } else {
//            teacherVOS = (ArrayList<TeacherVO>) teacherVOS.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum).reversed()).collect(Collectors.toList());
//        }
//        ArrayList<ExcelExportEntity> entitys = new ArrayList<>();
//        entitys.add(new ExcelExportEntity("老师姓名", "teacherName", 18));
//        entitys.add(new ExcelExportEntity("点评次数", "evaluateNum", 18));
//        entitys.add(new ExcelExportEntity("点评天数", "dayNum", 18));
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            entitys.add(new ExcelExportEntity("所在年级", "gradeOrClass", 18));
//        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            entitys.add(new ExcelExportEntity("所在班级", "gradeOrClass", 18));
//        }
//
//        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), entitys, teacherVOS);
//
//        EasyPoiUtil.adjustStyle(workbook.getSheetAt(0));
//
//        String startTime = DateUtil.format(dto.getStartTime(), Constant.CHINESE_FORMAT);
//        String endTime = DateUtil.format(dto.getEndTime(), Constant.CHINESE_FORMAT);
//
//        EasyPoiUtil.downLoadExcelWithFormat(MessageFormat.format(TEACHER_EXCEL_NAME, startTime, endTime), response, workbook);
//    }
//    @Override
//    public void exportTeacherEvaluate(DataStatisticsQuery dto, HttpServletResponse response) throws IOException {
//        log.info("[老师点评榜下载]-开始，请求参数：{}", JSONUtil.toJsonStr(dto));
//        TimeInterval TIME_INTERVAL = DateUtil.timer();
//        TIME_INTERVAL.restart();
//
//        List<Map<String,Object>> schoolCourseList = new ArrayList<>();
//        TeacherEvaluateExcel teacherEvaluateExcel = null;
//        //时间段内所有的点评数据
//        List<BehaviourRecordDTO> behaviourRecordDTOList = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime(),Constant.ONE);
//        log.info("[老师点评榜下载]-查询点评数据，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
//        TIME_INTERVAL.restart();
//        if (CollectionUtil.isEmpty(behaviourRecordDTOList)) {
//            throw new BizException("暂无数据，请重新筛选");
////            return;
//        }
//        // sheet-数据准备
//
//        // sheet2-数据准备
//        ArrayList<TeacherVO> teacherVOS = listTeacherVOS(dto, behaviourRecordDTOList);
//        if (Constant.ONE.equals(dto.getSort())) {
//            teacherVOS = (ArrayList<TeacherVO>) teacherVOS.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum)).collect(Collectors.toList());
//        } else {
//            teacherVOS = (ArrayList<TeacherVO>) teacherVOS.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum).reversed()).collect(Collectors.toList());
//        }
//        log.info("[老师点评榜下载]-sheet2数据准备，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
//        TIME_INTERVAL.restart();
//        // 第二个sheet参数定义
//        Map<String,Object> sheet2Map = new HashMap<>();
//        ExportParams sheet2Params = new ExportParams();
//        sheet2Params.setSheetName("老师个人点评排行榜");
//        sheet2Map.put("title",sheet2Params);
//        sheet2Map.put("entity", TeacherVO.class);
//        sheet2Map.put("data", CollUtil.newArrayList(teacherVOS));
//
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())
//                || SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())){
//            log.info("[老师点评榜下载]-学段/年级维度查询：准备增加新的sheet：年级和班级点评排行榜");
////            List<TeacherEvaluateSheet1> teacherEvaluateSheet1s = dataStatisticsLogic.listTeacherClassData(dto, behaviourRecordDTOList);
//            teacherEvaluateExcel = dataStatisticsLogic.listTeacherClassData(dto, behaviourRecordDTOList);
//            // 第一个sheet参数定义
//            Map<String,Object> sheet1Map = new HashMap<>();
//            ExportParams sheet1Params = new ExportParams();
//            sheet1Params.setSheetName("年级和班级点评排行榜");
//            sheet1Map.put("title", sheet1Params);
//            sheet1Map.put("entity", TeacherEvaluateSheet1.class);
//            sheet1Map.put("data", CollUtil.isEmpty(teacherEvaluateExcel.getTeacherEvaluateSheet1List()) ? Collections.EMPTY_LIST : teacherEvaluateExcel.getTeacherEvaluateSheet1List());
//            schoolCourseList.add(sheet1Map);
//        }
//        log.info("[老师点评榜下载]-sheet1数据准备，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
//        TIME_INTERVAL.restart();
//        schoolCourseList.add(sheet2Map);
//
////        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), entitys, teacherVOS);
//        Workbook workbook = ExcelExportUtil.exportExcel(schoolCourseList, ExcelType.XSSF);
//        // 处理sheet1-合并单元格
//        dataStatisticsLogic.mergeCell(workbook, dto.getType(), teacherEvaluateExcel);
//        // 处理sheet2-修改标题名称
//        dataStatisticsLogic.handleTitle(workbook, dto.getType());
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())
//                || SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())){
//            EasyPoiUtil.adjustStyle(workbook.getSheetAt(1));
//        }
//        EasyPoiUtil.adjustStyle(workbook.getSheetAt(0));
//
//        String startTime = DateUtil.format(dto.getStartTime(), Constant.CHINESE_FORMAT);
//        String endTime = DateUtil.format(dto.getEndTime(), Constant.CHINESE_FORMAT);
//        log.info("[老师点评榜下载]-导出并调整excel，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
//        TIME_INTERVAL.restart();
//        EasyPoiUtil.downLoadExcelWithFormat(MessageFormat.format(TEACHER_EXCEL_NAME, startTime, endTime), response, workbook);
////        EasyPoiUtil.saveExcelFile(workbook, "c://home//excel//1.xlsx");
//    }
//    /**
//     * 获取老师点评榜
//     *
//     * @param dto
//     * @param behaviourRecordDTOList
//     * @return
//     */
//    private ArrayList<TeacherVO> listTeacherVOS(DataStatisticsQuery dto, List<BehaviourRecordDTO> behaviourRecordDTOList) {
//        ArrayList<TeacherVO> teacherVOS = new ArrayList<>();
//        //根据老师分组
//        Map<String, List<BehaviourRecordDTO>> teacherBehaviour = behaviourRecordDTOList.stream().collect(Collectors.groupingBy(BehaviourRecordDTO::getAppraisalId));
//        //指标正常且配有点评项的老师ids
//        List<String> listTargetConfigTeacherIds = behaviourHandleManager.listTargetConfigTeacherIds(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime());
//        StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
//        staffBatchQueryDTO.setState(Constant.ZERO);
//        staffBatchQueryDTO.setStaffIdList(Convert.toList(Long.class, listTargetConfigTeacherIds));
//        //需点评老师
//        List<StaffBatchVO> staffBatchVOList = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
//        if (CollectionUtil.isEmpty(staffBatchVOList)) {
//            return teacherVOS;
//        }
//        List<Long> staffIds = staffBatchVOList.stream().map(StaffBatchVO::getId).collect(Collectors.toList());
//        StaffSectionOrGradeQueryDTO queryDTO = new StaffSectionOrGradeQueryDTO();
//        //从saas获取学段或者年级或者班级所有老师信息
//        List<StaffFullInfoVO> staffFullInfoVOS = new ArrayList<>();
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            queryDTO.setCampusSectionId(Convert.toLong(dto.getCampusSectionId()));
//            staffFullInfoVOS = basicInfoRemote.queryByCampusSectionId(queryDTO);
//        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            queryDTO.setGradeId(Convert.toLong(dto.getGradeId()));
//            staffFullInfoVOS = basicInfoRemote.queryByGradeId(queryDTO);
//        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            queryDTO.setClassId(Convert.toLong(dto.getClassId()));
//            staffFullInfoVOS = basicInfoRemote.queryByClassId(queryDTO);
//        }
//        if (CollectionUtil.isEmpty(staffFullInfoVOS)) {
//            return new ArrayList<>();
//        }
//        List<Long> collect = staffFullInfoVOS.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList());
//        staffIds = (List<Long>) CollectionUtil.intersection(collect, staffIds);
//        for (Long staffId : staffIds) {
//            TeacherVO teacherVO = new TeacherVO();
//            teacherVO.setTeacherId(Convert.toStr(staffId));
//            teacherVO.setTeacherName(CollectionUtil.isEmpty(staffFullInfoVOS.stream().filter(s -> s.getStaffId().equals(staffId)).collect(Collectors.toList())) ? null : staffFullInfoVOS.stream().filter(s -> s.getStaffId().equals(staffId)).collect(Collectors.toList()).get(0).getName());
//            teacherVO.setEvaluateNum(CollectionUtil.isEmpty(teacherBehaviour) ? Constant.ZERO : CollectionUtil.isEmpty(teacherBehaviour.get(Convert.toStr(staffId))) ? 0 : teacherBehaviour.get(Convert.toStr(staffId)).size());
//            teacherVO.setDayNum(CollectionUtil.isEmpty(teacherBehaviour) ? Constant.ZERO : CollectionUtil.isEmpty(teacherBehaviour.get(Convert.toStr(staffId))) ? 0 : teacherBehaviour.get(Convert.toStr(staffId)).stream().collect(Collectors.groupingBy(BehaviourRecordDTO::getSubmitTime)).keySet().size());
//            teacherVO.setIconUrl(CollectionUtil.isEmpty(staffFullInfoVOS.stream().filter(s -> s.getStaffId().equals(staffId)).collect(Collectors.toList())) ? null : staffFullInfoVOS.stream().filter(s -> s.getStaffId().equals(staffId)).collect(Collectors.toList()).get(0).getAvatar());
//            if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//                List<GradeFullInfoVO> gradeList = CollectionUtil.isEmpty(staffFullInfoVOS.stream().filter(s -> s.getStaffId().equals(staffId)).collect(Collectors.toList())) ? CollectionUtil.newArrayList() : staffFullInfoVOS.stream().filter(s -> s.getStaffId().equals(staffId)).collect(Collectors.toList()).get(0).getGradeList();
//                teacherVO.setGradeOrClass(String.join(Constant.CAESURA_SIGN, gradeList.stream().map(GradeFullInfoVO::getGradeName).collect(Collectors.toList())));
//            } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//                List<ClassFullInfoVO> classList = CollectionUtil.isEmpty(staffFullInfoVOS.stream().filter(s -> s.getStaffId().equals(staffId)).collect(Collectors.toList())) ? CollectionUtil.newArrayList() : staffFullInfoVOS.stream().filter(s -> s.getStaffId().equals(staffId)).collect(Collectors.toList()).get(0).getClassList();
//                teacherVO.setGradeOrClass(String.join(Constant.CAESURA_SIGN, classList.stream().map(ClassFullInfoVO::getClassName).collect(Collectors.toList())));
//            }
//            teacherVOS.add(teacherVO);
//        }
//        return teacherVOS;
//    }


    /**
     * 年级分组（校级看板未被点评学生名单）
     *
     * @param dto
     * @return
     */
    @Override
    public List<GradeInfoVO> listGrade(DataStatisticsQuery dto) {

        Long schoolId = Convert.toLong(WebUtil.getSchoolId());
        Long campusId = Convert.toLong(WebUtil.getCampusId());
        Long gradeId = Convert.toLong(dto.getGradeId());
        Long classId = Convert.toLong(dto.getClassId());
        Long campusSectionId = Convert.toLong(dto.getCampusSectionId());

        List<SassStudentVO> ucStudentClassBffVOList;
        if (Objects.nonNull(dto.getIsCurrentYear()) && Boolean.FALSE.equals(dto.getIsCurrentYear())) {
            //历史学生
            ucStudentClassBffVOList = saasHistoryStudentCacheManager.listHisStudentByGradeIdAndClassId(schoolId, dto.getSchoolYear(), campusId, gradeId, classId, campusSectionId);
        } else {
            //从saas获取该学段所有学生
            ucStudentClassBffVOList = listTotalSaasStudent(Convert.toLong(dto.getCampusSectionId()), dto.getSchoolYear(), gradeId, classId);
        }

        String gradeIdStr = StringUtils.equals("-1", dto.getGradeId()) ? null : dto.getGradeId();
        String classIdStr = StringUtils.equals("-1", dto.getClassId()) ? null : dto.getClassId();


        //从T+1统计表中查询点评过的学生id集合
        List<String> evaluatedStudentIds = studentDailyStatisticsManager.listStudentId(
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                dto.getCampusSectionId(),
                gradeIdStr,
                classIdStr,
                dto.getStartTime(),
                dto.getEndTime());

        Map<Long, GradeInfoVO> map = new HashMap<>();

        for (SassStudentVO sassStudentVO : ucStudentClassBffVOList) {
            //未被点评过的学生
            if (!evaluatedStudentIds.contains(Convert.toStr(sassStudentVO.getStudentId()))) {
                Long sassStudentVOGradeId = sassStudentVO.getGradeId();
                GradeInfoVO gradeInfoVO = map.get(sassStudentVOGradeId);
                if (gradeInfoVO == null) {

                    GradeInfoVO vo = new GradeInfoVO();
                    vo.setGradeId(Convert.toStr(sassStudentVOGradeId));
                    vo.setGradeName(sassStudentVO.getGradeName());
                    vo.setGradeCode(sassStudentVO.getGradeCode());
                    vo.setStudentNum(1);

                    map.put(sassStudentVOGradeId, vo);
                } else {
                    gradeInfoVO.setStudentNum(gradeInfoVO.getStudentNum() + 1);
                }
            }
        }

        if (CollUtil.isEmpty(map)) {
            return Collections.emptyList();
        } else {
            return map
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(GradeInfoVO::getGradeCode))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 未被点评学生名单
     *
     * @param dto
     * @return
     */
    @Override
    public Page<StudentVO> pageNoEvaluateStudent(DataStatisticsQuery dto) {
        //获取未点评学生信息
        ArrayList<StudentVO> studentVOS = listStudentVOSNew(dto);
        if (studentVOS == null) {
            return null;
        }
        //分页
        List<StudentVO> studentVOList = studentVOS
                .stream()
                .skip((long) (dto.getPageNum() - 1) * dto.getPageSize())
                .limit(dto.getPageSize())
                .collect(Collectors.toList());

        Page<StudentVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        page.setRecords(studentVOList);
        page.setTotal(studentVOS.size());
        return page;
    }

//    /**
//     * 获取未点评学生信息
//     *
//     * @param dto
//     * @return
//     */
//    private ArrayList<StudentVO> listStudentVOS(DataStatisticsQuery dto) {
//        ArrayList<StudentVO> studentVOS = new ArrayList<>();
//        //从saas获取该学段(年级，班级)所有正常状态学生（分页获取total）
//        List<UcStudentClassBffVO> ucStudentClassBffS = listUcStudentClassBffVOS(dto);
//        List<Long> studentIdsLong = ucStudentClassBffS.stream().map(UcStudentClassBffVO::getStudentId).collect(Collectors.toList());
//        List<String> studentIdList = Convert.toList(String.class, studentIdsLong);
//        //查询点评过的学生id集合
//        List<String> studentIds = behaviourHandleManager.listStudentIds(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime());
//        //未点评过的学生
//        List<String> noEvaluateStudents = (List<String>) CollectionUtil.subtract(studentIdList, studentIds);
//        List<Long> noEvaluateStudentList = Convert.toList(Long.class, noEvaluateStudents);
//        List<UcStudentClassBffVO> ucStudentClassBffVOS = ucStudentClassBffS.stream().filter(s -> noEvaluateStudentList.contains(s.getStudentId())).collect(Collectors.toList());
//        //按照班级、学号排序
//        List<UcStudentClassBffVO> ucStudentClassBffVOList = ucStudentClassBffVOS.stream().sorted(Comparator.comparing(UcStudentClassBffVO::getClassId, Comparator.nullsLast(Long::compareTo)).thenComparing(UcStudentClassBffVO::getStudentNo, Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
//        for (UcStudentClassBffVO ucStudentClassBffVO : ucStudentClassBffVOList) {
//            StudentVO studentVO = new StudentVO();
//            studentVO.setStudentId(Convert.toStr(ucStudentClassBffVO.getStudentId()));
//            studentVO.setStudentName(ucStudentClassBffVO.getStudentName());
//            studentVO.setClassName(ucStudentClassBffVO.getClassName());
//            studentVO.setGradeName(ucStudentClassBffVO.getGradeName());
//            studentVOS.add(studentVO);
//        }
//        return studentVOS;
//    }

    /**
     * 获取未点评学生信息(新)
     *
     * @param dto
     * @return
     */
    private ArrayList<StudentVO> listStudentVOSNew(DataStatisticsQuery dto) {
        ArrayList<StudentVO> studentVOS = new ArrayList<>();
        Long campusSectionId = Convert.toLong(dto.getCampusSectionId());
        String schoolYear = dto.getSchoolYear();
        Long campusId = Convert.toLong(WebUtil.getCampusId());
        Long schoolId = Convert.toLong(WebUtil.getSchoolId());

        Long gradeId = StringUtils.isNotBlank(dto.getGradeId()) && "-1".equals(dto.getGradeId()) ? null : Convert.toLong(dto.getGradeId());
        Long classId = StringUtils.isNotBlank(dto.getClassId()) && "-1".equals(dto.getClassId()) ? null : Convert.toLong(dto.getClassId());


        if (Objects.nonNull(dto.getIsCurrentYear()) && Boolean.FALSE.equals(dto.getIsCurrentYear())) {
            //历史学生
            List<SassStudentVO> sassStudentVOS = saasHistoryStudentCacheManager.listHisStudentByGradeIdAndClassId(
                    schoolId,
                    schoolYear,
                    campusId,
                    gradeId,
                    classId,
                    campusSectionId);

            List<Long> studentIdsLong = new ArrayList<>();
            if (CollUtil.isNotEmpty(sassStudentVOS)) {
                studentIdsLong = sassStudentVOS.stream().map(SassStudentVO::getStudentId).collect(Collectors.toList());
            }
            if (Objects.nonNull(gradeId) && -1 != gradeId) {
                sassStudentVOS = sassStudentVOS.stream().filter(s -> s.getGradeId().equals(gradeId)).collect(Collectors.toList());
            }

            List<String> studentIdList = Convert.toList(String.class, studentIdsLong);

            //从T+1统计表中查询点评过的学生id集合
            List<String> evaluatedStudentIds = studentDailyStatisticsManager.listStudentId(
                    WebUtil.getSchoolId(),
                    WebUtil.getCampusId(),
                    dto.getCampusSectionId(),
                    Convert.toStr(gradeId),
                    Convert.toStr(classId),
                    dto.getStartTime(),
                    dto.getEndTime());

            //未点评过的学生
            List<String> noEvaluateStudents = (List<String>) CollectionUtil.subtract(studentIdList, evaluatedStudentIds);

            List<Long> noEvaluateStudentList = Convert.toList(Long.class, noEvaluateStudents);

            List<SassStudentVO> ucStudentClassBffVOS = sassStudentVOS
                    .stream()
                    .filter(s -> noEvaluateStudentList.contains(s.getStudentId()))
                    .collect(Collectors.toList());


            //按照班级、学号排序
            sassStudentVOS = ucStudentClassBffVOS.stream()
                    .sorted(Comparator.comparing(SassStudentVO::getClassId, Comparator.nullsLast(Long::compareTo))
                            .thenComparing(SassStudentVO::getStudentNo, Comparator.nullsLast(String::compareTo)))
                    .collect(Collectors.toList());

            for (SassStudentVO sassStudentVO : sassStudentVOS) {
                StudentVO studentVO = new StudentVO();
                studentVO.setStudentId(Convert.toStr(sassStudentVO.getStudentId()));
                studentVO.setStudentName(sassStudentVO.getStudentName());
                studentVO.setGradeName(sassStudentVO.getGradeName());
                studentVO.setClassName(sassStudentVO.getClassName());
                studentVOS.add(studentVO);
            }
            return studentVOS;
        } else {
            //从saas获取该学段(年级，班级)所有正常状态学生（分页获取total）
            List<SassStudentVO> ucStudentClassBffS = listTotalSaasStudent(campusSectionId, schoolYear, gradeId, classId);
            List<Long> studentIdsLong = new ArrayList<>();
            if (CollUtil.isNotEmpty(ucStudentClassBffS)) {
                studentIdsLong = ucStudentClassBffS.stream().map(SassStudentVO::getStudentId).collect(Collectors.toList());
            }
            if (Objects.nonNull(gradeId) && -1 != gradeId) {
                ucStudentClassBffS = ucStudentClassBffS.stream().filter(s -> s.getGradeId().equals(gradeId)).collect(Collectors.toList());
            }
            List<String> studentIdList = Convert.toList(String.class, studentIdsLong);
            //从T+1统计表中查询点评过的学生id集合
            List<String> evaluatedStudentIds = studentDailyStatisticsManager.listStudentId(
                    WebUtil.getSchoolId(),
                    WebUtil.getCampusId(),
                    dto.getCampusSectionId(),
                    Convert.toStr(gradeId),
                    Convert.toStr(classId),
                    dto.getStartTime(),
                    dto.getEndTime());

            //未点评过的学生
            List<String> noEvaluateStudents = (List<String>) CollectionUtil.subtract(studentIdList, evaluatedStudentIds);
            List<Long> noEvaluateStudentList = Convert.toList(Long.class, noEvaluateStudents);
            List<SassStudentVO> ucStudentClassBffVOS = ucStudentClassBffS.stream().filter(s -> noEvaluateStudentList.contains(s.getStudentId())).collect(Collectors.toList());
            //按照班级、学号排序
            List<SassStudentVO> ucStudentClassBffVOList = ucStudentClassBffVOS.stream().sorted(Comparator.comparing(SassStudentVO::getClassId, Comparator.nullsLast(Long::compareTo)).thenComparing(SassStudentVO::getStudentNo, Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
            for (SassStudentVO ucStudentClassBffVO : ucStudentClassBffVOList) {
                StudentVO studentVO = new StudentVO();
                studentVO.setStudentId(Convert.toStr(ucStudentClassBffVO.getStudentId()));
                studentVO.setStudentName(ucStudentClassBffVO.getStudentName());
                studentVO.setClassName(ucStudentClassBffVO.getClassName());
                studentVO.setGradeName(ucStudentClassBffVO.getGradeName());
                studentVOS.add(studentVO);
            }
            return studentVOS;
        }
    }

    /**
     * 从saas获取(学段、年级、班级)的状态为正常的学生
     *
     * @param campusSectionId
     * @param schoolYear
     * @param gradeId
     * @param classId
     * @return
     */
    private List<SassStudentVO> listTotalSaasStudent(Long campusSectionId, String schoolYear, Long gradeId, Long classId) {

        List<SassStudentVO> sassStudentVOS =
                saasHistoryStudentCacheManager.getCurrentStudentVOS(
                        Convert.toLong(WebUtil.getSchoolId()),
                        WebUtil.getCampusIdLong(),
                        campusSectionId,
                        gradeId,
                        classId,
                        schoolYear, "0", "0");

        if (CollUtil.isNotEmpty(sassStudentVOS)) {
            //筛选行政班
            sassStudentVOS = sassStudentVOS.stream().filter(s -> Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
            //过滤学段
            if (com.hailiang.jxgy.common.utils.StringUtil.isNotEmpty(campusSectionId)) {
                sassStudentVOS = sassStudentVOS.stream().filter(s -> s.getCampusSectionId().equals(campusSectionId)).collect(Collectors.toList());
            }
            //指定班级id
            if (com.hailiang.jxgy.common.utils.StringUtil.isNotEmpty(classId) && -1 != classId) {
                sassStudentVOS = sassStudentVOS.stream().filter(s -> s.getClassId().equals(classId)).collect(Collectors.toList());
            }
        }
        return sassStudentVOS;
    }

    /**
     * 未被点评学生名单下载
     *
     * @param dto
     * @return
     */
    @Override
    public void exportNoEvaluateStudent(DataStatisticsQuery dto, HttpServletResponse response) throws IOException {
        //获取未点评学生信息
        ArrayList<StudentVO> studentVOS = listStudentVOSNew(dto);
        ArrayList<ExcelExportEntity> entitys = new ArrayList<>();
        entitys.add(new ExcelExportEntity("学生姓名", "studentName", 18));
        entitys.add(new ExcelExportEntity("年级", "gradeName", 18));
        entitys.add(new ExcelExportEntity("班级", "className", 18));
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), entitys, studentVOS);
        EasyPoiUtil.adjustStyle(workbook.getSheetAt(0));
        String startTime = DateUtil.format(dto.getStartTime(), Constant.CHINESE_FORMAT);
        String endTime = DateUtil.format(dto.getEndTime(), Constant.CHINESE_FORMAT);
        EasyPoiUtil.downLoadExcelWithFormat(MessageFormat.format(STUDENT_EXCEL_NAME, startTime, endTime, studentVOS.size()), response, workbook);
    }

    /**
     * 获取教职工的权限范围（3：校级，4：年级，5：班级）
     *
     * @param
     * @return
     */
    @Override
    public Integer getRoleType(String staffId) {
        ResRoleQueryDTO resRoleQueryDTO = new ResRoleQueryDTO();
        resRoleQueryDTO.setStaffIds(Collections.singletonList(Convert.toLong(staffId)));
        resRoleQueryDTO.setId(Convert.toLong(WebUtil.getSchoolId()));
        resRoleQueryDTO.setType(Constant.TWO);
        List<ResStaffRoleVO> resStaffRoleVOS = basicInfoRemote.queryStaffRoleList(resRoleQueryDTO);
        //该教职工所有角色code
        List<String> roleList = resStaffRoleVOS.get(0).getRoles().stream().map(ResRoleInfoPojo::getRoleCode).collect(Collectors.toList());
        Integer type;
        if (roleList.contains(Constant.HEADMASTER_ROLE_CODE) || roleList.contains(Constant.OPERATION_ROLE_CODE) || roleList.contains(Constant.MANAGER_ROLE_CODE)) {
            type = Constant.THREE;
        } else if (roleList.contains(Constant.GRADE_ROLE_CODE)) {
            type = Constant.FOUR;
        } else {
            type = Constant.FIVE;
        }
        return type;
    }

    /**
     * 根据校区获取需发生通知老师的信息
     *
     * @param campusId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public ArrayList<StaffFullInfoVO> listTeacherInfo(String schoolId, String campusId, String tenantId, Date startTime, Date endTime) {
        if (StrUtil.isBlank(campusId) || StrUtil.isBlank(schoolId)) {
            return null;
        }
        ArrayList<StaffFullInfoVO> staffFullInfoVOArrayList = new ArrayList<>();
        //手动开启定时任务时间
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            startTime = DateUtil.beginOfWeek(DateUtil.lastWeek());
            endTime = DateUtil.endOfDay(DateUtil.endOfWeek(startTime));
        } else {
            startTime = DateUtil.beginOfDay(startTime);
            endTime = DateUtil.endOfDay(endTime);
        }
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(campusId));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.CAMPUS.getCode());
        List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        if (CollectionUtil.isEmpty(eduOrgTreeVOS)) {
            return staffFullInfoVOArrayList;
        }
        //该校区下所有学段
        Map<Long, String> idAndName = eduOrgTreeVOS.get(0).getChildren().stream().collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getName));
        Iterator<Map.Entry<Long, String>> iterator = idAndName.entrySet().iterator();
        while (iterator.hasNext()) {
            ArrayList<StaffFullInfoVO> fullInfoVOArrayList = new ArrayList<>();
            Map.Entry<Long, String> next = iterator.next();
            //获取学期
            TermQuery termQuery = new TermQuery();
            termQuery.setSchoolId(Convert.toLong(schoolId));
            termQuery.setCampusId(Convert.toLong(campusId));
            termQuery.setCampusSectionId(Convert.toStr(next.getKey()));
            List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
            List<TermVo> termVoList = termVos.stream().filter(s -> s.isCurrentTerm()).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(termVos) || CollectionUtil.isEmpty(termVoList)) {
                continue;
            }
            if (startTime.before(DateUtil.parse(termVoList.get(0).getStartTime())) || endTime.after(DateUtil.parse(termVoList.get(0).getEndTime()))) {
                continue;
            }
            //该学段指标正常且配有点评项的老师ids
            List<String> listTargetConfigTeacherIds = behaviourHandleManager.listTargetConfigTeacherIds(schoolId, campusId, Convert.toStr(next.getKey()), null, null, startTime, endTime);
            List<String> collect = listTargetConfigTeacherIds.stream().distinct().collect(Collectors.toList());
            StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
            staffBatchQueryDTO.setState(Constant.ZERO);
            staffBatchQueryDTO.setStaffIdList(Convert.toList(Long.class, collect));
            //需点评老师
            List<StaffBatchVO> staffBatchVOList = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
            if (CollectionUtil.isEmpty(staffBatchVOList)) {
                continue;
            }
            List<Long> staffIds = staffBatchVOList.stream().map(StaffBatchVO::getId).collect(Collectors.toList());
            //从saas获取该学段老师信息
            StaffSectionOrGradeQueryDTO staffSectionOrGradeQueryDTO = new StaffSectionOrGradeQueryDTO();
            staffSectionOrGradeQueryDTO.setCampusSectionId(next.getKey());
            List<StaffFullInfoVO> staffFullInfoVOS = basicInfoRemote.queryByCampusSectionId(staffSectionOrGradeQueryDTO);
            if (CollectionUtil.isEmpty(staffFullInfoVOS)) {
                continue;
            }
            //筛选出老师能关联到班级和年级
            List<StaffFullInfoVO> staffFullInfoVOList = staffFullInfoVOS.stream().filter(s -> CollectionUtil.isNotEmpty(s.getClassList()) && CollectionUtil.isNotEmpty(s.getGradeList())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(staffFullInfoVOList)) {
                continue;
            }
            /**----------------------------------------------------------------点评项老师-----------------------------------------------------------------------------------*/
            //需发送通知的老师
            List<StaffFullInfoVO> fullInfoVOList = staffFullInfoVOList.stream().filter(s -> staffIds.contains(s.getStaffId())).collect(Collectors.toList());
            // fullInfoVOList = fullInfoVOList.stream().distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(fullInfoVOList)) {
                continue;
            }
            for (StaffFullInfoVO staffFullInfoVO : fullInfoVOList) {
//                staffFullInfoVO.setUniqueId(IdUtil.getSnowflakeNextIdStr());
                staffFullInfoVO.setTeacherRoleType(TeacherRoleEnum.NORMAL_TEACHER.getCode());
                fullInfoVOArrayList.add(staffFullInfoVO);
            }


            /**----------------------------------------------------------------班主任-----------------------------------------------------------------------------------*/
            //按班级分组
            List<List<ClassFullInfoVO>> classList = fullInfoVOList.stream().map(StaffFullInfoVO::getClassList).collect(Collectors.toList());
            //班级去重
            List<ClassFullInfoVO> classFullInfoVOS = classList.stream().flatMap(List::stream).distinct().collect(Collectors.toList());
            //班主任和其所在班级
            for (ClassFullInfoVO classFullInfoVO : classFullInfoVOS) {
                //根据班级ID查询班主任信息 saas
                TeacherInfoQuery teacherInfoQuery = new TeacherInfoQuery();
                teacherInfoQuery.setClassId(classFullInfoVO.getClassId());
                List<ClassStaffVO> classStaffVOS = basicInfoRemote.queryTeachersByClassId(teacherInfoQuery);
                //班主任staffId
                List<Long> classTeacherId = classStaffVOS.stream().filter(s -> s.getTeacherType().contains(Constant.ZERO.toString())).map(ClassStaffVO::getStaffId).distinct().collect(Collectors.toList());
                if (CollectionUtil.isEmpty(classTeacherId)) {
                    continue;
                }
                //需发送通知的班主任
                List<StaffFullInfoVO> classTeacherInfoVOList = staffFullInfoVOS.stream().filter(s -> classTeacherId.get(0).equals(s.getStaffId())).collect(Collectors.toList());
                StaffFullInfoVO staffFullInfoVO1 = new StaffFullInfoVO();
                BeanUtil.copyProperties(classTeacherInfoVOList.get(0), staffFullInfoVO1);
//                staffFullInfoVO1.setUniqueId(IdUtil.getSnowflakeNextIdStr());
                staffFullInfoVO1.setTeacherRoleType(TeacherRoleEnum.CLASS_TEACHER.getCode());
                staffFullInfoVO1.setClassOrGrade(classFullInfoVO.getClassId());
                fullInfoVOArrayList.add(staffFullInfoVO1);

            }


            /**----------------------------------------------------------------年级组长-----------------------------------------------------------------------------------*/
            //按年级分组
            List<List<GradeFullInfoVO>> gradeList = fullInfoVOList.stream().map(StaffFullInfoVO::getGradeList).collect(Collectors.toList());
            //年级去重
            List<GradeFullInfoVO> gradeFullInfoVOS = gradeList.stream().flatMap(List::stream).distinct().collect(Collectors.toList());
            for (GradeFullInfoVO gradeTeacherId : gradeFullInfoVOS) {
                //根据年级ID查询年级组长 saas
                GradeByIdQuery gradeByIdQuery = new GradeByIdQuery();
                gradeByIdQuery.setGradeId(gradeTeacherId.getGradeId());
                List<GradeStaffVO> gradeStaffVOS = basicInfoRemote.queryStaffByGradeId(gradeByIdQuery);
                List<Long> gradeTeacherIdList = gradeStaffVOS.stream().map(GradeStaffVO::getStaffId).distinct().collect(Collectors.toList());
                if (CollectionUtil.isEmpty(gradeTeacherIdList)) {
                    continue;
                }
                //需发送通知的年级组长
                List<StaffFullInfoVO> gradeTeacherInfoVOList = staffFullInfoVOS.stream().filter(s -> gradeTeacherIdList.get(0).equals(s.getStaffId())).collect(Collectors.toList());

                StaffFullInfoVO staffFullInfoVO1 = new StaffFullInfoVO();
                BeanUtil.copyProperties(gradeTeacherInfoVOList.get(0), staffFullInfoVO1);
//                staffFullInfoVO1.setUniqueId(IdUtil.getSnowflakeNextIdStr());
                staffFullInfoVO1.setTeacherRoleType(TeacherRoleEnum.GRADE_TEACHER.getCode());
                staffFullInfoVO1.setClassOrGrade(gradeTeacherId.getGradeId());
                fullInfoVOArrayList.add(staffFullInfoVO1);

            }


            /**----------------------------------------------------------------学生处主任-----------------------------------------------------------------------------------*/
            //根据学校id获取学生处主任
            SchoolQueryDTO dto = new SchoolQueryDTO();
            dto.setSchoolId(Convert.toLong(schoolId));
            dto.setRoleCodes(Collections.singletonList(Constant.MANAGER_ROLE_CODE));
            List<UcStaffInfoRoleSchoolVO> ucStaffInfoRoleSchoolVOS = basicInfoRemote.queryStaffListBySchoolId(dto);
            List<Long> schoolTeacherIdList = ucStaffInfoRoleSchoolVOS.stream().map(UcStaffInfoRoleSchoolVO::getStaffId).distinct().collect(Collectors.toList());
//            //需发送通知的学生处主任
//            List<StaffFullInfoVO> schoolTeacherInfoVOList = staffFullInfoVOS.stream().filter(s -> schoolTeacherIdList.contains(s.getStaffId())).collect(Collectors.toList());
//            for (StaffFullInfoVO staffFullInfoVO : schoolTeacherInfoVOList) {
//                StaffFullInfoVO staffFullInfoVO1 = staffFullInfoVO;
//                staffFullInfoVO1.setTeacherRoleType(TeacherRoleEnum.SCHOOL_TEACHER.getCode());
//                fullInfoVOArrayList.add(staffFullInfoVO1);
//            }

            for (UcStaffInfoRoleSchoolVO ucStaffInfoRoleSchoolVO : ucStaffInfoRoleSchoolVOS) {
                StaffFullInfoVO staffFullInfoVO1 = new StaffFullInfoVO();
                staffFullInfoVO1.setStaffId(ucStaffInfoRoleSchoolVO.getStaffId());
                staffFullInfoVO1.setName(ucStaffInfoRoleSchoolVO.getStaffName());
                staffFullInfoVO1.setMobile(ucStaffInfoRoleSchoolVO.getMobile());
                staffFullInfoVO1.setUserId(ucStaffInfoRoleSchoolVO.getUserId());
                staffFullInfoVO1.setSchoolId(Convert.toStr(ucStaffInfoRoleSchoolVO.getSchoolId()));
                staffFullInfoVO1.setTeacherRoleType(TeacherRoleEnum.SCHOOL_TEACHER.getCode());
                fullInfoVOArrayList.add(staffFullInfoVO1);
            }


            for (StaffFullInfoVO staffFullInfoVO : fullInfoVOArrayList) {
                StaffFullInfoVO staffFullInfoVO1 = new StaffFullInfoVO();
                BeanUtil.copyProperties(staffFullInfoVO, staffFullInfoVO1);
                staffFullInfoVO1.setCampusSectionId(Convert.toStr(next.getKey()));
                staffFullInfoVO1.setCampusId(campusId);
                staffFullInfoVO1.setSchoolId(schoolId);
                staffFullInfoVOArrayList.add(staffFullInfoVO1);
            }
        }
        Date finalStartTime = startTime;
        Date finalEndTime = endTime;
        if (CollectionUtil.isNotEmpty(staffFullInfoVOArrayList)) {
            staffFullInfoVOArrayList.stream().forEach(s -> {
                s.setStartTime(finalStartTime);
                s.setEndTime(finalEndTime);
            });
        }
        List<StaffFullInfoVO> collect = staffFullInfoVOArrayList.stream().filter(s -> s.getStaffId().equals(7054383501544947712L)).collect(Collectors.toList());
        List<StaffFullInfoVO> collect1 = staffFullInfoVOArrayList.stream().filter(s -> s.getMobile().equals("17826836024")).collect(Collectors.toList());
        List<StaffFullInfoVO> collect2 = staffFullInfoVOArrayList.stream().filter(s -> s.getTeacherRoleType().equals(4)).collect(Collectors.toList());
        return staffFullInfoVOArrayList;
    }

    /**
     * 根据老师获取老师需要发送的通知内容
     *
     * @param staffFullInfoVOS
     * @param startTime
     * @param endTime
     * @param msgType
     * @return
     */
    @Override
    public List<StaffFullInfoVO> listTeacherInform(String schoolId, String campusId, String tenantId, ArrayList<StaffFullInfoVO> staffFullInfoVOS, Date startTime, Date endTime, Integer msgType) {
        if (CollectionUtil.isEmpty(staffFullInfoVOS)) {
            return null;
        }
        //手动开启定时任务时间
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            startTime = DateUtil.beginOfWeek(DateUtil.lastWeek());
            endTime = DateUtil.endOfDay(DateUtil.endOfWeek(startTime));
        } else {
            startTime = DateUtil.beginOfDay(startTime);
            endTime = DateUtil.endOfDay(endTime);
        }
        //时间段内该校区所有的点评数据
        List<BehaviourRecordDTO> behaviourRecordDTOList = behaviourHandleManager.listBehaviourRecordNew(schoolId, campusId, null, null, null, startTime, endTime, 1);
        //获取所有年级各个老师的点评次数和人数
        ArrayList<TeacherVO> teacherVOS = listTeachers(schoolId, campusId, staffFullInfoVOS, startTime, endTime, behaviourRecordDTOList);
//        if (CollectionUtil.isEmpty(teacherVOS)) {
//            log.info("[根据老师获取老师需要发送的通知内容],没有老师");
//            return null;
//        }
        for (StaffFullInfoVO staffFullInfoVO : staffFullInfoVOS) {
            if (TeacherRoleEnum.NORMAL_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {
                List<TeacherVO> teacherVOList = teacherVOS.stream().filter(s -> s.getTeacherId().equals(Convert.toStr(staffFullInfoVO.getStaffId()))).collect(Collectors.toList());
                //点评次数
                int evaluateNum = CollectionUtil.isEmpty(teacherVOList) ? 0 : teacherVOList.stream().mapToInt(TeacherVO::getEvaluateNum).sum();
                //点评人数
                int evaluatePeopleNum = CollectionUtil.isEmpty(teacherVOList) ? 0 : teacherVOList.get(0).getDayNum();
                int teacherNum;
                ArrayList<String> teacherRanks = new ArrayList<>();
                String firstTeacherRanks = "";
                //该老师拥有的年级
                Map<Long, String> gradeIds = staffFullInfoVO.getGradeList().stream().collect(Collectors.toMap(GradeFullInfoVO::getGradeId, GradeFullInfoVO::getGradeName));
                //排名
                Iterator<Map.Entry<Long, String>> iterator = gradeIds.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Long, String> next = iterator.next();
                    //该年级的所有老师
                    List<TeacherVO> gradeTeachers = teacherVOS.stream().filter(s -> s.getGradeOrClass().equals(Convert.toStr(next.getKey()))).collect(Collectors.toList());
                    teacherNum = gradeTeachers.size();
                    gradeTeachers = gradeTeachers.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum).reversed().thenComparing(TeacherVO::getTeacherId)).collect(Collectors.toList());
                    int indexOf = CollectionUtil.indexOf(gradeTeachers, s -> s.getTeacherId().equals(Convert.toStr(staffFullInfoVO.getStaffId())));
                    Integer num = CollectionUtil.isEmpty(gradeTeachers) || Constant.NEGATIVE_ONE.equals(indexOf) ? 0 : gradeTeachers.get(indexOf).getEvaluateNum();
                    int index = Constant.ZERO.equals(num) ? teacherNum : indexOf + Constant.ONE;
                    String teacherRank = MessageFormat.format(NORMAL_TEACHER_DINGDING_TEACHER_INFORM, next.getValue(), teacherNum, index);
                    teacherRanks.add(teacherRank);
                    //微信显示的排名用第一个
                    firstTeacherRanks = firstTeacherRanks.isEmpty() ? Integer.toString(index) : firstTeacherRanks;
                }
                ArrayList<String> studentNameList = new ArrayList<>();
                //该老师所有的班级
                Map<Long, String> classIds = staffFullInfoVO.getClassList().stream().collect(Collectors.toMap(ClassFullInfoVO::getClassId, ClassFullInfoVO::getClassName));
                //各班级未被点评学生
                Iterator<Map.Entry<Long, String>> entryIterator = classIds.entrySet().iterator();
                while (entryIterator.hasNext()) {
                    Map.Entry<Long, String> next = entryIterator.next();
                    //从saas获取该班级所有正常状态学生
                    List<UcStudentClassBffVO> ucStudentClassBffS = listUcStudentClassBffVO(Convert.toLong(schoolId), next.getKey());
                    List<Long> studentLongId = ucStudentClassBffS.stream().map(UcStudentClassBffVO::getStudentId).collect(Collectors.toList());
                    List<String> studentIdList = Convert.toList(String.class, studentLongId);
                    //该班级查询点评过的学生id集合
                    List<String> studentIds = behaviourHandleManager.listStudentIds(schoolId, campusId, null, null, Convert.toStr(next.getKey()), startTime, endTime);
                    //未点评过的学生
                    List<String> noEvaluateStudents = (List<String>) CollectionUtil.subtract(studentIdList, studentIds);
                    if (CollectionUtil.isNotEmpty(noEvaluateStudents)) {
                        List<Long> noEvaluateStudentLong = Convert.toList(Long.class, noEvaluateStudents);
                        //未被点评过的学生信息
                        List<UcStudentClassBffVO> ucStudentClassBffVOS = ucStudentClassBffS.stream().filter(s -> noEvaluateStudentLong.contains(s.getStudentId())).collect(Collectors.toList());
                        List<String> studentNames = ucStudentClassBffVOS.stream().map(UcStudentClassBffVO::getStudentName).collect(Collectors.toList());
                        if (Constant.FIVE < studentNames.size()) {
                            studentNames = studentNames.subList(Constant.ZERO, Constant.FIVE);
                            String join = String.join(Constant.CAESURA_SIGN, studentNames);
                            studentNameList.add(next.getValue() + ": " + join + "..." + "\n");
                        } else {
                            studentNameList.add(next.getValue() + ": " + String.join(Constant.CAESURA_SIGN, studentNames) + "\n");
                        }
                    }
                }
                if (MsgTypeEnum.WECHAT.getCode().equals(msgType)) {
                    staffFullInfoVO.setInform(MessageFormat.format(NORMAL_TEACHER_WECHAT_INFORM, evaluateNum, evaluatePeopleNum, firstTeacherRanks));
                } else if (CollectionUtil.isEmpty(studentNameList)) {
                    staffFullInfoVO.setInform(MessageFormat.format(NORMAL_TEACHER_ALL_DINGDING_INFORM, evaluateNum, evaluatePeopleNum, String.join(Constant.CHINESE_COMMA, teacherRanks)));
                } else {
                    staffFullInfoVO.setInform(MessageFormat.format(NORMAL_TEACHER_DINGDING_INFORM, evaluateNum, evaluatePeopleNum, String.join(Constant.CHINESE_COMMA, teacherRanks), String.join("", studentNameList)));
                }
                staffFullInfoVO.setTitle(EVALUATE_STATISTICS_INFORM);
                staffFullInfoVO.setMessageUrl(messageUrl + "/home?current=2" +
                        "&tenantId=" + tenantId +
                        "&schoolId=" + schoolId +
                        "&campusId=" + campusId +
                        "&campusSectionId=" + staffFullInfoVO.getCampusSectionId() +
                        "&gradeId=" + staffFullInfoVO.getClassList().get(0).getGradeId() +
                        "&classId=" + staffFullInfoVO.getClassList().get(0).getClassId() +
                        "&staffId=" + staffFullInfoVO.getStaffId() +
                        "&startTime=" + DateUtil.format(startTime, Constant.PATTERN_FORMAT) +
                        "&endTime=" + DateUtil.format(endTime, Constant.PATTERN_FORMAT) +
                        "&fromMsg=1"
                );
                continue;

            } else if (TeacherRoleEnum.CLASS_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {
                //班主任老师所在的班级
                Long classId = staffFullInfoVO.getClassOrGrade();
                StaffSectionOrGradeQueryDTO queryDTO = new StaffSectionOrGradeQueryDTO();
                queryDTO.setClassId(classId);
                //该班级所有老师的信息
                List<StaffFullInfoVO> staffFullInfoVOList = basicInfoRemote.queryByClassId(queryDTO);
                if (CollectionUtil.isEmpty(staffFullInfoVOList)) {
                    log.info("[{}]班级下没有老师", classId);
                    continue;
                }
                Integer totalNum = 0;
                Integer totalPeopleNum = 0;
                //各老师点评次数
                ArrayList<String> teacherEvaluateNum = new ArrayList<>();
                //被点名的学生
                ArrayList<String> studentIdList = new ArrayList<>();
                for (StaffFullInfoVO fullInfoVO : staffFullInfoVOList) {
                    //点评次数
                    int evaluateNum = behaviourRecordDTOList.stream().filter(s -> Convert.toStr(fullInfoVO.getStaffId()).equals(s.getAppraisalId()) && Convert.toStr(classId).equals(s.getClassId())).collect(Collectors.toList()).size();
                    //点评人数
                    List<BehaviourRecordDTO> recordDTOS = behaviourRecordDTOList.stream().filter(s -> Convert.toStr(fullInfoVO.getStaffId()).equals(s.getAppraisalId()) && Convert.toStr(classId).equals(s.getClassId())).collect(Collectors.toList());
                    List<String> studentIds = recordDTOS.stream().map(BehaviourRecordDTO::getStudentId).distinct().collect(Collectors.toList());
                    totalNum += evaluateNum;
                    teacherEvaluateNum.add(fullInfoVO.getName() + evaluateNum + "次");
                    studentIdList.addAll(studentIds);
                }
                totalPeopleNum = studentIdList.stream().distinct().collect(Collectors.toList()).size();
                String studentNameList = null;
                //从saas获取该班级所有正常状态学生
                List<UcStudentClassBffVO> ucStudentClassBffS = listUcStudentClassBffVO(Convert.toLong(schoolId), classId);
                List<Long> studentLongId = ucStudentClassBffS.stream().map(UcStudentClassBffVO::getStudentId).collect(Collectors.toList());
                List<String> studentIdsList = Convert.toList(String.class, studentLongId);
                //该班级查询点评过的学生id集合
                List<String> studentIds = behaviourHandleManager.listStudentIds(schoolId, campusId, null, null, Convert.toStr(classId), startTime, endTime);
                //未点评过的学生
                List<String> noEvaluateStudents = (List<String>) CollectionUtil.subtract(studentIdsList, studentIds);
                if (CollectionUtil.isNotEmpty(noEvaluateStudents)) {
                    List<Long> noEvaluateStudentLong = Convert.toList(Long.class, noEvaluateStudents);
                    //未被点评过的学生信息
                    List<UcStudentClassBffVO> ucStudentClassBffVOS = ucStudentClassBffS.stream().filter(s -> noEvaluateStudentLong.contains(s.getStudentId())).collect(Collectors.toList());
                    List<String> studentNames = ucStudentClassBffVOS.stream().map(UcStudentClassBffVO::getStudentName).collect(Collectors.toList());
                    if (Constant.FIVE < studentNames.size()) {
                        studentNames = studentNames.subList(Constant.ZERO, Constant.FIVE);
                        String join = String.join(Constant.CAESURA_SIGN, studentNames);
                        studentNameList = join + "...";
                    } else {
                        studentNameList = String.join(Constant.CAESURA_SIGN, studentNames);
                    }
                }

                EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
                eduOrgQueryDTO.setCurrentId(classId);
                eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.CLASS.getCode());
                //从saas获取数据
                List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
                if (CollectionUtil.isEmpty(eduOrgTreeVOS)) {
                    staffFullInfoVO.setSendFlag(Constant.ONE);
                    continue;
                }
                //年级id
                Long parentId = eduOrgTreeVOS.get(Constant.ZERO).getParentId();
                eduOrgQueryDTO.setCurrentId(parentId);
                eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.GRADE.getCode());
                //从saas获取该年级所有班级
                List<EduOrgTreeVO> eduOrgTreeVOList = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
                if (CollectionUtil.isEmpty(eduOrgTreeVOList)) {
                    log.info("没有该年级，年级 id：[{}]", parentId);
                    continue;
                }
                List<EduOrgTreeVO> children = eduOrgTreeVOList.get(0).getChildren();
                if (CollectionUtil.isEmpty(eduOrgTreeVOList)) {
                    log.info("该年级下没有班级，年级 id：[{}]", parentId);
                    continue;
                }
                //各班级排名
                ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
                for (EduOrgTreeVO eduOrgTreeVO : children) {
                    queryDTO.setClassId(eduOrgTreeVO.getId());
                    //该班级所有老师的信息
                    List<StaffFullInfoVO> staffFullInfoVOSList = basicInfoRemote.queryByClassId(queryDTO);
                    List<Long> staffIds = staffFullInfoVOSList.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList());
                    //该班级所有老师点评次数
                    int evaluateNum = behaviourRecordDTOList.stream().filter(s -> staffIds.contains(Convert.toLong(s.getAppraisalId())) && s.getClassId().equals(Convert.toStr(eduOrgTreeVO.getId()))).collect(Collectors.toList()).size();
                    NameAndValueVO nameAndValueVO = new NameAndValueVO();
                    nameAndValueVO.setName(Convert.toStr(eduOrgTreeVO.getId()));
                    nameAndValueVO.setValue(Convert.toStr(evaluateNum));
                    nameAndValueVOS.add(nameAndValueVO);
                }
                nameAndValueVOS.stream().sorted(Comparator.comparing(NameAndValueVO::getValue).reversed().thenComparing(NameAndValueVO::getName)).collect(Collectors.toList());
                int indexOf = CollectionUtil.indexOf(nameAndValueVOS, s -> s.getName().equals(Convert.toStr(classId)));
                staffFullInfoVO.setMessageUrl(messageUrl + "/home?current=2" +
                        "&tenantId=" + tenantId +
                        "&schoolId=" + schoolId +
                        "&campusId=" + campusId +
                        "&campusSectionId=" + staffFullInfoVO.getCampusSectionId() +
                        "&gradeId=" + Convert.toStr(parentId) +
                        "&classId=" + Convert.toStr(classId) +
                        "&staffId=" + staffFullInfoVO.getStaffId() +
                        "&startTime=" + DateUtil.format(startTime, Constant.PATTERN_FORMAT) +
                        "&endTime=" + DateUtil.format(endTime, Constant.PATTERN_FORMAT) +
                        "&fromMsg=1"
                );
                List<EduClassInfoLinkVO> eduClassInfoLinkVOS = basicInfoRemote.listUnderByClassIds(Arrays.asList(classId));
                if (CollectionUtil.isEmpty(eduClassInfoLinkVOS)) {
                    log.info("没有该班级信息，班级 id：[{}]", classId);
                    continue;
                }
                staffFullInfoVO.setTitle(MessageFormat.format(CLASS_EVALUATE_STATISTICS_INFORM, eduClassInfoLinkVOS.get(0).getClassName()));
                if (MsgTypeEnum.WECHAT.getCode().equals(msgType)) {
                    staffFullInfoVO.setInform(MessageFormat.format(CLASS_TEACHER_ALL_WECHAT_INFORM,
                            eduClassInfoLinkVOS.get(0).getClassName(),
                            staffFullInfoVOList.size(),
                            totalNum,
                            totalPeopleNum,
                            Constant.ZERO.equals(totalNum) || Constant.NEGATIVE_ONE.equals(indexOf) ? children.size() : indexOf + 1));
                    continue;
                }
                if (CollectionUtil.isEmpty(noEvaluateStudents)) {
                    staffFullInfoVO.setInform(MessageFormat.format(CLASS_TEACHER_ALL_DINGDING_INFORM,
                            eduClassInfoLinkVOS.get(0).getClassName(),
                            staffFullInfoVOList.size(),
                            totalNum,
                            totalPeopleNum,
                            eduOrgTreeVOList.get(Constant.ZERO).getName(),
                            children.size(),
                            Constant.ZERO.equals(totalNum) || Constant.NEGATIVE_ONE.equals(indexOf) ? children.size() : indexOf + 1,
                            String.join(Constant.CAESURA_SIGN, teacherEvaluateNum),
                            eduClassInfoLinkVOS.get(0).getClassName()));
                    continue;
                }
                staffFullInfoVO.setInform(MessageFormat.format(CLASS_TEACHER_DINGDING_INFORM,
                        ucStudentClassBffS.get(Constant.ZERO).getClassName(),
                        staffFullInfoVOList.size(),
                        totalNum,
                        totalPeopleNum,
                        eduOrgTreeVOList.get(Constant.ZERO).getName(),
                        children.size(),
                        Constant.ZERO.equals(totalNum) || Constant.NEGATIVE_ONE.equals(indexOf) ? children.size() : indexOf + 1,
                        String.join(Constant.CAESURA_SIGN, teacherEvaluateNum),
                        ucStudentClassBffS.get(Constant.ZERO).getClassName(),
                        studentNameList
                ));
                continue;
            } else if (TeacherRoleEnum.GRADE_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {
                //该年级信息
                EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
                eduOrgQueryDTO.setCurrentId(staffFullInfoVO.getClassOrGrade());
                eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.GRADE.getCode());
                List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
                if (CollUtil.isEmpty(eduOrgTreeVOS)) {
                    log.warn("【{根据老师获取老师需要发送的通知内容}】没有该年级，入参：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
                    continue;
                }
                //年级下所有班级
                List<EduOrgTreeVO> children = eduOrgTreeVOS.get(0).getChildren();
                if (CollUtil.isEmpty(children)) {
                    log.warn("【{根据老师获取老师需要发送的通知内容}】该年级下没有班级，入参：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
                    continue;
                }
                //学段id
                Long parentId = eduOrgTreeVOS.get(0).getParentId();
                //年级名称
                String gradeName = eduOrgTreeVOS.get(0).getName();
                //该年级老师数量
                Integer gradeTeacherNum = 0;
                //该年级点评次数
                Integer gradeEvaluateNum = 0;
                //该年级点评人数
                Integer gradeStudentNum = 0;

                eduOrgQueryDTO.setCurrentId(parentId);
                eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
                List<EduOrgTreeVO> orgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
                if (CollUtil.isEmpty(orgTreeVOS)) {
                    log.warn("【{根据老师获取老师需要发送的通知内容}】没有该学段，入参：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
                    continue;
                }
                if (CollUtil.isEmpty(orgTreeVOS.get(0).getChildren())) {
                    log.warn("【{根据老师获取老师需要发送的通知内容}】该学段下没有年级，学段id：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
                    continue;
                }
                //学段名称
                String sectionName = orgTreeVOS.get(0).getName();
                //学段下所有年级id
                List<Long> gradeLongs = orgTreeVOS.get(0).getChildren().stream().map(EduOrgTreeVO::getId).collect(Collectors.toList());
                //该学段指标正常且配有点评项的老师ids
                List<String> listTargetConfigTeacherIds = behaviourHandleManager.listTargetConfigTeacherIds(schoolId, campusId, Convert.toStr(parentId), null, null, startTime, endTime);
                //各年级排名
                ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
                //各班级点评次数
                ArrayList<String> classEvaluateNum = new ArrayList<>();
                for (Long gradeLong : gradeLongs) {
                    //saas获取该年级所有老师
                    StaffSectionOrGradeQueryDTO dto = new StaffSectionOrGradeQueryDTO();
                    dto.setGradeId(staffFullInfoVO.getClassOrGrade());
                    List<StaffFullInfoVO> fullInfoVOS = basicInfoRemote.queryByGradeId(dto);
                    //该年级所有老师id
                    List<Long> longs = fullInfoVOS.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList());
                    List<String> stringIds = Convert.toList(String.class, longs);
                    //该年级有点评的老师
                    Collection<String> intersection = CollectionUtil.intersection(listTargetConfigTeacherIds, stringIds);
                    //该年级老师对该年级点评数据
                    List<BehaviourRecordDTO> recordDTOS = behaviourRecordDTOList.stream().filter(s -> stringIds.contains(s.getAppraisalId()) && Convert.toStr(gradeLong).equals(s.getGradeId())).collect(Collectors.toList());
                    //该年级老师对该年级点评次数
                    Integer evaluateNum = recordDTOS.size();
                    if (gradeLong.equals(staffFullInfoVO.getClassOrGrade())) {
                        //老师数量
                        gradeTeacherNum = intersection.size();
                        //点评次数
                        gradeEvaluateNum = evaluateNum;
                        //点评人次
                        gradeStudentNum = recordDTOS.stream().map(BehaviourRecordDTO::getStudentId).distinct().collect(Collectors.toList()).size();
                        for (EduOrgTreeVO child : children) {
                            //点评次数
                            int size = behaviourRecordDTOList.stream().filter(s -> stringIds.contains(s.getAppraisalId()) && Convert.toStr(child.getId()).equals(s.getClassId())).collect(Collectors.toList()).size();
                            classEvaluateNum.add(child.getName() + " " + size + "次");
                        }
                    }
                    NameAndValueVO nameAndValueVO = new NameAndValueVO();
                    nameAndValueVO.setName(Convert.toStr(gradeLong));
                    nameAndValueVO.setValue(Convert.toStr(evaluateNum));
                    nameAndValueVOS.add(nameAndValueVO);
                }
                //年级点评次数排序
                nameAndValueVOS.stream().sorted(Comparator.comparing(NameAndValueVO::getValue).reversed().thenComparing(NameAndValueVO::getName)).collect(Collectors.toList());
                //该年级排名
                int indexOf = CollectionUtil.indexOf(nameAndValueVOS, s -> s.getName().equals(Convert.toStr(staffFullInfoVO.getClassOrGrade())));
                staffFullInfoVO.setTitle(MessageFormat.format(GRADE_EVALUATE_STATISTICS_INFORM, gradeName));
                if (MsgTypeEnum.WECHAT.getCode().equals(msgType)) {
                    staffFullInfoVO.setInform(MessageFormat.format(GRADE_TEACHER_WECHAT_INFORM,
                            gradeName,
                            gradeTeacherNum,
                            gradeEvaluateNum,
                            gradeStudentNum,
                            Constant.ZERO.equals(gradeEvaluateNum) || Constant.NEGATIVE_ONE.equals(indexOf) ? gradeLongs.size() : indexOf + 1
                    ));
                } else {
                    staffFullInfoVO.setInform(MessageFormat.format(GRADE_TEACHER_DINGDING_INFORM,
                            gradeName,
                            gradeTeacherNum,
                            gradeEvaluateNum,
                            gradeStudentNum,
                            sectionName,
                            gradeLongs.size(),
                            Constant.ZERO.equals(gradeEvaluateNum) || Constant.NEGATIVE_ONE.equals(indexOf) ? gradeLongs.size() : indexOf + 1,
                            String.join(Constant.CAESURA_SIGN, classEvaluateNum)
                    ));
                }

                staffFullInfoVO.setMessageUrl(messageUrl + "/home?current=2" +
                        "&tenantId=" + tenantId +
                        "&schoolId=" + schoolId +
                        "&campusId=" + campusId +
                        "&campusSectionId=" + staffFullInfoVO.getCampusSectionId() +
                        "&gradeId=" + Convert.toStr(staffFullInfoVO.getClassOrGrade()) +
                        "&staffId=" + staffFullInfoVO.getStaffId() +
                        "&startTime=" + DateUtil.format(startTime, Constant.PATTERN_FORMAT) +
                        "&endTime=" + DateUtil.format(endTime, Constant.PATTERN_FORMAT) +
                        "&fromMsg=1"

                );
                continue;
            } else if (TeacherRoleEnum.SCHOOL_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {
                //学段
                String campusSectionId = staffFullInfoVO.getCampusSectionId();
                //该学校信息
                EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
                eduOrgQueryDTO.setCurrentId(Convert.toLong(campusSectionId));
                eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
                List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
                if (CollUtil.isEmpty(eduOrgTreeVOS)) {
                    log.warn("【{根据老师获取老师需要发送的通知内容}】没有该学段，入参：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
                    continue;
                }
                if (CollUtil.isEmpty(eduOrgTreeVOS.get(0).getChildren())) {
                    log.warn("【{根据老师获取老师需要发送的通知内容}】该学段下没有年级，入参：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
                    continue;
                }
                //学校名称
                String schoolName = eduOrgTreeVOS.get(0).getName();
//                //该学校所有校区
//                List<List<EduOrgTreeVO>> sectionEduOrgTreeVOS = eduOrgTreeVOS.stream().map(EduOrgTreeVO::getChildren).collect(Collectors.toList());
//                List<EduOrgTreeVO> orgTreeVOS = sectionEduOrgTreeVOS.stream().flatMap(List::stream).collect(Collectors.toList());
//                //该学校所有学段
//                List<List<EduOrgTreeVO>> lists = orgTreeVOS.stream().map(EduOrgTreeVO::getChildren).collect(Collectors.toList());
//                List<EduOrgTreeVO> eduOrgTreeVOList = lists.stream().filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
                //该学段下所有年级
                List<EduOrgTreeVO> gradeOrgTreeVOS = eduOrgTreeVOS.get(0).getChildren();
                //该学段所有点评记录
                List<BehaviourRecordDTO> recordDTOS = behaviourHandleManager.listBehaviourRecordNew(schoolId, campusId, staffFullInfoVO.getCampusSectionId(), null, null, startTime, endTime, null).stream().filter(s -> Constant.ONE.equals(s.getAppraisalType())).collect(Collectors.toList());
                //点评次数
                Integer evaluateNum = recordDTOS.size();
                //点评人次
                int size = recordDTOS.stream().map(BehaviourRecordDTO::getStudentId).distinct().collect(Collectors.toList()).size();
                //各年级点评次数
                ArrayList<String> gradeEvaluateNum = new ArrayList<>();
                for (EduOrgTreeVO gradeOrgTreeVO : gradeOrgTreeVOS) {
                    gradeEvaluateNum.add(gradeOrgTreeVO.getName() + " " + (CollectionUtil.isEmpty(recordDTOS.stream().filter(s -> s.getGradeId().equals(Convert.toStr(gradeOrgTreeVO.getId()))).collect(Collectors.toList())) ? 0 : recordDTOS.stream().filter(s -> s.getGradeId().equals(Convert.toStr(gradeOrgTreeVO.getId()))).collect(Collectors.toList()).size()) + "次");
                }
                staffFullInfoVO.setTitle(MessageFormat.format(SCHOOL_EVALUATE_STATISTICS_INFORM, schoolName));
                if (MsgTypeEnum.WECHAT.getCode().equals(msgType)) {
                    staffFullInfoVO.setInform(MessageFormat.format(SCHOOL_TEACHER_WECHAT_INFORM,
                            schoolName,
                            evaluateNum,
                            size
                    ));
                } else {
                    staffFullInfoVO.setInform(MessageFormat.format(SCHOOL_TEACHER_DINGDING_INFORM,
                            schoolName,
                            evaluateNum,
                            size,
                            String.join(Constant.CAESURA_SIGN, gradeEvaluateNum)
                    ));
                }
                staffFullInfoVO.setMessageUrl(messageUrl + "/home?current=2" +
                        "&tenantId=" + tenantId +
                        "&schoolId=" + schoolId +
                        "&campusId=" + campusId +
                        "&campusSectionId=" + staffFullInfoVO.getCampusSectionId() +
                        "&staffId=" + staffFullInfoVO.getStaffId() +
                        "&startTime=" + DateUtil.format(startTime, Constant.PATTERN_FORMAT) +
                        "&endTime=" + DateUtil.format(endTime, Constant.PATTERN_FORMAT) +
                        "&fromMsg=1"
                );
                continue;
            }
            staffFullInfoVO.setStartTime(startTime);
            staffFullInfoVO.setEndTime(endTime);
        }
        List<StaffFullInfoVO> staffFullInfoVOList = staffFullInfoVOS.stream().filter(s -> Constant.ZERO.equals(s.getSendFlag())).collect(Collectors.toList());
        List<StaffFullInfoVO> collect = staffFullInfoVOS.stream().filter(s -> s.getStaffId().equals(7032260496354471936L)).collect(Collectors.toList());
        List<StaffFullInfoVO> collect1 = staffFullInfoVOS.stream().filter(s -> s.getMobile().equals("19883175649")).collect(Collectors.toList());
        return staffFullInfoVOList;
    }

    @Override
    public List<StaffFullInfoVO> listTeacherInformV2(String schoolId,
                                                     String campusId,
                                                     String tenantId,
                                                     List<StaffFullInfoVO> staffFullInfoVOS,
                                                     Date startTime,
                                                     Date endTime,
                                                     Integer msgType,
                                                     List<BehaviourRecordDTO> behaviourRecordDTOList) {
        if (CollUtil.isEmpty(staffFullInfoVOS)) {
            return Collections.emptyList();
        }
        //手动开启定时任务时间
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            startTime = DateUtil.beginOfWeek(DateUtil.lastWeek());
            endTime = DateUtil.endOfDay(DateUtil.endOfWeek(startTime));
        } else {
            startTime = DateUtil.beginOfDay(startTime);
            endTime = DateUtil.endOfDay(endTime);
        }
        //时间段内该校区所有的点评数据
//        List<BehaviourRecordDTO> behaviourRecordDTOList = behaviourHandleManager.listBehaviourRecordNew(schoolId, campusId, null, null, null, startTime, endTime,1);
        //获取所有年级各个老师的点评次数和人数
        for (StaffFullInfoVO staffFullInfoVO : staffFullInfoVOS) {
            if (TeacherRoleEnum.NORMAL_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {
                if(CollUtil.isEmpty(staffFullInfoVO.getClassList()) || CollUtil.isEmpty(staffFullInfoVO.getGradeList())){
                    log.warn("【获取所有年级各个老师的点评次数和人数】老师没有班级或者年级， staffFullInfoVO:{}", JSONUtil.toJsonStr(staffFullInfoVO));
                    continue;
                }
                List<StaffFullInfoVO> fullInfoVOList = Collections.singletonList(staffFullInfoVO);
                ArrayList<TeacherVO> teacherVOS = this.listTeachersV2(schoolId, campusId, fullInfoVOList, startTime, endTime, behaviourRecordDTOList);
                this.buildNormalTeacherMsg(staffFullInfoVO, teacherVOS, schoolId, campusId, startTime, endTime, msgType, tenantId, behaviourRecordDTOList);
            } else if (TeacherRoleEnum.CLASS_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {

                this.buildClassTeacherMsg(staffFullInfoVO, behaviourRecordDTOList, schoolId, campusId, startTime, endTime, msgType, tenantId);
            } else if (TeacherRoleEnum.GRADE_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {

                this.buildGradeTeacherMsg(staffFullInfoVO, behaviourRecordDTOList, schoolId, campusId, startTime, endTime, msgType, tenantId);
            } else if (TeacherRoleEnum.SCHOOL_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {

                this.buildSchoolTeacherMsg(staffFullInfoVO, schoolId, campusId, startTime, endTime, msgType, tenantId, behaviourRecordDTOList);
            }
            staffFullInfoVO.setStartTime(startTime);
            staffFullInfoVO.setEndTime(endTime);
        }
        List<StaffFullInfoVO> staffFullInfoVOList = staffFullInfoVOS.stream().filter(s -> Constant.ZERO.equals(s.getSendFlag())).collect(Collectors.toList());
        return staffFullInfoVOList;
    }

//    /**
//     * 表扬与待改进
//     *
//     * @param dto
//     * @return
//     */
//    @Override
//    public PraiseImproveVO getPraiseImproveDetail(DataStatisticsQuery dto) {
//        // saas学段/年级/班级信息
//        SaasCurrentInfoDTO saasCurrentInfoDTO = listBusinessIdByBusinessType(dto);
//
//        PraiseImproveVO praiseImproveVO = new PraiseImproveVO();
//        String title = "表扬与待改进";
//        String detailTitle = "评分情况";
//        // 学段
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            title = "全校" + title;
//            detailTitle = "各年级" + detailTitle;
//        }
//        // 年级
//        if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            title = saasCurrentInfoDTO.getCurrentBusinessName() + title;
//            detailTitle = "各班级" + detailTitle;
//        }
//        // 班级
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            title = saasCurrentInfoDTO.getCurrentBusinessName() + title;
//            detailTitle = "各老师" + detailTitle;
//        }
//        praiseImproveVO.setEmptyFlag(Constant.NO);
//        praiseImproveVO.setTitle(title);
//        praiseImproveVO.setDetailTitle(detailTitle);
//
//        //时间段内所有的点评数据
//        List<BehaviourRecordDTO> behaviourRecords = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime(),null);
//        behaviourRecords = behaviourRecords.stream().filter(s -> CollUtil.newArrayList(ScoreTypeEnum.PLUS.getCode(), ScoreTypeEnum.REDUCE.getCode()).contains(s.getScoreType()) && Objects.nonNull(s.getScoreValue())).collect(Collectors.toList());
//        if (CollUtil.isEmpty(behaviourRecords)) {
//            praiseImproveVO.setEmptyFlag(Constant.YES);
//            return praiseImproveVO;
//        }
//        BigDecimal sumScore = behaviourRecords.stream().map(BehaviourRecordDTO::getScoreValue).reduce(BigDecimal.ZERO, BigDecimal::add);
//        // 如果所有分值相加为0
//        if (ObjectUtil.equals(BigDecimal.ZERO, sumScore)) {
//            praiseImproveVO.setEmptyFlag(Constant.YES);
//            return praiseImproveVO;
//        }
//
//
//        // 表扬总分值
//        BigDecimal praise = behaviourRecords.stream().filter(s -> ScoreTypeEnum.PLUS.getCode().equals(s.getScoreType())).map(BehaviourRecordDTO::getScoreValue).reduce(BigDecimal.ZERO, BigDecimal::add);
//        // 待改进总分值
//        BigDecimal needImprove = behaviourRecords.stream().filter(s -> ScoreTypeEnum.REDUCE.getCode().equals(s.getScoreType())).map(BehaviourRecordDTO::getScoreValue).reduce(BigDecimal.ZERO, BigDecimal::add);
//        // 表扬占比(保留两位小数)
//        BigDecimal praiseProportion = praise.divide(praise.add(needImprove), 3, RoundingMode.HALF_UP);
//        // 待改进占比(保留两位小数)
//        BigDecimal needImproveProportion = new BigDecimal(1).subtract(praiseProportion);
//
//        praiseImproveVO.setTotalScore(praise.add(needImprove));
//        praiseImproveVO.setPraiseProportion(praiseProportion.multiply(Convert.toBigDecimal(100)));
//        praiseImproveVO.setNeedImprovedProportion(needImproveProportion.multiply(Convert.toBigDecimal(100)));
//
//
//        // 行为记录根据类型(年级/班级/老师)分组
//        Map<String, List<BehaviourRecordDTO>> groupMap = getBehaviourGroupByCurrentIdTyp(saasCurrentInfoDTO.getBusinessMap(), behaviourRecords, dto.getType());
//
//        List<PraiseImproveDetailVO> details = new ArrayList<>();
//        for (String businessId : saasCurrentInfoDTO.getSortBusinessKey()) {
//            List<BehaviourRecordDTO> behaviourRecordDTOList = groupMap.get(businessId);
//            PraiseImproveDetailVO praiseDetailVO = new PraiseImproveDetailVO();
//            praiseDetailVO.setName(saasCurrentInfoDTO.getBusinessMap().getOrDefault(businessId, ""));
//
//            // 无数据情况
//            if (CollUtil.isEmpty(behaviourRecordDTOList)) {
//                details.add(praiseDetailVO);
//                continue;
//            }
//            // 总分
//            BigDecimal totalScore = behaviourRecordDTOList.stream().map(BehaviourRecordDTO::getScoreValue).reduce(BigDecimal.ZERO, BigDecimal::add);
//            if (ObjectUtil.equals(BigDecimal.ZERO, totalScore)) {
//                log.info("[表扬与待改进]-[业务分组占比]-[总分为空]");
//                continue;
//            }
//
//            // 表扬总分值
//            BigDecimal detailPraise = behaviourRecordDTOList.stream().filter(s -> ScoreTypeEnum.PLUS.getCode().equals(s.getScoreType())).map(BehaviourRecordDTO::getScoreValue).reduce(BigDecimal.ZERO, BigDecimal::add);
//            // 表扬占比(保留两位小数)
//            BigDecimal detailPraiseProportion = detailPraise.divide(totalScore, 3, RoundingMode.HALF_UP);
//            // 待改进占比(保留两位小数)
//            BigDecimal detailNeedImproveProportion = new BigDecimal(1).subtract(detailPraiseProportion);
//            praiseDetailVO.setPraiseProportion(detailPraiseProportion.multiply(Convert.toBigDecimal(100)));
//            praiseDetailVO.setNeedImprovedProportion(detailNeedImproveProportion.multiply(Convert.toBigDecimal(100)));
//            praiseDetailVO.setIsNotEmpty(Constant.YES);
//            details.add(praiseDetailVO);
//        }
//
//        // 班级
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            details = details.stream().sorted(Comparator.comparing(PraiseImproveDetailVO::getIsNotEmpty, Comparator.nullsFirst(Integer::compareTo).reversed())).collect(Collectors.toList());
//        }
//        praiseImproveVO.setDetails(details);
//
//        // 智能建议
//        List<String> businessNames = details.stream().filter(s -> {
//            boolean flag = false;
//            if (Objects.nonNull(s.getNeedImprovedProportion()) && s.getNeedImprovedProportion().compareTo(praiseImproveDetail) > 0) {
//                flag = Boolean.TRUE;
//            }
//            return flag;
//        }).map(PraiseImproveDetailVO::getName).collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(businessNames)) {
//            String intelligentSuggestion = StrUtil.join(StrPool.COMMA, businessNames) + "点评的待改进高于" + praiseImproveDetail + "%，希望多鼓励孩子喔";
//            praiseImproveVO.setIntelligentSuggestion(intelligentSuggestion);
//        }
//
//        return praiseImproveVO;
//    }
//
//
//    /**
//     * 五育分布
//     *
//     * @param dto
//     * @return
//     */
//    @Override
//    public FiveEducationVO getFiveEducation(DataStatisticsQuery dto) {
//        // saas学段/年级/班级信息
//        SaasCurrentInfoDTO saasCurrentInfoDTO = listBusinessIdByBusinessType(dto);
//
//        FiveEducationVO fiveEducationVO = new FiveEducationVO();
//        String title = "五育分布";
//        String detailTitle = "维度情况";
//        // 学段
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            title = "全校" + title;
//            detailTitle = "各年级" + detailTitle;
//        }
//        // 年级
//        if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            title = saasCurrentInfoDTO.getCurrentBusinessName() + title;
//            detailTitle = "各班级" + detailTitle;
//        }
//        // 班级
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            title = saasCurrentInfoDTO.getCurrentBusinessName() + title;
//            detailTitle = "各老师" + detailTitle;
//        }
//        fiveEducationVO.setEmptyFlag(Constant.NO);
//        fiveEducationVO.setTitle(title);
//        fiveEducationVO.setDetailTitle(detailTitle);
//
//        // 时间段内所有的点评数据
//        List<BehaviourRecordDTO> behaviourRecords = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime(),null);
//        // 点评次数map
//        Map<Integer, Long> commentNumMap = behaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecordDTO::getModuleCode, Collectors.counting()));
//
//        behaviourRecords = behaviourRecords.stream().filter(s -> CollUtil.newArrayList(ScoreTypeEnum.PLUS.getCode(), ScoreTypeEnum.REDUCE.getCode()).contains(s.getScoreType()) && Objects.nonNull(s.getScoreValue())).collect(Collectors.toList());
//        if (CollUtil.isEmpty(behaviourRecords)) {
//            fiveEducationVO.setEmptyFlag(Constant.YES);
//            return fiveEducationVO;
//        }
//
//
//        // 根据行为记录统计每个五育模块的占比
//        List<FiveEducationDistributionVO> distributions = encapsulationFiveEducation(behaviourRecords, null, null, commentNumMap);
//
//        // 总分
//        fiveEducationVO.setTotalScore(behaviourRecords.stream().map(BehaviourRecordDTO::getScoreValue).reduce(BigDecimal.ZERO, BigDecimal::add));
//        // 五育明细占比
//        fiveEducationVO.setDistributions(distributions);
//
//        // 行为记录根据类型(年级/班级/老师)分组
//        Map<String, List<BehaviourRecordDTO>> groupMap = getBehaviourGroupByCurrentIdTyp(saasCurrentInfoDTO.getBusinessMap(), behaviourRecords, dto.getType());
//
//        // 所有(年级/班级)的明细详情
//        List<FiveEducationDistributionVO> allBusinessDistributions = new ArrayList<>();
//
//        // 各年级(班级)五育分布占比详情数据封装
//        List<FiveEducationDetailVO> details = new ArrayList<>();
//        for (String businessId : saasCurrentInfoDTO.getSortBusinessKey()) {
//            FiveEducationDetailVO fiveEducationDetail = new FiveEducationDetailVO();
//            fiveEducationDetail.setName(saasCurrentInfoDTO.getBusinessMap().getOrDefault(businessId, ""));
//            List<BehaviourRecordDTO> behaviourRecordDTOList = groupMap.get(businessId);
//
//            // 如果没有点评数据
//            if (CollUtil.isEmpty(behaviourRecordDTOList)) {
//                details.add(fiveEducationDetail);
//                continue;
//            }
//
//            // 封装每个模块的占比
//            List<FiveEducationDistributionVO> detailDistributions = encapsulationFiveEducation(behaviourRecordDTOList, businessId, fiveEducationDetail.getName(), null);
//            fiveEducationDetail.setDistributions(detailDistributions);
//            fiveEducationDetail.setIsNotEmpty(Constant.YES);
//            allBusinessDistributions.addAll(detailDistributions);
//            details.add(fiveEducationDetail);
//        }
//        // 班级
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            details = details.stream().sorted(Comparator.comparing(FiveEducationDetailVO::getIsNotEmpty, Comparator.nullsFirst(Integer::compareTo).reversed())).collect(Collectors.toList());
//        }
//        fiveEducationVO.setDetails(details);
//
//        // 低点评数据
//        List<FiveEducationDistributionVO> lowProportions = allBusinessDistributions.stream().filter(s -> {
//            Boolean flag = Boolean.FALSE;
//            if (s.getProportion().compareTo(fiveEducation) < 0) {
//                flag = Boolean.TRUE;
//            }
//            return flag;
//        }).collect(Collectors.toList());
//        Map<Integer, List<FiveEducationDistributionVO>> moduleMap = lowProportions.stream().collect(Collectors.groupingBy(FiveEducationDistributionVO::getModuleCode));
//
//        // 智能建议
//        List<String> intelligentSuggestions = new ArrayList<>();
//        // 存在未点评的列表
//        List<String> emptyDataBusinessNames = details.stream().filter(s -> CollUtil.isEmpty(s.getDistributions())).map(FiveEducationDetailVO::getName).collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(emptyDataBusinessNames)) {
//            for (Integer moduleCode : ModuleEnum.moduleMap.keySet()) {
//                if (CollUtil.isNotEmpty(moduleMap.get(moduleCode))) {
//                    List<FiveEducationDistributionVO> fiveEducationDistributionVOList = moduleMap.get(moduleCode);
//                    // 筛选出年级
//                    List<String> businessNames = fiveEducationDistributionVOList.stream().map(FiveEducationDistributionVO::getBusinessName).distinct().collect(Collectors.toList());
//                    String intelligentSuggestion = ModuleEnum.getModuleName(moduleCode) + ":" + StrUtil.join(StrPool.COMMA, businessNames) + "," + StrUtil.join(StrPool.COMMA, emptyDataBusinessNames) + "点评率低于" + fiveEducation + "%，希望进行多点评";
//                    intelligentSuggestions.add(intelligentSuggestion);
//                } else {
//                    String intelligentSuggestion = ModuleEnum.getModuleName(moduleCode) + ":" + StrUtil.join(StrPool.COMMA, emptyDataBusinessNames) + "点评率低于" + fiveEducation + "%，希望进行多点评";
//                    intelligentSuggestions.add(intelligentSuggestion);
//                }
//            }
//        }
//        // 不存在未点评的列表
//        if (CollUtil.isEmpty(emptyDataBusinessNames)) {
//            for (Integer moduleCode : moduleMap.keySet()) {
//                List<FiveEducationDistributionVO> fiveEducationDistributionVOList = moduleMap.get(moduleCode);
//                // 筛选出年级
//                List<String> businessNames = fiveEducationDistributionVOList.stream().map(FiveEducationDistributionVO::getBusinessName).distinct().collect(Collectors.toList());
//                String intelligentSuggestion = ModuleEnum.getModuleName(moduleCode) + ":" + StrUtil.join(StrPool.COMMA, businessNames) + "点评率低于" + fiveEducation + "%，希望进行多点评";
//                intelligentSuggestions.add(intelligentSuggestion);
//            }
//        }
//        // 班级看板无智能建议
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            fiveEducationVO.setIntelligentSuggestion(null);
//        } else {
//            // 智能建议
//            fiveEducationVO.setIntelligentSuggestion(intelligentSuggestions);
//        }
//
//
//        return fiveEducationVO;
//    }

//    /**
//     * 指标覆盖率(只有校级数据,指标配置只有校级维度)
//     *
//     * @param dto
//     * @return
//     */
//    @Override
//    public TargetCoverageVO getTargetCoverage(DataStatisticsQuery dto) {
//        // saas学段/年级/班级信息
//        SaasCurrentInfoDTO saasCurrentInfoDTO = listBusinessIdByBusinessType(dto);
//
//        String title = "指标覆盖率";
//        String detailTitle = "指标覆盖情况";
//        // 学段
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType())) {
//            title = "全校" + title;
//            detailTitle = "全校" + detailTitle;
//        }
//        // 年级
//        if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            title = saasCurrentInfoDTO.getCurrentBusinessName() + title;
//            detailTitle = saasCurrentInfoDTO.getCurrentBusinessName() + detailTitle;
//        }
//        // 班级
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            title = saasCurrentInfoDTO.getCurrentBusinessName() + title;
//            detailTitle = saasCurrentInfoDTO.getCurrentBusinessName() + detailTitle;
//        }
//
//        // saas学段/年级/班级信息
//        TargetCoverageVO targetCoverageVO = new TargetCoverageVO();
//        targetCoverageVO.setEmptyFlag(Constant.NO);
//        targetCoverageVO.setTitle(title);
//        targetCoverageVO.setDetailTitle(detailTitle);
//        //时间段内所有的点评数据
//        List<BehaviourRecordDTO> behaviourRecords = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime(),null);
//        //获取校区内所有启用的指标
//        List<TargetDTO> targets = behaviourHandleManager.listEnableTarget(WebUtil.getSchoolId(), WebUtil.getCampusId());
//        if (CollUtil.isEmpty(targets)) {
//            targetCoverageVO.setEmptyFlag(Constant.YES);
//            return targetCoverageVO;
//        }
//        List<Long> targetIds = targets.stream().map(TargetDTO::getId).distinct().collect(Collectors.toList());
//        if (CollUtil.isEmpty(targetIds)) {
//            targetCoverageVO.setEmptyFlag(Constant.YES);
//            return targetCoverageVO;
//        }
//
//        //获取点评过的指标个数(需要过滤出启用且未删除的指标)
//        Map<Long, Long> commentMap = behaviourRecords.stream().filter(s -> targetIds.contains(s.getTargetId())).collect(Collectors.groupingBy(BehaviourRecordDTO::getTargetId, Collectors.counting()));
//
//        targetCoverageVO.setTotalNum(Convert.toInt(targetIds.size()));
//        // 被点评指标个数(点评过的指标个数/总指标个数)
//        BigDecimal beReviewedProportion = new BigDecimal(commentMap.keySet().size()).divide(new BigDecimal(targetIds.size()), 3, RoundingMode.HALF_UP);
//        // 未被点评指标占比
//        BigDecimal notReviewedProportion = new BigDecimal(1).subtract(beReviewedProportion);
//
//        targetCoverageVO.setBeReviewedProportion(beReviewedProportion.multiply(Convert.toBigDecimal(100)));
//        targetCoverageVO.setNotReviewedProportion(notReviewedProportion.multiply(Convert.toBigDecimal(100)));
//
//        // 智能建议
//        if (targetCoverageVO.getNotReviewedProportion().compareTo(Convert.toBigDecimal(0)) > 0) {
//            targetCoverageVO.setIntelligentSuggestion("有" + targetCoverageVO.getNotReviewedProportion().stripTrailingZeros().toPlainString() + "%的指标未被填写过，可以考虑督促老师填写或删除");
//        }
//
//        return targetCoverageVO;
//    }
//
//    /**
//     * 指标覆盖情况(只有校级数据,指标配置只有校级维度)
//     *
//     * @param dto
//     * @return
//     */
//    @Override
//    public List<TargetFrequencyVO> getTargetFrequency(DataStatisticsQuery dto) {
//
//        List<TargetRankVO> targetRankList = listAllTargetRank(dto);
//
//        List<TargetFrequencyVO> frequencyList = targetRankList.stream().map(s -> {
//            TargetFrequencyVO targetFrequency = new TargetFrequencyVO();
//            targetFrequency.setTargetName(s.getTargetName());
//            targetFrequency.setNum(s.getCount());
//            targetFrequency.setCreateTime(s.getCreateTime());
//            return targetFrequency;
//        }).collect(Collectors.toList());
//
//        if (CollUtil.isEmpty(frequencyList)) {
//            return Collections.emptyList();
//        }
//        //获取校区内所有启用的指标
//        List<TargetDTO> targetDTOS = behaviourHandleManager.listEnableTarget(WebUtil.getSchoolId(), WebUtil.getCampusId());
//        if (CollUtil.isEmpty(targetDTOS)) {
//            return Collections.emptyList();
//        }
//
//        List<TargetFrequencyVO> result = new ArrayList<>();
//        if (frequencyList.size() > 0 && frequencyList.size() <= 10) {
//            // 高频
//            List<TargetFrequencyVO> heightList = frequencyList.stream().filter(s -> s.getNum() > 0).collect(Collectors.toList());
//            heightList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.HEIGHT.getCode()));
//
//            // 低频
//            List<TargetFrequencyVO> lowList = frequencyList.stream().filter(s -> s.getNum() == 0).collect(Collectors.toList());
//            lowList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.LOW.getCode()));
//
//            // 汇总
//            result.addAll(heightList);
//            result.addAll(lowList);
//        }
//        if (frequencyList.size() > 10 && frequencyList.size() <= 20) {
//            // 大于0的数据
//            List<TargetFrequencyVO> noZeroNumList = frequencyList.stream().filter(s -> s.getNum() > 0).collect(Collectors.toList());
//            if (noZeroNumList.size() >= 10) {
//                // 高频
//                List<TargetFrequencyVO> heightList = frequencyList.stream().limit(10).collect(Collectors.toList());
//                heightList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.HEIGHT.getCode()));
//
//                // 低频
//                frequencyList.removeAll(heightList);
//                frequencyList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.LOW.getCode()));
//
//                // 汇总
//                result.addAll(heightList);
//                result.addAll(frequencyList);
//            } else {
//                // 高频
//                noZeroNumList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.HEIGHT.getCode()));
//
//                // 低频
//                frequencyList.removeAll(noZeroNumList);
//                frequencyList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.LOW.getCode()));
//
//                // 汇总
//                result.addAll(noZeroNumList);
//                result.addAll(frequencyList);
//            }
//
//        }
//        if (frequencyList.size() > 20) {
//            // 高频
//            List<TargetFrequencyVO> heightList = frequencyList.stream().filter(s -> s.getNum() > 0).sorted(Comparator.comparing(TargetFrequencyVO::getNum).reversed()).limit(10).collect(Collectors.toList());
//            heightList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.HEIGHT.getCode()));
//
//            // 低频
//            List<TargetFrequencyVO> lowList = frequencyList.stream().sorted(Comparator.comparing(TargetFrequencyVO::getNum).reversed().thenComparing(TargetFrequencyVO::getCreateTime).reversed()).limit(10).collect(Collectors.toList());
//            lowList.forEach(s -> s.setFrequencyType(FrequencyTypeEnum.LOW.getCode()));
//
//            // 汇总
//            result.addAll(heightList);
//            result.addAll(lowList);
//        }
//        List<TargetFrequencyVO> collect = result.stream().sorted(Comparator.comparing(TargetFrequencyVO::getNum).reversed()).collect(Collectors.toList());
//        return collect;
//    }
//
//    /**
//     * 指标排行榜
//     *
//     * @param dto
//     * @return
//     */
//    @Override
//    public Page<TargetRankVO> getTargetRank(DataStatisticsQuery dto) {
//        // 获取指标排行榜的所有数据
//        List<TargetRankVO> targetRankList = listAllTargetRank(dto);
//        List<TargetRankVO> collect = targetRankList.stream().skip((long) (dto.getPageNum() - 1) * dto.getPageSize()).limit(dto.getPageSize()).collect(Collectors.toList());
//        if (CollUtil.isEmpty(targetRankList)) {
//            return new Page<>(dto.getPageNum(), dto.getPageSize());
//        }
//        Page<TargetRankVO> resultPages = new Page<>();
//        resultPages.setTotal(targetRankList.size());
//        resultPages.setRecords(collect);
//        resultPages.setCurrent(dto.getPageNum());
//        resultPages.setSize(dto.getPageSize());
//        return resultPages;
//    }
//
//    /**
//     * 导出指标排行榜excel
//     *
//     * @param response
//     * @param dto
//     */
//    @Override
//    @SneakyThrows
//    public void exportTargetRank(HttpServletResponse response, DataStatisticsQuery dto) {
//
//        List<TargetRankVO> targetRankList = listAllTargetRank(dto);
//        List<ExcelTargetRankVO> targetRankVOS = targetConvert.toExcelTargetRankVOList(targetRankList);
//        List<ExcelExportEntity> entitys = new ArrayList<>();
//        entitys.add(new ExcelExportEntity("一级分类", "moduleName", 18));
//        entitys.add(new ExcelExportEntity("分组", "groupName", 18));
//        entitys.add(new ExcelExportEntity("指标", "targetName", 18));
//        entitys.add(new ExcelExportEntity("次数", "count", 18));
//        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), entitys, targetRankVOS);
//        EasyPoiUtil.adjustStyle(workbook.getSheetAt(0));
//        EasyPoiUtil.downLoadExcelWithFormat(getTitle("{0}至{1}日指标点评榜", dto.getStartTime(), dto.getEndTime()), response, workbook);
//
//    }
//
//    /**
//     * 行为记录根据类型(年级/班级/老师)分组
//     *
//     * @param businessMap      saas中的数据
//     * @param behaviourRecords 数据库中的行为记录
//     * @param type             SaasCurrentIdTypeEnum: 1为学校id，2为校区id，3为学段id，4为年级id，5为班级id
//     * @return
//     */
//    private Map<String, List<BehaviourRecordDTO>> getBehaviourGroupByCurrentIdTyp(Map<String, String> businessMap, List<BehaviourRecordDTO> behaviourRecords, Integer type) {
//        // 行为记录中的所有的年级,班级,老师都需要在saas中存在
//
//        Map<String, List<BehaviourRecordDTO>> groupMap = new HashMap<>();
//        // 学段下的年级分组
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
//            groupMap = behaviourRecords.stream().filter(s -> businessMap.containsKey(s.getGradeId())).collect(Collectors.groupingBy(BehaviourRecordDTO::getGradeId));
//        }
//        // 年级下的班级分组
//        if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
//            groupMap = behaviourRecords.stream().filter(s -> businessMap.containsKey(s.getClassId())).collect(Collectors.groupingBy(BehaviourRecordDTO::getClassId));
//        }
//        // 班级下的老师分组
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
//            groupMap = behaviourRecords.stream().filter(s -> TaskRoleTypeEnum.TEACHER.getCode().equals(s.getAppraisalType())).filter(s -> businessMap.containsKey(s.getAppraisalId())).collect(Collectors.groupingBy(BehaviourRecordDTO::getAppraisalId));
//        }
//        return groupMap;
//    }
//
//    /**
//     * 根据(学段/年级/班级)获取对应的(年级/班级/老师)列表
//     *
//     * @param dto 查询条件
//     * @return
//     */
//    private SaasCurrentInfoDTO listBusinessIdByBusinessType(DataStatisticsQuery dto) {
//        SaasCurrentInfoDTO saasCurrentInfoDTO = new SaasCurrentInfoDTO();
//        saasCurrentInfoDTO.setBusinessMap(new HashMap<>());
//        saasCurrentInfoDTO.setSortBusinessKey(new ArrayList<>());
//
//        // 根据(学段/年级/班级)获取对应的(年级/班级/老师)列表
//        Map<String, String> businessMap = new HashMap<>();
//        // 业务类型顺序
//        List<String> sortBusinessKey = new ArrayList<>();
//        // 当前学段/年级/班级信息
//        String currentBusinessName = "";
//
//        List<EduOrgTreeVO> eduOrgTreeVOS = listEduOrgTree(dto.getType(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId());
//        // 如果是学段,查询出学段下有哪些年级;如果是年级,查询出年级下有哪些班级
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(dto.getType()) || SaasCurrentIdTypeEnum.GRADE.getCode().equals(dto.getType())) {
//            if (CollUtil.isNotEmpty(eduOrgTreeVOS)) {
//                EduOrgTreeVO eduOrgTreeVO = CollUtil.getFirst(eduOrgTreeVOS);
//                if (Objects.nonNull(eduOrgTreeVO.getChildren())) {
//                    businessMap = eduOrgTreeVO.getChildren().stream().collect(Collectors.toMap(s -> Convert.toStr(s.getId()), EduOrgTreeVO::getName));
//                    sortBusinessKey = eduOrgTreeVO.getChildren().stream().map(s -> Convert.toStr(s.getId())).collect(Collectors.toList());
//                }
//                currentBusinessName = eduOrgTreeVO.getName();
//            }
//        }
//        // 如果是班级,查询出班级下有哪些老师
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(dto.getType())) {
//            StaffSectionOrGradeQueryDTO staffSectionOrGradeQueryDTO = new StaffSectionOrGradeQueryDTO();
//            staffSectionOrGradeQueryDTO.setCampusSectionId(Convert.toLong(dto.getCampusSectionId()));
//            staffSectionOrGradeQueryDTO.setGradeId(Convert.toLong(dto.getGradeId()));
//            staffSectionOrGradeQueryDTO.setClassId(Convert.toLong(dto.getClassId()));
//            List<StaffFullInfoVO> staffFullInfos = basicInfoRemote.queryByClassId(staffSectionOrGradeQueryDTO);
//
//            businessMap = staffFullInfos.stream().collect(Collectors.toMap(s -> Convert.toStr(s.getStaffId()), StaffFullInfoVO::getName));
//            sortBusinessKey = staffFullInfos.stream().map(s -> Convert.toStr(s.getStaffId())).collect(Collectors.toList());
//            currentBusinessName = eduOrgTreeVOS.get(0).getName();
//        }
//        saasCurrentInfoDTO.setBusinessMap(businessMap);
//        saasCurrentInfoDTO.setSortBusinessKey(sortBusinessKey);
//        saasCurrentInfoDTO.setCurrentBusinessName(currentBusinessName);
//        return saasCurrentInfoDTO;
//    }
//
//
//    /**
//     * 根据业务id获取到对于的名称(年级/班级/老师名称)
//     *
//     * @param behaviourRecords 数据库中的行为记录
//     * @param type             SaasCurrentIdTypeEnum: 1为学校id，2为校区id，3为学段id，4为年级id，5为班级id
//     * @return
//     */
//    @Deprecated
//    private Map<Long, String> getBusinessNameByCurrentIdTyp(List<BehaviourRecordDTO> behaviourRecords, Integer type) {
//        Map<Long, String> businessNameMap = new HashMap<>();
//        // 学段下的年级
//        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
//            List<Long> gradeIds = behaviourRecords.stream().map(s -> Convert.toLong(s.getGradeId())).collect(Collectors.toList());
//            for (Long gradeId : gradeIds) {
//                EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
//                eduOrgQueryDTO.setEndType(SaasCurrentIdTypeEnum.GRADE.getCode());
//                eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.GRADE.getCode());
//                eduOrgQueryDTO.setCurrentId(gradeId);
//                eduOrgQueryDTO.setIsTree(Constant.NO);
//
//                List<EduOrgTreeVO> eduOrgTrees = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
//                if (Objects.isNull(eduOrgTrees)) {
//                    continue;
//                }
//                EduOrgTreeVO gradeInfo = CollUtil.getFirst(eduOrgTrees);
//                businessNameMap.put(gradeInfo.getId(), gradeInfo.getName());
//            }
//        }
//        // 年级下的班级
//        if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
//            List<Long> classIds = behaviourRecords.stream().map(s -> Convert.toLong(s.getClassId())).collect(Collectors.toList());
//            List<EduClassInfoLinkVO> eduClassInfos = basicInfoRemote.listUnderByClassIds(classIds);
//            businessNameMap = eduClassInfos.stream().collect(Collectors.toMap(EduClassInfoLinkVO::getId, EduClassInfoLinkVO::getClassName));
//        }
//        // 班级下的老师
//        if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
//            List<Long> teacherIds = behaviourRecords.stream().filter(s -> TaskRoleTypeEnum.TEACHER.getCode().equals(s.getAppraisalType())).map(s -> Convert.toLong(s.getAppraisalId())).collect(Collectors.toList());
//            if (CollUtil.isEmpty(teacherIds)) {
//                return new HashMap<>();
//            }
//            StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
//            staffBatchQueryDTO.setStaffIdList(teacherIds);
//            staffBatchQueryDTO.setState(Constant.NO);
//            List<StaffBatchVO> staffInfos = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
//            businessNameMap = staffInfos.stream().collect(Collectors.toMap(StaffBatchVO::getId, StaffBatchVO::getName));
//        }
//
//
//        return businessNameMap;
//    }
//
//    /**
//     * 根据行为记录统计每个五育模块的占比
//     *
//     * @param behaviourRecords 行为记录
//     * @param businessId       业务(年级/班级)id
//     * @param commentNumMap    点评次数map
//     * @return
//     */
//    private List<FiveEducationDistributionVO> encapsulationFiveEducation(List<BehaviourRecordDTO> behaviourRecords, String businessId, String businessName, Map<Integer, Long> commentNumMap) {
//        // 总分
//        BigDecimal totalScore = behaviourRecords.stream().map(BehaviourRecordDTO::getScoreValue).reduce(BigDecimal.ZERO, BigDecimal::add);
//        // 如果所有分值相加为0
//        if (ObjectUtil.equals(BigDecimal.ZERO, totalScore)) {
//            log.info("[五育分布]-[计算每个模块占比]-[总分为空]");
//            return new ArrayList<>();
//        }
//
//        // 根据五育分组
//        Map<Integer, List<BehaviourRecordDTO>> groupByModuleMap = behaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecordDTO::getModuleCode));
//
//        int num = 1;
//        // 最后一个模块的占比计算(为了保证所有占比相加为1)
//        BigDecimal lastProportion = new BigDecimal(1);
//
//        // 五育分布明细
//        List<FiveEducationDistributionVO> distributions = new ArrayList<>();
//        Map<Integer, String> moduleMap = ModuleEnum.moduleMap;
//        for (Integer key : moduleMap.keySet()) {
//            List<BehaviourRecordDTO> behaviourRecordDTOList = groupByModuleMap.get(key);
//            FiveEducationDistributionVO fiveEducationDistributionVO = new FiveEducationDistributionVO();
//
//            // 如果某个模块明细数据不存在
//            if (CollUtil.isEmpty(behaviourRecordDTOList)) {
//                fiveEducationDistributionVO.setExtraScore(Convert.toBigDecimal(0));
//                fiveEducationDistributionVO.setSubtractScore(Convert.toBigDecimal(0));
//                if (Objects.nonNull(commentNumMap) && Objects.nonNull(commentNumMap.get(key))) {
//                    fiveEducationDistributionVO.setCommentCount(Convert.toInt(commentNumMap.get(key)));
//                }
//                fiveEducationDistributionVO.setModuleCode(key);
//                fiveEducationDistributionVO.setModuleName(ModuleEnum.getModuleName(key));
//                fiveEducationDistributionVO.setBusinessId(StrUtil.isNotBlank(businessId) ? businessId : null);
//                fiveEducationDistributionVO.setBusinessName(StrUtil.isNotBlank(businessName) ? businessName : null);
//                fiveEducationDistributionVO.setProportion(Convert.toBigDecimal(0));
//                distributions.add(fiveEducationDistributionVO);
//                lastProportion = lastProportion.subtract(Convert.toBigDecimal(0));
//                num++;
//                continue;
//            }
//
//            // 表扬总分值
//            BigDecimal praise = behaviourRecordDTOList.stream().filter(s -> ScoreTypeEnum.PLUS.getCode().equals(s.getScoreType())).map(BehaviourRecordDTO::getScoreValue).reduce(BigDecimal.ZERO, BigDecimal::add);
//            // 待改进总分值
//            BigDecimal needImprove = behaviourRecordDTOList.stream().filter(s -> ScoreTypeEnum.REDUCE.getCode().equals(s.getScoreType())).map(BehaviourRecordDTO::getScoreValue).reduce(BigDecimal.ZERO, BigDecimal::add);
//            // 当前五育占比
//            BigDecimal moduleProportion = praise.add(needImprove).divide(totalScore, 3, RoundingMode.HALF_UP);
//
//
//            fiveEducationDistributionVO.setExtraScore(praise);
//            fiveEducationDistributionVO.setSubtractScore(needImprove);
//            if (Objects.nonNull(commentNumMap) && Objects.nonNull(commentNumMap.get(key))) {
//                fiveEducationDistributionVO.setCommentCount(Convert.toInt(commentNumMap.get(key)));
//            }
//            fiveEducationDistributionVO.setModuleCode(key);
//            fiveEducationDistributionVO.setModuleName(ModuleEnum.getModuleName(key));
//            fiveEducationDistributionVO.setBusinessId(StrUtil.isNotBlank(businessId) ? businessId : null);
//            fiveEducationDistributionVO.setBusinessName(StrUtil.isNotBlank(businessName) ? businessName : null);
//            // 如果相等,说明是最后一个模块
//            if (moduleMap.size() == num) {
//                fiveEducationDistributionVO.setProportion(lastProportion.multiply(Convert.toBigDecimal(100)));
//            } else {
//                fiveEducationDistributionVO.setProportion(moduleProportion.multiply(Convert.toBigDecimal(100)));
//            }
//            distributions.add(fiveEducationDistributionVO);
//
//            lastProportion = lastProportion.subtract(moduleProportion);
//            num++;
//        }
//        return distributions;
//    }
//
//    /**
//     * 获取指标排行榜的所有数据
//     *
//     * @param dto
//     * @return
//     */
//    private List<TargetRankVO> listAllTargetRank(DataStatisticsQuery dto) {
//        //时间段内所有的点评数据
//        List<BehaviourRecordDTO> behaviourRecords = behaviourHandleManager.getRecord(WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getCampusSectionId(), dto.getGradeId(), dto.getClassId(), dto.getStartTime(), dto.getEndTime(),null);
//        //获取校区内所有启用的指标
//        List<TargetDTO> targetDTOS = behaviourHandleManager.listEnableTarget(WebUtil.getSchoolId(), WebUtil.getCampusId());
//        if (CollUtil.isEmpty(targetDTOS)) {
//            return Collections.emptyList();
//        }
//        List<Long> targetIds = targetDTOS.stream().map(TargetDTO::getId).distinct().collect(Collectors.toList());
//        Map<Long, TargetDTO> targetInfoMap = targetDTOS.stream().collect(Collectors.toMap(TargetDTO::getId, Function.identity()));
//
//        // 根据指标分组(需要过滤出启用且未删除得指标)
//        Map<Long, Long> targetNumMap = behaviourRecords.stream().filter(s -> targetIds.contains(s.getTargetId())).collect(Collectors.groupingBy(BehaviourRecordDTO::getTargetId, Collectors.counting()));
//
//        // 过滤出体测数据
//        List<BehaviourRecordDTO> sportDataList = behaviourRecords.stream().filter(s -> DataSourceEnum.SPORT.getCode().equals(s.getDataSource())).collect(Collectors.toList());
//        // 数据封装
//        List<TargetRankVO> targetRankList = new ArrayList<>();
//        for (Long targetId : targetIds) {
//            if (!targetInfoMap.containsKey(targetId)) {
//                continue;
//            }
//            TargetDTO targetDTO = targetInfoMap.get(targetId);
//            TargetRankVO targetRank = new TargetRankVO();
//            targetRank.setTargetId(Convert.toStr(targetId));
//            targetRank.setTargetName(Objects.nonNull(targetInfoMap.get(targetId)) ? targetInfoMap.get(targetId).getTargetName() : "");
//            targetRank.setModuleCode(targetInfoMap.get(targetId).getModuleCode());
//            targetRank.setModuleName(ModuleEnum.getModuleName(targetRank.getModuleCode()));
//            targetRank.setGroupName(targetInfoMap.get(targetId).getGroupName());
//            targetRank.setCreateTime(targetDTO.getCreateTime());
//            // 如果是体测指标且存在体测数据则次数为1(一学期使用次数只有1次或者0次)
//            if (DataSourceEnum.SPORT.getCode().equals(targetDTO.getDataSource())) {
//                targetRank.setCount(CollUtil.isNotEmpty(sportDataList) ? Constant.ONE : Constant.NO);
//            } else {
//                targetRank.setCount(Convert.toInt(targetNumMap.getOrDefault(targetId, 0L)));
//            }
//            targetRankList.add(targetRank);
//        }
//
//        return targetRankList.stream().sorted(Comparator
//                .comparing(TargetRankVO::getCount, Comparator.nullsFirst(Integer::compareTo)).reversed()
//                .thenComparing(TargetRankVO::getCreateTime, Comparator.nullsFirst(Date::compareTo)).reversed().reversed()).collect(Collectors.toList());
//    }
//
//    /**
//     * 获取统计报表标题
//     *
//     * @param title     待解析标题
//     * @param startTime 开始时间
//     * @param endTime   结束时间
//     * @return 报表标题
//     */
//    private String getTitle(String title, Date startTime, Date endTime) {
//        String start = DateUtil.format(startTime, Constant.CHINESE_FORMAT);
//        String end = DateUtil.format(endTime, Constant.CHINESE_FORMAT);
//        return MessageFormat.format(title, start, end);
//    }

    /**
     * 从saas获取该学校下学段及以下的教务组织架构树形
     *
     * @param type
     * @param campusSectionId
     * @param gradeId
     * @param classId
     * @return
     */
    private List<EduOrgTreeVO> listEduOrgTree(Integer type, String campusSectionId, String gradeId, String classId) {
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
            eduOrgQueryDTO.setCurrentId(Convert.toLong(campusSectionId));
        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
            eduOrgQueryDTO.setCurrentId(Convert.toLong(gradeId));
        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
            eduOrgQueryDTO.setCurrentId(Convert.toLong(classId));
        }
        eduOrgQueryDTO.setCurrentIdType(type);
        //从saas获取数据
        List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        return eduOrgTreeVOS;
    }

    /**
     * 获取所有年级各个老师的点评次数和人数
     *
     * @param staffFullInfoVOS
     * @param startTime
     * @param endTime
     * @param behaviourRecordDTOList
     */
    private ArrayList<TeacherVO> listTeachers(String schoolId, String campusId, ArrayList<StaffFullInfoVO> staffFullInfoVOS, Date startTime, Date endTime, List<BehaviourRecordDTO> behaviourRecordDTOList) {
        ArrayList<TeacherVO> teacherVOS = new ArrayList<>();
        //所有年级
        List<GradeFullInfoVO> gradeFullInfoVOS = staffFullInfoVOS.stream().filter(s -> CollectionUtil.isNotEmpty(s.getGradeList())).map(StaffFullInfoVO::getGradeList).distinct().collect(Collectors.toList()).stream().flatMap(Collection::stream).collect(Collectors.toList());
        Map<Long, String> gradeIdAndNameMap = gradeFullInfoVOS.stream().collect(Collectors.toMap(GradeFullInfoVO::getGradeId, GradeFullInfoVO::getGradeName, (k1, k2) -> k1, LinkedHashMap::new));
        Set<Long> gradeIdLongs = gradeIdAndNameMap.keySet();
        Iterator<Map.Entry<Long, String>> iterator = gradeIdAndNameMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, String> next = iterator.next();
            List<StaffFullInfoVO> staffFullInfoVOList = staffFullInfoVOS.stream().filter(s -> CollectionUtil.isNotEmpty(s.getGradeList())).collect(Collectors.toList());
            List<StaffFullInfoVO> fullInfoVOList = staffFullInfoVOList.stream().filter(s -> s.getGradeList().stream().map(GradeFullInfoVO::getGradeId).collect(Collectors.toList()).contains(next.getKey())).collect(Collectors.toList());
            //该年级指标正常且配有点评项的老师ids
            List<String> listTargetConfigTeacherIds = behaviourHandleManager.listTargetConfigTeacherIds(schoolId, campusId, fullInfoVOList.get(Constant.ZERO).getCampusSectionId(), Convert.toStr(next.getKey()), null, startTime, endTime);
            if (CollectionUtil.isEmpty(listTargetConfigTeacherIds)) {
                continue;
            }
            StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
            staffBatchQueryDTO.setState(Constant.ZERO);
            staffBatchQueryDTO.setStaffIdList(Convert.toList(Long.class, listTargetConfigTeacherIds));
            //需点评老师
            List<StaffBatchVO> staffBatchVOList = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
            if (CollectionUtil.isEmpty(staffBatchVOList)) {
                continue;
            }
            Long key = next.getKey();
            for (StaffBatchVO staffBatchVO : staffBatchVOList) {
                TeacherVO teacherVO = new TeacherVO();
                List<BehaviourRecordDTO> behaviourRecordDTOS = behaviourRecordDTOList.stream().filter(s -> Convert.toStr(staffBatchVO.getId()).equals(s.getAppraisalId()) && key.equals(Convert.toLong(s.getGradeId()))).collect(Collectors.toList());
                teacherVO.setEvaluateNum(behaviourRecordDTOS.size());
                int size = behaviourRecordDTOList.stream().filter(s -> Convert.toStr(staffBatchVO.getId()).equals(s.getAppraisalId()) && gradeIdLongs.contains(Convert.toLong(s.getGradeId()))).collect(Collectors.groupingBy(BehaviourRecordDTO::getStudentId)).size();
                teacherVO.setDayNum(size);
                teacherVO.setGradeOrClass(Convert.toStr(next.getKey()));
                teacherVO.setTeacherId(Convert.toStr(staffBatchVO.getId()));
                teacherVOS.add(teacherVO);
            }
        }
        return teacherVOS;
    }

    private ArrayList<TeacherVO> listTeachersV2(String schoolId, String campusId, List<StaffFullInfoVO> staffFullInfoVOS, Date startTime, Date endTime, List<BehaviourRecordDTO> behaviourRecordDTOList) {
        ArrayList<TeacherVO> teacherVOS = new ArrayList<>();
        //所有年级
        List<GradeFullInfoVO> gradeFullInfoVOS = staffFullInfoVOS.stream().filter(s -> CollectionUtil.isNotEmpty(s.getGradeList())).map(StaffFullInfoVO::getGradeList).distinct().collect(Collectors.toList()).stream().flatMap(Collection::stream).collect(Collectors.toList());
        Map<Long, String> gradeIdAndNameMap = gradeFullInfoVOS.stream().collect(Collectors.toMap(GradeFullInfoVO::getGradeId, GradeFullInfoVO::getGradeName, (k1, k2) -> k1, LinkedHashMap::new));
        Set<Long> gradeIdLongs = gradeIdAndNameMap.keySet();
        Iterator<Map.Entry<Long, String>> iterator = gradeIdAndNameMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, String> next = iterator.next();
            List<StaffFullInfoVO> staffFullInfoVOList = staffFullInfoVOS.stream().filter(s -> CollectionUtil.isNotEmpty(s.getGradeList())).collect(Collectors.toList());
            List<StaffFullInfoVO> fullInfoVOList = staffFullInfoVOList.stream().filter(s -> s.getGradeList().stream().map(GradeFullInfoVO::getGradeId).collect(Collectors.toList()).contains(next.getKey())).collect(Collectors.toList());
            //该年级指标正常且配有点评项的老师ids
            List<String> listTargetConfigTeacherIds = behaviourHandleManager.listTargetConfigTeacherIdsV2(schoolId, campusId, fullInfoVOList.get(Constant.ZERO).getCampusSectionId(), Convert.toStr(next.getKey()), null);
            if (CollectionUtil.isEmpty(listTargetConfigTeacherIds)) {
                continue;
            }
            StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
            staffBatchQueryDTO.setState(Constant.ZERO);
            staffBatchQueryDTO.setStaffIdList(Convert.toList(Long.class, listTargetConfigTeacherIds));
            //需点评老师
            List<StaffBatchVO> staffBatchVOList = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
            if (CollectionUtil.isEmpty(staffBatchVOList)) {
                continue;
            }
            Long key = next.getKey();
            for (StaffBatchVO staffBatchVO : staffBatchVOList) {
                TeacherVO teacherVO = new TeacherVO();
                List<BehaviourRecordDTO> behaviourRecordDTOS = behaviourRecordDTOList.stream().filter(s -> Convert.toStr(staffBatchVO.getId()).equals(s.getAppraisalId()) && key.equals(Convert.toLong(s.getGradeId()))).collect(Collectors.toList());
                teacherVO.setEvaluateNum(behaviourRecordDTOS.size());
                int size = behaviourRecordDTOList.stream().filter(s -> Convert.toStr(staffBatchVO.getId()).equals(s.getAppraisalId()) && gradeIdLongs.contains(Convert.toLong(s.getGradeId()))).collect(Collectors.groupingBy(BehaviourRecordDTO::getStudentId)).size();
                teacherVO.setDayNum(size);
                teacherVO.setGradeOrClass(Convert.toStr(next.getKey()));
                teacherVO.setTeacherId(Convert.toStr(staffBatchVO.getId()));
                teacherVOS.add(teacherVO);
            }
        }
        return teacherVOS;
    }



    @Override
    public DataStatisticsStudentEvaluateRateVO getStudentEvaluateStatistics(DataStatisticsQuery query) {
        // 时间段内所有的点评数据
        DataStatisticsHandler service;
        if (Objects.nonNull(query.getIsCurrentYear()) && Boolean.FALSE.equals(query.getIsCurrentYear())) {
            service = dataStatisticsFactory.getService(EvaluateBizTypeEnum.HISTORY);
        } else {
            service = dataStatisticsFactory.getService(EvaluateBizTypeEnum.CURRENT);
        }
        return service.getStudentEvaluateRate(query);
    }

    @Override
    public List<StaffFullInfoVO> listTeacherInfoV2(String schoolId, String campusId, String tenantId) {
        if (CharSequenceUtil.isBlank(campusId) || CharSequenceUtil.isBlank(schoolId)) {
            return Collections.emptyList();
        }

        // 获取上周的起始时间（周一0点）和 上周的结束时间（周日23:59:59）
        Date startTime = DateUtil.beginOfWeek(DateUtil.offsetWeek(DateUtil.date(), -1));
        Date endTime = DateUtil.endOfWeek(DateUtil.offsetWeek(DateUtil.date(), -1));

        ArrayList<StaffFullInfoVO> staffFullInfoVOArrayList = new ArrayList<>();

        //获取校区下的学段
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(Convert.toLong(schoolId));
        termQuery.setCampusId(Convert.toLong(campusId));
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        List<TermVo> termVoList = termVos.stream().filter(TermVo::isCurrentTerm).collect(Collectors.toList());
        if (CollUtil.isEmpty(termVos) || CollUtil.isEmpty(termVoList)) {
            return staffFullInfoVOArrayList;
        }

        for (TermVo termVo : termVoList) {
            List<StaffFullInfoVO> fullInfoVOArrayList = new ArrayList<>();
            if (startTime.before(DateUtil.parse(termVo.getStartTime())) || endTime.after(DateUtil.parse(termVo.getEndTime()))) {
                continue;
            }
            //该学段指标正常且配有点评项的老师ids
            List<String> listTargetConfigTeacherIds = behaviourHandleManager.listTargetConfigTeacherIdsV2(schoolId, campusId, Convert.toStr(termVo.getCampusSectionId()), null, null);
            if (CollUtil.isEmpty(listTargetConfigTeacherIds)) {
                continue;
            }
            List<Long> staffIds = listTargetConfigTeacherIds.stream().map(Convert::toLong).collect(Collectors.toList());
            //从saas获取该学段老师信息
            StaffSectionOrGradeQueryDTO staffSectionOrGradeQueryDTO = new StaffSectionOrGradeQueryDTO();
            staffSectionOrGradeQueryDTO.setCampusSectionId(termVo.getCampusSectionId());
            List<StaffFullInfoVO> staffFullInfoVOS = basicInfoRemote.queryByCampusSectionId(staffSectionOrGradeQueryDTO);

            List<StaffFullInfoVO> fullInfoVOList = this.filterTeacher(fullInfoVOArrayList, staffFullInfoVOS, staffIds);

            /**----------------------------------------------------------------班主任-----------------------------------------------------------------------------------*/
            this.addClassMaster(fullInfoVOList,fullInfoVOArrayList,staffFullInfoVOS);
            /**----------------------------------------------------------------年级组长-----------------------------------------------------------------------------------*/
            this.addGradeMaster(fullInfoVOList,fullInfoVOArrayList,staffFullInfoVOS);
            /**----------------------------------------------------------------学生处主任-----------------------------------------------------------------------------------*/
            this.addStudentMaster(schoolId, fullInfoVOArrayList);

            this.buildTeacherInfo(fullInfoVOArrayList, staffFullInfoVOArrayList, campusId, schoolId, startTime, endTime, termVo);
        }
        return staffFullInfoVOArrayList;
    }

    private List<StaffFullInfoVO> filterTeacher(List<StaffFullInfoVO> fullInfoVOArrayList, List<StaffFullInfoVO>  staffFullInfoVOS, List<Long> staffIds){
        if (CollUtil.isEmpty(staffFullInfoVOS)) {
            return Collections.emptyList();
        }
        //筛选出老师能关联到班级和年级
        List<StaffFullInfoVO> staffFullInfoVOList = staffFullInfoVOS.stream().filter(s -> CollUtil.isNotEmpty(s.getClassList()) && CollUtil.isNotEmpty(s.getGradeList())).collect(Collectors.toList());
        if (CollUtil.isEmpty(staffFullInfoVOList)) {
            return Collections.emptyList();
        }
        /**----------------------------------------------------------------点评项老师-----------------------------------------------------------------------------------*/
        //需发送通知的老师
        List<StaffFullInfoVO> fullInfoVOList = staffFullInfoVOList.stream().filter(s -> staffIds.contains(s.getStaffId())).collect(Collectors.toList());
        for (StaffFullInfoVO staffFullInfoVO : fullInfoVOList) {
            staffFullInfoVO.setTeacherRoleType(TeacherRoleEnum.NORMAL_TEACHER.getCode());
            fullInfoVOArrayList.add(staffFullInfoVO);
        }
        return fullInfoVOList;
    }
    
    private void addClassMaster(List<StaffFullInfoVO> fullInfoVOList,List<StaffFullInfoVO> fullInfoVOArrayList, List<StaffFullInfoVO> staffFullInfoVOS){
        //按班级分组
        List<List<ClassFullInfoVO>> classList = fullInfoVOList.stream().map(StaffFullInfoVO::getClassList).collect(Collectors.toList());
        //班级去重
        List<ClassFullInfoVO> classFullInfoVOS = classList.stream().flatMap(List::stream).distinct().collect(Collectors.toList());
        //班主任和其所在班级
        for (ClassFullInfoVO classFullInfoVO : classFullInfoVOS) {
            //根据班级ID查询班主任信息 saas
            TeacherInfoQuery teacherInfoQuery = new TeacherInfoQuery();
            teacherInfoQuery.setClassId(classFullInfoVO.getClassId());
            List<ClassStaffVO> classStaffVOS = basicInfoRemote.queryTeachersByClassId(teacherInfoQuery);
            //班主任staffId
            List<Long> classTeacherId = classStaffVOS.stream().filter(s -> s.getTeacherType().contains(Constant.ZERO.toString())).map(ClassStaffVO::getStaffId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(classTeacherId)) {
                continue;
            }
            //需发送通知的班主任
            List<StaffFullInfoVO> classTeacherInfoVOList = staffFullInfoVOS.stream().filter(s -> classTeacherId.get(0).equals(s.getStaffId())).collect(Collectors.toList());
            StaffFullInfoVO staffFullInfoVO1 = new StaffFullInfoVO();
            BeanUtil.copyProperties(classTeacherInfoVOList.get(0), staffFullInfoVO1);
            staffFullInfoVO1.setTeacherRoleType(TeacherRoleEnum.CLASS_TEACHER.getCode());
            staffFullInfoVO1.setClassOrGrade(classFullInfoVO.getClassId());
            fullInfoVOArrayList.add(staffFullInfoVO1);
        }
    }

    private void addGradeMaster(List<StaffFullInfoVO> fullInfoVOList,List<StaffFullInfoVO> fullInfoVOArrayList, List<StaffFullInfoVO> staffFullInfoVOS){
        //按年级分组
        List<List<GradeFullInfoVO>> gradeList = fullInfoVOList.stream().map(StaffFullInfoVO::getGradeList).collect(Collectors.toList());
        //年级去重
        List<GradeFullInfoVO> gradeFullInfoVOS = gradeList.stream().flatMap(List::stream).distinct().collect(Collectors.toList());
        for (GradeFullInfoVO gradeTeacherId : gradeFullInfoVOS) {
            //根据年级ID查询年级组长 saas
            GradeByIdQuery gradeByIdQuery = new GradeByIdQuery();
            gradeByIdQuery.setGradeId(gradeTeacherId.getGradeId());
            List<GradeStaffVO> gradeStaffVOS = basicInfoRemote.queryStaffByGradeId(gradeByIdQuery);
            List<Long> gradeTeacherIdList = gradeStaffVOS.stream().map(GradeStaffVO::getStaffId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(gradeTeacherIdList)) {
                continue;
            }
            //需发送通知的年级组长
            List<StaffFullInfoVO> gradeTeacherInfoVOList = staffFullInfoVOS.stream().filter(s -> gradeTeacherIdList.get(0).equals(s.getStaffId())).collect(Collectors.toList());

            StaffFullInfoVO staffFullInfoVO1 = new StaffFullInfoVO();
            BeanUtil.copyProperties(gradeTeacherInfoVOList.get(0), staffFullInfoVO1);
            staffFullInfoVO1.setTeacherRoleType(TeacherRoleEnum.GRADE_TEACHER.getCode());
            staffFullInfoVO1.setClassOrGrade(gradeTeacherId.getGradeId());
            fullInfoVOArrayList.add(staffFullInfoVO1);

        }
    }

    private void addStudentMaster(String schoolId, List<StaffFullInfoVO> fullInfoVOArrayList){
        //根据学校id获取学生处主任
        SchoolQueryDTO dto = new SchoolQueryDTO();
        dto.setSchoolId(Convert.toLong(schoolId));
        dto.setRoleCodes(Collections.singletonList(Constant.MANAGER_ROLE_CODE));
        List<UcStaffInfoRoleSchoolVO> ucStaffInfoRoleSchoolVOS = basicInfoRemote.queryStaffListBySchoolId(dto);

        for (UcStaffInfoRoleSchoolVO ucStaffInfoRoleSchoolVO : ucStaffInfoRoleSchoolVOS) {
            StaffFullInfoVO staffFullInfoVO1 = new StaffFullInfoVO();
            staffFullInfoVO1.setStaffId(ucStaffInfoRoleSchoolVO.getStaffId());
            staffFullInfoVO1.setName(ucStaffInfoRoleSchoolVO.getStaffName());
            staffFullInfoVO1.setMobile(ucStaffInfoRoleSchoolVO.getMobile());
            staffFullInfoVO1.setUserId(ucStaffInfoRoleSchoolVO.getUserId());
            staffFullInfoVO1.setSchoolId(Convert.toStr(ucStaffInfoRoleSchoolVO.getSchoolId()));
            staffFullInfoVO1.setTeacherRoleType(TeacherRoleEnum.SCHOOL_TEACHER.getCode());
            fullInfoVOArrayList.add(staffFullInfoVO1);
        }
    }

    private void buildTeacherInfo(List<StaffFullInfoVO> fullInfoVOArrayList,
                                  List<StaffFullInfoVO> staffFullInfoVOArrayList,
                                  String campusId,
                                  String schoolId,
                                  Date startTime,
                                  Date endTime,
                                  TermVo termVo){
        //添加各学段的待发送教职工到列表中
        for (StaffFullInfoVO staffFullInfoVO : fullInfoVOArrayList) {
            StaffFullInfoVO staffFullInfoVO1 = new StaffFullInfoVO();
            BeanUtil.copyProperties(staffFullInfoVO, staffFullInfoVO1);
            staffFullInfoVO1.setCampusSectionId(Convert.toStr(termVo.getCampusSectionId()));
            staffFullInfoVO1.setCampusId(campusId);
            staffFullInfoVO1.setSchoolId(schoolId);
            staffFullInfoVO1.setStartTime(startTime);
            staffFullInfoVO1.setEndTime(endTime);
            staffFullInfoVOArrayList.add(staffFullInfoVO1);
        }
    }

    private void buildNormalTeacherMsg(StaffFullInfoVO staffFullInfoVO,
                                       ArrayList<TeacherVO> teacherVOS,
                                       String schoolId,
                                       String campusId,
                                       Date startTime,
                                       Date endTime,
                                       Integer msgType,
                                       String tenantId,
                                       List<BehaviourRecordDTO> behaviourRecordDTOList){
        List<TeacherVO> teacherVOList = teacherVOS.stream().filter(s -> s.getTeacherId().equals(Convert.toStr(staffFullInfoVO.getStaffId()))).collect(Collectors.toList());
        //点评次数
        int evaluateNum = CollUtil.isEmpty(teacherVOList) ? 0 : teacherVOList.stream().mapToInt(TeacherVO::getEvaluateNum).sum();
        //点评人数
        int evaluatePeopleNum = CollUtil.isEmpty(teacherVOList) ? 0 : teacherVOList.get(0).getDayNum();
        int teacherNum;
        ArrayList<String> teacherRanks = new ArrayList<>();
        String firstTeacherRanks = "";
        //该老师拥有的年级
        Map<Long, String> gradeIds = staffFullInfoVO.getGradeList().stream().collect(Collectors.toMap(GradeFullInfoVO::getGradeId, GradeFullInfoVO::getGradeName));
        //排名
        for (Map.Entry<Long, String> entry : gradeIds.entrySet()) {
            //该年级的所有老师
            List<TeacherVO> gradeTeachers = teacherVOS.stream().filter(s -> s.getGradeOrClass().equals(Convert.toStr(entry.getKey()))).collect(Collectors.toList());
            teacherNum = gradeTeachers.size();
            gradeTeachers = gradeTeachers.stream().sorted(Comparator.comparing(TeacherVO::getEvaluateNum).reversed().thenComparing(TeacherVO::getTeacherId)).collect(Collectors.toList());
            int indexOf = CollUtil.indexOf(gradeTeachers, s -> s.getTeacherId().equals(Convert.toStr(staffFullInfoVO.getStaffId())));
            Integer num = CollUtil.isEmpty(gradeTeachers) || Constant.NEGATIVE_ONE.equals(indexOf) ? 0 : gradeTeachers.get(indexOf).getEvaluateNum();
            int index = Constant.ZERO.equals(num) ? teacherNum : indexOf + Constant.ONE;
            String teacherRank = MessageFormat.format(NORMAL_TEACHER_DINGDING_TEACHER_INFORM, entry.getValue(), teacherNum, index);
            teacherRanks.add(teacherRank);
            //微信显示的排名用第一个
            firstTeacherRanks = firstTeacherRanks.isEmpty() ? Integer.toString(index) : firstTeacherRanks;
        }
        ArrayList<String> studentNameList = new ArrayList<>();
        //该老师所有的班级
        Map<Long, String> classIdsMap = staffFullInfoVO.getClassList().stream().collect(Collectors.toMap(ClassFullInfoVO::getClassId, ClassFullInfoVO::getClassName));
//        List<String> classIds = staffFullInfoVO.getClassList().stream().map(ClassFullInfoVO::getClassId).map(Convert::toStr).collect(Collectors.toList());
//        List<BehaviourRecordDTO> behaviourRecordDTOS = behaviourHandleManager.listStudentIdsV2(schoolId, campusId, null, null, classIds, startTime, endTime);

        Map<String, List<BehaviourRecordDTO>> classToStudentMap = behaviourRecordDTOList
                .stream()
                .collect(Collectors.groupingBy(BehaviourRecordDTO::getClassId));

        for (Map.Entry<Long, String> entry : classIdsMap.entrySet()) {
            //从saas获取该班级所有正常状态学生
            List<UcStudentClassBffVO> ucStudentClassBffS = this.listUcStudentClassBffVO(Convert.toLong(schoolId), entry.getKey());
            List<Long> studentLongId = ucStudentClassBffS.stream().map(UcStudentClassBffVO::getStudentId).collect(Collectors.toList());
            List<String> studentIdList = Convert.toList(String.class, studentLongId);

            //查询该班级点评过的学生id集合
            List<BehaviourRecordDTO> recordDTOS = classToStudentMap.get(Convert.toStr(entry.getKey()));
            if(CollUtil.isEmpty(recordDTOS)){
                continue;
            }
            List<String> studentIds = recordDTOS.stream().map(BehaviourRecordDTO::getStudentId).distinct().collect(Collectors.toList());

            //未点评过的学生
            List<String> noEvaluateStudents = (List<String>) CollUtil.subtract(studentIdList, studentIds);
            if (CollUtil.isNotEmpty(noEvaluateStudents)) {
                List<Long> noEvaluateStudentLong = Convert.toList(Long.class, noEvaluateStudents);
                //未被点评过的学生信息
                List<UcStudentClassBffVO> ucStudentClassBffVOS = ucStudentClassBffS.stream().filter(s -> noEvaluateStudentLong.contains(s.getStudentId())).collect(Collectors.toList());
                List<String> studentNames = ucStudentClassBffVOS.stream().map(UcStudentClassBffVO::getStudentName).collect(Collectors.toList());
                if (Constant.FIVE < studentNames.size()) {
                    studentNames = studentNames.subList(Constant.ZERO, Constant.FIVE);
                    String join = String.join(Constant.CAESURA_SIGN, studentNames);
                    studentNameList.add(entry.getValue() + ": " + join + "..." + "\n");
                } else {
                    studentNameList.add(entry.getValue() + ": " + String.join(Constant.CAESURA_SIGN, studentNames) + "\n");
                }
            }
        }
        if (MsgTypeEnum.WECHAT.getCode().equals(msgType)) {
            staffFullInfoVO.setInform(MessageFormat.format(NORMAL_TEACHER_WECHAT_INFORM, evaluateNum, evaluatePeopleNum, firstTeacherRanks));
        } else if (CollUtil.isEmpty(studentNameList)) {
            staffFullInfoVO.setInform(MessageFormat.format(NORMAL_TEACHER_ALL_DINGDING_INFORM, evaluateNum, evaluatePeopleNum, String.join(Constant.CHINESE_COMMA, teacherRanks)));
        } else {
            staffFullInfoVO.setInform(MessageFormat.format(NORMAL_TEACHER_DINGDING_INFORM, evaluateNum, evaluatePeopleNum, String.join(Constant.CHINESE_COMMA, teacherRanks), String.join("", studentNameList)));
        }
        staffFullInfoVO.setTitle(EVALUATE_STATISTICS_INFORM);
        this.buildMsgUrl(tenantId, schoolId, campusId, staffFullInfoVO, startTime, endTime, null);
    }


    private void buildClassTeacherMsg(StaffFullInfoVO staffFullInfoVO,
                                      List<BehaviourRecordDTO> behaviourRecordDTOList,
                                      String schoolId,
                                      String campusId,
                                      Date startTime,
                                      Date endTime,
                                      Integer msgType,
                                      String tenantId){
        //班主任老师所在的班级
        Long classId = staffFullInfoVO.getClassOrGrade();
        StaffSectionOrGradeQueryDTO queryDTO = new StaffSectionOrGradeQueryDTO();
        queryDTO.setClassId(classId);
        //该班级所有老师的信息
        List<StaffFullInfoVO> staffFullInfoVOList = basicInfoRemote.queryByClassId(queryDTO);
        if(CollUtil.isEmpty(staffFullInfoVOList)){
            log.info("[{}]班级下没有老师",classId);
            return;
        }
        Integer totalNum = 0;
        Integer totalPeopleNum = 0;
        //各老师点评次数
        ArrayList<String> teacherEvaluateNum = new ArrayList<>();
        //被点评的学生
        ArrayList<String> studentIdList = new ArrayList<>();
        for (StaffFullInfoVO fullInfoVO : staffFullInfoVOList) {
            //点评次数
            int evaluateNum = behaviourRecordDTOList.stream().filter(s -> Convert.toStr(fullInfoVO.getStaffId()).equals(s.getAppraisalId()) && Convert.toStr(classId).equals(s.getClassId())).collect(Collectors.toList()).size();
            //点评人数
            List<BehaviourRecordDTO> recordDTOS = behaviourRecordDTOList.stream().filter(s -> Convert.toStr(fullInfoVO.getStaffId()).equals(s.getAppraisalId()) && Convert.toStr(classId).equals(s.getClassId())).collect(Collectors.toList());
            List<String> studentIds = recordDTOS.stream().map(BehaviourRecordDTO::getStudentId).distinct().collect(Collectors.toList());
            totalNum += evaluateNum;
            teacherEvaluateNum.add(fullInfoVO.getName() + evaluateNum + "次");
            studentIdList.addAll(studentIds);
        }
        totalPeopleNum = studentIdList.stream().distinct().collect(Collectors.toList()).size();
        String studentNameList = null;
        //从saas获取该班级所有正常状态学生
        List<UcStudentClassBffVO> ucStudentClassBffS = listUcStudentClassBffVO(Convert.toLong(schoolId), classId);
        List<Long> studentLongId = ucStudentClassBffS.stream().map(UcStudentClassBffVO::getStudentId).collect(Collectors.toList());
        List<String> studentIdsList = Convert.toList(String.class, studentLongId);
        //该班级查询点评过的学生id集合
        List<String> studentIds = behaviourRecordDTOList
                .stream()
                .filter(item -> Objects.equals(item.getClassId(), Convert.toStr(classId)))
                .map(BehaviourRecordDTO::getStudentId)
                .distinct()
                .collect(Collectors.toList());
//        List<String> studentIds = behaviourHandleManager.listStudentIds(schoolId, campusId, null, null, Convert.toStr(classId), startTime, endTime);
        //未点评过的学生
        List<String> noEvaluateStudents = (List<String>) CollUtil.subtract(studentIdsList, studentIds);
        if (CollUtil.isNotEmpty(noEvaluateStudents)) {
            List<Long> noEvaluateStudentLong = Convert.toList(Long.class, noEvaluateStudents);
            //未被点评过的学生信息
            List<UcStudentClassBffVO> ucStudentClassBffVOS = ucStudentClassBffS.stream().filter(s -> noEvaluateStudentLong.contains(s.getStudentId())).collect(Collectors.toList());
            List<String> studentNames = ucStudentClassBffVOS.stream().map(UcStudentClassBffVO::getStudentName).collect(Collectors.toList());
            if (Constant.FIVE < studentNames.size()) {
                studentNames = studentNames.subList(Constant.ZERO, Constant.FIVE);
                String join = String.join(Constant.CAESURA_SIGN, studentNames);
                studentNameList = join + "...";
            } else {
                studentNameList = String.join(Constant.CAESURA_SIGN, studentNames);
            }
        }

        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(classId);
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.CLASS.getCode());
        //从saas获取数据
        List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        if (CollUtil.isEmpty(eduOrgTreeVOS)) {
            staffFullInfoVO.setSendFlag(Constant.ONE);
            return;
        }
        //年级id
        Long parentId = eduOrgTreeVOS.get(Constant.ZERO).getParentId();
        eduOrgQueryDTO.setCurrentId(parentId);
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.GRADE.getCode());
        //从saas获取该年级所有班级
        List<EduOrgTreeVO> eduOrgTreeVOList = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        if(CollUtil.isEmpty(eduOrgTreeVOList)){
            log.info("没有该年级，年级 id：[{}]",parentId);
            return;
        }
        List<EduOrgTreeVO> children = eduOrgTreeVOList.get(0).getChildren();
        if(CollUtil.isEmpty(eduOrgTreeVOList)){
            log.info("该年级下没有班级，年级 id：[{}]",parentId);
            return;
        }
        this.buildClassTeacherInfo(staffFullInfoVO, children, behaviourRecordDTOList, classId, staffFullInfoVOList,
                msgType, totalNum, totalPeopleNum, noEvaluateStudents, eduOrgTreeVOList, ucStudentClassBffS, teacherEvaluateNum, studentNameList);
        this.buildMsgUrl(tenantId, schoolId, campusId, staffFullInfoVO, startTime, endTime, parentId);
    }

    private void buildGradeTeacherMsg(StaffFullInfoVO staffFullInfoVO,
                                      List<BehaviourRecordDTO> behaviourRecordDTOList,
                                      String schoolId,
                                      String campusId,
                                      Date startTime,
                                      Date endTime,
                                      Integer msgType,
                                      String tenantId){
        //该年级信息
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(staffFullInfoVO.getClassOrGrade());
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.GRADE.getCode());
        List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        if(CollUtil.isEmpty(eduOrgTreeVOS)){
            log.warn("【{根据老师获取老师需要发送的通知内容}】没有该年级，入参：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
            return;
        }
        //年级下所有班级
        List<EduOrgTreeVO> children = eduOrgTreeVOS.get(0).getChildren();
        if(CollUtil.isEmpty(children)){
            log.warn("【{根据老师获取老师需要发送的通知内容}】该年级下没有班级，入参：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
            return;
        }
        //学段id
        Long parentId = eduOrgTreeVOS.get(0).getParentId();

        eduOrgQueryDTO.setCurrentId(parentId);
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
        List<EduOrgTreeVO> orgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        if(CollUtil.isEmpty(orgTreeVOS)){
            log.warn("【{根据老师获取老师需要发送的通知内容}】没有该学段，入参：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
            return;
        }
        if(CollUtil.isEmpty(orgTreeVOS.get(0).getChildren())){
            log.warn("【{根据老师获取老师需要发送的通知内容}】该学段下没有年级，学段id：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
            return;
        }
        //该学段指标正常且配有点评项的老师ids
        List<String> listTargetConfigTeacherIds = behaviourHandleManager.listTargetConfigTeacherIds(schoolId, campusId, Convert.toStr(parentId), null, null, startTime, endTime);
        this.buildGradeTeacherInfo(staffFullInfoVO, behaviourRecordDTOList, children, orgTreeVOS, listTargetConfigTeacherIds, eduOrgTreeVOS, msgType);
        this.buildMsgUrl(tenantId, schoolId, campusId, staffFullInfoVO, startTime, endTime, null);
    }

    private void buildSchoolTeacherMsg(StaffFullInfoVO staffFullInfoVO,
                                       String schoolId,
                                       String campusId,
                                       Date startTime,
                                       Date endTime,
                                       Integer msgType,
                                       String tenantId,
                                       List<BehaviourRecordDTO> behaviourRecordDTOList){
        //学段
        String campusSectionId = staffFullInfoVO.getCampusSectionId();
        //该学校信息
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(campusSectionId));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
        List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        if(CollUtil.isEmpty(eduOrgTreeVOS)){
            log.warn("【{根据老师获取老师需要发送的通知内容}】没有该学段，入参：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
            return;
        }
        if(CollUtil.isEmpty(eduOrgTreeVOS.get(0).getChildren())){
            log.warn("【{根据老师获取老师需要发送的通知内容}】该学段下没有年级，入参：[{}]", JSONUtil.toJsonStr(eduOrgQueryDTO));
            return;
        }
        //学校名称
        String schoolName = eduOrgTreeVOS.get(0).getName();
        //该学段下所有年级
        List<EduOrgTreeVO> gradeOrgTreeVOS = eduOrgTreeVOS.get(0).getChildren();
        //该学段所有点评记录
//        List<BehaviourRecordDTO> recordDTOS = behaviourHandleManager.listBehaviourRecordNew(schoolId, campusId, staffFullInfoVO.getCampusSectionId(), null, null, startTime, endTime,null).stream().filter(s -> Constant.ONE.equals(s.getAppraisalType())).collect(Collectors.toList());

        List<BehaviourRecordDTO> recordDTOS = behaviourRecordDTOList.stream().filter(item -> Objects.equals(item.getCampusSectionId(), campusSectionId)).collect(Collectors.toList());
        //点评次数
        Integer evaluateNum = recordDTOS.size();
        //点评人次
        int size = recordDTOS.stream().map(BehaviourRecordDTO::getStudentId).distinct().collect(Collectors.toList()).size();
        //各年级点评次数
        ArrayList<String> gradeEvaluateNum = new ArrayList<>();
        for (EduOrgTreeVO gradeOrgTreeVO : gradeOrgTreeVOS) {
            gradeEvaluateNum.add(gradeOrgTreeVO.getName() + " " + (CollectionUtil.isEmpty(recordDTOS.stream().filter(s -> s.getGradeId().equals(Convert.toStr(gradeOrgTreeVO.getId()))).collect(Collectors.toList())) ? 0 : recordDTOS.stream().filter(s -> s.getGradeId().equals(Convert.toStr(gradeOrgTreeVO.getId()))).collect(Collectors.toList()).size()) + "次");
        }
        staffFullInfoVO.setTitle(MessageFormat.format(SCHOOL_EVALUATE_STATISTICS_INFORM, schoolName));
        if (MsgTypeEnum.WECHAT.getCode().equals(msgType)) {
            staffFullInfoVO.setInform(MessageFormat.format(SCHOOL_TEACHER_WECHAT_INFORM,
                    schoolName,
                    evaluateNum,
                    size
            ));
        } else {
            staffFullInfoVO.setInform(MessageFormat.format(SCHOOL_TEACHER_DINGDING_INFORM,
                    schoolName,
                    evaluateNum,
                    size,
                    String.join(Constant.CAESURA_SIGN, gradeEvaluateNum)
            ));
        }
        this.buildMsgUrl(tenantId, schoolId, campusId, staffFullInfoVO, startTime, endTime, null);
    }

    private void buildMsgUrl(String tenantId,
                             String schoolId,
                             String campusId,
                             StaffFullInfoVO staffFullInfoVO,
                             Date startTime,
                             Date endTime,
                             Long parentId){
        if (TeacherRoleEnum.NORMAL_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {
            // 老师
            staffFullInfoVO.setMessageUrl(messageUrl + "/home?current=2" +
                    "&tenantId=" + tenantId +
                    "&schoolId=" + schoolId +
                    "&campusId=" + campusId +
                    "&campusSectionId=" + staffFullInfoVO.getCampusSectionId() +
                    "&gradeId=" + staffFullInfoVO.getClassList().get(0).getGradeId() +
                    "&classId=" + staffFullInfoVO.getClassList().get(0).getClassId() +
                    "&staffId=" + staffFullInfoVO.getStaffId() +
                    "&startTime=" + DateUtil.format(startTime, Constant.PATTERN_FORMAT) +
                    "&endTime=" + DateUtil.format(endTime, Constant.PATTERN_FORMAT) +
                    "&fromMsg=1"
            );
        } else if (TeacherRoleEnum.CLASS_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {
            // 班主任
            staffFullInfoVO.setMessageUrl(messageUrl + "/home?current=2" +
                    "&tenantId=" + tenantId +
                    "&schoolId=" + schoolId +
                    "&campusId=" + campusId +
                    "&campusSectionId=" + staffFullInfoVO.getCampusSectionId() +
                    "&gradeId=" + Convert.toStr(parentId) +
                    "&classId=" + Convert.toStr(staffFullInfoVO.getClassOrGrade()) +
                    "&staffId=" + staffFullInfoVO.getStaffId() +
                    "&startTime=" + DateUtil.format(startTime, Constant.PATTERN_FORMAT) +
                    "&endTime=" + DateUtil.format(endTime, Constant.PATTERN_FORMAT) +
                    "&fromMsg=1"
            );
        } else if (TeacherRoleEnum.GRADE_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {
            // 年级组长
            staffFullInfoVO.setMessageUrl(messageUrl + "/home?current=2" +
                    "&tenantId=" + tenantId +
                    "&schoolId=" + schoolId +
                    "&campusId=" + campusId +
                    "&campusSectionId=" + staffFullInfoVO.getCampusSectionId() +
                    "&gradeId=" + Convert.toStr(staffFullInfoVO.getClassOrGrade()) +
                    "&staffId=" + staffFullInfoVO.getStaffId() +
                    "&startTime=" + DateUtil.format(startTime, Constant.PATTERN_FORMAT) +
                    "&endTime=" + DateUtil.format(endTime, Constant.PATTERN_FORMAT) +
                    "&fromMsg=1"

            );
        } else if (TeacherRoleEnum.SCHOOL_TEACHER.getCode().equals(staffFullInfoVO.getTeacherRoleType())) {
            //学生处长
            staffFullInfoVO.setMessageUrl(messageUrl + "/home?current=2" +
                    "&tenantId=" + tenantId +
                    "&schoolId=" + schoolId +
                    "&campusId=" + campusId +
                    "&campusSectionId=" + staffFullInfoVO.getCampusSectionId() +
                    "&staffId=" + staffFullInfoVO.getStaffId() +
                    "&startTime=" + DateUtil.format(startTime, Constant.PATTERN_FORMAT) +
                    "&endTime=" + DateUtil.format(endTime, Constant.PATTERN_FORMAT) +
                    "&fromMsg=1"
            );
        }
    }

    private void buildClassTeacherInfo(StaffFullInfoVO staffFullInfoVO,
                                       List<EduOrgTreeVO> children,
                                       List<BehaviourRecordDTO> behaviourRecordDTOList,
                                       Long classId, List<StaffFullInfoVO> staffFullInfoVOList,
                                       Integer msgType,
                                       Integer totalNum,
                                       Integer totalPeopleNum,
                                       List<String> noEvaluateStudents,
                                       List<EduOrgTreeVO> eduOrgTreeVOList,
                                       List<UcStudentClassBffVO> ucStudentClassBffS,
                                       ArrayList<String> teacherEvaluateNum,
                                       String studentNameList){

        List<Long> classIds = children.stream().map(EduOrgTreeVO::getId).collect(Collectors.toList());
        List<ClassTeacherInfoVO> classTeacherInfoVOS = saasClassManager.queryClassTeacherInfo(classIds);
        Map<Long, List<ClassTeacherInfoVO>> map = classTeacherInfoVOS.stream().collect(Collectors.groupingBy(ClassTeacherInfoVO::getClassId));

        //各班级排名
        ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
        for (EduOrgTreeVO eduOrgTreeVO : children) {
            List<Long> staffIds = new ArrayList<>();
            //该班级所有老师的信息
            List<ClassTeacherInfoVO> teacherInfoVOS = map.get(eduOrgTreeVO.getId());
            if(CollUtil.isEmpty(teacherInfoVOS)){
                continue;
            }
            teacherInfoVOS.forEach(item -> {
                staffIds.add(item.getMasterTacherId());
                List<Long> list = item.getTeacherList().stream().map(ClassTeacherInfoVO.ClassTeacher::getStaffId).collect(Collectors.toList());
                staffIds.addAll(list);
            });
            //该班级所有老师点评次数
            int evaluateNum = behaviourRecordDTOList.stream().filter(s -> staffIds.contains(Convert.toLong(s.getAppraisalId())) && s.getClassId().equals(Convert.toStr(eduOrgTreeVO.getId()))).collect(Collectors.toList()).size();
            NameAndValueVO nameAndValueVO = new NameAndValueVO();
            nameAndValueVO.setName(Convert.toStr(eduOrgTreeVO.getId()));
            nameAndValueVO.setValue(Convert.toStr(evaluateNum));
            nameAndValueVOS.add(nameAndValueVO);
        }
        nameAndValueVOS.stream().sorted(Comparator.comparing(NameAndValueVO::getValue).reversed().thenComparing(NameAndValueVO::getName)).collect(Collectors.toList());
        int indexOf = CollectionUtil.indexOf(nameAndValueVOS, s -> s.getName().equals(Convert.toStr(classId)));
        List<EduClassInfoLinkVO> eduClassInfoLinkVOS = basicInfoRemote.listUnderByClassIds(Arrays.asList(classId));
        if(CollUtil.isEmpty(eduClassInfoLinkVOS)){
            log.info("没有该班级信息，班级 id：[{}]",classId);
            return;
        }
        staffFullInfoVO.setTitle(MessageFormat.format(CLASS_EVALUATE_STATISTICS_INFORM, eduClassInfoLinkVOS.get(0).getClassName()));
        this.buildClassTeacherInform(staffFullInfoVO, staffFullInfoVOList, msgType, totalNum, totalPeopleNum, noEvaluateStudents, eduOrgTreeVOList, ucStudentClassBffS, teacherEvaluateNum, studentNameList, indexOf, eduClassInfoLinkVOS, children);
    }

    private void buildGradeTeacherInfo(StaffFullInfoVO staffFullInfoVO,
                                       List<BehaviourRecordDTO> behaviourRecordDTOList,
                                       List<EduOrgTreeVO> children,
                                       List<EduOrgTreeVO> orgTreeVOS,
                                       List<String> listTargetConfigTeacherIds,
                                       List<EduOrgTreeVO> eduOrgTreeVOS,
                                       Integer msgType){
        //年级名称
        String gradeName = eduOrgTreeVOS.get(0).getName();
        //该年级老师数量
        Integer gradeTeacherNum = 0;
        //该年级点评次数
        Integer gradeEvaluateNum = 0;
        //该年级点评人数
        Integer gradeStudentNum = 0;
        //学段名称
        String sectionName = orgTreeVOS.get(0).getName();
        //学段下所有年级id
        List<Long> gradeLongs = orgTreeVOS.get(0).getChildren().stream().map(EduOrgTreeVO::getId).collect(Collectors.toList());
        //各年级排名
        ArrayList<NameAndValueVO> nameAndValueVOS = new ArrayList<>();
        //各班级点评次数
        ArrayList<String> classEvaluateNum = new ArrayList<>();
        for (Long gradeLong : gradeLongs) {
            //saas获取该年级所有老师
            StaffSectionOrGradeQueryDTO dto = new StaffSectionOrGradeQueryDTO();
            dto.setGradeId(staffFullInfoVO.getClassOrGrade());
            List<StaffFullInfoVO> fullInfoVOS = basicInfoRemote.queryByGradeId(dto);
            //该年级所有老师id
            List<Long> longs = fullInfoVOS.stream().map(StaffFullInfoVO::getStaffId).collect(Collectors.toList());
            List<String> stringIds = Convert.toList(String.class, longs);
            //该年级有点评的老师
            Collection<String> intersection = CollectionUtil.intersection(listTargetConfigTeacherIds, stringIds);
            //该年级老师对该年级点评数据
            List<BehaviourRecordDTO> recordDTOS = behaviourRecordDTOList.stream().filter(s -> stringIds.contains(s.getAppraisalId()) && Convert.toStr(gradeLong).equals(s.getGradeId())).collect(Collectors.toList());
            //该年级老师对该年级点评次数
            Integer evaluateNum = recordDTOS.size();
            if (gradeLong.equals(staffFullInfoVO.getClassOrGrade())) {
                //老师数量
                gradeTeacherNum = intersection.size();
                //点评次数
                gradeEvaluateNum = evaluateNum;
                //点评人次
                gradeStudentNum = recordDTOS.stream().map(BehaviourRecordDTO::getStudentId).distinct().collect(Collectors.toList()).size();
                for (EduOrgTreeVO child : children) {
                    //点评次数
                    int size = behaviourRecordDTOList.stream().filter(s -> stringIds.contains(s.getAppraisalId()) && Convert.toStr(child.getId()).equals(s.getClassId())).collect(Collectors.toList()).size();
                    classEvaluateNum.add(child.getName() + " " + size + "次");
                }
            }
            NameAndValueVO nameAndValueVO = new NameAndValueVO();
            nameAndValueVO.setName(Convert.toStr(gradeLong));
            nameAndValueVO.setValue(Convert.toStr(evaluateNum));
            nameAndValueVOS.add(nameAndValueVO);
        }
        this.buildGradeTeacherInform(nameAndValueVOS, gradeStudentNum, gradeEvaluateNum, gradeTeacherNum, gradeName, msgType, sectionName, gradeLongs, staffFullInfoVO, classEvaluateNum);
    }

    private void buildClassTeacherInform(StaffFullInfoVO staffFullInfoVO,
                                         List<StaffFullInfoVO> staffFullInfoVOList,
                                         Integer msgType,
                                         Integer totalNum,
                                         Integer totalPeopleNum,
                                         List<String> noEvaluateStudents,
                                         List<EduOrgTreeVO> eduOrgTreeVOList,
                                         List<UcStudentClassBffVO> ucStudentClassBffS,
                                         ArrayList<String> teacherEvaluateNum,
                                         String studentNameList,
                                         Integer indexOf,
                                         List<EduClassInfoLinkVO> eduClassInfoLinkVOS,
                                         List<EduOrgTreeVO> children){
        if (MsgTypeEnum.WECHAT.getCode().equals(msgType)) {
            staffFullInfoVO.setInform(MessageFormat.format(CLASS_TEACHER_ALL_WECHAT_INFORM,
                    eduClassInfoLinkVOS.get(0).getClassName(),
                    staffFullInfoVOList.size(),
                    totalNum,
                    totalPeopleNum,
                    Constant.ZERO.equals(totalNum) || Constant.NEGATIVE_ONE.equals(indexOf) ? children.size() : indexOf + 1));
            return;
        }
        if (CollUtil.isEmpty(noEvaluateStudents)) {
            staffFullInfoVO.setInform(MessageFormat.format(CLASS_TEACHER_ALL_DINGDING_INFORM,
                    eduClassInfoLinkVOS.get(0).getClassName(),
                    staffFullInfoVOList.size(),
                    totalNum,
                    totalPeopleNum,
                    eduOrgTreeVOList.get(Constant.ZERO).getName(),
                    children.size(),
                    Constant.ZERO.equals(totalNum) || Constant.NEGATIVE_ONE.equals(indexOf) ? children.size() : indexOf + 1,
                    String.join(Constant.CAESURA_SIGN, teacherEvaluateNum),
                    eduClassInfoLinkVOS.get(0).getClassName()));
            return;
        }
        staffFullInfoVO.setInform(MessageFormat.format(CLASS_TEACHER_DINGDING_INFORM,
                ucStudentClassBffS.get(Constant.ZERO).getClassName(),
                staffFullInfoVOList.size(),
                totalNum,
                totalPeopleNum,
                eduOrgTreeVOList.get(Constant.ZERO).getName(),
                children.size(),
                Constant.ZERO.equals(totalNum) || Constant.NEGATIVE_ONE.equals(indexOf) ? children.size() : indexOf + 1,
                String.join(Constant.CAESURA_SIGN, teacherEvaluateNum),
                ucStudentClassBffS.get(Constant.ZERO).getClassName(),
                studentNameList
        ));
    }

    private void buildGradeTeacherInform(ArrayList<NameAndValueVO> nameAndValueVOS,
                                         Integer gradeStudentNum,
                                         Integer gradeEvaluateNum,
                                         Integer gradeTeacherNum,
                                         String gradeName,
                                         Integer msgType,
                                         String sectionName,
                                         List<Long> gradeLongs,
                                         StaffFullInfoVO staffFullInfoVO,
                                         ArrayList<String> classEvaluateNum){
        //年级点评次数排序
        nameAndValueVOS.stream().sorted(Comparator.comparing(NameAndValueVO::getValue).reversed().thenComparing(NameAndValueVO::getName)).collect(Collectors.toList());
        //该年级排名
        int indexOf = CollectionUtil.indexOf(nameAndValueVOS, s -> s.getName().equals(Convert.toStr(staffFullInfoVO.getClassOrGrade())));
        staffFullInfoVO.setTitle(MessageFormat.format(GRADE_EVALUATE_STATISTICS_INFORM, gradeName));
        if (MsgTypeEnum.WECHAT.getCode().equals(msgType)) {
            staffFullInfoVO.setInform(MessageFormat.format(GRADE_TEACHER_WECHAT_INFORM,
                    gradeName,
                    gradeTeacherNum,
                    gradeEvaluateNum,
                    gradeStudentNum,
                    Constant.ZERO.equals(gradeEvaluateNum) || Constant.NEGATIVE_ONE.equals(indexOf) ? gradeLongs.size() : indexOf + 1
            ));
        } else {
            staffFullInfoVO.setInform(MessageFormat.format(GRADE_TEACHER_DINGDING_INFORM,
                    gradeName,
                    gradeTeacherNum,
                    gradeEvaluateNum,
                    gradeStudentNum,
                    sectionName,
                    gradeLongs.size(),
                    Constant.ZERO.equals(gradeEvaluateNum) || Constant.NEGATIVE_ONE.equals(indexOf) ? gradeLongs.size() : indexOf + 1,
                    String.join(Constant.CAESURA_SIGN, classEvaluateNum)
            ));
        }
    }


}
