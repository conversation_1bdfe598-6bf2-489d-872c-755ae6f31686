package com.hailiang.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.convert.DictConvert;
import com.hailiang.exception.BizException;
import com.hailiang.mapper.DictDataMapper;
import com.hailiang.model.dto.DictDataDTO;
import com.hailiang.model.dto.DictTypeDTO;
import com.hailiang.model.entity.Dict;
import com.hailiang.model.entity.DictData;
import com.hailiang.model.vo.DictDataVO;
import com.hailiang.model.vo.DictTypeDataVO;
import com.hailiang.service.DictDataService;
import com.hailiang.service.DictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务实现
 *
 * <AUTHOR> 2023-03-22 14:30:40
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DictDataServiceImpl extends ServiceImpl<DictDataMapper, DictData> implements DictDataService {

    private final DictConvert dictConvert;
    private final DictService dictService;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public List<DictDataVO> listDictData(DictDataDTO dictDataDTO) {
        // 先查一下字典
        Dict dict = dictService.getOne(new LambdaQueryWrapper<Dict>()
                .eq(Dict::getDictType, dictDataDTO.getDictType())
                .eq(Dict::getStatus, Constant.NO));
        if (ObjectUtil.isEmpty(dict)) {
            throw new BizException("当前字典不存在或已禁用!");
        }

        List<DictData> dictDatas = this.list(new LambdaQueryWrapper<DictData>()
                .eq(DictData::getDictType, dictDataDTO.getDictType())
                .eq(DictData::getStatus, Constant.NO));

        return dictConvert.toDictDataVOs(dictDatas);
    }

    @Override
    public List<DictDataVO> listDictData(String dictType) {
        // 先查一下字典
        Dict dict = dictService.getOne(new LambdaQueryWrapper<Dict>()
                .eq(Dict::getDictType, dictType)
                .eq(Dict::getStatus, Constant.NO));
        if (ObjectUtil.isEmpty(dict)) {
            throw new BizException("当前字典不存在或已禁用!");
        }

        List<DictData> dictDatas = this.list(new LambdaQueryWrapper<DictData>()
                .eq(DictData::getDictType, dictType)
                .eq(DictData::getStatus, Constant.NO).orderByAsc(DictData::getDictSort));

        return dictConvert.toDictDataVOs(dictDatas);
    }

    @Override
    public String getNameById(Long id) {
        DictData dictData = this.getById(id);
        return ObjectUtil.isNull(dictData) ? StrUtil.EMPTY : dictData.getDictLabel();
    }

    @Override
    public List<DictTypeDataVO> listDictDataByDictType(DictTypeDTO dictTypeDTO) {
        List<String> dictTypes = dictTypeDTO.getDictTypes();
        Assert.notEmpty(dictTypes, "请求参数为空!");

        List<DictTypeDataVO> dictTypeDataVOS = new ArrayList<>();

        List<Dict> dicts = dictService.list(new LambdaQueryWrapper<Dict>()
                .in(Dict::getDictType, dictTypes)
                .eq(Dict::getStatus, Constant.NO));

        Assert.notEmpty(dicts, "当前字典不存在或已禁用!");

        List<DictData> dictDatas = this.list(new LambdaQueryWrapper<DictData>()
                .in(DictData::getDictType, dictTypes)
                .eq(DictData::getStatus, Constant.NO));

        Map<String, List<DictData>> dictDataMap = CollStreamUtil.groupByKey(dictDatas, DictData::getDictType);

        dictDataMap.forEach((k, v) -> {
            DictTypeDataVO dictTypeDataVO = new DictTypeDataVO();
            dictTypeDataVO.setDictType(k);
            dictTypeDataVO.setDictDataVOS(dictConvert.toDictDataVOs(v));

            dictTypeDataVOS.add(dictTypeDataVO);
        });

        return dictTypeDataVOS;
    }

    /**
     * 获取字典map
     *
     * @param dictType 字典类型
     * @return Map<String, String> key:code  value:名称
     */
    @Override
    public Map<String, String> getDictMap(String dictType) {
        log.info("[字典项]-准备缓存获取数据>>>>>>>>>>>>>>>>>>>>>>>>>>");
        List<DictData> dictDataList = redisUtil.getOrAdd(RedisKeyConstants.DICT_DATA, this::list, CacheConstants.HALF_HOUR);
        return dictDataList.stream().filter(s -> s.getDictType().equals(dictType)).collect(Collectors.toMap(DictData::getDictValue, DictData::getDictLabel));
    }

    @Override
    public Map<String, String> getDictMap2(String dictType) {
        log.info("[字典项]-准备缓存获取数据>>>>>>>>>>>>>>>>>>>>>>>>>>");
        List<DictData> dictDataList = redisUtil.getOrAdd(RedisKeyConstants.DICT_DATA, this::list, CacheConstants.HALF_HOUR);
        return dictDataList.stream().filter(s -> s.getDictType().equals(dictType)).collect(Collectors.toMap(DictData::getDictLabel, DictData::getDictValue));
    }
}
