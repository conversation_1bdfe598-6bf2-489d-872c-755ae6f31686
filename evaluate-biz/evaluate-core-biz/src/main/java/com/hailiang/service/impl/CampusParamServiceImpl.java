package com.hailiang.service.impl;

import com.hailiang.manager.CampusParamManager;
import com.hailiang.model.dto.query.CampusParamSaveDTO;
import com.hailiang.model.entity.CampusParamPO;
import com.hailiang.model.vo.CampusParamBaseInfoVO;
import com.hailiang.saas.uc.api.util.AssertUtil;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.CampusParamService;
import com.hailiang.util.WebUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【campus_param(校区参数表)】的数据库操作Service实现
* @createDate 2023-07-21 10:28:40
*/
@Service
public class CampusParamServiceImpl implements CampusParamService {

    @Autowired
    private CampusParamManager campusParamManager;

    @Autowired
    private BasicInfoService basicInfoService;
    /**
     * 通过校区id查校区参数信息，当前校区不存在则返回默认参数
     *
     * @param campusId
     * @return
     */
    @Override
    public CampusParamBaseInfoVO getCampusParam(String campusId) {
        CampusParamPO campusParamPO = campusParamManager.getCampusParam(campusId);

        // 当前学校下所有校区id--校区名称 map
        Map<String, String> campusIdNameMap = basicInfoService.queryCampusIdNameMap(WebUtil.getSchoolId());
        String campusName = campusIdNameMap.get(campusId);
        AssertUtil.checkNotBlank(campusName, "当前校区在saas不存在或不在当前学校下，请检查");

        return new CampusParamBaseInfoVO()
                .setCampusId(campusParamPO.getCampusId())
                .setCampusName(campusName)
                .setInitScore(campusParamPO.getInitScore());
    }

    /**
     * 根据校区id查初始分
     * @param campusId
     * @return
     */
    @Override
    public Integer getCampusInitScore(String campusId){
        CampusParamPO campusParamPO = campusParamManager.getCampusParam(campusId);
        return campusParamPO.getInitScore();
    }

    /**
     * 保存校区参数
     * @param param
     * @return
     */
    @Override
    public boolean saveCampusParam(CampusParamSaveDTO param){
        CampusParamPO campusParamPO = new CampusParamPO().setCampusId(WebUtil.getCampusId()).setInitScore(param.getInitScore());
        return campusParamManager.saveCampusParam(campusParamPO);
    }
}




