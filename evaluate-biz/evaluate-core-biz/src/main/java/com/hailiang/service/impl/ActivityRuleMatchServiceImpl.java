package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hailiang.constant.Constant;
import com.hailiang.convert.TaskRuleConvert;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.InfoTypeEnum;
import com.hailiang.enums.medal.*;
import com.hailiang.manager.*;
import com.hailiang.mapper.MedalActivityGradeMapper;
import com.hailiang.mapper.MedalRuleMatchRecordMapper;
import com.hailiang.mapper.MedalTaskRuleMapper;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.dto.activity.rule.save.MedalRuleDetailDTO;
import com.hailiang.model.dto.activity.rule.save.MedalTaskRuleTargetDTO;
import com.hailiang.model.dto.activity.rule.match.RuleBehaviourInfoDTO;
import com.hailiang.model.dto.activity.rule.match.TaskCompleteInfoDTO;
import com.hailiang.model.dto.activity.rule.save.OuterLayerRuleDTO;
import com.hailiang.model.dto.activity.rule.save.TopTaskCompleteDetailDTO;
import com.hailiang.model.entity.*;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.student.StudentVO;
import com.hailiang.service.ActivityRuleMatchService;
import com.hailiang.service.BasicInfoService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/6 17:40
 */
@Slf4j
@Service
public class ActivityRuleMatchServiceImpl implements ActivityRuleMatchService {

    @Resource
    private MedalActivityGradeMapper medalActivityGradeMapper;
    @Resource
    private MedalUserAcquireRecordManager medalUserAcquireRecordManager;
    @Resource
    private MedalTaskManager medalTaskManager;
    @Resource
    private MedalTaskRuleManager medalTaskRuleManager;
    @Resource
    private BehaviourHandleManager behaviourHandleManager;
    @Resource
    private MedalTaskRuleTargetManager medalTaskRuleTargetManager;
    @Resource
    private TaskRuleConvert taskRuleConvert;
    @Resource
    private MedalTaskCompletionManager medalTaskCompletionManager;
    @Resource
    private MedalRuleMatchRecordManager medalRuleMatchRecordManager;
    @Resource
    private MedalRuleMatchRecordMapper medalRuleMatchRecordMapper;

    @Autowired
    ThreadPoolTaskExecutor evaluateExecutor;

    @Resource
    private BasicInfoService basicInfoService;

    @Resource
    private MedalTaskCompletionRelationManager medalTaskCompletionRelationManager;

    @Resource
    private MedalUserOperationRecordManager medalUserOperationRecordManager;

    @Resource
    private MedalTaskRuleMapper medalTaskRuleMapper;

    /**
     * 匹配活动规则
     *
     * @param behaviorRecordIds 行为记录id
     * @return
     */
    public Boolean matchProcess(List<Long> behaviorRecordIds) {
        for (Long behaviorRecordId : behaviorRecordIds) {
            this.handlerMatchProcess(behaviorRecordId);
        }
        return Boolean.TRUE;
    }

//    /**
//     * 匹配活动规则(仅修复数据使用)
//     *
//     * @param behaviorRecordIds 行为记录id
//     * @return
//     */
//    public Boolean matchProcessFix(List<Long> behaviorRecordIds) {
//        for (Long behaviorRecordId : behaviorRecordIds) {
//            this.handlerMatchProcessFix(behaviorRecordId);
//        }
//        return Boolean.TRUE;
//    }

//    /**
//     * 匹配活动
//     *
//     * @param behaviorRecordId 行为记录id
//     */
//    @Transactional(rollbackFor = Exception.class, timeout = 600)
//    public Boolean handlerMatchProcessFix(Long behaviorRecordId) {
//        log.info("[争章活动]-[匹配任务规则]-[开始匹配]-行为记录id:{}", behaviorRecordId);
//
//        RuleBehaviourInfoDTO ruleBehaviour = behaviourHandleManager.getRuleBehaviour(behaviorRecordId);
//        if (Objects.isNull(ruleBehaviour)) {
//            log.info("[争章活动]-[匹配任务规则]-[命中规则]-[获取行为记录为空],行为记录id:{}", behaviorRecordId);
//            return Boolean.FALSE;
//        }
//
//        // 步骤1,命中规则,获取规则列表
//        List<MedalTaskRule> matchRuleTasks = getMatchRuleTaskFix(ruleBehaviour);
//        if (CollUtil.isEmpty(matchRuleTasks)) {
//            return Boolean.FALSE;
//        }
//
//        // 步骤2,根据规则计算一级任务完成情况
//        List<TaskCompleteInfoDTO> taskCompleteInfos = calculateSubtask(matchRuleTasks, ruleBehaviour);
//        if (CollUtil.isEmpty(taskCompleteInfos)) {
//            return Boolean.FALSE;
//        }
//
//        // 步骤3,根据一级任务完成情况处理数据库完成情况
//        Boolean subFlag = handleSubTaskSaveData(taskCompleteInfos, ruleBehaviour);
//        if (!subFlag) {
//            return Boolean.FALSE;
//        }
//
//        // 步骤4,根据一级任务完成情况计算二级任务完成情况
//        List<TaskCompleteInfoDTO> topTaskCompleteInfos = calculateTopTask(taskCompleteInfos, ruleBehaviour);
//        if (CollUtil.isEmpty(taskCompleteInfos)) {
//            return Boolean.FALSE;
//        }
//
//        // 步骤5,根据二级任务完成情况处理数据库完成情况
//        Boolean topFlag = handleTopTaskSaveData(topTaskCompleteInfos, ruleBehaviour);
//        if (!topFlag) {
//            return Boolean.FALSE;
//        }
//
//        // 步骤6,处理二级任务完成的详情
//        handleTopTaskDetail(topTaskCompleteInfos, ruleBehaviour);
//
//
////        // 步骤7,根据二级任务完成情况生成待颁章明细
////        Boolean aBoolean = saveMedalUserAcquireRecord(topTaskCompleteInfos, ruleBehaviour);
////        log.info("[争章活动]-[匹配任务规则]-[匹配结束]-行为记录id:{}", behaviorRecordId);
//
//        // 步骤8,填充明细的二级任务完成id信息(异常数据处理)
////        fillInRecordCompletionId(topTaskCompleteInfos);
//
//        return Boolean.TRUE;
//    }

    /**
     * 匹配活动
     *
     * @param behaviorRecordId 行为记录id
     */
    @Transactional(rollbackFor = Exception.class, timeout = 600)
    public Boolean handlerMatchProcess(Long behaviorRecordId) {
        log.info("[争章活动]-[匹配任务规则]-[开始匹配]-行为记录id:{}", behaviorRecordId);

        RuleBehaviourInfoDTO ruleBehaviour = behaviourHandleManager.getRuleBehaviour(behaviorRecordId);
        if (Objects.isNull(ruleBehaviour)) {
            log.info("[争章活动]-[匹配任务规则]-[命中规则]-[获取行为记录为空],行为记录id:{}", behaviorRecordId);
            return Boolean.FALSE;
        }

        // 步骤1,命中规则,获取规则列表
        List<MedalTaskRule> matchRuleTasks = getMatchRuleTask(ruleBehaviour);
        if (CollUtil.isEmpty(matchRuleTasks)) {
            return Boolean.FALSE;
        }

        // 步骤2,根据规则计算一级任务完成情况
        List<TaskCompleteInfoDTO> taskCompleteInfos = calculateSubtask(matchRuleTasks, ruleBehaviour);
        if (CollUtil.isEmpty(taskCompleteInfos)) {
            return Boolean.FALSE;
        }

        // 步骤3,根据一级任务完成情况处理数据库完成情况
        Boolean subFlag = handleSubTaskSaveData(taskCompleteInfos, ruleBehaviour);
        if (!subFlag) {
            return Boolean.FALSE;
        }

        // 步骤4,根据一级任务完成情况计算二级任务完成情况
        List<TaskCompleteInfoDTO> topTaskCompleteInfos = calculateTopTask(taskCompleteInfos, ruleBehaviour);
        if (CollUtil.isEmpty(taskCompleteInfos)) {
            return Boolean.FALSE;
        }

        // 步骤5,根据二级任务完成情况处理数据库完成情况
        Boolean topFlag = handleTopTaskSaveData(topTaskCompleteInfos, ruleBehaviour);
        if (!topFlag) {
            return Boolean.FALSE;
        }

        // 步骤6,处理二级任务完成的详情
        handleTopTaskDetail(topTaskCompleteInfos, ruleBehaviour);


//        // 步骤7,根据二级任务完成情况生成待颁章明细
//        Boolean aBoolean = saveMedalUserAcquireRecord(topTaskCompleteInfos, ruleBehaviour);
//        log.info("[争章活动]-[匹配任务规则]-[匹配结束]-行为记录id:{}", behaviorRecordId);

        // 步骤8,填充明细的二级任务完成id信息(异常数据处理)
//        fillInRecordCompletionId(topTaskCompleteInfos);

        return Boolean.TRUE;
    }

//    /**
//     * 步骤8,填充明细的二级任务完成id信息
//     *
//     * @param topTaskCompleteInfos 二级任务完成信息
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void fillInRecordCompletionId(List<TaskCompleteInfoDTO> topTaskCompleteInfos) {
//        try {
//            for (TaskCompleteInfoDTO topTaskCompleteInfo : topTaskCompleteInfos) {
//                // 获取颁章明细中的数据
//                List<MedalUserAcquireRecord> medalUserAcquires = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
//                        .eq(MedalUserAcquireRecord::getStudentId, topTaskCompleteInfo.getStudentId())
//                        .eq(MedalUserAcquireRecord::getMedalTaskId, topTaskCompleteInfo.getMedalTaskId())
//                );
//                if (CollUtil.isEmpty(medalUserAcquires)) {
//                    continue;
//                }
//                // 待填充的数据
//                List<MedalUserAcquireRecord> needFillUserAcquireRecords = medalUserAcquires.stream().filter(s -> Objects.isNull(s.getCompletionId())).collect(Collectors.toList());
//                if (CollUtil.isEmpty(needFillUserAcquireRecords)) {
//                    continue;
//                }
//                // 已填充的二级任务id
//                List<Long> isFillCompletionIds = medalUserAcquires.stream().filter(s -> Objects.nonNull(s.getCompletionId())).map(MedalUserAcquireRecord::getCompletionId).distinct().collect(Collectors.toList());
//
//                // 数据库中存在的二级任务数
//                List<MedalTaskCompletion> topTaskCompletes = medalTaskCompletionManager.list(new LambdaQueryWrapper<MedalTaskCompletion>()
//                        .eq(MedalTaskCompletion::getStudentId, topTaskCompleteInfo.getStudentId())
//                        .eq(MedalTaskCompletion::getMedalTaskId, topTaskCompleteInfo.getMedalTaskId())
//                        .eq(MedalTaskCompletion::getLevel, MedalRuleLevelEnum.TOP_LEVEL.getCode())
//                        .orderByAsc(MedalTaskCompletion::getId));
//
//                // 筛选出未与颁章明细关联的二级任务
//                List<MedalTaskCompletion> noRelationAcquireRecords = topTaskCompletes.stream().filter(s -> !isFillCompletionIds.contains(s.getId())).sorted(Comparator.comparing(MedalTaskCompletion::getId)).collect(Collectors.toList());
//
//
//                // 如果待填充的颁章明细数量与未关联的二级任务数量不一致
//                if (!ObjectUtil.equals(noRelationAcquireRecords.size(), needFillUserAcquireRecords.size())) {
//                    log.info("[争章活动]-[匹配任务规则]-[步骤8]-[待填充的颁章明细数量与未关联的二级任务数量不一致],二级任务完成信息信息:{}", topTaskCompleteInfos);
//                    continue;
//                }
//
//                int index = 0;
//                List<MedalUserAcquireRecord> list = new ArrayList<>();
//                for (MedalUserAcquireRecord medalUserAcquire : medalUserAcquires) {
//                    MedalTaskCompletion medalTaskCompletion = topTaskCompletes.get(index);
//                    medalUserAcquire.setCompletionId(medalTaskCompletion.getId());
//                    list.add(medalUserAcquire);
//                    index++;
//                }
//                medalUserAcquireRecordManager.updateBatchById(list);
//            }
//        } catch (Exception e) {
//            log.error("[争章活动]-[匹配任务规则]-[步骤8]-[填充明细的二级任务完成id信息异常],二级任务完成信息信息:{}", topTaskCompleteInfos, e);
//        }
//    }

    /**
     * 步骤6,处理二级任务完成的详情
     *
     * @param topTaskCompleteInfos 二级任务完成信息
     * @param ruleBehaviour        行为记录信息
     */
    private Boolean handleTopTaskDetail(List<TaskCompleteInfoDTO> topTaskCompleteInfos, RuleBehaviourInfoDTO ruleBehaviour) {
        try {
            for (TaskCompleteInfoDTO topTaskCompleteInfo : topTaskCompleteInfos) {
                // 数据库中存在的二级任务数
                List<MedalTaskCompletion> topTaskCompletes = medalTaskCompletionManager.list(new LambdaQueryWrapper<MedalTaskCompletion>() // NOSONAR
                        .eq(MedalTaskCompletion::getStatus, MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode())
                        .eq(MedalTaskCompletion::getStudentId, ruleBehaviour.getStudentId())
                        .eq(MedalTaskCompletion::getMedalTaskId, topTaskCompleteInfo.getMedalTaskId())
                        .eq(MedalTaskCompletion::getLevel, MedalRuleLevelEnum.TOP_LEVEL.getCode())
                        .orderByAsc(MedalTaskCompletion::getId));
                if (CollUtil.isEmpty(topTaskCompletes)) {
                    continue;
                }
                List<Long> taskCompletionIds = topTaskCompletes.stream().map(MedalTaskCompletion::getId).distinct().collect(Collectors.toList());
                // 查询二级任务详情表中的数据
                List<MedalTaskCompletionRelation> taskCompletionRelations = medalTaskCompletionRelationManager.list(new LambdaQueryWrapper<MedalTaskCompletionRelation>() // NOSONAR
                        .in(MedalTaskCompletionRelation::getTaskCompletionId, taskCompletionIds));
                List<Long> existTaskCompletionIds = taskCompletionRelations.stream().map(MedalTaskCompletionRelation::getTaskCompletionId).distinct().collect(Collectors.toList());

                taskCompletionIds.removeAll(existTaskCompletionIds);
                if (CollUtil.isEmpty(taskCompletionIds)) {
                    continue;
                }

                List<MedalTaskCompletionRelation> list = new ArrayList<>();
                List<TopTaskCompleteDetailDTO> topTaskCompleteDetails = topTaskCompleteInfo.getTopTaskCompleteDetails();
                int index = existTaskCompletionIds.size();
                for (Long taskCompletionId : taskCompletionIds) {
                    if ((index + 1) > topTaskCompleteDetails.size()) {
                        continue;
                    }
                    TopTaskCompleteDetailDTO topTaskCompleteDetailDTO = topTaskCompleteDetails.get(index);
                    for (Long subTaskRuleId : topTaskCompleteDetailDTO.getSubTaskRuleIds()) {
                        MedalRuleDetailDTO medalRuleDetail = medalTaskRuleMapper.getMedalRuleDetail(subTaskRuleId);
                        MedalTaskCompletionRelation medalTaskCompletionRelation = new MedalTaskCompletionRelation();
                        medalTaskCompletionRelation.setTaskCompletionId(taskCompletionId);
                        medalTaskCompletionRelation.setMedalRuleId(subTaskRuleId);
                        medalTaskCompletionRelation.setTenantId(ruleBehaviour.getTenantId());
                        medalTaskCompletionRelation.setSchoolId(ruleBehaviour.getSchoolId());
                        medalTaskCompletionRelation.setCampusId(ruleBehaviour.getCampusId());
                        medalTaskCompletionRelation.setCreateBy(ruleBehaviour.getCreateBy());
                        if (Objects.nonNull(medalRuleDetail)) {
                            medalTaskCompletionRelation.setMedalRuleType(medalRuleDetail.getRuleType());
                            medalTaskCompletionRelation.setTargetValue(medalRuleDetail.getTargetValue());
                            if (StrUtil.isNotBlank(medalRuleDetail.getRuleName())) {
                                String[] ruleNames = medalRuleDetail.getRuleName().split(StrPool.SLASH);
                                medalTaskCompletionRelation.setMedalRuleName(ruleNames[ruleNames.length - 1]);
                            }
                        }
                        list.add(medalTaskCompletionRelation);
                    }
                    index++;
                }
                medalTaskCompletionRelationManager.saveBatch(list);
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[争章活动]-[匹配任务规则]-[步骤6]-[处理二级任务完成的详情异常],二级任务完成信息信息:{}", topTaskCompleteInfos, e);
        }
        return Boolean.FALSE;
    }

    /**
     * 根据任务完成的情况处理数据库中的数据状态
     *
     * @param taskCompleteInfos 目标完成任务数据
     * @param level             任务层级  1:一级任务完成情况  2:二级任务完成情况
     * @param ruleBehaviour     行为记录信息
     * @return
     */
    private Boolean handleTaskSaveDataStatus(List<TaskCompleteInfoDTO> taskCompleteInfos, Integer level, RuleBehaviourInfoDTO ruleBehaviour) {
        if (CollUtil.isEmpty(taskCompleteInfos)) {
            return Boolean.FALSE;
        }

        // 一级规则id
        List<Long> ruleIds = taskCompleteInfos.stream().map(TaskCompleteInfoDTO::getMedalTaskRuleId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(ruleIds)) {
            return Boolean.FALSE;
        }
        // 查询数据库中的一/二级任务完成数
        List<MedalTaskCompletion> medalTaskCompletions = medalTaskCompletionManager.list(new LambdaQueryWrapper<MedalTaskCompletion>()
                .eq(MedalTaskCompletion::getStudentId, ruleBehaviour.getStudentId())
                .in(MedalTaskCompletion::getMedalRuleId, ruleIds)
                .eq(MedalTaskCompletion::getLevel, level));
        Map<Long, List<MedalTaskCompletion>> completeMap = medalTaskCompletions.stream().collect(Collectors.groupingBy(MedalTaskCompletion::getMedalRuleId));

        /**
         * 是否存在手动撤回状态的数据
         */
        Boolean existArtificialWithdraw = Boolean.FALSE;
        List<MedalTaskCompletion> artificalWithdraws = medalTaskCompletions.stream().filter(s -> MedalTaskCompleteStatusEnum.ARTIFICIAL_WITHDRAW.getCode().equals(s.getStatus())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(artificalWithdraws)) {
            existArtificialWithdraw = Boolean.TRUE;
        }

        // 根据规则id分组
        Map<Long, TaskCompleteInfoDTO> groupMap = taskCompleteInfos.stream().collect(Collectors.toMap(TaskCompleteInfoDTO::getMedalTaskRuleId, Function.identity()));

        for (Long ruleId : groupMap.keySet()) {
            // 根据规则计算完成的任务数
            TaskCompleteInfoDTO taskCompleteInfoDTO = groupMap.get(ruleId);
            // 一/二级任务目标完成数
            Integer targetNumber = taskCompleteInfoDTO.getCompleteNum();

            // 如果数据库中不存在数据
            if (CollUtil.isEmpty(completeMap.get(ruleId))) {
                // 如果目标任务完成数为0,数据库中也没有数据,则不做任何操作
                if (targetNumber.equals(Constant.ZERO)) {
                    continue;
                }
                // 如果目标任务完成数大于0,数据库中也没有数据,则新增完成的数据
                if (targetNumber > Constant.ZERO) {
                    saveNewCompletion(taskCompleteInfoDTO, targetNumber, level, ruleBehaviour, existArtificialWithdraw);
                }
            }

            // 如果数据库中存在数据
            if (CollUtil.isNotEmpty(completeMap.get(ruleId))) {
                List<MedalTaskCompletion> localTaskCompletions = completeMap.get(ruleId);
                // 未完成的
                List<MedalTaskCompletion> noCompletes = localTaskCompletions.stream().filter(s -> s.getStatus().equals(0)).collect(Collectors.toList());
                // 已完成的
                List<MedalTaskCompletion> isCompletes = localTaskCompletions.stream().filter(s -> s.getStatus().equals(1)).collect(Collectors.toList());
                // 冻结的
                List<MedalTaskCompletion> frozenCompletes = localTaskCompletions.stream().filter(s -> s.getStatus().equals(2)).collect(Collectors.toList());

                // 如果是二级任务,冻结的数据和撤回的数据都是固定的(自动颁章手动撤回的占循环章名额)
                if (level.equals(2)) {
                    frozenCompletes = localTaskCompletions.stream().filter(s -> CollUtil.newArrayList(2, 3).contains(s.getStatus())).collect(Collectors.toList());
                }

                // 如果目标任务完成数为0,数据库中存在数据,需要将已完成的状态变为未完成,冻结的不做处理
                if (targetNumber.equals(Constant.ZERO)) {
                    isCompletes.forEach(s -> {
                        s.setStatus(MedalTaskCompleteStatusEnum.NO_COMPLETION.getCode());
                        s.setUpdateBy(ruleBehaviour.getUpdateBy());
                    });
                    medalTaskCompletionManager.updateBatchById(isCompletes);
                    // 删除二级任务完成详情表中的数据
                    deleteCompletionDetail(level, medalTaskCompletions);
                }
                // 如果目标任务完成数大于数据库的数量,则需要将数据库中未完成的状态改为已完成,同时新增数据
                if (targetNumber > localTaskCompletions.size()) {
                    // 将数据库中未完成的状态改为已完成
                    if (CollUtil.isNotEmpty(noCompletes)) {
                        noCompletes.forEach(s -> s.setStatus(MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode()));
                        medalTaskCompletionManager.updateBatchById(noCompletes);
                    }
                    // 新增数据
                    Integer newSaveNum = targetNumber - localTaskCompletions.size();
                    saveNewCompletion(taskCompleteInfoDTO, newSaveNum, level, ruleBehaviour, existArtificialWithdraw);
                }

                // 如果目标任务数不为0,但是小于数据库中的数量
                if (targetNumber > Constant.ZERO && targetNumber <= localTaskCompletions.size()) {
                    // 剩余需要完成的任务数
                    Integer remainCompletionNum;
                    // 判断是否大于冻结的任务数,需要减去冻结的任务
                    if (targetNumber > frozenCompletes.size()) {
                        remainCompletionNum = targetNumber - frozenCompletes.size();
                    } else {
                        remainCompletionNum = 0;
                    }

                    // 比较剩余任务数和数据库中的完成数
                    // 如果数量不相等,进行下面处理
                    if (ObjectUtil.notEqual(remainCompletionNum, isCompletes.size())) {
                        // 剩余数大于已完成数,需要将数据库中部分未完成的状态改为已完成
                        if (remainCompletionNum > isCompletes.size()) {
                            int num = remainCompletionNum - isCompletes.size();
                            List<MedalTaskCompletion> collect = noCompletes.stream().limit(num).collect(Collectors.toList());
                            collect.forEach(s -> {
                                s.setStatus(MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode());
                                s.setUpdateBy(ruleBehaviour.getUpdateBy());
                            });
                            medalTaskCompletionManager.updateBatchById(collect);
                        }
                        // 剩余数小于已完成数,需要将数据库中部分已完成的状态改为未完成
                        if (remainCompletionNum < isCompletes.size()) {
                            int num = isCompletes.size() - remainCompletionNum;
                            List<MedalTaskCompletion> collect = isCompletes.stream().sorted(Comparator.comparing(MedalTaskCompletion::getId).reversed()).limit(num).collect(Collectors.toList());
                            collect.forEach(s -> {
                                s.setStatus(MedalTaskCompleteStatusEnum.NO_COMPLETION.getCode());
                                s.setUpdateBy(ruleBehaviour.getUpdateBy());
                            });
                            medalTaskCompletionManager.updateBatchById(collect);
                            // 删除二级任务完成详情表中的数据
                            deleteCompletionDetail(level, medalTaskCompletions);
                        }
                    }

                }
            }

            // 修改颁章明细状态以及判断是否需要修复数据
            if (MedalRuleLevelEnum.TOP_LEVEL.getCode().equals(level)) {
                List<MedalUserAcquireRecord> medalUserAcquireRecords = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>() // NOSONAR
                        .eq(MedalUserAcquireRecord::getStudentId, ruleBehaviour.getStudentId())
                        .eq(MedalUserAcquireRecord::getAwardType, MedalAwardTypeEnum.AUTO.getCode())
                        .in(MedalUserAcquireRecord::getMedalTaskId, taskCompleteInfoDTO.getMedalTaskId()));
                fixUserAcquireRecord(taskCompleteInfoDTO, medalUserAcquireRecords, existArtificialWithdraw);
            }

        }
        return Boolean.TRUE;
    }

    /**
     * 修复异常数据
     *
     * @param taskCompleteInfoDTO     二级任务信息
     * @param medalUserAcquireRecords 颁章明细数据
     * @param existArtificialWithdraw 是否存在手动撤回的二级任务数据
     * @return
     */
    private Boolean fixUserAcquireRecord(TaskCompleteInfoDTO taskCompleteInfoDTO, List<MedalUserAcquireRecord> medalUserAcquireRecords, Boolean existArtificialWithdraw) {
        // 查询当前数据库出所有二级任务信息
        List<MedalTaskCompletion> medalTaskCompletions = medalTaskCompletionManager.list(new LambdaQueryWrapper<MedalTaskCompletion>()
                .eq(MedalTaskCompletion::getStudentId, taskCompleteInfoDTO.getStudentId())
                .eq(MedalTaskCompletion::getMedalRuleId, taskCompleteInfoDTO.getMedalTaskRuleId())
                .eq(MedalTaskCompletion::getLevel, taskCompleteInfoDTO.getLevel()));
        if (CollUtil.isEmpty(medalTaskCompletions)) {
            return Boolean.TRUE;
        }

        // 筛选出颁章明细中completionId为空的数据
        List<MedalUserAcquireRecord> completionIdIsNulls = medalUserAcquireRecords.stream().filter(s -> Objects.isNull(s.getCompletionId())).collect(Collectors.toList());

        // 颁章明细中completionId为空的数据被修复数
        int num = 0;
        for (MedalTaskCompletion medalTaskCompletion : medalTaskCompletions) {
            List<MedalUserAcquireRecord> medalUserRecord = medalUserAcquireRecords.stream().filter(s -> ObjectUtil.equals(s.getCompletionId(), medalTaskCompletion.getId())).collect(Collectors.toList());
            Boolean flag = judgePreExistArtificialWithdraw(medalTaskCompletion.getId(), medalTaskCompletions);

            // 情况1:如果存在二级任务,但是颁章明细不存在,说明是异常数据,需要修复异常数据
            if (CollUtil.isEmpty(medalUserRecord)) {
                // 如果该二级任务之前存在手动撤回的数据,则不新增颁章明细
                if (flag) {
                    return Boolean.TRUE;
                }
                Integer awardStatus = null;
                // 如果二级任务状态为未完成,则颁章明细状态为自动撤回
                if (MedalTaskCompleteStatusEnum.NO_COMPLETION.getCode().equals(medalTaskCompletion.getStatus())) {
                    awardStatus = MedalAwardStatusEnum.AUTO_WITHDRAW.getCode();
                }
                // 如果二级任务状态为已完成,则颁章明细状态为待颁发
                if (MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode().equals(medalTaskCompletion.getStatus())) {
                    awardStatus = MedalAwardStatusEnum.IS_ISSUE.getCode();
                }
                if (num < completionIdIsNulls.size()) {
                    MedalUserAcquireRecord medalUserAcquireRecord = completionIdIsNulls.get(num);
                    medalUserAcquireRecord.setAwardStatus(awardStatus);
                    medalUserAcquireRecord.setCompletionId(medalTaskCompletion.getId());
                    medalUserAcquireRecordManager.updateById(medalUserAcquireRecord);
                } else {
                    // 如果completionId为空的数据都被修复,还存在异常数据,则新增明细数据
                    saveSingleNewAcquireRecord(taskCompleteInfoDTO, medalTaskCompletion, awardStatus, existArtificialWithdraw);
                }
                num++;
                continue;
            }

            // 情况2:正常修改颁章颁章状态
            MedalUserAcquireRecord medalUserAcquireRecord = CollUtil.getFirst(medalUserRecord);
            // 如果二级任务状态为未完成,则颁章明细状态为自动撤回
            if (MedalTaskCompleteStatusEnum.NO_COMPLETION.getCode().equals(medalTaskCompletion.getStatus())) {
                medalUserAcquireRecord.setAwardStatus(MedalAwardStatusEnum.AUTO_WITHDRAW.getCode());
            }
            // 如果二级任务状态为已完成,则颁章明细状态为待颁发
            if (MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode().equals(medalTaskCompletion.getStatus())) {
                // 如果存在手动撤回的数据,后续完成任务也不生成新的明细,二级任务完成,明细状态也不修改
                if (flag) {
                    log.info("[争章活动]-[根据二级任务完成状态修改颁章明细状态]-[由于存在手动撤回的二级任务-该二级任务完成,颁章明细状态不作修改]-[二级任务记录id:{}]", medalTaskCompletion.getId());
                    return Boolean.TRUE;
                }
                medalUserAcquireRecord.setAwardStatus(MedalAwardStatusEnum.IS_ISSUE.getCode());
            }
            medalUserAcquireRecordManager.updateById(medalUserAcquireRecord);
        }
        return Boolean.TRUE;
    }

    /**
     * 判断该任务之前是否存在手动撤回的数据
     *
     * @param completionId         二级任务完成id
     * @param medalTaskCompletions 二级任务完成详情
     */
    private Boolean judgePreExistArtificialWithdraw(Long completionId, List<MedalTaskCompletion> medalTaskCompletions) {
        Boolean flag = Boolean.FALSE;
        List<MedalTaskCompletion> existArtificialWithdraws = medalTaskCompletions.stream().filter(s -> s.getId() < completionId).filter(s -> MedalTaskCompleteStatusEnum.ARTIFICIAL_WITHDRAW.getCode().equals(s.getStatus())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(existArtificialWithdraws)) {
            flag = Boolean.TRUE;
        }
        return flag;
    }

    /**
     * 如果存在撤回的二级任,则删除二级任务的所有详情
     *
     * @param level
     * @param medalTaskCompletions
     */
    private void deleteCompletionDetail(Integer level, List<MedalTaskCompletion> medalTaskCompletions) {
        if (MedalRuleLevelEnum.SUB_LEVEL.getCode().equals(level)) {
            return;
        }
        if (CollUtil.isEmpty(medalTaskCompletions)) {
            return;
        }
        List<Long> completionIds = medalTaskCompletions.stream().map(MedalTaskCompletion::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(completionIds)) {
            return;
        }
        medalTaskCompletionRelationManager.remove(new LambdaQueryWrapper<MedalTaskCompletionRelation>()
                .in(MedalTaskCompletionRelation::getTaskCompletionId, completionIds));
    }


    /**
     * 新增完成的任务数据
     *
     * @param taskCompleteInfoDTO     规则信息
     * @param targetNumber            任务完成数
     * @param level                   层级
     * @param existArtificialWithdraw 是否存在手动撤回的二级任务数据
     */
    private Boolean saveNewCompletion(TaskCompleteInfoDTO taskCompleteInfoDTO, Integer targetNumber, Integer level, RuleBehaviourInfoDTO ruleBehaviour, Boolean existArtificialWithdraw) {
        List<MedalTaskCompletion> list = new ArrayList<>();
        for (int i = 0; i < targetNumber; i++) {
            MedalTaskCompletion medalTaskCompletion = new MedalTaskCompletion();
            medalTaskCompletion.setTenantId(taskCompleteInfoDTO.getTenantId());
            medalTaskCompletion.setSchoolId(taskCompleteInfoDTO.getSchoolId());
            medalTaskCompletion.setCampusId(taskCompleteInfoDTO.getCampusId());
            medalTaskCompletion.setLevel(level);
            medalTaskCompletion.setMedalTaskId(taskCompleteInfoDTO.getMedalTaskId());
            medalTaskCompletion.setMedalRuleId(taskCompleteInfoDTO.getMedalTaskRuleId());
            if (existArtificialWithdraw) {
                medalTaskCompletion.setStatus(MedalTaskCompleteStatusEnum.ARTIFICIAL_WITHDRAW.getCode());
            } else {
                medalTaskCompletion.setStatus(MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode());
            }
            medalTaskCompletion.setStudentId(taskCompleteInfoDTO.getStudentId());
            medalTaskCompletion.setCreateBy(ruleBehaviour.getCreateBy());
            list.add(medalTaskCompletion);
        }
        medalTaskCompletionManager.saveBatch(list);

//        // 如果存在手动撤回的数据,则后续不生成颁章明细
//        if (existArtificialWithdraw) {
//            log.info("[争章活动]-[根据二级任务完成状态新增颁章明细状态]-[由于存在手动撤回的二级任务-该二级任务完成,颁章明细不生成]-[二级任务信息:{}]", taskCompleteInfoDTO);
//            return Boolean.TRUE;
//        }
        // 新增颁章记录
        for (MedalTaskCompletion medalTaskCompletion : list) {
            saveSingleNewAcquireRecord(taskCompleteInfoDTO, medalTaskCompletion, MedalAwardStatusEnum.IS_ISSUE.getCode(), existArtificialWithdraw);
        }
        return Boolean.TRUE;
    }


    /**
     * 匹配获取任务列表
     *
     * @param ruleBehaviour 行为记录信息
     * @return
     */
    public List<MedalTaskRule> getMatchRuleTask(RuleBehaviourInfoDTO ruleBehaviour) {
        try {
            // 根据年级获取活动id
            List<Long> activityIds = medalActivityGradeMapper.listActivityIdsByGradeId(ruleBehaviour.getTenantId(), ruleBehaviour.getSchoolId(), ruleBehaviour.getCampusId(), ruleBehaviour.getGradeId());
            if (CollUtil.isEmpty(activityIds)) {
                return Collections.emptyList();
            }

            // 获取任务id(未失效的任务)
            List<MedalTask> activityTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>()
                    .eq(MedalTask::getStatus, Constant.NO)
                    .in(MedalTask::getMedalActivityId, activityIds));
            List<Long> taskIds = activityTasks.stream().map(MedalTask::getId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(taskIds)) {
                return Collections.emptyList();
            }

            // 获取规则(未失效的一级规则)
            List<MedalTaskRule> taskRules = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>()
                    .in(MedalTaskRule::getMedalTaskId, taskIds)
                    .eq(MedalTaskRule::getStatus, Constant.NO)
                    .eq(MedalTaskRule::getLevel, MedalRuleLevelEnum.SUB_LEVEL.getCode()));
            if (CollUtil.isEmpty(taskRules)) {
                return Collections.emptyList();
            }

            List<Long> ruleIds = taskRules.stream().map(MedalTaskRule::getId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(ruleIds)) {
                return Collections.emptyList();
            }
            List<MedalTaskRuleTarget> medalTaskRuleTargets = medalTaskRuleTargetManager.list(new LambdaQueryWrapper<MedalTaskRuleTarget>().in(MedalTaskRuleTarget::getMedalTaskRuleId, ruleIds));
            if (CollUtil.isEmpty(medalTaskRuleTargets)) {
                return Collections.emptyList();
            }
            Map<Long, MedalTaskRuleTarget> taskRuleTargetMap = medalTaskRuleTargets.stream().collect(Collectors.toMap(MedalTaskRuleTarget::getMedalTaskRuleId, Function.identity()));

            List<MedalTaskRule> matchRuleList = new ArrayList<>();
            // 匹配命中具体规则
            for (MedalTaskRule taskRule : taskRules) {

                // 点评次数/点评总分匹配规则
                if (CollUtil.newArrayList(MedalRuleTypeEnum.COMMENT_NUM.getCode(), MedalRuleTypeEnum.COMMENT_TOTAL.getCode()).contains(taskRule.getType())) {
                    if (Objects.isNull(taskRuleTargetMap.get(taskRule.getId()))) {
                        continue;
                    }
                    MedalTaskRuleTarget medalTaskRuleTarget = taskRuleTargetMap.get(taskRule.getId());
                    MedalTaskRuleTargetDTO behaviourRuleDetailDTO = taskRuleConvert.toMedalTaskRuleTargetDTO(medalTaskRuleTarget);

                    // 模块code不相等
                    if (Objects.nonNull(behaviourRuleDetailDTO.getModuleCode()) && !behaviourRuleDetailDTO.getModuleCode().equals(ruleBehaviour.getModuleCode())) {
                        continue;
                    }

                    // 分组不相等
                    if (Objects.nonNull(behaviourRuleDetailDTO.getGroupId()) && !behaviourRuleDetailDTO.getGroupId().equals(ruleBehaviour.getGroupId())) {
                        continue;
                    }

                    // 指标不相等
                    if (Objects.nonNull(behaviourRuleDetailDTO.getTargetId()) && !behaviourRuleDetailDTO.getTargetId().equals(ruleBehaviour.getTargetId())) {
                        continue;
                    }

                    // 点评项不相等
                    if (Objects.nonNull(behaviourRuleDetailDTO.getOptionId()) && !behaviourRuleDetailDTO.getOptionId().equals(ruleBehaviour.getOptionId())) {
                        continue;
                    }
                    taskRule.setContent(JSONUtil.toJsonStr(behaviourRuleDetailDTO));
                    matchRuleList.add(taskRule);
                }

                // 后期: 其他规则的匹配规则
            }
            // 命中的行为数据落表
            saveRuleBehaviour(matchRuleList, ruleBehaviour);

            // 生成明细的操作记录
            saveMedalUserOperationRecord(matchRuleList, ruleBehaviour);

            return matchRuleList;
        } catch (Exception e) {
            log.error("[争章活动]-[匹配任务规则]-[步骤1]-[命中规则]-获取命中规则列表异常],行为记录信息:{}", ruleBehaviour, e);
        }

        return Collections.emptyList();
    }

    /**
     * 匹配获取任务列表(仅修复数据使用)
     *
     * @param ruleBehaviour 行为记录信息
     * @return
     */
    public List<MedalTaskRule> getMatchRuleTaskFix(RuleBehaviourInfoDTO ruleBehaviour) {
        try {
            // 根据年级获取活动id
            List<Long> activityIds = medalActivityGradeMapper.listActivityIdsByGradeIdFix(ruleBehaviour.getTenantId(), ruleBehaviour.getSchoolId(), ruleBehaviour.getCampusId(), ruleBehaviour.getGradeId());
            if (CollUtil.isEmpty(activityIds)) {
                return Collections.emptyList();
            }

            // 获取任务id
            List<MedalTask> activityTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>()
                    .in(MedalTask::getMedalActivityId, activityIds));
            List<Long> taskIds = activityTasks.stream().map(MedalTask::getId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(taskIds)) {
                return Collections.emptyList();
            }

            // 获取规则
            List<MedalTaskRule> taskRules = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>()
                    .in(MedalTaskRule::getMedalTaskId, taskIds)
                    .eq(MedalTaskRule::getLevel, MedalRuleLevelEnum.SUB_LEVEL.getCode()));
            if (CollUtil.isEmpty(taskRules)) {
                return Collections.emptyList();
            }

            List<Long> ruleIds = taskRules.stream().map(MedalTaskRule::getId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(ruleIds)) {
                return Collections.emptyList();
            }
            List<MedalTaskRuleTarget> medalTaskRuleTargets = medalTaskRuleTargetManager.list(new LambdaQueryWrapper<MedalTaskRuleTarget>().in(MedalTaskRuleTarget::getMedalTaskRuleId, ruleIds));
            if (CollUtil.isEmpty(medalTaskRuleTargets)) {
                return Collections.emptyList();
            }
            Map<Long, MedalTaskRuleTarget> taskRuleTargetMap = medalTaskRuleTargets.stream().collect(Collectors.toMap(MedalTaskRuleTarget::getMedalTaskRuleId, Function.identity()));

            List<MedalTaskRule> matchRuleList = new ArrayList<>();
            // 匹配命中具体规则
            for (MedalTaskRule taskRule : taskRules) {

                // 点评次数/点评总分匹配规则
                if (CollUtil.newArrayList(MedalRuleTypeEnum.COMMENT_NUM.getCode(), MedalRuleTypeEnum.COMMENT_TOTAL.getCode()).contains(taskRule.getType())) {
                    if (Objects.isNull(taskRuleTargetMap.get(taskRule.getId()))) {
                        continue;
                    }
                    MedalTaskRuleTarget medalTaskRuleTarget = taskRuleTargetMap.get(taskRule.getId());
                    MedalTaskRuleTargetDTO behaviourRuleDetailDTO = taskRuleConvert.toMedalTaskRuleTargetDTO(medalTaskRuleTarget);

                    // 模块code不相等
                    if (Objects.nonNull(behaviourRuleDetailDTO.getModuleCode()) && !behaviourRuleDetailDTO.getModuleCode().equals(ruleBehaviour.getModuleCode())) {
                        continue;
                    }

                    // 分组不相等
                    if (Objects.nonNull(behaviourRuleDetailDTO.getGroupId()) && !behaviourRuleDetailDTO.getGroupId().equals(ruleBehaviour.getGroupId())) {
                        continue;
                    }

                    // 指标不相等
                    if (Objects.nonNull(behaviourRuleDetailDTO.getTargetId()) && !behaviourRuleDetailDTO.getTargetId().equals(ruleBehaviour.getTargetId())) {
                        continue;
                    }

                    // 点评项不相等
                    if (Objects.nonNull(behaviourRuleDetailDTO.getOptionId()) && !behaviourRuleDetailDTO.getOptionId().equals(ruleBehaviour.getOptionId())) {
                        continue;
                    }
                    taskRule.setContent(JSONUtil.toJsonStr(behaviourRuleDetailDTO));
                    matchRuleList.add(taskRule);
                }

                // 后期: 其他规则的匹配规则
            }
            // 命中的行为数据落表
            saveRuleBehaviour(matchRuleList, ruleBehaviour);

            // 生成明细的操作记录
            saveMedalUserOperationRecord(matchRuleList, ruleBehaviour);

            return matchRuleList;
        } catch (Exception e) {
            log.error("[争章活动]-[匹配任务规则]-[步骤1]-[命中规则]-获取命中规则列表异常],行为记录信息:{}", ruleBehaviour, e);
        }

        return Collections.emptyList();
    }

    /**
     * 命中规则的行为数据落表
     *
     * @param matchRuleTasks 匹配的活动规则
     * @param ruleBehaviour  行为记录信息
     * @return
     */
    private void saveMedalUserOperationRecord(List<MedalTaskRule> matchRuleTasks, RuleBehaviourInfoDTO ruleBehaviour) {
        for (MedalTaskRule matchRuleTask : matchRuleTasks) {
            // 如果是新增的记录
            if (Constant.NO.equals(ruleBehaviour.getDeleted())) {
                List<MedalUserOperationRecord> operationRecords = medalUserOperationRecordManager.list(new LambdaQueryWrapper<MedalUserOperationRecord>() // NOSONAR
                        .eq(MedalUserOperationRecord::getType, 1)
                        .eq(MedalUserOperationRecord::getMedalActivityId, matchRuleTask.getMedalActivityId())
                        .eq(MedalUserOperationRecord::getMedalTaskId, matchRuleTask.getMedalTaskId())
                        .eq(MedalUserOperationRecord::getMedalTaskRuleId, matchRuleTask.getId())
                        .eq(MedalUserOperationRecord::getStudentId, ruleBehaviour.getStudentId())
                        .eq(MedalUserOperationRecord::getBehaviourRecordId, ruleBehaviour.getBehaviourRecordId()));
                if (CollUtil.isNotEmpty(operationRecords)) {
                    // 如果是体测数据,更新最新分值
                    if (DataSourceEnum.SPORT.getCode().equals(ruleBehaviour.getDataSource())) {
                        operationRecords.forEach(s -> s.setScore(ruleBehaviour.getScore()));
                        medalUserOperationRecordManager.updateBatchById(operationRecords);
                    }
                    continue;
                }
                MedalUserOperationRecord medalUserOperationRecord = taskRuleConvert.toMedalUserOperationRecord(matchRuleTask);
                medalUserOperationRecord.setId(null);
                medalUserOperationRecord.setMedalTaskRuleId(matchRuleTask.getId());
                medalUserOperationRecord.setStudentId(ruleBehaviour.getStudentId());
                medalUserOperationRecord.setBehaviourRecordId(ruleBehaviour.getBehaviourRecordId());
                medalUserOperationRecord.setType(1);
                medalUserOperationRecord.setTargetName(ruleBehaviour.getTargetName());
                medalUserOperationRecord.setDataSource(ruleBehaviour.getDataSource());
                medalUserOperationRecord.setOptionName(InfoTypeEnum.OPTION.getCode().equals(ruleBehaviour.getInfoType()) ? ruleBehaviour.getInfoName() : null);
                medalUserOperationRecord.setScore(ruleBehaviour.getScore());
                medalUserOperationRecord.setCreateBy(ruleBehaviour.getCreateBy());
                medalUserOperationRecord.setCreateTime(null);
                medalUserOperationRecord.setUpdateBy(null);
                medalUserOperationRecord.setUpdateTime(null);
                medalUserOperationRecordManager.save(medalUserOperationRecord);
            }

            // 如果是删除的记录
            if (Constant.YES.equals(ruleBehaviour.getDeleted())) {
                List<MedalUserOperationRecord> operationRecords = medalUserOperationRecordManager.list(new LambdaQueryWrapper<MedalUserOperationRecord>() // NOSONAR
                        .eq(MedalUserOperationRecord::getType, 2)
                        .eq(MedalUserOperationRecord::getMedalActivityId, matchRuleTask.getMedalActivityId())
                        .eq(MedalUserOperationRecord::getMedalTaskId, matchRuleTask.getMedalTaskId())
                        .eq(MedalUserOperationRecord::getMedalTaskRuleId, matchRuleTask.getId())
                        .eq(MedalUserOperationRecord::getStudentId, ruleBehaviour.getStudentId())
                        .eq(MedalUserOperationRecord::getBehaviourRecordId, ruleBehaviour.getBehaviourRecordId()));
                if (CollUtil.isNotEmpty(operationRecords)) {
                    continue;
                }
                MedalUserOperationRecord medalUserOperationRecord = taskRuleConvert.toMedalUserOperationRecord(matchRuleTask);
                medalUserOperationRecord.setId(null);
                medalUserOperationRecord.setMedalTaskRuleId(matchRuleTask.getId());
                medalUserOperationRecord.setStudentId(ruleBehaviour.getStudentId());
                medalUserOperationRecord.setBehaviourRecordId(ruleBehaviour.getBehaviourRecordId());
                medalUserOperationRecord.setType(2);
                medalUserOperationRecord.setCreateBy(ruleBehaviour.getCreateBy());
                medalUserOperationRecord.setCreateTime(null);
                medalUserOperationRecord.setUpdateBy(null);
                medalUserOperationRecord.setUpdateTime(null);
                medalUserOperationRecordManager.save(medalUserOperationRecord);
            }
        }

    }

    /**
     * 命中规则的行为数据落表
     *
     * @param matchRuleTasks 匹配的活动规则
     * @param ruleBehaviour  行为记录信息
     * @return
     */
    private Boolean saveRuleBehaviour(List<MedalTaskRule> matchRuleTasks, RuleBehaviourInfoDTO ruleBehaviour) {
        for (MedalTaskRule matchRuleTask : matchRuleTasks) {
            // 判断数据是否已经落表
            List<MedalRuleMatchRecord> medalRuleMatchRecords = medalRuleMatchRecordManager.list(new LambdaQueryWrapper<MedalRuleMatchRecord>() // NOSONAR
                    .eq(MedalRuleMatchRecord::getMedalActivityId, matchRuleTask.getMedalActivityId())
                    .eq(MedalRuleMatchRecord::getMedalTaskId, matchRuleTask.getMedalTaskId())
                    .eq(MedalRuleMatchRecord::getMedalTaskRuleId, matchRuleTask.getId())
                    .eq(MedalRuleMatchRecord::getStudentId, ruleBehaviour.getStudentId())
                    .eq(MedalRuleMatchRecord::getBehaviourRecordId, ruleBehaviour.getBehaviourRecordId()));
            if (CollUtil.isNotEmpty(medalRuleMatchRecords)) {
                continue;
            }
            MedalRuleMatchRecord medalRuleMatchRecord = taskRuleConvert.toMedalBehaviourRecord(matchRuleTask);
            medalRuleMatchRecord.setId(null);
            medalRuleMatchRecord.setMedalTaskRuleId(matchRuleTask.getId());
            medalRuleMatchRecord.setStudentId(ruleBehaviour.getStudentId());
            medalRuleMatchRecord.setBehaviourRecordId(ruleBehaviour.getBehaviourRecordId());
            medalRuleMatchRecord.setCreateBy(ruleBehaviour.getCreateBy());
            medalRuleMatchRecord.setCreateTime(null);
            medalRuleMatchRecord.setUpdateBy(null);
            medalRuleMatchRecord.setUpdateTime(null);
            medalRuleMatchRecordManager.save(medalRuleMatchRecord);
        }
        return Boolean.TRUE;
    }

    /**
     * 根据规则计算一级任务完成情况
     *
     * @param matchRuleTasks 匹配的规则列表
     * @param ruleBehaviour  行为记录信息
     */
    private List<TaskCompleteInfoDTO> calculateSubtask(List<MedalTaskRule> matchRuleTasks, RuleBehaviourInfoDTO ruleBehaviour) {
        // 点评次数/点评总分匹配规则枚举
        List<Integer> ruleEnums = CollUtil.newArrayList(MedalRuleTypeEnum.COMMENT_NUM.getCode(), MedalRuleTypeEnum.COMMENT_TOTAL.getCode());

        try {
            List<TaskCompleteInfoDTO> taskCompleteInfoList = new ArrayList<>();
            // 规则匹配
            for (MedalTaskRule matchRuleTask : matchRuleTasks) {
                TaskCompleteInfoDTO taskCompleteInfoDTO = new TaskCompleteInfoDTO();
                taskCompleteInfoDTO.setMedalTaskRuleId(matchRuleTask.getId());
                taskCompleteInfoDTO.setMedalActivityId(matchRuleTask.getMedalActivityId());
                taskCompleteInfoDTO.setMedalTaskId(matchRuleTask.getMedalTaskId());
                taskCompleteInfoDTO.setTenantId(matchRuleTask.getTenantId());
                taskCompleteInfoDTO.setSchoolId(matchRuleTask.getSchoolId());
                taskCompleteInfoDTO.setCampusId(matchRuleTask.getCampusId());
                taskCompleteInfoDTO.setCampusSectionId(ruleBehaviour.getCampusSectionId());
                taskCompleteInfoDTO.setCampusSectionCode(ruleBehaviour.getCampusSectionCode());
                taskCompleteInfoDTO.setGradeId(ruleBehaviour.getGradeId());
                taskCompleteInfoDTO.setGradeCode(ruleBehaviour.getGradeCode());
                taskCompleteInfoDTO.setClassId(ruleBehaviour.getClassId());
                taskCompleteInfoDTO.setStudentId(ruleBehaviour.getStudentId());

                // 点评次数/点评总分匹配规则
                if (ruleEnums.contains(matchRuleTask.getType())) {
                    // 具体的规则内容
                    MedalTaskRuleTargetDTO behaviourRuleDetailDTO = JSONUtil.toBean(matchRuleTask.getContent(), MedalTaskRuleTargetDTO.class);

                    // 获取规则下匹配的行为记录
                    List<BehaviourRecordDTO> behaviourRecords = medalRuleMatchRecordMapper.listMatchRuleBehaviour(taskCompleteInfoDTO.getMedalTaskRuleId(), ruleBehaviour.getStudentId());
                    if (CollUtil.isEmpty(behaviourRecords)) {
                        log.info("[争章活动]-[匹配任务规则]-[步骤2]-[根据规则计算一级任务完成情况]-[获取匹配数据为空]-行为记录信息{}", ruleBehaviour);
                    }

                    // 匹配不同规则
                    // 根据点评次数计算任务完成数
                    if (MedalRuleTypeEnum.COMMENT_NUM.getCode().equals(matchRuleTask.getType())) {
                        Integer completeNum = 0;

                        // 计算总次数
                        int totalNum = behaviourRecords.size();
                        // 计算任务完成数
                        completeNum = totalNum / Convert.toInt(behaviourRuleDetailDTO.getTargetValue());
                        taskCompleteInfoDTO.setCompleteNum(completeNum);
                    }
                    // 根据点评分数计算任务完成数
                    if (MedalRuleTypeEnum.COMMENT_TOTAL.getCode().equals(matchRuleTask.getType())) {
                        Integer completeNum = 0;

                        // 计算总分
                        BigDecimal totalScore = behaviourRecords.stream().map(BehaviourRecordDTO::getScore).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 如果总分小于0
                        if (totalScore.compareTo(BigDecimal.ZERO) == -1) {
                            completeNum = 0;
                        } else {
                            // 计算任务完成数
                            completeNum = Convert.toInt(totalScore.divideToIntegralValue(behaviourRuleDetailDTO.getTargetValue()));
                        }
                        taskCompleteInfoDTO.setCompleteNum(completeNum);
                    }
                    taskCompleteInfoList.add(taskCompleteInfoDTO);

                }
            }

            return taskCompleteInfoList;
        } catch (Exception e) {
            log.error("[争章活动]-[匹配任务规则]-[步骤2]-[根据规则计算一级任务完成情况]-[获取数据为空],匹配的规则信息:{},行为记录信息{}", matchRuleTasks, ruleBehaviour, e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据一级任务完成情况处理数据库完成情况
     *
     * @param subTaskCompleteInfos 一级任务完成情况
     * @param ruleBehaviour        行为记录信息
     */
    private Boolean handleSubTaskSaveData(List<TaskCompleteInfoDTO> subTaskCompleteInfos, RuleBehaviourInfoDTO ruleBehaviour) {
        try {
            return handleTaskSaveDataStatus(subTaskCompleteInfos, MedalRuleLevelEnum.SUB_LEVEL.getCode(), ruleBehaviour);
        } catch (Exception e) {
            log.error("[争章活动]-[匹配任务规则]-[步骤3]-[根据一级任务完成情况处理数据库完成情况]-[计算异常],一级任务完成信息:{}", subTaskCompleteInfos, e);
        }
        return Boolean.FALSE;
    }

    /**
     * 根据一级任务完成情况计算二级任务完成情况
     *
     * @param taskCompleteInfos 匹配的规则列表
     */
    private List<TaskCompleteInfoDTO> calculateTopTask(List<TaskCompleteInfoDTO> taskCompleteInfos, RuleBehaviourInfoDTO ruleBehaviour) {
        try {
            if (CollUtil.isEmpty(taskCompleteInfos)) {
                return Collections.emptyList();
            }

            List<Long> taskIds = taskCompleteInfos.stream().filter(s -> Objects.nonNull(s.getMedalTaskId())).map(TaskCompleteInfoDTO::getMedalTaskId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(taskIds)) {
                return Collections.emptyList();
            }

            // 查询任务可获得的奖品数
            List<MedalTask> medalTasks = medalTaskManager.listByIds(taskIds);
            Map<Long, Integer> cycleNumMap = medalTasks.stream().collect(Collectors.toMap(MedalTask::getId, MedalTask::getCycleNum));

            // 一级任务完成情况
            List<MedalTaskCompletion> taskCompletions = medalTaskCompletionManager.list(new LambdaQueryWrapper<MedalTaskCompletion>()
                    .eq(MedalTaskCompletion::getStudentId, ruleBehaviour.getStudentId())
                    .in(MedalTaskCompletion::getMedalTaskId, taskIds)
                    .eq(MedalTaskCompletion::getLevel, MedalRuleLevelEnum.SUB_LEVEL.getCode())
                    .in(MedalTaskCompletion::getStatus, CollUtil.newArrayList(MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode(), MedalTaskCompleteStatusEnum.IS_FROZEN.getCode())));
            Map<Long, List<MedalTaskCompletion>> subCompletionMap = taskCompletions.stream().collect(Collectors.groupingBy(MedalTaskCompletion::getMedalTaskId));

            // 二级任务规则(未失效的规则)
            List<MedalTaskRule> topRules = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>()
                    .in(MedalTaskRule::getMedalTaskId, taskIds)
                    .eq(MedalTaskRule::getStatus, Constant.NO)
                    .eq(MedalTaskRule::getLevel, MedalRuleLevelEnum.TOP_LEVEL.getCode()));
            Map<Long, List<MedalTaskRule>> topRuleMap = topRules.stream().collect(Collectors.groupingBy(MedalTaskRule::getMedalTaskId));

            // 获取年级学段班级信息
            TaskCompleteInfoDTO completeInfoDTO = CollUtil.getFirst(taskCompleteInfos);

            List<TaskCompleteInfoDTO> list = new ArrayList<>();
            for (Long medalTaskId : topRuleMap.keySet()) {
                if (CollUtil.isEmpty(topRuleMap.get(medalTaskId))) {
                    continue;
                }
                MedalTaskRule topRule = CollUtil.getFirst(topRuleMap.get(medalTaskId));
                TaskCompleteInfoDTO taskCompleteInfoDTO = taskRuleConvert.toTaskCompleteInfoDTO(topRule);

                // 获取二级任务规则
                OuterLayerRuleDTO outerLayerRuleDTO = JSONUtil.toBean(topRule.getContent(), OuterLayerRuleDTO.class);
                // 获取一级任务完成数据
                List<MedalTaskCompletion> completeInfos = subCompletionMap.get(topRule.getMedalTaskId());

                // 二级任务完成数
                Integer topTaskCompletionNum = 0;
                // 二级任务完成的详情
                List<TopTaskCompleteDetailDTO> topTaskCompleteDetails = new ArrayList<>();
                if (CollUtil.isNotEmpty(completeInfos)) {
                    // 根据一级任务完成数计算二级任务完成数
                    topTaskCompletionNum = calculateTopTaskCompletionNum(completeInfos, topTaskCompletionNum, outerLayerRuleDTO.getDetailTaskNum(), topTaskCompleteDetails);
                    // 二级任务完成数不能大于循环章可获得奖牌数
                    if (Objects.nonNull(cycleNumMap.get(medalTaskId)) && topTaskCompletionNum > cycleNumMap.get(medalTaskId)) {
                        topTaskCompletionNum = cycleNumMap.get(medalTaskId);
                    }
                }
                taskCompleteInfoDTO.setMedalTaskRuleId(topRule.getId());
                taskCompleteInfoDTO.setCompleteNum(topTaskCompletionNum);
                taskCompleteInfoDTO.setCampusSectionId(completeInfoDTO.getCampusSectionId());
                taskCompleteInfoDTO.setCampusSectionCode(completeInfoDTO.getCampusSectionCode());
                taskCompleteInfoDTO.setGradeId(completeInfoDTO.getGradeId());
                taskCompleteInfoDTO.setGradeCode(completeInfoDTO.getGradeCode());
                taskCompleteInfoDTO.setClassId(completeInfoDTO.getClassId());
                taskCompleteInfoDTO.setStudentId(completeInfoDTO.getStudentId());
                taskCompleteInfoDTO.setTopTaskCompleteDetails(topTaskCompleteDetails);
                list.add(taskCompleteInfoDTO);
            }
            return list;
        } catch (Exception e) {
            log.error("[争章活动]-[匹配任务规则]-[步骤4]-[根据规则计算二级任务完成情况]-[获取数据为空],一级任务完成情况:{}", taskCompleteInfos, e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据二级任务完成情况处理数据库完成情况
     *
     * @param topTaskCompleteInfos 二级任务完成情况
     * @param ruleBehaviour        行为记录信息
     */
    private Boolean handleTopTaskSaveData(List<TaskCompleteInfoDTO> topTaskCompleteInfos, RuleBehaviourInfoDTO ruleBehaviour) {
        try {
            return handleTaskSaveDataStatus(topTaskCompleteInfos, MedalRuleLevelEnum.TOP_LEVEL.getCode(), ruleBehaviour);
        } catch (Exception e) {
            log.error("[争章活动]-[匹配任务规则]-[步骤5]-[根据二级任务完成情况处理数据库完成情况]-[计算异常],二级任务完成信息:{}", topTaskCompleteInfos, e);
        }
        return Boolean.FALSE;
    }

    /**
     * 根据二级任务完成情况生成待颁章明细
     *
     * @param topTaskCompleteInfos 二级任务完成明细
     * @param ruleBehaviour        行为数据信息
     */
    private Boolean saveMedalUserAcquireRecord(List<TaskCompleteInfoDTO> topTaskCompleteInfos, RuleBehaviourInfoDTO ruleBehaviour) {
        try {
            if (CollUtil.isEmpty(topTaskCompleteInfos)) {
                return Boolean.FALSE;
            }
            // 二级任务id
            List<Long> topRuleIds = topTaskCompleteInfos.stream().map(TaskCompleteInfoDTO::getMedalTaskRuleId).distinct().collect(Collectors.toList());

            // 获取颁章明细中的数据(去除已完成的)(自动颁发的)
            List<MedalUserAcquireRecord> medalUserAcquireRecords = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                    .eq(MedalUserAcquireRecord::getStudentId, ruleBehaviour.getStudentId())
                    .eq(MedalUserAcquireRecord::getAwardType, MedalAwardTypeEnum.AUTO.getCode())
                    .in(MedalUserAcquireRecord::getMedalTaskRuleId, topRuleIds));
            // 根据规则id分组
            Map<Long, List<MedalUserAcquireRecord>> topRuleGroup = medalUserAcquireRecords.stream().collect(Collectors.groupingBy(MedalUserAcquireRecord::getMedalTaskRuleId));

            // 遍历二级任务
            for (TaskCompleteInfoDTO topTaskCompleteInfo : topTaskCompleteInfos) {
                // 二级任务目标最终完成数
                Integer targetNumber = topTaskCompleteInfo.getCompleteNum();

                // 如果数据库中不存在数据
                if (CollUtil.isEmpty(topRuleGroup.get(topTaskCompleteInfo.getMedalTaskRuleId()))) {
                    // 如果目标任务完成数为0,数据库中也没有数据,则不做任何操作
                    if (targetNumber.equals(Constant.ZERO)) {
                        continue;
                    }
                    // 如果目标任务完成数大于0,数据库中也没有数据,则新增完成的数据
                    if (targetNumber > Constant.ZERO) {
                        // 新增明细数据
                        saveNewAcquireRecord(topTaskCompleteInfo, ruleBehaviour, targetNumber);
                    }
                }

                // 如果数据库中存在数据
                if (CollUtil.isNotEmpty(topRuleGroup.get(topTaskCompleteInfo.getMedalTaskRuleId()))) {
                    List<MedalUserAcquireRecord> acquireRecords = topRuleGroup.get(topTaskCompleteInfo.getMedalTaskRuleId());
                    // 待颁发的
                    List<MedalUserAcquireRecord> issued = acquireRecords.stream().filter(s -> s.getAwardStatus().equals(MedalAwardStatusEnum.IS_ISSUE.getCode())).collect(Collectors.toList());
                    // 已撤回的
                    List<MedalUserAcquireRecord> isWithdraw = acquireRecords.stream().filter(s -> s.getAwardStatus().equals(MedalAwardStatusEnum.AUTO_WITHDRAW.getCode())).collect(Collectors.toList());
                    // 已完成的
                    List<MedalUserAcquireRecord> isComplete = acquireRecords.stream().filter(s -> s.getAwardStatus().equals(MedalAwardStatusEnum.IS_COMPLETION.getCode())).collect(Collectors.toList());
                    // 自动颁发手动撤回
                    List<MedalUserAcquireRecord> artificialWithdraw = acquireRecords.stream().filter(s -> s.getAwardStatus().equals(MedalAwardStatusEnum.ARTIFICIAL_WITHDRAW.getCode())).collect(Collectors.toList());


                    // 如果目标明细完成数为0,数据库中存在数据,需要将待颁发的状态变为已撤回的,完成的不做处理
                    if (targetNumber.equals(Constant.ZERO)) {
                        issued.forEach(s -> {
                            s.setAwardStatus(MedalAwardStatusEnum.AUTO_WITHDRAW.getCode());
                            s.setUpdateBy(ruleBehaviour.getUpdateBy());
                        });
                        medalUserAcquireRecordManager.updateBatchById(issued);
                    }
                    // 如果目标明细完成数大于数据库的数量,则需要将数据库中未完成的状态改为已完成,同时新增数据
                    if (targetNumber > acquireRecords.size()) {
                        // 将数据库中已撤回的状态改为待颁发的
                        if (CollUtil.isNotEmpty(isWithdraw)) {
                            isWithdraw.forEach(s -> {
                                s.setAwardStatus(MedalAwardStatusEnum.IS_ISSUE.getCode());
                                s.setUpdateBy(ruleBehaviour.getUpdateBy());
                            });
                            medalUserAcquireRecordManager.updateBatchById(isWithdraw);
                        }
                        // 新增数据
                        Integer newSaveNum = targetNumber - acquireRecords.size();

                        // 如果存在自动颁发手动撤回的数据,则不再生成明细数据
                        if (CollUtil.isEmpty(artificialWithdraw)) {
                            // 新增明细数据
                            saveNewAcquireRecord(topTaskCompleteInfo, ruleBehaviour, newSaveNum);
                        }
                    }

                    // 如果目标明细数不为0,但是小于数据库中的数量
                    if (targetNumber > Constant.ZERO && targetNumber <= acquireRecords.size()) {
                        // 剩余需要完成的任务数
                        Integer remainCompletionNum;
                        // 判断是否大于(已完成+自动颁发手动撤回)的明细数,需要减去(已完成+自动颁发手动撤回)
                        if (targetNumber > (isComplete.size() + artificialWithdraw.size())) {
                            remainCompletionNum = targetNumber - (isComplete.size() + artificialWithdraw.size());
                        } else {
                            remainCompletionNum = 0;
                        }

                        // 比较剩余目标数和数据库中的待颁发数
                        // 数量相等,无需处理
                        if (ObjectUtil.equals(remainCompletionNum, issued.size())) {
                            continue;
                        }
                        // 剩余目标数大于待颁发数,需要将数据库中部分已撤回的状态改为待颁发
                        if (remainCompletionNum > issued.size()) {
                            int num = remainCompletionNum - issued.size();
                            List<MedalUserAcquireRecord> collect = isWithdraw.stream().limit(num).collect(Collectors.toList());
                            collect.forEach(s -> {
                                s.setAwardStatus(MedalAwardStatusEnum.IS_ISSUE.getCode());
                                s.setUpdateBy(ruleBehaviour.getUpdateBy());
                            });
                            medalUserAcquireRecordManager.updateBatchById(collect);
                        }
                        // 剩余数小于待颁发数,需要将数据库中部分待颁发的状态改为已撤回
                        if (remainCompletionNum < issued.size()) {
                            int num = issued.size() - remainCompletionNum;
                            List<MedalUserAcquireRecord> collect = issued.stream().sorted(Comparator.comparing(MedalUserAcquireRecord::getId).reversed()).limit(num).collect(Collectors.toList());
                            collect.forEach(s -> {
                                s.setAwardStatus(MedalAwardStatusEnum.AUTO_WITHDRAW.getCode());
                                s.setUpdateBy(ruleBehaviour.getUpdateBy());
                            });
                            medalUserAcquireRecordManager.updateBatchById(collect);
                        }
                    }
                }
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[争章活动]-[匹配任务规则]-[步骤7]-[根据二级任务完成情况生成奖章明细]-[处理异常],二级任务完成信息:{},行为记录信息:{}", topTaskCompleteInfos, ruleBehaviour, e);
        }
        return Boolean.FALSE;
    }

    /**
     * 新增明细数据(单条)
     *
     * @param topTaskCompleteInfo     明细完成信息
     * @param medalTaskCompletion     二级任务完成信息
     * @param awardStatus             颁发状态  1：待颁发  2：自动撤回  3：已完成   4：手动撤回
     * @param existArtificialWithdraw 是否存在手动撤回的二级任务数据
     * @return
     */
    private Boolean saveSingleNewAcquireRecord(TaskCompleteInfoDTO topTaskCompleteInfo, MedalTaskCompletion medalTaskCompletion, Integer awardStatus, Boolean existArtificialWithdraw) {
        try {
            if (!MedalRuleLevelEnum.TOP_LEVEL.getCode().equals(topTaskCompleteInfo.getLevel())) {
                return Boolean.TRUE;
            }
            StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(medalTaskCompletion.getSchoolId()), Convert.toLong(medalTaskCompletion.getStudentId()));
            MedalUserAcquireRecord medalUserAcquireRecord = new MedalUserAcquireRecord();
            medalUserAcquireRecord.setTenantId(topTaskCompleteInfo.getTenantId());
            medalUserAcquireRecord.setSchoolId(topTaskCompleteInfo.getSchoolId());
            medalUserAcquireRecord.setCampusId(topTaskCompleteInfo.getCampusId());
            medalUserAcquireRecord.setStudentId(medalTaskCompletion.getStudentId());
            medalUserAcquireRecord.setStudentName(studentVO.getStudentName());
            medalUserAcquireRecord.setCampusSectionId(topTaskCompleteInfo.getCampusSectionId());
            medalUserAcquireRecord.setCampusSectionCode(topTaskCompleteInfo.getCampusSectionCode());
            medalUserAcquireRecord.setGradeId(topTaskCompleteInfo.getGradeId());
            medalUserAcquireRecord.setGradeCode(topTaskCompleteInfo.getGradeCode());
            medalUserAcquireRecord.setClassId(topTaskCompleteInfo.getClassId());
            medalUserAcquireRecord.setClassName(studentVO.getClassName());
            medalUserAcquireRecord.setMedalActivityId(topTaskCompleteInfo.getMedalActivityId());
            medalUserAcquireRecord.setMedalTaskId(topTaskCompleteInfo.getMedalTaskId());
            medalUserAcquireRecord.setMedalTaskRuleId(topTaskCompleteInfo.getMedalTaskRuleId());
            medalUserAcquireRecord.setCompletionId(medalTaskCompletion.getId());
            // 如果存在手动撤回的数据
            if (existArtificialWithdraw) {
                medalUserAcquireRecord.setAwardStatus(MedalAwardStatusEnum.ARTIFICIAL_WITHDRAW.getCode());
            } else {
                medalUserAcquireRecord.setAwardStatus(MedalAwardStatusEnum.IS_ISSUE.getCode());
            }
            medalUserAcquireRecord.setAwardType(MedalAwardTypeEnum.AUTO.getCode());
            medalUserAcquireRecord.setAwardDate(DateUtil.offsetHour(DateUtil.beginOfDay(DateUtil.tomorrow()), 8));
            medalUserAcquireRecord.setCreateBy(medalTaskCompletion.getCreateBy());
            // 设置奖章id
            MedalTask medalTask = medalTaskManager.getById(topTaskCompleteInfo.getMedalTaskId());
            medalUserAcquireRecord.setMedalInfoId(Objects.nonNull(medalTask) ? medalTask.getMedalInfoId() : null);
            medalUserAcquireRecordManager.save(medalUserAcquireRecord);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[争章活动]-[匹配任务规则]-[步骤5]-[根据二级任务完成状态新增明细数据]-[计算异常],二级任务数据库id:{}", medalTaskCompletion.getId(), e);
        }
        return Boolean.FALSE;
    }

    /**
     * 根据二级任务完成状态修改颁章明细状态
     *
     * @param topTaskCompleteInfo     明细完成信息
     * @param completionId            二级任务完成id
     * @param existArtificialWithdraw 是否存在手动撤回的二级任务数据
     * @param handleStatus            任务完成状态  0：未完成  1：完成  2：已冻结 3：已撤回(自动颁发手动撤回)
     * @return
     */
    private Boolean updateAcquireRecordAwardStatus(TaskCompleteInfoDTO topTaskCompleteInfo, Long completionId, Boolean existArtificialWithdraw, Integer handleStatus) {
        try {
            if (!MedalRuleLevelEnum.TOP_LEVEL.getCode().equals(topTaskCompleteInfo.getLevel())) {
                return Boolean.TRUE;
            }
            MedalTaskCompletion taskCompletion = medalTaskCompletionManager.getOne(new LambdaQueryWrapper<MedalTaskCompletion>()
                    .eq(MedalTaskCompletion::getId, completionId)
                    .eq(MedalTaskCompletion::getLevel, MedalRuleLevelEnum.TOP_LEVEL.getCode())
                    .in(MedalTaskCompletion::getStatus, CollUtil.newArrayList(MedalTaskCompleteStatusEnum.NO_COMPLETION.getCode(), MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode())));
            if (Objects.isNull(taskCompletion)) {
                return Boolean.TRUE;
            }

            List<MedalUserAcquireRecord> medalUserAcquireRecords = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                    .eq(MedalUserAcquireRecord::getStudentId, topTaskCompleteInfo.getStudentId())
                    .eq(MedalUserAcquireRecord::getMedalTaskId, topTaskCompleteInfo.getMedalTaskId())
                    .eq(MedalUserAcquireRecord::getMedalTaskRuleId, topTaskCompleteInfo.getMedalTaskRuleId())
                    .eq(MedalUserAcquireRecord::getCompletionId, completionId));

            // 如果存在二级任务,但是颁章明细不存在,说明是异常数据,需要修复异常数据
            if (CollUtil.isEmpty(medalUserAcquireRecords)) {
                // 如果存在手动撤回的数据
                if (existArtificialWithdraw) {
                    return Boolean.TRUE;
                }
                Integer awardStatus = null;
                // 如果二级任务状态为未完成,则颁章明细状态为自动撤回
                if (MedalTaskCompleteStatusEnum.NO_COMPLETION.getCode().equals(handleStatus)) {
                    awardStatus = MedalAwardStatusEnum.AUTO_WITHDRAW.getCode();
                }
                // 如果二级任务状态为已完成,则颁章明细状态为待颁发
                if (MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode().equals(handleStatus)) {
                    awardStatus = MedalAwardStatusEnum.IS_ISSUE.getCode();
                }
                saveSingleNewAcquireRecord(topTaskCompleteInfo, taskCompletion, awardStatus, existArtificialWithdraw);
                return Boolean.TRUE;
            }


            // 颁章明细
            MedalUserAcquireRecord medalUserAcquireRecord = CollUtil.getFirst(medalUserAcquireRecords);
            // 如果二级任务状态为未完成,则颁章明细状态为自动撤回
            if (MedalTaskCompleteStatusEnum.NO_COMPLETION.getCode().equals(handleStatus)) {
                medalUserAcquireRecord.setAwardStatus(MedalAwardStatusEnum.AUTO_WITHDRAW.getCode());
            }
            // 如果二级任务状态为已完成,则颁章明细状态为待颁发
            if (MedalTaskCompleteStatusEnum.IS_COMPLETE.getCode().equals(handleStatus)) {
                // 如果存在手动撤回的数据,后续完成任务也不生成新的明细,二级任务完成,明细状态也不修改
                if (existArtificialWithdraw) {
                    log.info("[争章活动]-[根据二级任务完成状态修改颁章明细状态]-[由于存在手动撤回的二级任务-该二级任务完成,颁章明细状态不作修改]-[二级任务记录id:{}]", completionId);
                    return Boolean.TRUE;
                }
                medalUserAcquireRecord.setAwardStatus(MedalAwardStatusEnum.IS_ISSUE.getCode());
            }
            medalUserAcquireRecordManager.updateById(medalUserAcquireRecord);
            return Boolean.TRUE;


        } catch (Exception e) {
            log.error("[争章活动]-[匹配任务规则]-[步骤5]-[根据二级任务完成状态修改颁章明细状态]-[计算异常],二级任务数据库id:{}", completionId, e);
        }
        return Boolean.FALSE;
    }


    /**
     * 新增明细数据
     *
     * @param topTaskCompleteInfo 明细完成信息
     * @param ruleBehaviour       行为记录
     * @param targetNumber        新增明细数
     * @return
     */
    private Boolean saveNewAcquireRecord(TaskCompleteInfoDTO topTaskCompleteInfo, RuleBehaviourInfoDTO ruleBehaviour, Integer targetNumber) {

        List<MedalUserAcquireRecord> list = new ArrayList<>();
        for (int i = 0; i < targetNumber; i++) {
            StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(ruleBehaviour.getSchoolId()), Convert.toLong(ruleBehaviour.getStudentId()));
            MedalUserAcquireRecord medalUserAcquireRecord = new MedalUserAcquireRecord();
            medalUserAcquireRecord.setTenantId(topTaskCompleteInfo.getTenantId());
            medalUserAcquireRecord.setSchoolId(topTaskCompleteInfo.getSchoolId());
            medalUserAcquireRecord.setCampusId(topTaskCompleteInfo.getCampusId());
            medalUserAcquireRecord.setStudentId(ruleBehaviour.getStudentId());
            medalUserAcquireRecord.setStudentName(studentVO.getStudentName());
            medalUserAcquireRecord.setCampusSectionId(topTaskCompleteInfo.getCampusSectionId());
            medalUserAcquireRecord.setCampusSectionCode(topTaskCompleteInfo.getCampusSectionCode());
            medalUserAcquireRecord.setGradeId(topTaskCompleteInfo.getGradeId());
            medalUserAcquireRecord.setGradeCode(topTaskCompleteInfo.getGradeCode());
            medalUserAcquireRecord.setClassId(topTaskCompleteInfo.getClassId());
            medalUserAcquireRecord.setClassName(studentVO.getClassName());
            medalUserAcquireRecord.setMedalActivityId(topTaskCompleteInfo.getMedalActivityId());
            medalUserAcquireRecord.setMedalTaskId(topTaskCompleteInfo.getMedalTaskId());
            medalUserAcquireRecord.setMedalTaskRuleId(topTaskCompleteInfo.getMedalTaskRuleId());
            medalUserAcquireRecord.setAwardStatus(MedalAwardStatusEnum.IS_ISSUE.getCode());
            medalUserAcquireRecord.setAwardType(MedalAwardTypeEnum.AUTO.getCode());
            medalUserAcquireRecord.setAwardUserId(WebUtil.getStaffId());
            medalUserAcquireRecord.setAwardDate(DateUtil.offsetHour(DateUtil.beginOfDay(DateUtil.tomorrow()), 8));
            medalUserAcquireRecord.setCreateBy(ruleBehaviour.getCreateBy());
            // 设置奖章id
            MedalTask medalTask = medalTaskManager.getById(topTaskCompleteInfo.getMedalTaskId());
            medalUserAcquireRecord.setMedalInfoId(Objects.nonNull(medalTask) ? medalTask.getMedalInfoId() : null);
            list.add(medalUserAcquireRecord);
        }
        medalUserAcquireRecordManager.saveBatch(list);
        return Boolean.TRUE;
    }

    /**
     * 获取单个学生(班级)信息
     *
     * @param schoolId
     * @param studentId
     * @return
     */
    public StudentVO getStudentInfoByStudentId(Long schoolId, Long studentId) {
        log.info("[BasicInfoService层saas服务调用]====获取单个学生信息(行政班)(无权限)");
        try {
            if (ObjectUtil.hasNull(schoolId, studentId)) {
                log.info("根据学号获取学生信息参数,学校或学号为空,查询失败");
                return new StudentVO();
            }
            StudentVO studentVO = new StudentVO();
            // 通过学生id集合查询学生信息
            EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
            eduStudentPageQueryDTO.setSchoolId(schoolId);
            eduStudentPageQueryDTO.setStudentIds(CollUtil.newArrayList(studentId));
            List<EduStudentInfoVO> eduStudentInfoByStudentIds = basicInfoService.queryStudentPageNoAuth(eduStudentPageQueryDTO);
            if (CollUtil.isNotEmpty(eduStudentInfoByStudentIds)) {
                EduStudentInfoVO eduStudentInfoVO = CollUtil.getFirst(eduStudentInfoByStudentIds);

                //封装学生信息
                studentVO.setStudentId(eduStudentInfoVO.getId());
                studentVO.setStudentNo(eduStudentInfoVO.getStudentNo());
                studentVO.setStudentName(eduStudentInfoVO.getStudentName());
                studentVO.setClassId(eduStudentInfoVO.getClassId());
                studentVO.setClassName(eduStudentInfoVO.getClassName());
            }
            log.info("根据学号获取学生信息参数,studentInfo=[{}]", studentVO);
            return studentVO;
        } catch (Exception e) {
            log.error("根据学号获取学生信息失败,schoolId=[{}],studentIds=[{}]", schoolId, studentId, e);
        }
        return new StudentVO();
    }

    /**
     * 根据一级任务完成数递归计算大任务完成数
     *
     * @param completionInfos        一级任务完成信息数据
     * @param topTaskCompletionNum   二级任务完成数
     * @param targetNum              完成二级任务需要的目标数
     * @param topTaskCompleteDetails 二级任务完成的详情
     */
    private Integer calculateTopTaskCompletionNum(List<MedalTaskCompletion> completionInfos, Integer topTaskCompletionNum, Integer targetNum, List<TopTaskCompleteDetailDTO> topTaskCompleteDetails) {
        // 计算有几种类型的规则
        List<Long> ruleTypeNum = completionInfos.stream().map(MedalTaskCompletion::getMedalRuleId).distinct().collect(Collectors.toList());
        // 判断一级任务的最低完成组合数量是否满足
        if (ruleTypeNum.size() < targetNum) {
            return topTaskCompletionNum;
        }

        // 根据id分组
        Map<Long, List<MedalTaskCompletion>> ruleIdGroup = completionInfos.stream().collect(Collectors.groupingBy(MedalTaskCompletion::getMedalRuleId));
        // 随机筛选出满足一次二级任务完成的规则数(按照顺序取)
        List<Long> removeRuleIds = ruleTypeNum.stream().sorted(Comparator.comparing(s -> s)).limit(targetNum).collect(Collectors.toList());

        // 封装二级任务完成的详情
        TopTaskCompleteDetailDTO topTaskCompleteDetailDTO = new TopTaskCompleteDetailDTO();
        topTaskCompleteDetailDTO.setSubTaskRuleIds(removeRuleIds);
        topTaskCompleteDetails.add(topTaskCompleteDetailDTO);

        // 根据随机筛选出的规则id获取分组,每个分组随机剔除组内一条数据
        // 需要剔除的数据id集合
        List<Long> needRemoveDataIds = new ArrayList<>();
        for (Long removeRuleId : removeRuleIds) {
            List<MedalTaskCompletion> medalTaskCompletions = ruleIdGroup.get(removeRuleId);
            needRemoveDataIds.add(CollUtil.getLast(medalTaskCompletions).getId());
        }
        List<MedalTaskCompletion> remainCompletionInfos = completionInfos.stream().filter(s -> !needRemoveDataIds.contains(s.getId())).collect(Collectors.toList());

        return calculateTopTaskCompletionNum(remainCompletionInfos, topTaskCompletionNum + 1, targetNum, topTaskCompleteDetails);
    }
}
