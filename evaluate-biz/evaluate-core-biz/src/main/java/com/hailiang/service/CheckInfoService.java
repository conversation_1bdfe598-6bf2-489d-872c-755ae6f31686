package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.datastatistics.vo.CheckPermissionVo;
import com.hailiang.model.dto.check.CheckInfoSubmitDTO;
import com.hailiang.model.dto.check.CheckMessageTypeDTO;
import com.hailiang.model.dto.check.JudgeApprovalRecordDTO;
import com.hailiang.model.dto.check.appeal.*;
import com.hailiang.model.dto.query.CheckerClassInfoDTO;
import com.hailiang.model.query.check.CheckInfoDetailQuery;
import com.hailiang.model.query.check.CheckInfoQuery;
import com.hailiang.model.vo.check.CheckInfoDetailVO;
import com.hailiang.model.vo.check.CheckInfoVO;
import com.hailiang.model.vo.check.DutyRecord;
import com.hailiang.model.vo.check.appel.CheckApprovalInfoVO;
import com.hailiang.remote.saas.dto.auth.StaffDataAuthDTO;
import com.hailiang.remote.saas.dto.auth.VerifyStaffDataAuthDTO;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2023/7/17 15:50
 */
public interface CheckInfoService {

    /**
     * 获取检查项表单信息
     *
     * @param query 查询条件
     * @return
     */
    CheckInfoVO getCheckInfo(CheckInfoQuery query);

    /**
     * 获取检查项填报详情
     *
     * @param query 查询条件
     * @return
     */
    CheckInfoDetailVO getCheckInfoDetail(CheckInfoDetailQuery query);

    /**
     * 提交检查项
     *
     * @param dto 提交内容
     * @return
     */
    Boolean saveCheckInfo(CheckInfoSubmitDTO dto);

    Boolean fixSaveCheckInfo(CheckInfoSubmitDTO dto, Date date);

    /**
     * 老师发起申诉
     *
     * @param dto 请求体
     * @return
     */
    Boolean teacherAppeal(CheckAppealInfoDTO dto);

    /**
     * 管理员审核
     *
     * @param dto 请求体
     * @return
     */
    Boolean approvalHandle(CheckApprovalInfoDTO dto);

    /**
     * 检查人主动撤回检查记录
     *
     * @param dto 请求体
     * @return
     */
    Boolean initiativeWithdraw(CheckWithdrawInfoDTO dto, boolean studentMasterFlag);

    /**
     * 获取检查人的值日记录列表
     *
     * @param param 请求体
     * @return
     */
    Page<DutyRecord> listCheckerClassInfos(CheckerClassInfoDTO param);

    /**
     * 管理员查询带审核/已审核记录列表
     *
     * @param dto 请求体
     * @return
     */
    Page<CheckApprovalInfoVO> listApprovalRecord(CheckApprovalQuery dto);

    /**
     * 消息通知url跳转权限校验
     *
     * @param dto
     * @return
     */
    Boolean messageCheckAuthVerify(CheckMessageTypeDTO dto);

    /**
     * 判断周期内是否含有审核中的记录
     *
     * @param dto 请求体
     * @return
     */
    Boolean judgeApprovalRecord(JudgeApprovalRecordDTO dto);

    /**
     * 验证用户数据权限
     *
     * @param classId          班级id
     * @param staffDataAuthDTO 教职工信息
     * @return
     */
     Boolean verifyUserDataAuth(String classId, StaffDataAuthDTO staffDataAuthDTO);

    /**
     * 验证用户数据权限（不支持历史数据）
     *
     * @param verifyStaffDataAuthDTO 验证用户信息
     * @param staffDataAuthDTO       教职工信息
     * @return
     */
    CheckPermissionVo verifyUserDataAuth(VerifyStaffDataAuthDTO verifyStaffDataAuthDTO, StaffDataAuthDTO staffDataAuthDTO);

    /**
     * 获取待审核数量
     *
     * @return
     */
    Long getApprovalNum();

    /**
     * 学生处主任手动调整分值
     *
     * @param dto 请求体
     * @return
     */
    Boolean adjustScore(AdjustScoreDTO dto);
}
