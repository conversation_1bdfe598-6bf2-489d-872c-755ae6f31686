package com.hailiang.service;

import com.hailiang.model.entity.CheckDim;
import com.hailiang.model.dto.query.ListCheckDimBaseInfoDTO;
import com.hailiang.model.vo.CheckDimBaseInfoVO;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> gaoxin
 * @create 2023/7/19 15:50
 */
public interface CheckDimService {

    List<CheckDim> createDefaultTemplates(String tenantId, String schoolId, String campusId);

    List<CheckDim> queryDims();

    Map<Long, String> queryAllCheckDimIdNameMap(String campusId, String sourceType);

    List<CheckDimBaseInfoVO> queryCheckDimBaseInfoList(ListCheckDimBaseInfoDTO query);
}
