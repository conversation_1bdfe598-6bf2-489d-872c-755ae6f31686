package com.hailiang.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.hailiang.base.BaseService;
import com.hailiang.constant.CheckItemExtConstant;
import com.hailiang.constant.Constant;
import com.hailiang.enums.ScoreTypeEnum;
import com.hailiang.enums.SourceTypeEnum;
import com.hailiang.enums.StatusTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.manager.CheckItemManager;
import com.hailiang.model.dto.*;
import com.hailiang.model.entity.CheckItemPO;
import com.hailiang.model.ext.CheckItemExt;
import com.hailiang.request.HeaderRequest;
import com.hailiang.service.CheckItemService;
import com.hailiang.util.UrlUtil;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/20 11:25
 */
@Service
@RequiredArgsConstructor
public class CheckItemServiceImpl extends BaseService implements CheckItemService {


    private final UrlUtil urlUtil;
    private final CheckItemManager itemManager;

    @Override
    public void throwAnExceptionIfItExists(String groupId) {
        Assert.isFalse(itemManager.isExists(groupId), () -> new BizException(BizExceptionEnum.GROUP_REJECT_DELETE));
    }

    @Override
    public List<CheckItemPO> queryOrderedItemsForGroups(List<Long> groupIds) {
        return itemManager.lambdaQuery()
                .select(CheckItemPO::getId,
                        CheckItemPO::getIconUrl,
                        CheckItemPO::getName,
                        CheckItemPO::getScoreType,
                        CheckItemPO::getScoreValue,
                        CheckItemPO::getSortIndex,
                        CheckItemPO::getStatus,
                        CheckItemPO::getGroupId,
                        CheckItemPO::getScore,
                        CheckItemPO::getCext)
                .in(CheckItemPO::getGroupId, groupIds)
                .orderByAsc(Lists.newArrayList(CheckItemPO::getSortIndex, CheckItemPO::getId))
                .list();
    }

    @Override
    public Long convertAndCreateItem(SaveCheckItemDTO dto, String templateId) {

        ScoreControlDTO scoreControlDTO = dto.getScoreControl();

        CheckItemPO checkItemPO = convertDtoToPo(dto, templateId);

        this.parseScoreControlAndFillItem(scoreControlDTO, checkItemPO);

        this.fillExt(scoreControlDTO, dto.getEvaluators(), checkItemPO);

        Assert.isTrue(itemManager.save(checkItemPO), () -> new BizException(BizExceptionEnum.DATA_INSERTION_FAILED));

        return checkItemPO.getId();
    }


    private CheckItemPO convertDtoToPo(SaveCheckItemDTO dto, String templateId) {
        HeaderRequest headerRequest = super.checkRequestHeader();
        String tenantId = headerRequest.getTenantId();
        String schoolId = headerRequest.getSchoolId();
        String campusId = headerRequest.getCampusId();

        CheckItemPO checkItemPO = new CheckItemPO();
        checkItemPO.setId(IdWorker.getId(checkItemPO));
        checkItemPO.setDimId(dto.getDimId());
        checkItemPO.setGroupId(dto.getGroupId());
        checkItemPO.setTemplateId(templateId);
        checkItemPO.setSourceType(SourceTypeEnum.LDHQ.getCode());

        CheckObjDTO checkObj = dto.getCheckObj();
        checkItemPO.setOptStudent(Optional.ofNullable(checkObj.getOptStudent()).orElse(Boolean.FALSE));
        checkItemPO.setCheckObjType(checkObj.getType());

        String iconUrl = dto.getIconUrl().substring(urlUtil.getIconUrlPrefix().length());
        checkItemPO.setIconUrl(iconUrl);
        checkItemPO.setName(dto.getName());
        checkItemPO.setStatus(StatusTypeEnum.ENABLE.getCode());

        //TODO 为什么这里要设置成当前时间？
        checkItemPO.setSortIndex(System.currentTimeMillis());
        checkItemPO.setDeleted(Boolean.FALSE);
        checkItemPO.setYardStatus(dto.getYardStatus());

        checkItemPO.setTenantId(tenantId);
        checkItemPO.setSchoolId(schoolId);
        checkItemPO.setCampusId(campusId);
        return checkItemPO;
    }

    @Override
    public Long convertAndCreateSysItem(SaveCheckItemDTO dto, String templateId) {
        CheckItemPO item = new CheckItemPO();
        item.setId(IdWorker.getId(item));
        item.setDimId(dto.getDimId());
        item.setGroupId(dto.getGroupId());
        item.setTemplateId(templateId);
        item.setSourceType(SourceTypeEnum.LDHQ.getCode());

        CheckObjDTO checkObj = dto.getCheckObj();
        item.setOptStudent(Optional.ofNullable(checkObj.getOptStudent()).orElse(Boolean.FALSE));
        item.setCheckObjType(checkObj.getType());

        String iconUrl = dto.getIconUrl().substring(urlUtil.getIconUrlPrefix().length());
        item.setIconUrl(iconUrl);
        item.setName(dto.getName());
        item.setStatus(StatusTypeEnum.ENABLE.getCode());
        item.setSortIndex(System.currentTimeMillis());
        item.setDeleted(Boolean.FALSE);

        item.setTenantId(Constant.NEGATIVE_ONE.toString());
        item.setSchoolId(Constant.NEGATIVE_ONE.toString());
        item.setCampusId(Constant.NEGATIVE_ONE.toString());

        this.parseScoreControlAndFillItem(dto.getScoreControl(), item);
        this.fillExt(dto.getScoreControl(), dto.getEvaluators(), item);
        Assert.isTrue(itemManager.save(item), () -> new BizException(BizExceptionEnum.DATA_INSERTION_FAILED));

        return item.getId();
    }

    @Override
    public void updateItem(CheckItemPO checkItemPO, UpdateCheckItemDTO dto, String templateId) {

        checkItemPO.setDimId(dto.getDimId());
        checkItemPO.setGroupId(dto.getGroupId());
        checkItemPO.setName(dto.getName());
        checkItemPO.setIconUrl(dto.getIconUrl().substring(urlUtil.getIconUrlPrefix().length()));

        CheckObjDTO checkObj = dto.getCheckObj();
        checkItemPO.setOptStudent(checkObj.getOptStudent());
        checkItemPO.setCheckObjType(checkObj.getType());
        checkItemPO.setYardStatus(dto.getYardStatus());
        checkItemPO.setOptStudent(Optional.ofNullable(checkObj.getOptStudent()).orElse(Boolean.FALSE));
        checkItemPO.setUpdateBy(WebUtil.getStaffId());

        this.parseScoreControlAndFillItem(dto.getScoreControl(), checkItemPO);

        this.fillExt(dto.getScoreControl(), dto.getEvaluators(), checkItemPO);

        if (StringUtils.isNotBlank(templateId)) {
            checkItemPO.setTemplateId(templateId);
        }
        itemManager.updateDiy(checkItemPO);
    }

    @Override
    public void verifyItemExists(Long id) {
        CheckItemPO item = itemManager.getById(id);
        Assert.notNull(item, () -> new BizException(BizExceptionEnum.CHECK_ITEM_NOT_PRESENT));
    }

    @Override
    public CheckItemExt parseExt(String ext) {
        if (StringUtils.isBlank(ext)) return new CheckItemExt();
        return JSONObject.parseObject(ext, CheckItemExt.class);
    }

    /**
     * 根据校区id与来源查检查项map key：维度id value：维度名称
     *
     * @param campusId
     * @param sourceType
     * @return
     */
    @Override
    public Map<Long, String> queryCheckItemIdNameMap(String campusId, String sourceType) {
        return itemManager.queryCheckItemIdNameMap(campusId, sourceType);
    }

    /**
     * 根据校区id和来源查检查项集合
     *
     * @param campusId
     * @param sourceType
     * @return
     */
    @Override
    public List<CheckItemPO> queryCheckItemList(String campusId, String sourceType) {
        return itemManager.queryCheckItemList(campusId, sourceType);
    }

    /**
     * 解析分值控件，并填充到itemPO中
     *
     * @param scoreControl
     * @param checkItemPO
     */
    private void parseScoreControlAndFillItem(ScoreControlDTO scoreControl, CheckItemPO checkItemPO) {

        if (null == scoreControl) {
            checkItemPO.setScoreType(null);
            checkItemPO.setScore(null);
            checkItemPO.setScoreValue(null);
            return;
        }

        Integer scoreType = scoreControl.getType();
        BigDecimal score = scoreControl.getScore();
        if (scoreType == null || score == null) {
            return;
        }

        BigDecimal scoreAbsoluteValue = score.abs();
        checkItemPO.setScoreType(scoreType);
        checkItemPO.setScore(ScoreTypeEnum.PLUS.getCode().equals(scoreType) ? scoreAbsoluteValue : scoreAbsoluteValue.negate());
        checkItemPO.setScoreValue(scoreAbsoluteValue);
    }


    /**
     * 补充扩展字段信息
     *
     * @param scoreControl
     * @param evaluators
     * @param item
     */
    private void fillExt(ScoreControlDTO scoreControl, List<SaveCheckEvaluatorDTO> evaluators, CheckItemPO item) {
        Map<String, Object> extMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(evaluators)) {
            extMap.put(CheckItemExtConstant.EVALUATORS, evaluators);
        }

        if (scoreControl != null) {
            extMap.put(CheckItemExtConstant.SCORE_CONTROL, scoreControl);
        }

        if (!extMap.isEmpty()) {
            item.setCext(JSON.toJSONString(extMap));
        }
    }

}
