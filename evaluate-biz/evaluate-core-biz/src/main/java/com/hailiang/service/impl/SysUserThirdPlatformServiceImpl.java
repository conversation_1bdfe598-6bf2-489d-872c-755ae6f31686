package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.mapper.SysUserThirdPlatformMapper;
import com.hailiang.model.entity.SysUserThirdPlatform;
import com.hailiang.remote.hai.domain.dto.request.SentMsgTokenRequest;
import com.hailiang.service.SysUserThirdPlatformService;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 业务实现
 *
 * <AUTHOR> 2024-02-26 14:02:30
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SysUserThirdPlatformServiceImpl extends ServiceImpl<SysUserThirdPlatformMapper, SysUserThirdPlatform> implements SysUserThirdPlatformService {

    @Value("${third.hai.wechat.common.appid}")
    private String haiWechatAppId;
    @Value("${third.hai.wechat.common.secret}")
    private String haiWechatSecret;
    @Value("${third.hai.webBff}")
    private String haiWechatTokenUrl;

    private final RedisUtil redisUtil;
    private final String OAUTH2_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={}&secret={}&code={}&grant_type=authorization_code";

    @Override
    @Transactional
    public boolean bindHaiWechat(String code, String mobile) {
        // 根据code获取token(顺便获取到了openId)
        String openId = this.getOpenIdByCode(code);
        if (StrUtil.isBlank(openId)) {
            // 日志降级 code被使用会重新获取重新掉接口绑定（已和前端确认）
            log.warn("h5登录-绑定Hai家校公众号，获取openId为空");
            return false;
        }
        // 1. 先查一下是否绑定
        List<SysUserThirdPlatform> sysWxUserOldList = this.list(Wrappers.<SysUserThirdPlatform>lambdaQuery()
                .eq(SysUserThirdPlatform::getSysUserId, mobile)
                .eq(SysUserThirdPlatform::getSysUserType, 1)
                .eq(SysUserThirdPlatform::getThirdUserType, 2)
        );
        if (CollUtil.isEmpty(sysWxUserOldList)) {
            log.info("h5登录-绑定Hai家校公众号，之前未绑定过，准备第一次绑定");
        } else {
            log.info("h5登录-绑定Hai家校公众号，存在绑定信息，准备检查绑定信息是否一致");
            SysUserThirdPlatform sysUserThirdPlatform = sysWxUserOldList.get(0);
            String oldOpenId = sysUserThirdPlatform.getThirdUserId();
            if (ObjectUtil.equal(openId, oldOpenId)) {
                log.info("h5登录-绑定Hai家校公众号，存在绑定信息，openId一致,无需重新绑定，直接返回");
                return true;
            } else {
                log.info("h5登录-绑定Hai家校公众号，存在绑定信息，openId不一致,准备重新绑定");
                // 删除之前的绑定信息
                sysWxUserOldList.forEach(d -> {
                    d.setUpdateBy(Convert.toStr(WebUtil.getStaffId()));
                    d.setUpdateTime(DateUtil.date());
                });
                this.removeBatchByIds(sysWxUserOldList);
                redisUtil.deleteKey(RedisKeyConstants.GET_OPENID_BYMOBILE + mobile);
                log.info("h5登录-绑定Hai家校公众号，存在绑定信息，openId不一致,已删除之前的绑定信息");
            }
        }
        SysUserThirdPlatform sysUserThirdPlatform = new SysUserThirdPlatform();
        sysUserThirdPlatform.setTenantId(WebUtil.getTenantId());
        sysUserThirdPlatform.setSchoolId(WebUtil.getSchoolId());
        sysUserThirdPlatform.setCampusId(WebUtil.getCampusId());
        sysUserThirdPlatform.setSysUserId(mobile);
        sysUserThirdPlatform.setSysUserType(1);
        sysUserThirdPlatform.setThirdUserId(openId);
        sysUserThirdPlatform.setThirdUserType(2);
        log.info("h5登录-绑定Hai家校公众号，已新增绑定信息：{}", JSONUtil.toJsonStr(sysUserThirdPlatform));

        return this.save(sysUserThirdPlatform);
    }

    @Override
    public String getOpenIdByMobile(String mobile) {
        String redisKey = RedisKeyConstants.GET_OPENID_BYMOBILE + mobile;
        String redisOpenId = redisUtil.getStr(redisKey);
        if (StrUtil.isNotBlank(redisOpenId)) {
            return redisOpenId;
        }
        // 固定Hai家校的公众号
        List<SysUserThirdPlatform> sysWxUserList = this.list(Wrappers.<SysUserThirdPlatform>lambdaQuery()
                .eq(SysUserThirdPlatform::getSysUserId, mobile)
                .eq(SysUserThirdPlatform::getSysUserType, 1)
                .eq(SysUserThirdPlatform::getThirdUserType, 2));
        if (CollUtil.isEmpty(sysWxUserList)) {
            return null;
        }
        String openId = sysWxUserList.get(0).getThirdUserId();
        redisUtil.set(redisKey, openId, CacheConstants.TEN_MINUTE);

        return openId;
    }

    /**
     * 从hai 家校获取 openId
     */
    @Override
    public String getOpenIdByMobileFromHai(SentMsgTokenRequest request) {

        String redisKey = RedisKeyConstants.OPENID_BY_MOBILE + request.getMobile() + request.getFlag();

        String redisOpenId = redisUtil.getStr(redisKey);

        if (CharSequenceUtil.isNotBlank(redisOpenId)) {
            return redisOpenId;
        }

        String url = haiWechatTokenUrl;

        log.info("远程请求hai家校获取openId,请求URL：【{}】，入参：【{}】", url, JSONUtil.toJsonStr(request));

        TimeInterval timeInterval = DateUtil.timer();
        String wxResponse = HttpUtil.post(url + "/hai-web-bff/third/user/getWechatInfo", JSONUtil.toJsonStr(request), 6000);

        log.info("远程请求hai家校获取openId,返回值：【{}】，耗时：【{}】", wxResponse, timeInterval.intervalMs());

        JSONObject jsonObject = JSONUtil.parseObj(wxResponse);

        if (ObjectUtil.isNull(jsonObject)) {
            log.error("远程请求hai家校获取openId时报错，request:【{}】", JSONUtil.toJsonStr(request));
            return null;
        }
        JSONObject object = JSONUtil.parseObj(jsonObject.get("data"));
        if (ObjectUtil.isNull(object)) {
            log.error("远程请求hai家校获取openId时报错，request:【{}】", JSONUtil.toJsonStr(request));
            return null;
        }
        String openId = object.getStr("openId");

        redisUtil.set(redisKey, openId, CacheConstants.TEN_MINUTE);

        return openId;
    }

    private String getOpenIdByCode(String code) {
        String url = StrUtil.format(OAUTH2_ACCESS_TOKEN_URL, haiWechatAppId, haiWechatSecret, code);
        String wxResponse = HttpUtil.get(url, 6000);
        log.info("h5登录-绑定Hai家校公众号, 远程请求微信api获取openId,请求appId：{}, code：{},返回值：{}", haiWechatAppId, code, wxResponse);
        JSONObject jsonObject = JSONUtil.parseObj(wxResponse);
        if (jsonObject.containsKey("errcode")) {
            String errCode = jsonObject.getStr("errcode");
            // code已被使用
            if ("40163".equals(errCode)) {
                log.warn("h5登录-绑定Hai家校公众号，code已被使用，绑定失败");
                return null;
            }
            log.warn("h5登录-绑定Hai家校公众号，远程请求微信api获取openId时报错");
            return null;
        }
        return jsonObject.getStr("openid");
    }
}
