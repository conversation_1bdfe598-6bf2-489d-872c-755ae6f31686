package com.hailiang.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hailiang.convert.CheckItemConvert;
import com.hailiang.dto.DeleteDTO;
import com.hailiang.enums.SaaSOnDutyRoleEnum;
import com.hailiang.enums.StatusTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.manager.CheckDimManager;
import com.hailiang.manager.CheckEvaluatorManager;
import com.hailiang.manager.CheckGroupManager;
import com.hailiang.manager.CheckItemManager;
import com.hailiang.model.dto.*;
import com.hailiang.model.dto.save.TargetTemplateSaveDTO;
import com.hailiang.model.entity.CheckDim;
import com.hailiang.model.entity.CheckEvaluatorPO;
import com.hailiang.model.entity.CheckGroupPO;
import com.hailiang.model.entity.CheckItemPO;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.ext.CheckItemExt;
import com.hailiang.model.vo.*;
import com.hailiang.service.*;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.UrlUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 运营后台检查项服务
 *
 * @Description: 运营后台检查项服务
 * @Author: TanJian
 * @Date: Created in 2024-04-18
 * @Version: 1.6.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysCheckItemServiceImpl implements SysCheckItemService {

    private final CheckItemService checkItemService;
    private final CheckGroupService checkGroupService;
    private final CheckItemConvert checkItemConvert;
    private final CheckEvaluatorService checkEvaluatorService;
    private final UrlUtil urlUtil;
    private final CheckItemManager checkItemManager;
    private final TargetTemplateService targetTemplateService;
    private final CheckGroupManager checkGroupManager;
    private final CheckEvaluatorManager checkEvaluatorManager;
    private final CheckDimManager checkDimManager;
    private final TargetService targetService;

    @Override
    public List<CheckItemManagementVO> queryItem(QueryCheckItemDTO dto) {
        String dimId = dto.getDimId();

        List<CheckGroupPO> groups = checkGroupService.queryOrderedGroups(dimId);
        if (CollectionUtils.isEmpty(groups)) {
            return Collections.emptyList();
        }

        List<Long> groupIds = groups.stream().map(CheckGroupPO::getId).collect(Collectors.toList());
        List<CheckItemPO> allItems = checkItemService.queryOrderedItemsForGroups(groupIds);
        Map<String, List<CheckItemPO>> itemsByGroupId = allItems.stream().collect(Collectors.groupingBy(CheckItemPO::getGroupId));

        return this.fillCheckItems(groups, itemsByGroupId);
    }

    @Override
    public CheckItemFormVO findOne(CheckItemDTO dto) {
        CheckItemPO item = checkItemManager.getById(Long.parseLong(dto.getId()));
        Assert.notNull(item, () -> new BizException(BizExceptionEnum.CHECK_ITEM_NOT_PRESENT));

        CheckItemFormVO checkItemFormVO = new CheckItemFormVO();
        checkItemFormVO.setId(item.getId());
        checkItemFormVO.setDimId(item.getDimId());
        checkItemFormVO.setGroupId(item.getGroupId());
        checkItemFormVO.setIconUrl(urlUtil.getIconUrlPrefix().concat(item.getIconUrl()));
        checkItemFormVO.setName(item.getName());

        CheckObjDTO checkObjDTO = new CheckObjDTO();
        checkObjDTO.setType(item.getCheckObjType());
        checkObjDTO.setOptStudent(item.getOptStudent());
        checkItemFormVO.setCheckObj(checkObjDTO);

        TargetTemplate targetTemplate = targetTemplateService.get(item.getTemplateId());
        checkItemFormVO.setTemplateInfoList(targetTemplate.getTemplateInfoList());

        String ext = item.getCext();
        CheckItemExt itemExt = checkItemService.parseExt(ext);
        checkItemFormVO.setScoreControl(itemExt.getScoreControl());
        checkItemFormVO.setEvaluators(itemExt.getEvaluators());

        return checkItemFormVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createItem(SaveCheckItemDTO dto) {
        Long groupId = Long.valueOf(dto.getGroupId());
        CheckGroupPO group = checkGroupManager.getById(groupId);
        Assert.notNull(group, () -> new BizException(BizExceptionEnum.CHECK_GROUP_NOT_PRESENT));
        dto.setDimId(group.getDimId());

        TargetTemplate template = targetTemplateService.create(new TargetTemplateSaveDTO().setTemplateInfoList(dto.getTemplateInfoList()));
        Long checkItemId = checkItemService.convertAndCreateSysItem(dto, template.getId());
        checkEvaluatorService.convertAndCreateSysEvaluator(dto.getEvaluators(), String.valueOf(checkItemId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editItem(UpdateCheckItemDTO dto) {
        Long id = Long.valueOf(dto.getId());
        CheckItemPO item = checkItemManager.getById(id);
        Assert.notNull(item, () -> new BizException(BizExceptionEnum.CHECK_ITEM_NOT_PRESENT));

        Long groupId = Long.valueOf(dto.getGroupId());
        CheckGroupPO group = checkGroupManager.getById(groupId);
        Assert.notNull(group, () -> new BizException(BizExceptionEnum.CHECK_GROUP_NOT_PRESENT));
        dto.setDimId(group.getDimId());

        String templateId = null;

        if (!targetTemplateService.templateChangeOrNot(item.getTemplateId(), dto.getTemplateInfoList())) {
            TargetTemplate template = targetTemplateService.create(new TargetTemplateSaveDTO().setTemplateInfoList(dto.getTemplateInfoList()));
            templateId = template.getId();
        }

        checkItemService.updateItem(item, dto, templateId);

        if (Boolean.FALSE.equals(checkEvaluatorService.checkEvaluatorChangeOrNot(dto.getId(), dto.getEvaluators()))) {
            checkEvaluatorManager.remove(new LambdaQueryWrapper<CheckEvaluatorPO>().eq(CheckEvaluatorPO::getCheckItemId,dto.getId()));
            checkEvaluatorService.convertAndCreateSysEvaluator(dto.getEvaluators(), dto.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteItem(DeleteDTO dto) {
        Long id = Long.valueOf(dto.getId());
        checkItemService.verifyItemExists(id);
        checkItemManager.removeById(Long.valueOf(dto.getId()));
        checkEvaluatorManager.remove(new LambdaQueryWrapper<CheckEvaluatorPO>().eq(CheckEvaluatorPO::getCheckItemId,dto.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyItem(CopyCheckItemDTO dto) {
        Long id = Long.valueOf(dto.getId());
        CheckItemPO item = checkItemManager.getById(id);
        Assert.notNull(item, () -> new BizException(BizExceptionEnum.CHECK_ITEM_NOT_PRESENT));

        TargetTemplate newTemplate = this.copyTemplateAndCreate(item);
        this.fillItemAndCreate(dto, item, newTemplate);
        this.copyEvaluatorAndCreate(id, item);
    }

    @Override
    public void switchItem(SwitchCheckItemDTO dto) {
        Long id = Long.parseLong(dto.getId());
        checkItemService.verifyItemExists(id);
        checkItemManager.update(new LambdaUpdateWrapper<CheckItemPO>().set(CheckItemPO::getStatus, dto.getStatus()).eq(CheckItemPO::getId, id));
    }

    @Override
    public void sortItem(List<SortCheckItemDTO> dtoList) {
        List<CheckItemPO> checkItemPOS = checkItemConvert.toCheckItems(dtoList);
        checkItemManager.updateBatchById(checkItemPOS);
    }

    @Override
    public TemplateVO templateQuery(QueryDynamicFormDTO dto) {
        Long id = Long.parseLong(dto.getCheckItemId());
        CheckItemPO item = checkItemManager.getById(id);
        Assert.notNull(item, () -> new BizException(BizExceptionEnum.CHECK_ITEM_NOT_PRESENT));

        TargetTemplate targetTemplate = targetTemplateService.get(item.getTemplateId());
        TemplateVO templateVO = new TemplateVO();
        templateVO.setTemplateInfoList(targetTemplate.getTemplateInfoList());

        CheckDim dim = checkDimManager.getById(Long.valueOf(item.getDimId()));
        templateVO.setDimName(Optional.ofNullable(dim).orElse(new CheckDim()).getName());
        templateVO.setItemName(item.getName());

        return templateVO;
    }

    @Override
    public List<String> listIcon() {
        return targetService.listIcon();
    }

    @Override
    public List<SaaSOnDutyRoleVO> querySaaSOnDutyRoleList() {
        return SaaSOnDutyRoleEnum.list().stream().map(item -> new SaaSOnDutyRoleVO().setRoleCode(item.getCode()).setRoleName(item.getMessage())).collect(Collectors.toList());
    }

    @Override
    public String getSnowflakeId() {
        return SnowFlakeIdUtil.nextIdStr();
    }

    private List<CheckItemManagementVO> fillCheckItems(List<CheckGroupPO> groups, Map<String, List<CheckItemPO>> itemsByGroupId) {
        return groups.stream()
                .map(group -> {
                    List<CheckItemVO> itemVOS = Optional.ofNullable(itemsByGroupId.get(String.valueOf(group.getId())))
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(item -> {
                                CheckItemVO checkItemVO = checkItemConvert.toCheckItemVO(item);
                                String iconUrl = checkItemVO.getIconUrl();
                                if (StringUtils.isNotBlank(iconUrl)) {
                                    checkItemVO.setIconUrl(urlUtil.getIconUrlPrefix().concat(iconUrl));
                                }
                                CheckItemExt itemExt = checkItemService.parseExt(item.getCext());
                                checkItemVO.setEvaluator(checkEvaluatorService.formatName(itemExt.getEvaluators()));
                                return checkItemVO;
                            })
                            .collect(Collectors.toList());

                    CheckItemManagementVO managementVO = new CheckItemManagementVO();
                    managementVO.setGroupName(group.getName());
                    managementVO.setGroupId(String.valueOf(group.getId()));
                    managementVO.setItems(itemVOS);
                    return managementVO;
                })
                .collect(Collectors.toList());
    }

    private TargetTemplate copyTemplateAndCreate(CheckItemPO item) {
        TargetTemplate oldTemplate = targetTemplateService.get(item.getTemplateId());
        TargetTemplateSaveDTO newTemplate = new TargetTemplateSaveDTO();
        newTemplate.setTemplateInfoList(oldTemplate.getTemplateInfoList());
        return targetTemplateService.create(newTemplate);
    }

    private void fillItemAndCreate(CopyCheckItemDTO dto, CheckItemPO item, TargetTemplate newTemplate) {
        item.setName(dto.getName());
        item.setSortIndex(System.currentTimeMillis());
        item.setId(IdWorker.getId(item));
        item.setTemplateId(newTemplate.getId());
        item.setStatus(StatusTypeEnum.DISABLED.getCode());
        item.setUpdateTime(new Date());
        item.setCreateTime(new Date());
        checkItemManager.save(item);
    }

    private void copyEvaluatorAndCreate(Long id, CheckItemPO item) {
        List<CheckEvaluatorPO> evaluators = checkEvaluatorManager.list(new LambdaQueryWrapper<CheckEvaluatorPO>()
                .eq(CheckEvaluatorPO::getCheckItemId, String.valueOf(id)));
        List<SaveCheckEvaluatorDTO> list = evaluators.stream()
                .map(evaluator -> {
                    SaveCheckEvaluatorDTO evaluatorDTO = new SaveCheckEvaluatorDTO();
                    evaluatorDTO.setName(evaluator.getName());
                    evaluatorDTO.setBusinessCode(evaluator.getBusinessCode());
                    return evaluatorDTO;
                })
                .collect(Collectors.toList());

        checkEvaluatorService.convertAndCreateEvaluator(list, String.valueOf(item.getId()));
    }
}
