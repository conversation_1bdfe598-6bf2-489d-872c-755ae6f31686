package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.constant.Constant;
import com.hailiang.convert.MedalConvert;
import com.hailiang.enums.ActivityTypeEnum;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.manager.*;
import com.hailiang.model.entity.*;
import com.hailiang.model.medal.dto.MedalActivityEditDTO;
import com.hailiang.model.medal.dto.MedalActivityGradeVO;
import com.hailiang.model.medal.dto.MedalActivityQueryDTO;
import com.hailiang.model.medal.dto.MedalActivitySaveDTO;
import com.hailiang.model.medal.vo.MedalActivityVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.service.MedalActivityService;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import com.lark.oapi.core.utils.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: JJL
 * @Date: 2023/5/31 14:29
 */
@Service
@Slf4j
public class MedalActivityServiceImpl implements MedalActivityService {
    @Autowired
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private MedalConvert convert;
    @Autowired
    private MedalActivityManager medalActivityManager;
    @Autowired
    private MedalActivityGradeManager medalActivityGradeManager;
    @Resource
    private MedalTaskManager medalTaskManager;
    @Autowired
    private MedalLogoManager medalLogoManager;
    @Autowired
    private MedalTaskRuleManager medalTaskRuleManager;
    @Autowired
    private MedalInfoManager medalInfoManager;
    @Autowired
    private MedalTaskRuleTargetManager medalTaskRuleTargetManager;

    /**
     * 模块名称
     */
    private final static String MODULE_NAME = "[学生争章-";
    private final static String ACTIVITY_NAME = "(副本{0})";

    @Value("${oss.urlPrefix}")
    private String urlPrefix;

    /**
     * 新增争章活动
     *
     * @param dto
     * @param copyFlag 0：新增，1:复制  复制不做时间校验
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveMedalActivity(MedalActivitySaveDTO dto, Integer copyFlag) {
        log.info(MODULE_NAME + "新增争章活动], 入参:{} ", JSONUtil.toJsonStr(dto));
        if (BeanUtil.isEmpty(dto)) {
            return null;
        }
        //时间校验
        if (Constant.ZERO.equals(copyFlag)) {
            Assert.isFalse(dto.getStartTime().after(dto.getEndTime()) || dto.getStartTime().before(DateUtil.beginOfDay(DateUtil.date())),
                    () -> new BizException(BizExceptionEnum.ACTIVITY_TIME_ERROR.getMessage()));
        }
        //校验该校区下活动名称是否重名
        MedalActivity medalActivity = medalActivityManager.getByName(convert.toMedalActivity(dto));
        Assert.isFalse(BeanUtil.isNotEmpty(medalActivity), () -> new BizException(BizExceptionEnum.MEDAL_ACTIVITY_EXIST.getMessage()));
        long id = SnowFlakeIdUtil.nextId();
        dto.setId(id);
        dto.setTenantId(WebUtil.getTenantId());
        dto.setSchoolId(WebUtil.getSchoolId());
        dto.setCampusId(WebUtil.getCampusId());

        //活动对应的年级
        List<MedalActivityGradeVO> medalActivityGradeVOS = dto.getMedalActivityGradeVOS();
        medalActivityGradeVOS.stream().forEach(s -> {
            s.setMedalActivityId(id);
            s.setTenantId(WebUtil.getTenantId());
            s.setSchoolId(WebUtil.getSchoolId());
            s.setCampusId(WebUtil.getCampusId());
        });
        MedalActivity activity = convert.toMedalActivity(dto);
        //新增活动
        medalActivityManager.save(activity);
        //新增活动对应年级
        medalActivityGradeManager.saveBatch(convert.toMedalActivityGrade(medalActivityGradeVOS));
        return id;
    }

    /**
     * 修改争章活动
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMedalActivity(MedalActivityEditDTO dto) {
        if (BeanUtil.isEmpty(dto)) {
            return;
        }
        log.info(MODULE_NAME + "修改争章活动], 入参:{} ", JSONUtil.toJsonStr(dto));
        MedalActivity activityById = medalActivityManager.getById(dto.getId());
        if (!ActivityTypeEnum.UNPUBLISHED.getCode().equals(activityById.getStatus())
                && !ActivityTypeEnum.UNDERWAY.getCode().equals(activityById.getStatus())
                && !ActivityTypeEnum.NOT_START.getCode().equals(activityById.getStatus())) {
            return;
        }
        Assert.isFalse(dto.getStartTime().after(dto.getEndTime()), () -> new BizException(BizExceptionEnum.ACTIVITY_EDIT_TIME_ERROR.getMessage()));
        //校验该校区下活动名称是否重名
        MedalActivity medalActivity = medalActivityManager.getByName(convert.toEditMedalActivity(dto));
        Assert.isFalse(BeanUtil.isNotEmpty(medalActivity), () -> new BizException(BizExceptionEnum.MEDAL_ACTIVITY_EXIST.getMessage()));

        if (BeanUtil.isEmpty(activityById)) {
            return;
        }
        //活动不同状态下可修改的范围
        if (ActivityTypeEnum.UNPUBLISHED.getCode().equals(activityById.getStatus())) {
            medalActivityGradeManager.remove(new LambdaQueryWrapper<MedalActivityGrade>().eq(MedalActivityGrade::getMedalActivityId, dto.getId()));
            activityById.setName(dto.getName());
            activityById.setDescription(dto.getDescription());
            activityById.setStartTime(dto.getStartTime());
            activityById.setEndTime(dto.getEndTime());
            //活动对应的年级
            List<MedalActivityGradeVO> medalActivityGradeVOS = dto.getMedalActivityGradeVOS();
            medalActivityGradeVOS.stream().forEach(s -> {
                s.setMedalActivityId(dto.getId());
                s.setTenantId(WebUtil.getTenantId());
                s.setSchoolId(WebUtil.getSchoolId());
                s.setCampusId(WebUtil.getCampusId());
            });
            //新增活动对应年级
            medalActivityGradeManager.saveBatch(convert.toMedalActivityGrade(medalActivityGradeVOS));
        } else if (ActivityTypeEnum.UNDERWAY.getCode().equals(activityById.getStatus())) {
            activityById.setName(dto.getName());
            activityById.setDescription(dto.getDescription());
            activityById.setEndTime(dto.getEndTime());
        } else if (ActivityTypeEnum.NOT_START.getCode().equals(activityById.getStatus())) {
            activityById.setName(dto.getName());
            activityById.setDescription(dto.getDescription());
            activityById.setEndTime(dto.getEndTime());
            activityById.setStartTime(dto.getStartTime());
            //判断修改后的活动状态
            if (!DateUtil.date().before(dto.getStartTime()) && !DateUtil.date().after(dto.getEndTime())) {
                activityById.setStatus(ActivityTypeEnum.UNDERWAY.getCode());
            }
        }
        medalActivityManager.updateById(activityById);
    }

    /**
     * 查询争章活动(分页)
     *
     * @param dto
     * @return
     */
    @Override
    public Page<MedalActivityVO> pageMedalActivity(MedalActivityQueryDTO dto) {
        Page<MedalActivity> activityPage = medalActivityManager.page(new Page<>(dto.getPageNum(), dto.getPageSize()), new LambdaQueryWrapper<MedalActivity>()
                .eq(MedalActivity::getTenantId, WebUtil.getTenantId())
                .eq(MedalActivity::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalActivity::getCampusId, WebUtil.getCampusId())
                .eq(!Objects.isNull(dto.getStatus()), MedalActivity::getStatus, dto.getStatus())
                .like(StrUtil.isNotBlank(dto.getName()), MedalActivity::getName, dto.getName())
                .orderByDesc(MedalActivity::getCreateTime));
        if (CollectionUtil.isEmpty(activityPage.getRecords())) {
            return new Page<>();
        }
        //该校区所以奖章
        List<MedalInfo> medalInfos = medalInfoManager.listMedalInfos(WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId());
        //该校区的奖章logo
        List<MedalLogo> medalLogos = medalLogoManager.listMedalLogo();
        List<Long> ids = activityPage.getRecords().stream().map(MedalActivity::getId).collect(Collectors.toList());
        //活动对应的年级
        List<MedalActivityGrade> medalActivityGrades = medalActivityGradeManager.listByActivityId(ids);
        //活动对应的任务
        List<MedalTask> medalActivityTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>().in(MedalTask::getMedalActivityId, ids));
        List<Long> taskIds = medalActivityTasks.stream().map(MedalTask::getId).collect(Collectors.toList());
        //任务对应的指标项
        List<MedalTaskRule> medalTaskRules = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>().in(CollectionUtil.isNotEmpty(taskIds), MedalTaskRule::getMedalTaskId, taskIds));
        Page<MedalActivityVO> medalActivityVOPage = convert.toMedalActivityVOPage(activityPage);
        medalActivityVOPage.getRecords().stream().forEach(m -> {
            m.setTypeName(ActivityTypeEnum.getMessageByCode(m.getStatus()));
            List<MedalActivityGrade> activityGrades = medalActivityGrades.stream().filter(s -> s.getMedalActivityId().equals(m.getId())).collect(Collectors.toList());
            m.setMedalActivityGradeVOS(convert.toMedalActivityGradeVO(activityGrades));
            m.setMedalActivityNum(Convert.toInt(medalActivityTasks.stream().filter(s -> s.getMedalActivityId().equals(m.getId())).count()));
            List<Long> medalInfoIds = medalActivityTasks.stream().filter(s -> s.getMedalActivityId().equals(m.getId())).collect(Collectors.toList()).stream().map(MedalTask::getMedalInfoId).collect(Collectors.toList());
            List<MedalInfo> medalInfoList = medalInfos.stream().filter(s -> medalInfoIds.contains(s.getId())).collect(Collectors.toList());
            List<Long> collect = medalInfoList.stream().map(MedalInfo::getLogoId).collect(Collectors.toList());
            List<String> logoUrls = new ArrayList<>();
            collect.stream().forEach(z -> {
                List<String> url = medalLogos.stream().filter(s -> z.equals(s.getId())).collect(Collectors.toList()).stream().map(MedalLogo::getLogoUrl).collect(Collectors.toList());
                logoUrls.add(urlPrefix + url.get(Constant.ZERO));
            });
            m.setMedalLogoList(logoUrls);
            //失效任务
            List<MedalTask> medalTaskList = medalActivityTasks.stream().filter(s -> s.getMedalActivityId().equals(m.getId())).collect(Collectors.toList());
            long taskCount = medalTaskList.stream().filter(s -> Constant.ONE.equals(s.getStatus())).count();
            //失效点评项
            List<MedalTaskRule> taskRules = medalTaskRules.stream().filter(s -> Objects.equals(s.getMedalActivityId(),m.getId())).collect(Collectors.toList());
            long ruleCount = taskRules.stream().filter(s -> Constant.ONE.equals(s.getStatus())).count();
            m.setFailureActivityFlag(Constant.ZERO < Convert.toInt(taskCount) || Constant.ZERO < Convert.toInt(ruleCount));
        });
        return medalActivityVOPage;
    }

    /**
     * 争章活动详情
     *
     * @param id
     * @return
     */
    @Override
    public MedalActivityVO getMedalActivity(Long id) {
        if (BeanUtil.isEmpty(id)) {
            return null;
        }
        MedalActivity byId = medalActivityManager.getById(id);
        if (BeanUtil.isEmpty(byId)) {
            return null;
        }
        MedalActivityVO medalActivityVO = convert.toMedalActivityVO(byId);
        if (BeanUtil.isEmpty(medalActivityVO)) {
            return null;
        }
        //该校区的奖章logo
        List<MedalLogo> medalLogos = medalLogoManager.listMedalLogo();
        //活动对应的年级
        List<MedalActivityGrade> medalActivityGrades = medalActivityGradeManager.listByActivityId(Convert.toList(Long.class, id));
        //活动对应的任务
        List<MedalTask> medalActivityTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>().eq(MedalTask::getMedalActivityId, id));
        //活动对应的指标项
        List<MedalTaskRule> taskRules = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>().eq(MedalTaskRule::getMedalTaskId, id));
        medalActivityVO.setMedalActivityNum(Convert.toInt(medalActivityTasks.stream().filter(s -> s.getMedalActivityId().equals(id)).count()));
        //任务对应想奖章
        List<Long> medalInfoIds = medalActivityTasks.stream().filter(s -> s.getMedalActivityId().equals(id)).collect(Collectors.toList()).stream().map(MedalTask::getMedalInfoId).collect(Collectors.toList());
        List<MedalInfo> medalInfos = medalInfoManager.listMedalInfos(WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId());
        List<MedalInfo> medalInfoList = medalInfos.stream().filter(s -> medalInfoIds.contains(s.getId())).collect(Collectors.toList());
        List<Long> collect = medalInfoList.stream().map(MedalInfo::getLogoId).collect(Collectors.toList());
        List<String> logoUrls = new ArrayList<>();
        collect.stream().forEach(z -> {
            List<String> url = medalLogos.stream().filter(s -> z.equals(s.getId())).collect(Collectors.toList()).stream().map(MedalLogo::getLogoUrl).collect(Collectors.toList());
            logoUrls.add(urlPrefix + url.get(Constant.ZERO));
        });
        medalActivityVO.setMedalLogoList(logoUrls);
        medalActivityVO.setMedalActivityGradeVOS(convert.toMedalActivityGradeVO(medalActivityGrades));
        List<MedalTask> medalTaskList = medalActivityTasks.stream().filter(s -> s.getMedalActivityId().equals(id)).collect(Collectors.toList());
        long count = medalTaskList.stream().filter(s -> Constant.ONE.equals(s.getStatus())).count();
        long ruleCount = taskRules.stream().filter(s -> Constant.ONE.equals(s.getStatus())).count();
        medalActivityVO.setFailureActivityFlag(Constant.ZERO < Convert.toInt(count) || Constant.ZERO < Convert.toInt(ruleCount));
        return medalActivityVO;
    }

    /**
     * 删除争章活动
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMedalActivity(Long id) {
        if (ObjectUtil.isNull(id)) {
            return;
        }
        log.info(MODULE_NAME + "删除争章活动], 入参:{} ", id);
        //活动下的任务
        List<MedalTask> medalTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>().eq(MedalTask::getMedalActivityId, id));
        List<Long> taskIds = medalTasks.stream().map(MedalTask::getId).collect(Collectors.toList());
        //删除活动
        medalActivityManager.removeById(id);
        if (CollectionUtil.isNotEmpty(medalTasks)) {
            //删除活动下面的任务
            medalTaskManager.removeBatchByIds(medalTasks);
        }
        //删除活动对应范围
        medalActivityGradeManager.remove(new LambdaQueryWrapper<MedalActivityGrade>().eq(MedalActivityGrade::getMedalActivityId, id));

        //删除对应的规则
        medalTaskRuleManager.remove(new LambdaQueryWrapper<MedalTaskRule>().eq(MedalTaskRule::getMedalActivityId, id));
        //删除任务详情
        if (CollectionUtil.isNotEmpty(taskIds)) {
            medalTaskRuleTargetManager.remove(new LambdaQueryWrapper<MedalTaskRuleTarget>().in(CollectionUtil.isNotEmpty(taskIds), MedalTaskRuleTarget::getMedalTaskId, taskIds));
        }


    }

    /**
     * 发布争章活动
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishMedalActivity(Long id) {
        if(ObjectUtil.isNull(id)){
            return;
        }
        log.info(MODULE_NAME + "发布争章活动], 入参:{} ", id);
        MedalActivity activityManagerById = medalActivityManager.getById(id);
        if (!ActivityTypeEnum.UNPUBLISHED.getCode().equals(activityManagerById.getStatus())) {
            return;
        }
        // 活动对应的任务
        List<MedalTask> medalActivityTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>().eq(MedalTask::getMedalActivityId, id));
        // 判断是否存在任务
        Assert.isFalse(CollectionUtil.isEmpty(medalActivityTasks), () -> new BizException(BizExceptionEnum.ACTIVITY_NO_TASK_ERROR.getMessage()));
        long count = medalActivityTasks.stream().filter(s -> Constant.ONE.equals(s.getStatus())).count();
        // 判断是否存在失效任务
        Assert.isFalse(Constant.ZERO < Convert.toInt(count), () -> new BizException(BizExceptionEnum.TASK_LOSE_EFFICACY_ERROR.getMessage()));
        // 判断结束时间不能早于当前发布时间
        Assert.isFalse(activityManagerById.getEndTime().before(DateUtil.date()), () -> new BizException(BizExceptionEnum.ACTIVITY_END_TIME_ERROR.getMessage()));
        // 判断是否存在失效指标项，如果失效则toast提示“当前活动存在失效点评项”
        List<Long> taskIds = medalActivityTasks.stream().map(MedalTask::getId).collect(Collectors.toList());
        List<MedalTaskRule> medalTaskRules = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>().in(MedalTaskRule::getMedalTaskId, taskIds));
        // 存在失效指标项的任务
        List<MedalTaskRule> loseTaskRuleList = medalTaskRules.stream().filter(s -> Constant.ONE.equals(s.getStatus())).collect(Collectors.toList());
        Assert.isFalse(CollectionUtil.isNotEmpty(loseTaskRuleList), () -> new BizException(BizExceptionEnum.MEDAL_TASK_HAS_LOSE_EFFICACY_ERROR.getMessage()));
        // 存在空的任务规则
        if (CollUtil.isNotEmpty(taskIds)){
            List<MedalTaskRuleTarget> medalTaskRuleTargets = medalTaskRuleTargetManager.list(new LambdaQueryWrapper<MedalTaskRuleTarget>().in(MedalTaskRuleTarget::getMedalTaskId, taskIds));
            medalTaskRuleTargets.forEach(s->{
                Assert.notNull(s.getModuleCode(), () -> new BizException(BizExceptionEnum.EMPTY_RULE_TARGET.getMessage()));
            });
        }


        Integer type;
        Date startTime = activityManagerById.getStartTime();
        Date endTime = activityManagerById.getEndTime();
        // 判断发布后的活动状态
        if (DateUtil.date().before(startTime)) {
            type = ActivityTypeEnum.NOT_START.getCode();
        } else if (DateUtil.date().after(endTime)) {
            type = ActivityTypeEnum.FINISHED.getCode();
        } else {
            type = ActivityTypeEnum.UNDERWAY.getCode();
        }
        activityManagerById.setStatus(type);
        medalActivityManager.updateById(activityManagerById);
    }

    /**
     * 停用争章活动
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableMedalActivity(Long id) {
        if(ObjectUtil.isNull(id)){
            return;
        }
        log.info(MODULE_NAME + "停用争章活动], 入参:{} ", id);
        MedalActivity activityManagerById = medalActivityManager.getById(id);
        activityManagerById.setStatus(ActivityTypeEnum.LOSE_EFFICACY.getCode());
        medalActivityManager.updateById(activityManagerById);
    }

    /**
     * 复制争章活动
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyMedalActivity(Long id) {
        if(ObjectUtil.isNull(id)){
            return;
        }
        MedalActivityVO medalActivity = getMedalActivity(id);
        if (BeanUtil.isEmpty(medalActivity)) {
            return;
        }
        long count = medalActivityManager.count(new LambdaQueryWrapper<MedalActivity>()
                .eq(MedalActivity::getTenantId, WebUtil.getTenantId())
                .eq(MedalActivity::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalActivity::getCampusId, WebUtil.getCampusId())
                .like(MedalActivity::getName, medalActivity.getName())
        );
        if (Constant.ONE.equals(Convert.toInt(count))) {
            medalActivity.setName(medalActivity.getName() + MessageFormat.format(ACTIVITY_NAME, Constant.EMPTY_STRING));
        } else {
            medalActivity.setName(medalActivity.getName() + MessageFormat.format(ACTIVITY_NAME, Convert.toInt(count) - Constant.ONE));
        }
        medalActivity.setStartTime(DateUtil.beginOfDay(DateUtil.parse(DateUtil.format(medalActivity.getStartTime(), Constant.PATTERN_FORMAT))));
        medalActivity.setEndTime(DateUtil.endOfDay(DateUtil.parse(DateUtil.format(medalActivity.getEndTime(), Constant.PATTERN_FORMAT))).offset(DateField.MILLISECOND, Constant.DATETIME_OFFSET));
        MedalActivitySaveDTO medalActivitySaveDTO = toMedalActivitySaveDTO(medalActivity);
        //新增活动
        Long activityId = saveMedalActivity(medalActivitySaveDTO, Constant.ONE);
        //活动对应的任务
        List<MedalTask> medalActivityTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>().eq(MedalTask::getMedalActivityId, id));
        if (CollectionUtil.isEmpty(medalActivityTasks)) {
            return;
        }
        List<Long> originalTaskIds = medalActivityTasks.stream().map(MedalTask::getId).collect(Collectors.toList());
        //原任务规则详情-点评项内容
        List<MedalTaskRuleTarget> medalTaskRuleTargets = medalTaskRuleTargetManager.list(new LambdaQueryWrapper<MedalTaskRuleTarget>()
                .in(CollectionUtil.isNotEmpty(originalTaskIds), MedalTaskRuleTarget::getMedalTaskId, originalTaskIds));
        //活动下的所有任务的规则
        List<MedalTaskRule> taskRuleList = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>().eq(MedalTaskRule::getMedalActivityId, id));
        List<MedalTaskRule> rules = new ArrayList<>();
        List<MedalTaskRuleTarget> targets = new ArrayList<>();

        medalActivityTasks.stream().forEach(s -> {
            long taskId = SnowFlakeIdUtil.nextId();
            //任务对应规则
            List<MedalTaskRule> taskRules = taskRuleList.stream().filter(sin -> sin.getMedalTaskId().equals(s.getId())).collect(Collectors.toList());
            taskRules.stream().forEach(z -> {
                long ruleId = SnowFlakeIdUtil.nextId();
                List<MedalTaskRuleTarget> taskRuleTargets = medalTaskRuleTargets.stream().filter(m -> m.getMedalTaskRuleId().equals(z.getId())).collect(Collectors.toList());
                z.setMedalActivityId(activityId);
                z.setMedalTaskId(taskId);
                z.setId(ruleId);
                z.setCreateBy(null);
                z.setCreateTime(null);
                z.setUpdateBy(null);
                z.setUpdateTime(null);
                taskRuleTargets.stream().forEach(n -> {
                    n.setId(SnowFlakeIdUtil.nextId());
                    n.setMedalTaskId(taskId);
                    n.setMedalTaskRuleId(ruleId);
                    n.setCreateBy(null);
                    n.setCreateTime(null);
                    n.setUpdateBy(null);
                    n.setUpdateTime(null);
                });
                targets.addAll(taskRuleTargets);
            });
            rules.addAll(taskRules);
            s.setMedalActivityId(activityId);
            s.setId(taskId);
            s.setCreateBy(null);
            s.setCreateTime(null);
            s.setUpdateBy(null);
            s.setUpdateTime(null);
        });
        //复制活动下的任务
        medalTaskManager.saveBatch(medalActivityTasks);
        //复制任务对应规则
        medalTaskRuleManager.saveBatch(rules);
        //复制任务规则详情-点评项内容
        medalTaskRuleTargetManager.saveBatch(targets);

    }

    /**
     * 类型转换
     *
     * @param medalActivity
     * @return
     */
    private MedalActivitySaveDTO toMedalActivitySaveDTO(MedalActivityVO medalActivity) {
        MedalActivitySaveDTO medalActivitySaveDTO = new MedalActivitySaveDTO();
        if (medalActivity.getStartTime() != null) {
            medalActivitySaveDTO.setStartTime(DateUtil.format(medalActivity.getStartTime(), Constant.PATTERN_FORMAT));
        }
        if (medalActivity.getEndTime() != null) {
            medalActivitySaveDTO.setEndTime(DateUtil.format(medalActivity.getEndTime(), Constant.PATTERN_FORMAT));
        }
        medalActivitySaveDTO.setId(medalActivity.getId());
        medalActivitySaveDTO.setTenantId(medalActivity.getTenantId());
        medalActivitySaveDTO.setSchoolId(medalActivity.getSchoolId());
        medalActivitySaveDTO.setCampusId(medalActivity.getCampusId());
        medalActivitySaveDTO.setName(medalActivity.getName());
        List<MedalActivityGradeVO> list = medalActivity.getMedalActivityGradeVOS();
        if (list != null) {
            medalActivitySaveDTO.setMedalActivityGradeVOS(new ArrayList<>(list));
        }
        medalActivitySaveDTO.setDescription(medalActivity.getDescription());
        medalActivitySaveDTO.setStatus(medalActivity.getStatus());
        medalActivitySaveDTO.setStatus(Constant.ONE);
        return medalActivitySaveDTO;
    }

    /**
     * 获取该校区下所有年级（除去幼儿园）
     *
     * @return
     */
    @Override
    public List<EduOrgTreeVO> listGradeByCampusId() {
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(WebUtil.getCampusId()));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.CAMPUS.getCode());
        eduOrgQueryDTO.setEndType(Constant.FOUR);
        eduOrgQueryDTO.setIsTree(Constant.ZERO);
        List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        if (CollectionUtil.isEmpty(eduOrgTreeVOS)) {
            return Collections.EMPTY_LIST;
        }
        List<EduOrgTreeVO> orgTreeVOList = eduOrgTreeVOS.stream().filter(s -> Constant.FOUR.equals(s.getType()) && StrUtil.isNotBlank(s.getCode()) && !s.getCode().startsWith("1")).collect(Collectors.toList());
        return orgTreeVOList;
    }

    /**
     * 争章活动状态转换
     *
     * @param jobStartTime
     */
    @Override
    public void changeMedalActivityStatus(DateTime jobStartTime) {
        List<MedalActivity> medalActivities = medalActivityManager.list(new LambdaQueryWrapper<MedalActivity>()
                .in(MedalActivity::getStatus, Lists.newArrayList(ActivityTypeEnum.NOT_START.getCode(), ActivityTypeEnum.UNDERWAY.getCode())));
        if (CollectionUtil.isEmpty(medalActivities)) {
            return;
        }
        List<MedalTask> medalTasks = medalTaskManager.list();
        //更改未开始和进行中的活动状态
        medalActivities.parallelStream().forEach(m -> {
            Integer type;
            //判断发布后的活动状态
            if (jobStartTime.before(m.getStartTime())) {
                type = ActivityTypeEnum.NOT_START.getCode();
            } else if (jobStartTime.after(m.getEndTime())) {
                type = ActivityTypeEnum.FINISHED.getCode();
            } else {
                type = ActivityTypeEnum.UNDERWAY.getCode();
            }
            m.setStatus(type);
            List<MedalTask> taskList = medalTasks.stream().filter(s -> m.getId().equals(s.getMedalActivityId())).collect(Collectors.toList());
            List<MedalTask> tasks = taskList.stream().filter(s -> Constant.ONE.equals(s.getStatus())).collect(Collectors.toList());
            if (tasks.size() == taskList.size()) {
                m.setStatus(ActivityTypeEnum.LOSE_EFFICACY.getCode());
            }
        });
        medalActivityManager.updateBatchById(medalActivities);
    }
}
