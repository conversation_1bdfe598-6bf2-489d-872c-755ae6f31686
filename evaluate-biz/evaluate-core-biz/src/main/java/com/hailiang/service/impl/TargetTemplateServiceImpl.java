package com.hailiang.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.hailiang.constant.Constant;
import com.hailiang.constant.TargetConstant;
import com.hailiang.helper.InfoHelper;
import com.hailiang.mapper.mongo.TargetTemplateDao;
import com.hailiang.model.dto.save.TargetTemplateSaveDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.service.TargetTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class TargetTemplateServiceImpl implements TargetTemplateService {

    @Resource
    private TargetTemplateDao templateDao;
    @Resource
    private InfoHelper infoHelper;

    /**
     * 保存表单
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TargetTemplate save(TargetTemplateSaveDTO dto) {
        // 结构验证
        // 添加时才校验，表单不存在修改情况，修改会产生新表单
        if (StrUtil.isBlank(dto.getTemplateId())) {
            infoHelper.judgeTemplate(dto.getTemplateInfoList());
        }

        TargetTemplate tp = get(dto.getTemplateId());
        // 表单不存在则添加
        if (ObjectUtil.isEmpty(tp)) {
            // 参数判断
            Assert.isTrue(CollUtil.isNotEmpty(dto.getTemplateInfoList()), "表单信息列表不能为空");
            // 保存表单到MongoDB
            TargetTemplate template = new TargetTemplate()
                    .setTemplateInfoList(dto.getTemplateInfoList());
            return templateDao.save(template);
        }
        // 表单存在则添加指标id
        Assert.isTrue(ObjectUtil.isNotNull(dto.getTargetId()), "指标id不能为空");
        TargetTemplate template = new TargetTemplate().setTargetId(dto.getTargetId());
        template.setId(dto.getTemplateId());
        // 先根据指标id删除原有表单
        TargetTemplate oldTemplate = getByTargetId(dto.getTargetId());
        if (ObjectUtil.isNotNull(oldTemplate)) {
            templateDao.deleteById(oldTemplate.getId());
        }
        // 修改表单
        templateDao.update(template);
        return template;
    }

    @Override
    public Collection<TargetTemplate> bulkSave(List<TargetTemplate> targetTemplates) {
        if(CollectionUtils.isEmpty(targetTemplates)){
            return Collections.emptyList();
        }
        Collection<TargetTemplate> results = null;
        //分批次批量新增
        List<List<TargetTemplate>> partition = Lists.partition(targetTemplates, TargetConstant.TARGET_TEMPLATE_BATCH_INSERT_SIZE);
        for (List<TargetTemplate> templates : partition) {
            Collection<TargetTemplate> templateSaved = templateDao.bulkInsert(templates, TargetTemplate.class);
            if(CollectionUtils.isEmpty(results)){
                results = templateSaved;
            }else{
                results.addAll(templateSaved);
            }
        }
        return results;
    }

    /**
     * 根据表单id查表单
     *
     * @param templateId
     * @return
     */
    @Override
    public TargetTemplate get(String templateId) {
        if (ObjectUtil.isNull(templateId)) {
            return null;
        }
        return templateDao.findById(templateId);
    }

    @Override
    public List<TargetTemplate> getByTargetIdList(List<Long> targetIdList) {
        Query query = new Query();
        query.addCriteria(Criteria.where("targetId").in(targetIdList)).addCriteria(Criteria.where("deleted").is(0));
        return templateDao.find(query);
    }

    /**
     * 根据指标id查表单
     *
     * @param targetId
     * @return
     */
    @Override
    public TargetTemplate getByTargetId(Long targetId) {
        if (ObjectUtil.isNull(targetId)) {
            return null;
        }
        TargetTemplate template = new TargetTemplate().setTargetId(targetId);
        template.setDeleted(Constant.ZERO);
        return templateDao.findOne(templateDao.buildQuery(template));
    }
    /**
     * 根据指标id查表单，取某一时间createTime之前最近的一条Mongodb配置数据
     *
     * @param targetId
     * @return
     */
    @Override
    public TargetTemplate getByTargetIdAndCreateTime(Long targetId, Date createTime) {
        createTime = DateUtil.endOfDay(createTime);
        log.info("获取最近一条指标模版，入参： targetId:{},createTime:{}", targetId, DateUtil.formatDateTime(createTime));
        if (ObjectUtil.hasEmpty(targetId, createTime)) {
            log.info("获取最近一条指标模版，入参为空，返回null");
            return null;
        }
        TargetTemplate template = new TargetTemplate().setTargetId(targetId);

        TargetTemplate templateResult = templateDao.findOne(templateDao.buildQueryWithDeleted(template).addCriteria(Criteria.where("createTime").lt(createTime)));
        log.info("获取最近一条指标模版，结果：{}", JSONUtil.toJsonStr(templateResult));
        return templateResult;
    }
    /**
     * @param targetId
     * @return
     */
    @Override
    public String getTemplateJsonStrByTargetId(Long targetId) {
        if (ObjectUtil.isNull(targetId)) {
            return "";
        }
        TargetTemplate template = getByTargetId(targetId);
        return ObjectUtil.isNull(template) ? "" : JSONUtil.toJsonStr(JSONUtil.parse(template.getTemplateInfoList()));
    }

    @Override
    public boolean templateChangeOrNot(String templateId, List<TemplateInfoSaveDTO> templateInfoList) {
        if (StringUtils.isBlank(templateId) || CollUtil.isEmpty(templateInfoList)) {
            return false;
        }

        TargetTemplate targetTemplate = templateDao.findById(templateId);
        if (ObjectUtil.isNull(targetTemplate) || CollUtil.isEmpty(targetTemplate.getTemplateInfoList())) {
            return false;
        }

        return Objects.equals(JSONUtil.parse(templateInfoList), JSONUtil.parse(targetTemplate.getTemplateInfoList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TargetTemplate create(TargetTemplateSaveDTO dto) {
        TargetTemplate tp = get(dto.getTemplateId());

        // 表单不存在则添加
        if (ObjectUtil.isEmpty(tp)) {
            // 参数判断
            Assert.isTrue(CollUtil.isNotEmpty(dto.getTemplateInfoList()), "表单信息列表不能为空");
            // 保存表单到MongoDB
            TargetTemplate template = new TargetTemplate()
                    .setTemplateInfoList(dto.getTemplateInfoList());
            return templateDao.save(template);
        }

        // 表单存在则添加指标id
        Assert.isTrue(ObjectUtil.isNotNull(dto.getTargetId()), "指标id不能为空");
        TargetTemplate template = new TargetTemplate().setTargetId(dto.getTargetId());
        template.setId(dto.getTemplateId());
        // 先根据指标id删除原有表单
        TargetTemplate oldTemplate = getByTargetId(dto.getTargetId());
        if (ObjectUtil.isNotNull(oldTemplate)) {
            templateDao.deleteById(oldTemplate.getId());
        }
        // 修改表单
        templateDao.update(template);
        return template;
    }

    @Override
    public List<TargetTemplate> listByTemplateIds(Query query) {
        return templateDao.find(query);
    }

    @Override
    public void batchSave(List<TargetTemplate> targetTemplates) {
        if(CollUtil.isEmpty(targetTemplates)){
            log.warn("保存表单数据为空:{}",JSONUtil.toJsonStr(targetTemplates));
            return ;
        }
        targetTemplates.forEach(targetTemplate -> templateDao.save(targetTemplate));
    }
}
