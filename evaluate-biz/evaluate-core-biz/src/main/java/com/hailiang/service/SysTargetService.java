package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.dto.TargetCopyDTO;
import com.hailiang.model.dto.query.TargetIdQuery;
import com.hailiang.model.dto.save.TargetSaveDTO;
import com.hailiang.model.dto.save.TargetSortDTO;
import com.hailiang.model.entity.SysTarget;
import com.hailiang.model.vo.SportGroupTargetVO;
import com.hailiang.model.vo.TargetDetailVO;
import com.hailiang.model.vo.TargetUserQueryVO;
import com.hailiang.remote.saas.dto.staff.SchoolStaffQueryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/29
 */
public interface SysTargetService {
    String getIconUrlPrefix();

    void save(TargetSaveDTO dto);

    void delete(Long targetId);

    void changeStatus(Long targetId, Integer opType);

    void sort(List<TargetSortDTO.InnerSort> sortList);

    TargetDetailVO detail(Long targetId);

    SysTarget get(Long targetId);

    List<String> listIcon();

//    List<TargetListTargetUserVO> listTargetWritePeople(ListTargetWritePeopleDaoDTO dto);


    Page<TargetUserQueryVO> queryStaffListBySchool(SchoolStaffQueryDTO schoolStaffQueryDTO);

    SportGroupTargetVO checkSportGroupAndTarget();

    boolean copy(TargetCopyDTO targetCopyDTO);

    Boolean checkAndCopyTargetAsync(String tenantId, String schoolId, String campusId);
    Long getTenantId(String schoolId);
    Boolean copyTarget(String tenantId, String schoolId, String campusId);

    void setPictureEvaluate(TargetIdQuery query);
    /**
     * 图文点评指标列表
     */
    List<Long> listPictureEvaluates();
}
