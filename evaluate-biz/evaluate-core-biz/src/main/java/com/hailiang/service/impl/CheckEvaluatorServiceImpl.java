package com.hailiang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.hailiang.base.BaseService;
import com.hailiang.constant.Constant;
import com.hailiang.convert.CheckEvaluatorConvert;
import com.hailiang.entity.CheckItemYardPO;
import com.hailiang.enums.EvaluatorTypeEnum;
import com.hailiang.enums.SourceTypeEnum;
import com.hailiang.manager.CheckEvaluatorManager;
import com.hailiang.manager.CheckItemYardManager;
import com.hailiang.model.dto.CheckItemYardDTO;
import com.hailiang.model.dto.SaveCheckEvaluatorDTO;
import com.hailiang.model.entity.CheckEvaluatorPO;
import com.hailiang.request.HeaderRequest;
import com.hailiang.service.CheckEvaluatorService;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/20 11:56
 */
@Service
@RequiredArgsConstructor
public class CheckEvaluatorServiceImpl extends BaseService implements CheckEvaluatorService {

    private static final String SEPARATOR = "、";
    private static final String SOURCE_TYPE = SourceTypeEnum.LDHQ.getCode();
    private static final String EVALUATOR_TYPE = EvaluatorTypeEnum.ROLE.getCode();

    private final CheckEvaluatorManager evaluatorManager;

    private final CheckEvaluatorConvert evaluatorConvert;

    private final CheckItemYardManager checkItemYardManager;


    @Override
    public String formatName(List<SaveCheckEvaluatorDTO> evaluators) {
        if (CollectionUtils.isEmpty(evaluators)) return null;
        return evaluators.stream().map(SaveCheckEvaluatorDTO::getName).collect(Collectors.joining(SEPARATOR));
    }

    @Override
    public void convertAndCreateEvaluator(List<SaveCheckEvaluatorDTO> evaluators, String itemId) {

        HeaderRequest headerRequest = super.checkRequestHeader();
        String tenantId = headerRequest.getTenantId();
        String campusId = headerRequest.getCampusId();
        String schoolId = headerRequest.getSchoolId();
        String staffId = headerRequest.getStaffId();

        List<CheckEvaluatorPO> checkEvaluatorPOS = evaluators.stream()
                .map(evaluator -> {
                    CheckEvaluatorPO checkEvaluatorPO = new CheckEvaluatorPO();
                    checkEvaluatorPO.setId(IdWorker.getId(evaluator));
                    checkEvaluatorPO.setCheckItemId(itemId);
                    checkEvaluatorPO.setSourceType(SOURCE_TYPE);
                    checkEvaluatorPO.setName(evaluator.getName());
                    checkEvaluatorPO.setEvaluatorType(EVALUATOR_TYPE);
                    checkEvaluatorPO.setBusinessCode(evaluator.getBusinessCode());
                    checkEvaluatorPO.setTenantId(tenantId);
                    checkEvaluatorPO.setSchoolId(schoolId);
                    checkEvaluatorPO.setCampusId(campusId);
                    checkEvaluatorPO.setCreateBy(staffId);
                    return checkEvaluatorPO;
                })
                .collect(Collectors.toList());

        evaluatorManager.saveBatchDiy(checkEvaluatorPOS);
    }

    @Override
    public void convertAndCreateYard(List<CheckItemYardDTO> checkItemYardList, Long itemId) {

        HeaderRequest headerRequest = super.checkRequestHeader();
        String tenantId = headerRequest.getTenantId();
        String campusId = headerRequest.getCampusId();
        String schoolId = headerRequest.getSchoolId();
        String staffId = headerRequest.getStaffId();

        Date date = new Date();

        List<CheckItemYardPO> checkItemYardPOList = checkItemYardList.stream()
                .map(yardDTO -> {
                    CheckItemYardPO checkItemYardPO = new CheckItemYardPO();
                    checkItemYardPO.setTenantId(tenantId);
                    checkItemYardPO.setSchoolId(schoolId);
                    checkItemYardPO.setCampusId(campusId);
                    checkItemYardPO.setCheckItemId(itemId);
                    checkItemYardPO.setYardType(yardDTO.getYardType());
                    checkItemYardPO.setYardId(yardDTO.getYardId());
                    checkItemYardPO.setYardName(yardDTO.getYardName());
                    checkItemYardPO.setId(IdWorker.getId(yardDTO));
                    checkItemYardPO.setCreateBy(staffId);
                    checkItemYardPO.setCreateTime(date);
                    checkItemYardPO.setUpdateTime(date);
                    return checkItemYardPO;
                })
                .collect(Collectors.toList());

        checkItemYardManager.saveBatch(checkItemYardPOList);
    }

    @Override
    public void convertAndCreateSysEvaluator(List<SaveCheckEvaluatorDTO> evaluators, String itemId) {
        List<CheckEvaluatorPO> checkEvaluatorPOS = evaluators.stream()
                .map(evaluator -> {
                    CheckEvaluatorPO checkEvaluatorPO = new CheckEvaluatorPO();
                    checkEvaluatorPO.setId(IdWorker.getId(evaluator));
                    checkEvaluatorPO.setCheckItemId(itemId);
                    checkEvaluatorPO.setSourceType(SOURCE_TYPE);
                    checkEvaluatorPO.setName(evaluator.getName());
                    checkEvaluatorPO.setEvaluatorType(EVALUATOR_TYPE);
                    checkEvaluatorPO.setBusinessCode(evaluator.getBusinessCode());
                    checkEvaluatorPO.setTenantId(Constant.NEGATIVE_ONE.toString());
                    checkEvaluatorPO.setSchoolId(Constant.NEGATIVE_ONE.toString());
                    checkEvaluatorPO.setCampusId(Constant.NEGATIVE_ONE.toString());
                    checkEvaluatorPO.setCreateBy(WebUtil.getStaffId());
                    return checkEvaluatorPO;
                })
                .collect(Collectors.toList());

        evaluatorManager.saveBatchDiy(checkEvaluatorPOS);
    }

    @Override
    public Boolean checkEvaluatorChangeOrNot(String itemId, List<SaveCheckEvaluatorDTO> newEvaluatorDTOS) {

        List<CheckEvaluatorPO> oldEvaluators = evaluatorManager.list(new LambdaQueryWrapper<CheckEvaluatorPO>()
                .eq(CheckEvaluatorPO::getCheckItemId, itemId));
        List<CheckEvaluatorPO> newEvaluators = evaluatorConvert.toCheckEvaluators(newEvaluatorDTOS);

        return this.compareLists(oldEvaluators, newEvaluators);
    }

    private boolean compareLists(List<CheckEvaluatorPO> oldEvaluators, List<CheckEvaluatorPO> newEvaluators) {
        if (oldEvaluators.size() != newEvaluators.size()) {
            return false;
        }

        Comparator<CheckEvaluatorPO> codeComparator = Comparator.comparing(CheckEvaluatorPO::getBusinessCode);

        oldEvaluators.sort(codeComparator);
        newEvaluators.sort(codeComparator);

        int pointer1 = 0;
        int pointer2 = 0;
        while (pointer1 < oldEvaluators.size() && pointer2 < newEvaluators.size()) {
            CheckEvaluatorPO evaluator1 = oldEvaluators.get(pointer1);
            CheckEvaluatorPO evaluator2 = newEvaluators.get(pointer2);
            int comparisonResult = codeComparator.compare(evaluator1, evaluator2);
            if (comparisonResult != 0) {
                return false;
            }
            pointer1++;
            pointer2++;
        }

        return true;
    }
}
