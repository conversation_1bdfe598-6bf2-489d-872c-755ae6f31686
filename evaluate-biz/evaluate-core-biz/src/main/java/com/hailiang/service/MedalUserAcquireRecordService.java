package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.entity.MedalUserAcquireRecord;
import com.hailiang.model.medal.dto.MedalStatisticsQueryDTO;
import com.hailiang.model.medal.dto.StudentMedalQueryDTO;
import com.hailiang.model.medal.dto.StudentRankQueryDTO;
import com.hailiang.model.medal.vo.*;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: JJL
 * @Date: 2023/6/16 9:52
 */
public interface MedalUserAcquireRecordService {
    /**
     * 获取该校区下所有班级（除去幼儿园）
     * @return
     */
    List<EduOrgTreeVO> listClassByCampusId();

    /**
     * 获章总数、获章人数、获章列表
     * @param dto
     * @return
     */
    MedalStatisticsVO listMedals(MedalStatisticsQueryDTO dto);
    /**
     * 获章趋势
     * @param dto
     * @return
     */
    List<MedalTrendVO> listMedalTrend(MedalStatisticsQueryDTO dto);

    /**
     * 获章排名
     * @param dto
     * @return
     */
    List<MedalTrendVO> listMedalRank(MedalStatisticsQueryDTO dto);
    /**
     * 学生排名
     * @param dto
     * @return
     */
    Page<StudentMedalRankVO> pageStudentMedalRank(StudentRankQueryDTO dto);

    /**
     * 学生奖章列表
     * @param dto
     * @return
     */
    Page<StudentMedalVO> pageStudentMedal(StudentRankQueryDTO dto);

    /**
     * 个人争章展示学生信息以及其获得的章目
     * @param studentId
     * @return
     */
    StudentMedalInfoVO getStudentInfo(String studentId);

    /**
     * 个人争章展示学生章目下的奖章
     * @param studentId   学生id
     * @param medalCatalogueId   章目id
     * @return
     */
    List<MedalInfoVO> listStudentMedals(String studentId, Long medalCatalogueId);

    /**
     * 学生奖章记录
     * @return
     */
    Page<StudentMedalListVO> pageStudentMedalInfos(Integer pageNum, Integer pageSize, HttpServletRequest request);

    /**
     * 学生奖章记录详情
     * @param id
     * @return
     */
    StudentMedalListVO getStudentMedalInfo(Long id,HttpServletRequest request);

    /**
     * 星动力判断是否有学生个人争章展示按钮
     * @param studentId
     * @return
     */
    MedalMenuVO getMedalMenuFlag(String studentId, String campusId);

    /**
     * 查学生奖章信息
     * @param dto
     * @return
     */
    Page<MedalInfoVO> pageStudentMedalInfo(StudentMedalQueryDTO dto);

    /**
     * 获取获章同学消息通知内容
     * @param acquireRecords 奖章明细内容
     * @return
     */
    List<StudentMedalContentVO> listStudentMedalContent(List<MedalUserAcquireRecord> acquireRecords);

}
