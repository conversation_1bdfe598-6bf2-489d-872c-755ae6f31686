package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hailiang.config.SpecialSubjectConfig;
import com.hailiang.constant.Constant;
import com.hailiang.service.SpecialSubjectService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class SpecialSubjectServiceImpl implements SpecialSubjectService {
    @Resource
    private SpecialSubjectConfig specialSubjectConfig;

    @Override
    public List<String> getCourses(Long campusId) {
        //获取特殊课程配置
        List<SpecialSubjectConfig.School> schools = specialSubjectConfig.getSchools();
        if (CollUtil.isEmpty(schools)) return Collections.emptyList();
        //匹配特殊校区
        Map<Long, SpecialSubjectConfig.School> map = schools.stream().collect(Collectors.toMap(SpecialSubjectConfig.School::getCampusId, Function.identity()));
        SpecialSubjectConfig.School school = map.get(campusId);
        if (ObjectUtil.isNull(school)) return Collections.emptyList();
        //获取二级课程
        List<SpecialSubjectConfig.School.Subject> subjects = school.getSubjects();
        return subjects.stream().filter(subject -> !Constant.STR_ZERO.equals(subject.getParentId())).map(SpecialSubjectConfig.School.Subject::getSubjectCode).collect(Collectors.toList());
    }

    @Override
    public SpecialSubjectConfig.School getSpecialSubjectConfig(Long campusId) {
        //获取特殊课程配置
        List<SpecialSubjectConfig.School> schools = specialSubjectConfig.getSchools();
        if (CollUtil.isEmpty(schools)) return null;

        //匹配特殊校区
        Map<Long, SpecialSubjectConfig.School> map = schools.stream().collect(Collectors.toMap(SpecialSubjectConfig.School::getCampusId, Function.identity()));
        return map.get(campusId);
    }

}
