package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.save.TaskOperateLogSaveDTO;
import com.hailiang.model.entity.TaskOperateLog;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface TaskOperateLogService extends IService<TaskOperateLog> {

    // 保存指标任务操作日志
    boolean saveEvaluateTaskOperateLog(TaskOperateLogSaveDTO dto);


    @Transactional(rollbackFor = Exception.class)
    boolean saveEvaluateTaskOperateLog_Playback(TaskOperateLogSaveDTO dto, Date date);

    boolean saveEvaluateTaskOperateLogs(List<TaskOperateLogSaveDTO> dtos);

    /**
     * 判断当前点评提交人的提交区分度
     * 当老师在1个周内全部点评的比例超过50%时
     * （计算公式：比例=选择全班/全年级/全学段后点评项也一致的点评次数（图文和极速）/总点评次数）
     * @return
     */
    boolean checkBatchReviewTimesAWeek();

    /**
     * 判断当前点评提交人的提交区分度
     * 当老师在1个周内全部点评的比例超过50%时返回true
     * @return
     */
    boolean checkBatchReviewTimesAWeekV2();


}