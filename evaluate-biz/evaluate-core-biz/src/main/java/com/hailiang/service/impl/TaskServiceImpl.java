package com.hailiang.service.impl;

import static java.util.stream.Collectors.groupingBy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.MongoPage;
import com.hailiang.MongoPageConvertUtils;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.convert.TaskConvert;
import com.hailiang.entity.EvaluateBehaviourImportsRecordDetailPO;
import com.hailiang.entity.EvaluateBehaviourRecordExtPO;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.ExamTimeTypeEnum;
import com.hailiang.enums.InfoTypeEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.RecordDataSourceEnum;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.enums.TargetTypeEnum;
import com.hailiang.enums.TaskApprovalEnum;
import com.hailiang.enums.TaskRoleTypeEnum;
import com.hailiang.enums.TaskStatusEnum;
import com.hailiang.enums.TaskUserTypeEnum;
import com.hailiang.enums.speed.SpeedApplyLevelEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.helper.TaskHelper;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.logic.InfoLogic;
import com.hailiang.logic.TargetLogic;
import com.hailiang.logic.TaskLogic;
import com.hailiang.logic.TermLogic;
import com.hailiang.manager.EvaluateBehaviourImportsRecordDetailManager;
import com.hailiang.manager.EvaluateBehaviourRecordExtManager;
import com.hailiang.manager.EvaluateBehaviourRecordOptExtManager;
import com.hailiang.manager.TargetGroupManager;
import com.hailiang.manager.TargetManager;
import com.hailiang.mapper.SpeedTargetGroupMapper;
import com.hailiang.mapper.TaskMapper;
import com.hailiang.model.dto.query.GetEvaluateTaskDetailQueryDTO;
import com.hailiang.model.dto.query.GetTaskDetailForDDDTO;
import com.hailiang.model.dto.query.PageEvaluateTaskHistoryQueryDTO;
import com.hailiang.model.dto.query.PageEvaluateTaskHistoryTeacherQueryDTO;
import com.hailiang.model.dto.query.ParentTaskNodeDTO;
import com.hailiang.model.dto.save.TaskSaveDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO.InnerSubmitOptionInfoSave;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.SpeedTargetGroupPO;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TargetGroup;
import com.hailiang.model.entity.TaskPO;
import com.hailiang.model.entity.mongo.Info;
import com.hailiang.model.entity.mongo.StudentInfo;
import com.hailiang.model.entity.mongo.SubStudentInfo;
import com.hailiang.model.entity.mongo.SubmitInfo;
import com.hailiang.model.response.speed.SpeedRecordResponse;
import com.hailiang.model.response.speed.SpeedRecordStudentDetailResponse;
import com.hailiang.model.response.speed.SpeedRecordStudentResponse;
import com.hailiang.model.vo.GetEvaluateTaskDetailVO;
import com.hailiang.model.vo.GetTaskDetailForDDVO;
import com.hailiang.model.vo.ListAllEvaluateTargetVOModule;
import com.hailiang.model.vo.ListCurrentEvaluateTaskVO;
import com.hailiang.model.vo.ListEvaluateTaskHistorySubVO;
import com.hailiang.model.vo.ListTeacherCurrentEvaluateTaskVO;
import com.hailiang.model.vo.MapVO;
import com.hailiang.model.vo.NodeDetailVo;
import com.hailiang.model.vo.PageEvaluateTaskHistoryVO;
import com.hailiang.model.vo.SubListCurrentEvaluateTaskVO;
import com.hailiang.model.vo.SubmitInfoSubVO;
import com.hailiang.model.vo.TaskSaveVO;
import com.hailiang.model.vo.TeacherApprovalHistoryVO;
import com.hailiang.model.vo.TeacherGroupVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.student.StudentByIdQuery;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasClassManager;
import com.hailiang.saas.SaasSchoolCacheManager;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.dto.StudentDTO;
import com.hailiang.saas.model.dto.StudentPageQueryDTO;
import com.hailiang.saas.model.dto.school.EduOrgQueryV2DTO;
import com.hailiang.saas.model.vo.ClassSubjectDetailVO;
import com.hailiang.saas.model.vo.StudentInfo1VO;
import com.hailiang.saas.model.vo.StudentInfoVO;
import com.hailiang.saas.model.vo.educational.TchClassOpenV2VO;
import com.hailiang.saas.model.vo.school.EduOrgTreeV2VO;
import com.hailiang.saas.model.vo.staff.ClassTeacherInfoVO;
import com.hailiang.saas.model.vo.staff.ClassTeacherInfoVO.ClassTeacher;
import com.hailiang.service.TargetGroupService;
import com.hailiang.service.TargetService;
import com.hailiang.service.TaskService;
import com.hailiang.util.WebUtil;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class TaskServiceImpl extends ServiceImpl<TaskMapper, TaskPO> implements TaskService {


    @Resource
    private EvaluateBehaviourImportsRecordDetailManager evaluateBehaviourImportsRecordDetailManager;
    @Resource
    private TaskLogic taskLogic;
    @Resource
    private InfoLogic infoLogic;
    @Resource
    private TermLogic termLogic;
    @Resource
    private TaskConvert taskConvert;
    @Resource
    private TaskHelper taskHelper;
    @Resource
    private TargetGroupService targetGroupService;
    @Resource
    private TargetLogic targetLogic;
    @Resource
    private TaskService taskService;
    @Resource
    private TargetService targetService;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private SaasStudentManager saasStudentManager;
    @Resource
    private BehaviourRecordManager behaviourRecordManager;
    @Resource
    private SpeedTargetGroupMapper speedTargetGroupMapper;
    @Resource
    private EvaluateBehaviourRecordExtManager evaluateBehaviourRecordExtManager;
    @Resource
    private SaasClassManager saasClassManager;
    @Resource
    private SaasSchoolCacheManager saasSchoolCacheManager;
    @Resource
    private TargetManager targetManager;
    @Resource
    private TargetGroupManager targetGroupManager;
    @Resource
    private EvaluateBehaviourRecordOptExtManager evaluateBehaviourRecordOptExtManager;

    @Autowired(
            required = false
    )
    protected MongoTemplate mongoTemplate;
    @Value("${oss.urlPrefix}")
    private String urlPrefix;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TaskSaveVO> saveEvaluateTasks(List<TaskSaveDTO> dtos) {
        if (CollUtil.isEmpty(dtos)) {
            return new ArrayList<>();
        }
        // 参数校验
        for (TaskSaveDTO dto : dtos) {
            taskHelper.judgeSaveTask(dto);
        }
        // 填充实体
        List<TaskPO> evaluateTaskPOS = taskConvert.toEvaluateTasks(dtos);
        taskLogic.saveBatch(evaluateTaskPOS);
        return taskConvert.toSaveVOs(evaluateTaskPOS);
    }

    @Override
    public Long saveFinishedEvaluateTasks(TaskSaveDTO dto) {
        taskHelper.judgeFinishedSaveTask(dto);
        TaskPO taskPO = taskConvert.toEvaluateTask(dto);
        taskLogic.save(taskPO);
        return taskPO.getId();
    }

    @Override
    public List<ListTeacherCurrentEvaluateTaskVO> listTeacherCurrentEvaluateTask() {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.restart();

        Date now = DateUtil.date();
        //查询当前老师下有多少学生
        String staffId = WebUtil.getStaffId();
//        List<String> studentIds = basicInfoRemote.queryStudentStrByStaffId(staffId);
        List<String> studentIds = redisUtil.getOrAdd("evaluate:listTeacherCurrentEvaluateTask:queryStudentStrByStaffId:" + staffId, () -> basicInfoRemote.queryStudentStrByStaffId(staffId), CacheConstants.HALF_HOUR);

        log.info("[获取老师当前指标]-[step.1.1]-[查询当前老师下有多少学生]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.intervalMs());
        timeInterval.restart();

        //防止数据为空sql查询异常
        if (CollUtil.isEmpty(studentIds)) {
            studentIds.add("1");
        }
        // todo sql 较慢，启用新方法
//        List<TaskPO> evaluateTaskPOS = this.list(new LambdaQueryWrapper<TaskPO>()
//                .eq(TaskPO::getCampusId, WebUtil.getCampusId())
//                .and(s -> s.eq(TaskPO::getSubmitStaffId, WebUtil.getStaffId()).eq(TaskPO::getTaskStatus, TaskStatusEnum.NOT_SUBMIT.getCode())
//                        .or(t -> t.in(TaskPO::getSubmitStaffId, studentIds).eq(TaskPO::getApprovalStatus, TaskApprovalEnum.APPROVAL_ING.getCode()).eq(TaskPO::getTaskStatus, 2).isNull(TaskPO::getReviewTeacherId))
//                        .or(w -> w.isNotNull(TaskPO::getReviewTeacherId).like(TaskPO::getReviewTeacherId, WebUtil.getStaffId()))
//                )
//                .orderByDesc(TaskPO::getCreateTime));
        // 获取当前老师下任务列表
        List<TaskPO> evaluateTaskPOS = taskLogic.listByCondition(WebUtil.getCampusId(), staffId, studentIds);

        log.info("[获取老师当前指标]-[step.1.2]-[查询数据库时间]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.intervalMs());
        timeInterval.restart();

        List<SubListCurrentEvaluateTaskVO> listCurrentEvaluateTaskList = taskConvert.toCurrentEvaluateTaskVOS(evaluateTaskPOS);
        if (CollUtil.isEmpty(listCurrentEvaluateTaskList)) {
            return Collections.emptyList();
        }

        //指标任务的详情id Map(mongodbId)
        Map<Long, String> infoIdMap = new HashMap<>();
        List<Long> taskIds = listCurrentEvaluateTaskList.parallelStream().map(s -> Convert.toLong(s.getTaskId())).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(taskIds)) {
            Query query = new Query(Criteria.where("taskId").in(taskIds).and("deleted").is(0));
            List<Info> infos = mongoTemplate.find(query, Info.class);
            infoIdMap = infos.stream().collect(Collectors.toMap(Info::getTaskId, Info::getId));
        }
        log.info("[获取老师当前指标]-[step.1.3]-[查询mongo]]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.intervalMs());
        timeInterval.restart();

        //图标map
        List<Long> targetIds = evaluateTaskPOS.parallelStream().map(TaskPO::getTargetId).collect(Collectors.toList());
        List<Target> targets = targetLogic.listByIds(targetIds);
        Map<String, String> targetIconUrlMap = targets.parallelStream().collect(Collectors.toMap(i -> String.valueOf(i.getId()), i -> urlPrefix + i.getIconUrl(), (a, b) -> b));

        log.info("[获取老师当前指标]-[step.1.4]-[查询指标信息]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.intervalMs());
        timeInterval.restart();

        //审核状态
        List<Long> noSubmitTaskIds = evaluateTaskPOS.parallelStream().filter(s -> TaskStatusEnum.NOT_SUBMIT.getCode().equals(s.getTaskStatus())).distinct().map(TaskPO::getId).collect(Collectors.toList());

        //学生信息
        Map<Long, String> studentNameMap = new HashMap<>();
        StudentPageQueryDTO studentPageQueryDTO = new StudentPageQueryDTO();
        studentPageQueryDTO.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
        studentPageQueryDTO.setStudentIds(studentIds.stream().map(s -> Convert.toLong(s)).collect(Collectors.toList()));
        List<StudentInfo1VO> studentInfoVOS = saasStudentManager.queryStudentPage(studentPageQueryDTO);
        if (CollUtil.isNotEmpty(studentInfoVOS)) {
            studentNameMap = studentInfoVOS.parallelStream().collect(Collectors.toMap(StudentInfo1VO::getId, StudentInfo1VO::getStudentName));
        }
        log.info("[获取老师当前指标]-[step.1.5]-[基本数据信息获取]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.intervalMs());
        timeInterval.restart();

        //数据填充
        Map<Long, String> finalStudentNameMap = studentNameMap;
        Map<Long, String> finalInfoIdMap = infoIdMap;
        listCurrentEvaluateTaskList.parallelStream().forEach(s -> {
            if (Objects.nonNull(s.getRemindTime())) {
                // 延时几小时
                s.setOverHours(DateUtil.between(s.getRemindTime(), now, DateUnit.HOUR));
            }
            //指标任务详情
            s.setInfoId(finalInfoIdMap.getOrDefault(Convert.toLong(s.getTaskId()), ""));
            //图标
            s.setIconUrl(targetIconUrlMap.get(s.getTargetId()));
            //审核状态,如果不是未填报的就是已经拒绝(重新提交)的状态
            s.setApprovalStatus(noSubmitTaskIds.contains(Convert.toLong(s.getTaskId())) ? 1 : 2);
            //学生姓名
            s.setStudentName(finalStudentNameMap.get(Convert.toLong(s.getSubmitStaffId())));
        });
        //根据时间分组
        Map<Date, List<SubListCurrentEvaluateTaskVO>> groupByTaskDateMap = listCurrentEvaluateTaskList.stream().filter(s -> Objects.nonNull(s.getTaskDate())).collect(groupingBy(s -> DateUtil.parse(DateUtil.format(s.getTaskDate(), "yyyy-MM-dd"), "yyyy-MM-dd")));

        // 数据封装
        List<ListTeacherCurrentEvaluateTaskVO> taskVOList = new ArrayList<>();
        List<SubListCurrentEvaluateTaskVO> teacher = new ArrayList<>();
        for (Date date : groupByTaskDateMap.keySet()) {
            // 时间分组
            ListTeacherCurrentEvaluateTaskVO listCur = new ListTeacherCurrentEvaluateTaskVO();
            listCur.setDate(DateUtil.format(date, "yyyy-MM-dd"));
            if (CollUtil.isNotEmpty(groupByTaskDateMap.get(date))) {
                List<SubListCurrentEvaluateTaskVO> taskList = groupByTaskDateMap.get(date);

                // 根据指标id分组
                Map<String, List<SubListCurrentEvaluateTaskVO>> groupByTaskTargetIdMap = taskList.stream().filter(s -> Objects.nonNull(s.getTargetId())).collect(groupingBy(SubListCurrentEvaluateTaskVO::getTargetId));

                // 第二级的数据(老师自身填报数据)
                teacher.clear();

                List<Integer> approvalRoleCodes = CollUtil.newArrayList(TaskRoleTypeEnum.PARENT.getCode(),TaskRoleTypeEnum.STUDENT.getCode());
                for (String targetId : groupByTaskTargetIdMap.keySet()) {
                    if (CollUtil.isNotEmpty(groupByTaskTargetIdMap.get(targetId))) {
                        // 所有相同名称的指标为一组
                        List<SubListCurrentEvaluateTaskVO> currentTasks = groupByTaskTargetIdMap.get(targetId);

                        // [第二级]---老师的指标任务 根据任务id倒序,获取家长指标前后节点也根据这个排序
                        List<SubListCurrentEvaluateTaskVO> teacherTasks = currentTasks.stream().filter(s -> Objects.nonNull(s.getRoleType())).filter(s -> TaskRoleTypeEnum.TEACHER.getCode().equals(s.getRoleType())).sorted(Comparator.comparing(SubListCurrentEvaluateTaskVO::getTaskId).reversed()).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(teacherTasks)) {
                            //老师的数据
                            List<SubListCurrentEvaluateTaskVO> teacherGroups = teacherTasks.stream().map(s -> {
                                SubListCurrentEvaluateTaskVO teacherLabor = new SubListCurrentEvaluateTaskVO();
                                BeanUtil.copyProperties(s, teacherLabor);
                                return teacherLabor;
                            }).collect(Collectors.toList());
                            teacher.addAll(teacherGroups);
                        }

                        // [第三级]---家长的审核任务(提交时间倒序)
                        List<SubListCurrentEvaluateTaskVO> parentTasks = currentTasks.stream().filter(s -> Objects.nonNull(s.getRoleType())).filter(s -> approvalRoleCodes.contains(s.getRoleType())).sorted(Comparator.comparing(SubListCurrentEvaluateTaskVO::getSubmitTime, Comparator.nullsLast(Date::compareTo)).reversed()).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(parentTasks)) {
                            SubListCurrentEvaluateTaskVO parentGroup = new SubListCurrentEvaluateTaskVO();
                            // 指标名称
                            parentGroup.setTaskName(CollUtil.isNotEmpty(parentTasks) ? CollUtil.getFirst(parentTasks).getTaskName() : "");
                            // 图标
                            parentGroup.setIconUrl(CollUtil.isNotEmpty(parentTasks) ? CollUtil.getFirst(parentTasks).getIconUrl() : "");
                            // 提交时间(最新数据的提交时间)
                            parentGroup.setSubmitTime(CollUtil.isNotEmpty(parentTasks) ? CollUtil.getFirst(parentTasks).getSubmitTime() : null);
                            parentGroup.setTasks(parentTasks);

                            teacher.add(parentGroup);
                        }
                    }
                }
                //二级数据根据根据时间倒序
                List<SubListCurrentEvaluateTaskVO> teacherCollects = teacher.stream().sorted(Comparator.comparing(SubListCurrentEvaluateTaskVO::getSubmitTime, Comparator.nullsLast(Date::compareTo)).reversed()).collect(Collectors.toList());
                listCur.setTasks(teacherCollects);
                taskVOList.add(listCur);
            }
        }
        List<ListTeacherCurrentEvaluateTaskVO> collect = taskVOList.stream().sorted(Comparator.comparing(ListTeacherCurrentEvaluateTaskVO::getDate).reversed()).collect(Collectors.toList());

        log.info("[获取老师当前指标]-[step.1.6]-[数据填充]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.intervalMs());
        timeInterval.restart();
        return collect;
    }

    @Override
    public List<ListAllEvaluateTargetVOModule> listTeacherAllEvaluateTarget() {
        return targetGroupService.listAllEvaluateTarget(TaskUserTypeEnum.TEACHER.getCode());
    }

    @Override
    public MongoPage<PageEvaluateTaskHistoryVO> pageEvaluateTaskHistory(PageEvaluateTaskHistoryTeacherQueryDTO dto) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        //校验参数
        validateTaskHistoryTeacherQueryParam(dto);
        if (CollectionUtils.isEmpty(dto.getClassIdList()) || CollectionUtils.isEmpty(dto.getClassIdStrList())) {
            return MongoPage.getEmptyPage();
        }
        List<TaskPO> taskPOList = taskService.list(new LambdaQueryWrapper<TaskPO>()
                        .eq(TaskPO::getCampusId, WebUtil.getCampusId())
                        .eq(TaskPO::getSubmitStaffId, WebUtil.getStaffId())
                        .eq(TaskPO::getTaskStatus, TaskStatusEnum.SUBMITTED.getCode())
                        .between(Objects.nonNull(dto.getStartTime()) && Objects.nonNull(dto.getEndTime()), TaskPO::getSubmitTime, dto.getStartTime(), dto.getEndTime())
//                .orderByDesc(TaskPO::getId)
        );
        log.info("【H5】-【我的点评】-【查询指标评价任务表】耗时:{}", TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        //极速点评记录
        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.listSpeedRecords(dto);
        log.info("【H5】-【我的点评】-【获取极速点评记录】耗时:{}", TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        if (CollUtil.isEmpty(taskPOList) && CollUtil.isEmpty(behaviourRecords)) {
            log.warn("【获取老师点评记录】【无历史记录】直接返回，数 staffId：{}", WebUtil.getStaffId());
            return MongoPageConvertUtils.convertMongo(new MongoPage<>(Convert.toLong(Constant.ZERO), Convert.toLong(dto.getPageSize()), Convert.toLong(dto.getPageNum()), taskPOList), Collections.emptyList());
        }

        List<PageEvaluateTaskHistoryVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(taskPOList)) {
            //获取任务时间
            Map<Long, Date> taskTimeMap = taskPOList.stream().collect(Collectors.toMap(TaskPO::getId, TaskPO::getTaskDate));
            //根据task表中的infoId获取mongodb信息
            List<Long> taskIds = taskPOList.stream().map(TaskPO::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(taskIds)) {
                List<Info> infos = new ArrayList<>();
                Criteria mongoCondition = Criteria.where("taskId")
                        .in(taskIds)
                        .and("deleted")
                        .is(0);
                if (CollectionUtils.isNotEmpty(dto.getClassIdStrList())) {
                    mongoCondition.and("studentInfos.studentInfos").elemMatch(Criteria
                            .where("classId")
                            .in(dto.getClassIdStrList()));
                }
                Query query = new Query(mongoCondition);
                query.fields().include("studentInfos")
                        .include("taskId")
                        .include("taskName")
                        .include("submitInfoList")
                        .include("id");
                infos.addAll(mongoTemplate.find(query, Info.class));
                log.info("【H5】-【我的点评】-【mongo中查询点评详情】查询语句:{}", query.toString());
                log.info("【H5】-【我的点评】-【mongo中查询点评详情】，查询到的数据量:{},耗时:{}", infos.size(), TIME_INTERVAL.interval());
                TIME_INTERVAL.restart();
                for (Info evaluateInfo : infos) {
                    List<SubmitInfo> submitInfoList = evaluateInfo.getSubmitInfoList();
                    if (CollUtil.isEmpty(submitInfoList)) {
                        continue;
                    }
                    // 前两项的填写情况
                    List<ListEvaluateTaskHistorySubVO> submitInfoSubList = new ArrayList<>();
                    PageEvaluateTaskHistoryVO listEvaluateTaskHistoryVO = new PageEvaluateTaskHistoryVO();
                    listEvaluateTaskHistoryVO.setTaskId(String.valueOf(evaluateInfo.getTaskId()));
                    listEvaluateTaskHistoryVO.setTaskName(evaluateInfo.getTaskName());
                    listEvaluateTaskHistoryVO.setSubmitInfoList(submitInfoSubList);
                    listEvaluateTaskHistoryVO.setInfoId(evaluateInfo.getId());
//                    listEvaluateTaskHistoryVO.setSource(RecordDataSourceEnum.HISTORY.getCode());
                    if (Objects.nonNull(taskTimeMap.get(evaluateInfo.getTaskId()))) {
                        listEvaluateTaskHistoryVO.setDate(DateUtil.format(taskTimeMap.get(evaluateInfo.getTaskId()), "yyyy-MM-dd HH:mm:ss"));
                    }
                    list.add(listEvaluateTaskHistoryVO);
                    // 填充数据
                    taskHelper.fullFillSubVO(submitInfoList, submitInfoSubList, evaluateInfo, false);
                }
            }
        }
        TIME_INTERVAL.restart();
        if (CollUtil.isNotEmpty(behaviourRecords)) {
            //处理新的点评记录
            this.fillNewRecords(behaviourRecords, list);

        }
        log.info("【H5】-【我的点评】-【处理新的点评记录】耗时:{}", TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        List<PageEvaluateTaskHistoryVO> collect = list
                .stream().sorted(Comparator.comparing(PageEvaluateTaskHistoryVO::getDate).reversed())
                .collect(Collectors.toList());
        collect.forEach(s -> s.setDate(DateUtil.format(DateUtil.parse(s.getDate()), "yyyy-MM-dd")));
        // 过滤点评记录
        collect  = this.filterRecordHistory(dto.getScoreType(), collect);
        //模糊搜索
        List<PageEvaluateTaskHistoryVO> taskHistoryVOList = searchByName(dto, collect);


        //将学生明细控件往前移
        orderStudent(dto, taskHistoryVOList);
        // 分页
        List<PageEvaluateTaskHistoryVO> taskHistoryVOS = taskHistoryVOList.stream().distinct().skip((dto.getPageNum() - 1) * dto.getPageSize()).limit(dto.getPageSize()).collect(Collectors.toList());
        return MongoPageConvertUtils.convertMongo(new MongoPage<>(Convert.toLong(taskHistoryVOList.size()), Convert.toLong(dto.getPageSize()), Convert.toLong(dto.getPageNum()), taskPOList), taskHistoryVOS);

    }

    /**
     * 过滤点评记录
     * type 2 仅过滤点评记录中不包含减分项的点评记录
     * type 3 仅过滤点评记录中不包含加分项的点评记录
     * @param type
     * @param taskHistoryVOList
     * @return
     */
    private List<PageEvaluateTaskHistoryVO> filterRecordHistory(Integer type,
                                                                List<PageEvaluateTaskHistoryVO> taskHistoryVOList){
        return taskHistoryVOList.stream().filter(item -> {
            Integer evaluateType = CollUtil.isEmpty(item.getSubmitInfoList()) ? 2 : 1;
            if(Objects.equals(1, evaluateType)){

                // 只看加分和0分
                if(Objects.equals(type, 2)){
                    List<BigDecimal> scoreList = new ArrayList<>();
                    Optional<ListEvaluateTaskHistorySubVO> first = item.getSubmitInfoList().stream()
                            .filter(subItem -> {
                                if(Objects.nonNull(subItem.getScore())){
                                    return subItem.getScore().compareTo(BigDecimal.ZERO) <= 0;
                                }
                                // 明细
                                else if(CollUtil.isNotEmpty(subItem.getDetailScoreList())){
                                    boolean anyMatch = subItem.getDetailScoreList().stream()
                                            .anyMatch(score -> score.compareTo(BigDecimal.ZERO) < 0);
                                    if(anyMatch){
                                        return true;
                                    }
                                    scoreList.addAll(subItem.getDetailScoreList());
                                }
                                else if(CollUtil.isNotEmpty(subItem.getScoreList())){
                                    boolean anyMatch = subItem.getScoreList().stream()
                                            .anyMatch(score -> score.compareTo(BigDecimal.ZERO) < 0);
                                    if(anyMatch){
                                        return true;
                                    }
                                    scoreList.addAll(subItem.getScoreList());
                                }
                                return false;
                            }).findFirst();
                    if(first.isPresent()){
                        return false;
                    }
                    boolean allMatch = scoreList.stream()
                            .allMatch(score -> score.compareTo(BigDecimal.ZERO) == 0);
                    if(allMatch){
                        return false;
                    }
                }
                // 只看减分和0分
                if(Objects.equals(type, 3)){
                    List<BigDecimal> scoreList = new ArrayList<>();
                    Optional<ListEvaluateTaskHistorySubVO> first = item.getSubmitInfoList().stream()
                            .filter(subItem -> {
                                if(Objects.nonNull(subItem.getScore())){
                                    return subItem.getScore().compareTo(BigDecimal.ZERO) >= 0;
                                }
                                // 明细
                                else if(CollUtil.isNotEmpty(subItem.getDetailScoreList())){
                                    boolean anyMatch = subItem.getDetailScoreList().stream()
                                            .anyMatch(score -> score.compareTo(BigDecimal.ZERO) > 0);
                                    if(anyMatch){
                                        return true;
                                    }
                                    scoreList.addAll(subItem.getDetailScoreList());
                                }
                                else if(CollUtil.isNotEmpty(subItem.getScoreList())){
                                    boolean anyMatch = subItem.getScoreList().stream()
                                            .anyMatch(score -> score.compareTo(BigDecimal.ZERO) > 0);
                                    if(anyMatch){
                                        return true;
                                    }
                                    scoreList.addAll(subItem.getScoreList());
                                }
                                return false;
                            }).findFirst();
                    if(first.isPresent()){
                        return false;
                    }
                    boolean allMatch = scoreList.stream()
                            .allMatch(score -> score.compareTo(BigDecimal.ZERO) == 0);
                    if(allMatch){
                        return false;
                    }
                }
            }
            if(Objects.equals(2, evaluateType)){
                // 只看加分和0分
                if(Objects.equals(type, 2)){
                    Optional<SpeedRecordStudentDetailResponse> optional = item.getSpeedRecordStudentResponseList().stream()
                            .flatMap(ele -> ele.getSpeedRecordResponses()
                                    .stream()).flatMap(e -> e.getSpeedRecordStudentDetailResponses().stream())
                            .filter(detail -> detail.getScore().compareTo(BigDecimal.ZERO) < 0)
                            .findFirst();
                    if(optional.isPresent()){
                        return false;
                    }
                    boolean allMatch = item.getSpeedRecordStudentResponseList().stream()
                            .flatMap(ele -> ele.getSpeedRecordResponses()
                                    .stream()).flatMap(e -> e.getSpeedRecordStudentDetailResponses().stream())
                            .allMatch(detail -> detail.getScore().compareTo(BigDecimal.ZERO) == 0);
                    if(allMatch){
                        return false;
                    }
                }
                // 只看减分和0分
                if(Objects.equals(type, 3)){
                    Optional<SpeedRecordStudentDetailResponse> optional = item.getSpeedRecordStudentResponseList().stream()
                            .flatMap(ele -> ele.getSpeedRecordResponses()
                                    .stream()).flatMap(e -> e.getSpeedRecordStudentDetailResponses().stream())
                            .filter(detail -> detail.getScore().compareTo(BigDecimal.ZERO) > 0)
                            .findFirst();
                    if(optional.isPresent()){
                        return false;
                    }
                    boolean allMatch = item.getSpeedRecordStudentResponseList().stream()
                            .flatMap(ele -> ele.getSpeedRecordResponses()
                                    .stream()).flatMap(e -> e.getSpeedRecordStudentDetailResponses().stream())
                            .allMatch(detail -> detail.getScore().compareTo(BigDecimal.ZERO) == 0);
                    if(allMatch){
                        return false;
                    }
                }
            }
            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 校验老师查看点评列表参数
     *
     * @param dto
     */
    private void validateTaskHistoryTeacherQueryParam(PageEvaluateTaskHistoryTeacherQueryDTO dto) {
        Assert.notNull(dto.getStartTime(), () -> new BizException("开始时间不能为空"));
        Assert.notNull(dto.getEndTime(), () -> new BizException("结束时间不能为空"));
        Assert.notBlank(dto.getSchoolId(), () -> new BizException("学校id不能为空"));
        Assert.notBlank(dto.getCampusId(), () -> new BizException("校区id不能为空"));
        Assert.notBlank(dto.getCampusSectionId(), () -> new BizException("学段id不能为空"));
        Assert.notBlank(dto.getSchoolYear(), () -> new BizException("学年不能为空"));
        Assert.notBlank(dto.getTermName(), () -> new BizException("学期不能为空"));
        TermVo targetTerm = termLogic.getTermVoByCampusId(Long.valueOf(dto.getSchoolId()), Long.valueOf(dto.getCampusId()), Long.valueOf(dto.getCampusSectionId()), dto.getStartTime());
        dto.setStartTime(DateUtil.beginOfDay(dto.getStartTime()));
        dto.setEndTime(DateUtil.endOfDay(dto.getEndTime()));
        if (!StringUtils.equals(targetTerm.getSchoolYear(), dto.getSchoolYear())) {
            throw new BizException("时间选择错误");
        }
        if (!StringUtils.equals(targetTerm.getTermName(), dto.getTermName())) {
            throw new BizException("时间选择错误");
        }
        DateTime termEndTime = DateUtil.endOfDay(DateUtil.parseDate(targetTerm.getEndTime()));
        if (termEndTime.compareTo(dto.getEndTime()) < 0) {
            throw new BizException("时间选择错误");
        }
        List<Long> classIdList = new ArrayList<>();
        String campusSectionId = dto.getCampusSectionId();
        String schoolYear = dto.getSchoolYear();
        //查年级下的所有班级
        EduOrgQueryV2DTO eduOrgQueryV2DTO = new EduOrgQueryV2DTO();
        eduOrgQueryV2DTO.setIsTree(Constant.ONE);
        eduOrgQueryV2DTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
        eduOrgQueryV2DTO.setSchoolYear(schoolYear);
        eduOrgQueryV2DTO.setCurrentId(Long.valueOf(campusSectionId));
        List<EduOrgTreeV2VO> eduOrgTreeV2VOS = saasSchoolCacheManager.queryHistoryEducationalOrgTree(eduOrgQueryV2DTO);
        classIdList.addAll(findGradeAllClassId(eduOrgTreeV2VOS, campusSectionId));
        if (CollectionUtils.isNotEmpty(classIdList)) {
            dto.setClassIdList(classIdList);
            List<String> classIdStrList = new ArrayList<>();
            for (Long targetId : classIdList) {
                classIdStrList.add(targetId + "");
            }
            dto.setClassIdStrList(classIdStrList);
        }
    }

    /**
     * 通过年级查询所有班级id
     *
     * @param eduOrgTreeV2VOS
     * @param campusSectionId 学段id
     * @return
     */
    private Collection<Long> findGradeAllClassId(List<EduOrgTreeV2VO> eduOrgTreeV2VOS, String campusSectionId) {
        List<Long> classIdList = new ArrayList<>();
        for (EduOrgTreeV2VO eduOrgTreeV2VO : eduOrgTreeV2VOS) {
            if (Objects.equals(eduOrgTreeV2VO.getType(), SaasCurrentIdTypeEnum.SECTION.getCode())
                    && Objects.equals(eduOrgTreeV2VO.getId(), Long.valueOf(campusSectionId))) {
                List<EduOrgTreeV2VO> gradeList = eduOrgTreeV2VO.getChildren();
                for (EduOrgTreeV2VO grade : gradeList) {
                    List<EduOrgTreeV2VO> classList = grade.getChildren();
                    for (EduOrgTreeV2VO classVo : classList) {
                        if (Objects.equals(classVo.getType(), SaasCurrentIdTypeEnum.CLASS.getCode())) {
                            classIdList.add(classVo.getId());
                        }
                    }
                }
                return classIdList;
            } else if (Objects.equals(eduOrgTreeV2VO.getType(), SaasCurrentIdTypeEnum.SCHOOL.getCode())
                    && Objects.equals(eduOrgTreeV2VO.getType(), SaasCurrentIdTypeEnum.CAMPUS.getCode())) {
                findGradeAllClassId(eduOrgTreeV2VO.getChildren(), campusSectionId);
            }
        }
        return Collections.emptyList();
    }

    /**
     * 从info信息中获取所有class和grade的对应关系
     *
     * @param infos
     * @return
     */
    private Map<String, String> getClassGradeMapFromInfo(List<Info> infos) {
        Map<String, String> classGradeMap = new HashMap<>();
        if (CollUtil.isEmpty(infos)) {
            return classGradeMap;
        }
        Set<String> classIdSet = new HashSet<>();
        //获取所有班级信息
        for (Info info : infos) {
            List<StudentInfo> studentInfos = info.getStudentInfos();
            if ((CollectionUtils.isNotEmpty(studentInfos))) {
                for (StudentInfo studentInfo : studentInfos) {
                    List<SubStudentInfo> subStudentInfos = studentInfo.getStudentInfos();
                    if (CollectionUtils.isNotEmpty(subStudentInfos)) {
                        for (SubStudentInfo subStudentInfo : subStudentInfos) {
                            if (StringUtils.isBlank(subStudentInfo.getClassId())) {
                                continue;
                            }
                            classIdSet.add(subStudentInfo.getClassId());
                        }
                    }
                }
            }
        }
        if (CollUtil.isEmpty(classIdSet)) {
            return classGradeMap;
        }
        List<TchClassOpenV2VO> tchClassOpenV2VOS = saasClassManager.queryClassByIds(new ArrayList<>(classIdSet));
        if (CollectionUtils.isNotEmpty(tchClassOpenV2VOS)) {
            for (TchClassOpenV2VO classOpenV2VO : tchClassOpenV2VOS) {
                classGradeMap.put(classOpenV2VO.getId() + "", classOpenV2VO.getGradeId() + "");
            }
        }
        return classGradeMap;
    }

    /**
     * 处理新的点评详情（批量导入）
     */
    private void getBatchImportsGetEvaluateInfoDetailVO(BehaviourRecord behaviourRecord, List<PageEvaluateTaskHistoryVO> list) {

        //获取批量导入记录行ID
       String importsRowId = behaviourRecord.getInfoId();


        List<EvaluateBehaviourImportsRecordDetailPO> importsRecordDetailPOS = evaluateBehaviourImportsRecordDetailManager
                .listByInfoId(Convert.toLong(importsRowId));
        if (CollUtil.isEmpty(importsRecordDetailPOS)) {
            return;
        }

        PageEvaluateTaskHistoryVO vo = new PageEvaluateTaskHistoryVO();
        vo.setDate(DateUtil.format(importsRecordDetailPOS.get(0).getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        vo.setInfoId(importsRowId);
        vo.setTaskName(behaviourRecord.getTargetName());

        List<ListEvaluateTaskHistorySubVO> submitInfoList = new ArrayList<>();
        buildStudentInfo(importsRecordDetailPOS, submitInfoList);

        for (EvaluateBehaviourImportsRecordDetailPO importsRecordDetailPO : importsRecordDetailPOS) {
            ListEvaluateTaskHistorySubVO submitInfoVO = new ListEvaluateTaskHistorySubVO();
            submitInfoVO.setType(importsRecordDetailPO.getControlType());
            submitInfoVO.setTypeName(SubmitInfoTypeEnum.getByValue(importsRecordDetailPO.getControlType()).getMessage());
            submitInfoVO.setName(importsRecordDetailPO.getControlName());
            submitInfoVO.setIsScore(importsRecordDetailPO.getControlIsScore() == 0 ? false : true);

            //点评项为选项则特殊处理
            if (SubmitInfoTypeEnum.isCheckSubmitInfo(importsRecordDetailPO.getControlType())) {

                SubmitInfoSubVO submitInfoSubVO = new SubmitInfoSubVO();

                if (importsRecordDetailPO.getControlIsScore() == 1) {
                    BigDecimal optionScore = importsRecordDetailPO.getOptionScore();
                    if (optionScore != null) {
                        submitInfoVO.setScoreType(optionScore.compareTo(BigDecimal.ZERO));
                        submitInfoVO.setScore(optionScore);
                        submitInfoSubVO.setValue(optionScore);
                    }
                }

                submitInfoSubVO.setLabel(importsRecordDetailPO.getOptionValue());
                submitInfoSubVO.setKey(importsRecordDetailPO.getOptionId());

                if (SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(importsRecordDetailPO.getControlType())) {
                    submitInfoVO.setSubmitValue(CollUtil.newArrayList(submitInfoSubVO));
                } else {
                    submitInfoVO.setSubmitValue(submitInfoSubVO);
                }
            }
            //点评项为分值特殊处理
            else if (SubmitInfoTypeEnum.SCORE.getText().equals(importsRecordDetailPO.getControlType())) {

                String controlValue = importsRecordDetailPO.getControlValue();
                if (controlValue != null) {

                    BigDecimal score = new BigDecimal(controlValue);
                    submitInfoVO.setScoreType(score.compareTo(BigDecimal.ZERO));
                    submitInfoVO.setScore(score);
                    submitInfoVO.setSubmitValue(controlValue);
                }
            } else {
                submitInfoVO.setSubmitValue(importsRecordDetailPO.getControlValue());
            }

            submitInfoList.add(submitInfoVO);
        }

        vo.setSubmitInfoList(submitInfoList);

        list.add(vo);
    }

    /**
     * 组装学生信息
     *
     * @param importsRecordDetailPOS
     * @param submitInfoList
     */
    private void buildStudentInfo(List<EvaluateBehaviourImportsRecordDetailPO> importsRecordDetailPOS, List<ListEvaluateTaskHistorySubVO> submitInfoList) {
        ListEvaluateTaskHistorySubVO submitStudentInfoVO = new ListEvaluateTaskHistorySubVO();
        submitStudentInfoVO.setType("student");
        submitStudentInfoVO.setTypeName("批量导入学生");
        submitStudentInfoVO.setName("学生");

        StudentInfo studentInfo = new StudentInfo();
        List<SubStudentInfo> subStudentInfos = new ArrayList<>();
        SubStudentInfo subStudentInfo = new SubStudentInfo();
        EvaluateBehaviourImportsRecordDetailPO evaluateBehaviourImportsRecordDetailPO = importsRecordDetailPOS.get(0);
        subStudentInfo.setId(Convert.toStr(evaluateBehaviourImportsRecordDetailPO.getStudentId()));
        subStudentInfo.setName(evaluateBehaviourImportsRecordDetailPO.getStudentName());
        subStudentInfo.setType(6);
        subStudentInfo.setSchoolId(Convert.toStr(evaluateBehaviourImportsRecordDetailPO.getSchoolId()));
        subStudentInfo.setClassId(Convert.toStr(evaluateBehaviourImportsRecordDetailPO.getClassId()));
        subStudentInfo.setSectionCode(evaluateBehaviourImportsRecordDetailPO.getCampusSectionCode());
        subStudentInfos.add(subStudentInfo);
        studentInfo.setStudentInfos(subStudentInfos);
        submitStudentInfoVO.setSubmitValue(CollUtil.newArrayList(studentInfo));

        submitInfoList.add(submitStudentInfoVO);
    }

    /**
     * 处理新的点评记录
     */
    private void fillNewRecords(List<BehaviourRecord> behaviourRecords, List<PageEvaluateTaskHistoryVO> list) {
        if (CollUtil.isEmpty(behaviourRecords)) {
            log.warn("【处理新的点评记录】入参为空，直接返回");
            return;
        }

        List<BehaviourRecord> filtered = new ArrayList<>();

        for (BehaviourRecord behaviourRecord : behaviourRecords) {
            if (DataSourceEnum.BATCH_IMPORTS.getCode().equals(behaviourRecord.getDataSource())) {
                getBatchImportsGetEvaluateInfoDetailVO(behaviourRecord, list);
            } else {
                filtered.add(behaviourRecord);
            }
        }

        if (CollUtil.isEmpty(filtered)) {
            return;
        }


        //获取学生信息
        List<StudentInfoVO> studentInfoVOS = this.listStudentInfoVOS(filtered);

        //按infoId分组，同一infoId为一次批量提交
        Map<String, List<BehaviourRecord>> infoIdListMap = CollStreamUtil
                .groupByKey(filtered, BehaviourRecord::getInfoId);

        // 查询行为记录关联的分类
        List<Long> ids = filtered.stream().map(BehaviourRecord::getId).collect(Collectors.toList());
        List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordExtPOS = evaluateBehaviourRecordExtManager.listByEvaluateIds(ids);
        Map<Long, EvaluateBehaviourRecordExtPO> longEvaluateBehaviourRecordClassifyPOMap = evaluateBehaviourRecordExtPOS.stream().collect(Collectors.toMap(EvaluateBehaviourRecordExtPO::getBehaviourRecordId, Function.identity()));

        for (Map.Entry<String, List<BehaviourRecord>> infoIdListEntry : infoIdListMap.entrySet()) {
            List<SpeedRecordStudentResponse> speedRecordStudentResponseList = new ArrayList<>();
            this.buildEvaluateTaskHistoryVO(list, infoIdListEntry, speedRecordStudentResponseList);

            List<BehaviourRecord> studentBehaviourResponses = infoIdListEntry.getValue();

            Map<String, List<BehaviourRecord>> studentIdToRecordsMap = CollStreamUtil
                    .groupByKey(studentBehaviourResponses, BehaviourRecord::getStudentId);

            for (Map.Entry<String, List<BehaviourRecord>> studentIdToRecordEntry : studentIdToRecordsMap.entrySet()) {
                List<BehaviourRecord> studentIdToRecordEntryValue = studentIdToRecordEntry.getValue();

                List<SpeedRecordResponse> speedRecordResponses =
                        this.listSpeedRecordResponses(studentIdToRecordEntry, speedRecordStudentResponseList, studentInfoVOS);

                //校标
                this.fillSchoolRecordDetail(studentIdToRecordEntryValue, speedRecordResponses, longEvaluateBehaviourRecordClassifyPOMap,
                        new HashMap<>());

                //师标
                this.fillGeneralRecordDetail(studentIdToRecordEntryValue, speedRecordResponses, longEvaluateBehaviourRecordClassifyPOMap);
            }
        }
    }

    /**
     * 填充师标记录详情
     */
    @Override
    public void fillGeneralRecordDetail(List<BehaviourRecord> studentIdToRecordEntryValue,
                                        List<SpeedRecordResponse> speedRecordResponses,
                                        Map<Long, EvaluateBehaviourRecordExtPO> longEvaluateBehaviourRecordClassifyPOMap) {
        if (CollUtil.isEmpty(studentIdToRecordEntryValue)) {
            log.warn("【填充师标记录详情】入参为空，直接返回，studentIdToRecordEntryValue：{}",
                    JSONUtil.toJsonStr(studentIdToRecordEntryValue));
            return;
        }
        List<BehaviourRecord> teacherRecords = studentIdToRecordEntryValue.stream()
                .filter(s -> Objects.equals(s.getInfoType(), InfoTypeEnum.SPEED_OPTION_TEACHER_TEACHER.getCode())).collect(Collectors.toList());
        teacherRecords.forEach(item -> {
            Long id = item.getId();
            EvaluateBehaviourRecordExtPO evaluateBehaviourRecordExtPO = longEvaluateBehaviourRecordClassifyPOMap.get(id);
            if (Objects.nonNull(evaluateBehaviourRecordExtPO)) {
                item.setTargetId(evaluateBehaviourRecordExtPO.getClassifyId());
                item.setTargetName(evaluateBehaviourRecordExtPO.getClassifyName());
            }
        });
        if (CollUtil.isNotEmpty(teacherRecords)) {

            Map<Long, List<BehaviourRecord>> generalListMap = CollStreamUtil
                    .groupByKey(teacherRecords, BehaviourRecord::getTargetId);
            generalListMap.forEach((key, value) -> {
                List<SpeedRecordStudentDetailResponse> speedRecordStudentDetailResponses = new ArrayList<>();
                SpeedRecordResponse speedRecordResponse = new SpeedRecordResponse();
                speedRecordResponse.setApplyLevel(SpeedApplyLevelEnum.GENERAL.getCode());
                speedRecordResponse.setGroupName(value.get(0).getTargetName());
                speedRecordResponse.setGroupId(Convert.toLong(key));
                speedRecordResponse.setSpeedRecordStudentDetailResponses(speedRecordStudentDetailResponses);
                speedRecordResponses.add(speedRecordResponse);

                speedRecordStudentDetailResponses.addAll(
                        value.stream()
                                .map(this::buildSpeedRecordStudentDetailResponse)
                                .collect(Collectors.toList())
                );
            });
        }
    }

    @Override
    public List<ListAllEvaluateTargetVOModule> listStudentEvaluateTarget() {
        return targetGroupService.listAllEvaluateTarget(TaskUserTypeEnum.STUDENT.getCode());
    }

    /**
     * 填充校标记录详情
     */
    @Override
    public void fillSchoolRecordDetail(List<BehaviourRecord> studentIdToRecordEntryValue,
            List<SpeedRecordResponse> speedRecordResponses,
            Map<Long, EvaluateBehaviourRecordExtPO> longEvaluateBehaviourRecordClassifyPOMap,
            Map<Long, List<String>> optExtMap) {
        if (CollUtil.isEmpty(studentIdToRecordEntryValue)) {
            log.warn("【填充校标记录详情】入参为空，直接返回，studentIdToRecordEntryValue：{}",
                    JSONUtil.toJsonStr(studentIdToRecordEntryValue));
            return;
        }
        List<BehaviourRecord> schoolRecords = studentIdToRecordEntryValue.stream()
                .filter(s -> !Objects.equals(s.getInfoType(), InfoTypeEnum.SPEED_OPTION_TEACHER_TEACHER.getCode())).collect(Collectors.toList());
        schoolRecords.forEach(item -> {
            Long id = item.getId();
            EvaluateBehaviourRecordExtPO evaluateBehaviourRecordExtPO = longEvaluateBehaviourRecordClassifyPOMap.get(id);
            if (Objects.nonNull(evaluateBehaviourRecordExtPO)) {
                item.setTargetId(evaluateBehaviourRecordExtPO.getClassifyId());
                item.setTargetName(evaluateBehaviourRecordExtPO.getClassifyName());
            }
        });
        if (CollUtil.isNotEmpty(schoolRecords)) {

            Map<Long, List<BehaviourRecord>> schoolListMap = CollStreamUtil
                    .groupByKey(schoolRecords, BehaviourRecord::getTargetId);
            schoolListMap.forEach((key, value) -> {
                SpeedRecordResponse speedRecordResponse = new SpeedRecordResponse();
                List<SpeedRecordStudentDetailResponse> speedRecordStudentDetailResponses = new ArrayList<>();
                speedRecordResponse.setApplyLevel(SpeedApplyLevelEnum.SCHOOL.getCode());
//                if (CollUtil.isNotEmpty(finalSpeedTargetGroupPOS)) {
//                    finalSpeedTargetGroupPOS.stream().filter(s -> s.getId().equals(Convert.toLong(key)))
//                            .findFirst().ifPresent(speedTargetGroupPO ->
//                                    speedRecordResponse.setGroupName(speedTargetGroupPO.getGroupName()));
//                }
                speedRecordResponse.setGroupName(value.get(0).getTargetName());

                speedRecordResponse.setGroupId(Convert.toLong(key));
                speedRecordResponse.setSpeedRecordStudentDetailResponses(speedRecordStudentDetailResponses);
                speedRecordResponses.add(speedRecordResponse);

                List<SpeedRecordStudentDetailResponse> detailList = value.stream()
                        .map(this::buildSpeedRecordStudentDetailResponse)
                        .peek(item -> item.setTextareaList(
                                optExtMap.getOrDefault(item.getEvaluateId(), new ArrayList<>())))
                        .collect(Collectors.toList());
                speedRecordStudentDetailResponses.addAll(detailList);
            });
        }
    }

    /**
     * 构建极速点评学生详情
     */
    @Override
    public List<SpeedRecordResponse> listSpeedRecordResponses(Map.Entry<String, List<BehaviourRecord>> studentIdToRecordEntry,
                                                              List<SpeedRecordStudentResponse> speedRecordStudentResponseList,
                                                              List<StudentInfoVO> studentInfoVOS) {
        SpeedRecordStudentResponse speedRecordStudentResponse = new SpeedRecordStudentResponse();
        speedRecordStudentResponseList.add(speedRecordStudentResponse);
        speedRecordStudentResponse.setStudentId(studentIdToRecordEntry.getKey());
        studentInfoVOS.stream()
                .filter(s -> s.getStudentId().equals(Convert.toLong(studentIdToRecordEntry.getKey())))
                .findFirst()
                .ifPresent(studentInfoVO -> speedRecordStudentResponse.setStudentName(studentInfoVO.getStudentName()));
        List<SpeedRecordResponse> speedRecordResponses = new ArrayList<>();
        speedRecordStudentResponse.setSpeedRecordResponses(speedRecordResponses);
        return speedRecordResponses;
    }

    /**
     * 构建点评记录 response
     */
    private void buildEvaluateTaskHistoryVO(List<PageEvaluateTaskHistoryVO> list,
                                            Map.Entry<String, List<BehaviourRecord>> dateListEntry,
                                            List<SpeedRecordStudentResponse> speedRecordStudentResponseList) {
        PageEvaluateTaskHistoryVO evaluateTaskHistoryVO = new PageEvaluateTaskHistoryVO();
        list.add(evaluateTaskHistoryVO);
        evaluateTaskHistoryVO.setSpeedRecordStudentResponseList(speedRecordStudentResponseList);
        evaluateTaskHistoryVO.setSource(RecordDataSourceEnum.NEW.getCode());
        evaluateTaskHistoryVO.setDate(DateUtil.format(dateListEntry.getValue().get(0).getSubmitTime(), "yyyy-MM-dd HH:mm:ss"));
        evaluateTaskHistoryVO.setInfoId(dateListEntry.getValue().get(0).getInfoId());
        evaluateTaskHistoryVO.setTaskName("批量点评");
    }

    /**
     * 构建新的点评记录详情
     */
    private SpeedRecordStudentDetailResponse buildSpeedRecordStudentDetailResponse(BehaviourRecord behaviourRecord) {
        SpeedRecordStudentDetailResponse speedRecordStudentDetailResponse = new SpeedRecordStudentDetailResponse();
        if (BeanUtil.isEmpty(behaviourRecord)) {
            return speedRecordStudentDetailResponse;
        }
        speedRecordStudentDetailResponse.setEvaluateId(behaviourRecord.getId());
        speedRecordStudentDetailResponse.setInfoName(behaviourRecord.getInfoName());
        speedRecordStudentDetailResponse.setInfoType(behaviourRecord.getInfoType());
        speedRecordStudentDetailResponse.setOptionId(behaviourRecord.getOptionId());
        speedRecordStudentDetailResponse.setScore(behaviourRecord.getScore());
        speedRecordStudentDetailResponse.setScoreType(behaviourRecord.getScoreType());
        speedRecordStudentDetailResponse.setSubmitTime(behaviourRecord.getSubmitTime());
        speedRecordStudentDetailResponse.setDataSource(behaviourRecord.getDataSource());
        return speedRecordStudentDetailResponse;
    }


    /**
     * 获取极速点评分组
     */
    private List<SpeedTargetGroupPO> listSpeedTargetGroupPOS(List<BehaviourRecord> behaviourRecords) {
        List<String> speedGroupIds = behaviourRecords.stream().map(BehaviourRecord::getTemplateId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<SpeedTargetGroupPO> speedTargetGroupPOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(speedGroupIds)) {
            speedTargetGroupPOS = speedTargetGroupMapper.selectBatchIds(speedGroupIds);
        }
        return speedTargetGroupPOS;
    }

    /**
     * 获取学生信息
     */
    private List<com.hailiang.saas.model.vo.StudentInfoVO> listStudentInfoVOS(List<BehaviourRecord> behaviourRecords) {
        if (CollUtil.isEmpty(behaviourRecords)) {
            log.warn("点评记录-获取学生信息失败，参数为空，behaviourRecords：{}", JSONUtil.toJsonStr(behaviourRecords));
            return Collections.emptyList();
        }
        List<Long> studentIds = behaviourRecords.stream().map(BehaviourRecord::getStudentId).distinct()
                .map(Convert::toLong)
                .collect(Collectors.toList());
        StudentDTO studentDTO = new StudentDTO();

        List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOS = new ArrayList<>();

        if (studentIds.size() <= 1000) {
            studentDTO.setStudentIds(studentIds);
            return saasStudentManager.studentDetailV2(studentDTO);
        } else {
            int size = studentIds.size();
            int loop = size / 1000;
            for (int i = 0; i <= loop; i++) {
                int start = i * 1000;
                int end = Math.min((i + 1) * 1000, size);
                List<Long> splitStudentIds = studentIds.subList(start, end);
                if (CollUtil.isEmpty(splitStudentIds)) {
                    break;
                }
                studentDTO.setStudentIds(splitStudentIds);
                List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOList = saasStudentManager.studentDetailV2(studentDTO);
                if (CollUtil.isEmpty(studentInfoVOList)) {
                    return studentInfoVOList;
                }
                if (studentInfoVOList.size() < 1000 && splitStudentIds.size() < 1000) {
                    studentInfoVOS.addAll(studentInfoVOList);
                    return studentInfoVOS;
                }
                studentInfoVOS.addAll(studentInfoVOList);
            }
        }
        return studentInfoVOS;
    }

    /**
     * 模糊搜索
     *
     * @param dto
     * @param collect
     * @return
     */
    private static List<PageEvaluateTaskHistoryVO> searchByName(PageEvaluateTaskHistoryQueryDTO dto, List<PageEvaluateTaskHistoryVO> collect) {
        List<PageEvaluateTaskHistoryVO> taskHistoryVOList = new ArrayList<>();
        for (PageEvaluateTaskHistoryVO pageEvaluateTaskHistoryVO : collect) {
            //新点评搜索
            if (RecordDataSourceEnum.NEW.getCode().equals(pageEvaluateTaskHistoryVO.getSource())) {
                if (JSONUtil.toJsonStr(pageEvaluateTaskHistoryVO).contains(dto.getTaskName())) {
                    taskHistoryVOList.add(pageEvaluateTaskHistoryVO);
                }
                continue;
            }

            //指标名称匹配
            if (pageEvaluateTaskHistoryVO.getTaskName().contains(dto.getTaskName())) {
                taskHistoryVOList.add(pageEvaluateTaskHistoryVO);
            }
            //学生控件
            List<ListEvaluateTaskHistorySubVO> historySubVOS = pageEvaluateTaskHistoryVO.getSubmitInfoList().stream().filter(s -> SubmitInfoTypeEnum.STUDENT.getText().equals(s.getType())).collect(Collectors.toList());
            if (CollUtil.isEmpty(historySubVOS)) {
                //明细控件
                List<ListEvaluateTaskHistorySubVO> evaluateTaskHistorySubVOS = pageEvaluateTaskHistoryVO.getSubmitInfoList().stream().filter(s -> SubmitInfoTypeEnum.DETAIL.getText().equals(s.getType())).collect(Collectors.toList());
                if (CollUtil.isEmpty(evaluateTaskHistorySubVOS)) {
                    continue;
                }

                //明细里面的学生
                List<Object> submitValue = evaluateTaskHistorySubVOS.stream().map(ListEvaluateTaskHistorySubVO::getSubmitValue).collect(Collectors.toList());
                List<StudentInfo> studentInfos = submitValue.stream().map(obj -> (List<StudentInfo>) obj).flatMap(Collection::stream).collect(Collectors.toList());
                List<SubStudentInfo> studentInfoList = studentInfos.stream().map(StudentInfo::getStudentInfos).flatMap(Collection::stream).collect(Collectors.toList());
                studentInfoList = studentInfoList.stream().filter(s -> s.getName().contains(dto.getTaskName())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(studentInfoList)) {
                    taskHistoryVOList.add(pageEvaluateTaskHistoryVO);
                }
                continue;
            }
            //有学生控件的话，匹配
            Optional<ListEvaluateTaskHistorySubVO> historySubVO = historySubVOS.stream().findFirst();
            ListEvaluateTaskHistorySubVO listEvaluateTaskHistorySubVO = historySubVO.get();
            List<StudentInfo> studentInfos = JSON.parseArray(JSON.toJSONString(listEvaluateTaskHistorySubVO.getSubmitValue()), StudentInfo.class);
            List<SubStudentInfo> studentInfoList = studentInfos.get(Constant.ZERO).getStudentInfos();
            if (CollUtil.isEmpty(studentInfoList)) {
                continue;
            }
            studentInfoList = studentInfoList.stream().filter(s -> s.getName().contains(dto.getTaskName())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(studentInfoList)) {
                taskHistoryVOList.add(pageEvaluateTaskHistoryVO);
            }
        }
        return taskHistoryVOList;
    }

    /**
     * 将学生和明细控件往前移
     *
     * @param dto
     * @param taskHistoryVOList
     */
    private static void orderStudent(PageEvaluateTaskHistoryQueryDTO dto, List<PageEvaluateTaskHistoryVO> taskHistoryVOList) {
        for (PageEvaluateTaskHistoryVO pageEvaluateTaskHistoryVO : taskHistoryVOList) {
            if (RecordDataSourceEnum.NEW.getCode().equals(pageEvaluateTaskHistoryVO.getSource())) {
                continue;
            }
            List<ListEvaluateTaskHistorySubVO> evaluateTaskHistorySubVOS = pageEvaluateTaskHistoryVO.getSubmitInfoList().stream().filter(s -> (ObjectUtil.isNotNull(s.getSubmitValue()) && s.getSubmitValue().toString().contains(dto.getTaskName()))).collect(Collectors.toList());
            pageEvaluateTaskHistoryVO.getSubmitInfoList().removeIf(s -> (ObjectUtil.isNotNull(s.getSubmitValue()) && s.getSubmitValue().toString().contains(dto.getTaskName())));
            pageEvaluateTaskHistoryVO.getSubmitInfoList().addAll(0, evaluateTaskHistorySubVOS);
            if (Constant.TWO <= pageEvaluateTaskHistoryVO.getSubmitInfoList().size()) {
                pageEvaluateTaskHistoryVO.setSubmitInfoList(pageEvaluateTaskHistoryVO.getSubmitInfoList().subList(0, 2));
            }
        }
    }


    @Override
    public GetEvaluateTaskDetailVO getEvaluateTaskDetail(GetEvaluateTaskDetailQueryDTO dto) {
        Assert.isTrue(dto.getTargetId() != null,
                () -> new BizException(BizExceptionEnum.TARGET_ID_NOT_NULL.getMessage()));

        Target target = targetManager.getById(dto.getTargetId());
        Assert.notNull(target, () -> new BizException("指标不存在！"));

        TargetGroup targetGroup = targetGroupManager.getById(target.getGroupId());
        Assert.notNull(targetGroup, () -> new BizException("指标组不存在！"));

        GetEvaluateTaskDetailVO taskDetail;
        if (dto.getTaskId() != null) {
            taskDetail = taskHelper.getTaskDetail(dto.getTaskId());
        } else {
            taskDetail = taskHelper.getTaskTemplate(dto.getTargetId());
        }
        if (ObjectUtil.isNull(taskDetail)) {
            return taskDetail;
        }
        //体测项目
        Integer systemRecommendCode = TargetTypeEnum.SYSTEM_RECOMMEND.getCode();
        if (targetGroup.getModuleCode().equals(ModuleEnum.SPORT.getCode())
                && systemRecommendCode.equals(target.getTargetType())
                && systemRecommendCode.equals(targetGroup.getGroupType())) {
            taskDetail.setIsSportTarget(Boolean.TRUE);
        }
        taskDetail.setStudentSubmitReviewFlag(target.getStudentSubmitReviewFlag());

        // 处理学生填报学科选项信息
        this.handleStudentSubjectOption(dto, taskDetail);

        return taskDetail;
    }

    /**
     * 处理学生填报学科选项信息
     *
     * @param dto
     * @param taskDetail
     */
    private void handleStudentSubjectOption(GetEvaluateTaskDetailQueryDTO dto, GetEvaluateTaskDetailVO taskDetail) {
        // 如果表单中有学科信息,说明是学生填报的表单,需要过滤学生当前学段的学科
        Long studentIdLong = WebUtil.getStudentIdLong();
        List<TemplateInfoSaveDTO> templateInfo = taskDetail.getTemplateInfo();
        for (TemplateInfoSaveDTO templateInfoSaveDTO : templateInfo) {
            if (SubmitInfoTypeEnum.SUBJECT.getText().equals(templateInfoSaveDTO.getType())
                    && Objects.nonNull(studentIdLong)){
                List<StudentInfoVO> saasStudentInfos = saasStudentManager.studentDetailV2(studentIdLong);
                if (CollUtil.isEmpty(saasStudentInfos)){
                    continue;
                }
                // 获取学生的所有班级信息(包括走班)
                List<Long> classIds = saasStudentInfos.stream().map(StudentInfoVO::getClassId).distinct()
                        .collect(Collectors.toList());
                // 获取班级教师任教课程
                List<ClassTeacherInfoVO> saasClassInfos = saasClassManager.queryClassTeacherInfo(classIds);
                if (CollUtil.isEmpty(saasClassInfos)){
                    continue;
                }
                List<ClassTeacher> classTeachers = saasClassInfos.stream()
                        .filter(s -> CollUtil.isNotEmpty(s.getTeacherList())).map(
                                ClassTeacherInfoVO::getTeacherList).flatMap(Collection::stream)
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(classTeachers)){
                    continue;
                }
                List<ClassSubjectDetailVO> classSubjectInfos = classTeachers.stream()
                        .filter(s -> CollUtil.isNotEmpty(s.getSubjectList())).map(ClassTeacher::getSubjectList)
                        .flatMap(Collection::stream).collect(Collectors.toList());
                if (CollUtil.isEmpty(classSubjectInfos)){
                    continue;
                }
                InnerSubmitOptionInfoSave option = templateInfoSaveDTO.getOptions();
                if (Objects.isNull(option)){
                    continue;
                }

                if (CollUtil.isEmpty(option.getOptions())){
                    continue;
                }

                ClassTeacherInfoVO saasClassInfo = CollUtil.getFirst(saasClassInfos);
                TchClassOpenV2VO tchClassOpenV2VO = saasClassManager.queryClassById(
                        Convert.toStr(saasClassInfo.getClassId()));
                if (Objects.isNull(tchClassOpenV2VO)){
                    continue;
                }
                // 当前学生所属学段id
                Long currentStudentSectionId = tchClassOpenV2VO.getCampusSectionId();
                // 当前学生学段下的选项课程信息
                List<InnerSubmitOptionInfoSubSave> sectionOptions = option.getOptions().stream()
                        .filter(s -> ObjectUtil.equal(s.getSectionId(), Convert.toStr(currentStudentSectionId)))
                        .collect(Collectors.toList());
                // 选项内已存在的课程id信息
                List<String> existCourseIds = sectionOptions.stream()
                        .map(InnerSubmitOptionInfoSubSave::getCourseId)
                        .filter(CharSequenceUtil::isNotBlank).distinct().collect(Collectors.toList());

                List<InnerSubmitOptionInfoSubSave> options = new ArrayList<>();
                for (ClassSubjectDetailVO classSubjectInfo : classSubjectInfos) {
                    InnerSubmitOptionInfoSubSave infoSubSave = new InnerSubmitOptionInfoSubSave();
                    infoSubSave.setLabel(classSubjectInfo.getSubjectName());
                    if (CharSequenceUtil.isNotBlank(classSubjectInfo.getDisciplineName())
                            && !CharSequenceUtil.equals(classSubjectInfo.getSubjectName(), classSubjectInfo.getDisciplineName())){
                        infoSubSave.setLabel(classSubjectInfo.getSubjectName()+"（"+classSubjectInfo.getDisciplineName()+"）");
                    }
                    infoSubSave.setValue(BigDecimal.ZERO);
                    infoSubSave.setKey(classSubjectInfo.getSubjectCode());
                    infoSubSave.setSectionId(Convert.toStr(currentStudentSectionId));
                    infoSubSave.setSectionCode(tchClassOpenV2VO.getSectionCode());
                    infoSubSave.setSectionName(tchClassOpenV2VO.getSectionName());
                    infoSubSave.setCourseId(Convert.toStr(classSubjectInfo.getSubjectId()));
                    infoSubSave.setCourseCode(classSubjectInfo.getSubjectCode());
                    infoSubSave.setCourseName(classSubjectInfo.getSubjectName());
                    infoSubSave.setSubjectId(Convert.toStr(classSubjectInfo.getDisciplineId()));
                    infoSubSave.setSubjectCode(classSubjectInfo.getDisciplineCode());
                    infoSubSave.setSubjectName(classSubjectInfo.getDisciplineName());
                    if (CharSequenceUtil.isNotBlank(infoSubSave.getSectionId())
                            && CharSequenceUtil.isNotBlank(infoSubSave.getCourseId())) {
                        infoSubSave.setKeyValue(infoSubSave.getSectionId() + "_" + infoSubSave.getCourseId());
                    }
                    options.add(infoSubSave);
                }
                // 基于keyValue字段比较
                List<InnerSubmitOptionInfoSubSave> intersection = sectionOptions.stream()
                        .filter(sectionOption -> options.stream()
                                .anyMatch(s -> Objects.equals(sectionOption.getKeyValue(), s.getKeyValue())))
                        .collect(Collectors.toList());
                option.setOptions(intersection);
            }
        }
    }

    @Override
    public GetTaskDetailForDDVO getTaskDetailForDD(GetTaskDetailForDDDTO dto) {
        GetTaskDetailForDDVO vo = new GetTaskDetailForDDVO();
        if (dto.getTaskId() == null) {
            return vo;
        }
        Info info = infoLogic.getByTaskId(dto.getTaskId());
        if (info == null) {
            return vo;
        }
        return vo.setInfoId(info.getId());
    }

    @Override
    public List<ListCurrentEvaluateTaskVO> listParentCurrentEvaluateTask() {
        Date now = DateUtil.date();
        // 查询家长未提交或重新提交(审核拒绝)的任务
        List<TaskPO> evaluateTaskPOS = this.list(new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getSubmitStaffId, WebUtil.getStudentIdStr())
                .and(wrapper -> wrapper.eq(TaskPO::getTaskStatus, TaskStatusEnum.NOT_SUBMIT.getCode())
                        .or(s -> s.eq(TaskPO::getApprovalStatus, TaskApprovalEnum.APPROVAL_REFUSE.getCode()).eq(TaskPO::getTaskStatus, TaskStatusEnum.SUBMITTED.getCode()))));
        if (CollUtil.isEmpty(evaluateTaskPOS)) {
            return Collections.emptyList();
        }
        List<SubListCurrentEvaluateTaskVO> listCurrentEvaluateTasks = taskConvert.toCurrentEvaluateTaskVOS(evaluateTaskPOS);

        Set<Long> targetIdSet = new HashSet<>();
        Set<Long> taskIdSet = new HashSet<>();
        Set<Long> noSubmitTaskIdSet = new HashSet<>();

        for (TaskPO d : evaluateTaskPOS) {
            targetIdSet.add(d.getTargetId());
            taskIdSet.add(d.getId());
            if (TaskStatusEnum.NOT_SUBMIT.getCode().equals(d.getTaskStatus())) {
                noSubmitTaskIdSet.add(d.getId());
            }
        }
//        List<Long> targetIds = evaluateTasks.stream().map(Task::getTargetId).collect(Collectors.toList());
        List<Target> targets = targetService.listByIds(targetIdSet);

        //获取InfoId的Map
        Map<Long, String> infoIdMap = new HashMap<>();
//        List<Long> taskIds = evaluateTasks.stream().map(Task::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(taskIdSet)) {
            Query query = new Query(Criteria.where("taskId").in(taskIdSet).and("deleted").is(0));
            List<Info> infos = mongoTemplate.find(query, Info.class);
            infoIdMap = infos.parallelStream().filter(s -> Objects.nonNull(s.getTaskId())).collect(Collectors.toMap(Info::getTaskId, Info::getId));
        }


        // 图标urlMap
        Map<String, String> targetIconUrlMap = new HashMap<>();
        if (CollUtil.isNotEmpty(targets)) {
            targetIconUrlMap = targets.parallelStream().collect(Collectors.toMap(i -> String.valueOf(i.getId()), i -> urlPrefix + i.getIconUrl(), (a, b) -> b));
        }
        //审核状态
//        List<Long> noSubmitTaskIds = evaluateTasks.stream().filter(s -> TaskStatusEnum.NOT_SUBMIT.getCode().equals(s.getTaskStatus())).distinct().map(Task::getId).collect(Collectors.toList());

        //数据填充
        Map<String, String> finalTargetIconUrlMap = targetIconUrlMap;
        Map<Long, String> finalInfoIdMap = infoIdMap;
        listCurrentEvaluateTasks.forEach(s -> {
            if (Objects.nonNull(s.getRemindTime())) {
                // 延时几小时
                s.setOverHours(DateUtil.between(s.getRemindTime(), now, DateUnit.HOUR));
            }
            //图标
            s.setIconUrl(finalTargetIconUrlMap.get(s.getTargetId()));
            // 审核状态,如果不是未填报的就是已经拒绝(重新提交)的状态
            s.setApprovalStatus(noSubmitTaskIdSet.contains(Convert.toLong(s.getTaskId())) ? 1 : 2);
            //拒绝原因
            s.setInfoId(finalInfoIdMap.get(Convert.toLong(s.getTaskId())));
        });
        //根据时间分组
        Map<Date, List<SubListCurrentEvaluateTaskVO>> groupByTaskDateMap = listCurrentEvaluateTasks.parallelStream().filter(s -> Objects.nonNull(s.getTaskDate())).collect(groupingBy(s -> DateUtil.parse(DateUtil.format(s.getTaskDate(), "yyyy-MM-dd"), "yyyy-MM-dd")));

        // 数据封装
        List<ListCurrentEvaluateTaskVO> taskVOList = new ArrayList<>();
        for (Date date : groupByTaskDateMap.keySet()) {
            ListCurrentEvaluateTaskVO listCur = new ListCurrentEvaluateTaskVO();
            listCur.setDate(DateUtil.format(date, "yyyy-MM-dd"));
            if (CollUtil.isNotEmpty(groupByTaskDateMap.get(date))) {
                listCur.setTasks(groupByTaskDateMap.get(date));
                taskVOList.add(listCur);
            }
        }

        List<ListCurrentEvaluateTaskVO> collect = taskVOList.stream().sorted(Comparator.comparing(ListCurrentEvaluateTaskVO::getDate).reversed()).collect(Collectors.toList());

        return collect;
    }

    /**
     * 查询家长的所有填报指标
     *
     * @return
     */
    @Override
    public List<ListAllEvaluateTargetVOModule> listParentAllEvaluateTarget() {
        return targetGroupService.listAllEvaluateTarget(TaskUserTypeEnum.PARENT.getCode());
    }

    @Override
    public MongoPage<PageEvaluateTaskHistoryVO> pageParentEvaluateTaskHistory(PageEvaluateTaskHistoryQueryDTO dto, Integer roleType) {
        Page<TaskPO> taskPage = taskService.page(new Page<>(dto.getPageNum(), dto.getPageSize()), new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getRoleType, roleType)
                .eq(TaskPO::getSubmitStaffId, WebUtil.getStudentIdStr())
                .eq(TaskPO::getTaskStatus, TaskStatusEnum.SUBMITTED.getCode())
                .like(StrUtil.isNotBlank(dto.getTaskName()), TaskPO::getTaskName, dto.getTaskName())
                .orderByDesc(TaskPO::getId));
        if (CollUtil.isEmpty(taskPage.getRecords())) {
            return MongoPageConvertUtils.convertMongo(new MongoPage<>(taskPage.getTotal(), taskPage.getSize(), taskPage.getCurrent(), taskPage.getRecords()), Collections.emptyList());
        }

        //审核状态map
        Map<Long, Integer> approvalStatusMap = taskPage.getRecords().stream().collect(Collectors.toMap(TaskPO::getId, TaskPO::getApprovalStatus));

        List<PageEvaluateTaskHistoryVO> list = new ArrayList<>();
        //根据task表中的infoId获取mongodb信息
        List<Long> taskIds = taskPage.getRecords().stream().map(TaskPO::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(taskIds)) {
            Query query = new Query(Criteria.where("taskId").in(taskIds).and("deleted").is(0));
            query.with(Sort.by(Sort.Direction.DESC, new String[]{"updateTime"}));
            List<Info> infos = mongoTemplate.find(query, Info.class);
            for (Info evaluateInfo : infos) {
                List<SubmitInfo> submitInfoList = evaluateInfo.getSubmitInfoList();
                if (CollUtil.isEmpty(submitInfoList)) {
                    continue;
                }
                // 前两项的填写情况
                List<ListEvaluateTaskHistorySubVO> submitInfoSubList = new ArrayList<>();
                PageEvaluateTaskHistoryVO listEvaluateTaskHistoryVO = new PageEvaluateTaskHistoryVO()
                        .setDate(DateUtil.format(evaluateInfo.getSubmitTime(), "yyyy-MM-dd"))
                        .setApprovalStatus(approvalStatusMap.getOrDefault(Convert.toLong(evaluateInfo.getTaskId()), null))
                        .setTaskId(String.valueOf(evaluateInfo.getTaskId()))
                        .setTaskName(evaluateInfo.getTaskName())
                        .setSubmitInfoList(submitInfoSubList)
                        .setInfoId(evaluateInfo.getId());
                list.add(listEvaluateTaskHistoryVO);
                // 填充数据
                taskHelper.fullFillSubVO(submitInfoList, submitInfoSubList, evaluateInfo, true);
            }
        }
        return MongoPageConvertUtils.convertMongo(new MongoPage<>(taskPage.getTotal(), taskPage.getSize(), taskPage.getCurrent(), taskPage.getRecords()), list);
    }

    @Override
    public MongoPage<PageEvaluateTaskHistoryVO> pageStudentEvaluateTaskHistory(PageEvaluateTaskHistoryQueryDTO dto) {
        return this.pageParentEvaluateTaskHistory(dto, TaskRoleTypeEnum.STUDENT.getCode());
    }

    @Override
    public Page<TeacherApprovalHistoryVO> pageTeacherApprovalHistory(PageEvaluateTaskHistoryTeacherQueryDTO dto) {
        //校验参数
        validateTaskHistoryTeacherQueryParam(dto);
        if (CollectionUtils.isEmpty(dto.getClassIdList()) || CollectionUtils.isEmpty(dto.getClassIdStrList())) {
            return new Page<>(dto.getPageNum(), dto.getPageSize());
        }
        Set<Long> classIdSet = new HashSet<>(dto.getClassIdList());
        //查询当前老师下有多少学生
        List<String> teacherStudentIds = basicInfoRemote.queryStudentStrByStaffId(WebUtil.getStaffId());
        if (CollUtil.isEmpty(teacherStudentIds)) {
            return new Page<>(dto.getPageNum(), dto.getPageSize());
        }
        List<TaskPO> taskPOS = taskService.list(new LambdaQueryWrapper<TaskPO>()
                        .in(TaskPO::getRoleType, CollUtil.newArrayList(TaskRoleTypeEnum.PARENT.getCode(),TaskRoleTypeEnum.STUDENT.getCode()))
                        .eq(TaskPO::getCampusId, WebUtil.getCampusId())
                        .and(s -> s.in(TaskPO::getSubmitStaffId, teacherStudentIds).eq(TaskPO::getApprovalStatus, TaskApprovalEnum.APPROVAL_REFUSE.getCode())
                                .or(t -> t.eq(TaskPO::getApprovalUserId, WebUtil.getStaffId()).eq(TaskPO::getApprovalStatus, TaskApprovalEnum.APPROVAL_PASS.getCode())))
                        .eq(TaskPO::getTaskStatus, TaskStatusEnum.SUBMITTED.getCode())
//                .like(StrUtil.isNotBlank(dto.getTaskName()), Task::getTaskName, dto.getTaskName())
                        .between(Objects.nonNull(dto.getStartTime()) && Objects.nonNull(dto.getEndTime()), TaskPO::getSubmitTime, dto.getStartTime(), dto.getEndTime())
//                .orderByDesc(TaskPO::getId)
        );
        if (CollUtil.isEmpty(taskPOS)) {
            return new Page<>(dto.getPageNum(), dto.getPageSize());
        }

        //获取InfoId的Map
        Map<Long, String> infoIdMap = new HashMap<>();
        List<Long> taskIds = taskPOS.stream().map(TaskPO::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(taskIds)) {
            Query query = new Query(Criteria.where("taskId").in(taskIds).and("deleted").is(0));
            List<Info> infos = mongoTemplate.find(query, Info.class);
            infoIdMap = infos.stream().filter(s -> Objects.nonNull(s.getTaskId())).collect(Collectors.toMap(Info::getTaskId, Info::getId));
        }

        //获取学生姓名
        Map<Long, String> studentNameMap = new HashMap<>();
        List<Long> studentIds = taskPOS.stream().filter(s ->  CollUtil.newArrayList(TaskRoleTypeEnum.PARENT.getCode(),TaskRoleTypeEnum.STUDENT.getCode()).contains(s.getRoleType())).map(s -> Convert.toLong(s.getSubmitStaffId())).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(studentIds)) {
            StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
            studentByIdQuery.setStudentIds(studentIds);
            List<UcStudentClassBffVO> voList = basicInfoRemote.listByStudentIds(studentByIdQuery);
            if (CollUtil.isEmpty(voList)) {
                return new Page<>(dto.getPageNum(), dto.getPageSize());
            }
            //根据查询条件对学生进行过滤
            voList = voList.stream().filter(stu -> {
                //班级id不相等
                if (classIdSet.contains(stu.getClassId())) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            for (UcStudentClassBffVO ucStudentClassBffVO : voList) {
                studentNameMap.put(ucStudentClassBffVO.getStudentId(), ucStudentClassBffVO.getStudentName());
            }
        }

        //根据指标id和时间分组
        Map<String, List<TaskPO>> groupMap = taskPOS.stream().filter(s -> StrUtil.isNotBlank(s.getTaskName())).filter(s -> Objects.nonNull(s.getTaskDate())).collect(groupingBy(s -> {
            String taskName = s.getTaskName();
            Long targetId = s.getTargetId();
            String date = DateUtil.format(s.getTaskDate(), DatePattern.NORM_DATE_PATTERN);
            return StrUtil.join(StrPool.COMMA, taskName, targetId, date);
        }));

        List<TeacherApprovalHistoryVO> list = new ArrayList<>();
        for (String key : groupMap.keySet()) {
            Map<Long, String> finalInfoIdMap = infoIdMap;
            Map<Long, String> finalStudentNameMap = studentNameMap;
            if (CollUtil.isNotEmpty(groupMap.get(key))) {
                TeacherApprovalHistoryVO teacherApprovalHistoryVO = new TeacherApprovalHistoryVO();
                List<TaskPO> taskPOList = groupMap.get(key);
                taskPOList = taskPOList.stream().filter(taskPO -> {
                    return finalStudentNameMap.containsKey(Long.valueOf(taskPO.getSubmitStaffId()));
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(taskPOList)) {
                    continue;
                }
                // 根据提交时间倒序
                List<TeacherGroupVO> collect = taskPOList.stream().sorted(Comparator.comparing(TaskPO::getSubmitTime, Comparator.nullsLast(Date::compareTo)).reversed()).map(s -> {
                    TeacherGroupVO teacherGroupVO = new TeacherGroupVO();
                    teacherGroupVO.setDate(DateUtil.format(s.getTaskDate(), DatePattern.NORM_DATE_PATTERN));
                    teacherGroupVO.setTaskId(Convert.toStr(s.getId()));
                    teacherGroupVO.setInfoId(finalInfoIdMap.getOrDefault(s.getId(), ""));
                    teacherGroupVO.setStudentName(finalStudentNameMap.getOrDefault(Convert.toLong(s.getSubmitStaffId()), ""));
                    teacherGroupVO.setApprovalStatus(s.getApprovalStatus());
                    teacherGroupVO.setStudentId(s.getSubmitStaffId());
                    teacherGroupVO.setSubmitTime(s.getSubmitTime());
                    return teacherGroupVO;
                }).collect(Collectors.toList());

                teacherApprovalHistoryVO.setTaskTypes(collect);
                teacherApprovalHistoryVO.setTaskName(StrUtil.split(key, StrPool.COMMA).get(0));
                teacherApprovalHistoryVO.setDate(StrUtil.split(key, StrPool.COMMA).get(2));
                //聚合指标的时间取聚合数据中最新的一条提交时间
                if (CollUtil.isNotEmpty(collect)) {
                    teacherApprovalHistoryVO.setSubmitTime(CollUtil.getFirst(collect).getSubmitTime());
                }
                list.add(teacherApprovalHistoryVO);
            }
        }
        List<TeacherApprovalHistoryVO> collect = list.stream().sorted(Comparator.comparing(TeacherApprovalHistoryVO::getSubmitTime, Comparator.nullsLast(Date::compareTo)).reversed()).collect(Collectors.toList());

        List<TeacherApprovalHistoryVO> teacherApprovalHistoryVOS = new ArrayList<>();
        //模糊搜索
        search(dto, collect, teacherApprovalHistoryVOS);

        List<TeacherApprovalHistoryVO> historyVOS = teacherApprovalHistoryVOS.stream().skip((long) (dto.getPageNum() - 1) * dto.getPageSize()).limit(dto.getPageSize()).collect(Collectors.toList());
        Page<TeacherApprovalHistoryVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        page.setRecords(historyVOS);
        page.setTotal(teacherApprovalHistoryVOS.size());

        return page;
    }

    /**
     * 我审核的模糊搜索
     *
     * @param dto
     * @param collect
     * @param teacherApprovalHistoryVOS
     */
    private static void search(PageEvaluateTaskHistoryQueryDTO dto, List<TeacherApprovalHistoryVO> collect, List<TeacherApprovalHistoryVO> teacherApprovalHistoryVOS) {
        for (TeacherApprovalHistoryVO teacherApprovalHistoryVO : collect) {
            if (teacherApprovalHistoryVO.getTaskName().contains(dto.getTaskName())) {
                teacherApprovalHistoryVOS.add(teacherApprovalHistoryVO);
                continue;
            }
            List<TeacherGroupVO> taskTypes = teacherApprovalHistoryVO.getTaskTypes();
            if (CollUtil.isEmpty(taskTypes)) {
                continue;
            }
            List<TeacherGroupVO> teacherGroupVOS = taskTypes.stream().filter(s -> s.getStudentName().contains(dto.getTaskName())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(teacherGroupVOS)) {
                teacherApprovalHistoryVOS.add(teacherApprovalHistoryVO);
            }
        }
    }

    @Override
    public NodeDetailVo getParentTaskNextNode(ParentTaskNodeDTO dto) {
        Info evaluateInfo = infoLogic.getById(dto.getInfoId());
        if (Objects.isNull(evaluateInfo)) {
            return new NodeDetailVo();
        }

        LocalDateTime startTime = evaluateInfo.getSubmitTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atTime(0, 0, 0);
        LocalDateTime endTime = evaluateInfo.getSubmitTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atTime(23, 59, 59);

        //根据任务id倒序,和待评估列表顺序一致(根据提交时间倒序)
        List<TaskPO> taskPOS = taskService.list(new LambdaQueryWrapper<TaskPO>()
                .between(TaskPO::getTaskDate, startTime, endTime)
                .eq(TaskPO::getTargetId, evaluateInfo.getTargetId())
                .eq(TaskPO::getRoleType, TaskRoleTypeEnum.PARENT.getCode())
                .orderByDesc(TaskPO::getSubmitTime));

        //根据task表中的infoId获取mongodb信息
        Map<Long, String> infoIdMap = new HashMap<>();
        List<Long> taskIds = taskPOS.stream().map(TaskPO::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(taskIds)) {
            Query query = new Query(Criteria.where("taskId").in(taskIds).and("deleted").is(0));
            List<Info> infos = mongoTemplate.find(query, Info.class);
            infoIdMap = infos.stream().collect(Collectors.toMap(Info::getTaskId, Info::getId));
        }

        List<NodeDetailVo> handlerList = new ArrayList<>();
        Integer sortNum = 1;
        //新增排序值
        for (TaskPO taskPO : taskPOS) {
            NodeDetailVo preNextHandlerVO = new NodeDetailVo();
            preNextHandlerVO.setTaskId(taskPO.getId());
            preNextHandlerVO.setSortNum(sortNum);
            preNextHandlerVO.setStudentId(taskPO.getSubmitStaffId());
            preNextHandlerVO.setInfoId(infoIdMap.getOrDefault(taskPO.getId(), ""));
            preNextHandlerVO.setApprovalStatus(taskPO.getApprovalStatus());
            handlerList.add(preNextHandlerVO);
            sortNum++;
        }
        //获取当前数据的排序值
        List<NodeDetailVo> collect = handlerList.stream().filter(s -> evaluateInfo.getTaskId().equals(s.getTaskId())).collect(Collectors.toList());

        //查询当前老师下有多少学生
        List<Long> studentIds = basicInfoRemote.queryStudentByStaffId(WebUtil.getStaffId());

        //筛选全部出待审核的数据且为这个老师下的学生数据
        List<NodeDetailVo> nodeDetailVos = handlerList.stream().filter(s -> TaskApprovalEnum.APPROVAL_ING.getCode().equals(s.getApprovalStatus()))
                .filter(s -> {
                    boolean result = Boolean.FALSE;
                    if (CollUtil.isNotEmpty(studentIds) && studentIds.contains(Convert.toLong(s.getStudentId()))) {
                        result = Boolean.TRUE;
                    }
                    return result;
                }).collect(Collectors.toList());

        if (CollUtil.isEmpty(collect)) {
            return new NodeDetailVo();
        }
        NodeDetailVo currentData = collect.get(0);
        Integer currentNodeSortNum = currentData.getSortNum();

        //当前节点之前的所有的数据
        List<NodeDetailVo> preList = nodeDetailVos.stream().filter(s -> s.getSortNum() < currentNodeSortNum).collect(Collectors.toList());
        //当前节点之后的所有的数据
        List<NodeDetailVo> nextList = nodeDetailVos.stream().filter(s -> s.getSortNum() > currentNodeSortNum).collect(Collectors.toList());
        nextList.addAll(preList);

        if (CollUtil.isEmpty(nextList)) {
            return new NodeDetailVo();
        }
        return nextList.get(0);
    }

    /**
     * 获取时间枚举 1:近两周 2：近一月 3：近两月 4：近四月 5：本学期
     *
     * @return
     */
    @Override
    public List<MapVO> getTimeEnum() {
        List<MapVO> mapVOS = new ArrayList<>();
        ExamTimeTypeEnum[] values = ExamTimeTypeEnum.values();
        for (ExamTimeTypeEnum value : values) {
            MapVO mapVO = new MapVO();
            mapVO.setKey(value.getCode());
            mapVO.setValue(value.getMessage());
            mapVOS.add(mapVO);
        }
        return mapVOS;
    }


    @Override
    public boolean finishTask(Long taskId) {
        TaskPO update = new TaskPO();
        update.setTaskStatus(TaskStatusEnum.SUBMITTED.getCode()).setId(taskId);
        return taskLogic.update(update);
    }


}
