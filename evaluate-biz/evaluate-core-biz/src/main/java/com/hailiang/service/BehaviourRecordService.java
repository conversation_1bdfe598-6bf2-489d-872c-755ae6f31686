package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.QueryBehaviourScoreDTO;
import com.hailiang.model.dto.StudentBehaviourScoreDTO;
import com.hailiang.model.dto.query.*;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.Target;
import com.hailiang.model.medal.dto.StudentMedalQueryDTO;
import com.hailiang.model.medal.vo.MedalInfoVO;
import com.hailiang.model.query.OptionIdQuery;
import com.hailiang.model.report.dto.PageStudentRecordQueryDTO;
import com.hailiang.model.report.vo.ReportModuleScoreStatisticsVO;
import com.hailiang.model.report.vo.ReportModuleScoreVO;
import com.hailiang.model.request.EvaluateFixDataRequest;
import com.hailiang.model.response.EvaluateFixDataResponse;
import com.hailiang.model.vo.*;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import javax.servlet.http.HttpServletResponse;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface BehaviourRecordService extends IService<BehaviourRecord> {

//    boolean saveBehaviourRecords(List<BehaviourRecordSaveDTO> dtos);


    /**
     * 综合得分
     *
     * @param behaviourRecordQueryDTO
     * @return
     */
    BehaviourRecordSynthesisScoreVO getSynthesisScore(BehaviourRecordQueryDTO behaviourRecordQueryDTO);


    /**
     * 综合得分(不包含积分排名)
     *
     * @param behaviourRecordQueryDTO
     * @return
     */
    BehaviourRecordSynthesisScoreVO getSynthesisScoreWithoutRank(BehaviourRecordQueryDTO behaviourRecordQueryDTO);

//    /**
//     * 综合素质
//     *
//     * @param behaviourRecordQueryDTO
//     * @return
//     */
//    List<BehaviourComprehensiveQualityVO> listComprehensiveQuality(BehaviourRecordQueryDTO behaviourRecordQueryDTO);

//    /**
//     * 行为表现
//     *
//     * @param behaviourRecordQueryDTO
//     * @return
//     */
//    BehavioralExpressionVO getBehavioralExpression(BehaviourRecordQueryDTO behaviourRecordQueryDTO);

//    /**
//     * 成长趋势
//     *
//     * @param behaviourRecordQueryDTO
//     * @return
//     */
//    BehaviourGrowthTrendVO getGrowthTrend(BehaviourRecordQueryDTO behaviourRecordQueryDTO);

//    /**
//     * 智能评价
//     *
//     * @param behaviourRecordQueryDTO
//     * @return
//     */
//    BehaviourIntelligentEvaluationVO getIntelligentEvaluation(BehaviourRecordQueryDTO behaviourRecordQueryDTO);

    /**
     * 通过枚举获取开始时间和结束时间
     *
     * @param timeType
     * @return
     */
    SubmitTimeVO getTime(Integer timeType, String campusSectionId);

//    BehaviourAnalysisVO getBehaviourAnalysis(BehaviourAnalysisDTO dto);

    List<BehaviourAnalysisVOInnerModuleVO> getBehaviourAnalysis(List<StuBehaviorOptionVO> optionVOS);

    /**
     * 学生档案
     *
     * @param behaviourStudentFileQueryDTO
     * @return
     */
    List<BehaviourStudentFileVO> listStudentFile(BehaviourStudentFileQueryDTO behaviourStudentFileQueryDTO);

    List<BehaviourStudentFileVO> listStudentScores(BehaviourStudentFileQueryDTO behaviourStudentFileQueryDTO,
                                                   List<EduStudentInfoVO> eduStudentInfoVOList);

//    @Deprecated
//    ListModuleDetailVO listModuleDetail(ListModuleDetailDTO dto);

    ListModuleDetailNewVO listModuleDetailForParent(ListModuleDetailForParentDTO dto);

    ListModuleDetailNewVO listModuleDetailForCheck(BehaviourRecordQueryDTO dto);

    ListModuleDetailNewVO listModuleDetailNew(ListModuleDetailDTO dto);

    /**
     * 如果没有加分项分值,分数信息设置为空
     *
     * @param behaviorRecordId 行为记录id
     * @return
     */
    Boolean updateScoreInfoIsNull(@Param("behaviorRecordId") Long behaviorRecordId);

    /**
     * 查学生奖章信息
     *
     * @param dto
     * @return
     */
    Page<MedalInfoVO> pageStudentMedalInfo(StudentMedalQueryDTO dto);

    /**
     * 按条件查询学生指标分数
     *
     * @param dto
     * @return
     */
    Map<String, List<StudentBehaviourScoreDTO>> queryStudentBehaviourScore(QueryBehaviourScoreDTO dto);

    /**
     * Step-13.0 按条件查询直学生指标分数
     *
     * @param dto
     * @return
     */
    Map<String, List<StudentBehaviourScoreDTO>> queryStudentBehaviourScoreV2(QueryBehaviourScoreDTO dto,
                                                                             List<String> subjectCodes);


    Page<ListModuleDetailVODateNewInfo> pageStudentRecords(PageStudentRecordQueryDTO dto);

    /**
     * 按条件查询学生五育分数
     *
     * @param dto
     * @return
     */
    List<ReportModuleScoreVO> queryStudentScore(QueryBehaviourScoreDTO dto);

    /**
     * 按条件查询学生五育统计分数
     *
     * @param dto
     * @return
     */
    List<ReportModuleScoreStatisticsVO> queryStudentScoreStatistics(QueryBehaviourScoreDTO dto);

    /**
     * 按条件查询学生五育统计分数（班主任）
     */
    List<ReportModuleScoreStatisticsVO> queryStudentScoreStatisticsByMaster(QueryBehaviourScoreDTO dto);


    List<BehaviourRecord> getListByInfoId(String infoId);

    /**
     * 根据校区id列表 获取当天有点评的学生
     *
     * @param campusIdList 校区id列表
     * @return 学生id列表
     */
    List<BehaviourRecord> getTodayBehaviourStudentList(List<String> campusIdList);

    /**
     * 根据校区id和最近一次推送时间 获取最近一次推送时间之后有点评的学生
     *
     * @param campusId     校区id
     * @param lastPushTime 最近一次推送时间
     * @return 学生id列表
     */
    List<BehaviourRecord> getBehaviourStudentListByLastPushTime(String campusId, Date lastPushTime, Date endDate);


    /**
     * 根据班级ID和提交时间获取分数
     */
    List<BehaviourRecord> listByStudentIdAndSubmitTime(List<String> studentIds,
                                                       Date startSubmitTime, Date endSubmitTime);

    /**
     * 根据班级ID和提交时间获取分数
     */
    List<BehaviourRecord> listByClassIdAndSubmitTime(QueryBehaviourScoreDTO queryBehaviourScoreDTO);

    /**
     * 根据点评人ID和班级ID和提交时间获取分数
     */

    List<BehaviourRecord> listByClassIdAndAppraisalIdAndSubmitTime(String appraisalId,
                                                                   String classId,
                                                                   Date startSubmitTime,
                                                                   Date endSubmitTime);

    /**
     * 统计老师十四天之内点评项
     */
    List<BehaviourRecord> countTeacherRecord();

    /**
     * 获取班级学科过滤后的行为记录
     *
     * @param classSubjectQueryDTO
     * @return
     */
    List<BehaviourRecord> getClassSubjectFilterList(BehaviourClassSubjectQueryDTO classSubjectQueryDTO);

    /**
     * 根据选项id集合查询范围内的行为记录
     *
     * @param query
     * @return
     */
    List<BehaviourRecord> listByOptionIds(OptionIdQuery query);

    /**
     * 查询所有扣分延续的指标
     *
     * @return
     */
    List<Target> listScoreContinuationFlag();

    /**
     * 修复数据，type=1:查询，type=2:修改数据
     * @return
     */
    void fixData(EvaluateFixDataRequest request, HttpServletResponse response);
}