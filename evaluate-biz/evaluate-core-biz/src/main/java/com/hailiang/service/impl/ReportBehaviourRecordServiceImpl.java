package com.hailiang.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.common.mybatis.base.BaseEntity;
import com.hailiang.enums.report.ReportBehaviourTypeEnum;
import com.hailiang.mapper.ReportBehaviourRecordMapper;
import com.hailiang.model.dto.report.ReportBehaviourRecordDiffDTO;
import com.hailiang.model.entity.ReportBehaviourRecord;
import com.hailiang.service.ReportBehaviourRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/1/13 11:54
 */
@Service("reportBehaviourRecordService")
@Slf4j
public class ReportBehaviourRecordServiceImpl extends ServiceImpl<ReportBehaviourRecordMapper, ReportBehaviourRecord> implements ReportBehaviourRecordService {
    @Override
    public List<Long> listNoSendRecordByDetail(String detailId) {
        List<ReportBehaviourRecord> reportBehaviourRecords = this.list(new LambdaQueryWrapper<ReportBehaviourRecord>()
                .eq(ReportBehaviourRecord::getReviewDetailId, detailId)
                .eq(ReportBehaviourRecord::getType, 3));
        if (CollUtil.isEmpty(reportBehaviourRecords)) {
            log.info("[获取当前推送报告未推送的数据-无数据，直接返回");
            return Collections.emptyList();
        }
        return CollStreamUtil.toList(reportBehaviourRecords, ReportBehaviourRecord::getBehaviourRecordId);
    }

    @Override
    public List<ReportBehaviourRecord> listNoSendRecordByDetailList(Long detailId) {
        List<ReportBehaviourRecord> reportBehaviourRecords = this.list(new LambdaQueryWrapper<ReportBehaviourRecord>()
                .eq(ReportBehaviourRecord::getReviewDetailId, detailId)
                .eq(ReportBehaviourRecord::getType, 3));
        if (CollUtil.isEmpty(reportBehaviourRecords)) {
            log.info("[获取当前推送报告未推送的数据-无数据，直接返回");
            return Collections.emptyList();
        }
        return reportBehaviourRecords;
    }

    @Override
    public List<ReportBehaviourRecord> listNoSendRecordByDetailIdList(List<Long> detailIdList) {
        List<ReportBehaviourRecord> reportBehaviourRecords = this.list(new LambdaQueryWrapper<ReportBehaviourRecord>()
                .in(ReportBehaviourRecord::getReviewDetailId, detailIdList)
                .eq(ReportBehaviourRecord::getType, 3));
        if (CollUtil.isEmpty(reportBehaviourRecords)) {
            log.info("[获取当前推送报告未推送的数据-无数据，直接返回");
            return Collections.emptyList();
        }
        return reportBehaviourRecords;
    }

    @Override
    public List<ReportBehaviourRecord> listSendRecordByDetailList(Long detailId) {
        return this.list(new LambdaQueryWrapper<ReportBehaviourRecord>()
                .eq(ReportBehaviourRecord::getReviewDetailId, detailId)
                .eq(BaseEntity::getDeleted, 0)
                .in(ReportBehaviourRecord::getType, Arrays.asList(1, 2)));
    }

    @Override
    public ReportBehaviourRecordDiffDTO listSendRecordDIffByDetailList(Long detailId) {
        List<ReportBehaviourRecord> reportBehaviourRecords = this.listSendRecordByDetailList(detailId);
        ReportBehaviourRecordDiffDTO reportBehaviourRecordDiffDTO = new ReportBehaviourRecordDiffDTO();
        List<ReportBehaviourRecord> behaviourRecordList =  new ArrayList<>();
        List<ReportBehaviourRecord> helpBehaviourRecordList =  new ArrayList<>();
        reportBehaviourRecordDiffDTO.setBehaviourRecordList(behaviourRecordList);
        reportBehaviourRecordDiffDTO.setHelpBehaviourRecordList(helpBehaviourRecordList);
        reportBehaviourRecords.forEach(reportBehaviourRecord -> {
            if(Objects.equals(reportBehaviourRecord.getRecordType(), ReportBehaviourTypeEnum.BEHAVIOUR_RECORD.getCode())){
                behaviourRecordList.add(reportBehaviourRecord);
            }else if(Objects.equals(reportBehaviourRecord.getRecordType(), ReportBehaviourTypeEnum.HELP_BEHAVIOUR_RECORD.getCode())){
                helpBehaviourRecordList.add(reportBehaviourRecord);
            }
        });
        reportBehaviourRecordDiffDTO.setBehaviourRecordList(behaviourRecordList);
        reportBehaviourRecordDiffDTO.setHelpBehaviourRecordList(helpBehaviourRecordList);
        return reportBehaviourRecordDiffDTO;
    }
}
