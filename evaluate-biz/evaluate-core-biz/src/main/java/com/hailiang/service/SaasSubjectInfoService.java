package com.hailiang.service;

import com.hailiang.model.dto.QueryClassSubjectRelDTO;
import com.hailiang.model.dto.response.TeachSubjectResponse;


/**
 * 获取组装Saas科目信息
 *
 * @Description: 获取组装Saas科目信息
 * @Author: Jovi
 * @Date: Created in 2024/11/29
 * @Version: 2.0.0
 */
public interface SaasSubjectInfoService {


    /**
     * 根据班级ID和教职工ID，获取该教职工在该班级下的任教科目信息（注意：此处只处理单班级情况，不是多班级情况）
     *
     * @param staffId
     * @return
     */
    TeachSubjectResponse getRelatedSubjectV2(QueryClassSubjectRelDTO request, String staffId);
}