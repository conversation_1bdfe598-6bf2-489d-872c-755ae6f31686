package com.hailiang.service;

import com.hailiang.model.entity.ReportVideoFilePO;

/**
 * 视频转码服务
 *
 * @Description: 视频转码服务
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: Created in 2025-01-07
 * @Version: 1.0.0
 */
public interface EvaluateVideoTranscodingService {

    /**
     * 创建转码任务
     * @param moralFilePO moralFilePO实体类
     */
    void createTranscodingTask(ReportVideoFilePO reportVideoFilePO);

    /**
     * 同步转码状态
     */
    void syncTranscodingResultHandle();
}
