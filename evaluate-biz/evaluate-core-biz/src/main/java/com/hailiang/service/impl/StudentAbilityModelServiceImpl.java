package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.common.lock.annotation.RLock;
import com.hailiang.common.lock.model.LockTimeoutStrategy;
import com.hailiang.constant.Constant;
import com.hailiang.constant.StudentAbilityExamSourceConstant;
import com.hailiang.convert.StudentModelV2Convert;
import com.hailiang.enums.DimStatusEnum;
import com.hailiang.enums.FixSubjectCodeEnum;
import com.hailiang.enums.report.ScoreStandardExamTypeEnum;
import com.hailiang.enums.studentModel.BusinessMergeTypeEnum;
import com.hailiang.enums.studentModel.StudentAbilityModelContrastRangeEnum;
import com.hailiang.enums.studentModel.StudentAbilityModelItemConfigTypeEnum;
import com.hailiang.enums.target.TargetBusinessMergeSubmitTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.logic.TermLogic;
import com.hailiang.manager.*;
import com.hailiang.mapper.StudentAbilityModelMapper;
import com.hailiang.model.StudentAbilityOutcomeDTO;
import com.hailiang.model.domain.studentmodel.*;
import com.hailiang.model.domain.studentmodel.compare.*;
import com.hailiang.model.dto.activity.rule.query.RuleModuleInfoVO;
import com.hailiang.model.dto.save.SysArchivedLogDTO;
import com.hailiang.model.dto.studentmodel.StudentAbilityModelConfigDTO;
import com.hailiang.model.entity.*;
import com.hailiang.model.report.dto.ExamConfigDTO;
import com.hailiang.model.request.studentmodel.StudentAbilityModelRequest;
import com.hailiang.model.request.studentmodel.StudentAbilityModelV2Request;
import com.hailiang.model.request.studentmodel.StudentAbilityModelV2UpdateEnabledRequest;
import com.hailiang.model.request.studentmodel.item.*;
import com.hailiang.model.response.studentmodel.*;
import com.hailiang.portrait.entity.StudentAbilityModelArchivedPO;
import com.hailiang.portrait.query.StuAbilityUpgradeQuery;
import com.hailiang.service.StudentAbilityModelService;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * 业务实现
 *
 * <AUTHOR> 2024-04-19 15:29:38
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StudentAbilityModelServiceImpl extends ServiceImpl<StudentAbilityModelMapper, StudentAbilityModel> implements StudentAbilityModelService {

    private final StudentAbilityModelConfigManager studentAbilityModelConfigManager;
    private final StudentAbilityModelManager studentAbilityModelManager;
    private final StudentAbilityModelItemManager studentAbilityModelItemManager;
    private final SubjectBusinessMergeManager subjectBusinessMergeManager;
    private final TargetBusinessMergeManager targetBusinessMergeManager;
    private final StudentAbilityModelItemConfigManager studentAbilityModelItemConfigManager;
    private final StudentAbilityModelArchivedManager studentAbilityModelArchivedManager;
    private final ThreadPoolTaskExecutor evaluateExecutor;
    private final RedisTemplate<String, Object> redisTemplate;
    private final TermLogic termLogic;
    private final StudentModelV2Convert studentModelV2Convert;
    private final SysArchivedLogManager sysArchivedLogManager;
    private final RedisUtil redisUtil;

    @Override
    public StudentAbilityModelResponse detailStudentAbilityModel(String campusId) {
        log.info("[学生能力模型]-准备查询当前校区学生能力模型数据，校区：{}", campusId);
        StudentAbilityModel studentAbilityModel = this.getOne(Wrappers.<StudentAbilityModel>lambdaQuery().eq(StudentAbilityModel::getCampusId, campusId));
        if (ObjectUtil.isEmpty(studentAbilityModel)) {
            log.info("[学生能力模型]-当前校区未配置学生能力模型，直接返回");
            return null;
        }
        StudentAbilityModelResponse studentAbilityModelResponse = new StudentAbilityModelResponse();
        studentAbilityModelResponse.setId(studentAbilityModel.getId());
        studentAbilityModelResponse.setAbilityModelName(studentAbilityModel.getAbilityModelName());
        studentAbilityModelResponse.setContrastRange(studentAbilityModel.getContrastRange());

        List<StudentAbilityModelConfig> studentAbilityModelConfigs = studentAbilityModelConfigManager
                .list(Wrappers.<StudentAbilityModelConfig>lambdaQuery().eq(StudentAbilityModelConfig::getAbilityModelId, studentAbilityModel.getId()).orderByAsc(StudentAbilityModelConfig::getSortIndex));
        if (ObjectUtil.isNotEmpty(studentAbilityModelConfigs)) {
            List<StudentAbilityModelConfigDTO> studentAbilityModelConfigDTOS = new ArrayList<>();

            studentAbilityModelConfigs.forEach(d -> {
                StudentAbilityModelConfigDTO studentAbilityModelConfigDTO = new StudentAbilityModelConfigDTO();
                BeanUtil.copyProperties(d, studentAbilityModelConfigDTO);
                studentAbilityModelConfigDTO.setAbilityId(d.getId());

                studentAbilityModelConfigDTOS.add(studentAbilityModelConfigDTO);
            });
            studentAbilityModelResponse.setStudentAbilityModelConfigs(studentAbilityModelConfigDTOS);
        }
        log.info("[学生能力模型]-查询完成，返回数据：{}", JSONUtil.toJsonStr(studentAbilityModelResponse));
        return studentAbilityModelResponse;
    }

    @Override
    public StudentAbilityModelResponse getModelDetail(String campusId, String schoolYear, Boolean isCurrentYear) {
        if (Boolean.TRUE.equals(isCurrentYear)) {
            return detailStudentAbilityModel(campusId);
        }

        StudentAbilityModelArchivedPO modelArchive = studentAbilityModelArchivedManager.getByYear(campusId, schoolYear);
        if (ObjectUtil.isEmpty(modelArchive)) {
            log.warn("【学生能力模型查询】-当前学年和学期下，校区未配置学生能力模型，直接返回最新 {}, {}", campusId, schoolYear);
            return detailStudentAbilityModel(campusId);
        }
        StudentAbilityModelResponse studentAbilityModelResponse = new StudentAbilityModelResponse();
        studentAbilityModelResponse.setId(modelArchive.getId());
        studentAbilityModelResponse.setAbilityModelName(modelArchive.getAbilityModelName());
        studentAbilityModelResponse.setContrastRange(modelArchive.getContrastRange());
        studentAbilityModelResponse.setRuleModuleInfoVOList(JSON.parseArray(modelArchive.getRuleModuleList(), RuleModuleInfoVO.class));

        if (Objects.isNull(modelArchive.getConfig())) {
            log.error("【学生能力模型查询】-当前学年和学期下，校区配置学生能力模型异常，直接返回最新 {}, {}", campusId, schoolYear);
            return detailStudentAbilityModel(campusId);
        }

        try {
            List<StudentAbilityModelConfigDTO> configList = JSON.parseArray(modelArchive.getConfig(), StudentAbilityModelConfigDTO.class);

            studentAbilityModelResponse.setStudentAbilityModelConfigs(configList);
        } catch (Exception ex) {
            log.error("【学生能力模型查询】-当前学年和学期下，校区配置学生能力模型序列化异常，直接返回最新 {}, {}", campusId, schoolYear);
            return detailStudentAbilityModel(campusId);
        }

        return studentAbilityModelResponse;
    }

    @Override
    @Transactional
    @RLock(waitTime = 10, leaseTime = 20, name = "saveStudentAbilityModel", keys = {"#campusId"}, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public boolean saveStudentAbilityModel(String campusId, StudentAbilityModelRequest studentAbilityModelRequest) {
        log.info("准备保存学生能力模型数据,数据：{}", JSONUtil.toJsonStr(studentAbilityModelRequest));
        List<StudentAbilityModelConfigDTO> studentAbilityModelConfigDTOs = studentAbilityModelRequest.getStudentAbilityModelConfigs();
        Assert.notEmpty(studentAbilityModelConfigDTOs, "能力模型配置不能为空");
        Assert.isTrue(studentAbilityModelConfigDTOs.size() >= 3, "能力项最少不能少于3项");
        Assert.isTrue(studentAbilityModelConfigDTOs.size() <= 15, "能力项最多可添加15项");
        // 查看该校区之前有没有能力模型数据
        StudentAbilityModel oldStudentAbilityModel = this.getOne(Wrappers.<StudentAbilityModel>lambdaQuery().eq(StudentAbilityModel::getCampusId, WebUtil.getCampusId()));
        if (ObjectUtil.isNotEmpty(oldStudentAbilityModel)) {
            log.info("当前校区已有能力模型配置数据，准备删除之前数据");
            this.removeById(oldStudentAbilityModel.getId());
            log.info("已删除校区能力模型数据");
            studentAbilityModelConfigManager.update(Wrappers.<StudentAbilityModelConfig>lambdaUpdate()
                    .set(StudentAbilityModelConfig::getDeleted, true)
                    .set(StudentAbilityModelConfig::getUpdateBy, WebUtil.getStaffId())
                    .set(StudentAbilityModelConfig::getUpdateTime, DateUtil.now())
                    .eq(StudentAbilityModelConfig::getAbilityModelId, oldStudentAbilityModel.getId()));
            log.info("已删除校区能力模型配置数据");
        }
        // 1. 保存学生能力模型
        StudentAbilityModel studentAbilityModel = new StudentAbilityModel();
        studentAbilityModel.setAbilityModelName(studentAbilityModelRequest.getAbilityModelName());
        studentAbilityModel.setContrastRange(studentAbilityModelRequest.getContrastRange());
        this.save(studentAbilityModel);
        log.info("已新增校区能力模型数据");
        // 2. 保存学生能力模型配置
        List<StudentAbilityModelConfig> studentAbilityModelConfigs = new ArrayList<>();
        for (StudentAbilityModelConfigDTO studentAbilityModelConfigDTO : studentAbilityModelConfigDTOs) {
            StudentAbilityModelConfig studentAbilityModelConfig = new StudentAbilityModelConfig();
            studentAbilityModelConfig.setAbilityModelId(studentAbilityModel.getId());
            studentAbilityModelConfig.setAbilityName(studentAbilityModelConfigDTO.getAbilityName());
            studentAbilityModelConfig.setAbilityType(studentAbilityModelConfigDTO.getAbilityType());
            studentAbilityModelConfig.setAbilityConfig(studentAbilityModelConfigDTO.getAbilityConfig());
            studentAbilityModelConfig.setSortIndex(studentAbilityModelConfigDTO.getSortIndex());

            studentAbilityModelConfigs.add(studentAbilityModelConfig);
        }
        studentAbilityModelConfigManager.saveBatch(studentAbilityModelConfigs);
        log.info("已新增校区能力模型配置数据");

        return true;
    }

    @Override
    @Transactional
    @RLock(waitTime = 10, leaseTime = 20, name = "saveStudentAbilityModel", keys = {"#campusId"}, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public boolean saveStudentAbilityModelNew(String campusId, StudentAbilityModelRequest studentAbilityModelRequest) {
        log.info("准备保存学生能力模型数据,数据：{}", JSONUtil.toJsonStr(studentAbilityModelRequest));
        List<StudentAbilityModelConfigDTO> studentAbilityModelConfigDTOs = studentAbilityModelRequest.getStudentAbilityModelConfigs();
        Assert.notEmpty(studentAbilityModelConfigDTOs, "能力模型配置不能为空");
        Assert.isTrue(studentAbilityModelConfigDTOs.size() >= 3, "能力项最少不能少于3项");
        Assert.isTrue(studentAbilityModelConfigDTOs.size() <= 15, "能力项最多可添加15项");
        // 查看该校区之前有没有能力模型数据
        StudentAbilityModel oldStudentAbilityModel = this.getOne(Wrappers.<StudentAbilityModel>lambdaQuery().eq(StudentAbilityModel::getCampusId, WebUtil.getCampusId()));
        if (ObjectUtil.isNotEmpty(oldStudentAbilityModel)) {
            log.info("当前校区之前已有能力模型数据，准备更新");
            studentAbilityModelManager.update(Wrappers.<StudentAbilityModel>lambdaUpdate()
                    .set(StudentAbilityModel::getAbilityModelName, studentAbilityModelRequest.getAbilityModelName())
                    .set(StudentAbilityModel::getContrastRange, studentAbilityModelRequest.getContrastRange())
                    .set(StudentAbilityModel::getUpdateBy, WebUtil.getStaffId())
                    .set(StudentAbilityModel::getUpdateTime, DateUtil.now())
                    .eq(StudentAbilityModel::getId, oldStudentAbilityModel.getId()));
            log.info("已更新校区能力模型数据");
            // 根据新增、删除、修改的不同情况判断
            // 查一下表中的配置
            List<StudentAbilityModelConfig> studentAbilityModelConfigs = studentAbilityModelConfigManager.list(Wrappers.<StudentAbilityModelConfig>lambdaQuery().eq(StudentAbilityModelConfig::getAbilityModelId, oldStudentAbilityModel.getId()));
            List<Long> oldAbilityIds = CollStreamUtil.toList(studentAbilityModelConfigs, StudentAbilityModelConfig::getId);
            // 新增的数据
            List<StudentAbilityModelConfigDTO> addConfigs = studentAbilityModelConfigDTOs.stream().filter(d -> ObjectUtil.isEmpty(d.getAbilityId())).collect(Collectors.toList());
            List<Long> newAbilityIds = studentAbilityModelConfigDTOs.stream().filter(d -> ObjectUtil.isNotEmpty(d.getAbilityId())).map(d -> d.getAbilityId()).collect(Collectors.toList());
            // 删除的数据
            List<Long> delAbilityIds = oldAbilityIds.stream().filter(d -> !newAbilityIds.contains(d)).collect(Collectors.toList());
            // 修改的数据
            List<StudentAbilityModelConfigDTO> updateConfigs = studentAbilityModelConfigDTOs.stream().filter(d -> oldAbilityIds.contains(d.getAbilityId())).collect(Collectors.toList());
            log.info("新增条数：{},删除条数：{},修改条数：{}", addConfigs.size(), delAbilityIds.size(), updateConfigs.size());
            // 处理新增
            if (CollUtil.isNotEmpty(addConfigs)) {
                List<StudentAbilityModelConfig> addStudentAbilityModelConfigs = new ArrayList<>();
                for (StudentAbilityModelConfigDTO d : addConfigs) {
                    StudentAbilityModelConfig studentAbilityModelConfig = new StudentAbilityModelConfig();
                    studentAbilityModelConfig.setAbilityModelId(oldStudentAbilityModel.getId());
                    studentAbilityModelConfig.setAbilityName(d.getAbilityName());
                    studentAbilityModelConfig.setAbilityType(d.getAbilityType());
                    studentAbilityModelConfig.setAbilityConfig(d.getAbilityConfig());
                    studentAbilityModelConfig.setSortIndex(d.getSortIndex());

                    addStudentAbilityModelConfigs.add(studentAbilityModelConfig);
                }
                studentAbilityModelConfigManager.saveBatch(addStudentAbilityModelConfigs);
                log.info("【已新增】校区能力模型配置数据");
            }

            // 处理删除
            if (CollUtil.isNotEmpty(delAbilityIds)) {
                studentAbilityModelConfigManager.update(Wrappers.<StudentAbilityModelConfig>lambdaUpdate()
                        .set(StudentAbilityModelConfig::getDeleted, true)
                        .set(StudentAbilityModelConfig::getUpdateBy, WebUtil.getStaffId())
                        .set(StudentAbilityModelConfig::getUpdateTime, DateUtil.now())
                        .in(StudentAbilityModelConfig::getId, delAbilityIds));
                log.info("【已删除】校区能力模型配置数据");
            }

            // 处理修改
            if (CollUtil.isNotEmpty(updateConfigs)) {
                DateTime nowDate = DateUtil.date();
                for (StudentAbilityModelConfigDTO d : updateConfigs) {
                    studentAbilityModelConfigManager.update(Wrappers.<StudentAbilityModelConfig>lambdaUpdate()
                            .set(StudentAbilityModelConfig::getAbilityName, d.getAbilityName())
                            .set(StudentAbilityModelConfig::getAbilityType, d.getAbilityType())
                            .set(StudentAbilityModelConfig::getAbilityConfig, d.getAbilityConfig())
                            .set(StudentAbilityModelConfig::getSortIndex, d.getSortIndex())
                            .set(StudentAbilityModelConfig::getUpdateBy, WebUtil.getStaffId())
                            .set(StudentAbilityModelConfig::getUpdateTime, nowDate)
                            .eq(StudentAbilityModelConfig::getId, d.getAbilityId()));
                }
                log.info("【已修改】校区能力模型配置数据");
            }
        } else {
            log.info("当前校区之前没有能力模型数据，准备新增");
            // 1. 保存学生能力模型
            StudentAbilityModel studentAbilityModel = new StudentAbilityModel();
            studentAbilityModel.setAbilityModelName(studentAbilityModelRequest.getAbilityModelName());
            studentAbilityModel.setContrastRange(studentAbilityModelRequest.getContrastRange());
            this.save(studentAbilityModel);
            log.info("已新增校区能力模型数据");

            // 2. 保存学生能力模型配置
            List<StudentAbilityModelConfig> studentAbilityModelConfigs = new ArrayList<>();
            for (StudentAbilityModelConfigDTO studentAbilityModelConfigDTO : studentAbilityModelConfigDTOs) {
                StudentAbilityModelConfig studentAbilityModelConfig = new StudentAbilityModelConfig();
                studentAbilityModelConfig.setAbilityModelId(studentAbilityModel.getId());
                studentAbilityModelConfig.setAbilityName(studentAbilityModelConfigDTO.getAbilityName());
                studentAbilityModelConfig.setAbilityType(studentAbilityModelConfigDTO.getAbilityType());
                studentAbilityModelConfig.setAbilityConfig(studentAbilityModelConfigDTO.getAbilityConfig());
                studentAbilityModelConfig.setSortIndex(studentAbilityModelConfigDTO.getSortIndex());

                studentAbilityModelConfigs.add(studentAbilityModelConfig);
            }
            studentAbilityModelConfigManager.saveBatch(studentAbilityModelConfigs);
            log.info("已新增校区能力模型配置数据");
        }
        // 开启一个多线程，删掉指定的缓存（STUDENT_ABILITY_MODEL_SUMITIDS:校区id前缀），用于学生画像中的显示
        evaluateExecutor.execute(() -> this.deleteStudentAbilityModelSumitIdsCache(campusId));

        return true;
    }

    /**
     * 获取指定时间之前最近的一条记录
     *
     * @param campusId
     * @param dateTime
     * @return
     */
    @Override
    public StudentAbilityModel getStudentAbilityModelByDate(String campusId, String dateTime) {
        // 默认当天
        if (ObjectUtil.isEmpty(dateTime)) {
            dateTime = DateUtil.now();
        }
        StudentAbilityModel studentAbilityModel = studentAbilityModelManager.getStudentAbilityModelByDate(campusId, dateTime);
        // 兜底 当学校未配置的时候 返回默认值
        if (ObjectUtil.isEmpty(studentAbilityModel)) {
            studentAbilityModel = new StudentAbilityModel();
            studentAbilityModel.setAbilityModelName("学生能力模型");
            studentAbilityModel.setContrastRange(1);
        }

        return studentAbilityModel;
    }

    @Override
    @Transactional
    public boolean saveStudentAbilityModelV2(String campusId, String staffId, StudentAbilityModelV2Request studentAbilityModelRequest) {
        String lockKey = RedisKeyConstants.STUDENT_ABILITY_MODEL_CONFIG_SAVE_V2_KEY_PREFIX + campusId;
        String requestId = Thread.currentThread().getId() + "";
        try {
            if (!redisUtil.tryLock(lockKey, requestId, Constant.TEN)) {
                throw new BizException("当前配置正在保存中");
            }
            //1.校验学生能力模型参数
            checkStudentAbilityModelV2Param(studentAbilityModelRequest, campusId);
            //2.查询现存学生能力模型
            StudentAbilityModel studentAbilityModel = studentAbilityModelManager.getStudentAbilityModel(campusId);
            //3.构建新增/修改的能力项配置信息
            StudentAbilityModelDO studentAbilityModelDO = buildStudentAbilityModelDO(WebUtil.getTenantId(), WebUtil.getSchoolId(), campusId, staffId, studentAbilityModel, studentAbilityModelRequest);
            if (Objects.isNull(studentAbilityModel)) {
                //当前不存在，直接新增
                studentAbilityModelManager.saveStudentAbilityModelDO(studentAbilityModelDO);
                return true;
            }
            StudentAbilityModelCompareDO studentAbilityModelCompareDO = new StudentAbilityModelCompareDO();
            //4.查询现存能力项的具体配置
            StudentAbilityModelDO existStudentAbilityModelDO = studentAbilityModelManager.getStudentAbilityModelDO(studentAbilityModel);
            //5.对比学生能力模型配置
            compareStudentAbilityModelItem(studentAbilityModelDO.getStudentAbilityModelItemDOList(), existStudentAbilityModelDO.getStudentAbilityModelItemDOList(), studentAbilityModelCompareDO);
            //6.处理对比结果
            dealStudentAbilityModelCompareDO(studentAbilityModel, staffId, studentAbilityModelDO, studentAbilityModelCompareDO);
        } finally {
            redisUtil.releaseLock(lockKey, requestId);
        }
        return true;
    }

    /**
     * 处理对比结果
     *
     * @param studentAbilityModelCompareDO
     */
    private void dealStudentAbilityModelCompareDO(StudentAbilityModel studentAbilityModel,
                                                  String staffId,
                                                  StudentAbilityModelDO studentAbilityModelDO,
                                                  StudentAbilityModelCompareDO studentAbilityModelCompareDO) {

        //更新学生能力模型主表记录
        studentAbilityModel.setAbilityModelName(studentAbilityModelDO.getStudentAbilityModel().getAbilityModelName());
        studentAbilityModel.setContrastRange(studentAbilityModelDO.getStudentAbilityModel().getContrastRange());
        studentAbilityModel.setExamSource(studentAbilityModelDO.getStudentAbilityModel().getExamSource());
        studentAbilityModel.setSubmitTime(new Date());
        studentAbilityModel.setUpdateTime(new Date());
        studentAbilityModel.setUpdateBy(staffId);
        studentAbilityModelManager.updateById(studentAbilityModel);
        //处理能力项变更并发送MQ
        handleModelItemChangeAndSendMQ(studentAbilityModel, studentAbilityModelCompareDO);
        try {
            //处理能力项变更
            dealItemCompare(studentAbilityModelCompareDO.getChangeItemCompareDOList());
            //处理能力项配置变更
            dealItemConfigCompare(studentAbilityModelCompareDO.getChangeItemConfigCompareDOList());
            //处理学科配置关系变更
            dealSubjectBusinessMergeCompare(studentAbilityModelCompareDO.getChangeSubjectItemCompareDOList());
            //处理指标配置关系变更
            dealTargetBusinessMergeCompare(studentAbilityModelCompareDO.getChangeTargetItemCompareDOList());
        } catch (DuplicateKeyException ex) {
            throw new BizException("当前页面数据发送变化，请刷新后重试");
        }
    }

    /**
     * 处理能力模型变更并发送MQ通知
     *
     * @param studentAbilityModel
     * @param studentAbilityModelCompareDO
     */
    private void handleModelItemChangeAndSendMQ(StudentAbilityModel studentAbilityModel, StudentAbilityModelCompareDO studentAbilityModelCompareDO) {
        if (CollectionUtils.isNotEmpty(studentAbilityModelCompareDO.getChangeItemCompareDOList())
                || CollectionUtils.isNotEmpty(studentAbilityModelCompareDO.getChangeItemConfigCompareDOList())
                || CollectionUtils.isNotEmpty(studentAbilityModelCompareDO.getChangeTargetItemCompareDOList())
                || CollectionUtils.isNotEmpty(studentAbilityModelCompareDO.getChangeSubjectItemCompareDOList())) {
            //只要有一项发生变化，则发送MQ通知能力模型变更
            //todo 发送MQ通知
        }
    }

    /**
     * 处理指标配置关系变更
     *
     * @param targetBusinessItemConfigCompareDOList
     */
    private void dealTargetBusinessMergeCompare(List<StudentAbilityModelTargetBusinessItemConfigCompareDO> targetBusinessItemConfigCompareDOList) {
        if (CollectionUtils.isEmpty(targetBusinessItemConfigCompareDOList)) {
            return;
        }
        List<TargetBusinessMergePO> addList = new ArrayList<>();
        List<TargetBusinessMergePO> updateList = new ArrayList<>();
        List<TargetBusinessMergePO> deleteList = new ArrayList<>();
        for (StudentAbilityModelTargetBusinessItemConfigCompareDO targetBusinessItemConfigCompareDO : targetBusinessItemConfigCompareDOList) {
            if (targetBusinessItemConfigCompareDO.getAddConfig()) {
                addList.addAll(targetBusinessItemConfigCompareDO.getTargetBusinessMergePOList());
            } else if (targetBusinessItemConfigCompareDO.getUpdateConfig()) {
                updateList.addAll(targetBusinessItemConfigCompareDO.getTargetBusinessMergePOList());
            } else if (targetBusinessItemConfigCompareDO.getDeleteConfig()) {
                deleteList.addAll(targetBusinessItemConfigCompareDO.getTargetBusinessMergePOList());
            }
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            targetBusinessMergeManager.insertBatch(addList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            targetBusinessMergeManager.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            List<Long> deleteIdList = deleteList.stream().map(TargetBusinessMergePO::getId).collect(Collectors.toList());
            targetBusinessMergeManager.removeBatchByIds(deleteIdList);
        }
    }

    /**
     * 处理学科配置关系变更
     *
     * @param targetBusinessItemConfigCompareDOList
     */
    private void dealSubjectBusinessMergeCompare(List<StudentAbilityModelSubjectBusinessItemConfigCompareDO> targetBusinessItemConfigCompareDOList) {
        if (CollectionUtils.isEmpty(targetBusinessItemConfigCompareDOList)) {
            return;
        }
        List<SubjectBusinessMergePO> addList = new ArrayList<>();
        List<SubjectBusinessMergePO> updateList = new ArrayList<>();
        List<SubjectBusinessMergePO> deleteList = new ArrayList<>();
        for (StudentAbilityModelSubjectBusinessItemConfigCompareDO subjectBusinessItemConfigCompareDO : targetBusinessItemConfigCompareDOList) {
            if (subjectBusinessItemConfigCompareDO.getAddConfig()) {
                addList.addAll(subjectBusinessItemConfigCompareDO.getSubjectBusinessMergePOList());
            } else if (subjectBusinessItemConfigCompareDO.getUpdateConfig()) {
                updateList.addAll(subjectBusinessItemConfigCompareDO.getSubjectBusinessMergePOList());
            } else if (subjectBusinessItemConfigCompareDO.getDeleteConfig()) {
                deleteList.addAll(subjectBusinessItemConfigCompareDO.getSubjectBusinessMergePOList());
            }
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            subjectBusinessMergeManager.insertBatch(addList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            subjectBusinessMergeManager.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            List<Long> deleteIdList = deleteList.stream().map(SubjectBusinessMergePO::getId).collect(Collectors.toList());
            subjectBusinessMergeManager.removeBatchByIds(deleteIdList);
        }
    }

    /**
     * 处理能力项配置变更
     *
     * @param changeItemConfigCompareDOList
     */
    private void dealItemConfigCompare(List<StudentAbilityModelItemConfigCompareDO> changeItemConfigCompareDOList) {
        if (CollectionUtils.isEmpty(changeItemConfigCompareDOList)) {
            return;
        }
        List<EvaluateStudentAbilityModelItemConfigPO> addList = new ArrayList<>();
        List<EvaluateStudentAbilityModelItemConfigPO> updateList = new ArrayList<>();
        List<EvaluateStudentAbilityModelItemConfigPO> deleteList = new ArrayList<>();
        //区分新增，修改，删除
        for (StudentAbilityModelItemConfigCompareDO itemConfigCompareDO : changeItemConfigCompareDOList) {
            if (itemConfigCompareDO.getAddConfig()) {
                addList.add(itemConfigCompareDO.getEvaluateStudentAbilityModelItemConfigPO());
            } else if (itemConfigCompareDO.getUpdateConfig()) {
                updateList.add(itemConfigCompareDO.getEvaluateStudentAbilityModelItemConfigPO());
            } else if (itemConfigCompareDO.getDeleteConfig()) {
                deleteList.add(itemConfigCompareDO.getEvaluateStudentAbilityModelItemConfigPO());
            }
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            studentAbilityModelItemConfigManager.insertBatch(addList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            studentAbilityModelItemConfigManager.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            List<Long> deleteIdList = deleteList.stream().map(EvaluateStudentAbilityModelItemConfigPO::getId).collect(Collectors.toList());
            studentAbilityModelItemConfigManager.removeBatchByIds(deleteIdList);
        }
    }

    /**
     * 处理能力项变更结果
     *
     * @param changeItemCompareDOList
     */
    private void dealItemCompare(List<StudentAbilityModelItemCompareDO> changeItemCompareDOList) {
        if (CollectionUtils.isEmpty(changeItemCompareDOList)) {
            return;
        }
        List<EvaluateStudentAbilityModelItemPO> addList = new ArrayList<>();
        List<EvaluateStudentAbilityModelItemPO> updateList = new ArrayList<>();
        List<EvaluateStudentAbilityModelItemPO> deleteList = new ArrayList<>();
        //区分新增，修改，删除
        for (StudentAbilityModelItemCompareDO studentAbilityModelItemCompareDO : changeItemCompareDOList) {
            if (studentAbilityModelItemCompareDO.getAddItem()) {
                addList.add(studentAbilityModelItemCompareDO.getEvaluateStudentAbilityModelItemPO());
            } else if (studentAbilityModelItemCompareDO.getUpdateItem()) {
                updateList.add(studentAbilityModelItemCompareDO.getEvaluateStudentAbilityModelItemPO());
            } else if (studentAbilityModelItemCompareDO.getDeleteItem()) {
                deleteList.add(studentAbilityModelItemCompareDO.getEvaluateStudentAbilityModelItemPO());
            }
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            studentAbilityModelItemManager.insertBatch(addList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            studentAbilityModelItemManager.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            List<Long> deleteIdList = deleteList.stream().map(EvaluateStudentAbilityModelItemPO::getId).collect(Collectors.toList());
            studentAbilityModelItemManager.removeBatchByIds(deleteIdList);
        }
    }

    /**
     * 对比新旧学生能力模型
     *
     * @param newItemList                  新学生能力模型能力项
     * @param existItemList                现存学生能力模型能力项
     * @param studentAbilityModelCompareDO 对比结果
     * @return 对比结果
     */
    public void compareStudentAbilityModelItem(List<StudentAbilityModelItemDO> newItemList,
                                               List<StudentAbilityModelItemDO> existItemList,
                                               StudentAbilityModelCompareDO studentAbilityModelCompareDO) {
        if (CollectionUtils.isEmpty(newItemList) && CollectionUtils.isEmpty(existItemList)) {
            //新老能力项均没有，直接返回
            return;
        }
        if (CollectionUtils.isEmpty(newItemList)) {
            //新的没有子能力项，删除同等老得所有能力项
            for (StudentAbilityModelItemDO existItemDO : existItemList) {
                addChangeItemCompareDO(existItemDO, Boolean.FALSE, Boolean.FALSE, Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, studentAbilityModelCompareDO);
            }
            return;
        }
        if (Objects.isNull(existItemList)) {
            //不存在老能力项，新增所有新能力项
            for (StudentAbilityModelItemDO newItemDO : newItemList) {
                addChangeItemCompareDO(newItemDO, Boolean.TRUE, Boolean.FALSE, Boolean.FALSE, Boolean.TRUE, Boolean.TRUE, studentAbilityModelCompareDO);
            }
            return;
        }
        //新增的学生能力模型map
        Map<Long/*第一级itemId*/, StudentAbilityModelItemDO> updateItemMap = new HashMap<>();
        //现存的学生能力模型map
        Map<Long/*第一级itemId*/, StudentAbilityModelItemDO> existItemMap = new HashMap<>();
        for (StudentAbilityModelItemDO studentAbilityModelItemDO : newItemList) {
            Long itemId = studentAbilityModelItemDO.getEvaluateStudentAbilityModelItemPO().getId();
            updateItemMap.put(itemId, studentAbilityModelItemDO);
        }
        for (StudentAbilityModelItemDO studentAbilityModelItemDO : existItemList) {
            existItemMap.put(studentAbilityModelItemDO.getEvaluateStudentAbilityModelItemPO().getId(), studentAbilityModelItemDO);
        }
        //对比能力项
        doCompareStudentAbilityModelItem(updateItemMap, existItemMap, studentAbilityModelCompareDO);
    }

    /**
     * 对比新旧学生能力模型配置
     *
     * @param updateItemMap 更新的能力项
     * @param existItemMap  现存能力项
     * @return
     */
    private void doCompareStudentAbilityModelItem(Map<Long, StudentAbilityModelItemDO> updateItemMap,
                                                  Map<Long, StudentAbilityModelItemDO> existItemMap,
                                                  StudentAbilityModelCompareDO studentAbilityModelCompareDO) {
        //已经处理的现存能力项id
        Set<Long> dealExistItemIdSet = new HashSet<>();
        for (Map.Entry<Long, StudentAbilityModelItemDO> modelItemDOEntry : updateItemMap.entrySet()) {
            Long itemId = modelItemDOEntry.getKey();
            StudentAbilityModelItemDO newItemDO = modelItemDOEntry.getValue();
            StudentAbilityModelItemDO existItemDO = existItemMap.get(itemId);
            if (Objects.isNull(existItemDO)) {
                //老配置项中不存在，新增新配置项
                addChangeItemCompareDO(newItemDO, Boolean.TRUE, Boolean.FALSE, Boolean.FALSE, Boolean.TRUE, Boolean.TRUE, studentAbilityModelCompareDO);
            } else {
                dealExistItemIdSet.add(existItemDO.getEvaluateStudentAbilityModelItemPO().getId());
                //存在，有更新，删除，修改的情况
                //对比新老能力项配置是否发生变动
                compareAndAddItemChange(newItemDO, existItemDO, studentAbilityModelCompareDO);
                //处理子节点
                compareStudentAbilityModelItem(newItemDO.getSubItemList(), existItemDO.getSubItemList(), studentAbilityModelCompareDO);
            }
        }
        for (Map.Entry<Long, StudentAbilityModelItemDO> existItemEntry : existItemMap.entrySet()) {
            Long itemId = existItemEntry.getKey();
            StudentAbilityModelItemDO itemDO = existItemEntry.getValue();
            if (!dealExistItemIdSet.contains(itemId)) {
                //未处理到的能力项，加入删除列表
                addChangeItemCompareDO(itemDO, Boolean.FALSE, Boolean.FALSE, Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, studentAbilityModelCompareDO);
            }
        }
    }

    /**
     * 对比并添加能力项
     *
     * @param newItemDO   新能力项
     * @param existItemDO 老能力项
     * @return changeList 发生变动的列表
     */
    private void compareAndAddItemChange(StudentAbilityModelItemDO newItemDO,
                                         StudentAbilityModelItemDO existItemDO,
                                         StudentAbilityModelCompareDO studentAbilityModelCompareDO) {
        EvaluateStudentAbilityModelItemPO newItemPO = newItemDO.getEvaluateStudentAbilityModelItemPO();
        EvaluateStudentAbilityModelItemPO existItemPO = existItemDO.getEvaluateStudentAbilityModelItemPO();
        newItemPO.setId(existItemPO.getId());
        Boolean itemChangeFlag = Boolean.FALSE;
        //对比能力项
        if (!StringUtils.equals(newItemPO.getAbilityItemName(), existItemPO.getAbilityItemName())) {
            itemChangeFlag = Boolean.TRUE;
        } else if (!Objects.equals(newItemPO.getSortIndex(), existItemPO.getSortIndex())) {
            itemChangeFlag = Boolean.TRUE;
        } else if (!Objects.equals(newItemPO.getLevel(), existItemPO.getLevel())) {
            itemChangeFlag = Boolean.TRUE;
        } else if (!Objects.equals(newItemPO.getParentId(), existItemPO.getParentId())) {
            itemChangeFlag = Boolean.TRUE;
        }
        if (itemChangeFlag) {
            //能力项发生变化
            newItemPO.setCreateBy(existItemPO.getCreateBy());
            newItemPO.setCreateTime(existItemPO.getCreateTime());
            addChangeItemCompareDO(newItemDO, Boolean.FALSE, Boolean.TRUE, Boolean.FALSE, Boolean.FALSE, Boolean.FALSE, studentAbilityModelCompareDO);
        }
        //新的，老的，必须有一个没有子能力项才能新增
        if (!newItemDO.getHasSubItemList() || !existItemDO.getHasSubItemList()) {
            //对比德育活动配置
            compareMoralItemConfig(newItemDO, existItemDO, studentAbilityModelCompareDO);
            //对比指标配置信息
            compareItemConfig(newItemDO.getTargetBusinessItemConfigDO(), existItemDO.getTargetBusinessItemConfigDO(), studentAbilityModelCompareDO, new BiFunction<StudentAbilityModelItemConfigDO, EvaluateStudentAbilityModelItemConfigPO, List<?>>() {
                @Override
                public List<?> apply(StudentAbilityModelItemConfigDO itemConfigDO, EvaluateStudentAbilityModelItemConfigPO itemConfigPO) {
                    List<TargetBusinessMergePO> targetBusinessMergePOList = ((StudentAbilityModelTargetBusinessItemConfigDO) itemConfigDO).getTargetBusinessMergePOList();
                    targetBusinessMergePOList.forEach(business -> business.setBusinessId(itemConfigPO.getId()));
                    return targetBusinessMergePOList;
                }
            });
            //对比科目配置信息
            compareItemConfig(newItemDO.getSubjectBusinessItemConfigDO(), existItemDO.getSubjectBusinessItemConfigDO(), studentAbilityModelCompareDO, new BiFunction<StudentAbilityModelItemConfigDO, EvaluateStudentAbilityModelItemConfigPO, List<?>>() {
                @Override
                public List<?> apply(StudentAbilityModelItemConfigDO itemConfigDO, EvaluateStudentAbilityModelItemConfigPO itemConfigPO) {
                    List<SubjectBusinessMergePO> subjectBusinessMergePOList = ((StudentAbilityModelSubjectBusinessItemConfigDO) itemConfigDO).getSubjectBusinessMergePOList();
                    subjectBusinessMergePOList.forEach(business -> business.setBusinessId(itemConfigPO.getId()));
                    return subjectBusinessMergePOList;
                }
            });
        }
    }


    /**
     * 比较课程能力项配置
     *
     * @param existSubjectItemConfigDO
     * @param newSubjectItemConfigDO
     * @param addList
     * @param deleteList
     */
    private void compareSubjectItemConfig(StudentAbilityModelSubjectBusinessItemConfigDO newSubjectItemConfigDO, StudentAbilityModelSubjectBusinessItemConfigDO existSubjectItemConfigDO, List<Object> addList, List<Object> deleteList) {
        EvaluateStudentAbilityModelItemConfigPO newItemConfig = newSubjectItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO();
        //对比科目配置信息
        List<SubjectBusinessMergePO> existSubjectBusinessMergePOList = existSubjectItemConfigDO.getSubjectBusinessMergePOList();
        existSubjectBusinessMergePOList = Objects.isNull(existSubjectBusinessMergePOList) ? new ArrayList<>() : existSubjectBusinessMergePOList;
        Map<String/*subjectId+subjectCode*/, SubjectBusinessMergePO> existSubjectBusinessMap = new HashMap<>();
        for (SubjectBusinessMergePO subjectBusinessMergePO : existSubjectBusinessMergePOList) {
            String key = buildSubjectBusinessMapKey(subjectBusinessMergePO.getSubjectId(), subjectBusinessMergePO.getSubjectCode(), subjectBusinessMergePO.getSubjectName());
            existSubjectBusinessMap.put(key, subjectBusinessMergePO);
        }
        //已存在并被处理的集合
        Set<Long> dealExistIdSet = new HashSet<>();
        List<SubjectBusinessMergePO> newSubjectBusinessMergePOList = newSubjectItemConfigDO.getSubjectBusinessMergePOList();
        newSubjectBusinessMergePOList = Objects.isNull(newSubjectBusinessMergePOList) ? new ArrayList<>() : newSubjectBusinessMergePOList;
        //不存在的科目业务关联关系，就是要新增的
        for (SubjectBusinessMergePO subjectBusinessMergePO : newSubjectBusinessMergePOList) {
            subjectBusinessMergePO.setBusinessId(newItemConfig.getId());
            String key = buildSubjectBusinessMapKey(subjectBusinessMergePO.getSubjectId(), subjectBusinessMergePO.getSubjectCode(), subjectBusinessMergePO.getSubjectName());
            SubjectBusinessMergePO existSubjectBusiness = existSubjectBusinessMap.get(key);
            if (Objects.isNull(existSubjectBusiness)) {
                //老的不存在，新增新的
                addList.add(subjectBusinessMergePO);
            } else {
                if (existSubjectBusiness.getWeight().compareTo(subjectBusinessMergePO.getWeight()) != 0) {
                    //权重不相等，新增并且删除
                    addList.add(subjectBusinessMergePO);
                } else {
                    //权重相等，不做处理
                    dealExistIdSet.add(existSubjectBusiness.getId());
                }
            }
        }
        //处理要删除的数据
        for (SubjectBusinessMergePO subjectBusinessMergePO : existSubjectBusinessMergePOList) {
            if (!dealExistIdSet.contains(subjectBusinessMergePO.getId())) {
                //要删除的数据
                deleteList.add(subjectBusinessMergePO);
            }
        }
    }

    /**
     * 对比指标配置信息
     *
     * @param newItemConfigDO
     * @param existItemConfigDO
     * @param studentAbilityModelCompareDO
     */
    private void compareItemConfig(StudentAbilityModelItemConfigDO newItemConfigDO, StudentAbilityModelItemConfigDO existItemConfigDO,
                                   StudentAbilityModelCompareDO studentAbilityModelCompareDO, BiFunction<StudentAbilityModelItemConfigDO, EvaluateStudentAbilityModelItemConfigPO, List<?>> getTargetListFunction) {
        //新增列表
        List<Object> addList = new ArrayList<>();
        List<Object> deleteList = new ArrayList<>();
        //老的存在，新的没有，删除老的
        //新的存在，老的没有，新增
        //新的也存在，老的也存在，对比
        if (Objects.nonNull(newItemConfigDO) && Objects.nonNull(existItemConfigDO)) {
            EvaluateStudentAbilityModelItemConfigPO newItemConfig = newItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO();
            EvaluateStudentAbilityModelItemConfigPO existItemConfig = existItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO();
            newItemConfig.setId(existItemConfig.getId());
            //对比并新增能力项配置
            compareAndAddItemConfig(newItemConfig, existItemConfig, studentAbilityModelCompareDO);
            if (newItemConfigDO instanceof StudentAbilityModelTargetBusinessItemConfigDO) {
                //对比指标配置信息
                compareTargetItemConfig((StudentAbilityModelTargetBusinessItemConfigDO) newItemConfigDO, (StudentAbilityModelTargetBusinessItemConfigDO) existItemConfigDO, addList, deleteList);
            } else if (newItemConfigDO instanceof StudentAbilityModelSubjectBusinessItemConfigDO) {
                //对比课程配置信息
                compareSubjectItemConfig((StudentAbilityModelSubjectBusinessItemConfigDO) newItemConfigDO, (StudentAbilityModelSubjectBusinessItemConfigDO) existItemConfigDO, addList, deleteList);
            }
        } else if (Objects.isNull(newItemConfigDO)) {
            //新的不存在，删除老的
            if (existItemConfigDO instanceof StudentAbilityModelTargetBusinessItemConfigDO) {
                List<TargetBusinessMergePO> targetBusinessMergePOList = ((StudentAbilityModelTargetBusinessItemConfigDO) existItemConfigDO).getTargetBusinessMergePOList();
                if (CollectionUtils.isNotEmpty(targetBusinessMergePOList)) {
                    deleteList.addAll(targetBusinessMergePOList);
                }
            } else if (existItemConfigDO instanceof StudentAbilityModelSubjectBusinessItemConfigDO) {
                List<SubjectBusinessMergePO> subjectBusinessMergePOList = ((StudentAbilityModelSubjectBusinessItemConfigDO) existItemConfigDO).getSubjectBusinessMergePOList();
                if(CollectionUtils.isNotEmpty(subjectBusinessMergePOList)){
                    deleteList.addAll(subjectBusinessMergePOList);
                }
            }
            addChangeItemConfig(Boolean.FALSE, Boolean.FALSE, Boolean.TRUE, studentAbilityModelCompareDO, existItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO());
        } else {
            //老的不存在,新增新的
            addChangeItemConfig(Boolean.TRUE, Boolean.FALSE, Boolean.FALSE, studentAbilityModelCompareDO, newItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO());
            List<?> newBusinessList = getTargetListFunction.apply(newItemConfigDO, newItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO());
            //经过上述操作之后，id可能会发生变化
            addList.addAll(newBusinessList);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            //处理新增的数据
            if (newItemConfigDO instanceof StudentAbilityModelTargetBusinessItemConfigDO) {
                //对比指标配置信息
                addChangeTargetItemConfig(Boolean.TRUE, Boolean.FALSE, Boolean.FALSE, studentAbilityModelCompareDO, addList);
            } else if (newItemConfigDO instanceof StudentAbilityModelSubjectBusinessItemConfigDO) {
                addChangeSubjectItemConfig(Boolean.TRUE, Boolean.FALSE, Boolean.FALSE, studentAbilityModelCompareDO, addList);
            }
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            //处理删除的数据
            if (newItemConfigDO instanceof StudentAbilityModelTargetBusinessItemConfigDO) {
                //对比指标配置信息
                addChangeTargetItemConfig(Boolean.FALSE, Boolean.FALSE, Boolean.TRUE, studentAbilityModelCompareDO, deleteList);
            } else if (newItemConfigDO instanceof StudentAbilityModelSubjectBusinessItemConfigDO) {
                addChangeSubjectItemConfig(Boolean.FALSE, Boolean.FALSE, Boolean.TRUE, studentAbilityModelCompareDO, deleteList);
            }
        }
    }

    /**
     * 比较指标是否相同
     *
     * @param newItemConfigDO
     * @param existItemConfigDO
     * @param addList
     * @param deleteList
     */
    private void compareTargetItemConfig(StudentAbilityModelTargetBusinessItemConfigDO newItemConfigDO, StudentAbilityModelTargetBusinessItemConfigDO existItemConfigDO, List addList, List deleteList) {
        EvaluateStudentAbilityModelItemConfigPO newItemConfig = newItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO();
        List<TargetBusinessMergePO> existTargetBusinessMergePOList = existItemConfigDO.getTargetBusinessMergePOList();
        Map<String/*moduleCode+submitId+SubmitType*/, TargetBusinessMergePO> existTargetBusinessMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(existTargetBusinessMergePOList)) {
            for (TargetBusinessMergePO targetBusinessMergePO : existTargetBusinessMergePOList) {
                String key = buildTargetBusinessMapKey(targetBusinessMergePO.getModuleCode(), targetBusinessMergePO.getSubmitId(), targetBusinessMergePO.getSubmitType());
                existTargetBusinessMap.put(key, targetBusinessMergePO);
            }
        }
        //已存在并被处理的集合
        Set<Long> dealExistIdSet = new HashSet<>();
        List<TargetBusinessMergePO> newTargetBusinessMergePOList = newItemConfigDO.getTargetBusinessMergePOList();
        newTargetBusinessMergePOList = Objects.isNull(newTargetBusinessMergePOList) ? new ArrayList<>() : newTargetBusinessMergePOList;
        //不存在的指标业务关联关系，就是要新增的
        for (TargetBusinessMergePO targetBusinessMergePO : newTargetBusinessMergePOList) {
            targetBusinessMergePO.setBusinessId(newItemConfig.getId());
            String key = buildTargetBusinessMapKey(targetBusinessMergePO.getModuleCode(), targetBusinessMergePO.getSubmitId(), targetBusinessMergePO.getSubmitType());
//            newTargetBusinessMap.put(key, targetBusinessMergePO);
            TargetBusinessMergePO existTargetBusiness = existTargetBusinessMap.get(key);
            if (Objects.isNull(existTargetBusiness)) {
                //不存在，新增
                addList.add(targetBusinessMergePO);
            } else {
                dealExistIdSet.add(existTargetBusiness.getId());
            }
        }
        existTargetBusinessMergePOList = Objects.isNull(existTargetBusinessMergePOList) ? new ArrayList<>() : existTargetBusinessMergePOList;
        //处理要删除的数据
        for (TargetBusinessMergePO targetBusinessMergePO : existTargetBusinessMergePOList) {
            if (!dealExistIdSet.contains(targetBusinessMergePO.getId())) {
                //要删除的数据
                deleteList.add(targetBusinessMergePO);
            }
        }
    }

    /**
     * 构建指标业务关联关系map的key
     *
     * @param moduleCode
     * @param submitId
     * @param submitType
     * @return
     */
    private String buildTargetBusinessMapKey(Integer moduleCode, String submitId, Integer submitType) {
        return String.join(Constants.DASH, moduleCode + "", submitId, submitType + "");
    }

    /**
     * 构建科目业务关联关系map的key
     *
     * @param subjectId
     * @param subjectCode
     * @param subjectName
     * @return
     */
    private String buildSubjectBusinessMapKey(String subjectId, String subjectCode, String subjectName) {
        return String.join(Constants.DASH, subjectId, subjectCode, subjectName);
    }

    /**
     * 对比德育活动配置信息
     *
     * @param newItemDO
     * @param existItemDO
     * @param studentAbilityModelCompareDO
     */
    private void compareMoralItemConfig(StudentAbilityModelItemDO newItemDO, StudentAbilityModelItemDO existItemDO,
                                        StudentAbilityModelCompareDO studentAbilityModelCompareDO) {
        StudentAbilityModelMoralBusinessItemConfigDO newMoralBusinessItemConfigDO = newItemDO.getMoralBusinessItemConfigDO();
        StudentAbilityModelMoralBusinessItemConfigDO existMoralBusinessItemConfigDO = existItemDO.getMoralBusinessItemConfigDO();
        //老的存在，新的没有，删除老的
        //新的存在，老的没有，新增
        //新的也存在，老的也存在，对比
        if (Objects.nonNull(newMoralBusinessItemConfigDO) && Objects.nonNull(existMoralBusinessItemConfigDO)) {
            //新老都存在，对比
            EvaluateStudentAbilityModelItemConfigPO newItemConfig = newMoralBusinessItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO();
            EvaluateStudentAbilityModelItemConfigPO existItemConfig = existMoralBusinessItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO();
            newItemConfig.setId(existItemConfig.getId());
            if (Objects.isNull(newItemConfig.getId())) {
                //转换德育活动配置信息
                addChangeItemConfig(Boolean.TRUE, Boolean.FALSE, Boolean.FALSE, studentAbilityModelCompareDO, newMoralBusinessItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO());
                return;
            }
            //对比并添加能力项配置
            compareAndAddItemConfig(newItemConfig, existItemConfig, studentAbilityModelCompareDO);
        } else if (Objects.isNull(newMoralBusinessItemConfigDO)) {
            //新的不存在，删除老的
            addChangeItemConfig(Boolean.FALSE, Boolean.FALSE, Boolean.TRUE, studentAbilityModelCompareDO, existMoralBusinessItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO());
        } else {
            //老的不存在，新增
            addChangeItemConfig(Boolean.TRUE, Boolean.FALSE, Boolean.FALSE, studentAbilityModelCompareDO, newMoralBusinessItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO());
        }

    }

    /**
     * 对比并新增能力项配置
     *
     * @param newItemConfig
     * @param existItemConfig
     * @param studentAbilityModelCompareDO
     */
    private void compareAndAddItemConfig(EvaluateStudentAbilityModelItemConfigPO newItemConfig,
                                         EvaluateStudentAbilityModelItemConfigPO existItemConfig,
                                         StudentAbilityModelCompareDO studentAbilityModelCompareDO) {
        Boolean itemConfigChange = Boolean.FALSE;
        if (newItemConfig.getWeight().compareTo(existItemConfig.getWeight()) != 0) {
            itemConfigChange = Boolean.TRUE;
        } else if (!Objects.equals(newItemConfig.getEnabled(), existItemConfig.getEnabled())) {
            itemConfigChange = Boolean.TRUE;
        }
        if (itemConfigChange) {
            newItemConfig.setCreateTime(existItemConfig.getCreateTime());
            newItemConfig.setCreateBy(existItemConfig.getCreateBy());
            addChangeItemConfig(Boolean.FALSE, Boolean.TRUE, Boolean.FALSE, studentAbilityModelCompareDO, newItemConfig);
        }
    }


    /**
     * 从领域对象转换为对比对象
     *
     * @param originDO          原始领域对象
     * @param addItem           是否新增
     * @param updateItem        是否更新
     * @param deleteItem        是否删除
     * @param convertItemConfig 是否转换能力项配置
     * @param convertSubList    是否转换子能力项
     * @return
     */
    private void addChangeItemCompareDO(StudentAbilityModelItemDO originDO,
                                        Boolean addItem, Boolean updateItem,
                                        Boolean deleteItem, Boolean convertItemConfig,
                                        Boolean convertSubList,
                                        StudentAbilityModelCompareDO studentAbilityModelCompareDO) {
        StudentAbilityModelItemCompareDO studentAbilityModelItemCompareDO = new StudentAbilityModelItemCompareDO();
        studentAbilityModelItemCompareDO.setEvaluateStudentAbilityModelItemPO(originDO.getEvaluateStudentAbilityModelItemPO());
        //转换子能力项
        if (convertSubList) {
            List<StudentAbilityModelItemDO> subItemList = originDO.getSubItemList();
            if (CollectionUtils.isNotEmpty(subItemList)) {
                for (StudentAbilityModelItemDO studentAbilityModelItemDO : subItemList) {
                    addChangeItemCompareDO(studentAbilityModelItemDO, addItem, updateItem, deleteItem, convertItemConfig, convertSubList, studentAbilityModelCompareDO);
                }
            }

        }
        //是否转换能力项配置
        if (convertItemConfig) {
            //转换能力项配置
            addNewItemConfig(originDO, addItem, updateItem, deleteItem, studentAbilityModelCompareDO);
        }
        studentAbilityModelItemCompareDO.setAddItem(addItem);
        studentAbilityModelItemCompareDO.setDeleteItem(deleteItem);
        studentAbilityModelItemCompareDO.setUpdateItem(updateItem);
        studentAbilityModelCompareDO.getChangeItemCompareDOList().add(studentAbilityModelItemCompareDO);
    }

    /**
     * 转换能力项配置
     *
     * @param originDO                     原始对象
     * @param addItem                      是否新增
     * @param updateItem                   是否更新
     * @param deleteItem                   是否删除
     * @param studentAbilityModelCompareDO 能力模型对比返回值
     */
    private void addNewItemConfig(StudentAbilityModelItemDO originDO, Boolean addItem, Boolean updateItem, Boolean deleteItem,
                                  StudentAbilityModelCompareDO studentAbilityModelCompareDO) {
        StudentAbilityModelMoralBusinessItemConfigDO moralBusinessItemConfigDO = originDO.getMoralBusinessItemConfigDO();
        StudentAbilityModelSubjectBusinessItemConfigDO subjectBusinessItemConfigDO = originDO.getSubjectBusinessItemConfigDO();
        StudentAbilityModelTargetBusinessItemConfigDO targetBusinessItemConfigDO = originDO.getTargetBusinessItemConfigDO();
        //转换德育活动配置信息
        if (Objects.nonNull(moralBusinessItemConfigDO)) {
            addChangeItemConfig(addItem, updateItem, deleteItem, studentAbilityModelCompareDO, moralBusinessItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO());
        }
        //转换科目关联关系和配置信息
        if (Objects.nonNull(subjectBusinessItemConfigDO)) {
            EvaluateStudentAbilityModelItemConfigPO itemConfigPO = subjectBusinessItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO();
            addChangeItemConfig(addItem, updateItem, deleteItem, studentAbilityModelCompareDO, itemConfigPO);
            List<SubjectBusinessMergePO> subjectBusinessMergePOList = subjectBusinessItemConfigDO.getSubjectBusinessMergePOList();
            if (CollectionUtils.isNotEmpty(subjectBusinessMergePOList)) {
                subjectBusinessMergePOList.forEach(sbm -> sbm.setBusinessId(itemConfigPO.getId()));
                addChangeSubjectItemConfig(addItem, updateItem, deleteItem, studentAbilityModelCompareDO, subjectBusinessMergePOList);
            }
        }
        //转换指标关联关系和配置信息
        if (Objects.nonNull(targetBusinessItemConfigDO)) {
            EvaluateStudentAbilityModelItemConfigPO itemConfigPO = targetBusinessItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO();
            addChangeItemConfig(addItem, updateItem, deleteItem, studentAbilityModelCompareDO, targetBusinessItemConfigDO.getEvaluateStudentAbilityModelItemConfigPO());
            List<TargetBusinessMergePO> targetBusinessMergePOList = targetBusinessItemConfigDO.getTargetBusinessMergePOList();
            if (CollectionUtils.isNotEmpty(targetBusinessMergePOList)) {
                targetBusinessMergePOList.forEach(tbm -> tbm.setBusinessId(itemConfigPO.getId()));
                addChangeTargetItemConfig(addItem, updateItem, deleteItem, studentAbilityModelCompareDO, targetBusinessMergePOList);
            }
        }
    }

    /**
     * 转换能力项配置
     *
     * @param addConfig
     * @param updateConfig
     * @param deleteConfig
     * @param studentAbilityModelCompareDO 对比结果
     * @param itemConfigPO                 变化的能力项配置
     */
    private void addChangeItemConfig(Boolean addConfig, Boolean updateConfig, Boolean deleteConfig,
                                     StudentAbilityModelCompareDO studentAbilityModelCompareDO,
                                     EvaluateStudentAbilityModelItemConfigPO itemConfigPO) {
        if (Objects.isNull(itemConfigPO)) {
            return;
        }
        List<StudentAbilityModelItemConfigCompareDO> itemConfigCompareDOList = studentAbilityModelCompareDO.getChangeItemConfigCompareDOList();
        StudentAbilityModelItemConfigCompareDO itemConfigCompareDO = new StudentAbilityModelItemConfigCompareDO();
        if (addConfig) {
            //如果是新增，重制id
            itemConfigPO.setId(SnowFlakeIdUtil.nextId());
        }
        itemConfigCompareDO.setEvaluateStudentAbilityModelItemConfigPO(itemConfigPO);
        itemConfigCompareDO.setAddConfig(addConfig);
        itemConfigCompareDO.setUpdateConfig(updateConfig);
        itemConfigCompareDO.setDeleteConfig(deleteConfig);
        itemConfigCompareDOList.add(itemConfigCompareDO);
    }

    /**
     * 转换指标配置
     *
     * @param addConfig
     * @param updateConfig
     * @param deleteConfig
     * @param studentAbilityModelCompareDO 对比结果
     * @param targetBusinessMergePOList    变化列表
     */
    private void addChangeTargetItemConfig(Boolean addConfig, Boolean updateConfig, Boolean deleteConfig,
                                           StudentAbilityModelCompareDO studentAbilityModelCompareDO,
                                           List targetBusinessMergePOList) {
        if (CollectionUtils.isEmpty(targetBusinessMergePOList)) {
            return;
        }
        List<StudentAbilityModelTargetBusinessItemConfigCompareDO> targetItemCompareDOList = studentAbilityModelCompareDO.getChangeTargetItemCompareDOList();
        StudentAbilityModelTargetBusinessItemConfigCompareDO targetBusinessItemConfigCompareDO = new StudentAbilityModelTargetBusinessItemConfigCompareDO();
        targetBusinessItemConfigCompareDO.setTargetBusinessMergePOList(targetBusinessMergePOList);
        targetBusinessItemConfigCompareDO.setAddConfig(addConfig);
        targetBusinessItemConfigCompareDO.setUpdateConfig(updateConfig);
        targetBusinessItemConfigCompareDO.setDeleteConfig(deleteConfig);
        targetItemCompareDOList.add(targetBusinessItemConfigCompareDO);
    }

    /**
     * 转换科目配置
     *
     * @param addConfig
     * @param updateConfig
     * @param deleteConfig
     * @param studentAbilityModelCompareDO 对比结果
     * @param subjectBusinessMergePOList   变化列表
     */
    private void addChangeSubjectItemConfig(Boolean addConfig, Boolean updateConfig, Boolean deleteConfig,
                                            StudentAbilityModelCompareDO studentAbilityModelCompareDO,
                                            List subjectBusinessMergePOList) {
        if (CollectionUtils.isEmpty(subjectBusinessMergePOList)) {
            return;
        }
        List<StudentAbilityModelSubjectBusinessItemConfigCompareDO> subjectItemCompareDOList = studentAbilityModelCompareDO.getChangeSubjectItemCompareDOList();
        StudentAbilityModelSubjectBusinessItemConfigCompareDO subjectBusinessItemConfigCompareDO = new StudentAbilityModelSubjectBusinessItemConfigCompareDO();
        subjectBusinessItemConfigCompareDO.setSubjectBusinessMergePOList(subjectBusinessMergePOList);
        subjectBusinessItemConfigCompareDO.setAddConfig(addConfig);
        subjectBusinessItemConfigCompareDO.setUpdateConfig(updateConfig);
        subjectBusinessItemConfigCompareDO.setDeleteConfig(deleteConfig);
        subjectItemCompareDOList.add(subjectBusinessItemConfigCompareDO);
    }


    /**
     * 构建学生能力模型领域对象
     *
     * @param studentAbilityModelRequest 能力模型新增参数
     * @return
     */
    private StudentAbilityModelDO buildStudentAbilityModelDO(String tenantId, String schoolId, String campusId, String staffId,
                                                             StudentAbilityModel existStudentAbilityModel,
                                                             StudentAbilityModelV2Request studentAbilityModelRequest) {
        StudentAbilityModelDO studentAbilityModelDO = new StudentAbilityModelDO();
        StudentAbilityModel studentAbilityModel = new StudentAbilityModel();
        Date now = new Date();
        studentAbilityModel.setAbilityModelName(studentAbilityModelRequest.getAbilityModelName());
        studentAbilityModel.setContrastRange(studentAbilityModelRequest.getContrastRange());
        studentAbilityModel.setTenantId(tenantId);
        studentAbilityModel.setSchoolId(schoolId);
        studentAbilityModel.setCampusId(campusId);
        studentAbilityModel.setSubmitTime(now);
        studentAbilityModel.setId(Objects.nonNull(existStudentAbilityModel) ? existStudentAbilityModel.getId() : SnowFlakeIdUtil.nextId());
        studentAbilityModel.setCreateBy(staffId);
        studentAbilityModel.setCreateTime(now);
        studentAbilityModel.setUpdateTime(now);
        studentAbilityModel.setUpdateBy(staffId);
        studentAbilityModel.setEnabled(Constant.ZERO);
        studentAbilityModel.setDeleted(Boolean.FALSE);
        studentAbilityModel.setExamSource(JSONUtil.toJsonStr(studentAbilityModelRequest.getStudentAbilityOutcomeDTO()));
        studentAbilityModelDO.setStudentAbilityModel(studentAbilityModel);
        //构建学生能力模型能力项
        List<StudentAbilityModelItemDO> modelItemDOList = buildStudentAbilityModelItemDO(tenantId, schoolId, campusId, null, staffId, studentAbilityModel, studentAbilityModelRequest.getModelItemList());
        studentAbilityModelDO.setStudentAbilityModelItemDOList(modelItemDOList);
        return studentAbilityModelDO;
    }

    /**
     * 构建能力项配置
     *
     * @param modelItemList 能力项列表参数
     */
    private List<StudentAbilityModelItemDO> buildStudentAbilityModelItemDO(String tenantId, String schoolId, String campusId,
                                                                           Long parentId, String staffId, StudentAbilityModel studentAbilityModel, List<StudentAbilityModelItemV2Request> modelItemList) {
        List<StudentAbilityModelItemDO> itemList = new ArrayList<>();
        for (StudentAbilityModelItemV2Request studentAbilityModelItem : modelItemList) {
            StudentAbilityModelItemDO studentAbilityModelItemDO = new StudentAbilityModelItemDO();
            //构建能力项
            EvaluateStudentAbilityModelItemPO evaluateStudentAbilityModelItemPO = new EvaluateStudentAbilityModelItemPO();
            Boolean newItem = Boolean.FALSE;
            if (Objects.isNull(studentAbilityModelItem.getItemId())) {
                evaluateStudentAbilityModelItemPO.setId(SnowFlakeIdUtil.nextId());
                newItem = Boolean.TRUE;
            } else {
                evaluateStudentAbilityModelItemPO.setId(studentAbilityModelItem.getItemId());
            }
            evaluateStudentAbilityModelItemPO.setCreateBy(staffId);
//            evaluateStudentAbilityModelItemPO.setCreateTime(new Date());
//            evaluateStudentAbilityModelItemPO.setUpdateTime(new Date());
//            evaluateStudentAbilityModelItemPO.setDeleted(Boolean.FALSE);
            evaluateStudentAbilityModelItemPO.setUpdateBy(staffId);
            evaluateStudentAbilityModelItemPO.setTenantId(tenantId);
            evaluateStudentAbilityModelItemPO.setSchoolId(schoolId);
            evaluateStudentAbilityModelItemPO.setCampusId(campusId);
            evaluateStudentAbilityModelItemPO.setParentId(Objects.isNull(parentId) ? Constant.LONG_NEGATIVE_ONE : parentId);
            evaluateStudentAbilityModelItemPO.setAbilityModelId(studentAbilityModel.getId());
            evaluateStudentAbilityModelItemPO.setAbilityItemName(studentAbilityModelItem.getAbilityItemName());
            evaluateStudentAbilityModelItemPO.setSortIndex(studentAbilityModelItem.getSortIndex());
            evaluateStudentAbilityModelItemPO.setLevel(studentAbilityModelItem.getLevel());
            studentAbilityModelItemDO.setEvaluateStudentAbilityModelItemPO(evaluateStudentAbilityModelItemPO);
            List<StudentAbilityModelItemV2Request> subItemList = studentAbilityModelItem.getSubItemList();
            studentAbilityModelItemDO.setHasSubItemList(Boolean.FALSE);
            if (CollectionUtils.isNotEmpty(subItemList)) {
                //存在子能力项
                studentAbilityModelItemDO.setHasSubItemList(Boolean.TRUE);
                //构建子节点
                List<StudentAbilityModelItemDO> subItemDOList = buildStudentAbilityModelItemDO(tenantId, schoolId, campusId, evaluateStudentAbilityModelItemPO.getId(), staffId, studentAbilityModel, subItemList);
                studentAbilityModelItemDO.setSubItemList(subItemDOList);
            } else {
                //不存在子能力项
                //构建能力项配置
                buildItemConfig(tenantId, schoolId, campusId, staffId, studentAbilityModelItemDO, studentAbilityModelItem, newItem);
            }
            itemList.add(studentAbilityModelItemDO);
        }
        return itemList;
    }

    /**
     * 构建能力项配置
     *
     * @param studentAbilityModelItem
     */
    private void buildItemConfig(String tenantId, String schoolId, String campusId, String staffId, StudentAbilityModelItemDO studentAbilityModelItemDO, StudentAbilityModelItemV2Request studentAbilityModelItem, Boolean newItem) {
        //综合实践活动
        //目前只有德育活动，德育活动的配置在创建德育活动时进行
        //构造能力项配置
        EvaluateStudentAbilityModelItemConfigPO moralItemConfigPO = buildEvaluateStudentAbilityModelItemConfigPO(tenantId, schoolId, campusId, staffId, studentAbilityModelItemDO, studentAbilityModelItem.getMoralItemConfig(), newItem);
        StudentAbilityModelMoralBusinessItemConfigDO studentAbilityModelMoralBusinessItemConfigDO = new StudentAbilityModelMoralBusinessItemConfigDO();
        studentAbilityModelMoralBusinessItemConfigDO.setEvaluateStudentAbilityModelItemConfigPO(moralItemConfigPO);
        studentAbilityModelItemDO.setMoralBusinessItemConfigDO(studentAbilityModelMoralBusinessItemConfigDO);
        //指标
        //构造能力项配置
        StudentAbilityModelTargetItemConfigV2Request targetItemConfig = studentAbilityModelItem.getTargetItemConfig();
        EvaluateStudentAbilityModelItemConfigPO targetItemConfigPO = buildEvaluateStudentAbilityModelItemConfigPO(tenantId, schoolId, campusId, staffId, studentAbilityModelItemDO, targetItemConfig, newItem);
        StudentAbilityModelTargetBusinessItemConfigDO targetBusinessItemConfigDO = new StudentAbilityModelTargetBusinessItemConfigDO();
        targetBusinessItemConfigDO.setEvaluateStudentAbilityModelItemConfigPO(targetItemConfigPO);
        List<TargetBusinessMergePO> targetBusinessMergePOList = new ArrayList<>();
        for (StudentAbilityTargetBusinessMergeRequest targetBusinessMergeRequest : targetItemConfig.getTargetBusinessMergeList()) {
            TargetBusinessMergePO targetBusinessMergePO = new TargetBusinessMergePO();
            targetBusinessMergePO.setId(SnowFlakeIdUtil.nextId());
            targetBusinessMergePO.setSubmitId(targetBusinessMergeRequest.getSubmitId());
            targetBusinessMergePO.setSubmitName(targetBusinessMergeRequest.getSubmitName());
            targetBusinessMergePO.setSubmitType(targetBusinessMergeRequest.getSubmitType());
            targetBusinessMergePO.setModuleCode(targetBusinessMergeRequest.getModuleCode());
            targetBusinessMergePO.setId(Objects.nonNull(targetBusinessMergeRequest.getTargetBusinessMergeId())
                    ? targetBusinessMergeRequest.getTargetBusinessMergeId() : SnowFlakeIdUtil.nextId());
            targetBusinessMergePO.setBusinessType(BusinessMergeTypeEnum.STUDENT_MODEL.getType());
            targetBusinessMergePO.setBusinessId(targetItemConfigPO.getId());
            targetBusinessMergePO.setTenantId(tenantId);
            targetBusinessMergePO.setSchoolId(schoolId);
            targetBusinessMergePO.setCampusId(campusId);
            targetBusinessMergePO.setCreateBy(staffId);
//            targetBusinessMergePO.setCreateTime(new Date());
//            targetBusinessMergePO.setUpdateTime(new Date());
//            targetBusinessMergePO.setDeleted(Boolean.FALSE);
            targetBusinessMergePO.setUpdateBy(staffId);
            //根据submitId和submitType构建具体类型id
            parseSubmitId(targetBusinessMergePO);
            targetBusinessMergePOList.add(targetBusinessMergePO);
        }
        targetBusinessItemConfigDO.setTargetBusinessMergePOList(targetBusinessMergePOList);
        studentAbilityModelItemDO.setTargetBusinessItemConfigDO(targetBusinessItemConfigDO);
        //课程
        StudentAbilityModelSubjectItemConfigV2Request subjectItemConfig = studentAbilityModelItem.getSubjectItemConfig();
        EvaluateStudentAbilityModelItemConfigPO subjectItemConfigPO = buildEvaluateStudentAbilityModelItemConfigPO(tenantId, schoolId, campusId, staffId, studentAbilityModelItemDO, subjectItemConfig, newItem);
        StudentAbilityModelSubjectBusinessItemConfigDO subjectBusinessItemConfigDO = new StudentAbilityModelSubjectBusinessItemConfigDO();
        subjectBusinessItemConfigDO.setEvaluateStudentAbilityModelItemConfigPO(subjectItemConfigPO);
        List<SubjectBusinessMergePO> subjectBusinessMergePOList = new ArrayList<>();
        for (StudentAbilitySubjectBusinessMergeRequest subjectBusinessMergeRequest : subjectItemConfig.getSubjectBusinessMergeList()) {
            SubjectBusinessMergePO subjectBusinessMergePO = studentModelV2Convert.toSubjectBusinessMergePO(subjectBusinessMergeRequest);
            subjectBusinessMergePO.setId(SnowFlakeIdUtil.nextId());
            subjectBusinessMergePO.setBusinessId(subjectItemConfigPO.getId());
            subjectBusinessMergePO.setBusinessType(BusinessMergeTypeEnum.STUDENT_MODEL.getType());
            subjectBusinessMergePO.setTenantId(tenantId);
            subjectBusinessMergePO.setSchoolId(schoolId);
            subjectBusinessMergePO.setCampusId(campusId);
            subjectBusinessMergePO.setCreateBy(staffId);
//            subjectBusinessMergePO.setCreateTime(new Date());
//            subjectBusinessMergePO.setUpdateTime(new Date());
//            subjectBusinessMergePO.setDeleted(Boolean.FALSE);
            subjectBusinessMergePO.setUpdateBy(staffId);
            subjectBusinessMergePOList.add(subjectBusinessMergePO);
        }
        subjectBusinessItemConfigDO.setSubjectBusinessMergePOList(subjectBusinessMergePOList);
        studentAbilityModelItemDO.setSubjectBusinessItemConfigDO(subjectBusinessItemConfigDO);
    }

    //构造能力项配置
    private EvaluateStudentAbilityModelItemConfigPO buildEvaluateStudentAbilityModelItemConfigPO(String tenantId, String schoolId, String campusId, String staffId, StudentAbilityModelItemDO studentAbilityModelItemDO, StudentAbilityModelItemConfigV2Request itemConfig, Boolean newItem) {
        EvaluateStudentAbilityModelItemConfigPO itemConfigPO = studentModelV2Convert.toStudentAbilityModelItemConfigPO(itemConfig);
        if (Objects.isNull(itemConfig.getItemConfigId())) {
            itemConfigPO.setId(SnowFlakeIdUtil.nextId());
        } else {
            if (newItem) {
                //新增能力项，能力项配置也要新增
                itemConfigPO.setId(SnowFlakeIdUtil.nextId());
            } else {
                itemConfigPO.setId(itemConfig.getItemConfigId());
            }
        }
        itemConfigPO.setAbilityItemType(itemConfig.getAbilityItemConfigType());
        itemConfigPO.setCreateBy(staffId);
        itemConfigPO.setUpdateBy(staffId);
        itemConfigPO.setTenantId(tenantId);
        itemConfigPO.setSchoolId(schoolId);
        itemConfigPO.setCampusId(campusId);
//        itemConfigPO.setCreateTime(new Date());
//        itemConfigPO.setUpdateTime(new Date());
//        itemConfigPO.setDeleted(Boolean.FALSE);
        itemConfigPO.setAbilityItemId(studentAbilityModelItemDO.getEvaluateStudentAbilityModelItemPO().getId());
        itemConfigPO.setEnabled(itemConfigPO.getEnabled());
        return itemConfigPO;
    }

    /**
     * 根据submitId和submitType构建具体类型id
     *
     * @param targetBusinessMergePO
     */
    private void parseSubmitId(TargetBusinessMergePO targetBusinessMergePO) {
        targetBusinessMergePO.setTargetGroupId(Constant.LONG_NEGATIVE_ONE);
        targetBusinessMergePO.setTargetId(Constant.LONG_NEGATIVE_ONE);
        targetBusinessMergePO.setOptionId(Constant.MINUS_ONE);
        if (Objects.equals(targetBusinessMergePO.getSubmitType(), TargetBusinessMergeSubmitTypeEnum.GROUP.getType())) {
            targetBusinessMergePO.setTargetGroupId(Long.valueOf(targetBusinessMergePO.getSubmitId()));
        } else if (Objects.equals(targetBusinessMergePO.getSubmitType(), TargetBusinessMergeSubmitTypeEnum.TARGET.getType())) {
            targetBusinessMergePO.setTargetId(Long.valueOf(targetBusinessMergePO.getSubmitId()));
        } else if (Objects.equals(targetBusinessMergePO.getSubmitType(), TargetBusinessMergeSubmitTypeEnum.OPTION.getType())) {
            targetBusinessMergePO.setOptionId(targetBusinessMergePO.getSubmitId());
        }
    }

    /**
     * 学生能力模型回显接口
     */
    @Override
    public StudentAbilityModelNewResponse getStudentAbilityModel(String campusId) {

        StudentAbilityModelDO studentAbilityModelDO = studentAbilityModelManager.getStudentAbilityModelDO(campusId);
        if (BeanUtil.isEmpty(studentAbilityModelDO)) {
            log.warn("学生能力模型记录为空，campusId:{}", campusId);
            return null;
        }

        StudentAbilityModel studentAbilityModel = studentAbilityModelDO.getStudentAbilityModel();
        if (BeanUtil.isEmpty(studentAbilityModel)) {
            log.warn("学生能力模型主记录为空，studentAbilityModelDO:{}", JSONUtil.toJsonStr(studentAbilityModelDO));
            return null;
        }
        StudentAbilityModelNewResponse response = studentModelV2Convert.toStudentAbilityModelNewResponse(studentAbilityModel);

        //成绩来源
        StudentAbilityOutcomeDTO studentAbilityOutcome = new StudentAbilityOutcomeDTO();
        String examSource = studentAbilityModel.getExamSource();
        if(CharSequenceUtil.isBlank(examSource)){
            studentAbilityOutcome.setExamSourceType(StudentAbilityExamSourceConstant.EXAM_SCORE);
            studentAbilityOutcome.setCalculateType(1);
            List<ExamConfigDTO> examConfigs = this.getExamConfigDTOS();
            studentAbilityOutcome.setExamConfigs(examConfigs);
        }else {
            studentAbilityOutcome = JSONUtil.toBean(examSource, StudentAbilityOutcomeDTO.class);
        }
        response.setStudentAbilityOutcomeDTO(studentAbilityOutcome);


        List<StudentAbilityModelItemDO> studentAbilityModelItemDOList = studentAbilityModelDO.getStudentAbilityModelItemDOList();
        if (CollUtil.isEmpty(studentAbilityModelItemDOList)) {
            log.warn("学生能力模型能力项记录为空，studentAbilityModelDO:{}", JSONUtil.toJsonStr(studentAbilityModelDO));
            return response;
        }

        // 构建能力模型配置
        List<StudentAbilityModelItemV2Response> modelItemList = this.buildModelItemList(studentAbilityModelItemDOList);
        response.setModelItemList(modelItemList);

        return response;
    }


    /**
     * 获取考试配置
     */
    private List<ExamConfigDTO> getExamConfigDTOS() {
        List<ExamConfigDTO> examConfigs = new ArrayList<>();

        // 获取ReportExamTypeEnum的所有枚举值
        ScoreStandardExamTypeEnum[] values = ScoreStandardExamTypeEnum.values();
        for (ScoreStandardExamTypeEnum value : values) {
            ExamConfigDTO examConfigDTO = new ExamConfigDTO();
            examConfigDTO.setExamType(value.getCode());
            if (ScoreStandardExamTypeEnum.FINAL_EXAM.getCode().equals(value.getCode())) {
                examConfigDTO.setWeight(100);
                examConfigDTO.setEnableFlag(true);
            } else {
                examConfigDTO.setWeight(0);
                examConfigDTO.setEnableFlag(false);
            }
            examConfigDTO.setAllExamFlag(true);
            examConfigDTO.setSortIndex(value.getCode());
            examConfigDTO.setExams(Collections.emptyList());
            examConfigs.add(examConfigDTO);
        }
        return examConfigs;
    }

    /**
     * 学生能力模型归档
     */
    @Override
    public void archivedAndLog(StuAbilityUpgradeQuery query,
                               StudentAbilityModelArchivedPO abilityModelArchive,
                               SysArchivedLogDTO sysArchivedLogDTO) {
        try {
            studentAbilityModelArchivedManager.save(abilityModelArchive);
            log.info("【学生能力模型归档】保存数据成功 归档参数：{}", JSONUtil.toJsonStr(abilityModelArchive));
            sysArchivedLogDTO.setStatus(DimStatusEnum.SUCCESS.getCode());
        } catch (Exception e) {
            sysArchivedLogDTO.setStatus(DimStatusEnum.FAILED.getCode());
            sysArchivedLogDTO.setRemark(e.getMessage());
            log.error("【学生能力模型归档】-失败 归档参数：{}", JSONUtil.toJsonStr(query), e);
            throw new BizException("学生能力模型归档失败");
        } finally {
            if (sysArchivedLogManager.saveSysArchivedLog(sysArchivedLogDTO)) {
                log.info("【学生能力模型归档】归档日志保存成功 归档参数：{}", JSONUtil.toJsonStr(query));
            }
        }
    }

    @Override
    public StudentAbilityModelOverviewResponse getStudentAbilityModelOverview(String campusId) {
        StudentAbilityModel studentAbilityModel = studentAbilityModelManager.getStudentAbilityModel(campusId);
        if (Objects.isNull(studentAbilityModel)) {
            studentAbilityModel = buildDefaultStudentModelV2(WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId(), WebUtil.getStaffId());
        }
        return studentModelV2Convert.toStudentAbilityModelOverviewResponse(studentAbilityModel);
    }

    @Override
    public Boolean updateEnabled(StudentAbilityModelV2UpdateEnabledRequest updateEnabledRequest) {
        StudentAbilityModel existStudentAbilityModel = studentAbilityModelManager.getStudentAbilityModel(WebUtil.getCampusId());
        if (Objects.nonNull(existStudentAbilityModel)) {
            updateEnabledRequest.setId(existStudentAbilityModel.getId());
        }
        if (Objects.isNull(updateEnabledRequest.getId())) {
            //新增学生能力模型记录
            StudentAbilityModel studentAbilityModel = buildDefaultStudentModelV2(WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId(), WebUtil.getStaffId());
            studentAbilityModel.setEnabled(updateEnabledRequest.getEnabled());
            studentAbilityModelManager.save(studentAbilityModel);
            return Boolean.TRUE;
        }
        return studentAbilityModelManager.updateEnabledStatus(updateEnabledRequest);
    }

    /**
     * 构建默认的学生能力模型
     *
     * @return
     */
    private StudentAbilityModel buildDefaultStudentModelV2(String tenantId, String schoolId, String campusId, String staffId) {
        Date now = new Date();
        StudentAbilityModel studentAbilityModel = new StudentAbilityModel();
        studentAbilityModel.setEnabled(Constant.ZERO);
        studentAbilityModel.setAbilityModelName("学生能力模型");
        studentAbilityModel.setDeleted(Boolean.FALSE);
        studentAbilityModel.setContrastRange(StudentAbilityModelContrastRangeEnum.CLASS.getType());
        studentAbilityModel.setTenantId(tenantId);
        studentAbilityModel.setSchoolId(schoolId);
        studentAbilityModel.setCampusId(campusId);
        studentAbilityModel.setCreateTime(now);
        studentAbilityModel.setCreateBy(staffId);
        studentAbilityModel.setUpdateTime(now);
        studentAbilityModel.setUpdateBy(staffId);
        studentAbilityModel.setSubmitTime(now);
        return studentAbilityModel;
    }

    /**
     * 构建能力模型能力配置
     */
    private List<StudentAbilityModelItemV2Response> buildModelItemList(List<StudentAbilityModelItemDO> studentAbilityModelItemDOList) {
        List<StudentAbilityModelItemV2Response> modelItemList = new ArrayList<>();
        for (StudentAbilityModelItemDO studentAbilityModelItemDO : studentAbilityModelItemDOList) {
            if (BeanUtil.isEmpty(studentAbilityModelItemDO)) {
                log.warn("学生能力模型能力项为空，studentAbilityModelItemDO:{}", JSONUtil.toJsonStr(studentAbilityModelItemDO));
                continue;
            }
            EvaluateStudentAbilityModelItemPO evaluateStudentAbilityModelItemPO = studentAbilityModelItemDO
                    .getEvaluateStudentAbilityModelItemPO();
            if (BeanUtil.isEmpty(evaluateStudentAbilityModelItemPO)) {
                log.warn("学生能力模型能力项PO为空，studentAbilityModelItemDO:{}", JSONUtil.toJsonStr(studentAbilityModelItemDO));
                continue;
            }
            StudentAbilityModelItemV2Response itemResponse = studentModelV2Convert
                    .toStudentAbilityModelItemV2Response(evaluateStudentAbilityModelItemPO);
            // 构建能力项
            this.buildStudentAbilityModelItemV2Response(studentAbilityModelItemDO, itemResponse);
            modelItemList.add(itemResponse);
        }
        return modelItemList;
    }

    /**
     * 构建能力项
     */
    private void buildStudentAbilityModelItemV2Response(StudentAbilityModelItemDO studentAbilityModelItemDO,
                                                        StudentAbilityModelItemV2Response itemResponse) {
        if (Boolean.TRUE.equals(studentAbilityModelItemDO.getHasSubItemList())) {
            List<StudentAbilityModelItemV2Response> subItemListResponse = new ArrayList<>();
            List<StudentAbilityModelItemDO> subItemList = studentAbilityModelItemDO.getSubItemList();
            if (CollectionUtils.isEmpty(subItemList)) {
                log.warn("学生能力模型能力项子项记录为空，studentAbilityModelItemDO:{}",
                        JSONUtil.toJsonStr(studentAbilityModelItemDO));
                return;
            }
            for (StudentAbilityModelItemDO abilityModelItemDO : subItemList) {
                if (BeanUtil.isEmpty(abilityModelItemDO)) {
                    log.warn("学生能力模型能力项子项为空，abilityModelItemDO:{}", JSONUtil.toJsonStr(abilityModelItemDO));
                    continue;
                }

                EvaluateStudentAbilityModelItemPO evaluateStudentAbilityModelItemPO = abilityModelItemDO
                        .getEvaluateStudentAbilityModelItemPO();
                if (BeanUtil.isEmpty(evaluateStudentAbilityModelItemPO)) {
                    log.warn("学生能力模型能力项子项PO为空，abilityModelItemDO:{}", JSONUtil.toJsonStr(abilityModelItemDO));
                    continue;
                }

                StudentAbilityModelItemV2Response childrenItemResponse = studentModelV2Convert
                        .toStudentAbilityModelItemV2Response(evaluateStudentAbilityModelItemPO);

                // 学科-学生能力模型关联对象
                this.buildSubjectBusinessItemConfigResponse(abilityModelItemDO, childrenItemResponse);
                // 指标-学生能力模型关联对象
                this.buildTargetBusinessItemConfigResponse(abilityModelItemDO, childrenItemResponse);
                // 德育-学生能力模型关联对象
                this.buildMoralBusinessItemConfigResponse(abilityModelItemDO, childrenItemResponse);
                subItemListResponse.add(childrenItemResponse);
            }
            itemResponse.setSubItemList(subItemListResponse);
        } else {
            // 学科-学生能力模型关联对象
            this.buildSubjectBusinessItemConfigResponse(studentAbilityModelItemDO, itemResponse);
            // 指标-学生能力模型关联对象
            this.buildTargetBusinessItemConfigResponse(studentAbilityModelItemDO, itemResponse);
            // 德育-学生能力模型关联对象
            this.buildMoralBusinessItemConfigResponse(studentAbilityModelItemDO, itemResponse);
        }
    }

    /**
     * 德育活动-学生能力模型关联对象
     */
    private void buildMoralBusinessItemConfigResponse(StudentAbilityModelItemDO studentAbilityModelItemDO,
                                                      StudentAbilityModelItemV2Response itemResponse) {
        StudentAbilityModelMoralBusinessItemConfigDO studentAbilityModelMoralBusinessItemConfigDO = Optional
                .ofNullable(studentAbilityModelItemDO.getMoralBusinessItemConfigDO())
                .orElse(new StudentAbilityModelMoralBusinessItemConfigDO());
        EvaluateStudentAbilityModelItemConfigPO modelItemConfigPO = studentAbilityModelMoralBusinessItemConfigDO
                .getEvaluateStudentAbilityModelItemConfigPO();
        if (Objects.equals(Constant.ZERO, modelItemConfigPO.getEnabled())
                && BigDecimal.ZERO.compareTo(modelItemConfigPO.getWeight()) == 0) {
            modelItemConfigPO.setWeight(null);
        }
        StudentAbilityModelItemConfigBaseResponse modelItemConfigBaseResponse = studentModelV2Convert
                .toStudentAbilityModelItemConfigBaseResponse(modelItemConfigPO);
        itemResponse.setMoralItemConfig(modelItemConfigBaseResponse);
    }

    /**
     * 指标-学生能力模型关联对象
     */
    private void buildTargetBusinessItemConfigResponse(StudentAbilityModelItemDO studentAbilityModelItemDO,
                                                       StudentAbilityModelItemV2Response itemResponse) {
        StudentAbilityModelTargetBusinessItemConfigDO targetBusinessItemConfigDO = Optional
                .ofNullable(studentAbilityModelItemDO.getTargetBusinessItemConfigDO())
                .orElse(new StudentAbilityModelTargetBusinessItemConfigDO());
        EvaluateStudentAbilityModelItemConfigPO targetStudentAbilityModelItemConfigPO = targetBusinessItemConfigDO
                .getEvaluateStudentAbilityModelItemConfigPO();
        if (Objects.equals(Constant.ZERO, targetStudentAbilityModelItemConfigPO.getEnabled())
                && BigDecimal.ZERO.compareTo(targetStudentAbilityModelItemConfigPO.getWeight()) == 0) {
            targetStudentAbilityModelItemConfigPO.setWeight(null);
        }
        List<TargetBusinessMergePO> targetBusinessMergePOList = targetBusinessItemConfigDO
                .getTargetBusinessMergePOList();
        StudentAbilityModelItemConfigTargetResponse targetBusinessItemConfigResponse = studentModelV2Convert
                .toStudentAbilityModelItemConfigTargetResponse(targetStudentAbilityModelItemConfigPO);
        List<StudentAbilityTargetBusinessMergeResponse> targetBusinessMergeList = studentModelV2Convert
                .toStudentAbilityTargetBusinessMergeResponseList(targetBusinessMergePOList);
        if (CollectionUtils.isEmpty(targetBusinessMergeList)) {
            targetBusinessMergeList = Collections.emptyList();
        }
        targetBusinessItemConfigResponse.setTargetBusinessMergeList(targetBusinessMergeList);
        itemResponse.setTargetItemConfig(targetBusinessItemConfigResponse);
    }

    /**
     * 学科-学生能力模型关联对象
     */
    private void buildSubjectBusinessItemConfigResponse(StudentAbilityModelItemDO studentAbilityModelItemDO,
                                                        StudentAbilityModelItemV2Response childrenItemResponse) {
        StudentAbilityModelSubjectBusinessItemConfigDO subjectBusinessItemConfigDO = Optional
                .ofNullable(studentAbilityModelItemDO.getSubjectBusinessItemConfigDO())
                .orElse(new StudentAbilityModelSubjectBusinessItemConfigDO());
        EvaluateStudentAbilityModelItemConfigPO evaluateStudentAbilityModelItemConfigPO = subjectBusinessItemConfigDO
                .getEvaluateStudentAbilityModelItemConfigPO();
        if (Objects.equals(Constant.ZERO, evaluateStudentAbilityModelItemConfigPO.getEnabled())
                && BigDecimal.ZERO.compareTo(evaluateStudentAbilityModelItemConfigPO.getWeight()) == 0) {
            evaluateStudentAbilityModelItemConfigPO.setWeight(null);
        }
        StudentAbilityModelItemConfigSubjectResponse subjectBusinessItemConfigResponse = studentModelV2Convert
                .toStudentAbilityModelItemConfigSubjectResponse(evaluateStudentAbilityModelItemConfigPO);
        List<SubjectBusinessMergePO> subjectBusinessMergePOList = subjectBusinessItemConfigDO
                .getSubjectBusinessMergePOList();
        List<StudentAbilitySubjectBusinessMergeResponse> subjectBusinessMergeList = studentModelV2Convert
                .toStudentAbilitySubjectBusinessMergeResponseList(subjectBusinessMergePOList);
        if (CollUtil.isEmpty(subjectBusinessMergeList)) {
            subjectBusinessMergeList = Collections.emptyList();
        }
        subjectBusinessItemConfigResponse.setSubjectBusinessMergeList(subjectBusinessMergeList);
        childrenItemResponse.setSubjectItemConfig(subjectBusinessItemConfigResponse);
    }

    /**
     * 校验学生能力模型参数
     *
     * @param studentAbilityModelRequest
     */
    private void checkStudentAbilityModelV2Param(StudentAbilityModelV2Request studentAbilityModelRequest, String campusId) {
        log.info("【学生能力模型】-【保存/修改学生能力模型配置】-配置信息:{}", JSONObject.toJSONString(studentAbilityModelRequest));
        Assert.notBlank(studentAbilityModelRequest.getAbilityModelName(), () -> new BizException("请输入能力模型名称"));
        Assert.notNull(studentAbilityModelRequest.getContrastRange(), () -> new BizException("请选择能力模型对比范围"));
        List<StudentAbilityModelItemV2Request> modelItemList = studentAbilityModelRequest.getModelItemList();
        Assert.notEmpty(modelItemList, () -> new BizException("请填写能力项配置"));
        //校验能力项参数
        checkStudentAbilityModelItemV2Param(modelItemList);
    }

    /**
     * 校验能力项参数
     *
     * @param modelItemList
     */
    private void checkStudentAbilityModelItemV2Param(List<StudentAbilityModelItemV2Request> modelItemList) {
        if (CollectionUtils.isEmpty(modelItemList)) {
            return;
        }
        for (StudentAbilityModelItemV2Request studentAbilityModelItem : modelItemList) {
            Assert.notBlank(studentAbilityModelItem.getAbilityItemName(), () -> new BizException("请填写能力项名称"));
            Assert.isTrue(studentAbilityModelItem.getAbilityItemName().length() <= 15, () -> new BizException("能力项名称最多不超过15个字符"));
            Assert.notNull(studentAbilityModelItem.getLevel(), () -> new BizException("能力项等级不能为空"));
            Assert.notNull(studentAbilityModelItem.getSortIndex(), () -> new BizException("排序字段不能为空"));
            List<StudentAbilityModelItemV2Request> subItemList = studentAbilityModelItem.getSubItemList();
            if (CollectionUtils.isEmpty(subItemList)) {
                //校验能力项配置参数
                checkStudentAbilityModelItemConfigV2Param(studentAbilityModelItem);
            } else {
                //存在子能力项
                checkStudentAbilityModelItemV2Param(subItemList);
            }
        }
    }

    /**
     * 校验子能力项配置参数
     *
     * @param studentAbilityModelItem
     */
    private void checkStudentAbilityModelItemConfigV2Param(StudentAbilityModelItemV2Request studentAbilityModelItem) {
        StudentAbilityModelMoralItemConfigV2Request moralItemConfig = studentAbilityModelItem.getMoralItemConfig();
        moralItemConfig.setAbilityItemConfigType(StudentAbilityModelItemConfigTypeEnum.MORAL.getType());
        StudentAbilityModelTargetItemConfigV2Request targetItemConfig = studentAbilityModelItem.getTargetItemConfig();
        targetItemConfig.setAbilityItemConfigType(StudentAbilityModelItemConfigTypeEnum.TARGET.getType());
        StudentAbilityModelSubjectItemConfigV2Request subjectItemConfig = studentAbilityModelItem.getSubjectItemConfig();
        subjectItemConfig.setAbilityItemConfigType(StudentAbilityModelItemConfigTypeEnum.SUBJECT.getType());
        if (Objects.equals(moralItemConfig.getEnabled(), Constant.ZERO)
                && Objects.equals(targetItemConfig.getEnabled(), Constant.ZERO)
                && Objects.equals(subjectItemConfig.getEnabled(), Constant.ZERO)) {
            //三个选项都未启用
            throw new BizException("请至少启用一个能力项的纬度配置");
        }
        //所有启用的能力项权重之和
        BigDecimal totalEnabledWeight = BigDecimal.ZERO;
        //检查能力项配置
        totalEnabledWeight = checkItemConfig(moralItemConfig, totalEnabledWeight);
        totalEnabledWeight = checkItemConfig(targetItemConfig, totalEnabledWeight);
        totalEnabledWeight = checkItemConfig(subjectItemConfig, totalEnabledWeight);
        if (new BigDecimal("100").compareTo(totalEnabledWeight) != 0) {
            throw new BizException("能力项权重配置相加必须等于100");
        }
        //格式化权重小数点
        formatWeight(moralItemConfig, targetItemConfig, subjectItemConfig);
        //校验指标-业务关联关系
        if (CollectionUtils.isEmpty(targetItemConfig.getTargetBusinessMergeList())) {
            if (Objects.equals(targetItemConfig.getEnabled(), Constant.ONE)) {
                //启用，则会抛出异常
                throw new BizException("请完善过程性评价配置信息");
            }
        } else {
            //检验指标-业务关联关系
            checkTargetBusinessListParam(targetItemConfig.getTargetBusinessMergeList());
        }
        //校验课程-业务关联关系
        if (CollectionUtils.isEmpty(subjectItemConfig.getSubjectBusinessMergeList())) {
            if (Objects.equals(subjectItemConfig.getEnabled(), Constant.ONE)) {
                throw new BizException("请完善学科成绩配置信息");
            }
        } else {
            //课程-业务关联关系
            checkSubjectBusinessListParam(subjectItemConfig.getSubjectBusinessMergeList());
        }
    }

    /**
     * 格式化权重小数点,防止四舍五入时出现相加不等于100的情况
     *
     * @param moralItemConfig
     * @param targetItemConfig
     * @param subjectItemConfig
     */
    private void formatWeight(StudentAbilityModelMoralItemConfigV2Request moralItemConfig,
                              StudentAbilityModelTargetItemConfigV2Request targetItemConfig,
                              StudentAbilityModelSubjectItemConfigV2Request subjectItemConfig) {
        BigDecimal totalWeight = new BigDecimal("100");
        //判断配置是否启用
        if (Objects.equals(moralItemConfig.getEnabled(), Constant.ONE)) {
            moralItemConfig.setWeight(moralItemConfig.getWeight().setScale(2, RoundingMode.HALF_UP));
            totalWeight = totalWeight.subtract(moralItemConfig.getWeight());
        }
        //判断配置是否启用
        if (Objects.equals(targetItemConfig.getEnabled(), Constant.ONE)) {
            BigDecimal targetWeight = targetItemConfig.getWeight();
            if (totalWeight.compareTo(targetWeight) < 0) {
                //指标权重大于剩余权重
                targetItemConfig.setWeight(totalWeight);
            } else {
                targetItemConfig.setWeight(targetItemConfig.getWeight().setScale(2, RoundingMode.HALF_UP));
            }
        }
        //判断配置是否启用
        if (Objects.equals(targetItemConfig.getEnabled(), Constant.ONE)) {
            totalWeight = totalWeight.subtract(targetItemConfig.getWeight());
            subjectItemConfig.setWeight(totalWeight);
        }
    }

    /**
     * 检查能力项配置
     *
     * @param itemConfig
     * @throws BizException
     */
    private BigDecimal checkItemConfig(StudentAbilityModelItemConfigV2Request itemConfig, BigDecimal totalEnabledWeight) throws BizException {
        Assert.notNull(itemConfig.getEnabled(), () -> new BizException("请传入配置是否启用"));
        if (Objects.equals(itemConfig.getEnabled(), Constant.ONE)) {
            Assert.notNull(itemConfig.getAbilityItemConfigType(), () -> new BizException("请传入能力项类型"));
            Assert.notNull(itemConfig.getWeight(), () -> new BizException("请填写权重信息"));
            Assert.isTrue(itemConfig.getWeight().compareTo(BigDecimal.ZERO) >= 0, () -> new BizException("能力项权重不能为负数"));
            totalEnabledWeight = totalEnabledWeight.add(itemConfig.getWeight());
        } else {
            //配置未启用，如果权重为空，则设置权重为0
            if (Objects.isNull(itemConfig.getWeight())) {
                itemConfig.setWeight(BigDecimal.ZERO);
            }
        }
        return totalEnabledWeight;
    }

    /**
     * 检验指标-学生能力模型关联关系
     *
     * @param targetBusinessMergeList
     */
    private void checkTargetBusinessListParam(List<StudentAbilityTargetBusinessMergeRequest> targetBusinessMergeList) {
        for (StudentAbilityTargetBusinessMergeRequest targetBusinessMergeRequest : targetBusinessMergeList) {
            Assert.notNull(targetBusinessMergeRequest.getModuleCode(), () -> new BizException("请选择五育模块"));
            Assert.notBlank(targetBusinessMergeRequest.getSubmitName(), () -> new BizException("请选择指标名称"));
//            if (Objects.equals(targetBusinessMergeRequest.getSubmitType(), TargetBusinessMergeSubmitTypeEnum.INITIAL_SCORE.getType())) {
//                targetBusinessMergeRequest.setSubmitId(Constant.NEGATIVE_ONE + "");
//            } else {
            Assert.notNull(targetBusinessMergeRequest.getSubmitId(), () -> new BizException("请选择指标id"));
            Assert.notNull(targetBusinessMergeRequest.getSubmitType(), () -> new BizException("请选择指标类型"));
//            }
        }
    }

    /**
     * 检验课程-学生能力模型关联关系
     *
     * @param subjectBusinessMergeList
     */
    private void checkSubjectBusinessListParam(List<StudentAbilitySubjectBusinessMergeRequest> subjectBusinessMergeList) {
        BigDecimal allWeight = new BigDecimal(0);
        for (StudentAbilitySubjectBusinessMergeRequest subjectBusinessMergeRequest : subjectBusinessMergeList) {
            Assert.notNull(subjectBusinessMergeRequest.getSubjectCode(), () -> new BizException("请传入课程code"));
            Assert.notNull(subjectBusinessMergeRequest.getWeight(), () -> new BizException("请传入课程权重"));
            Assert.isTrue(subjectBusinessMergeRequest.getWeight().compareTo(BigDecimal.ZERO) >= 0, () -> new BizException("课程权重不能为负数"));
            allWeight = allWeight.add(subjectBusinessMergeRequest.getWeight());
            //体测
            if (StringUtils.equals(FixSubjectCodeEnum.TICE.getCode(), subjectBusinessMergeRequest.getSubjectCode())) {
                subjectBusinessMergeRequest.setSubjectId(Constant.MINUS_ONE);
                subjectBusinessMergeRequest.setSubjectCode(FixSubjectCodeEnum.TICE.getCode());
                subjectBusinessMergeRequest.setSubjectName(FixSubjectCodeEnum.TICE.getDesc());
            } else {
                Assert.notNull(subjectBusinessMergeRequest.getSubjectId(), () -> new BizException("请传入课程id"));
                Assert.notBlank(subjectBusinessMergeRequest.getSubjectName(), () -> new BizException("请传入课程名称"));
            }
        }
        if (new BigDecimal("100").compareTo(allWeight) != 0) {
            throw new BizException("课程权重相加必须等于100");
        }
    }


    private void deleteStudentAbilityModelSumitIdsCache(String campusId) {
        log.info("准备删除学生能力模型相关的缓存");
        String pattern = StrUtil.format(RedisKeyConstants.STUDENT_ABILITY_MODEL_SUMITIDS, campusId, "*", "*");

        List<String> keys = new ArrayList<>();
        redisTemplate.execute(new RedisCallback<Void>() {
            @Override
            public Void doInRedis(RedisConnection connection) throws DataAccessException {
                ScanOptions options = ScanOptions.scanOptions().match(pattern).count(1000).build();
                try (Cursor<byte[]> cursor = connection.scan(options)) {
                    while (cursor.hasNext()) {
                        keys.add(new String(cursor.next()));
                    }
                } catch (IOException e) {
                    // Handle the IOException if necessary
                    log.warn("Error occurred while scanning Redis keys", e);
                }
                return null;
            }
        });

        if (!keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
        log.info("已删除学生能力模型相关的缓存，key的信息：{}, 共删除{}个", JSONUtil.toJsonStr(keys), keys.size());
    }
}
