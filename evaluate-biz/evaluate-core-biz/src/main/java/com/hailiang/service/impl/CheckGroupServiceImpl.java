package com.hailiang.service.impl;

import cn.hutool.core.lang.Assert;
import com.google.common.collect.Lists;
import com.hailiang.base.BaseService;
import com.hailiang.convert.CheckGroupConvert;
import com.hailiang.enums.SourceTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.manager.CheckGroupManager;
import com.hailiang.model.dto.SaveCheckGroupDTO;
import com.hailiang.model.dto.UpdateCheckGroupDTO;
import com.hailiang.model.entity.CheckGroupPO;
import com.hailiang.request.HeaderRequest;
import com.hailiang.service.CheckGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/19 16:33
 */
@Service
@RequiredArgsConstructor
public class CheckGroupServiceImpl extends BaseService implements CheckGroupService {

    private static final String SOURCE_TYPE = SourceTypeEnum.LDHQ.getCode();

    private final CheckGroupConvert groupConvert;
    private final CheckGroupManager groupManager;

    @Override
    public void convertAndCreateGroup(SaveCheckGroupDTO dto) {
        HeaderRequest headerRequest = super.checkRequestHeader();
        String tenantId = headerRequest.getTenantId();
        String schoolId = headerRequest.getSchoolId();
        String campusId = headerRequest.getCampusId();

        CheckGroupPO group = groupConvert.toCheckGroup(dto);
        group.setSourceType(SOURCE_TYPE);
        group.setTenantId(tenantId);
        group.setSchoolId(schoolId);
        group.setCampusId(campusId);
        group.setSortIndex(System.currentTimeMillis());
        group.setDeleted(Boolean.FALSE);
        groupManager.save(group);
    }

    @Override
    public void editGroup(UpdateCheckGroupDTO dto) {
        CheckGroupPO group = groupConvert.toCheckGroup(dto);
        groupManager.updateById(group);
    }

    @Override
    public List<CheckGroupPO> fillSortIndex(List<String> ids) {
        return IntStream.range(0, ids.size())
                .mapToObj(index -> {
                    CheckGroupPO group = new CheckGroupPO();
                    group.setId(Long.valueOf(ids.get(index)));
                    group.setSortIndex(index + 1L);
                    return group;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<CheckGroupPO> queryOrderedGroups(String dimId) {
        return groupManager.lambdaQuery()
                .select(CheckGroupPO::getId, CheckGroupPO::getName, CheckGroupPO::getSortIndex, CheckGroupPO::getDimId)
                .eq(CheckGroupPO::getDimId, dimId)
                .orderByAsc(Lists.newArrayList(CheckGroupPO::getSortIndex, CheckGroupPO::getId))
                .list();
    }

    @Override
    public void verifyGroupExists(Long id) {
        CheckGroupPO group = groupManager.getById(id);
        Assert.notNull(group, () -> new BizException(BizExceptionEnum.CHECK_GROUP_NOT_PRESENT));
    }

}
