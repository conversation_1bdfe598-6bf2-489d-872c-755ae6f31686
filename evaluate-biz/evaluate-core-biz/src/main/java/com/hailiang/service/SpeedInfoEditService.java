package com.hailiang.service;

import com.hailiang.model.dto.remove.RemoveSpeedRequest;
import com.hailiang.model.dto.save.BehaviourRecordHandleDTO;
import com.hailiang.model.dto.save.InfoSpeedPlayBackRequest;
import com.hailiang.model.dto.save.InfoSpeedRequest;
import com.hailiang.model.dto.save.XxbInfoSpeedDeleteRequest;
import com.hailiang.saas.model.dto.FaceDetectRequest;
import com.hailiang.saas.model.vo.StudentInfoVO;

import java.util.List;

/**
 * 极速点评
 *
 * @Description: 极速点评
 * @Author: JiangJunLi
 * @Date: Created in 2024-01-26
 * @Version: 1.6.0
 */
public interface SpeedInfoEditService {

    /**
     * 极速点评保存 星动力、星未来融合后版本
     * @param request
     */
    BehaviourRecordHandleDTO speedSaveTeacherEvaluateInfoV2(InfoSpeedRequest request);

    /**
     * 删除极速点评记录
     * @param removeSpeedRequest
     */
    void deleteSpeedByIds(RemoveSpeedRequest removeSpeedRequest);

    /**
     * 人脸自动识别
     */
    List<StudentInfoVO> faceSearch(FaceDetectRequest request);

    /**
     * 删除星学伴极速点评记录
     * @param request
     */
    void deleteSpeedByBusinessIds(XxbInfoSpeedDeleteRequest request);
}
