package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.save.SysArchivedLogDTO;
import com.hailiang.model.entity.StudentAbilityModel;
import com.hailiang.model.request.studentmodel.StudentAbilityDataV2Request;
import com.hailiang.model.request.studentmodel.StudentAbilityModelRequest;
import com.hailiang.model.request.studentmodel.StudentAbilityModelV2Request;
import com.hailiang.model.request.studentmodel.StudentAbilityModelV2UpdateEnabledRequest;
import com.hailiang.model.response.studentmodel.StudentAbilityModelDataV2Response;
import com.hailiang.model.response.studentmodel.StudentAbilityModelNewResponse;
import com.hailiang.model.response.studentmodel.StudentAbilityModelOverviewResponse;
import com.hailiang.model.response.studentmodel.StudentAbilityModelResponse;
import com.hailiang.portrait.entity.StudentAbilityModelArchivedPO;
import com.hailiang.portrait.query.StuAbilityUpgradeQuery;


/**
 * 接口
 *
 * <AUTHOR> 2024-04-19 15:29:38
 */
public interface StudentAbilityModelService extends IService<StudentAbilityModel>{

    StudentAbilityModelResponse detailStudentAbilityModel(String campusId);

    /**
     * 获取五育配置-包括历史
     */
    StudentAbilityModelResponse getModelDetail(String campusId, String schoolYear, Boolean isCurrentYear);

    boolean saveStudentAbilityModel(String campusId, StudentAbilityModelRequest studentAbilityModelRequest);

    boolean saveStudentAbilityModelNew(String campusId, StudentAbilityModelRequest studentAbilityModelRequest);

    StudentAbilityModel getStudentAbilityModelByDate(String campusId, String dateTime);

    /**
     * 保存学生能力模型V2
     * @param campusId
     * @param studentAbilityModelRequest
     * @return
     */
    boolean saveStudentAbilityModelV2(String campusId,String staffId, StudentAbilityModelV2Request studentAbilityModelRequest);

    /**
     * 学生能力模型回显接口
     */
    StudentAbilityModelNewResponse getStudentAbilityModel(String campusId);

    /**
     * 学生能力模型归档
     */
    void archivedAndLog(StuAbilityUpgradeQuery query,
                        StudentAbilityModelArchivedPO abilityModelArchive,
                        SysArchivedLogDTO sysArchivedLogDTO);

    /**
     * 获取学生能力模型概览
     * @param campusId
     * @return
     */
    StudentAbilityModelOverviewResponse getStudentAbilityModelOverview(String campusId);

    /**
     * 更新启用状态
     * @param updateEnabledRequest
     * @return
     */
    Boolean updateEnabled(StudentAbilityModelV2UpdateEnabledRequest updateEnabledRequest);
}