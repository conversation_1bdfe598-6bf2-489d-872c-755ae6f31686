package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.hailiang.constant.Constant;
import com.hailiang.convert.CheckClassInfoConvert;
import com.hailiang.enums.*;
import com.hailiang.exception.BizException;
import com.hailiang.logic.RoleLogic;
import com.hailiang.manager.*;
import com.hailiang.mapper.CheckClassInfoMapper;
import com.hailiang.model.datastatistics.dto.ClassGradeInfoDTO;
import com.hailiang.model.dto.check.CheckItemBasicInfoDTO;
import com.hailiang.model.dto.query.ListCheckDimBaseInfoDTO;
import com.hailiang.model.entity.*;
import com.hailiang.model.vo.CheckDimBaseInfoVO;
import com.hailiang.model.vo.check.*;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStaffClassQueryDTO;
import com.hailiang.remote.saas.dto.org.TcOrgQueryDTO;
import com.hailiang.remote.saas.dto.role.ResRoleQueryDTO;
import com.hailiang.remote.saas.dto.staff.StaffRoleQueryDTO;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.pojo.role.ResRoleInfoPojo;
import com.hailiang.remote.saas.vo.educational.*;
import com.hailiang.remote.saas.vo.role.ResRoleBffVO;
import com.hailiang.remote.saas.vo.role.ResStaffRoleVO;
import com.hailiang.remote.saas.vo.staff.StaffVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.service.CampusParamService;
import com.hailiang.service.CheckDutyService;
import com.hailiang.util.AssertUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/17 15:52
 */
@Slf4j
@Service
public class CheckDutyServiceImpl implements CheckDutyService {

    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private CheckClassInfoManager checkClassInfoManager;

    @Resource
    private CheckClassInfoConvert checkClassInfoConvert;

    @Resource
    private CacheSaasManager cacheSaasManager;

    @Resource
    private SaasGeneralManager saasGeneralManager;

    @Resource
    private CheckClassInfoMapper checkClassInfoMapper;

    @Resource
    private CheckDimServiceImpl checkDimService;

    @Resource
    private CampusParamService campusParamService;

    @Resource
    private CheckAppealInfoManager checkAppealInfoManager;
    @Resource
    private CheckAwardRecordManager checkAwardRecordManager;

    @Resource
    private CheckClassAwardRecordManager checkClassAwardRecordManager;

    @Resource
    private RoleLogic roleLogic;

    @Value("${oss.urlPrefix}")
    private String urlPrefix;

    /**
     * 班主任获取班级值日详情
     *
     * @param query 查询条件
     * @return
     */
    @Override
    public ClassDutyDetailVO getClassDutyDetail(ClassDutyQuery query) {

        ClassDutyDetailVO classDutyDetail = new ClassDutyDetailVO();
        classDutyDetail.setClassId(Convert.toStr(query.getClassId()));
        List<EduClassInfoLinkVO> classInfos = basicInfoRemote.listUnderByClassIds(CollUtil.newArrayList(query.getClassId()));
        Assert.notEmpty(classInfos, () -> new BizException("班级不存在"));
        EduClassInfoLinkVO classInfo = CollUtil.getFirst(classInfos);
        classDutyDetail.setClassName(classInfo.getClassName());
        classDutyDetail.setGradeCode(classInfo.getGradeCode());
        // 自动获取值日日期范围
        getCheckDateRange(classDutyDetail);

        Date startTime;
        Date endTime;

        if (Objects.nonNull(query.getStartTime()) || Objects.nonNull(query.getEndTime())) {
            // 手动传递日期
            startTime = query.getStartTime();
            endTime = DateUtil.endOfDay(query.getEndTime());
        } else {
            startTime = classDutyDetail.getStartTime();
            endTime = classDutyDetail.getEndTime();
        }
        // 初始分
        Integer campusInitScore = campusParamService.getCampusInitScore(WebUtil.getCampusId());
        classDutyDetail.setInitScore(Objects.nonNull(campusInitScore) ? Convert.toBigDecimal(campusInitScore) : Convert.toBigDecimal(0));

        // 获取值日记录
        List<CheckClassInfo> checkClassInfos = checkClassInfoManager.list(new LambdaQueryWrapper<CheckClassInfo>()
                .eq(CheckClassInfo::getClassId, Convert.toStr(query.getClassId()))
                .eq(StringUtils.isNotEmpty(query.getGradeCode()),CheckClassInfo::getGradeCode, query.getGradeCode())
                .ge(Objects.nonNull(startTime), CheckClassInfo::getCreateTime, startTime)
                .le(Objects.nonNull(endTime), CheckClassInfo::getCreateTime, endTime)
                .orderByDesc(CheckClassInfo::getCreateTime));
        if (CollUtil.isEmpty(checkClassInfos)) {
            classDutyDetail.setCurrentScore(classDutyDetail.getInitScore());
            return classDutyDetail;
        }
        if (!query.getGradeCode().equals(classDutyDetail.getGradeCode())){
            classDutyDetail.setGradeCode(query.getGradeCode());
            classDutyDetail.setClassName(checkClassInfos.get(0).getClassName());
        }
        // 封装值日记录信息
        encapsulateDutyRecord(classDutyDetail, checkClassInfos);
        return classDutyDetail;
    }

    /**
     * 封装值日记录信息
     *
     * @param classDutyDetail 待封装的实体类
     * @param checkClassInfos 值日记录信息
     */
    private void encapsulateDutyRecord(ClassDutyDetailVO classDutyDetail, List<CheckClassInfo> checkClassInfos) {
        Map<Long, String> itemMap = new HashMap<>();
        List<Long> itemIds = checkClassInfos.stream().map(CheckClassInfo::getCheckItemId).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(itemIds)) {
            // 查询检查项基本信息
            List<CheckItemBasicInfoDTO> checkItemBasics = checkClassInfoMapper.listCheckItemBasicInfo(itemIds, WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId());
            if (CollUtil.isNotEmpty(checkItemBasics)) {
                itemMap = checkItemBasics.stream().collect(Collectors.toMap(CheckItemBasicInfoDTO::getItemId, CheckItemBasicInfoDTO::getIconUrl));
            }
        }

        // 计算总分(过滤已撤回的)
        BigDecimal totalScore = checkClassInfos.stream().filter(s->CollUtil.newArrayList(1,3).contains(s.getCheckStatus())).map(CheckClassInfo::getTotalScore).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        classDutyDetail.setCurrentScore(classDutyDetail.getInitScore().add(totalScore));
        // 设置图标
        List<DutyRecord> dutyRecords = checkClassInfoConvert.toDutyRecordList(checkClassInfos);
        for (DutyRecord dutyRecord : dutyRecords) {
            if (StrUtil.isNotBlank(itemMap.get(dutyRecord.getCheckItemId()))) {
                dutyRecord.setIconUrl(urlPrefix + itemMap.get(dutyRecord.getCheckItemId()));
            }
        }
        // 查询是否存在申诉记录
        Map<Long, CheckAppealInfo> checkAppealInfoMap = new HashMap<>();
        List<Long> classInfoIds = dutyRecords.stream().filter(s -> CheckClassInfoStatusEnum.ENTER.getCode().equals(s.getCheckStatus())).map(DutyRecord::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(classInfoIds)) {
            List<CheckAppealInfo> classInfoAppealInfos = checkAppealInfoManager.list(new LambdaQueryWrapper<CheckAppealInfo>()
                    .in(CheckAppealInfo::getCheckClassInfoId, classInfoIds)
                    .eq(CheckAppealInfo::getAppealStatus, CheckAppealStatusEnum.NEED_APPROVAL.getCode()));
            checkAppealInfoMap = classInfoAppealInfos.stream().collect(Collectors.toMap(CheckAppealInfo::getCheckClassInfoId, Function.identity()));
        }

        Map<Long, CheckAppealInfo> finalCheckAppealInfoMap = checkAppealInfoMap;
        dutyRecords.forEach(s -> {
            if (Objects.nonNull(finalCheckAppealInfoMap.get(s.getId()))) {
                s.setCheckStatus(CheckClassInfoStatusEnum.APPEALING.getCode());
            }
        });
        // 设置值日记录
        classDutyDetail.setDutyRecords(dutyRecords);
    }

    /**
     * 校验权限
     *
     * @param classDutyDetail 需要封装德数据
     * @param classId         班级id
     */
    private Boolean checkRoleAuth(ClassDutyDetailVO classDutyDetail, Long classId) {
        // 角色权限判断
        ResRoleQueryDTO resRoleQueryDTO = new ResRoleQueryDTO();
        resRoleQueryDTO.setStaffIds(CollUtil.newArrayList(WebUtil.getStaffIdLong()));
        resRoleQueryDTO.setId(WebUtil.getSchoolIdLong());
        resRoleQueryDTO.setType(SaasOrgQueryEnum.SCHOOL_ID.getCode());
        List<ResStaffRoleVO> resStaffRoleVOList = basicInfoRemote.queryStaffRoleList(resRoleQueryDTO);
        Assert.notEmpty(resStaffRoleVOList, () -> new BizException("无权限,请联系管理员"));
        ResStaffRoleVO staffRoleVO = CollUtil.getFirst(resStaffRoleVOList);
        Assert.notEmpty(staffRoleVO.getRoles(), () -> new BizException("无权限,请联系管理员"));
        List<String> roleCodes = staffRoleVO.getRoles().stream().map(ResRoleInfoPojo::getRoleCode).distinct().collect(Collectors.toList());
        Assert.isTrue(roleLogic.isHaveCheckMaterRole(roleCodes), () -> new BizException("无管理者权限"));
        log.info("[班级排行]-[当前用户角色信息:{}]-[当前用户staffId:{}]", roleCodes, WebUtil.getStaffId());


        // 如果只有一个角色,且该角色是班主任
        if (roleLogic.isHaveCheckChargeRole(roleCodes)) {
            if (CollUtil.isEmpty(listMasterCurrentClass())) {
                log.info("[班级排行]-[当前用户为班主任角色]-[未查询到相关班级-直接返回]-[当前用户staffId:{}]", WebUtil.getStaffId());
                return Boolean.FALSE;
            }
            // 如果不传班级id,且角色为班主任(存在多个班级随机返回一个班级)
            if (Objects.isNull(classId)) {
                EduStaffMasterClassVO masterClassVO = CollUtil.getFirst(listMasterCurrentClass());
                classDutyDetail.setClassId(Convert.toStr(masterClassVO.getClassId()));
                classDutyDetail.setClassName(masterClassVO.getClassName());
            } else {
                // 查询某个具体班级
                EduStaffMasterClassVO masterClassVO = listMasterCurrentClass().stream().filter(s -> s.getClassId().equals(classId)).findFirst().orElse(null);
                if (Objects.isNull(masterClassVO)) {
                    log.info("[班级排行]-[当前用户为班主任角色]-[未查询到相关班级-直接返回]-[当前用户staffId:{}]", WebUtil.getStaffId());
                    return Boolean.FALSE;
                }
                classDutyDetail.setClassId(Convert.toStr(masterClassVO.getClassId()));
                classDutyDetail.setClassName(masterClassVO.getClassName());
            }
        } else {
            // 如果是管理员,查询具体某个班级
            ClassGradeInfoDTO classGradeInfo = saasGeneralManager.getClassGradeInfo(Convert.toStr(classId));
            if (Objects.isNull(classGradeInfo)) {
                log.info("[班级排行]-[当前用户为管理员角色]-[未查询到相关班级-直接返回]-[当前用户staffId:{}]", WebUtil.getStaffId());
                return Boolean.FALSE;
            }
            classDutyDetail.setClassId(Convert.toStr(classId));
            classDutyDetail.setClassName(classGradeInfo.getClassName());
        }
        return Boolean.TRUE;
    }

    /**
     * 获取值日日期范围
     *
     * @param classDutyDetail 值日详情
     */
    private void getCheckDateRange(ClassDutyDetailVO classDutyDetail) {

        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentIdType(2);
        eduOrgQueryDTO.setCurrentId(WebUtil.getCampusIdLong());
        eduOrgQueryDTO.setEndType(3);
        eduOrgQueryDTO.setIsTree(0);
        List<EduOrgTreeVO> eduOrgTrees = cacheSaasManager.queryEducationalOrgTree(eduOrgQueryDTO);
        // 学段id集合
        List<Long> campusSectionIds = eduOrgTrees.stream().filter(s -> s.getType().equals(3)).map(EduOrgTreeVO::getId).collect(Collectors.toList());

        // 查询不同学段下匹配的学期
        List<TermVo> termVoList = new ArrayList<>();
        for (Long campusSectionId : campusSectionIds) {
            // 获取学期
            TermQuery termQuery = new TermQuery();
            termQuery.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
            termQuery.setCampusId(Convert.toLong(WebUtil.getCampusId()));
            termQuery.setCampusSectionId(Convert.toStr(campusSectionId));
            List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
            List<TermVo> vos = termVos.stream().filter(TermVo::isCurrentTerm).collect(Collectors.toList());
            termVoList.addAll(vos);
        }

        // 没有一个学段的学期匹配,则取当年一月一号
        if (CollUtil.isEmpty(termVoList)) {
            classDutyDetail.setStartTime(DateUtil.beginOfYear(new Date()));
            classDutyDetail.setEndTime(new Date());
            // 时间范围限制
            classDutyDetail.setStartLimitTime(DateUtil.beginOfYear(new Date()));
            classDutyDetail.setEndLimitTime(new Date());
        } else {
            // 获取最早的学期
            TermVo earlyTerm = termVoList.stream().min(Comparator.comparing(TermVo::getStartTime)).orElse(null);
            if (Objects.nonNull(earlyTerm)) {
                classDutyDetail.setStartTime(DateUtil.parse(earlyTerm.getStartTime(), "yyyy-MM-dd"));
                classDutyDetail.setEndTime(DateUtil.parse(earlyTerm.getEndTime(), "yyyy-MM-dd"));
                // 时间范围限制
                classDutyDetail.setStartLimitTime(DateUtil.parse(earlyTerm.getStartTime(), "yyyy-MM-dd"));
                classDutyDetail.setEndLimitTime(DateUtil.parse(earlyTerm.getEndTime(), "yyyy-MM-dd"));
            }
        }


        // 判断是否存在流动红旗,默认为最近一次红旗颁发结束日期后一天至今
        CheckClassAwardRecord classAwardRecordManagerOne = checkClassAwardRecordManager.getOne(new LambdaQueryWrapper<CheckClassAwardRecord>()
                .eq(CheckClassAwardRecord::getTenantId, WebUtil.getTenantId())
                .eq(CheckClassAwardRecord::getSchoolId, WebUtil.getSchoolId())
                .eq(CheckClassAwardRecord::getCampusId, WebUtil.getCampusId())
                .eq(CheckClassAwardRecord::getClassId, classDutyDetail.getClassId())
                .orderByDesc(CheckClassAwardRecord::getId)
                .last("limit 1"));
        if (Objects.nonNull(classAwardRecordManagerOne)) {
            CheckAwardRecord checkAwardRecord = checkAwardRecordManager.getById(classAwardRecordManagerOne.getAwardId());
            if (Objects.nonNull(checkAwardRecord)) {
                classDutyDetail.setStartTime(DateUtil.beginOfDay(DateUtil.offsetDay(checkAwardRecord.getEndTime(), 1)));
                classDutyDetail.setEndTime(null);
            }
        }
    }


    /**
     * 获取当前用户担任班主任的班级集合
     *
     * @return
     */
    @Override
    public List<EduStaffMasterClassVO> listMasterCurrentClass() {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.start("[查询当前老师所担任的班主任]");

        EduStaffClassQueryDTO eduStaffClassQueryDTO = new EduStaffClassQueryDTO();
        eduStaffClassQueryDTO.setStaffIds(CollUtil.newArrayList(Convert.toLong(WebUtil.getStaffId())));
        eduStaffClassQueryDTO.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
        eduStaffClassQueryDTO.setGraduationStatus("0");
        eduStaffClassQueryDTO.setClassTypes(CollUtil.newArrayList("0"));
        List<EduStaffClassVO> eduStaffClassList = basicInfoRemote.queryStaffTeachInfo(eduStaffClassQueryDTO);
        log.info("[查询当前老师所担任的班主任]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.interval("[查询当前老师所担任的班主任]"));

        EduStaffClassVO eduStaffClass = eduStaffClassList.stream().findFirst().orElse(null);
        if (Objects.isNull(eduStaffClass)) {
            return Collections.emptyList();
        }
        List<EduStaffMasterClassVO> leaderClassInfos = eduStaffClass.getLeaderClassInfos();
        if (CollUtil.isEmpty(leaderClassInfos)) {
            return Collections.emptyList();
        }
        List<Long> classIds = leaderClassInfos.stream().map(EduStaffMasterClassVO::getClassId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(classIds)) {
            return Collections.emptyList();
        }

        // 过滤出当前校区的班级
        List<EduClassInfoLinkVO> eduClassInfos = basicInfoRemote.listUnderByClassIds(classIds);
        log.info("[根据班级id集合查询班级的信息]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.interval("[查询当前老师所担任的班主任]"));

        List<EduStaffMasterClassVO> collect = eduClassInfos.stream().filter(s -> WebUtil.getCampusIdLong().equals(s.getCampusId())).map(s -> {
            EduStaffMasterClassVO eduStaffMasterClassVO = new EduStaffMasterClassVO();
            eduStaffMasterClassVO.setClassId(s.getId());
            eduStaffMasterClassVO.setClassName(s.getClassName());
            return eduStaffMasterClassVO;
        }).collect(Collectors.toList());

        return collect;
    }

    /**
     * 获取学校的学生从主任信息
     *
     * @param schoolId 学校id
     * @return
     */
    @Override
    public Set<StaffVO> getStudentDirectors(Long schoolId) {
        TcOrgQueryDTO tcOrgQueryDTO = new TcOrgQueryDTO();
        tcOrgQueryDTO.setId(schoolId);
        tcOrgQueryDTO.setType(2);
        List<ResRoleBffVO> resRoleBffVOList = basicInfoRemote.queryOrgRoleList(tcOrgQueryDTO);
        // 过滤出学生处主任角色信息
        List<ResRoleBffVO> roleInfos = resRoleBffVOList.stream().filter(s -> "xs1010".equals(s.getRoleCode())).collect(Collectors.toList());
        if (CollUtil.isEmpty(roleInfos)) {
            return new HashSet<>();
        }
        Set<Long> roleIds = roleInfos.stream().map(ResRoleBffVO::getRoleId).distinct().collect(Collectors.toSet());
        Set<StaffVO> list = new HashSet<>();
        for (Long roleId : roleIds) {
            StaffRoleQueryDTO staffRoleQueryDTO = new StaffRoleQueryDTO();
            staffRoleQueryDTO.setRoleId(roleId);
            List<StaffVO> staffVOS = basicInfoRemote.queryStaffListByRole(staffRoleQueryDTO);
            list.addAll(staffVOS);
        }
        return list;
    }


    /**
     * 获取检查维度列表
     *
     * @return
     */
    @Override
    public List<CheckDimVO> listCheckDim(String campusId) {
        AssertUtil.checkNotBlank(campusId, "校区id不能为空");
        ListCheckDimBaseInfoDTO query = new ListCheckDimBaseInfoDTO()
                .setCampusId(campusId)
                .setSourceType(SourceTypeEnum.LDHQ.getCode())
                .setContainsItemFlag(Boolean.FALSE)
                .setIsDefault(Constant.YES);
        List<CheckDimBaseInfoVO> dimBaseInfoList = checkDimService.queryCheckDimBaseInfoList(query);
        if (CollUtil.isEmpty(dimBaseInfoList)) {
            return Collections.emptyList();
        }

        return dimBaseInfoList.stream().map(item -> new CheckDimVO().setDimId(item.getCheckDimId()).setDimName(item.getCheckDimName())).collect(Collectors.toList());
    }


}
