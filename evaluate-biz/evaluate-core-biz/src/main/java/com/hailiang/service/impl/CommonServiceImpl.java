package com.hailiang.service.impl;


import static com.hailiang.enums.SysSwitchConfigEnum.CLASS_EVALUATION;
import static com.hailiang.enums.SysSwitchConfigEnum.MORAL_ACTIVITY;
import static com.hailiang.enums.SysSwitchConfigEnum.MORAL_SCHOOL_FACE;
import static com.hailiang.enums.SysSwitchConfigEnum.STUDENT_EVALUATION;
import static com.hailiang.enums.SysSwitchConfigEnum.STUDENT_MOTIVATION;
import static com.hailiang.enums.SysSwitchConfigEnum.STUDENT_PORTRAIT;
import static com.hailiang.enums.SysSwitchConfigEnum.XWL_SCHOOL_FACE;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.google.common.collect.Lists;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.constant.SaasConstant;
import com.hailiang.enums.HeaderSourceEnum;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.enums.SaasEndTypeEnum;
import com.hailiang.enums.SysSwitchConfigEnum;
import com.hailiang.exception.BizException;
import com.hailiang.internal.model.response.SaasActiveResponse;
import com.hailiang.logic.RoleLogic;
import com.hailiang.manager.SysSwitchConfigManager;
import com.hailiang.model.datastatistics.vo.CommonMobileVO;
import com.hailiang.model.datastatistics.vo.ZheLiBanAppVO;
import com.hailiang.model.dto.DataAuthDTO;
import com.hailiang.model.dto.ProvinceCityAreaDTO;
import com.hailiang.model.dto.ProvinceCityAreaQueryDTO;
import com.hailiang.model.dto.SportDataUserTestSchoolQueryDTO;
import com.hailiang.model.dto.request.CorpIdClassExchangeRequest;
import com.hailiang.model.dto.request.SchoolSwitchConfigRequest;
import com.hailiang.model.dto.response.CorpIdClassExchangeResponse;
import com.hailiang.model.entity.SysSwitchConfigPO;
import com.hailiang.model.response.common.ProvinceSchoolCampusResponse;
import com.hailiang.model.vo.CodeNameVO;
import com.hailiang.model.vo.ListOrgIdAndRoleIdVO;
import com.hailiang.mp.commonsource.api.PageResult;
import com.hailiang.remote.hai.HaiRemoteHelper;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.DataAuthService;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.login.LoginDTO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.saas.SaasSchoolCacheManager;
import com.hailiang.saas.SaasSchoolManager;
import com.hailiang.saas.model.dto.school.AppIdBaseReq;
import com.hailiang.saas.model.dto.school.SchoolIdsRequest;
import com.hailiang.saas.model.dto.school.TchSchoolFullQuery;
import com.hailiang.saas.model.vo.CampusH5ConfigVO;
import com.hailiang.saas.model.vo.school.SchoolBaseResponse;
import com.hailiang.saas.model.vo.school.SchoolBaseVO;
import com.hailiang.saas.model.vo.school.SchoolCampusPojo;
import com.hailiang.saas.model.vo.school.SchoolCampusResponse;
import com.hailiang.saas.model.vo.school.SchoolFullInfoVO;
import com.hailiang.saas.model.vo.school.TchSchoolFullVO;
import com.hailiang.saas.sync.SyncClient;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.CommonService;
import com.hailiang.util.RSAUtil;
import com.hailiang.util.WebUtil;
import com.saas.syncer.data.client.dto.ExchangeIdDto;
import com.saas.syncer.data.client.qry.BodyWrapper;
import com.saas.syncer.data.client.qry.ExchangeIdQry;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


/**
 * @Author: JJL
 * @Date: 2023/5/16 9:57
 */
@Service
@Slf4j
@RefreshScope
public class CommonServiceImpl implements CommonService {
    private final static String MODULE_NAME = "公共接口";
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private HaiRemoteHelper haiRemoteHelper;
    @Autowired
    private BasicInfoService basicInfoService;
    @Autowired
    private BasicInfoRemote basicInfoRemote;
    @Autowired
    private RoleLogic roleLogic;
    @Autowired
    private SaasSchoolCacheManager saasSchoolCacheManager;
    @Autowired
    private CacheSaasManager cacheSaasManager;
    @Resource
    private DataAuthService dataAuthService;
    @Resource
    private SaasSchoolManager saasSchoolManager;
    @Resource
    private SysSwitchConfigManager sysSwitchConfigManager;
    @Resource
    private SyncClient syncClient;
    @Value("${evaluate.appId}")
    private String appId;

    /**
     * 用户id
     */
    @Value("${evaluate.xueZaiZheJiang.teacher.staffId}")
    private String staffId;
    /**
     * 学校id
     */
    @Value("${evaluate.xueZaiZheJiang.teacher.schoolId}")
    private String schoolId;
    /**
     * 校区id
     */
    @Value("${evaluate.xueZaiZheJiang.teacher.campusId}")
    private String campusId;
    /**
     * 租户id
     */
    @Value("${evaluate.xueZaiZheJiang.teacher.tenantId}")
    private String tenantId;
    /**
     * 用户id
     */
    @Value("${evaluate.xueZaiZheJiang.teacher.userId}")
    private String userId;
    /**
     * 学号
     */
    @Value("${evaluate.xueZaiZheJiang.parent.studentCode}")
    private String studentCode;
    /**
     * 学生名字
     */
    @Value("${evaluate.xueZaiZheJiang.parent.studentName}")
    private String studentName;
    /**
     * 手机号
     */
    @Value("${evaluate.xueZaiZheJiang.parent.mobile}")
    private String mobile;
    /**
     * 公钥
     */
    @Value("${evaluate.xueZaiZheJiang.saasToken.publicKey}")
    private String publicKey;
    /**
     * 账号
     */
    @Value("${evaluate.xueZaiZheJiang.saasToken.userName}")
    private String userName;
    /**
     * 密码
     */
    @Value("${evaluate.xueZaiZheJiang.saasToken.password}")
    private String password;
    @Value("${evaluate.passSchoolId}")
    private List<Long> passSchoolIds;

    /**
     * 获取token和学校header信息
     *
     * @param type       1:教师端 2：家长端
     * @param sourceFrom 学在浙江（浙里办应用名称）
     * @return
     */
    @Override
    public ZheLiBanAppVO getZheLiBanToken(Integer type, String sourceFrom) {
        log.info("访问模块：[{}],请求参数：[{}]", MODULE_NAME + "-获取token和学校header信息", sourceFrom);
        if (!Constant.ONE.equals(type) && !Constant.TWO.equals(type)) {
            return null;
        }
        ZheLiBanAppVO zheLiBanAppVO = new ZheLiBanAppVO();
        //家长端
        if (Constant.TWO.equals(type)) {
            zheLiBanAppVO.setStudentCode(studentCode);
            zheLiBanAppVO.setStudentName(studentName);
            zheLiBanAppVO.setMobile(mobile);
            return zheLiBanAppVO;
        }
        //教师端
        zheLiBanAppVO.setSchoolId(schoolId);
        zheLiBanAppVO.setCampusId(campusId);
        zheLiBanAppVO.setTenantId(tenantId);
        zheLiBanAppVO.setStaffId(staffId);
        zheLiBanAppVO.setUserId(userId);
        LoginDTO loginDTO = new LoginDTO();
        loginDTO.setEndpoint("WEB_BROWSER");
        loginDTO.setChannel("PASSWORD");
        loginDTO.setPassword(RSAUtil.encrypt(password, publicKey));
        loginDTO.setUserName(userName);
        loginDTO.setAppType("pass");
        //获取token并放入缓存
        String token = redisUtil.getOrAdd(RedisKeyConstants.COMMON_TOKEN + sourceFrom,
                () -> basicInfoRemote.doLogin(loginDTO).getAuthenticationResource().getAccessToken(),
                CacheConstants.TWELVE_HOUR);
        zheLiBanAppVO.setToken(token);
        return zheLiBanAppVO;
    }

    /**
     * 根据token解析手机号
     *
     * @param request
     * @return
     */
    @Override
    public CommonMobileVO getMobileByToken(HttpServletRequest request) {
        String authorization = request.getHeader(SaasConstant.AUTHORIZATION);
        String source = request.getHeader(SaasConstant.SOURCE);
        //家长端toke解析不通过hai家校
        if (HeaderSourceEnum.PARENTS.getCode().equals(source)){
            CommonMobileVO commonMobileVO = new CommonMobileVO();
            JWT jwt = JWTUtil.parseToken(authorization);
            String mobile = (String) jwt.getPayload().getClaimsJson().get("username");
            commonMobileVO.setMobile(mobile);
            return commonMobileVO;
        }
        JSONObject jsonObject = haiRemoteHelper.authCheck(authorization);
        if (JSONUtil.isNull(jsonObject) || JSONUtil.isNull(jsonObject.getJSONObject("data")) || JSONUtil.isNull(jsonObject.getJSONObject("data").getJSONObject("userInfo"))) {
            log.warn("Hai家校获取tokens失败,完整返回信息：{}", JSONUtil.toJsonStr(jsonObject));
            return new CommonMobileVO();
        }
        String mobile = jsonObject.getJSONObject("data").getJSONObject("userInfo").getStr("mobile");
        if (StrUtil.isBlank(mobile)) {
            log.warn("Hai家校获取tokens失败,家长手机号为空");
            return new CommonMobileVO();
        }
        CommonMobileVO commonMobileVO = new CommonMobileVO();
        commonMobileVO.setMobile(mobile);
        return commonMobileVO;
    }

    /**
     * 是否包含产品运营角色
     *
     * @return
     */
    @Override
    public Boolean isHaveRunnerRole() {
        //权限
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = basicInfoService.listOrgIdAndRoleId();
        List<String> roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        //是否有产品运营角色
        return roleLogic.isHaveRunnerRole(roleCodeList);

    }

    /**
     * 开通综评应用学校的省市区
     *
     * @return
     */
    @Override
    public List<CodeNameVO> listArea(SportDataUserTestSchoolQueryDTO dto) {

        //是否展示测试学校
        Integer useTestSchool = dto.getUseTestSchool();
        List<CodeNameVO> codeNameVOS = new ArrayList<>();

        //获取开通综评的学校列表
        List<TchSchoolFullVO> tchSchoolFullVOList = listTchSchoolFullVOs(useTestSchool);

        if (CollectionUtils.isEmpty(tchSchoolFullVOList)) {
            log.warn("开通综评应用的学校为空");
            return codeNameVOS;
        }

        //按省市区分组
        Map<String, Map<String, Map<String, List<TchSchoolFullVO>>>> map = tchSchoolFullVOList.stream().filter(s -> StrUtil.isNotBlank(s.getProvinceCode())
                && StrUtil.isNotBlank(s.getCityCode())
                && StrUtil.isNotBlank(s.getAreaCode())
        ).collect(
                Collectors.groupingBy(TchSchoolFullVO::getProvinceCode,
                        Collectors.groupingBy(TchSchoolFullVO::getCityCode,
                                Collectors.groupingBy(TchSchoolFullVO::getAreaCode))
                )
        );

        //转为树形结构
        toTree(map, tchSchoolFullVOList, codeNameVOS);
        return codeNameVOS;
    }

    /**
     * 获取开通综评的学校列表的省份
     *
     * @param userTestSchool
     * @return
     */
    private List<TchSchoolFullVO> listTchSchoolFullVOs(Integer userTestSchool) {
        AppIdBaseReq appIdBaseReq = new AppIdBaseReq();
        appIdBaseReq.setAppId(Convert.toLong(appId));
        //开通综评应用的学校
        List<SchoolFullInfoVO> schoolFullInfoVOS = saasSchoolCacheManager.querySchoolByAppId(appIdBaseReq);
        if (CollUtil.isEmpty(schoolFullInfoVOS)) {
            log.info("开通综评应用的学校为空");
            return null;
        }
        //学校 ids
        List<Long> schoolIds = schoolFullInfoVOS.stream().map(SchoolFullInfoVO::getSchoolId).collect(Collectors.toList());
        //去掉测试学校
        if (Constant.ZERO.equals(userTestSchool)) {
            schoolIds.removeAll(passSchoolIds);
        }
        Set<Long> schoolIdsSet = Convert.toSet(Long.class, schoolIds);
        TchSchoolFullQuery tchSchoolFullQuery = new TchSchoolFullQuery();
        tchSchoolFullQuery.setSchoolIds(schoolIdsSet);
        tchSchoolFullQuery.setNeedTotalSize(true);
        tchSchoolFullQuery.setPageSize(schoolIdsSet.size());
        //查询省市区
        PageResult<TchSchoolFullVO> tchSchoolFullVOPage = saasSchoolCacheManager.queryBySchoolIds(tchSchoolFullQuery);
        List<TchSchoolFullVO> tchSchoolFullVOList = Convert.toList(TchSchoolFullVO.class, tchSchoolFullVOPage.getList());
        return tchSchoolFullVOList;
    }

    private static void toTree(Map<String, Map<String, Map<String, List<TchSchoolFullVO>>>> map, List<TchSchoolFullVO> tchSchoolFullVOList, List<CodeNameVO> codeNameVOS) {
        for (Map.Entry<String, Map<String, Map<String, List<TchSchoolFullVO>>>> entry : map.entrySet()) {
            List<CodeNameVO> cityChildren = new ArrayList<>();
            CodeNameVO codeNameVO = new CodeNameVO();
            codeNameVO.setCode(entry.getKey());
            codeNameVO.setName(tchSchoolFullVOList.stream().filter(s -> entry.getKey().equals(s.getProvinceCode())).findFirst().get().getProvinceName());

            for (Map.Entry<String, Map<String, List<TchSchoolFullVO>>> cityEntry : entry.getValue().entrySet()) {
                List<CodeNameVO> areaChildren = new ArrayList<>();
                CodeNameVO cityCodeNameVO = new CodeNameVO();
                cityCodeNameVO.setParentCode(entry.getKey());
                cityCodeNameVO.setCode(cityEntry.getKey());
                cityCodeNameVO.setName(tchSchoolFullVOList.stream().filter(s -> cityEntry.getKey().equals(s.getCityCode())).findFirst().get().getCityName());

                for (Map.Entry<String, List<TchSchoolFullVO>> areaEntry : cityEntry.getValue().entrySet()) {
                    CodeNameVO areaCodeNameVO = new CodeNameVO();
                    areaCodeNameVO.setParentCode(cityEntry.getKey());
                    areaCodeNameVO.setCode(areaEntry.getKey());
                    areaCodeNameVO.setName(tchSchoolFullVOList.stream().filter(s -> areaEntry.getKey().equals(s.getAreaCode())).findFirst().get().getAreaName());
                    areaChildren.add(areaCodeNameVO);
                }
                cityCodeNameVO.setChildren(areaChildren);
                cityChildren.add(cityCodeNameVO);
            }

            codeNameVO.setChildren(cityChildren);
            codeNameVOS.add(codeNameVO);
        }
    }


    /**
     * 根据省市区查询所有学校和校区
     *
     * @return
     */
    @Override
    public List<EduOrgTreeVO> listSchoolByArea(ProvinceCityAreaQueryDTO dto) {
        //是否展示测试学校
        Integer useTestSchool = dto.getUseTestSchool();

        List<EduOrgTreeVO> eduOrgTreeVOList = new ArrayList<>();

        //获取开通综评的学校列表
        List<TchSchoolFullVO> tchSchoolFullVOList = listTchSchoolFullVOs(useTestSchool);
        if (CollectionUtils.isEmpty(tchSchoolFullVOList)) {
            log.info("开通综评应用的学校为空");
            return null;
        }

        List<ProvinceCityAreaDTO> provinceCityAreaDTOList = dto.getProvinceCityAreaDTOList();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("获取所有学校和校区");
        //全部区域
        if (BeanUtil.isEmpty(dto) || CollUtil.isEmpty(provinceCityAreaDTOList)) {
            //获取所有学校和校区
            listSchoolCampus(tchSchoolFullVOList, eduOrgTreeVOList);
            stopWatch.stop();
            log.info("获取所有学校和校区耗时：{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
            return eduOrgTreeVOList;
        }

        //过滤省市区
        List<TchSchoolFullVO> tchSchoolFullVOs = new ArrayList<>();
        for (ProvinceCityAreaDTO provinceCityAreaDTO : provinceCityAreaDTOList) {
            if (StrUtil.isNotBlank(provinceCityAreaDTO.getAreaCode())) {
                tchSchoolFullVOs.addAll(tchSchoolFullVOList.stream().filter(s -> provinceCityAreaDTO.getAreaCode().equals(s.getAreaCode())).collect(Collectors.toList()));
                continue;
            }
            if (StrUtil.isNotBlank(provinceCityAreaDTO.getCityCode())) {
                tchSchoolFullVOs.addAll(tchSchoolFullVOList.stream().filter(s -> provinceCityAreaDTO.getCityCode().equals(s.getCityCode())).collect(Collectors.toList()));
                continue;
            }
            if (StrUtil.isNotBlank(provinceCityAreaDTO.getProvinceCode())) {
                tchSchoolFullVOs.addAll(tchSchoolFullVOList.stream().filter(s -> provinceCityAreaDTO.getProvinceCode().equals(s.getProvinceCode())).collect(Collectors.toList()));
            }
        }

        //获取校区
        listSchoolCampus(tchSchoolFullVOs, eduOrgTreeVOList);
        stopWatch.stop();
        log.info("获取所有学校和校区耗时：{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return eduOrgTreeVOList;
    }

    @Override
    public Boolean getIsAdmin() {
        //督导
        DataAuthDTO dataAuth = dataAuthService.getDataAuth();
        if (BeanUtil.isNotEmpty(dataAuth) && dataAuth.getIsAdmin()) {
            return true;
        }
        return false;
    }

    /**
     * 开通综评应用的省-学校-校区
     *
     * @return
     */
    @Override
    public List<ProvinceSchoolCampusResponse> listCampus() {
        //获取开通综评的学校列表的省份
        List<TchSchoolFullVO> tchSchoolFullVOS = this.listTchSchoolFullVOs(Constant.ONE);
        if (CollUtil.isEmpty(tchSchoolFullVOS)) {
            log.info("开通综评应用的学校省份为空，appId：[{}]", appId);
            return Collections.emptyList();
        }

        //学校 ids
        List<Long> schoolIds = tchSchoolFullVOS.stream().map(TchSchoolFullVO::getId).collect(Collectors.toList());
        List<SchoolBaseResponse> schoolBaseResponses = new ArrayList<>();
        List<List<Long>> partition = Lists.partition(schoolIds, Constant.FIVE_HUNDRED);
        //获取校区
        for (List<Long> longs : partition) {
            SchoolIdsRequest schoolIdsRequest = new SchoolIdsRequest();
            schoolIdsRequest.setSchoolIds(longs);
            List<SchoolBaseVO> schoolBaseVOS = saasSchoolManager.querySchoolCampusListBySchoolId(schoolIdsRequest);
            for (SchoolBaseVO schoolBaseVO : schoolBaseVOS) {
                SchoolBaseResponse schoolBaseResponse = this.convertToResponse(schoolBaseVO);
                schoolBaseResponses.add(schoolBaseResponse);
            }
        }


        //按省份分组
        Map<String, List<TchSchoolFullVO>> provinceMap = CollStreamUtil.groupByKey(tchSchoolFullVOS, TchSchoolFullVO::getProvinceCode);
        provinceMap.remove(null);

        //按省份分组 获取学校校区
        List<ProvinceSchoolCampusResponse> responses = provinceMap.values().stream()
                .map(provinceVOs -> {
                    Optional<TchSchoolFullVO> firstVO = provinceVOs.stream().findFirst();
                    if (!firstVO.isPresent()) {
                        return null; // 或者返回一个空的ProvinceSchoolCampusResponse对象
                    }
                    ProvinceSchoolCampusResponse response = new ProvinceSchoolCampusResponse();
                    response.setProvinceCode(firstVO.get().getProvinceCode());
                    response.setProvinceName(firstVO.get().getProvinceName());
                    //获取校区

                    List<Long> schoolIdList = provinceVOs.stream().map(TchSchoolFullVO::getId).distinct().collect(Collectors.toList());
                    List<SchoolBaseResponse> baseResponses = schoolBaseResponses.stream().filter(s -> schoolIdList.contains(s.getId())).collect(Collectors.toList());


//                    this.listSchoolCampus(provinceVOs, eduOrgTreeVOList);
                    if (CollUtil.isEmpty(baseResponses)) {
                        return null; // 或者返回一个空的ProvinceSchoolCampusResponse对象
                    }
                    response.setChildren(baseResponses);
                    return response;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return responses;
    }


    /**
     * 校验学校开关状态
     */
    @Override
    public Boolean checkSwitch(SchoolSwitchConfigRequest request) {
        // 判断是否可以人脸识别以及校验
        SysSwitchConfigPO sysSwitchConfig = sysSwitchConfigManager
                .getBySchoolId(WebUtil.getSchoolId(), request.getType());
        if (BeanUtil.isEmpty(sysSwitchConfig) || Constant.NO.equals(sysSwitchConfig.getStatus())) {
            return false;
        }
        return true;
    }

    @Override
    public List<CampusH5ConfigVO> listCampusH5ConfigByCampusId(Long campusId) {
        List<SysSwitchConfigPO> sysSwitchConfigPOS = sysSwitchConfigManager.listByCampusId(Convert.toStr(campusId));

        List<CampusH5ConfigVO> campusH5ConfigVOS = new ArrayList<>();

        Map<Integer, SysSwitchConfigPO> campusMap = sysSwitchConfigPOS.stream().collect(Collectors.toMap(SysSwitchConfigPO::getBusinessType, Function.identity()));

        CampusH5ConfigVO campusH5ConfigVO = new CampusH5ConfigVO();



        if(CollUtil.isEmpty(sysSwitchConfigPOS)){
            campusH5ConfigVO.setCampusId(campusId);
            campusH5ConfigVO.setEnableStudentMotivation(1);
            campusH5ConfigVO.setEnableStudentEvaluation(1);
            campusH5ConfigVO.setEnableClassEvaluation(1);
            campusH5ConfigVO.setEnableStudentPortrait(1);
            campusH5ConfigVO.setEnableMoralActivity(1);
            campusH5ConfigVOS.add(campusH5ConfigVO);
            return campusH5ConfigVOS;
        }

        for (SysSwitchConfigEnum sysSwitchConfigEnum : SysSwitchConfigEnum.values()) {
            if (Objects.equals(sysSwitchConfigEnum.getCode(), MORAL_SCHOOL_FACE.getCode())
                    || Objects.equals(sysSwitchConfigEnum.getCode(), XWL_SCHOOL_FACE.getCode())) {
                continue;
            }

            campusH5ConfigVO.setCampusId(campusId);
            Integer key = sysSwitchConfigEnum.getCode();
            SysSwitchConfigPO sysSwitchConfigPO = campusMap.get(key);
            if (Objects.equals(sysSwitchConfigEnum.getCode(), STUDENT_EVALUATION.getCode())) {
                campusH5ConfigVO.setEnableStudentEvaluation(sysSwitchConfigPO == null
                        || Objects.equals(sysSwitchConfigPO.getStatus(), 1) ? 1 : 0);
            }

            if (Objects.equals(sysSwitchConfigEnum.getCode(), STUDENT_PORTRAIT.getCode())) {
                campusH5ConfigVO.setEnableStudentPortrait(sysSwitchConfigPO == null
                        || Objects.equals(sysSwitchConfigPO.getStatus(), 1) ? 1 : 0);
            }

            if (Objects.equals(sysSwitchConfigEnum.getCode(), CLASS_EVALUATION.getCode())) {
                campusH5ConfigVO.setEnableClassEvaluation(sysSwitchConfigPO == null
                        || Objects.equals(sysSwitchConfigPO.getStatus(), 1) ? 1 : 0);
            }

            if (Objects.equals(sysSwitchConfigEnum.getCode(), STUDENT_MOTIVATION.getCode())) {
                campusH5ConfigVO.setEnableStudentMotivation(sysSwitchConfigPO == null
                        || Objects.equals(sysSwitchConfigPO.getStatus(), 1) ? 1 : 0);
            }
            if (Objects.equals(sysSwitchConfigEnum.getCode(), MORAL_ACTIVITY.getCode())) {
                campusH5ConfigVO.setEnableMoralActivity(sysSwitchConfigPO == null
                        || Objects.equals(sysSwitchConfigPO.getStatus(), 1) ? 1 : 0);
            }
        }
        campusH5ConfigVOS.add(campusH5ConfigVO);


        return campusH5ConfigVOS;
    }
    /**
     * 转换corpId和classId
     * @param request
     * @return
     */
    @Override
    public CorpIdClassExchangeResponse exchangeCorpIdAndClassId(CorpIdClassExchangeRequest request) {
        CorpIdClassExchangeResponse corpIdClassExchangeResponse = new CorpIdClassExchangeResponse();
        String corpId = request.getCorpId();
        String classId = request.getClassId();
        Long tenantId = request.getTenantId();

        Assert.isTrue(StrUtil.isAllNotBlank(corpId, classId), () -> new BizException("参数不能为空"));

        ExchangeIdQry corpIdQry = new ExchangeIdQry();
        corpIdQry.setId(corpId);
        corpIdQry.setTenantId(tenantId);
        corpIdQry.setIdType("SCHOOL");
        corpIdQry.setSourceChannelType("DING_TALK");
        corpIdQry.setTargetChannelType("SAAS");

        ExchangeIdQry classIdQry = new ExchangeIdQry();
        classIdQry.setId(classId);
        classIdQry.setTenantId(tenantId);
        classIdQry.setIdType("CLASS");
        classIdQry.setSourceChannelType("DING_TALK");
        classIdQry.setTargetChannelType("SAAS");

        List<ExchangeIdQry> exchangeIdQryList = new ArrayList<>();
        exchangeIdQryList.add(corpIdQry);
        exchangeIdQryList.add(classIdQry);

        BodyWrapper<List<ExchangeIdQry>> bodyWrapper = new BodyWrapper<>();
        bodyWrapper.setBody(exchangeIdQryList);
        com.saas.syncer.common.response.CommonResult<List<ExchangeIdDto>> commonResult = syncClient.exchangeId(
                bodyWrapper);
        // 打印出参
        log.info("【获取exchange-id】----请求地址：/data-exchange/exchange-id, 请求参数: {}, 响应结果: {}", JSONUtil.toJsonStr(bodyWrapper), JSONUtil.toJsonStr(commonResult));
        Integer code = Convert.toInt(commonResult.getCode());
        if (ObjectUtil.notEqual(code, 200)) {
            log.error("【获取exchange-id】----获取海思谷ID失败,原因:{}", commonResult.getMessage());
            return corpIdClassExchangeResponse;
        }
        List<ExchangeIdDto> data = commonResult.getData();
        if (CollUtil.isNotEmpty(data)) {
            for (ExchangeIdDto exchangeIdDto : data) {
                if (Objects.equals(exchangeIdDto.getIdType(), "SCHOOL")) {
                    corpIdClassExchangeResponse.setSchoolId(exchangeIdDto.getTargetId());
                }
                if (Objects.equals(exchangeIdDto.getIdType(), "CLASS")) {
                    corpIdClassExchangeResponse.setClassId(exchangeIdDto.getTargetId());
                }
            }
            return corpIdClassExchangeResponse;
        }
        return corpIdClassExchangeResponse;
    }

    public SchoolBaseResponse convertToResponse(SchoolBaseVO vo) {
        SchoolBaseResponse response = new SchoolBaseResponse();
        response.setId(vo.getSchoolId());
        response.setName(vo.getSchoolName());

        // 转换校区集合
        if (vo.getCampusPojoList() != null) {
            List<SchoolCampusResponse> campusResponses = new ArrayList<>();
            for (SchoolCampusPojo campusPojo : vo.getCampusPojoList()) {
                campusResponses.add(convertToCampusResponse(campusPojo));
            }
            response.setChildren(campusResponses);
        }

        return response;
    }

    private SchoolCampusResponse convertToCampusResponse(SchoolCampusPojo pojo) {
        SchoolCampusResponse response = new SchoolCampusResponse();
        response.setId(pojo.getId());
        response.setName(pojo.getCampusName());
        return response;
    }

    /**
     * 根据学校id获取校区
     *
     * @param tchSchoolFullVOList
     * @param eduOrgTreeVOList
     */
    private void listSchoolCampus(List<TchSchoolFullVO> tchSchoolFullVOList, List<EduOrgTreeVO> eduOrgTreeVOList) {
        List<Long> schoolIds = tchSchoolFullVOList.stream().map(TchSchoolFullVO::getId).distinct().collect(Collectors.toList());
        for (Long id : schoolIds) {
            EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
            eduOrgQueryDTO.setCurrentId(id);
            eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SCHOOL.getCode());
            eduOrgQueryDTO.setEndType(SaasEndTypeEnum.CAMPUS.getCode());
            List<EduOrgTreeVO> eduOrgTreeVOS = cacheSaasManager.queryEducationalOrgTree(eduOrgQueryDTO);

            if (CollUtil.isNotEmpty(eduOrgTreeVOS) && CollUtil.isNotEmpty(eduOrgTreeVOS.get(0).getChildren())) {
                eduOrgTreeVOList.addAll(eduOrgTreeVOS);
            }
        }
    }

    @Override
    public SaasActiveResponse saasActiveConf(String code) {
        Long schoolId = WebUtil.getSchoolIdLong();
        return basicInfoRemote.saasActiveConf(code, schoolId);
    }
}
