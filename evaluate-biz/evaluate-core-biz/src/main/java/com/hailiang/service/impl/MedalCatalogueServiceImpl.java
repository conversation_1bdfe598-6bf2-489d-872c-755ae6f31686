package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hailiang.constant.Constant;
import com.hailiang.convert.MedalConvert;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.manager.MedalCatalogueManager;
import com.hailiang.manager.MedalInfoManager;
import com.hailiang.manager.MedalTaskManager;
import com.hailiang.model.entity.MedalCatalogue;
import com.hailiang.model.entity.MedalInfo;
import com.hailiang.model.entity.MedalTask;
import com.hailiang.model.medal.dto.MedalCatalogueEditDTO;
import com.hailiang.model.medal.dto.MedalCatalogueSaveDTO;
import com.hailiang.model.medal.vo.MedalCatalogueVO;
import com.hailiang.service.MedalCatalogueService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: JJL
 * @Date: 2023/5/31 14:28
 */
@Service
@Slf4j
public class MedalCatalogueServiceImpl implements MedalCatalogueService {

    @Autowired
    private MedalCatalogueManager medalCatalogueManager;
    @Resource
    private MedalConvert convert;
    @Autowired
    private MedalInfoManager medalInfoManager;
    @Autowired
    private MedalTaskManager medalTaskManager;
    /**
     * 模块名称
     */
    private final static String MODULE_NAME = "[学生争章-";

    /**
     * 新增章目
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMedalCatalogue(MedalCatalogueSaveDTO dto) {
        if(BeanUtil.isEmpty(dto)){
            return;
        }
        log.info(MODULE_NAME + "新增章目], 入参:{} ", JSONUtil.toJsonStr(dto));
        //校验该校区下章目名称是否重名
        MedalCatalogue medalCatalogue = medalCatalogueManager.getOne(new LambdaQueryWrapper<MedalCatalogue>()
                .eq(MedalCatalogue::getName, dto.getName())
                .eq(MedalCatalogue::getTenantId, WebUtil.getTenantId())
                .eq(MedalCatalogue::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalCatalogue::getCampusId, WebUtil.getCampusId()));
        Assert.isFalse(BeanUtil.isNotEmpty(medalCatalogue), () -> new BizException(BizExceptionEnum.MEDAL_CATALOGUE_EXIST.getMessage()));

        dto.setTenantId(WebUtil.getTenantId());
        dto.setSchoolId(WebUtil.getSchoolId());
        dto.setCampusId(WebUtil.getCampusId());
        //新增章目
        medalCatalogueManager.save(convert.toMedalCatalogue(dto));

    }

    /**
     * 删除章目
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMedalCatalogue(Long id) {
        if(ObjectUtil.isNull(id)){
            return;
        }
        log.info(MODULE_NAME + "删除章目], 入参:{} ", id);
        //逻辑删除
        medalCatalogueManager.removeById(id);
        //删除章目下的奖章
        List<MedalInfo> medalInfos = medalInfoManager.selectByCatalogueId(id);
        List<Long> ids = medalInfos.stream().map(MedalInfo::getId).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(ids)){
            return;
        }
        medalInfoManager.removeBatchByIds(ids);
        // 奖章对应的任务失效
        List<MedalTask> medalTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>()
                .eq(MedalTask::getTenantId, WebUtil.getTenantId())
                .eq(MedalTask::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalTask::getCampusId, WebUtil.getCampusId())
                .in(CollectionUtil.isNotEmpty(ids), MedalTask::getMedalInfoId, ids));
        if(CollectionUtil.isEmpty(medalTasks)){
            return;
        }
        medalTasks.stream().forEach(s -> {
            s.setStatus(Constant.ONE);
        });
        medalTaskManager.updateBatchById(medalTasks);
    }

    /**
     * 修改章目
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMedalCatalogue(MedalCatalogueEditDTO dto) {
        if(BeanUtil.isEmpty(dto)){
            return;
        }
        log.info(MODULE_NAME + "修改章目], 入参:{} ", JSONUtil.toJsonStr(dto));
        //校验该校区下章目名称是否重名
        MedalCatalogue medalCatalogue = medalCatalogueManager.getOne(new LambdaQueryWrapper<MedalCatalogue>()
                .eq(MedalCatalogue::getName, dto.getName())
                .ne(MedalCatalogue::getId, dto.getId())
                .eq(MedalCatalogue::getTenantId, WebUtil.getTenantId())
                .eq(MedalCatalogue::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalCatalogue::getCampusId, WebUtil.getCampusId()));
        Assert.isFalse(BeanUtil.isNotEmpty(medalCatalogue), () -> new BizException(BizExceptionEnum.MEDAL_CATALOGUE_EXIST.getMessage()));
        //修改章目
        medalCatalogueManager.updateById(convert.toEditMedalCatalogue(dto));

    }

    /**
     * 查询章目
     *
     * @return
     */
    @Override
    public List<MedalCatalogueVO> listMedalCatalogue() {
        List<MedalCatalogue> medalCatalogues = medalCatalogueManager.list(new LambdaQueryWrapper<MedalCatalogue>()
                .eq(MedalCatalogue::getTenantId, WebUtil.getTenantId())
                .eq(MedalCatalogue::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalCatalogue::getCampusId, WebUtil.getCampusId())
                .orderByAsc(MedalCatalogue::getCreateTime));
        return convert.toMedalCatalogueVO(medalCatalogues);
    }


}
