package com.hailiang.service;

import com.hailiang.model.dto.activity.rule.match.RuleBehaviourInfoDTO;
import com.hailiang.model.entity.MedalTaskRule;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/6 17:40
 */
public interface ActivityRuleMatchService {

    /**
     * 匹配活动规则
     *
     * @param behaviorRecordIds 行为记录id
     * @return
     */
    Boolean matchProcess(List<Long> behaviorRecordIds);

//    /**
//     * 匹配活动规则
//     *
//     * @param behaviorRecordIds 行为记录id(仅修复数据使用)
//     * @return
//     */
//    Boolean matchProcessFix(List<Long> behaviorRecordIds);


    /**
     * 匹配获取任务列表
     *
     * @param ruleBehaviour 行为记录信息
     * @return
     */
    List<MedalTaskRule> getMatchRuleTask(RuleBehaviourInfoDTO ruleBehaviour);
}
