package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.TargetCopyDTO;
import com.hailiang.model.dto.query.ListTargetWritePeopleDaoDTO;
import com.hailiang.model.dto.query.TargetIdQuery;
import com.hailiang.model.dto.response.SubjectCommonResponse;
import com.hailiang.model.dto.save.TargetSaveDTO;
import com.hailiang.model.dto.save.TargetSortDTO;
import com.hailiang.model.dto.target.TargetBatchOperateQueryDTO;
import com.hailiang.model.dto.target.TargetBatchOperateSaveDTO;
import com.hailiang.model.entity.Target;
import com.hailiang.model.qo.target.TargetQO;
import com.hailiang.model.request.StringRequest;
import com.hailiang.model.request.SubjectLikeRequest;
import com.hailiang.model.response.TargetReviewTeacherResponse;
import com.hailiang.model.response.target.batch.TargetBatchSaveResponse;
import com.hailiang.model.vo.SportGroupTargetVO;
import com.hailiang.model.vo.TargetDetailVO;
import com.hailiang.model.vo.TargetListTargetUserVO;
import com.hailiang.model.vo.TargetUserQueryVO;
import com.hailiang.model.vo.target.TargetBatchOperateVO;
import com.hailiang.remote.saas.dto.staff.SchoolStaffQueryDTO;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【evaluate_target(指标表)】的数据库操作Service
* @createDate 2022-12-29 11:08:23
*/
public interface TargetService extends IService<Target> {

    void save(TargetSaveDTO dto);

    void delete(Long targetId);

    void changeStatus(Long targetId, Integer opType);

    void sort(List<TargetSortDTO.InnerSort> sortList);

    TargetDetailVO detail(Long targetId);

    Target get(Long targetId);

    List<String> listIcon();

    List<TargetListTargetUserVO> listTargetWritePeople(ListTargetWritePeopleDaoDTO dto);

    String getIconUrlPrefix();

    Page<TargetUserQueryVO> queryStaffListBySchool(SchoolStaffQueryDTO schoolStaffQueryDTO);

    SportGroupTargetVO checkSportGroupAndTarget();

    boolean copy(TargetCopyDTO targetCopyDTO);

    List<Long> listTargetIdsByGroupId(Long groupId);

    /**
     * 根据指标ids批量列表查询指标
     *
     * @param targetIds
     * @return
     */
    List<Target> listByTargetIds(List<Long> targetIds);

    /**
     * 图文点评设置
     * @param query
     */
    void setPictureEvaluate(TargetIdQuery query);

    /**
     * 图文点评指标列表
     */
    List<Long> listPictureEvaluates();

    /**
     * 条件查询
     * @return
     */
    List<Target> queryByCondition(TargetQO targetQO);

    /**
     * 指标批量导入-根据五育模块指标回显
     * @param batchOperateQueryDTO
     * @return
     */
    List<TargetBatchOperateVO> batchOperateQueryByModule(TargetBatchOperateQueryDTO batchOperateQueryDTO);

    /**
     * 指标批量导入-导入操作
     * @param batchOperateSaveDTO
     * @return
     */
    TargetBatchSaveResponse batchOperateSaveTarget(TargetBatchOperateSaveDTO batchOperateSaveDTO);

    /**
     * 班级内的课程老师列表，支持模糊搜索
     */
    List<TargetReviewTeacherResponse> listClassTeacher(StringRequest request);

    /**
     * 检查指标是否为体测项目指标
     */
    Map<Long,Boolean> checkTargetFromSport(List<Long> targetIds);

    /**
     * 获取学校课程列表
     * @return
     */
    List<SubjectCommonResponse> listSubject(SubjectLikeRequest req);
}
