package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.DictDataDTO;
import com.hailiang.model.dto.DictTypeDTO;
import com.hailiang.model.entity.DictData;
import com.hailiang.model.vo.DictDataVO;
import com.hailiang.model.vo.DictTypeDataVO;

import java.util.List;
import java.util.Map;


/**
 * 接口
 *
 * <AUTHOR> 2023-03-22 14:30:40
 */
public interface DictDataService extends IService<DictData> {

    List<DictDataVO> listDictData(DictDataDTO dictDataDTO);

    List<DictDataVO> listDictData(String dictType);

    String getNameById(Long id);

    List<DictTypeDataVO> listDictDataByDictType(DictTypeDTO dictTypeDTO);

    /**
     * 获取字典map
     *
     * @param dictType 字典类型
     * @return Map<String, String> key:code  value:名称
     */
    Map<String, String> getDictMap(String dictType);
    /**
     * 获取字典map
     *
     * @param dictType 字典类型
     * @return Map<String, String> key:名称  value:code
     */
    Map<String, String> getDictMap2(String dictType);
}