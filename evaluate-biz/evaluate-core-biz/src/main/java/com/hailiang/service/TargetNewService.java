package com.hailiang.service;

import com.hailiang.model.dto.request.QueryTargetRequest;
import com.hailiang.model.dto.response.TargetCommonResponse;

import java.util.List;

/**
 * 指标新服务
 *
 * @Description: 指标新服务
 * @Author: Jovi
 * @Date: Created in 2024-12-02
 * @Version: 2.0.0
 */
public interface TargetNewService {
    List<TargetCommonResponse> listTargetByGroupId(QueryTargetRequest request);
}
