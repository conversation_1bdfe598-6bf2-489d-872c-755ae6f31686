package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.mapper.DictMapper;
import com.hailiang.model.dto.DictDTO;
import com.hailiang.model.entity.Dict;
import com.hailiang.service.DictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 业务实现
 *
 * <AUTHOR> 2023-03-22 14:30:40
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DictServiceImpl extends ServiceImpl<DictMapper, Dict> implements DictService {

    @Override
    public boolean saveDict(DictDTO dictDTO) {
        Dict dict = new Dict();
      
        BeanUtil.copyProperties(dictDTO, dict);
          
        return this.save(dict);
    }
}
