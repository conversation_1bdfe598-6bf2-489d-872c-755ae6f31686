package com.hailiang.service;

import com.hailiang.model.medal.dto.MedalCatalogueEditDTO;
import com.hailiang.model.medal.dto.MedalCatalogueSaveDTO;
import com.hailiang.model.medal.vo.MedalCatalogueVO;

import java.util.List;

/**
 * @Author: JJL
 * @Date: 2023/5/31 14:27
 */
public interface MedalCatalogueService {
    /**
     * 新增章目
     *
     * @param dto
     * @return
     */
    void saveMedalCatalogue(MedalCatalogueSaveDTO dto);

    /**
     * 删除章目
     *
     * @param id
     * @return
     */
    void deleteMedalCatalogue(Long id);

    /**
     * 修改章目
     *
     * @param dto
     * @return
     */
    void updateMedalCatalogue(MedalCatalogueEditDTO dto);

    /**
     * 查询章目
     *
     * @return
     */
    List<MedalCatalogueVO> listMedalCatalogue();


}
