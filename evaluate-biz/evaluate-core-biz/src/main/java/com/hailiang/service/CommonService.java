package com.hailiang.service;

import com.hailiang.internal.model.response.SaasActiveResponse;
import com.hailiang.model.datastatistics.vo.CommonMobileVO;
import com.hailiang.model.datastatistics.vo.ZheLiBanAppVO;
import com.hailiang.model.dto.ProvinceCityAreaQueryDTO;
import com.hailiang.model.dto.SportDataUserTestSchoolQueryDTO;
import com.hailiang.model.dto.request.CorpIdClassExchangeRequest;
import com.hailiang.model.dto.request.SchoolSwitchConfigRequest;
import com.hailiang.model.dto.response.CorpIdClassExchangeResponse;
import com.hailiang.model.response.common.ProvinceSchoolCampusResponse;
import com.hailiang.model.vo.CodeNameVO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.saas.model.vo.CampusH5ConfigVO;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

/**
 * @Author: JJL
 * @Date: 2023/5/16 9:57
 */
public interface CommonService {

    /**
     * 获取token和学校header信息
     *
     * @param type       1:教师端 2：家长端
     * @param sourceFrom 学在浙江（浙里办应用名称）
     * @return
     */
    ZheLiBanAppVO getZheLiBanToken(Integer type, String sourceFrom);

    /**
     * 根据token解析手机号
     *
     * @param request
     * @return
     */
    CommonMobileVO getMobileByToken(HttpServletRequest request);

    /**
     * 是否包含产品运营角色
     *
     * @return
     */
    Boolean isHaveRunnerRole();

    /**
     * 开通综评应用学校的省市区
     *
     * @return
     */
    List<CodeNameVO> listArea(SportDataUserTestSchoolQueryDTO dto);

    /**
     * 根据省市区查询所有学校和校区
     *
     * @return
     */
    List<EduOrgTreeVO> listSchoolByArea(ProvinceCityAreaQueryDTO dto);

    Boolean getIsAdmin();

    /**
     * 开通综评应用的省-学校-校区
     *
     * @return
     */
    List<ProvinceSchoolCampusResponse> listCampus();

    /**
     * 校验学校开关状态
     */
    Boolean checkSwitch(SchoolSwitchConfigRequest request);

    /**
     * 获取校区h5配置状态
     * @param campusId
     * @return
     */
    List<CampusH5ConfigVO> listCampusH5ConfigByCampusId(Long campusId);

    /**
     * 转换corpId和classId
     * @param request
     * @return
     */
    CorpIdClassExchangeResponse exchangeCorpIdAndClassId(CorpIdClassExchangeRequest request);
    /**
     * 获取saas活动配置信息
     * @param code
     * @return
     */
    SaasActiveResponse saasActiveConf(String code);
}
