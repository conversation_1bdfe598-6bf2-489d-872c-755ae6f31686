package com.hailiang.service.impl;

import static com.hailiang.enums.ImportsValidationEnum.DATE_VALID;
import static com.hailiang.enums.ImportsValidationEnum.NOT_EMPTY_VALID;
import static com.hailiang.enums.ImportsValidationEnum.NUMERIC_VALID;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.hailiang.constant.Constant;
import com.hailiang.entity.EvaluateBehaviourImportsRecordPO;
import com.hailiang.enums.InfoTypeEnum;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.enums.SubmitRateEnum;
import com.hailiang.exception.BizException;
import com.hailiang.helper.evaluate.ImportsEvaluateCommonValidHelper;
import com.hailiang.manager.DorisBehaviourRecordManager;
import com.hailiang.manager.EvaluateBehaviourImportsRecordManager;
import com.hailiang.manager.TargetGroupManager;
import com.hailiang.manager.TargetManager;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.dto.ClassStudentBaseDTO;
import com.hailiang.model.dto.ImportsBehaviourRecordMsgDTO;
import com.hailiang.model.dto.ImportsBehaviourRecordMsgDetailDTO;
import com.hailiang.model.dto.ImportsBehaviourRecordMsgRowDTO;
import com.hailiang.model.dto.ImportsEvaluateDTO;
import com.hailiang.model.dto.SaasClassAndStudentInfoDTO;
import com.hailiang.model.dto.request.evaluate.imports.ImportsEvaluateBaseInfoRequest;
import com.hailiang.model.dto.request.evaluate.imports.ImportsEvaluateBaseInfoRequest.ClassInfo;
import com.hailiang.model.dto.request.evaluate.imports.ImportsEvaluateRequest;
import com.hailiang.model.dto.request.evaluate.imports.ImportsEvaluateTableDataRequest;
import com.hailiang.model.dto.request.evaluate.imports.ImportsEvaluateTemplateOptionRequest;
import com.hailiang.model.dto.request.evaluate.imports.ImportsEvaluateTemplateRequest;
import com.hailiang.model.dto.response.evaluate.imports.ImportsEvaluateErrorDataResponse;
import com.hailiang.model.dto.response.evaluate.imports.ImportsEvaluateResponse;
import com.hailiang.model.dto.response.evaluate.imports.ImportsEvaluateResponse.Student;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TargetGroup;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.vo.educational.EduClassInfoLinkVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasStaffManager;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.dto.StudentDTO;
import com.hailiang.saas.model.vo.StudentInfoVO;
import com.hailiang.saas.model.vo.classes.ClassStudentOutVO;
import com.hailiang.saas.model.vo.classes.StudentBaseOutVO;
import com.hailiang.saas.model.vo.staff.StaffBatchVO;
import com.hailiang.service.ImportsEvaluateMQService;
import com.hailiang.service.ImportsEvaluateService;
import com.hailiang.service.TargetTemplateService;
import com.hailiang.service.TargetUserService;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 点评导入核心服务
 *
 * @Description: 点评导入核心服务
 * @Author: Jovi
 * @Date: Created in 2024-12-02
 * @Version: 2.0.0
 */
@Service
@Slf4j
public class ImportsEvaluateServiceImpl implements ImportsEvaluateService {

    public static final String STUDENT_NAME = "studentName";

    public static final String STUDENT_NO = "studentNo";


    public static final String CLASS_NAME = "className";


    List<String> baseColumn = Arrays.asList(STUDENT_NO, CLASS_NAME, STUDENT_NAME);

    @Resource
    private ImportsEvaluateCommonValidHelper importsEvaluateCommonValidHelper;

    @Resource
    private TargetManager targetManager;

    @Resource
    private TargetGroupManager targetGroupManager;

    @Resource
    private TargetUserService targetUserService;

    @Resource
    private TargetTemplateService targetTemplateService;

    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private SaasStudentManager saasStudentManager;

    @Resource
    private SaasStaffManager saasStaffManager;

    @Resource
    private EvaluateBehaviourImportsRecordManager importsRecordManager;

    @Resource
    private ImportsEvaluateMQService importsEvaluateMQService;

    @Resource
    private DorisBehaviourRecordManager dorisBehaviourRecordManager;

    @Override
    public List<TemplateInfoSaveDTO> listNormalSubmitInfoByTargetId(Long targetId) {
        TargetTemplate targetTemplatePO = targetTemplateService.getByTargetId(targetId);

        if (targetTemplatePO == null) {
            throw new BizException("指标具体点评项不存在，或被删除，请刷新页面重试");
        }

        //具体点评项信息
        List<TemplateInfoSaveDTO> templateInfoList = targetTemplatePO.getTemplateInfoList();

        List<TemplateInfoSaveDTO> filteredNormalTemplateInfoSaveDTOList = templateInfoList
                .stream()
                .filter(templateInfo ->
                        SubmitInfoTypeEnum.isNormalSubmitInfo(templateInfo.getType()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(filteredNormalTemplateInfoSaveDTOList)) {
            throw new BizException("该指标不存在选项、文本、数字、日期类型指标，不可导入");
        }

        log.info("【批量点评导入数据】-【指标具体点评项信息：{}】",
                JSONUtil.toJsonStr(filteredNormalTemplateInfoSaveDTOList));

        return filteredNormalTemplateInfoSaveDTOList;
    }


    @Override
    public ImportsEvaluateResponse importsEvaluate(ImportsEvaluateRequest importsEvaluateRequest) {

        log.info("【批量点评导入数据】-【点评入参：{}】", importsEvaluateRequest);

        Target target = targetManager.getById(importsEvaluateRequest.getBaseInfoRequest().getTargetId());
        Assert.notNull(target, "指标不存在或者已删除！");

        ImportsEvaluateDTO importsEvaluateDTO = buildBaseImportsEvaluateDTO();
        //基础数据校验
        ImportsEvaluateResponse importsEvaluateResponse = validRequest(
                importsEvaluateRequest,
                importsEvaluateDTO);

        // 导入方式 1 初始方式  2 选项做表头 选项做表头且开启了分值的情况下过滤掉分值为空的情况
        if(Objects.equals(importsEvaluateRequest.getImportType(), 2)){
            this.filterRowAndColumn(importsEvaluateRequest);
        }

        //校验成功后则处理导入逻辑，否则直接返回校验失败的结果
        if (Constant.TWO.equals(importsEvaluateResponse.getOperateSuccess())) {
            return importsEvaluateResponse;
        }

        //构建入库记录
        List<ImportsBehaviourRecordMsgRowDTO> importsBehaviourRecordMsgRowDTOList = buildImportsBehaviourRecordLists(
                importsEvaluateRequest,
                importsEvaluateDTO);


        //校验学生控件是否需要全选学生（返回未选择的学生）
        List<ClassStudentOutVO> existUnselectedStudents = checkStudentControls(
                target,
                importsEvaluateRequest);

        if (CollUtil.isNotEmpty(existUnselectedStudents)){
            //过滤异常状态学生
            List<Student> errorStudents = filterAbnormalStateStudents(existUnselectedStudents);
            if (CollUtil.isNotEmpty(errorStudents)){
                importsEvaluateResponse.setOperateSuccess(Constant.THREE);
                importsEvaluateResponse.setErrorStudents(errorStudents);
                return importsEvaluateResponse;
            }
        }

        //校验指标提交频次，学生是否有重复提交的点评记录
        List<Student> restrictedStudents = checkSubmissionFrequency(
                target,
                importsEvaluateRequest,
                importsBehaviourRecordMsgRowDTOList);

        if (CollUtil.isNotEmpty(restrictedStudents)) {
            List<String> failStudentIds = restrictedStudents
                    .stream()
                    .map(Student::getStudentId).collect(Collectors.toList());
            //返回异常数据给前端
            importsEvaluateResponse.setOperateSuccess(Constant.FOUR);
            importsEvaluateResponse.setRepeatImportRequests(importsEvaluateRequest.getTableDataRequests()
                    .stream()
                    .filter(item -> failStudentIds.contains(Convert.toStr(item.getStudentId())))
                    .sorted(Comparator.comparing(
                            ImportsEvaluateTableDataRequest::getStudentNo,
                            Comparator.nullsLast(String::compareTo)
                    ))
                    .collect(Collectors.toList()));
            //过滤掉异常的学生数据
            importsBehaviourRecordMsgRowDTOList = importsBehaviourRecordMsgRowDTOList.
                    stream()
                    .filter(saveDto -> !failStudentIds.contains(saveDto.getStudentId()))
                    .collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(importsBehaviourRecordMsgRowDTOList)){
            return importsEvaluateResponse;
        }

        Boolean saveFlag = buildAndSaveImportsRecord(
                importsEvaluateDTO,
                importsBehaviourRecordMsgRowDTOList);

        if (saveFlag) {

            ImportsBehaviourRecordMsgDTO importsBehaviourRecordMsgDTO = new ImportsBehaviourRecordMsgDTO();
            importsBehaviourRecordMsgDTO.setBatchSubmitId(importsEvaluateDTO.getSubmitId());

            importsEvaluateMQService.sendMQ(importsBehaviourRecordMsgDTO);

        } else {
            throw new BizException("导入保存记录失败，请稍后重试，或联系管理员");
        }

        return importsEvaluateResponse;
    }

    private List<Student> filterAbnormalStateStudents(List<ClassStudentOutVO> existUnselectedStudents) {
        //过滤学生状态
        StudentDTO studentDTO = new StudentDTO();
        studentDTO.setStudentIds(existUnselectedStudents
                .stream()
                .map(ClassStudentOutVO::getStudentBaseOutVOList)
                .flatMap(List::stream).map(StudentBaseOutVO::getId).distinct().collect(Collectors.toList()));
        List<StudentInfoVO> studentInfoVOS = saasStudentManager.studentDetail(studentDTO);

        return studentInfoVOS
                .stream()
                .filter(s ->
                        Constant.ZERO.equals(Convert.toInt(s.getUpgradeStatus()))
                                && Constant.ZERO.equals(Convert.toInt(s.getStudentStatus()))
                                && Constant.ZERO.equals(Convert.toInt(s.getClassType())))
                .map(student -> {
                    Student studentInfoVO = new Student();
                    studentInfoVO.setStudentId(Convert.toStr(student.getStudentId()));
                    studentInfoVO.setStudentName(student.getStudentName());
                    studentInfoVO.setStudentNo(student.getStudentNo());
                    studentInfoVO.setClassId(Convert.toStr(student.getClassId()));
                    studentInfoVO.setClassName(student.getClassName());
                    return studentInfoVO;
                }).sorted(Comparator.comparing(Student::getStudentNo)).collect(Collectors.toList());
    }


    private List<ClassStudentOutVO> checkStudentControls(Target target,
                                                         ImportsEvaluateRequest importsEvaluateRequest) {

        List<ClassStudentOutVO> resultList = Lists.newArrayList();

        //无须校验，前端控制
        if (Boolean.FALSE.equals(importsEvaluateRequest.getCheckStudentControls())){
            return resultList;
        }
        TargetTemplate targetTemplate = targetTemplateService.get(target.getTemplateId());
        if (ObjectUtil.isNull(targetTemplate)){
            return resultList;
        }

        //学生表单
        TemplateInfoSaveDTO studentTemplateInfo = targetTemplate.getTemplateInfoList()
                .stream()
                .filter(item -> item.getType().equals(SubmitInfoTypeEnum.STUDENT.getText()))
                .findFirst().orElse(null);
        //不存在学生控件、或者不是全选学生控件
        if (ObjectUtil.isNull(studentTemplateInfo) || Boolean.FALSE.equals(studentTemplateInfo.getIsAllStudent())) {
            return resultList;
        }
        //查询班级下的学生信息
        List<ClassInfo> classInfos = importsEvaluateRequest.getBaseInfoRequest().getClassInfos();
        List<Long> classIds = classInfos
                .stream()
                .map(ClassInfo::getClassId).distinct().collect(Collectors.toList());
        List<ClassStudentOutVO> classStudentOutVOS = saasStudentManager.listClassStudentByClassIds(classIds);
        Assert.notEmpty(classStudentOutVOS, "所选班级下没有学生，请先添加学生！");

        Map<String, List<ImportsEvaluateTableDataRequest>> saveDtoMap = importsEvaluateRequest.getTableDataRequests()
                .stream()
                .collect(Collectors.groupingBy(ImportsEvaluateTableDataRequest::getClassName));

        Map<Long, String> classNameMap = classInfos
                .stream()
                .collect(Collectors.toMap(ClassInfo::getClassId, ClassInfo::getClassName, (v1, v2) -> v1));

        //判断班级下的学生全部选中
        for (ClassStudentOutVO classStudentOutVO : classStudentOutVOS) {
            //获取班级下提交的记录
            Long classId = classStudentOutVO.getClassId();
            List<StudentBaseOutVO> studentBaseOutVOList = classStudentOutVO.getStudentBaseOutVOList();
            if (CollUtil.isEmpty(studentBaseOutVOList)){
                continue;
            }

            String className = classNameMap.get(classId);
            if (ObjectUtil.isNull(className)){
                continue;
            }

            //班级下未提交任何学生数据
            List<ImportsEvaluateTableDataRequest> importsEvaluateTableDataRequests = saveDtoMap.get(className);
            if (CollUtil.isEmpty(importsEvaluateTableDataRequests)) {
                resultList.add(classStudentOutVO);
                continue;
            }
            //班级下有学生数据
            List<String> studentNames = importsEvaluateTableDataRequests
                    .stream()
                    .map(ImportsEvaluateTableDataRequest::getStudentName).collect(Collectors.toList());
            //过滤出班级下没有提交的学生
            studentBaseOutVOList = studentBaseOutVOList
                    .stream()
                    .filter(item -> !studentNames.contains(item.getName()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(studentBaseOutVOList)){
                continue;
            }

            classStudentOutVO.setStudentBaseOutVOList(studentBaseOutVOList);
            resultList.add(classStudentOutVO);
        }

        return resultList;
    }
    private List<ImportsEvaluateResponse.Student> checkSubmissionFrequency(Target target,
                                                                           ImportsEvaluateRequest importsEvaluateRequest,
                                                                           List<ImportsBehaviourRecordMsgRowDTO> importsBehaviourRecordMsgRowDTOList) {

        //限制提交学生列表
        List<ImportsEvaluateResponse.Student> restrictedStudentList = new ArrayList<>();

        ImportsEvaluateBaseInfoRequest baseInfoRequest = importsEvaluateRequest.getBaseInfoRequest();
        //查询指标是否限制提交频次
        if (!SubmitRateEnum.TERM.getSubmitType().equals(target.getSubmitType())) {
            return restrictedStudentList;
        }

        //查询当前学期信息（获取开始结束时间）
        TermVo currentTermVo = getCurrentTermVo(baseInfoRequest);

        //校验本次提交的记录中是否存在重复的数据，如果存在填充restrictedStudentList
        checkRepeatSubmitStudentAndFillRestrictedStudentList(
                importsBehaviourRecordMsgRowDTOList,
                restrictedStudentList);

        //过滤重复提交的学生点评记录
        List<String> submitDuplicateStudentIdList = restrictedStudentList
                .stream()
                .map(ImportsEvaluateResponse.Student::getStudentId).distinct().collect(Collectors.toList());

        importsBehaviourRecordMsgRowDTOList = importsBehaviourRecordMsgRowDTOList
                .stream()
                .filter(item -> !submitDuplicateStudentIdList.contains(item.getStudentId()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(importsBehaviourRecordMsgRowDTOList)) {
            return restrictedStudentList;
        }

        //比对学生已经提交的指标是否重复，如果已经提交过则填充restrictedStudentList
         this.diffSubmittedTargetAndFillRestrictedStudentList(
                 currentTermVo,
                 baseInfoRequest,
                 importsBehaviourRecordMsgRowDTOList,
                 restrictedStudentList);

        return restrictedStudentList;
    }

    private void diffSubmittedTargetAndFillRestrictedStudentList(TermVo currentTermVo,
                                     ImportsEvaluateBaseInfoRequest baseInfoRequest,
                                     List<ImportsBehaviourRecordMsgRowDTO> importsBehaviourRecordMsgRowDTOList,
                                     List<ImportsEvaluateResponse.Student> restrictedStudentList) {
        //查询学生当前指标的提交记录
        List<BehaviourRecordDTO> existStudentRecords = dorisBehaviourRecordManager.getRecord(
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                null,
                null,
                null,
                DateUtil.beginOfDay(DateUtil.parseDate(currentTermVo.getStartTime())),
                DateUtil.endOfDay(DateUtil.parseDate(currentTermVo.getEndTime())),
                null,
                baseInfoRequest.getTargetId(),
                importsBehaviourRecordMsgRowDTOList
                        .stream()
                        .map(ImportsBehaviourRecordMsgRowDTO::getStudentId)
                        .distinct().collect(Collectors.toList()));
        //无提交记录
        if (CollUtil.isEmpty(existStudentRecords)) {
            return;
        }

        Map<String, List<BehaviourRecordDTO>> existStudentRecordMap = existStudentRecords
                .stream()
                .collect(Collectors.groupingBy(BehaviourRecordDTO::getStudentId));

        for (ImportsBehaviourRecordMsgRowDTO dto : importsBehaviourRecordMsgRowDTOList) {

            List<BehaviourRecordDTO> subExistStudentRecords = existStudentRecordMap.get(dto.getStudentId());
            if (CollUtil.isEmpty(subExistStudentRecords)) {
                continue;
            }

            //匹配班级（考虑换班场景）
            subExistStudentRecords = subExistStudentRecords
                    .stream()
                    .filter(item -> Objects.equals(item.getClassId(), dto.getClassId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(subExistStudentRecords)) {
                ImportsEvaluateResponse.Student student = new ImportsEvaluateResponse.Student();
                student.setStudentName(dto.getStudentName());
                student.setStudentId(dto.getStudentId());
                restrictedStudentList.add(student);
            }
        }
    }

    private static void checkRepeatSubmitStudentAndFillRestrictedStudentList(List<ImportsBehaviourRecordMsgRowDTO> importsBehaviourRecordMsgRowDTOList,
                                                                             List<ImportsEvaluateResponse.Student> restrictedStudentList) {

        Map<String, List<ImportsBehaviourRecordMsgRowDTO>> importsDtoMap = importsBehaviourRecordMsgRowDTOList
                .stream()
                .collect(Collectors.groupingBy(ImportsBehaviourRecordMsgRowDTO::getStudentId));

        for (List<ImportsBehaviourRecordMsgRowDTO> studentSaveDTOList : importsDtoMap.values()) {
            if (studentSaveDTOList.size() > 1) {
                ImportsBehaviourRecordMsgRowDTO behaviourRecordMsgRowDTO = CollUtil.getFirst(studentSaveDTOList);
                ImportsEvaluateResponse.Student student = new ImportsEvaluateResponse.Student();
                student.setStudentName(behaviourRecordMsgRowDTO.getStudentName());
                student.setStudentId(behaviourRecordMsgRowDTO.getStudentId());
                restrictedStudentList.add(student);
            }
        }
    }

    private TermVo getCurrentTermVo(ImportsEvaluateBaseInfoRequest baseInfoRequest) {
        TermQuery termQuery = new TermQuery();
        termQuery.setCampusSectionId(Convert.toStr(baseInfoRequest.getSectionId()));
        termQuery.setCampusId(WebUtil.getCampusIdLong());
        termQuery.setSchoolId(WebUtil.getSchoolIdLong());

        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        Assert.notEmpty(termVos, "海思谷学期信息为空！");

        TermVo currentTermVo = termVos
                .stream().filter(TermVo::isCurrentTerm).findFirst().orElse(null);
        Assert.notNull(currentTermVo, "当前学期为空！");
        return currentTermVo;
    }

    /**
     * 选项做表头的情况下
     * 分值选项控件下有个别选项未填写分值时 排除掉这部分数据
     * @param importsEvaluateRequest
     */
    private void filterRowAndColumn(ImportsEvaluateRequest importsEvaluateRequest) {
        for (ImportsEvaluateTableDataRequest tableDataRequest : importsEvaluateRequest.getTableDataRequests()) {
            tableDataRequest.getTemplateRequests().removeIf(item -> {
                if (!baseColumn.contains(item.getControlType()) && Objects.equals(item.getIsScore(), true)
                        && Objects.nonNull(item.getOptionRequest()) && CharSequenceUtil.isBlank(
                        item.getOptionRequest()
                                .getScore())) {
                    return true;
                }
                return false;
            });
        }
    }

    /**
     * 组装拼接转换数据的基础信息
     *
     * @return
     */
    private ImportsEvaluateDTO buildBaseImportsEvaluateDTO() {
        Date now = new Date();
        Long submitId = SnowFlakeIdUtil.nextId();

        Long staffIdLong = WebUtil.getStaffIdLong();
        List<StaffBatchVO> staffBatchVOS = saasStaffManager.queryStaffByIds(Lists.newArrayList(staffIdLong), 0);
        if (CollUtil.isEmpty(staffBatchVOS)) {
            throw new BizException("当前登录用户不存在，请重新登录");
        }

        String staffName = staffBatchVOS.get(0).getName();
        String tenantId = WebUtil.getTenantId();
        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();

        ImportsEvaluateDTO importsEvaluateDTO = new ImportsEvaluateDTO();
        importsEvaluateDTO.setAppraisalId(staffIdLong);
        importsEvaluateDTO.setAppraisalName(staffName);
        importsEvaluateDTO.setSubmitId(submitId);
        importsEvaluateDTO.setSubmitDate(now);
        importsEvaluateDTO.setTenantId(tenantId);
        importsEvaluateDTO.setSchoolId(schoolId);
        importsEvaluateDTO.setCampusId(campusId);

        return importsEvaluateDTO;
    }

    /**
     * 循环构建每一条点评记录
     *
     * @param importsEvaluateRequest
     * @param importsEvaluateDTO
     * @return
     */
    private List<ImportsBehaviourRecordMsgRowDTO> buildImportsBehaviourRecordLists(
            ImportsEvaluateRequest importsEvaluateRequest,
            ImportsEvaluateDTO importsEvaluateDTO) {

        List<ImportsEvaluateTableDataRequest> tableDataRowRequests = importsEvaluateRequest.getTableDataRequests();

        List<ImportsBehaviourRecordMsgRowDTO> importsBehaviourRecordMsgRowDTOList = new ArrayList<>();

        //校验完成后，开始处理导入逻辑，遍历每一行数据
        for (ImportsEvaluateTableDataRequest tableDataRowRequest : tableDataRowRequests) {

            buildSingleRowRecord(importsEvaluateDTO, tableDataRowRequest, importsBehaviourRecordMsgRowDTOList);
        }

        return importsBehaviourRecordMsgRowDTOList;
    }

    /**
     * 处理每一行数据
     *
     * @param importsEvaluateDTO
     * @param tableDataRowRequest
     * @param importsBehaviourRecordMsgRowDTOList
     */
    private void buildSingleRowRecord(ImportsEvaluateDTO importsEvaluateDTO,
                                      ImportsEvaluateTableDataRequest tableDataRowRequest,
                                      List<ImportsBehaviourRecordMsgRowDTO> importsBehaviourRecordMsgRowDTOList) {

        Long rowId = SnowFlakeIdUtil.nextId();

        ImportsBehaviourRecordMsgRowDTO importsBehaviourRecordMsgRowDTO = new ImportsBehaviourRecordMsgRowDTO();
        importsBehaviourRecordMsgRowDTO.setTenantId(importsEvaluateDTO.getTenantId());
        importsBehaviourRecordMsgRowDTO.setSchoolId(importsEvaluateDTO.getSchoolId());
        importsBehaviourRecordMsgRowDTO.setCampusId(importsEvaluateDTO.getCampusId());
        importsBehaviourRecordMsgRowDTO.setCampusSectionId(Convert.toStr(importsEvaluateDTO.getSectionId()));
        importsBehaviourRecordMsgRowDTO.setCampusSectionCode(importsEvaluateDTO.getSectionCode());
        importsBehaviourRecordMsgRowDTO.setGradeId(Convert.toStr(importsEvaluateDTO.getGradeId()));
        importsBehaviourRecordMsgRowDTO.setGradeCode(importsEvaluateDTO.getGradeCode());
        if (Objects.isNull(tableDataRowRequest.getXzClassId())) {
            throw new BizException("行政班班级ID为空，系统异常");
        }
        importsBehaviourRecordMsgRowDTO.setClassId(Convert.toStr(tableDataRowRequest.getXzClassId()));
        if (Objects.isNull(tableDataRowRequest.getStudentId())) {
            throw new BizException("行政班班级ID为空，系统异常");
        }
        importsBehaviourRecordMsgRowDTO.setStudentId(Convert.toStr(tableDataRowRequest.getStudentId()));
        importsBehaviourRecordMsgRowDTO.setStudentName(Convert.toStr(tableDataRowRequest.getStudentName()));
        importsBehaviourRecordMsgRowDTO.setModuleCode(importsEvaluateDTO.getModuleCode());
        importsBehaviourRecordMsgRowDTO.setTargetId(importsEvaluateDTO.getTargetId());
        importsBehaviourRecordMsgRowDTO.setTargetName(importsEvaluateDTO.getTargetName());
        importsBehaviourRecordMsgRowDTO.setSubmitDate(importsEvaluateDTO.getSubmitDate());
        importsBehaviourRecordMsgRowDTO.setAppraisalType(1);
        importsBehaviourRecordMsgRowDTO.setAppraisalId(Convert.toStr(importsEvaluateDTO.getAppraisalId()));
        importsBehaviourRecordMsgRowDTO.setAppraisalName(importsEvaluateDTO.getAppraisalName());

        String subjectCode = importsEvaluateDTO.getSubjectCode();
        if (StringUtils.isNotBlank(subjectCode)) {
            importsBehaviourRecordMsgRowDTO.setSubjectCode(subjectCode);
        } else {
            importsBehaviourRecordMsgRowDTO.setSubjectCode("0");
        }

        importsBehaviourRecordMsgRowDTO.setNotPartCount(importsEvaluateDTO.getTargetNotPartCount());
        //一行记录的infoId都是一样的
        importsBehaviourRecordMsgRowDTO.setInfoId(Convert.toStr(rowId));

        List<ImportsBehaviourRecordMsgDetailDTO> detailList = new ArrayList<>();

        //遍历每一个的每一个单元格
        tableDataRowRequest.getTemplateRequests().forEach(templateRequest ->
                buildSingleImportsBehaviourRecordDTO(
                        templateRequest,
                        importsBehaviourRecordMsgRowDTO,
                        detailList));

        //如果是包含选项，且不是分值的控件，则需要拆分
        if (importsBehaviourRecordMsgRowDTO.getIsIncludedCheck()
                && !importsBehaviourRecordMsgRowDTO.getIsScoreControl()) {

            importsBehaviourRecordMsgRowDTO.setIsSplit(true);
            importsBehaviourRecordMsgRowDTO.setInfoType(InfoTypeEnum.OPTION.getCode());

        }

        importsBehaviourRecordMsgRowDTO.setDetailList(detailList);

        importsBehaviourRecordMsgRowDTOList.add(importsBehaviourRecordMsgRowDTO);
    }

    /**
     * 构建单个点评记录转换数据，用于传递消息和转换成JSON
     *
     * @param templateRequest
     * @param detailList
     */
    private void buildSingleImportsBehaviourRecordDTO(ImportsEvaluateTemplateRequest templateRequest,
                                                      ImportsBehaviourRecordMsgRowDTO importsBehaviourRecordMsgRowDTO,
                                                      List<ImportsBehaviourRecordMsgDetailDTO> detailList) {

        if (baseColumn.contains(templateRequest.getControlType())) {
            return;
        }

        ImportsBehaviourRecordMsgDetailDTO importsBehaviourRecordMsgDetailDTO = new ImportsBehaviourRecordMsgDetailDTO();
        importsBehaviourRecordMsgDetailDTO.setControlId(templateRequest.getControlId());
        importsBehaviourRecordMsgDetailDTO.setControlType(templateRequest.getControlType());
        importsBehaviourRecordMsgDetailDTO.setControlName(templateRequest.getColumnName());

        //选项控件情况下
        if (SubmitInfoTypeEnum.isCheckSubmitInfo(templateRequest.getControlType())) {

            ImportsEvaluateTemplateOptionRequest optionRequest = templateRequest.getOptionRequest();

            if (templateRequest.getIsScore()) {

                importsBehaviourRecordMsgDetailDTO.setIsScore(true);
                BigDecimal score = new BigDecimal(optionRequest.getScore());
                importsBehaviourRecordMsgDetailDTO.setScore(score);
                importsBehaviourRecordMsgDetailDTO.setScoreValue(score.abs());
                importsBehaviourRecordMsgDetailDTO.setScoreType(score.compareTo(BigDecimal.ZERO) >= 0 ? 1 : 2);

                //带分值的选项肯定是要拆分，因为和分值控件互斥，所以直接设置为拆分状态
                importsBehaviourRecordMsgRowDTO.setIsSplit(true);
            }

            importsBehaviourRecordMsgDetailDTO.setInfoName(optionRequest.getOptionValue());
            importsBehaviourRecordMsgDetailDTO.setOptionValue(optionRequest.getOptionValue());
            importsBehaviourRecordMsgDetailDTO.setOptionId(optionRequest.getOptionId());

            //只要有一个选项控件，就设置为true，后续循环完整行所有控件后，根据此标识和是否分值控件来判断是否拆分
            importsBehaviourRecordMsgRowDTO.setIsIncludedCheck(true);

        }
        //分值控件情况下
        else if (templateRequest.getControlType().equals(SubmitInfoTypeEnum.SCORE.getText())) {
            importsBehaviourRecordMsgDetailDTO.setInfoName(templateRequest.getColumnName());
            importsBehaviourRecordMsgDetailDTO.setIsScore(true);
            BigDecimal score = new BigDecimal(templateRequest.getControlValue());

            //分值情况下也要给Score赋值，用于BehaviourRecord的值
            importsBehaviourRecordMsgDetailDTO.setScore(score);
            importsBehaviourRecordMsgDetailDTO.setScoreValue(score.abs());
            importsBehaviourRecordMsgDetailDTO.setScoreType(score.compareTo(BigDecimal.ZERO) >= 0 ? 1 : 2);

            //用于给BehaviourRecordImportDetail的ControlValue赋值
            importsBehaviourRecordMsgDetailDTO.setControlValue(templateRequest.getControlValue());

            //不管分值控件在前在后，只要又分值控件就设置成拆分状态
            importsBehaviourRecordMsgRowDTO.setIsSplit(false);

            //只要有分值控件，就设置为true，后续循环完整行所有控件后，根据此标识和是否包含选项标识来判断是否拆分
            importsBehaviourRecordMsgRowDTO.setIsScoreControl(true);
        }
        //非选项控件情况下
        else {

            importsBehaviourRecordMsgDetailDTO.setControlValue(templateRequest.getControlValue());
            importsBehaviourRecordMsgDetailDTO.setOptionId(templateRequest.getControlId());
            importsBehaviourRecordMsgDetailDTO.setInfoName(templateRequest.getColumnName());

            //只有在拆分标识为空的情况下，才设置为false，代表从来没有设置过拆分标识，否则按照之前的设置，也就是以分值和选项控件来判断是否拆分，
            if (importsBehaviourRecordMsgRowDTO.getIsSplit() == null) {
                importsBehaviourRecordMsgRowDTO.setIsSplit(false);
            }
        }

        detailList.add(importsBehaviourRecordMsgDetailDTO);
    }


    /**
     * 组装批量导入记录，并保存到导入记录表中，一次导入存一条数据
     *
     * @param importsEvaluateDTO
     * @param importsBehaviourRecordMsgRowDTOList
     * @return
     */
    private Boolean buildAndSaveImportsRecord(ImportsEvaluateDTO importsEvaluateDTO,
                                              List<ImportsBehaviourRecordMsgRowDTO> importsBehaviourRecordMsgRowDTOList) {

        EvaluateBehaviourImportsRecordPO importsRecord = new EvaluateBehaviourImportsRecordPO();
        importsRecord.setId(importsEvaluateDTO.getSubmitId());
        importsRecord.setTenantId(importsEvaluateDTO.getTenantId());
        importsRecord.setSchoolId(importsEvaluateDTO.getSchoolId());
        importsRecord.setCampusId(importsEvaluateDTO.getCampusId());
        importsRecord.setCampusSectionId(Convert.toStr(importsEvaluateDTO.getSectionId()));
        importsRecord.setCampusSectionCode(importsEvaluateDTO.getSectionCode());
        importsRecord.setGradeId(Convert.toStr(importsEvaluateDTO.getGradeId()));
        importsRecord.setGradeCode(importsEvaluateDTO.getGradeCode());
        importsRecord.setClassIds(JSONUtil.toJsonStr(importsEvaluateDTO.getClassIds()));
        importsRecord.setAppraisalId(importsEvaluateDTO.getAppraisalId());
        importsRecord.setAppraisalName(importsEvaluateDTO.getAppraisalName());
        importsRecord.setAppraisalType(1);
        importsRecord.setModuleCode(importsEvaluateDTO.getModuleCode());
        importsRecord.setTargetGroupId(importsEvaluateDTO.getTargetGroupId());
        importsRecord.setTargetGroupName(importsEvaluateDTO.getTargetGroupName());
        importsRecord.setTargetId(importsEvaluateDTO.getTargetId());
        importsRecord.setTargetName(importsEvaluateDTO.getTargetName());
        importsRecord.setSubmitId(importsEvaluateDTO.getSubmitId());
        importsRecord.setSubjectCode(importsEvaluateDTO.getSubjectCode());
        importsRecord.setSubjectName(importsEvaluateDTO.getSubjectName());
        importsRecord.setValidatedSubmitContent(JSONUtil.toJsonStr(importsBehaviourRecordMsgRowDTOList));

        return importsRecordManager.saveRecord(importsRecord);
    }


    /**
     * 入参校验
     *
     * @param importsEvaluateRequest
     */
    private ImportsEvaluateResponse validRequest(ImportsEvaluateRequest importsEvaluateRequest,
                                                 ImportsEvaluateDTO importsEvaluateDTO) {

        ImportsEvaluateResponse importsEvaluateResponse = new ImportsEvaluateResponse();

        ImportsEvaluateBaseInfoRequest baseInfoRequest = importsEvaluateRequest.getBaseInfoRequest();

        List<ImportsEvaluateTableDataRequest> tableDataRequests = importsEvaluateRequest.getTableDataRequests();

        Integer importType = importsEvaluateRequest.getImportType();

        Long targetId = getTargetIdAndBaseRequestValid(
                importsEvaluateDTO,
                tableDataRequests,
                baseInfoRequest);

        List<TemplateInfoSaveDTO> filteredNormalTemplateInfoSaveDTOList = listNormalSubmitInfoByTargetId(targetId);

        List<SaasClassAndStudentInfoDTO> saasClassAndStudentInfoDTOS = dealClassAndStudentByClassIds(
                baseInfoRequest.getClassIds());

        //获取以选项KEY为KEY的指标项
        Map<String/*controlId*/, TemplateInfoSaveDTO> targetOptionMap = filteredNormalTemplateInfoSaveDTOList
                .stream()
                .collect(Collectors.toMap(TemplateInfoSaveDTO::getKey, templateInfo -> templateInfo));

        List<ImportsEvaluateErrorDataResponse> failRecords = validTable(
                tableDataRequests,
                targetOptionMap,
                saasClassAndStudentInfoDTOS,
                importType);

        if (CollUtil.isNotEmpty(failRecords)) {
            importsEvaluateResponse.setFailRecords(failRecords);
            importsEvaluateResponse.setOperateSuccess(Constant.TWO);
        }

        return importsEvaluateResponse;
    }

    /**
     * 校验基础常规入参，并赋值转换数据，返回指标ID
     *
     * @param importsEvaluateDTO
     * @param tableDataRequests
     * @param baseInfoRequest
     * @return
     */
    private Long getTargetIdAndBaseRequestValid(ImportsEvaluateDTO importsEvaluateDTO,
                                                List<ImportsEvaluateTableDataRequest> tableDataRequests,
                                                ImportsEvaluateBaseInfoRequest baseInfoRequest) {

        if (tableDataRequests.size() > 5000) {
            throw new BizException("最多支持导入5000条记录");
        }

        Long targetGroupId = baseInfoRequest.getGroupId();
        TargetGroup targetGroupPO = targetGroupManager.getById(targetGroupId);

        if (targetGroupPO == null) {
            throw new BizException("指标分组不存在，或被删除，请刷新页面重试");
        }

        Long targetId = baseInfoRequest.getTargetId();
        Target targetPO = targetManager.getById(targetId);
        if (targetPO == null) {
            throw new BizException("指标不存在，或被删除，请刷新页面重试");
        }

        if (!targetUserService.isValidByStaffIdAndTargetId(importsEvaluateDTO.getAppraisalId(), targetId)) {
            throw new BizException("您无权操作该指标");
        }

        importsEvaluateDTO.setSectionId(baseInfoRequest.getSectionId());
        importsEvaluateDTO.setSectionCode(baseInfoRequest.getSectionCode());
        importsEvaluateDTO.setSectionName(baseInfoRequest.getSectionName());
        importsEvaluateDTO.setGradeId(baseInfoRequest.getGradeId());
        importsEvaluateDTO.setGradeCode(baseInfoRequest.getGradeCode());
        importsEvaluateDTO.setClassIds(baseInfoRequest.getClassIds());
        importsEvaluateDTO.setModuleCode(baseInfoRequest.getModuleCode());
        importsEvaluateDTO.setTargetId(targetId);
        importsEvaluateDTO.setTargetName(targetPO.getTargetName());
        importsEvaluateDTO.setTargetGroupId(targetGroupId);
        importsEvaluateDTO.setTargetGroupName(targetGroupPO.getGroupName());
        importsEvaluateDTO.setSubjectId(
                StringUtils.isBlank(baseInfoRequest.getSubjectId()) ? "" : baseInfoRequest.getSubjectId());
        importsEvaluateDTO.setSubjectCode(
                StringUtils.isBlank(baseInfoRequest.getSubjectCode()) ? "" : baseInfoRequest.getSubjectCode());
        importsEvaluateDTO.setSubjectName(
                StringUtils.isBlank(baseInfoRequest.getSubjectName()) ? "" : baseInfoRequest.getSubjectName());
        importsEvaluateDTO.setTargetNotPartCount(targetPO.getTargetNotPartCount());

        log.info("【批量导入点评数据】-【校验基础常规入参，并赋值转换数据，返回指标ID：{}】-【转换数据：{}】",
                targetId, importsEvaluateDTO);

        return targetId;
    }


    /**
     * 处理班级信息
     *
     * @param classIds
     */
    private List<SaasClassAndStudentInfoDTO> dealClassAndStudentByClassIds(List<Long> classIds) {

        List<SaasClassAndStudentInfoDTO> classAndStudentInfoDTOList = new ArrayList<>();

        List<EduClassInfoLinkVO> eduClassInfoLinkVOS = basicInfoRemote.listUnderByClassIds(classIds);
        if (CollUtil.isEmpty(eduClassInfoLinkVOS)) {
            throw new BizException("所选班级都不存在，或被删除，请刷新页面重试");
        }

        // 通过学生管理服务获取与给定班级ID列表相关联的学生信息
        List<ClassStudentOutVO> classStudentOutVOS = saasStudentManager.listClassStudentByClassIds(classIds);

        // 如果获取到的学生信息列表为空，则抛出异常
        if (CollUtil.isEmpty(classStudentOutVOS)) {
            throw new BizException("所选班级下不存在学生，或被删除，请刷新页面重试");
        }

        // 将学生信息列表转换为以班级ID为键的映射
        Map<Long/*classId*/, ClassStudentOutVO> classStudentOutVOMap = classStudentOutVOS
                .stream()
                .collect(Collectors.toMap(ClassStudentOutVO::getClassId, classStudent -> classStudent));

        for (EduClassInfoLinkVO eduClassInfoLinkVO : eduClassInfoLinkVOS) {

            SaasClassAndStudentInfoDTO saasClassAndStudentInfoDTO = new SaasClassAndStudentInfoDTO();

            Long classId = eduClassInfoLinkVO.getId();
            String className = eduClassInfoLinkVO.getClassName();
            String classType = eduClassInfoLinkVO.getClassType();

            saasClassAndStudentInfoDTO.setClassId(classId);
            saasClassAndStudentInfoDTO.setClassName(className);
            saasClassAndStudentInfoDTO.setClassType(classType);

            ClassStudentOutVO classStudentOutVO = classStudentOutVOMap.get(classId);
            if (classStudentOutVO == null) {
                log.warn("该班级下不存在学生，跳过，班级ID：【{}】", classId);
                continue;
            }

            dealSingleClassStudentInfos(saasClassAndStudentInfoDTO, classStudentOutVO);

            classAndStudentInfoDTOList.add(saasClassAndStudentInfoDTO);

        }

        log.info("【批量导入点评数据】-【处理完Saas班级后的信息：{}】", classAndStudentInfoDTOList);

        return classAndStudentInfoDTOList;
    }

    /**
     * 获取班级下学生信息
     *
     * @param classStudentOutVO
     * @return
     */
    private void dealSingleClassStudentInfos(SaasClassAndStudentInfoDTO saasClassAndStudentInfoDTO,
                                             ClassStudentOutVO classStudentOutVO) {

        // 从学生信息中提取学生基本信息列表
        List<StudentBaseOutVO> studentBaseOutVOList = classStudentOutVO.getStudentBaseOutVOList();

        // 创建一个新的列表来存储转换后的学生信息DTO
        List<ClassStudentBaseDTO> classStudentBaseDTOS = new ArrayList<>();

        // 用于检测学生姓名是否重复的集合
        Map<String/*studentName*/, List<String>/*studentNos*/> repeatStudentMap = new HashMap<>();

        // 遍历学生基本信息列表并进行转换和检查
        for (StudentBaseOutVO studentBaseOutVO : studentBaseOutVOList) {

            ClassStudentBaseDTO classStudentBaseDTO = getClassStudentBaseDTO(studentBaseOutVO, repeatStudentMap);

            if (classStudentBaseDTO != null) {
                classStudentBaseDTOS.add(classStudentBaseDTO);
            }
        }

        saasClassAndStudentInfoDTO.setStudentBaseOutVOList(classStudentBaseDTOS);

        saasClassAndStudentInfoDTO.setRepeatStudentMap(repeatStudentMap);
    }


    /**
     * 获取学生信息DTO
     *
     * @param studentBaseOutVO
     * @return
     */
    private ClassStudentBaseDTO getClassStudentBaseDTO(StudentBaseOutVO studentBaseOutVO,
                                                       Map<String/*studentName*/, List<String>/*studentNos*/> repeatStudentMap) {

        // 创建一个新的学生信息DTO对象
        ClassStudentBaseDTO classStudentBaseDTO = new ClassStudentBaseDTO();
        classStudentBaseDTO.setStudentId(studentBaseOutVO.getId());

        String studentNo = studentBaseOutVO.getStudentNo();
        classStudentBaseDTO.setStudentNo(studentNo);

        String studentName = studentBaseOutVO.getName();
        classStudentBaseDTO.setStudentName(studentName);

        classStudentBaseDTO.setXzClassId(studentBaseOutVO.getXzClassId());
        classStudentBaseDTO.setXzClassName(studentBaseOutVO.getXzClassName());

        if (StringUtils.isBlank(studentName) || StringUtils.isBlank(studentNo)) {
            log.warn("【批量点评导入】-Saas学生信息中存在空值，跳过该条数据，学生ID：【{}】", studentBaseOutVO.getId());
            return null;
        }

        // 如果学生姓名重复，则设置属性为true，并添加到重复检测集合中
        if (repeatStudentMap.containsKey(studentName)) {
            List<String> studentNos = repeatStudentMap.get(studentName);
            if (CollUtil.isNotEmpty(studentNos) && !studentNos.contains(studentNo)) {
                studentNos.add(studentNo);
            }
        } else {
            List<String> studentNos = new ArrayList<>();
            studentNos.add(studentNo);
            repeatStudentMap.put(studentName, studentNos);
        }

        return classStudentBaseDTO;
    }

    /**
     * 校验表格内容
     *
     * @param tableDataRequests
     */
    private List<ImportsEvaluateErrorDataResponse> validTable(List<ImportsEvaluateTableDataRequest> tableDataRequests,
                                                              Map<String/*controlId*/, TemplateInfoSaveDTO> targetOptionMap,
                                                              List<SaasClassAndStudentInfoDTO> saasClassAndStudentInfoDTOS,
                                                              Integer importType) {

        List<ImportsEvaluateErrorDataResponse> errorRecords = new ArrayList<>();

        // 选项做表头导入方式校验选项组件必填
        if (Objects.equals(importType, 2)) {
            for (ImportsEvaluateTableDataRequest tableRowRequest : tableDataRequests) {
                List<ImportsEvaluateTemplateRequest> templateCellRequests = tableRowRequest.getTemplateRequests();

                this.validControl(templateCellRequests, targetOptionMap, tableRowRequest, errorRecords);
            }

            // 校验不通过无需进行后续校验
            if (CollUtil.isNotEmpty(errorRecords)) {
                return errorRecords;
            }
        }

        //行级校验
        for (ImportsEvaluateTableDataRequest tableRowRequest : tableDataRequests) {

            List<ImportsEvaluateTemplateRequest> templateCellRequests = tableRowRequest.getTemplateRequests();

            Set<String> checkControlId = new HashSet<>();

            //将学生姓名列前置
            Collections.sort(templateCellRequests, (o1, o2) -> {
                if (STUDENT_NAME.equals(o1.getControlType()) && STUDENT_NAME.equals(o2.getControlType())) {
                    return 0;
                }
                if (STUDENT_NAME.equals(o1.getControlType())) {
                    return -1;
                }
                if (STUDENT_NAME.equals(o2.getControlType())) {
                    return 1;
                }
                return 0;
            });

            //单元格校验
            for (ImportsEvaluateTemplateRequest templateCellRequest : templateCellRequests) {

                validClass(saasClassAndStudentInfoDTOS, tableRowRequest, templateCellRequest, errorRecords);

                validStudent(saasClassAndStudentInfoDTOS, tableRowRequest, templateCellRequest, errorRecords);

                validCellCheck(targetOptionMap, tableRowRequest, templateCellRequest, checkControlId, errorRecords, importType);
            }
        }

        return errorRecords;
    }

    /**
     * 校验指标必填组件在选项做表头的情况下是否填写了内容
     * @param templateRequests
     * @param targetOptionMap
     * @param tableRowRequest
     * @param errorRecords
     */
    private void validControl(List<ImportsEvaluateTemplateRequest> templateRequests,
                              Map<String/*controlId*/, TemplateInfoSaveDTO> targetOptionMap,
                              ImportsEvaluateTableDataRequest tableRowRequest,
                              List<ImportsEvaluateErrorDataResponse> errorRecords){
        List<ImportsEvaluateTemplateRequest> evaluateControl = templateRequests.stream()
                .filter(item -> !baseColumn.contains(item.getControlType()))
                .collect(Collectors.toList());

        Map<String, List<ImportsEvaluateTemplateRequest>> controlMap = evaluateControl.stream()
                .collect(Collectors.groupingBy(ImportsEvaluateTemplateRequest::getControlId));

        controlMap.forEach((controlId, importsEvaluateTemplateRequests) -> {
            TemplateInfoSaveDTO templateInfoSaveDTO = targetOptionMap.get(controlId);
            if (Objects.equals(templateInfoSaveDTO.getRequired(), true) && Objects.equals(
                    templateInfoSaveDTO.getIsScore(), true)
                    && SubmitInfoTypeEnum.isCheckSubmitInfo(templateInfoSaveDTO.getType())) {
                List<ImportsEvaluateTemplateRequest> filterList = importsEvaluateTemplateRequests.stream()
                        .filter(item -> {
                            ImportsEvaluateTemplateOptionRequest optionRequest = item.getOptionRequest();
                            if (Objects.nonNull(optionRequest) && CharSequenceUtil.isNotBlank(
                                    optionRequest.getScore())) {
                                return true;
                            }
                            if(Objects.nonNull(item.getControlValue())){
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
                if(CollUtil.isEmpty(filterList)){
                    importsEvaluateTemplateRequests.forEach(item -> this.buildErrorMsg(tableRowRequest, item, errorRecords,
                            "必填选项请至少填写一项"));
                }
            }
        });
    }

    /**
     * 校验班级
     *
     * @param saasClassAndStudentInfoDTOS
     * @param tableDataCellRequest
     * @param errorRecords
     */
    private void validClass(List<SaasClassAndStudentInfoDTO> saasClassAndStudentInfoDTOS,
                            ImportsEvaluateTableDataRequest tableRowRequest,
                            ImportsEvaluateTemplateRequest tableDataCellRequest,
                            List<ImportsEvaluateErrorDataResponse> errorRecords) {

        if (!tableDataCellRequest.getControlType().equals(CLASS_NAME)) {
            return;
        }

        List<String> classNameList = saasClassAndStudentInfoDTOS
                .stream()
                .map(SaasClassAndStudentInfoDTO::getClassName)
                .collect(Collectors.toList());

        if (!classNameList.contains(tableDataCellRequest.getControlValue())) {
            errorRecords.add(ImportsEvaluateErrorDataResponse.builder()
                    .rowIndex(tableRowRequest.getRowIndex())
                    .columnIndex(tableDataCellRequest.getColumnIndex())
                    .columnName(CLASS_NAME)
                    .errorMsg("该班级不在导入范围，请检查").build());
        }
    }

    /**
     * 校验学生信息
     *
     * @param saasClassAndStudentInfoDTOS
     * @param tableDataCellRequest
     * @param errorRecords
     */
    private void validStudent(List<SaasClassAndStudentInfoDTO> saasClassAndStudentInfoDTOS,
                              ImportsEvaluateTableDataRequest tableRowRequest,
                              ImportsEvaluateTemplateRequest tableDataCellRequest,
                              List<ImportsEvaluateErrorDataResponse> errorRecords) {

        if (!tableDataCellRequest.getControlType().equals(STUDENT_NAME)
                && !tableDataCellRequest.getControlType().equals(STUDENT_NO)) {
            return;
        }

        for (SaasClassAndStudentInfoDTO saasClassAndStudentInfoDTO : saasClassAndStudentInfoDTOS) {

            validSingleClassStudentNameAndNo(
                    tableRowRequest,
                    tableDataCellRequest,
                    errorRecords,
                    saasClassAndStudentInfoDTO);
        }
    }


    /**
     * 校验单个学生姓名和学号和学号是否匹配
     *
     * @param tableRowRequest
     * @param tableDataCellRequest
     * @param errorRecords
     * @param saasClassAndStudentInfoDTO
     */
    private void validSingleClassStudentNameAndNo(ImportsEvaluateTableDataRequest tableRowRequest,
                                                  ImportsEvaluateTemplateRequest tableDataCellRequest,
                                                  List<ImportsEvaluateErrorDataResponse> errorRecords,
                                                  SaasClassAndStudentInfoDTO saasClassAndStudentInfoDTO) {

        String studentName = tableRowRequest.getStudentName();
        String studentNo = tableRowRequest.getStudentNo();

        if (!saasClassAndStudentInfoDTO.getClassName().equals(tableRowRequest.getClassName())) {
            return;
        }

        List<ClassStudentBaseDTO> studentBaseOutVOList = saasClassAndStudentInfoDTO.getStudentBaseOutVOList();

        //学生姓名单元格校验

        if (tableDataCellRequest.getControlType().equals(STUDENT_NAME)) {

            if (studentBaseOutVOList.stream().noneMatch(
                    studentBaseOutVO -> studentName.equals(studentBaseOutVO.getStudentName()))) {

                errorRecords.add(ImportsEvaluateErrorDataResponse.builder()
                        .rowIndex(tableRowRequest.getRowIndex())
                        .columnIndex(tableDataCellRequest.getColumnIndex())
                        .columnName(STUDENT_NAME)
                        .errorMsg("该学生不属于该班级，请检查").build());
                return;
            }

            for (ClassStudentBaseDTO classStudentBaseDTO : studentBaseOutVOList) {
                if (classStudentBaseDTO.getStudentName().equals(studentName)) {

                    //如果学生姓名匹配，则校验学号是否为空，不为空则是重名情况，则需要校验学号是否一致
                    if (StringUtils.isNotBlank(studentNo)) {

                        //如果学号不为空，则校验学号是否一致，一致则将对应学生ID，正确学号，班级ID设置到行对象上
                        if (studentNo.equals(classStudentBaseDTO.getStudentNo())) {
                            tableRowRequest.setStudentId(classStudentBaseDTO.getStudentId());
                            tableRowRequest.setStudentRightNo(classStudentBaseDTO.getStudentNo());
                            tableRowRequest.setXzClassId(classStudentBaseDTO.getXzClassId());
                            break;
                        }
                        /*
                        如果姓名匹配上了，学号也填了，但是不一致，则认为是学号不匹配，不会赋值正确学号和班级ID，会在下面的学号单元个校验进行拦截
                        此处如果填了，则同名情况下会有问题，会将两个同名的学生赋值一样的学生ID和正确学号
                         */
                    }
                    //如果为空，则不是重名情况，则只需要姓名一致即可
                    else {
                        tableRowRequest.setStudentId(classStudentBaseDTO.getStudentId());
                        tableRowRequest.setStudentRightNo(classStudentBaseDTO.getStudentNo());
                        tableRowRequest.setXzClassId(classStudentBaseDTO.getXzClassId());
                        break;
                    }
                }
            }
        }

        //学号单元格校验
        if (tableDataCellRequest.getControlType().equals(STUDENT_NO)) {

            List<String> repeatStudentNos = saasClassAndStudentInfoDTO
                    .getRepeatStudentMap()
                    .get(studentName);

            //学号不为空的情况下，首先校验学号是否在当前行的学生姓名的MAP的集合中
            if (StringUtils.isNotBlank(studentNo)) {

                if (CollUtil.isEmpty(repeatStudentNos) || !repeatStudentNos.contains(studentNo)) {
                    errorRecords.add(ImportsEvaluateErrorDataResponse.builder()
                            .rowIndex(tableRowRequest.getRowIndex())
                            .columnIndex(tableDataCellRequest.getColumnIndex())
                            .columnName(STUDENT_NO)
                            .errorMsg("该学生填写的对应学号不正确，请检查后重新提交").build());
                    return;
                }
            }

            if (CollUtil.isNotEmpty(repeatStudentNos) && repeatStudentNos.size() > 1) {

                if (StringUtils.isBlank(studentNo)) {
                    errorRecords.add(ImportsEvaluateErrorDataResponse.builder()
                            .rowIndex(tableRowRequest.getRowIndex())
                            .columnIndex(tableDataCellRequest.getColumnIndex())
                            .columnName(STUDENT_NO)
                            .errorMsg("该学生姓名重复但是没填写学号，请检查后重新提交").build());
                    return;
                }

                if (!repeatStudentNos.contains(studentNo)) {
                    errorRecords.add(ImportsEvaluateErrorDataResponse.builder()
                            .rowIndex(tableRowRequest.getRowIndex())
                            .columnIndex(tableDataCellRequest.getColumnIndex())
                            .columnName(STUDENT_NO)
                            .errorMsg("该学生存在重名但是对应学号不正确，请检查后重新提交").build());
                }
            }

        }
    }


    /**
     * 单个单元格校验
     *
     * @param targetOptionMap
     * @param tableDataRequest
     * @param templateCellRequest
     * @param failRecords
     */
    private void validCellCheck(Map<String/*controlId*/, TemplateInfoSaveDTO> targetOptionMap,
                                ImportsEvaluateTableDataRequest tableDataRequest,
                                ImportsEvaluateTemplateRequest templateCellRequest,
                                Set<String> checkControlIds,
                                List<ImportsEvaluateErrorDataResponse> failRecords,
                                Integer importType) {

        //非点评项单元格
        if (baseColumn.contains(templateCellRequest.getControlType())) {
            return;
        }

        String controlId = templateCellRequest.getControlId();

        //控件值
        String controlValue = templateCellRequest.getControlValue();

        if (!isContains(controlId,
                targetOptionMap.keySet().stream().map(String::toString).collect(Collectors.toList()),
                tableDataRequest,
                templateCellRequest,
                failRecords,
                "该点评项不是该指标下的，请重新选择")) {
            return;
        }

        validEvaluateOption(targetOptionMap,
                tableDataRequest,
                templateCellRequest,
                failRecords,
                controlId,
                checkControlIds,
                controlValue,
                importType);
    }

    /**
     * 校验点评项
     *
     * @param targetOptionMap
     * @param tableDataRequest
     * @param templateCellRequest
     * @param failRecords
     * @param controlId
     * @param controlValue
     */
    private void validEvaluateOption(Map<String/*controlId*/, TemplateInfoSaveDTO> targetOptionMap,
                                     ImportsEvaluateTableDataRequest tableDataRequest,
                                     ImportsEvaluateTemplateRequest templateCellRequest,
                                     List<ImportsEvaluateErrorDataResponse> failRecords,
                                     String controlId,
                                     Set<String> checkControlIds,
                                     String controlValue,
                                     Integer importType) {
        //具体点评项信息
        TemplateInfoSaveDTO templateInfoSaveDTO = targetOptionMap.get(controlId);

        String errorMsg;

        switch (SubmitInfoTypeEnum.getByValue(templateInfoSaveDTO.getType())) {
            case SINGLE_CHECK:
            case MULTI_CHECK:
                if (validCheck(tableDataRequest,
                        templateCellRequest,
                        failRecords,
                        templateInfoSaveDTO,
                        checkControlIds,
                        importType)) {
                    return;
                }
                break;
            case SINGLE_TEXT:
            case MULTI_TEXT:
                if (templateInfoSaveDTO.getRequired()) {

                    errorMsg = importsEvaluateCommonValidHelper.validateWithoutStandard(controlValue,
                            ListUtil.of(NOT_EMPTY_VALID.getValidStrategyCode()));

                    if (StringUtils.isNotBlank(errorMsg)) {
                        buildErrorMsg(tableDataRequest, templateCellRequest, failRecords, errorMsg);
                        break;
                    }
                }
                break;
            case DATE:

                if (templateInfoSaveDTO.getRequired()) {

                    errorMsg = importsEvaluateCommonValidHelper.validateWithoutStandard(controlValue,
                            ListUtil.of(NOT_EMPTY_VALID.getValidStrategyCode()));

                    if (StringUtils.isNotBlank(errorMsg)) {
                        buildErrorMsg(tableDataRequest, templateCellRequest, failRecords, errorMsg);
                        return;
                    }
                }

                if (StringUtils.isNotBlank(controlValue)) {

                    errorMsg = importsEvaluateCommonValidHelper.validateWithoutStandard(controlValue,
                            ListUtil.of(DATE_VALID.getValidStrategyCode()));

                    if (StringUtils.isNotBlank(errorMsg)) {
                        buildErrorMsg(tableDataRequest, templateCellRequest, failRecords, errorMsg);
                        return;
                    }
                }
                break;

            case SCORE:
                errorMsg = importsEvaluateCommonValidHelper.validateWithoutStandard(controlValue,
                        ListUtil.of(NOT_EMPTY_VALID.getValidStrategyCode()));

                if (StringUtils.isNotBlank(errorMsg)) {
                    buildErrorMsg(tableDataRequest, templateCellRequest, failRecords, errorMsg);
                    return;
                }

                errorMsg = importsEvaluateCommonValidHelper.validateWithoutStandard(controlValue,
                        ListUtil.of(NUMERIC_VALID.getValidStrategyCode()));

                if (StringUtils.isNotBlank(errorMsg)) {
                    buildErrorMsg(tableDataRequest, templateCellRequest, failRecords, errorMsg);
                    return;
                }
                break;

            case NUMBER:
                if (templateInfoSaveDTO.getRequired()) {

                    errorMsg = importsEvaluateCommonValidHelper.validateWithoutStandard(controlValue,
                            ListUtil.of(NOT_EMPTY_VALID.getValidStrategyCode()));

                    if (StringUtils.isNotBlank(errorMsg)) {
                        buildErrorMsg(tableDataRequest, templateCellRequest, failRecords, errorMsg);
                        return;
                    }
                }

                if (StringUtils.isNotBlank(controlValue)) {
                    errorMsg = importsEvaluateCommonValidHelper.validateWithoutStandard(controlValue,
                            ListUtil.of(NUMERIC_VALID.getValidStrategyCode()));

                    if (StringUtils.isNotBlank(errorMsg)) {
                        buildErrorMsg(tableDataRequest, templateCellRequest, failRecords, errorMsg);
                        return;
                    }
                }
                break;

            default:
        }
    }

    /**
     * 单选或者多选校验
     *
     * @param tableRowRequest
     * @param templateCellRequest
     * @param failRecords
     * @param templateInfoSaveDTO
     * @return
     */
    private boolean validCheck(ImportsEvaluateTableDataRequest tableRowRequest,
                               ImportsEvaluateTemplateRequest templateCellRequest,
                               List<ImportsEvaluateErrorDataResponse> failRecords,
                               TemplateInfoSaveDTO templateInfoSaveDTO,
                               Set<String> checkControlIds,
                               Integer importType) {

        ImportsEvaluateTemplateOptionRequest optionRequest = templateCellRequest.getOptionRequest();
        if (Objects.isNull(optionRequest)) {
            buildErrorMsg(tableRowRequest, templateCellRequest, failRecords, "该点评项为必填项，请选择");
            return true;
        }

        //具体点评项中的选项中的选项值和得分
        String optionId = templateCellRequest.getOptionRequest().getOptionId();
        String scoreStr = templateCellRequest.getOptionRequest().getScore();

        //必填校验
        if (templateInfoSaveDTO.getRequired() && (StringUtils.isBlank(optionId))) {

            buildErrorMsg(tableRowRequest, templateCellRequest, failRecords, "该点评项为必填项，请填写");
            return true;
        }

        TemplateInfoSaveDTO.InnerSubmitOptionInfoSave option = templateInfoSaveDTO.getOptions();

        //是否分值状态校验
        if (templateInfoSaveDTO.getIsScore() != templateCellRequest.getIsScore()) {
            buildErrorMsg(tableRowRequest, templateCellRequest, failRecords, "该点评项是否分值已变动，请重新选择");
            return true;
        }

        //数据库中存在的指标模板选项
        List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> options = option.getOptions();
        Map<String/*optionLabel*/, BigDecimal> optionsKeyValueMap = options
                .stream()
                .collect(Collectors.toMap(TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave::getKey,
                        TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave::getValue));

        //选项校验
        if (!isContains(optionId,
                optionsKeyValueMap.keySet().stream().map(String::toString).collect(Collectors.toList()),
                tableRowRequest,
                templateCellRequest,
                failRecords,
                "该选项不存在，请重新选择")) {
            return true;
        }

        //分值情况下校验
        if (templateCellRequest.getIsScore()) {

            // importType：1 初始方式导入 校验
            if (Objects.equals(importType, 1) &&
                    checkControlIds.contains(templateCellRequest.getControlId()) &&
                    StringUtils.isNotBlank(optionId) &&
                    (StringUtils.isBlank(scoreStr) || !scoreStr.matches("\\d+"))) {
                buildErrorMsgScore(tableRowRequest, templateCellRequest, failRecords, "该选项为分值点评项，请输入分值");
                return true;
            }

            checkControlIds.add(templateCellRequest.getControlId());
        }

        return false;
    }


    /**
     * 判断是否包含
     *
     * @param value
     * @param contains
     * @param tableDataRequest
     * @param templateRowRequest
     * @param failRecords
     * @param errorMsg
     * @return
     */
    private Boolean isContains(String value, List<String> contains,
                               ImportsEvaluateTableDataRequest tableDataRequest,
                               ImportsEvaluateTemplateRequest templateRowRequest,
                               List<ImportsEvaluateErrorDataResponse> failRecords,
                               String errorMsg) {

        if (!contains.contains(value)) {
            buildErrorMsg(tableDataRequest, templateRowRequest, failRecords, errorMsg);
            return false;
        }

        return true;
    }

    /**
     * 组装错误信息
     *
     * @param tableDataRequest
     * @param templateRowRequest
     * @param failRecords
     * @param errorMsg
     */
    private void buildErrorMsg(ImportsEvaluateTableDataRequest tableDataRequest,
                               ImportsEvaluateTemplateRequest templateRowRequest,
                               List<ImportsEvaluateErrorDataResponse> failRecords,
                               String errorMsg) {

        failRecords.add(ImportsEvaluateErrorDataResponse.builder()
                .rowIndex(tableDataRequest.getRowIndex())
                .columnIndex(templateRowRequest.getColumnIndex())
                .columnName(templateRowRequest.getColumnName())
                .errorMsg(errorMsg).build());
    }

    /**
     * 组装错误信息
     *
     * @param tableDataRequest
     * @param templateRowRequest
     * @param failRecords
     * @param errorMsg
     */
    private void buildErrorMsgScore(ImportsEvaluateTableDataRequest tableDataRequest,
                                    ImportsEvaluateTemplateRequest templateRowRequest,
                                    List<ImportsEvaluateErrorDataResponse> failRecords,
                                    String errorMsg) {

        failRecords.add(ImportsEvaluateErrorDataResponse.builder()
                .rowIndex(tableDataRequest.getRowIndex())
                .columnIndex(templateRowRequest.getColumnIndex())
                .columnName(templateRowRequest.getColumnName())
                .isScore(true)
                .errorMsg(errorMsg).build());
    }

}
