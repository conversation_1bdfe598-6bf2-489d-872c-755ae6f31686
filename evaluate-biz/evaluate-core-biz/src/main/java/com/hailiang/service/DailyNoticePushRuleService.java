package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.save.ReportPushRuleSaveDTO;
import com.hailiang.model.entity.DailyNoticePushRule;

/**
 * 报告推送规则表(ReportPushRule)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-13 11:30:31
 */
public interface DailyNoticePushRuleService extends IService<DailyNoticePushRule> {

    void saveDailyPushRule(ReportPushRuleSaveDTO reportPushRuleSaveDTO);
}

