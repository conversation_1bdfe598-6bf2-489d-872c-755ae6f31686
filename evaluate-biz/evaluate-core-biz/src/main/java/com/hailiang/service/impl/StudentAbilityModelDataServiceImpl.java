package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.enums.studentModel.CalculateStatusEnum;
import com.hailiang.enums.studentModel.TriggerCalculateSceneEnum;
import com.hailiang.exception.BizException;
import com.hailiang.model.dto.studentmodel.v2.CalculatedDTO;
import com.hailiang.model.request.studentmodel.StudentAbilityDataV2Request;
import com.hailiang.model.response.studentmodel.*;
import com.hailiang.saas.SaasStudentCacheManager;
import com.hailiang.saas.model.vo.StudentInfoVO;
import com.hailiang.service.StudentAbilityModelDataService;
import com.hailiang.service.StudentAbilityModelService;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务实现
 *
 * <AUTHOR> 2024-04-19 15:29:38
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StudentAbilityModelDataServiceImpl implements StudentAbilityModelDataService {
    @Value("${rocketmq.topic.studentModelCalculate}")
    private String studentModelCalculateTopic;
    private final RocketMQTemplate rocketMQTemplate;
    private final StudentAbilityModelService studentAbilityModelService;
    private final RedisUtil redisUtil;
    private final SaasStudentCacheManager saasStudentCacheManager;


    @Override
    public StudentAbilityModelDataFinalV2Response getDataV2(StudentAbilityDataV2Request studentAbilityDataV2Request) {
        log.info("【学生能力模型】-【获取数据】校区id：{}, 学生id：{},完整请求参数：{}", WebUtil.getCampusId(), studentAbilityDataV2Request.getStudentId(), JSONUtil.toJsonStr(studentAbilityDataV2Request));
        StudentAbilityModelDataFinalV2Response studentAbilityModelDataFinalV2Response = new StudentAbilityModelDataFinalV2Response();

        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();
        String schoolYear = studentAbilityDataV2Request.getSchoolYear();
        String termName = studentAbilityDataV2Request.getTermName();
        String studentId = studentAbilityDataV2Request.getStudentId();
        Boolean isCurrentYear = studentAbilityDataV2Request.getIsCurrentYear();
        Boolean isCurrentTerm = studentAbilityDataV2Request.getIsCurrentTerm();
        String calculatedDimId = null;
        if (isCurrentTerm){
            calculatedDimId =  schoolYear + "null";
            // 这个为了兼容班牌没有结束时间的情况下 计算时参数校验计算失败
            if (ObjectUtil.isEmpty(studentAbilityDataV2Request.getEndTime())){
                studentAbilityDataV2Request.setEndTime(DateUtil.parse(DateUtil.today()));
            }
        } else {
            calculatedDimId =  schoolYear + termName;
        }
        String dimId = schoolYear + termName;
        // 有缓存 计算完成 返回结果
        StudentAbilityModelNewResponse studentAbilityModel = studentAbilityModelService.getStudentAbilityModel(WebUtil.getCampusId());
        if (ObjectUtil.isEmpty(studentAbilityModel) || studentAbilityModel.getEnabled() == 0 || CollUtil.isEmpty(studentAbilityModel.getModelItemList())){
            log.info("【学生能力模型】-【获取数据】当前学校未配置学生能力模型，直接返回");
            studentAbilityModelDataFinalV2Response.setCalculateStatus(CalculateStatusEnum.NOT_CONFIG.getType());
            return studentAbilityModelDataFinalV2Response;
        }
        // 先查一下这个校区下有没有计算过
        String key = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_CAMPUS_CALCULATE_STATUS, campusId, calculatedDimId);
        Object calculateStatusObject = redisUtil.get(key);
        log.info("【学生能力模型】-【获取数据】校区计算结果，key：{}, value:{}", key, Convert.toStr(calculateStatusObject));

        // 没有缓存 触发一下计算 并返回计算中
        if (ObjectUtil.isEmpty(calculateStatusObject)){
            // 没有缓存 触发一下计算 并返回计算中
            CalculatedDTO calculatedDTO = new CalculatedDTO();
            calculatedDTO.setSchoolId(schoolId);
            calculatedDTO.setCampusId(campusId);
            calculatedDTO.setEndTime(studentAbilityDataV2Request.getEndTime());
            calculatedDTO.setSchoolYear(schoolYear);
            calculatedDTO.setTermName(isCurrentTerm ? null : termName);
            calculatedDTO.setIsCurrentSchoolYear(isCurrentYear);
            calculatedDTO.setTriggerCalculateScene(isCurrentYear ? TriggerCalculateSceneEnum.STUDENT_PORTRAIT_REALTIME.getType() : TriggerCalculateSceneEnum.STUDENT_PORTRAIT_HISTORY.getType());
            studentAbilityModelDataFinalV2Response.setCalculateStatus(CalculateStatusEnum.CALCULATING.getType());
            // 触发一下计算
            this.sendMqTask(calculatedDTO);
            log.info("【学生能力模型】-【获取数据】当前校区【未计算】，触发一次该校区的学生能力模型计算，请求参数：{}", JSONUtil.toJsonStr(calculatedDTO));
            return studentAbilityModelDataFinalV2Response;
        }
        int calculateStatus = Convert.toInt(calculateStatusObject);
        if (CalculateStatusEnum.CALCULATING.getType() == calculateStatus ||
                CalculateStatusEnum.FAIL.getType() == calculateStatus) {
            // 有缓存 计算中 返回计算中(计算失败暂时也算计算中，计算失败缓存10分钟，10分钟后会可尝试重新触发计算)
            studentAbilityModelDataFinalV2Response.setCalculateStatus(CalculateStatusEnum.CALCULATING.getType());
            log.info("【学生能力模型】-【获取数据】当前校区【计算中】或【计算失败】，直接返回");
            return studentAbilityModelDataFinalV2Response;
        }
        if (CalculateStatusEnum.SUCCESS.getType() != calculateStatus){
            studentAbilityModelDataFinalV2Response.setCalculateStatus(CalculateStatusEnum.CALCULATING.getType());
            log.error("【学生能力模型】-【获取数据】计算状态异常");
            return studentAbilityModelDataFinalV2Response;
        }
        studentAbilityModelDataFinalV2Response.setCalculateStatus(CalculateStatusEnum.SUCCESS.getType());
        List<StudentAbilityModelDataV2Response> studentAbilityModelDataV2Responses = new ArrayList<>();

        // 查一下学生信息
        StudentInfoVO studentInfoVO = saasStudentCacheManager.studentDetail(studentId);
        Assert.notNull(studentInfoVO, () -> new BizException("学生信息不存在，请联系管理员"));
        String classId = Convert.toStr(studentInfoVO.getClassId());
        String gradeId = Convert.toStr(studentInfoVO.getGradeId());
        Assert.isTrue(StrUtil.isAllNotBlank(classId, gradeId), () -> new BizException("学生信息异常，请联系管理员"));

        studentAbilityModelDataV2Responses = this.fillStudentData(studentAbilityModel, studentId, classId, gradeId, dimId);

        studentAbilityModelDataFinalV2Response.setStudentAbilityModelDataV2Responses(studentAbilityModelDataV2Responses);
        studentAbilityModelDataFinalV2Response.setAbilityModelName(studentAbilityModel.getAbilityModelName());

        return studentAbilityModelDataFinalV2Response;
    }
    @Override
    public List<StudentAbilityModelDataV2Response> fillStudentData(StudentAbilityModelNewResponse studentAbilityModel
            , String studentId
            , String classId
            , String gradeId
            , String dimId) {
        List<StudentAbilityModelItemV2Response> modelItemList = studentAbilityModel.getModelItemList();
        Integer contrastRange = studentAbilityModel.getContrastRange();
        List<StudentAbilityModelDataV2Response> studentAbilityModelDataV2Responses = new ArrayList<>();
        for (StudentAbilityModelItemV2Response d : modelItemList){
            List<StudentAbilityModelItemV2Response> subItemList = d.getSubItemList();
            StudentAbilityModelDataV2Response studentAbilityModelDataV2Response = new StudentAbilityModelDataV2Response();
            // 说明没有子级 只有1级
            if (CollUtil.isEmpty(subItemList)) {
                studentAbilityModelDataV2Response.setId(d.getItemId());
                studentAbilityModelDataV2Response.setName(d.getAbilityItemName());
                studentAbilityModelDataV2Response.setSortIndex(d.getSortIndex());
                Long itemId = d.getItemId();
                // 获取这个能力项的班级平均、班级最高、个人得分
                buildDataForItem(studentAbilityModelDataV2Response, dimId, itemId, studentId, contrastRange, classId, gradeId);
            }
            // 有子级 先构建子级的数据
            else {
                studentAbilityModelDataV2Response.setId(d.getItemId());
                studentAbilityModelDataV2Response.setName(d.getAbilityItemName());
                studentAbilityModelDataV2Response.setSortIndex(d.getSortIndex());
                List<StudentAbilityModelDataItemV2Response> studentAbilityModelDataItemV2Responses = new ArrayList<>();
                for (StudentAbilityModelItemV2Response subItem : subItemList) {
                    Long itemId = subItem.getItemId();
                    StudentAbilityModelDataItemV2Response studentAbilityModelDataItemV2Response = new StudentAbilityModelDataItemV2Response();
                    studentAbilityModelDataItemV2Response.setId(itemId);
                    studentAbilityModelDataItemV2Response.setName(subItem.getAbilityItemName());
                    studentAbilityModelDataItemV2Response.setSortIndex(subItem.getSortIndex());
                    // 获取这个能力项的班级平均、班级最高、个人得分
                    buildDataForItemChild(studentAbilityModelDataItemV2Response, dimId, itemId, studentId, contrastRange, classId, gradeId);
                    studentAbilityModelDataItemV2Responses.add(studentAbilityModelDataItemV2Response);
                }
                buildDataForParent(studentAbilityModelDataV2Response, studentAbilityModelDataItemV2Responses, dimId, contrastRange, classId, gradeId);
            }
            studentAbilityModelDataV2Responses.add(studentAbilityModelDataV2Response);
        }
        return studentAbilityModelDataV2Responses;
    }

    private void buildDataForParent(StudentAbilityModelDataV2Response studentAbilityModelDataV2Response, List<StudentAbilityModelDataItemV2Response> studentAbilityModelDataItemV2Responses, String dimId, Integer contrastRange, String classId, String gradeId) {
        if (CollUtil.isEmpty(studentAbilityModelDataItemV2Responses)){
            return;
        }
        // studentAbilityModelDataV2Response中的studentScore、avgScore、maxScore都是studentAbilityModelDataItemV2Responses里面的汇总
        studentAbilityModelDataV2Response.setStudentScore(studentAbilityModelDataItemV2Responses.stream().map(StudentAbilityModelDataItemV2Response::getStudentScore).reduce(BigDecimal.ZERO, BigDecimal::add));
        studentAbilityModelDataV2Response.setAvgScore(studentAbilityModelDataItemV2Responses.stream().map(StudentAbilityModelDataItemV2Response::getAvgScore).reduce(BigDecimal.ZERO, BigDecimal::add));
        String redisKey = RedisKeyConstants.ABILITY_MODEL_CLASS_MAX;
        if (contrastRange == 1) {
            redisKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_CLASS_MAX, dimId, studentAbilityModelDataV2Response.getId(), classId);
        } else if (contrastRange == 2) {
            redisKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_GRADE_MAX, dimId, studentAbilityModelDataV2Response.getId(), gradeId);
        }
        if (redisUtil.hasKey(redisKey)){
            BigDecimal maxScore = (BigDecimal) redisUtil.get(redisKey);
            log.info("【学生能力模型】-【获取数据】父能力项-班级/年级最高分，key：{}, value:{}", redisKey, Convert.toStr(maxScore));
            studentAbilityModelDataV2Response.setMaxScore((NumberUtil.round(maxScore, 2)));
        }

        studentAbilityModelDataV2Response.setStudentAbilityModelDataItemV2Responses(studentAbilityModelDataItemV2Responses);
    }

    private void buildDataForItem(StudentAbilityModelDataV2Response studentAbilityModelDataV2Response, String dimId, Long itemId, String studentId, Integer contrastRange, String classId, String gradeId){
        // 个人得分的redisKey
        String studentScoreKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_STUDENT_SCORE, dimId, itemId, studentId);
        if (redisUtil.hasKey(studentScoreKey)){
            BigDecimal studentScore = (BigDecimal) redisUtil.get(studentScoreKey);
            log.info("【学生能力模型】-【获取数据】学生个人得分，key：{}, value:{}", studentScoreKey, Convert.toStr(studentScore));
            studentAbilityModelDataV2Response.setStudentScore((NumberUtil.round(studentScore, 2)));
        }
        if (contrastRange == 1){
            String classAvgKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_CLASS_AVG, dimId, itemId, classId);
            String classMaxKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_CLASS_MAX, dimId, itemId, classId);
            if (redisUtil.hasKey(classAvgKey)){
                BigDecimal classAvgScore = (BigDecimal) redisUtil.get(classAvgKey);
                log.info("【学生能力模型】-【获取数据】班级平均得分，key：{}, value:{}", classAvgKey, Convert.toStr(classAvgScore));
                studentAbilityModelDataV2Response.setAvgScore((NumberUtil.round(classAvgScore, 2)));
            }
            if (redisUtil.hasKey(classMaxKey)){
                BigDecimal classMaxScore = (BigDecimal) redisUtil.get(classMaxKey);
                log.info("【学生能力模型】-【获取数据】班级最高得分，key：{}, value:{}", classMaxKey, Convert.toStr(classMaxScore));
                studentAbilityModelDataV2Response.setMaxScore((NumberUtil.round(classMaxScore, 2)));
            }
        }
        if (contrastRange == 2){
            String gradeAvgKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_GRADE_AVG, dimId, itemId, gradeId);
            String gradeMaxKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_GRADE_MAX, dimId, itemId, gradeId);
            if (redisUtil.hasKey(gradeAvgKey)){
                BigDecimal gradeAvgScore = (BigDecimal) redisUtil.get(gradeAvgKey);
                log.info("【学生能力模型】-【获取数据】年级平均得分，key：{}, value:{}", gradeAvgKey, Convert.toStr(gradeAvgScore));
                studentAbilityModelDataV2Response.setAvgScore((NumberUtil.round(gradeAvgScore, 2)));
            }
            if (redisUtil.hasKey(gradeMaxKey)){
                BigDecimal gradeMaxScore = (BigDecimal) redisUtil.get(gradeMaxKey);
                log.info("【学生能力模型】-【获取数据】年级最高得分，key：{}, value:{}", gradeMaxKey, Convert.toStr(gradeMaxScore));
                studentAbilityModelDataV2Response.setMaxScore((NumberUtil.round(gradeMaxScore, 2)));
            }
        }
    }
    private void buildDataForItemChild(StudentAbilityModelDataItemV2Response studentAbilityModelDataV2Response, String dimId, Long itemId, String studentId, Integer contrastRange, String classId, String gradeId){
        // 个人得分的redisKey
        String studentScoreKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_STUDENT_SCORE, dimId, itemId, studentId);
        if (redisUtil.hasKey(studentScoreKey)){
            BigDecimal studentScore = (BigDecimal) redisUtil.get(studentScoreKey);
            log.info("【学生能力模型】-【获取数据】学生个人得分，key：{}, value:{}", studentScoreKey, Convert.toStr(studentScore));
            studentAbilityModelDataV2Response.setStudentScore(NumberUtil.round(studentScore, 2));
        }
        if (contrastRange == 1){
            String classAvgKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_CLASS_AVG, dimId, itemId, classId);
            String classMaxKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_CLASS_MAX, dimId, itemId, classId);
            if (redisUtil.hasKey(classAvgKey)){
                BigDecimal classAvgScore = (BigDecimal) redisUtil.get(classAvgKey);
                log.info("【学生能力模型】-【获取数据】班级平均得分，key：{}, value:{}", classAvgKey, Convert.toStr(classAvgScore));
                studentAbilityModelDataV2Response.setAvgScore(NumberUtil.round(classAvgScore, 2));
            }
            if (redisUtil.hasKey(classMaxKey)){
                BigDecimal classMaxScore = (BigDecimal) redisUtil.get(classMaxKey);
                log.info("【学生能力模型】-【获取数据】班级最高得分，key：{}, value:{}", classMaxKey, Convert.toStr(classMaxScore));
                studentAbilityModelDataV2Response.setMaxScore(NumberUtil.round(classMaxScore, 2));
            }
        }
        if (contrastRange == 2){
            String gradeAvgKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_GRADE_AVG, dimId, itemId, gradeId);
            String gradeMaxKey = StrUtil.format(RedisKeyConstants.ABILITY_MODEL_GRADE_MAX, dimId, itemId, gradeId);
            if (redisUtil.hasKey(gradeAvgKey)){
                BigDecimal gradeAvgScore = (BigDecimal) redisUtil.get(gradeAvgKey);
                log.info("【学生能力模型】-【获取数据】年级平均得分，key：{}, value:{}", gradeAvgKey, Convert.toStr(gradeAvgScore));
                studentAbilityModelDataV2Response.setAvgScore(NumberUtil.round(gradeAvgScore, 2));
            }
            if (redisUtil.hasKey(gradeMaxKey)){
                BigDecimal gradeMaxScore = (BigDecimal) redisUtil.get(gradeMaxKey);
                log.info("【学生能力模型】-【获取数据】年级最高得分，key：{}, value:{}", gradeMaxKey, Convert.toStr(gradeMaxScore));
                studentAbilityModelDataV2Response.setMaxScore(NumberUtil.round(gradeMaxScore, 2));
            }
        }
    }
    private void sendMqTask(CalculatedDTO calculatedDTO) {
        rocketMQTemplate.asyncSend(studentModelCalculateTopic, calculatedDTO, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("【学生能力模型】-【发送计算请求】-【请求数据:{}】", JSONUtil.toJsonStr(calculatedDTO));
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("【学生能力模型】-【发送计算请求】 请求数据:{}", JSONUtil.toJsonStr(calculatedDTO), throwable);
            }
        });
    }
}
