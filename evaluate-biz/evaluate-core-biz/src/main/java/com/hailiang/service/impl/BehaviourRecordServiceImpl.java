package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hailiang.base.saasHolder.StudentHolder;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.convert.BehaviourRecordConvert;
import com.hailiang.convert.HelpBehaviourRecordConvert;
import com.hailiang.dingtalk.DailyNoticeClient;
import com.hailiang.enums.*;
import com.hailiang.enums.error.ReportErrorEnum;
import com.hailiang.enums.report.ReportBehaviourTypeEnum;
import com.hailiang.enums.report.ReportBusinessTypeEnum;
import com.hailiang.enums.report.SubmitTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.helper.DingTalkExchangeHelper;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.logic.ReportBehaviourRecordLogic;
import com.hailiang.manager.BehaviourHandleManager;
import com.hailiang.manager.EvaluateHelpBehaviourRecordManager;
import com.hailiang.manager.InitialScoreManager;
import com.hailiang.manager.SubjectInfoManager;
import com.hailiang.manager.TargetManager;
import com.hailiang.mapper.BehaviourRecordMapper;
import com.hailiang.mapper.TargetMapper;
import com.hailiang.model.dto.CalculateScoreDTO;
import com.hailiang.model.dto.QueryBehaviourScoreDTO;
import com.hailiang.model.dto.StudentBehaviourScoreDTO;
import com.hailiang.model.dto.behaviour.help.HelpBehaviourRecordQueryDTO;
import com.hailiang.model.dto.query.*;
import com.hailiang.model.entity.*;
import com.hailiang.model.medal.dto.StudentMedalQueryDTO;
import com.hailiang.model.medal.vo.MedalInfoVO;
import com.hailiang.model.processeva.response.ProcessEvaRecordResponse;
import com.hailiang.model.query.OptionIdQuery;
import com.hailiang.model.report.Pair;
import com.hailiang.model.report.dto.PageStudentRecordQueryDTO;
import com.hailiang.model.report.entity.SubjectEvaluationDimTargetBusinessMergePO;
import com.hailiang.model.report.entity.SubjectInfoPO;
import com.hailiang.model.report.vo.ReportModuleScoreStatisticsVO;
import com.hailiang.model.report.vo.ReportModuleScoreVO;
import com.hailiang.model.request.EvaluateFixDataRequest;
import com.hailiang.model.response.EvaluateFixDataResponse;
import com.hailiang.model.vo.*;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.dto.educational.PageEduStudentQueryDTO;
import com.hailiang.remote.saas.dto.student.StudentByIdQuery;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.vo.StudentInfo1VO;
import com.hailiang.service.*;
import com.hailiang.util.EasyExcelUtil;
import com.hailiang.util.NumUtil;
import com.hailiang.util.WebUtil;
import java.io.IOException;
import java.util.function.Function;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BehaviourRecordServiceImpl extends ServiceImpl<BehaviourRecordMapper, BehaviourRecord> implements
        BehaviourRecordService {

    @Lazy
    @Resource
    private TargetManager targetManager;

    /**
     * 饼图指标项百分比保留小数位数
     */
    private final Integer BEHAVIOUR_RATE_SIZE = 1;

    /**
     * 饼图分组百分比保留小数位数
     */
    private final Integer BEHAVIOUR_GROUP_RATE_SIZE = 1;
    /**
     * 饼图表扬列表、待改进列表、无分数列表行为数量 -1 表示不做限制
     */
    private final Integer BEHAVIOUR_LIST_SIZE = -1;

    /**
     * 饼图行为数量(饼图里展示的指标数量)
     */
    private final Integer BEHAVIOUR_ROUND_LIST_SIZE = 10;
    @Resource
    private BehaviourRecordConvert convert;
    @Autowired(required = false)
    private BehaviourRecordMapper behaviourRecordMapper;
    @Autowired
    private BasicInfoService basicInfoService;
    @Autowired
    private TargetService targetService;
    @Autowired
    private TargetGroupService targetGroupService;
    @Lazy
    @Resource
    private BehaviourRecordManager behaviourRecordManager;
    @Resource
    private ReportBehaviourRecordLogic reportBehaviourRecordLogic;
    @Autowired(required = false)
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private TargetMapper targetMapper;
    @Resource
    private ReportReviewDetailService reportReviewDetailService;
    @Resource
    private ReportReviewTaskService reportReviewTaskService;
    @Autowired
    private MedalUserAcquireRecordService medalUserAcquireRecordService;
    @Resource
    private EvaluateHelpBehaviourRecordManager helpBehaviourRecordManager;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private SaasStudentManager saasStudentManager;
    @Autowired
    private ReportBehaviourRecordServiceImpl reportBehaviourRecordService;
    @Resource
    private InitialScoreManager initialScoreManager;
    @Resource
    private BehaviourHandleManager behaviourHandleManager;
    @Resource
    private ReportPushRuleService reportPushRuleService;
    @Resource
    private DailyNoticeClient dailyNoticeClient;
    @Resource
    private DingTalkExchangeHelper dingTalkExchangeHelper;
    @Resource
    private SubjectInfoManager subjectInfoManager;
    /**
     * 获得本周一0点时间
     *
     * @return
     */
    public static Date getTimesWeekMorning() {
        Calendar cal = Calendar.getInstance();
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONDAY), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return cal.getTime();
    }

//    @Override
//    public boolean saveBehaviourRecords(List<BehaviourRecordSaveDTO> dtos) {
//        if (CollUtil.isEmpty(dtos)) {
//            return false;
//        }
//        return this.saveBatch(convert.toBehaviourRecords(dtos));
//    }

    /**
     * 综合得分
     *
     * @param behaviourRecordQueryDTO
     * @return
     */
    @Override
    public BehaviourRecordSynthesisScoreVO getSynthesisScore(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
        TimeInterval timer = DateUtil.timer();
        BehaviourRecordSynthesisScoreWithBehaviourListVO scoreVO = getSynthesisScoreWithoutRank(
                behaviourRecordQueryDTO);
        // 全班得分排名list
        List<BehaviourRecordSynthesisScoreRankVO> ranks = this.getRankList(behaviourRecordQueryDTO);
        //超越了多少学生人数
        // 这个分数不加分配分 用于比较班内总分排比
        BigDecimal tempSumScore = scoreVO.getSumScore();
        List<BehaviourRecord> behaviourRecords = scoreVO.getBehaviourRecordList();
        Integer passPeopleNum = ObjectUtil.isNull(tempSumScore)
                ? null
                :
                        ranks.stream()
                                .filter(d -> Constant.NEGATIVE_ONE.equals(d.getSumScore().compareTo(tempSumScore)))
                                .collect(Collectors.toList()).size();
        //超越全班百分比 计算公式为 超越人数/全部总人数，如果就一个人，正分超越100%，负分0%
        String surpassRate = this.getSurpassRate(passPeopleNum, ranks, ranks.size());
        scoreVO.setSurpassRate(surpassRate);
        //只有一个人且是一条行为情况且分数为0的情况
        if (Constant.ZERO.equals(passPeopleNum) && Constant.ONE.equals(behaviourRecords.size())) {
            scoreVO.setSurpassRate(Constant.ONE.equals(behaviourRecords.get(0).getScoreType()) ?
                    new BigDecimal(Constant.HUNDRED).toPlainString() + Constant.PERCENT :
                    BigDecimal.ZERO.toPlainString() + Constant.PERCENT);
        }
        log.info("学生画像-综合得分 返回结果：[{}],耗时:{}", JSONUtil.toJsonStr(scoreVO), timer.intervalMs());
        return scoreVO;
    }

    /**
     * 综合得分
     *
     * @param behaviourRecordQueryDTO
     * @return
     */
    @Override
    public BehaviourRecordSynthesisScoreWithBehaviourListVO getSynthesisScoreWithoutRank(
            BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();
        BehaviourRecordSynthesisScoreWithBehaviourListVO scoreVO = new BehaviourRecordSynthesisScoreWithBehaviourListVO();

        LambdaQueryWrapper<BehaviourRecord> queryWrapper = new LambdaQueryWrapper<>();
        //不需要的行为记录表id
        List<Long> unNeedBehavioutIdList = new ArrayList<>();
        //不需要的帮扶表id
        List<Long> unNeedHelpBehavioutIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(behaviourRecordQueryDTO.getReviewDetailId())) {
            List<ReportBehaviourRecord> reportBehaviourRecords = reportBehaviourRecordService.listNoSendRecordByDetailList(
                    Long.valueOf(behaviourRecordQueryDTO.getReviewDetailId()));
            reportBehaviourRecords.forEach(reportBehaviourRecord -> {
                if (Objects.equals(reportBehaviourRecord.getRecordType(),
                        ReportBehaviourTypeEnum.BEHAVIOUR_RECORD.getCode())) {
                    unNeedBehavioutIdList.add(reportBehaviourRecord.getBehaviourRecordId());
                } else if (Objects.equals(reportBehaviourRecord.getRecordType(),
                        ReportBehaviourTypeEnum.HELP_BEHAVIOUR_RECORD.getCode())) {
                    unNeedHelpBehavioutIdList.add(reportBehaviourRecord.getBehaviourRecordId());
                }
            });
        }
        queryWrapper.select(BehaviourRecord::getClassId, BehaviourRecord::getStudentId, BehaviourRecord::getInfoName,
                        BehaviourRecord::getScore, BehaviourRecord::getScoreType)
//                .eq(StrUtil.isNotBlank(WebUtil.getTenantId()), BehaviourRecord::getTenantId, WebUtil.getTenantId())
//                .eq(StrUtil.isNotBlank(WebUtil.getSchoolId()), BehaviourRecord::getSchoolId, WebUtil.getSchoolId())
                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getCampusId()), BehaviourRecord::getCampusId,
                        behaviourRecordQueryDTO.getCampusId())
                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getClassId()), BehaviourRecord::getClassId,
                        behaviourRecordQueryDTO.getClassId())
                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getStudentId()), BehaviourRecord::getStudentId,
                        behaviourRecordQueryDTO.getStudentId())
                .ge(ObjectUtil.isNotNull(behaviourRecordQueryDTO.getStartTime()), BehaviourRecord::getSubmitTime,
                        behaviourRecordQueryDTO.getStartTime())
                .le(ObjectUtil.isNotNull(behaviourRecordQueryDTO.getEndTime()), BehaviourRecord::getSubmitTime,
                        behaviourRecordQueryDTO.getEndTime())
                .notIn(CollectionUtils.isNotEmpty(unNeedBehavioutIdList), BehaviourRecord::getId, unNeedBehavioutIdList)
                .eq(BehaviourRecord::getIsScore, Constant.YES);
        //学生筛选时间内所有的有分值的行为表现
        List<BehaviourRecord> behaviourRecords = new ArrayList<>(behaviourRecordMapper.selectList(queryWrapper));
        // 过滤无需统计的行为记录
        behaviourRecords = behaviourRecords
                .stream()
                .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                .collect(Collectors.toList());

        //加入帮扶积分
        HelpBehaviourRecordQueryDTO helpBehaviourRecordQueryDTO = new HelpBehaviourRecordQueryDTO();
        helpBehaviourRecordQueryDTO.setStudentId(behaviourRecordQueryDTO.getStudentId());
        helpBehaviourRecordQueryDTO.setStartTime(behaviourRecordQueryDTO.getStartTime());
        helpBehaviourRecordQueryDTO.setEndTime(behaviourRecordQueryDTO.getEndTime());
        helpBehaviourRecordQueryDTO.setUnNeedHelpBehaviourRecordIds(unNeedHelpBehavioutIdList);
        List<EvaluateHelpBehaviourRecordPO> evaluateHelpBehaviourRecordPOS = helpBehaviourRecordManager.queryByCondition(
                helpBehaviourRecordQueryDTO);
        if (CollectionUtils.isNotEmpty(evaluateHelpBehaviourRecordPOS)) {
            List<BehaviourRecord> behaviourRecordList = HelpBehaviourRecordConvert.toBehaviourRecordList(
                    evaluateHelpBehaviourRecordPOS);
            // 过滤无需统计的帮扶行为记录
            behaviourRecordList = behaviourRecordList
                    .stream()
                    .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                    .collect(Collectors.toList());

            behaviourRecords.addAll(behaviourRecordList);
        }
        boolean isAllAddZero = true;
        boolean isAllSubtractZero = true;
        //加分
        List<BehaviourRecord> addScoreBehaviourRecordList =
                behaviourRecords.stream().filter(d -> Constant.ONE.equals(d.getScoreType()))
                        .collect(Collectors.toList());
        for (BehaviourRecord d : addScoreBehaviourRecordList) {
            if (d.getScore().compareTo(BigDecimal.ZERO) != 0) {
                isAllAddZero = false;
                break;
            }
        }
        BigDecimal addScore = Constant.ZERO.equals(addScoreBehaviourRecordList.size()) ? null :
                addScoreBehaviourRecordList.stream().map(BehaviourRecord::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add); // NOSONAR
        //减分
        List<BehaviourRecord> subtractScoreBehaviourRecordList =
                behaviourRecords.stream().filter(d -> Constant.TWO.equals(d.getScoreType()))
                        .collect(Collectors.toList());
        for (BehaviourRecord d : subtractScoreBehaviourRecordList) {
            if (d.getScore().compareTo(BigDecimal.ZERO) != 0) {
                isAllSubtractZero = false;
                break;
            }
        }
        BigDecimal subtractScore = Constant.ZERO.equals(subtractScoreBehaviourRecordList.size()) ? null :
                subtractScoreBehaviourRecordList.stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO,
                        BigDecimal::add);

        TermQuery termQuery = new TermQuery();
        termQuery.setCampusId(Convert.toLong(behaviourRecordQueryDTO.getCampusId()));
        termQuery.setSchoolId(Convert.toLong(behaviourRecordQueryDTO.getSchoolId()));
        List<TermVo> termVos;
        if (CharSequenceUtil.isBlank(behaviourRecordQueryDTO.getSchoolId())
                || CharSequenceUtil.isBlank(behaviourRecordQueryDTO.getCampusId())) {
            log.error("getSynthesisScoreWithoutRank，学校id或校区id为空，无法获取当前学期");
            termVos = new ArrayList<>();
        } else {
            termVos = basicInfoRemote.queryTermList(termQuery);
        }
        Optional<TermVo> first = termVos.stream().filter(TermVo::isCurrentTerm).findFirst();
        InitialScorePO oneByBaseInfo = new InitialScorePO();
        if (first.isPresent()) {
            TermVo termVo = first.get();
            oneByBaseInfo = initialScoreManager.getOneByBaseInfo(behaviourRecordQueryDTO.getCampusId(),
                    termVo.getSchoolYear(),
                    termVo.getTermName(),
                    ModuleEnum.OTHER.getCode());
        }
        BigDecimal initScore = BigDecimal.ZERO;
        if (BeanUtil.isNotEmpty(oneByBaseInfo) && ObjectUtil.isNotNull(oneByBaseInfo.getInitialScore())) {
            initScore = oneByBaseInfo.getInitialScore();
        }

        // TODO: 2024/11/18 初始分重新设计，用新的初始分，暂时注释
        // 分配的积分
//        List<InitialScoreAllocation> initialScoreAllocations =
//                initialScoreAllocationManager.list(new LambdaQueryWrapper<InitialScoreAllocation>()
//                        .eq(Objects.nonNull(behaviourRecordQueryDTO.getCampusId()), InitialScoreAllocation::getCampusId, behaviourRecordQueryDTO.getCampusId())
//                        .eq(InitialScoreAllocation::getStudentId, behaviourRecordQueryDTO.getStudentId())
//                        .between(InitialScoreAllocation::getCreateTime, behaviourRecordQueryDTO.getStartTime(),
//                                behaviourRecordQueryDTO.getEndTime()));
        if (CollUtil.isEmpty(behaviourRecords) && BigDecimal.ZERO.compareTo(initScore) == 0) {
            return new BehaviourRecordSynthesisScoreWithBehaviourListVO();
        }

//        //加分(分配分)
//        List<InitialScoreAllocation> addInitialScoreAllocations =
//                initialScoreAllocations.stream().filter(d -> Constant.ONE.equals(d.getScoreType())).collect(Collectors.toList());
//        BigDecimal addInitialScore = Constant.ZERO.equals(addInitialScoreAllocations.size()) ? null :
//                new BigDecimal(addInitialScoreAllocations.stream().map(InitialScoreAllocation::getInitialScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO).stripTrailingZeros().toPlainString());
//
//        //减分(分配分)
//        List<InitialScoreAllocation> subInitialScoreAllocations =
//                initialScoreAllocations.stream().filter(d -> Constant.TWO.equals(d.getScoreType())).collect(Collectors.toList());
//        BigDecimal subInitialScore = Constant.ZERO.equals(subInitialScoreAllocations.size()) ? null :
//                new BigDecimal(subInitialScoreAllocations.stream().map(InitialScoreAllocation::getInitialScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO).stripTrailingZeros().toPlainString());
//        // 返回给前端的加减分跟分配分没有关系 注释掉

        // 总得分和分配分有关系
        BigDecimal sumScore;
        if (ObjectUtil.isNull(addScore) && ObjectUtil.isNotNull(subtractScore)) {
            sumScore = subtractScore;
        } else if (ObjectUtil.isNull(subtractScore) && ObjectUtil.isNotNull(addScore)) {
            sumScore = addScore;
        } else if (ObjectUtil.isNotNull(subtractScore) && ObjectUtil.isNotNull(addScore)) {
            sumScore = addScore.add(subtractScore); // NOSONAR
        } else {
            sumScore = null;
        }

        // 总分在加一下分配分
//        if (Objects.nonNull(addInitialScore)) {
        if (Objects.isNull(sumScore)) {
            sumScore = BigDecimal.ZERO;
        }
        sumScore = sumScore.add(initScore);
//        }
//        if (Objects.nonNull(subInitialScore)) {
//            if (Objects.isNull(sumScore)) {
//                sumScore = BigDecimal.ZERO;
//            }
//            sumScore = sumScore.add(subInitialScore);
//        }
        // 全部加分分值+全部减分分值绝对值
        BigDecimal totalScore;
        if (ObjectUtil.isNull(addScore) && ObjectUtil.isNotNull(subtractScore)) {
            totalScore = subtractScore;
        } else if (ObjectUtil.isNull(subtractScore) && ObjectUtil.isNotNull(addScore)) {
            totalScore = addScore;
        } else if (ObjectUtil.isNotNull(subtractScore) && ObjectUtil.isNotNull(addScore)) {
            totalScore = addScore.subtract(subtractScore); // NOSONAR
        } else {
            totalScore = null;
        }
        //加分占比  加分总分/（全部加分分值+全部减分分值绝对值）
        BigDecimal addRate = ObjectUtil.isNull(addScore)
                ? null
                : Constant.ZERO.equals(totalScore.compareTo(BigDecimal.ZERO)) // NOSONAR
                        ? Constant.ONE.equals(behaviourRecords.size()) ? BigDecimal.ONE : BigDecimal.ZERO
                        :
                                new BigDecimal(addScore.divide(totalScore, Constant.THREE, RoundingMode.HALF_UP)
                                        .stripTrailingZeros().toPlainString());
        String addScoreRate = ObjectUtil.isNull(addRate) || isAllAddZero ? null : NumUtil.convertToPercent(addRate,
                Constant.ONE);
        //减分占比 减分总分/（全部加分分值+全部减分分值绝对值）
        String subtractScoreRate = ObjectUtil.isNull(subtractScore) || isAllSubtractZero
                ? null
                : (ObjectUtil.isNotNull(addRate)
                        ? NumUtil.convertToPercent(BigDecimal.ONE.subtract(addRate), Constant.ONE)
                        : NumUtil.convertToPercent(BigDecimal.ONE, Constant.ONE));

        scoreVO.setSumScore(ObjectUtil.isNull(sumScore) ? null
                        : new BigDecimal(sumScore.setScale(Constant.TWO).stripTrailingZeros().toPlainString())) // NOSONAR
                .setAddScore(ObjectUtil.isNull(addScore) || isAllAddZero ? null : new BigDecimal(
                        addScore.setScale(Constant.TWO).stripTrailingZeros().toPlainString())) // NOSONAR
                .setAddScoreRate(addScoreRate)
                .setSubtractScore(ObjectUtil.isNull(subtractScore) || isAllSubtractZero ? null : new BigDecimal(
                        subtractScore.setScale(Constant.TWO).stripTrailingZeros().toPlainString())) // NOSONAR
                .setSubtractScoreRate(subtractScoreRate);
        scoreVO.setBehaviourRecordList(behaviourRecords);
        log.info("【学生画像】-【计算综合得分】- 【耗时】：[{}]", TIME_INTERVAL.intervalMs());
        return scoreVO;

    }

    private List<BehaviourRecordSynthesisScoreRankVO> getRankList(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
        List<BehaviourRecordSynthesisScoreRankVO> ranks = new ArrayList<>();
        // 行为记录数据 已经按照学生分组过
        List<BehaviourRecord> behaviourRecordsList = listStudentRank(behaviourRecordQueryDTO,
                DateUtil.dateNew(behaviourRecordQueryDTO.getStartTime()),
                DateUtil.dateNew(behaviourRecordQueryDTO.getEndTime()));
        InitialScorePO oneByBaseInfo = initialScoreManager.getOneByBaseInfo(behaviourRecordQueryDTO.getCampusId(),
                behaviourRecordQueryDTO.getSchoolYear(),
                behaviourRecordQueryDTO.getTermName(),
                ModuleEnum.OTHER.getCode());

        BigDecimal initScore = BigDecimal.ZERO;
        if (BeanUtil.isNotEmpty(oneByBaseInfo) && ObjectUtil.isNotNull(oneByBaseInfo.getInitialScore())) {
            initScore = oneByBaseInfo.getInitialScore();
        }
        // TODO: 2024/11/18 初始分重新设计，用新的初始分，暂时注释
//        List<InitialScoreAllocation> initialList = listInitialRank(behaviourRecordQueryDTO,
//                DateUtil.dateNew(behaviourRecordQueryDTO.getStartTime()),
//                DateUtil.dateNew(behaviourRecordQueryDTO.getEndTime()));
        Map<String, BigDecimal> behaviourRecordsMap = CollStreamUtil.toMap(behaviourRecordsList,
                BehaviourRecord::getStudentId, BehaviourRecord::getScore);
//        Map<String, BigDecimal> initialMap = CollStreamUtil.toMap(initialList, InitialScoreAllocation::getStudentId,
//                InitialScoreAllocation::getInitialScore);
        // 查询班级下的学生
        List<StudentInfo1VO> studentInfo1VOS = saasStudentManager.queryStudentPageByClassId(WebUtil.getSchoolIdLong(),
                Convert.toLong(behaviourRecordQueryDTO.getClassId()));
        for (StudentInfo1VO d : studentInfo1VOS) {
            BehaviourRecordSynthesisScoreRankVO rank = new BehaviourRecordSynthesisScoreRankVO();
            BigDecimal behaviourScore = behaviourRecordsMap.getOrDefault(Convert.toStr(d.getId()), BigDecimal.ZERO);
//            BigDecimal initialScore = initialMap.getOrDefault(Convert.toStr(d.getId()), BigDecimal.ZERO);
            rank.setStudentId(d.getId());
            rank.setStudentName(d.getStudentName());
            rank.setBehaviourScore(behaviourScore);
            rank.setInitialScore(initScore);
            rank.setSumScore(NumberUtil.add(behaviourScore, initScore));

            ranks.add(rank);
        }
        return ranks;
    }

    private String getSurpassRate(Integer passPeopleNum, List<BehaviourRecordSynthesisScoreRankVO> ranks,
                                  Integer rankSize) {
        if (ObjectUtil.isNull(passPeopleNum) || rankSize == 0) {
            return null;
        }
        if (Constant.ONE.equals(ranks.size())) {
            if (Constant.ONE.equals(ranks.get(0).getSumScore().compareTo(BigDecimal.ZERO)) || Constant.ZERO.equals(
                    ranks.get(0).getSumScore().compareTo(BigDecimal.ZERO))) {
                return BigDecimal.ONE.multiply(BigDecimal.valueOf(Constant.HUNDRED)).stripTrailingZeros()
                        .toPlainString() + Constant.PERCENT;
            } else {
                return BigDecimal.ZERO.toPlainString() + Constant.PERCENT;
            }
        } else {
            return new BigDecimal(passPeopleNum)
                    .divide(new BigDecimal(rankSize), Constant.THREE, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(Constant.HUNDRED))
                    .setScale(Constant.ONE).stripTrailingZeros().toPlainString() + Constant.PERCENT;
        }
    }

//    /**
//     * 综合素质
//     *
//     * @param behaviourRecordQueryDTO
//     * @return
//     */
//    @Override
//    public List<BehaviourComprehensiveQualityVO> listComprehensiveQuality(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
//        ArrayList<BehaviourComprehensiveQualityVO> behaviourComprehensiveQualityVOArrayList = new ArrayList<>();
//        //班级所有同学行为集合
//        QueryWrapper<BehaviourRecord> testWrapper = new QueryWrapper<>();
//        testWrapper.select("student_id, module_code,score")
//                .eq(StrUtil.isNotBlank(WebUtil.getTenantId()), "tenant_id", WebUtil.getTenantId())
//                .eq(StrUtil.isNotBlank(WebUtil.getSchoolId()), "school_id", WebUtil.getSchoolId())
//                .eq(StrUtil.isNotBlank(WebUtil.getCampusId()), "campus_id", WebUtil.getCampusId())
//                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getClassId()), "class_id",
//                        behaviourRecordQueryDTO.getClassId())
//                .ge(ObjectUtil.isNotNull(behaviourRecordQueryDTO.getStartTime()), "submit_time",
//                        behaviourRecordQueryDTO.getStartTime())
//                .le(ObjectUtil.isNotNull(behaviourRecordQueryDTO.getEndTime()), "submit_time",
//                        behaviourRecordQueryDTO.getEndTime())
//                .eq("is_score", Constant.YES);
//        List<BehaviourRecord> classRecordList = behaviourRecordMapper.selectList(testWrapper);
//        if (CollectionUtil.isEmpty(classRecordList)) {
//            return Collections.EMPTY_LIST;
//        }
//        //班级人数
//        int classPeopleCount =
//                classRecordList.stream().map(BehaviourRecord::getStudentId).distinct().collect(Collectors.toList()).size();
//        //按学号分组
//        Map<String, List<BehaviourRecord>> listMap =
//                classRecordList.parallelStream().collect(Collectors.groupingBy(BehaviourRecord::getStudentId));
//        //获取各模块班级最高分
//        HashMap<Integer, BigDecimal> moduleMaxScoreMap = getModuleMaxScore(listMap);
//
//        /***
//         -------------------------------------------------------德育-------------------------------------------------------------*/
//        BehaviourComprehensiveQualityVO moralComprehensiveQualityVO = new BehaviourComprehensiveQualityVO()
//                .setModuleCode(ModuleEnum.MORAL.getCode())
//                .setModuleName(ModuleEnum.getModuleName(ModuleEnum.MORAL.getCode()))
//                .setStudentScore(classRecordList.stream().filter(single -> (single.getStudentId().equals(behaviourRecordQueryDTO.getStudentId())
//                                && single.getModuleCode().equals(ModuleEnum.MORAL.getCode()))).collect(Collectors.toList())
//                        .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO))
//                .setMaxScore(moduleMaxScoreMap.get(ModuleEnum.MORAL.getCode()))
//                .setAvgScore(classRecordList.stream().filter(single -> (single.getModuleCode().equals(ModuleEnum.MORAL.getCode()))).collect(Collectors.toList())
//                        .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add)
//                        .divide(BigDecimal.valueOf(classPeopleCount), Constant.TWO, RoundingMode.HALF_UP));
//        behaviourComprehensiveQualityVOArrayList.add(moralComprehensiveQualityVO);
//
//        /***
//         * -------------------------------------------------------------智育-------------------------------------------------------------*/
//        BehaviourComprehensiveQualityVO wisdomComprehensiveQualityVO = new BehaviourComprehensiveQualityVO()
//                .setModuleCode(ModuleEnum.WISDOM.getCode())
//                .setModuleName(ModuleEnum.getModuleName(ModuleEnum.WISDOM.getCode()))
//                .setStudentScore(classRecordList.stream().filter(single -> (single.getStudentId().equals(behaviourRecordQueryDTO.getStudentId())
//                                && single.getModuleCode().equals(ModuleEnum.WISDOM.getCode()))).collect(Collectors.toList())
//                        .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO))
//                .setMaxScore(moduleMaxScoreMap.get(ModuleEnum.WISDOM.getCode()))
//                .setAvgScore(classRecordList.stream().filter(single -> (single.getModuleCode().equals(ModuleEnum.WISDOM.getCode()))).collect(Collectors.toList())
//                        .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add)
//                        .divide(BigDecimal.valueOf(classPeopleCount), Constant.TWO, RoundingMode.HALF_UP));
//        behaviourComprehensiveQualityVOArrayList.add(wisdomComprehensiveQualityVO);
//
//        /***
//         ----------------------------------------------------------体育-------------------------------------------------------------*/
//        BehaviourComprehensiveQualityVO sportComprehensiveQualityVO3 = new BehaviourComprehensiveQualityVO()
//                .setModuleCode(ModuleEnum.SPORT.getCode())
//                .setModuleName(ModuleEnum.getModuleName(ModuleEnum.SPORT.getCode()))
//                .setStudentScore(classRecordList.stream().filter(single -> (single.getStudentId().equals(behaviourRecordQueryDTO.getStudentId())
//                                && single.getModuleCode().equals(ModuleEnum.SPORT.getCode()))).collect(Collectors.toList())
//                        .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO))
//                .setMaxScore(moduleMaxScoreMap.get(ModuleEnum.SPORT.getCode()))
//                .setAvgScore(classRecordList.stream().filter(single -> (single.getModuleCode().equals(ModuleEnum.SPORT.getCode()))).collect(Collectors.toList())
//                        .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add)
//                        .divide(BigDecimal.valueOf(classPeopleCount), Constant.TWO, RoundingMode.HALF_UP));
//        behaviourComprehensiveQualityVOArrayList.add(sportComprehensiveQualityVO3);
//
//        /***
//         -----------------------------------------------------------美育-------------------------------------------------------------*/
//        BehaviourComprehensiveQualityVO prettyComprehensiveQualityVO4 = new BehaviourComprehensiveQualityVO()
//                .setModuleCode(ModuleEnum.PRETTY.getCode())
//                .setModuleName(ModuleEnum.getModuleName(ModuleEnum.PRETTY.getCode()))
//                .setStudentScore(classRecordList.stream().filter(single -> (single.getStudentId().equals(behaviourRecordQueryDTO.getStudentId())
//                                && single.getModuleCode().equals(ModuleEnum.PRETTY.getCode()))).collect(Collectors.toList())
//                        .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO))
//                .setMaxScore(moduleMaxScoreMap.get(ModuleEnum.PRETTY.getCode()))
//                .setAvgScore(classRecordList.stream().filter(single -> (single.getModuleCode().equals(ModuleEnum.PRETTY.getCode()))).collect(Collectors.toList())
//                        .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add)
//                        .divide(BigDecimal.valueOf(classPeopleCount), Constant.TWO, RoundingMode.HALF_UP));
//        behaviourComprehensiveQualityVOArrayList.add(prettyComprehensiveQualityVO4);
//
//        /***
//         -------------------------------------------------------------劳育-------------------------------------------------------------*/
//        BehaviourComprehensiveQualityVO workComprehensiveQualityVO5 = new BehaviourComprehensiveQualityVO()
//                .setModuleCode(ModuleEnum.WORK.getCode())
//                .setModuleName(ModuleEnum.getModuleName(ModuleEnum.WORK.getCode()))
//                .setStudentScore(classRecordList.stream().filter(single -> (single.getStudentId().equals(behaviourRecordQueryDTO.getStudentId())
//                                && single.getModuleCode().equals(ModuleEnum.WORK.getCode()))).collect(Collectors.toList())
//                        .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO))
//                .setMaxScore(moduleMaxScoreMap.get(ModuleEnum.WORK.getCode()))
//                .setAvgScore(classRecordList.stream().filter(single -> (single.getModuleCode().equals(ModuleEnum.WORK.getCode()))).collect(Collectors.toList())
//                        .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add)
//                        .divide(BigDecimal.valueOf(classPeopleCount), Constant.TWO, RoundingMode.HALF_UP));
//        behaviourComprehensiveQualityVOArrayList.add(workComprehensiveQualityVO5);
//        log.info("学生画像-综合素质 返回结果：[{}]", JSONUtil.toJsonStr(behaviourComprehensiveQualityVOArrayList));
//        return behaviourComprehensiveQualityVOArrayList;
//    }

    /**
     * 获取各模块班级最高分 方法无调用 不设置其他育得分
     *
     * @param listMap
     */
    private HashMap<Integer, BigDecimal> getModuleMaxScore(Map<String, List<BehaviourRecord>> listMap) {
        HashMap<Integer, BigDecimal> integerBigDecimalHashMap = new HashMap<>();
        TreeSet<BigDecimal> moralBigDecimals = new TreeSet<>();
        TreeSet<BigDecimal> wisdomBigDecimals = new TreeSet<>();
        TreeSet<BigDecimal> sportBigDecimals = new TreeSet<>();
        TreeSet<BigDecimal> prettyBigDecimals = new TreeSet<>();
        TreeSet<BigDecimal> workBigDecimals = new TreeSet<>();
        for (String studentId : listMap.keySet()) {
            //德育
            BigDecimal moralBigDecimal =
                    listMap.get(studentId).stream()
                            .filter(single -> single.getModuleCode().equals(ModuleEnum.MORAL.getCode()))
                            .collect(Collectors.toList())
                            .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO);
            moralBigDecimals.add(moralBigDecimal);
            //智育
            BigDecimal wisdomBigDecimal =
                    listMap.get(studentId).stream()
                            .filter(single -> single.getModuleCode().equals(ModuleEnum.WISDOM.getCode()))
                            .collect(Collectors.toList())
                            .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO);
            wisdomBigDecimals.add(wisdomBigDecimal);
            //体育
            BigDecimal sportBigDecimal =
                    listMap.get(studentId).stream()
                            .filter(single -> single.getModuleCode().equals(ModuleEnum.SPORT.getCode()))
                            .collect(Collectors.toList())
                            .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO);
            sportBigDecimals.add(sportBigDecimal);
            //美育
            BigDecimal prettyBigDecimal =
                    listMap.get(studentId).stream()
                            .filter(single -> single.getModuleCode().equals(ModuleEnum.PRETTY.getCode()))
                            .collect(Collectors.toList())
                            .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO);
            prettyBigDecimals.add(prettyBigDecimal);
            //劳育
            BigDecimal workBigDecimal =
                    listMap.get(studentId).stream()
                            .filter(single -> single.getModuleCode().equals(ModuleEnum.WORK.getCode()))
                            .collect(Collectors.toList())
                            .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO);
            workBigDecimals.add(workBigDecimal);
        }
        integerBigDecimalHashMap.put(ModuleEnum.MORAL.getCode(), moralBigDecimals.last());
        integerBigDecimalHashMap.put(ModuleEnum.WISDOM.getCode(), wisdomBigDecimals.last());
        integerBigDecimalHashMap.put(ModuleEnum.SPORT.getCode(), sportBigDecimals.last());
        integerBigDecimalHashMap.put(ModuleEnum.PRETTY.getCode(), prettyBigDecimals.last());
        integerBigDecimalHashMap.put(ModuleEnum.WORK.getCode(), workBigDecimals.last());
        return integerBigDecimalHashMap;
    }

//    /**
//     * 行为表现
//     *
//     * @param behaviourRecordQueryDTO
//     * @return
//     */
//    @Override
//    public BehavioralExpressionVO getBehavioralExpression(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
//        BehavioralExpressionVO behavioralExpressionVO = new BehavioralExpressionVO();
//        // 所有行为分值从高到低
//        List<BehaviourRecord> behaviourRecords = listSumScore(behaviourRecordQueryDTO);
//        // 获取名称为选项的指标名称Map
//        Map<Long, String> targetNameMap = new HashMap<>();
//        List<Long> targetIds =
//                behaviourRecords.stream().filter(s -> InfoTypeEnum.OPTION.getCode().equals(s.getInfoType())).map(BehaviourRecord::getTargetId).collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(targetIds)) {
//            List<Target> targets = targetMapper.listTargetByIds(targetIds);
//            targetNameMap = targets.stream().collect(Collectors.toMap(Target::getId, Target::getTargetName));
//        }
//
//
//        // 加分项行为名称（筛选掉表扬中的0分（0分不加入表扬分类中））
//        List<BehaviourRecord> preFours =
//                behaviourRecords.stream().filter(single -> Constant.ONE.equals(single.getScoreType()) && single.getScore().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
//        List<BehaviourNameInfoVO> preFourList = new ArrayList<>();
//        for (BehaviourRecord preFour : preFours) {
//            BehaviourNameInfoVO behaviourNameInfoVO = new BehaviourNameInfoVO();
//            behaviourNameInfoVO.setBehaviourId(Convert.toStr(preFour.getId()));
//            behaviourNameInfoVO.setBehaviourName(preFour.getInfoName());
//            // 如果是选项名称的填充指标名称
//            if (InfoTypeEnum.OPTION.getCode().equals(preFour.getInfoType())) {
//                behaviourNameInfoVO.setTargetName(targetNameMap.get(preFour.getTargetId()));
//            }
//            preFourList.add(behaviourNameInfoVO);
//        }
//
//        // 取前四位，不满四位全返回
//        behavioralExpressionVO.setPraiseBehaviourList(Constant.BEHAVIOUR_LIST >= preFourList.size() ? preFourList :
//                preFourList.subList(Constant.ZERO, Constant.BEHAVIOUR_LIST));
//
//
//        // 减分项行为名称（筛选掉表扬中的0分（0分不加入待改进分类中））
//        List<BehaviourRecord> subFours =
//                behaviourRecords.stream().filter(single -> Constant.TWO.equals(single.getScoreType()) && single.getScore().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
//        List<BehaviourNameInfoVO> subFourList = new ArrayList<>();
//        for (BehaviourRecord subFour : subFours) {
//            BehaviourNameInfoVO behaviourNameInfoVO = new BehaviourNameInfoVO();
//            behaviourNameInfoVO.setBehaviourId(Convert.toStr(subFour.getId()));
//            behaviourNameInfoVO.setBehaviourName(subFour.getInfoName());
//            // 如果是选项名称的填充指标名称
//            if (InfoTypeEnum.OPTION.getCode().equals(subFour.getInfoType())) {
//                behaviourNameInfoVO.setTargetName(targetNameMap.get(subFour.getTargetId()));
//            }
//            subFourList.add(behaviourNameInfoVO);
//        }
//
//        // 取前四位，不满四位全返回
//        behavioralExpressionVO.setToImproveBehaviourList(Constant.BEHAVIOUR_LIST >= subFourList.size() ? subFourList
//                : CollectionUtil.reverse(subFourList).subList(Constant.ZERO, Constant.BEHAVIOUR_LIST));
//        log.info("学生画像-行为表现 返回结果：[{}]", JSONUtil.toJsonStr(behavioralExpressionVO));
//        return behavioralExpressionVO;
//    }

//    /**
//     * 成长趋势
//     *
//     * @param behaviourRecordQueryDTO
//     * @return
//     */
//    @Override
//    public BehaviourGrowthTrendVO getGrowthTrend(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
//        //如果开始日期和结束日期相差超过365天，则提示超过时间范围
//        Assert.isTrue(DateUtil.betweenDay(behaviourRecordQueryDTO.getStartTime(),
//                behaviourRecordQueryDTO.getEndTime(), true)
//                <= Constant.DAY_NUMBER_OF_YEAR, () -> new BizException("筛选时间不能超过365天"));
//        BehaviourGrowthTrendVO behaviourGrowthTrendVO = new BehaviourGrowthTrendVO();
//        //筛选时间范围内所有周或者月的开始、结束时间
//        ArrayList<SubmitTimeVO> submitTimeVOS = new ArrayList<>();
//        //时间间隔超过30天，按月算，否则按周算
//        behaviourGrowthTrendVO.setWeekOrMonthCode(DateUtil.betweenDay(behaviourRecordQueryDTO.getStartTime(),
//                behaviourRecordQueryDTO.getEndTime(), true) > Constant.THIRTY
//                ? TimeEnum.MONTH.getCode() : TimeEnum.WEEK.getCode());
//        behaviourGrowthTrendVO.setWeekOrMonthName(TimeEnum.getValueByCode(behaviourGrowthTrendVO.getWeekOrMonthCode()));
//        //获取筛选时间范围内所有周或者月的开始、结束时间
//        listWeekMonthTime(behaviourRecordQueryDTO, behaviourGrowthTrendVO, submitTimeVOS);
//
//        ArrayList<BehaviourGrowthTrendVOInner> behaviourGrowthTrendVOInnerArrayList = new ArrayList<>();
//        //周计数器，用来表示一周前，两周前
//        int count = 0;
//        //逆向遍历获取周信息，从本周开始
//        ListIterator<SubmitTimeVO> listIterator = submitTimeVOS.listIterator(submitTimeVOS.size());
//        while (listIterator.hasPrevious()) {
//            SubmitTimeVO previous = listIterator.previous();
//            //学生分数
//            BehaviourRecord behaviourRecord = getStudentScore(behaviourRecordQueryDTO, previous.getStartTime(),
//                    previous.getEndTime());
//            //班级人数
//            BehaviourRecordQueryDTO behaviourRecordQueryDTOByTime = new BehaviourRecordQueryDTO();
//            behaviourRecordQueryDTOByTime.setClassId(behaviourRecordQueryDTO.getClassId());
//            behaviourRecordQueryDTOByTime.setStartTime(String.valueOf(previous.getStartTime()));
//            behaviourRecordQueryDTOByTime.setEndTime(String.valueOf(previous.getEndTime()));
//            Long classPeopleCount = getClassPeopleCount(behaviourRecordQueryDTOByTime);
//            //班级总分
//            QueryWrapper<BehaviourRecord> sumWrapper = new QueryWrapper<>();
//            sumWrapper.select("sum(score) as score")
//                    .eq(StrUtil.isNotBlank(WebUtil.getTenantId()), "tenant_id", WebUtil.getTenantId())
//                    .eq(StrUtil.isNotBlank(WebUtil.getSchoolId()), "school_id", WebUtil.getSchoolId())
//                    .eq(StrUtil.isNotBlank(WebUtil.getCampusId()), "campus_id", WebUtil.getCampusId())
//                    .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getClassId()), "class_id",
//                            behaviourRecordQueryDTO.getClassId())
//                    .ge(ObjectUtil.isNotNull(previous.getStartTime()), "submit_time", previous.getStartTime())
//                    .le(ObjectUtil.isNotNull(previous.getEndTime()), "submit_time", previous.getEndTime())
//                    .eq("is_score", Constant.YES);
//            BehaviourRecord avgBehaviourRecord = behaviourRecordMapper.selectOne(sumWrapper);
//            //学生分数
//            BigDecimal studentScore;
//            //班级平均分（总分/班级总人数）
//            BigDecimal classAvgScore;
//            studentScore = ObjectUtil.isNull(behaviourRecord) ? BigDecimal.ZERO : behaviourRecord.getScore();
//            classAvgScore = ObjectUtil.isNull(avgBehaviourRecord)
//                    ? BigDecimal.ZERO : avgBehaviourRecord.getScore().divide(BigDecimal.valueOf(classPeopleCount),
//                    Constant.TWO, RoundingMode.HALF_UP);
//            BehaviourGrowthTrendVOInner behaviourGrowthTrendVOInner = new BehaviourGrowthTrendVOInner()
//                    .setStudentScore(studentScore)
//                    .setClassAvgScore(classAvgScore)
//                    .setWeekMonthNumber(behaviourGrowthTrendVO.getWeekOrMonthCode().equals(TimeEnum.WEEK.getCode())
//                            ? count : DateUtil.month(previous.getStartTime()) + Constant.ONE);
//            behaviourGrowthTrendVOInnerArrayList.add(Constant.ZERO, behaviourGrowthTrendVOInner);
//            count++;
//        }
//        if (CollectionUtil.isEmpty(behaviourGrowthTrendVOInnerArrayList)) {
//            return null;
//        }
//        behaviourGrowthTrendVO.setBehaviourGrowthTrendVOInnerList(behaviourGrowthTrendVOInnerArrayList);
//        //最高分以及最低分
//        List<BigDecimal> classAvgScoreList =
//                behaviourGrowthTrendVOInnerArrayList.stream().map(BehaviourGrowthTrendVOInner::getClassAvgScore).collect(Collectors.toList());
//        List<BigDecimal> studentScoreList =
//                behaviourGrowthTrendVOInnerArrayList.stream().map(BehaviourGrowthTrendVOInner::getStudentScore).collect(Collectors.toList());
//        classAvgScoreList.addAll(studentScoreList);
//        Collections.sort(classAvgScoreList);
//        behaviourGrowthTrendVO.setMaxScore(classAvgScoreList.get(classAvgScoreList.size() - 1))
//                .setMinScore(classAvgScoreList.get(0));
//        log.info("学生画像-成长趋势 返回结果：[{}]", JSONUtil.toJsonStr(behaviourGrowthTrendVO));
//        return behaviourGrowthTrendVO;
//    }
//
//    /**
//     * 智能评价
//     *
//     * @param behaviourRecordQueryDTO
//     * @return
//     */
//    @Override
//    public BehaviourIntelligentEvaluationVO getIntelligentEvaluation(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
//        Assert.notNull(behaviourRecordQueryDTO.getStudentId(), () -> new BizException("学生id不能为空"));
//
//        BehaviourIntelligentEvaluationVO behaviourIntelligentEvaluationVO = new BehaviourIntelligentEvaluationVO();
//        behaviourIntelligentEvaluationVO.setStudentId(behaviourRecordQueryDTO.getStudentId());
//        //根据学生id获取学生姓名
//        List<EduStudentInfoVO> eduStudentInfoVOS =
//                basicInfoRemote.queryStudentPage(new EduStudentPageQueryDTO().setSchoolId(WebUtil.getSchoolIdLong())
//                        .setStudentIds(CollUtil.newArrayList(Convert.toLong(behaviourRecordQueryDTO.getStudentId()))));
//        if (CollectionUtil.isNotEmpty(eduStudentInfoVOS)) {
//            behaviourIntelligentEvaluationVO.setStudentName(eduStudentInfoVOS.get(0).getStudentName());
//        }
//        //学生分数
//        BehaviourRecord behaviourRecord = getStudentScore(behaviourRecordQueryDTO,
//                behaviourRecordQueryDTO.getStartTime(), behaviourRecordQueryDTO.getEndTime());
//        behaviourIntelligentEvaluationVO.setSumScore(ObjectUtil.isNotNull(behaviourRecord) ?
//                        behaviourRecord.getScore() : null)
//                .setStartTime(behaviourRecordQueryDTO.getStartTime())
//                .setEndTime(behaviourRecordQueryDTO.getEndTime());
//
//        //获取所有行为分值从高到低
//        List<BehaviourRecord> listSumScore = listSumScore(behaviourRecordQueryDTO);
//        if (CollectionUtil.isNotEmpty(listSumScore)) {
//            // 获取名称为选项的指标名称Map
//            Map<Long, String> targetNameMap = new HashMap<>();
//            List<Long> targetIds =
//                    listSumScore.stream().filter(s -> InfoTypeEnum.OPTION.getCode().equals(s.getInfoType())).map(BehaviourRecord::getTargetId).collect(Collectors.toList());
//            if (CollUtil.isNotEmpty(targetIds)) {
//                List<Target> targets = targetMapper.listTargetByIds(targetIds);
//                targetNameMap = targets.stream().collect(Collectors.toMap(Target::getId, Target::getTargetName));
//            }
//
//            // 分值最高的行为
//            List<BehaviourRecord> maxScores = listSumScore.stream()
//                    // 0分不放入表扬中
//                    .filter(single -> single.getScore().compareTo(BigDecimal.ZERO) != 0)
//                    .filter(single -> single.getScore().compareTo(listSumScore.get(0).getScore()) == 0
//                            && Constant.ONE.equals(single.getScoreType()))
//                    .collect(Collectors.toList());
//            List<BehaviourNameInfoVO> maxScoreList = new ArrayList<>();
//            for (BehaviourRecord maxScore : maxScores) {
//                BehaviourNameInfoVO behaviourNameInfoVO = new BehaviourNameInfoVO();
//                behaviourNameInfoVO.setBehaviourId(Convert.toStr(maxScore.getId()));
//                behaviourNameInfoVO.setBehaviourName(maxScore.getInfoName());
//                // 如果是选项名称的填充指标名称
//                if (InfoTypeEnum.OPTION.getCode().equals(maxScore.getInfoType())) {
//                    behaviourNameInfoVO.setTargetName(targetNameMap.get(maxScore.getTargetId()));
//                }
//                maxScoreList.add(behaviourNameInfoVO);
//            }
//            behaviourIntelligentEvaluationVO.setBestBehaviour(Constant.TWO > maxScoreList.size() ? maxScoreList :
//                    maxScoreList.subList(Constant.ZERO, Constant.TWO));
//
//            // 分值最低的行为
//            List<BehaviourRecord> minScores = listSumScore.stream()
//                    // 0分不放入待改进
//                    .filter(single -> single.getScore().compareTo(BigDecimal.ZERO) != 0)
//                    .filter(single -> single.getScore().compareTo(listSumScore.get(listSumScore.size() - 1).getScore()) == 0
//                            && Constant.TWO.equals(single.getScoreType())).collect(Collectors.toList());
//            List<BehaviourNameInfoVO> minScoreList = new ArrayList<>();
//            for (BehaviourRecord minScore : minScores) {
//                BehaviourNameInfoVO behaviourNameInfoVO = new BehaviourNameInfoVO();
//                behaviourNameInfoVO.setBehaviourId(Convert.toStr(minScore.getId()));
//                behaviourNameInfoVO.setBehaviourName(minScore.getInfoName());
//                // 如果是选项名称的填充指标名称
//                if (InfoTypeEnum.OPTION.getCode().equals(minScore.getInfoType())) {
//                    behaviourNameInfoVO.setTargetName(targetNameMap.get(minScore.getTargetId()));
//                }
//                minScoreList.add(behaviourNameInfoVO);
//            }
//
//            behaviourIntelligentEvaluationVO.setWorstBehaviour(Constant.TWO > minScoreList.size() ? minScoreList :
//                    minScoreList.subList(Constant.ZERO, Constant.TWO));
//        }
//        DateTime beginOfWeek = DateUtil.beginOfWeek(behaviourRecordQueryDTO.getEndTime());
//        DateTime endOfWeek = DateUtil.endOfWeek(behaviourRecordQueryDTO.getEndTime());
//        //筛选时间截止日期所在周得分
//        BehaviourRecord studentScore = getStudentScore(behaviourRecordQueryDTO, beginOfWeek, endOfWeek);
//        behaviourIntelligentEvaluationVO.setLastWeekScore(ObjectUtil.isNull(studentScore) ? null :
//                studentScore.getScore());
//
//        /**
//         ------------------------------------------------本周和上周数据*-------------------------------------------------------------*/
//        //本周班级排名
//        List<BehaviourRecord> studentRank = listStudentRank(behaviourRecordQueryDTO, beginOfWeek, endOfWeek);
//
//        //上周班级排名
//        DateTime lastBeginOfWeek = DateUtil.offsetWeek(beginOfWeek, Constant.NEGATIVE_ONE);
//        DateTime lastEndOfWeek = DateUtil.endOfWeek(lastBeginOfWeek);
//        List<BehaviourRecord> lastStudentRank = listStudentRank(behaviourRecordQueryDTO, lastBeginOfWeek,
//                lastEndOfWeek);
//
//        List<BehaviourRecord> student =
//                studentRank.stream().filter(single -> single.getStudentId().equals(behaviourRecordQueryDTO.getStudentId())).collect(Collectors.toList());
//        List<BehaviourRecord> lastStudent =
//                lastStudentRank.stream().filter(single -> single.getStudentId().equals(behaviourRecordQueryDTO.getStudentId())).collect(Collectors.toList());
//        //上周或者本周没数据，则不参与排名
//        if ((CollectionUtil.isEmpty(student) || (CollectionUtil.isEmpty(lastStudent)))) {
//            behaviourIntelligentEvaluationVO.setProgressRank(null);
//        } else {
//            //本周名次
//            int classRank = getClassRank(studentRank, student.get(0).getScore());
//            //上周名次
//            int lastClassRank = getClassRank(lastStudentRank, lastStudent.get(0).getScore());
//            behaviourIntelligentEvaluationVO.setProgressRank(lastClassRank - classRank);
//        }
//        log.info("学生画像-智能评价 返回结果：[{}]", JSONUtil.toJsonStr(behaviourIntelligentEvaluationVO));
//        return behaviourIntelligentEvaluationVO;
//    }

    /**
     * 通过枚举获取开始时间和结束时间
     *
     * @param timeType
     * @return
     */
    @Override
    public SubmitTimeVO getTime(Integer timeType, String campusSectionId) {
        SubmitTimeVO submitTimeVO = new SubmitTimeVO()
                .setStartTime(behaviourRecordManager.getStartTime(timeType, campusSectionId))
                .setEndTime(behaviourRecordManager.getEndTime(timeType, campusSectionId));
        log.info("学生画像-通过枚举获取开始时间和结束时间 返回结果：[{}]", JSONUtil.toJsonStr(submitTimeVO));
        return submitTimeVO;
    }

    /**
     * 学生档案
     *
     * @param dto
     * @return
     */
    @Override
    public List<BehaviourStudentFileVO> listStudentFile(BehaviourStudentFileQueryDTO dto) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();

        EduStudentPageQueryDTO eduStudentPageQueryDTO = convert.toEduStudentPageQueryDTO(dto);
        eduStudentPageQueryDTO.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
        eduStudentPageQueryDTO.setNameLike(null);
        eduStudentPageQueryDTO.setCampusId(Convert.toLong(WebUtil.getCampusId()));
        // saas获取数据(不做名字搜索)
        List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoService.queryStudentPage(eduStudentPageQueryDTO);
        log.warn("[学生画像-列表]-[step.1.1]-[获取saas学生数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 根据班级id筛选出数据
        if (CollUtil.isNotEmpty(dto.getClassIds())) {
            eduStudentInfoVOS =
                    eduStudentInfoVOS.stream().filter(s -> dto.getClassIds().contains(s.getClassId()))
                            .collect(Collectors.toList());
        }
        List<String> classIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(dto.getClassIds())) {
            classIds = dto.getClassIds().stream().map(Convert::toStr).collect(Collectors.toList());
        }
        log.warn("[学生画像-列表]-[step.1.1.1]-[获取saas学生数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        // studentIds
        List<String> studentIds =
                eduStudentInfoVOS.stream().map(s -> Convert.toStr(s.getId())).distinct().collect(Collectors.toList());

        // TODO: 2024/11/12 初始分重新设计，启用新的逻辑，暂时注释
        // 总的积分
//        List<InitialScoreAllocation> totalInitialScoreAllocations = new ArrayList<>();
//        if (CollUtil.isNotEmpty(studentIds)) {
//            totalInitialScoreAllocations =
//                    initialScoreAllocationManager.list(new LambdaQueryWrapper<InitialScoreAllocation>()
//                            .eq(InitialScoreAllocation::getTenantId, WebUtil.getTenantId())
//                            .eq(InitialScoreAllocation::getSchoolId, WebUtil.getSchoolId())
//                            .eq(InitialScoreAllocation::getCampusId, WebUtil.getCampusId())
//                            .in(InitialScoreAllocation::getStudentId, studentIds)
//                            .between(InitialScoreAllocation::getCreateTime, dto.getStartTime(), dto.getEndTime()));
//        }

        TermVo currentTerm = this.getCurrentTerm(WebUtil.getSchoolIdLong(), WebUtil.getCampusIdLong(),
                dto.getCampusSectionId());
        BigDecimal totalInitialScore = new BigDecimal(0);
        if (BeanUtil.isNotEmpty(currentTerm)) {
            //初始分
            InitialScorePO oneByBaseInfo = initialScoreManager.getOneByBaseInfo(
                    Convert.toStr(WebUtil.getSchoolId()),
                    Convert.toStr(WebUtil.getCampusId()),
                    currentTerm.getSchoolYear(),
                    currentTerm.getTermName(),
                    ModuleEnum.OTHER.getCode()
            );
            if (BeanUtil.isNotEmpty(oneByBaseInfo) && ObjectUtil.isNotNull(oneByBaseInfo.getInitialScore())) {
                totalInitialScore = oneByBaseInfo.getInitialScore();
            }
        }

        log.warn("[学生画像-列表]-[step.1.1.2]-[获取saas学生数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 一周内的积分
//        List<InitialScoreAllocation> weekScoreAllocations =
//                totalInitialScoreAllocations.stream().filter(s -> s.getCreateTime().after(dto.getStartTime()) && s.getCreateTime().before(dto.getEndTime())).collect(Collectors.toList());
        log.warn("[学生画像-列表]-[step.1.1.3]-[获取saas学生数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 根据时间查询出总的行为记录
        List<BehaviourRecord> totalBehaviourRecords = queryBehaviourRecords(classIds, dto.getStartTime(),
                dto.getEndTime(), dto.getTargetId(), dto.getCampusSectionId(), dto.getModuleCode());
        // 过滤无需统计的行为记录
        totalBehaviourRecords = totalBehaviourRecords
                .stream()
                .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                .collect(Collectors.toList());

        log.warn("[学生画像-列表]-[step.1.2]-[获取所有行为记录数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 获取本周范围内的数据
        List<BehaviourRecord> weekBehaviourRecords = queryBehaviourRecords(classIds, getTimesWeekMorning(),
                new Date(), null, dto.getCampusSectionId(), dto.getModuleCode());
        // 过滤无需统计的行为记录
        weekBehaviourRecords = weekBehaviourRecords
                .stream()
                .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                .collect(Collectors.toList());

        log.warn("[学生画像-列表]-[step.1.3]-[获取本周内行为记录数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        List<BehaviourStudentFileVO> studentInfos = new ArrayList<>();
        Map<String, List<BehaviourRecord>> totalStudentRecordMap = CollStreamUtil.groupByKey(totalBehaviourRecords,
                BehaviourRecord::getStudentId);
        Map<String, List<BehaviourRecord>> weekStudentRecordMap = CollStreamUtil.groupByKey(weekBehaviourRecords,
                BehaviourRecord::getStudentId);

//        Map<String, List<InitialScoreAllocation>> totalInitialMap =
//                CollStreamUtil.groupByKey(totalInitialScoreAllocations, InitialScoreAllocation::getStudentId);
//        Map<String, List<InitialScoreAllocation>> weekInitialMap = CollStreamUtil.groupByKey(weekScoreAllocations,
//                InitialScoreAllocation::getStudentId);

        if (CollectionUtil.isNotEmpty(eduStudentInfoVOS) && ObjectUtil.isNotNull(totalBehaviourRecords)) {
            for (EduStudentInfoVO eduStudentInfoVO : eduStudentInfoVOS) {
                BehaviourStudentFileVO behaviourStudentFileVO = convert.toBehaviourStudentFileVO(eduStudentInfoVO);
                String studentId = Convert.toStr(eduStudentInfoVO.getId());
                dto.setStudentId(studentId);
                List<BehaviourRecord> totalStudentBehaviourRecords = totalStudentRecordMap.get(studentId);
                List<BehaviourRecord> weekStudentBehaviourRecords = weekStudentRecordMap.get(studentId);
//                List<InitialScoreAllocation> totalInitialScoreAllocationList = totalInitialMap.get(studentId);
//                List<InitialScoreAllocation> weekInitialScoreAllocationList = weekInitialMap.get(studentId);
                // 总数据
                CalculateScoreDTO totalCalculateScoreDTO = calculateScore(totalStudentBehaviourRecords);
                // 周数据
                CalculateScoreDTO weekCalculateScoreDTO = calculateScore(weekStudentBehaviourRecords);
                // 积分总数据
//                CalculateScoreDTO totalInitialScoreDTO = initialCalculateScore(totalInitialScoreAllocationList);

                // 筛选单个育时不计算初始分
//                if (Objects.nonNull(totalInitialScoreDTO.getAddScore()) && Objects.isNull(dto.getModuleCode())) {
//                    totalInitialScore = totalInitialScore.add(totalInitialScoreDTO.getAddScore());
//                }
//                if (Objects.nonNull(totalInitialScoreDTO.getSubtractScore()) && Objects.isNull(dto.getModuleCode())) {
//                    totalInitialScore = totalInitialScore.add(totalInitialScoreDTO.getSubtractScore());
//                }
                // 积分周数据
//                CalculateScoreDTO weekInitialScoreDTO = initialCalculateScore(weekInitialScoreAllocationList);

//                if (Objects.nonNull(totalInitialScoreDTO.getAddScore()) && Objects.nonNull(totalCalculateScoreDTO
//                .getAddScore())) {
//                    totalCalculateScoreDTO.setAddScore(totalCalculateScoreDTO.getAddScore().add
//                    (totalInitialScoreDTO.getAddScore()));
//                }
//                if (Objects.nonNull(totalInitialScoreDTO.getSubtractScore()) && Objects.nonNull
//                (totalCalculateScoreDTO.getSubtractScore())) {
//                    totalCalculateScoreDTO.setSubtractScore(totalCalculateScoreDTO.getSubtractScore().add
//                    (totalInitialScoreDTO.getSubtractScore()));
//                }
//                if (Objects.nonNull(weekInitialScoreDTO.getAddScore()) && Objects.nonNull(weekCalculateScoreDTO
//                .getAddScore())) {
//                    weekCalculateScoreDTO.setAddScore(weekCalculateScoreDTO.getAddScore().add(weekInitialScoreDTO
//                    .getAddScore()));
//                }
//                if (Objects.nonNull(weekInitialScoreDTO.getSubtractScore()) && Objects.nonNull
//                (weekCalculateScoreDTO.getSubtractScore())) {
//                    weekCalculateScoreDTO.setSubtractScore(weekCalculateScoreDTO.getSubtractScore().add
//                    (weekInitialScoreDTO.getSubtractScore()));
//                }

                //得分
                BigDecimal sumScore = null;
                if (ObjectUtil.isNull(totalCalculateScoreDTO.getAddScore()) && ObjectUtil.isNotNull(
                        totalCalculateScoreDTO.getSubtractScore())) {
                    sumScore = totalCalculateScoreDTO.getSubtractScore();
                } else if (ObjectUtil.isNull(totalCalculateScoreDTO.getSubtractScore()) && ObjectUtil.isNotNull(
                        totalCalculateScoreDTO.getAddScore())) {
                    sumScore = totalCalculateScoreDTO.getAddScore();
                } else if (ObjectUtil.isNotNull(totalCalculateScoreDTO.getSubtractScore()) && ObjectUtil.isNotNull(
                        totalCalculateScoreDTO.getAddScore())) {
                    sumScore = totalCalculateScoreDTO.getAddScore().add(totalCalculateScoreDTO.getSubtractScore());
                }

                if (Objects.isNull(sumScore)) {
                    sumScore = totalInitialScore;
                } else {
                    sumScore = sumScore.add(totalInitialScore);
                }

                behaviourStudentFileVO.setSumScore(ObjectUtil.isNull(sumScore) ? null :
                        new BigDecimal(sumScore.setScale(Constant.TWO).stripTrailingZeros().toPlainString()));
                behaviourStudentFileVO.setAddScore(totalCalculateScoreDTO.getAddScore());
                behaviourStudentFileVO.setWeekAddScore(weekCalculateScoreDTO.getAddScore());
                behaviourStudentFileVO.setWeekSubtractScore(weekCalculateScoreDTO.getSubtractScore());
                behaviourStudentFileVO.setSubtractScore(totalCalculateScoreDTO.getSubtractScore());
                studentInfos.add(behaviourStudentFileVO);
            }
        }
        log.warn("[学生画像-列表]-[step.1.4]-[计算分数]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        List<BehaviourStudentFileVO> collect = studentInfos.stream().sorted(Comparator
                .comparing(BehaviourStudentFileVO::getSumScore, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(BehaviourStudentFileVO::getSubtractScore, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(BehaviourStudentFileVO::getStudentId, Comparator.nullsFirst(String::compareTo))
                .reversed()).collect(Collectors.toList());
        log.info("学生画像-学生档案 返回结果：[{}]", JSONUtil.toJsonStr(collect));
        log.warn("[学生画像-列表]-[step.1.5]-[计算分数-数据转换]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 学生名次计算
        Map<BigDecimal, Integer> rankMap = getClassRank(collect);
        collect.forEach(s -> s.setRank(rankMap.get(s.getSumScore())));
        log.warn("[学生画像-列表]-[step.1.6]-[计算学生名次]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        if (StrUtil.isNotBlank(dto.getNameLike())) {
            List<String> studentIdList =
                    eduStudentInfoVOS.stream().filter(s -> s.getStudentName().contains(dto.getNameLike()))
                            .map(s -> Convert.toStr(s.getId())).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(studentIdList)) {
                return Collections.emptyList();
            }
            collect =
                    collect.stream().filter(s -> studentIdList.contains(s.getStudentId())).collect(Collectors.toList());
        }
        log.warn("[学生画像-列表]-[step.1.6]-[计算学生名次-数据转换]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        return collect;
    }

    /**
     * 获取当前学期时间
     */
    private TermVo getCurrentTerm(Long schoolId, Long campusId, String campusSectionId) {

        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(schoolId);
        termQuery.setCampusId(campusId);
        termQuery.setCampusSectionId(campusSectionId);
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        if (CollUtil.isEmpty(termVos)) {
            log.warn("【从saas获取学期时间】返回结果空，schoolId：{},campusId：{},campusSectionId：{}",
                    schoolId, campusId, campusSectionId);
            return null;
        }

        //当前学期
        List<TermVo> currentTerm = termVos.stream().filter(TermVo::isCurrentTerm).collect(Collectors.toList());
        if (CollUtil.isEmpty(currentTerm)) {
            log.warn("【从saas获取学期时间】没有当前学期，返回结果空，schoolId：{},campusId：{},campusSectionId：{}",
                    schoolId, campusId, campusSectionId);
            return null;
        }
        return currentTerm.get(Constant.ZERO);
    }


    @Override
    public List<BehaviourStudentFileVO> listStudentScores(BehaviourStudentFileQueryDTO dto,
                                                          List<EduStudentInfoVO> eduStudentInfoVOS) {
        log.info("[学生列表]-[获取所有行为记录数据]--入参，[{}]", JSONUtil.toJsonStr(dto));
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();

        // studentIds
        List<String> studentIds =
                eduStudentInfoVOS.stream().map(s -> Convert.toStr(s.getId())).distinct().collect(Collectors.toList());

        // 总的积分
        InitialScorePO oneByBaseInfo = initialScoreManager.getOneByBaseInfo(WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                dto.getSchoolYear(),
                dto.getTermName(),
                ModuleEnum.OTHER.getCode());

        BigDecimal initScore = BigDecimal.ZERO;
        if (BeanUtil.isNotEmpty(oneByBaseInfo) && ObjectUtil.isNotNull(oneByBaseInfo.getInitialScore())) {
            initScore = oneByBaseInfo.getInitialScore();
        }
        // TODO: 2024/11/18 初始分重新设计，暂时注释
//        List<InitialScoreAllocation> totalInitialScoreAllocations = Lists.newCopyOnWriteArrayList();
//        if (CollUtil.isNotEmpty(studentIds)) {
//
//            //分片多线程处理
//            List<List<String>> partition = ListUtil.partition(studentIds, 50);
//            String tenantId = WebUtil.getTenantId();
//            String schoolId = WebUtil.getSchoolId();
//            String campusId = WebUtil.getCampusId();
//            partition.parallelStream().forEach(s ->
//            {
//                List<InitialScoreAllocation> list = initialScoreAllocationManager.list(new LambdaQueryWrapper<InitialScoreAllocation>()
//                        .eq(InitialScoreAllocation::getTenantId, tenantId)
//                        .eq(InitialScoreAllocation::getSchoolId, schoolId)
//                        .eq(InitialScoreAllocation::getCampusId, campusId)
//                        .in(InitialScoreAllocation::getStudentId, s)
//                        .between(InitialScoreAllocation::getCreateTime, dto.getStartTime(), dto.getEndTime())
//                );
//                totalInitialScoreAllocations.addAll(list);
//            });
//        }

        log.warn("[学生列表]-[step.1.2]-[获取初始分]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        List<String> classIds;
        if (CollUtil.isNotEmpty(dto.getClassIds())) {
            classIds = dto.getClassIds().stream().map(Convert::toStr).collect(Collectors.toList());
        } else {
            classIds = Collections.emptyList();
        }

        // 根据时间查询出总的行为记录
        String key = StrUtil.join(StrPool.UNDERLINE, StrUtil.join(StrPool.UNDERLINE, classIds), dto.getStartTime(),
                dto.getEndTime(), dto.getTargetId());
        List<BehaviourRecord> totalBehaviourRecords =
                redisUtil.getOrAdd(RedisKeyConstants.EVALUATE_BEHAVIOUR_RECORD + key,
                        () -> queryBehaviourRecords(classIds,
                                dto.getStartTime(), dto.getEndTime(), dto.getTargetId(), dto.getCampusSectionId(),
                                null),
                        CacheConstants.ONE_MINUTE);

        log.warn("[学生列表]-[step.1.2]-[获取所有行为记录数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        List<BehaviourStudentFileVO> studentInfos = new ArrayList<>();
        Map<String, List<BehaviourRecord>> totalStudentRecordMap = CollStreamUtil.groupByKey(totalBehaviourRecords,
                BehaviourRecord::getStudentId);

//        Map<String, List<InitialScoreAllocation>> totalInitialMap =
//                CollStreamUtil.groupByKey(totalInitialScoreAllocations, InitialScoreAllocation::getStudentId);

        if (CollectionUtil.isNotEmpty(eduStudentInfoVOS) && ObjectUtil.isNotNull(totalBehaviourRecords)) {
            for (EduStudentInfoVO eduStudentInfoVO : eduStudentInfoVOS) {
                BehaviourStudentFileVO behaviourStudentFileVO = convert.toBehaviourStudentFileVO(eduStudentInfoVO);
                String studentId = Convert.toStr(eduStudentInfoVO.getId());
                dto.setStudentId(studentId);
                List<BehaviourRecord> totalStudentBehaviourRecords = totalStudentRecordMap.get(studentId);
//                List<InitialScoreAllocation> totalInitialScoreAllocationList = totalInitialMap.get(studentId);
                // 总数据
                CalculateScoreDTO totalCalculateScoreDTO = calculateScore(totalStudentBehaviourRecords);
                // 积分总数据
//                CalculateScoreDTO totalInitialScoreDTO = initialCalculateScore(totalInitialScoreAllocationList);

                BigDecimal totalInitialScore = initScore;

                //得分
                BigDecimal sumScore = null;
                if (ObjectUtil.isNull(totalCalculateScoreDTO.getAddScore()) && ObjectUtil.isNotNull(
                        totalCalculateScoreDTO.getSubtractScore())) {
                    sumScore = totalCalculateScoreDTO.getSubtractScore();
                } else if (ObjectUtil.isNull(totalCalculateScoreDTO.getSubtractScore()) && ObjectUtil.isNotNull(
                        totalCalculateScoreDTO.getAddScore())) {
                    sumScore = totalCalculateScoreDTO.getAddScore();
                } else if (ObjectUtil.isNotNull(totalCalculateScoreDTO.getSubtractScore()) && ObjectUtil.isNotNull(
                        totalCalculateScoreDTO.getAddScore())) {
                    sumScore = totalCalculateScoreDTO.getAddScore().add(totalCalculateScoreDTO.getSubtractScore());
                }

                if (Objects.isNull(sumScore)) {
                    sumScore = totalInitialScore;
                } else {
                    sumScore = sumScore.add(totalInitialScore);
                }

                behaviourStudentFileVO.setSumScore(ObjectUtil.isNull(sumScore) ? null :
                        new BigDecimal(sumScore.setScale(Constant.TWO).stripTrailingZeros().toPlainString()));
                behaviourStudentFileVO.setAddScore(totalCalculateScoreDTO.getAddScore());
                behaviourStudentFileVO.setSubtractScore(totalCalculateScoreDTO.getSubtractScore());
                studentInfos.add(behaviourStudentFileVO);
            }
        }
        log.warn("[学生列表]-[step.1.3]-[计算分数]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        List<BehaviourStudentFileVO> collect = studentInfos.stream().sorted(Comparator
                .comparing(BehaviourStudentFileVO::getSumScore, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(BehaviourStudentFileVO::getSubtractScore, Comparator.nullsFirst(BigDecimal::compareTo))
                .thenComparing(BehaviourStudentFileVO::getStudentId, Comparator.nullsFirst(String::compareTo))
                .reversed()).collect(Collectors.toList());
        log.warn("[学生列表]-[step.1.4]-[计算分数-数据转换]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 学生名次计算
        Map<BigDecimal, Integer> rankMap = getClassRank(collect);
        collect.forEach(s -> s.setRank(rankMap.get(s.getSumScore())));
        log.warn("[学生列表]-[step.1.5]-[计算学生名次]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        if (StrUtil.isNotBlank(dto.getNameLike())) {
            List<String> studentIdList =
                    eduStudentInfoVOS.stream().filter(s -> s.getStudentName().contains(dto.getNameLike()))
                            .map(s -> Convert.toStr(s.getId())).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(studentIdList)) {
                return Collections.emptyList();
            }
            collect =
                    collect.stream().filter(s -> studentIdList.contains(s.getStudentId())).collect(Collectors.toList());
        }
        log.warn("[学生列表]-[step.1.6]-[计算学生名次-数据转换]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        return collect;
    }

//    private CalculateScoreDTO initialCalculateScore(List<InitialScoreAllocation> totalInitialScoreAllocationList) {
//        if (CollUtil.isEmpty(totalInitialScoreAllocationList)) {
//            CalculateScoreDTO calculateScoreDTO = new CalculateScoreDTO();
//            calculateScoreDTO.setAddScore(new BigDecimal(0));
//            calculateScoreDTO.setSubtractScore(new BigDecimal(0));
//            return calculateScoreDTO;
//        }
//        //加分
//        List<InitialScoreAllocation> addScoreBehaviourRecordList = totalInitialScoreAllocationList.stream()
//                .filter(d -> Constant.ONE.equals(d.getScoreType())).collect(Collectors.toList());
//
//        BigDecimal addScore = Constant.ZERO.equals(addScoreBehaviourRecordList.size()) ? null :
//                new BigDecimal(addScoreBehaviourRecordList.stream().map(InitialScoreAllocation::getInitialScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO).stripTrailingZeros().toPlainString());
//        //减分
//        List<InitialScoreAllocation> subtractScoreBehaviourRecordList =
//                totalInitialScoreAllocationList.stream().filter(d -> Constant.TWO.equals(d.getScoreType())).collect(Collectors.toList());
//
//        BigDecimal subtractScore = Constant.ZERO.equals(subtractScoreBehaviourRecordList.size()) ? null :
//                new BigDecimal(subtractScoreBehaviourRecordList.stream().map(InitialScoreAllocation::getInitialScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO).stripTrailingZeros().toPlainString());
//
//        CalculateScoreDTO calculateScoreDTO = new CalculateScoreDTO();
//        calculateScoreDTO.setAddScore(addScore);
//        calculateScoreDTO.setSubtractScore(subtractScore);
//
//        return calculateScoreDTO;
//    }

    /**
     * 该方法可用于学生综合得分排名计算操作 传入需要处理的学生信息集合和分数
     *
     * @param list 学生信息集合
     * @return
     */
    public Map<BigDecimal, Integer> getClassRank(List<BehaviourStudentFileVO> list) {
        // 思路: 按照综合得分来进行排名,在然后排名的时候进行比较,如果这一名的综合得分和上一名的相同,那么名次相同,如果比上一名分数低,那么排名加一

        List<BigDecimal> arrayList = new ArrayList();
        Map<BigDecimal, Integer> rankMap = new HashMap();
        //将需要排序的字段放入集合
        for (int p = 0; p < list.size(); p++) {
            arrayList.add(list.get(p).getSumScore());
        }
        //学生总分为key，循坏下标为value生成map
        for (int i = 0; i < arrayList.size(); i++) {
            if (i == 0) {
                rankMap.put(arrayList.get(0), 1);
            }
            if (!rankMap.containsKey(arrayList.get(i))) {
                rankMap.put(arrayList.get(i), i + 1);
            }
        }
        return rankMap;
    }

    /**
     * 根据行为记录获取学生的加减分
     *
     * @param totalBehaviourRecords 行为记录数据
     * @return
     */
    private CalculateScoreDTO calculateScore(List<BehaviourRecord> totalBehaviourRecords) {
        if (CollUtil.isEmpty(totalBehaviourRecords)) {
            CalculateScoreDTO calculateScoreDTO = new CalculateScoreDTO();
            calculateScoreDTO.setAddScore(new BigDecimal(0));
            calculateScoreDTO.setSubtractScore(new BigDecimal(0));
            return calculateScoreDTO;
        }
        //加分
        List<BehaviourRecord> addScoreBehaviourRecordList = totalBehaviourRecords.stream()
                .filter(d -> Constant.ONE.equals(d.getScoreType())).collect(Collectors.toList());

        BigDecimal addScore = Constant.ZERO.equals(addScoreBehaviourRecordList.size()) ? null :
                new BigDecimal(
                        addScoreBehaviourRecordList.stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO,
                                BigDecimal::add).setScale(Constant.TWO).stripTrailingZeros().toPlainString());
        //减分
        List<BehaviourRecord> subtractScoreBehaviourRecordList =
                totalBehaviourRecords.stream().filter(d -> Constant.TWO.equals(d.getScoreType()))
                        .collect(Collectors.toList());

        BigDecimal subtractScore = Constant.ZERO.equals(subtractScoreBehaviourRecordList.size()) ? null :
                new BigDecimal(subtractScoreBehaviourRecordList.stream().map(BehaviourRecord::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO).stripTrailingZeros()
                        .toPlainString());

        CalculateScoreDTO calculateScoreDTO = new CalculateScoreDTO();
        calculateScoreDTO.setAddScore(addScore);
        calculateScoreDTO.setSubtractScore(subtractScore);
        return calculateScoreDTO;
    }

    /**
     * 根据日期,班级查询行为记录
     *
     * @param classIds        班级id集合
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @param campusSectionId 学段
     * @return
     */
    private List<BehaviourRecord> queryBehaviourRecords(List<String> classIds, Date startTime, Date endTime,
                                                        String targetId, String campusSectionId, Integer moduleCode) {
        LambdaQueryWrapper<BehaviourRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BehaviourRecord::getClassId, BehaviourRecord::getStudentId, BehaviourRecord::getInfoName,
                        BehaviourRecord::getScore, BehaviourRecord::getScoreType, BehaviourRecord::getNotPartCount)
                .eq(StrUtil.isNotBlank(WebUtil.getCampusId()), BehaviourRecord::getCampusId, WebUtil.getCampusId())
                .in(CollUtil.isNotEmpty(classIds), BehaviourRecord::getClassId, classIds)
                .ge(ObjectUtil.isNotNull(startTime), BehaviourRecord::getSubmitTime, startTime)
                .le(ObjectUtil.isNotNull(endTime), BehaviourRecord::getSubmitTime, endTime)
                .eq(StrUtil.isNotBlank(targetId), BehaviourRecord::getTargetId, targetId)
                .eq(StrUtil.isNotBlank(campusSectionId), BehaviourRecord::getCampusSectionId, campusSectionId)
                .eq(BehaviourRecord::getIsScore, Constant.YES)
                .eq(Objects.nonNull(moduleCode), BehaviourRecord::getModuleCode, moduleCode);
        return behaviourRecordMapper.selectList(queryWrapper);
    }

//    /**
//     * 所有行为分值从高到低
//     *
//     * @param behaviourRecordQueryDTO
//     * @return
//     */
//    private List<BehaviourRecord> listSumScore(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
//        //所有行为分值
//        QueryWrapper<BehaviourRecord> queryWrapper = new QueryWrapper<BehaviourRecord>()
//                .select("id,target_id,template_id,option_id,info_type, info_name,sum(score) as score, score_type")
//                .eq(StrUtil.isNotBlank(WebUtil.getTenantId()), "tenant_id", WebUtil.getTenantId())
//                .eq(StrUtil.isNotBlank(WebUtil.getSchoolId()), "school_id", WebUtil.getSchoolId())
//                .eq(StrUtil.isNotBlank(WebUtil.getCampusId()), "campus_id", WebUtil.getCampusId())
//                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getClassId()), "class_id",
//                        behaviourRecordQueryDTO.getClassId())
//                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getStudentId()), "student_id",
//                        behaviourRecordQueryDTO.getStudentId())
//                .ge(ObjectUtil.isNotNull(behaviourRecordQueryDTO.getStartTime()), "submit_time",
//                        behaviourRecordQueryDTO.getStartTime())
//                .le(ObjectUtil.isNotNull(behaviourRecordQueryDTO.getEndTime()), "submit_time",
//                        behaviourRecordQueryDTO.getEndTime())
//                .notIn(CollUtil.isNotEmpty(behaviourRecordQueryDTO.getUnNeedBehaviourRecordIds()), "id",
//                        behaviourRecordQueryDTO.getUnNeedBehaviourRecordIds())
//                .eq("is_score", Constant.YES)
//                .groupBy("target_id, option_id, info_name, score")
//                .orderByDesc("score");
//        List<BehaviourRecord> behaviourRecords = behaviourRecordMapper.selectList(queryWrapper);
//        // 去除0分
//        log.info("所有行为分值从高到低 返回结果：[{}]", JSONUtil.toJsonStr(behaviourRecords));
////         筛选掉表扬中的0分（0分不加入表扬分类中）
////        List<BehaviourRecord> finalBehaviourRecords = behaviourRecords.stream().filter(d -> (d.getScoreType() == 1
////        && d.getScore().compareTo(BigDecimal.ZERO) != 0) || d.getScoreType() == 2) .collect(Collectors.toList());
////        log.info("所有行为分值从高到低 过滤后结果：[{}]", JSONUtil.toJsonStr(finalBehaviourRecords));
//        return CollectionUtil.isEmpty(behaviourRecords) ? Collections.EMPTY_LIST : behaviourRecords;
//    }

//    /**
//     * 该方法可用于分数成绩排名计算操作
//     * 传入需要处理的用户成绩集合和分数
//     *
//     * @param list
//     * @param userScore
//     * @return
//     */
//    private int getClassRank(List<BehaviourRecord> list, BigDecimal userScore) {
//        ArrayList<BigDecimal> arrayList = new ArrayList<>();
//        Map<BigDecimal, Integer> map = new HashMap<>();
//        //将需要排序的字段放入集合
//        for (BehaviourRecord behaviourRecord : list) {
//            arrayList.add(behaviourRecord.getScore());
//        }
//        //用户成绩为key，循坏下标为value生成map
//        for (int i = 0; i < arrayList.size(); i++) {
//            if (i == 0) {
//                map.put(arrayList.get(0), 1);
//            }
//            if (!map.containsKey(arrayList.get(i))) {
//                map.put(arrayList.get(i), i + 1);
//            }
//        }
//        //从map中取得对应的位置
//        return map.get(userScore);
//    }


    /**
     * 获取一段时间内班级排名得分
     *
     * @param behaviourRecordQueryDTO
     * @param beginOfWeek
     * @param endOfWeek
     */
    private List<BehaviourRecord> listStudentRank(BehaviourRecordQueryDTO behaviourRecordQueryDTO,
                                                  DateTime beginOfWeek, DateTime endOfWeek) {
        QueryWrapper<BehaviourRecord> wrapper = new QueryWrapper<>();
        wrapper.select("student_id,sum(score) as score,score_type")
                .eq(StrUtil.isNotBlank(WebUtil.getTenantId()), "tenant_id", WebUtil.getTenantId())
                .eq(StrUtil.isNotBlank(WebUtil.getSchoolId()), "school_id", WebUtil.getSchoolId())
                .eq(StrUtil.isNotBlank(WebUtil.getCampusId()), "campus_id", WebUtil.getCampusId())
                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getClassId()), "class_id",
                        behaviourRecordQueryDTO.getClassId())
                .ge(ObjectUtil.isNotNull(beginOfWeek), "submit_time", beginOfWeek)
                .le(ObjectUtil.isNotNull(endOfWeek), "submit_time", endOfWeek)
                .notIn(CollUtil.isNotEmpty(behaviourRecordQueryDTO.getUnNeedBehaviourRecordIds()), "id",
                        behaviourRecordQueryDTO.getUnNeedBehaviourRecordIds())
                .eq("is_score", Constant.YES)
                .groupBy("student_id,score_type")
                .orderByDesc("score");
        //全班得分排名list
        List<BehaviourRecord> behaviourRecordsList = behaviourRecordMapper.selectList(wrapper);
        log.info("获取一段时间内班级排名得分 返回结果：[{}]", JSONUtil.toJsonStr(behaviourRecordsList));
        return behaviourRecordsList;
    }

    /**
     * 获取一段时间内班级排名得分-初始分
     *
     * @param behaviourRecordQueryDTO
     * @param beginOfWeek
     * @param endOfWeek
     */
    // TODO: 2024/11/18 初始分重新设计，暂时注释
//    private List<InitialScoreAllocation> listInitialRank(BehaviourRecordQueryDTO behaviourRecordQueryDTO,
//                                                         DateTime beginOfWeek, DateTime endOfWeek) {
//        QueryWrapper<InitialScoreAllocation> wrapper = new QueryWrapper<>();
//        wrapper.select("student_id,sum(initial_score) as initialScore,score_type")
//                .eq(StrUtil.isNotBlank(WebUtil.getTenantId()), "tenant_id", WebUtil.getTenantId())
//                .eq(StrUtil.isNotBlank(WebUtil.getSchoolId()), "school_id", WebUtil.getSchoolId())
//                .eq(StrUtil.isNotBlank(WebUtil.getCampusId()), "campus_id", WebUtil.getCampusId())
//                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getClassId()), "class_id",
//                        behaviourRecordQueryDTO.getClassId())
//                .ge(ObjectUtil.isNotNull(beginOfWeek), "create_time", beginOfWeek)
//                .le(ObjectUtil.isNotNull(endOfWeek), "create_time", endOfWeek)
//                .groupBy("student_id");
//        //全班得分排名list
//        List<InitialScoreAllocation> initialList = initialScoreAllocationManager.list(wrapper);
//        log.info("获取一段时间内班级排名得分 返回结果：[{}]", JSONUtil.toJsonStr(initialList));
//        return initialList;
//    }

//    /**
//     * <<<<<<< HEAD
//     * 获取学生档案 得分 加分 减分
//     */
//    private BehaviourRecordSynthesisScoreVO getStudentFileScore(BehaviourStudentFileQueryDTO behaviourStudentFileQueryDTO) {
//        LambdaQueryWrapper<BehaviourRecord> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.select(BehaviourRecord::getClassId, BehaviourRecord::getStudentId, BehaviourRecord::getInfoName,
//                        BehaviourRecord::getScore)
//                .eq(StrUtil.isNotBlank(WebUtil.getTenantId()), BehaviourRecord::getTenantId, WebUtil.getTenantId())
//                .eq(StrUtil.isNotBlank(WebUtil.getSchoolId()), BehaviourRecord::getSchoolId, WebUtil.getSchoolId())
//                .eq(StrUtil.isNotBlank(WebUtil.getCampusId()), BehaviourRecord::getCampusId, WebUtil.getCampusId())
//                .in(CollUtil.isNotEmpty(behaviourStudentFileQueryDTO.getClassIds()), BehaviourRecord::getClassId,
//                        behaviourStudentFileQueryDTO.getClassIds())
//                .eq(StrUtil.isNotBlank(behaviourStudentFileQueryDTO.getStudentId()), BehaviourRecord::getStudentId,
//                        behaviourStudentFileQueryDTO.getStudentId())
//                .ge(ObjectUtil.isNotNull(behaviourStudentFileQueryDTO.getStartTime()), BehaviourRecord::getSubmitTime
//                        , behaviourStudentFileQueryDTO.getStartTime())
//                .le(ObjectUtil.isNotNull(behaviourStudentFileQueryDTO.getEndTime()), BehaviourRecord::getSubmitTime,
//                        behaviourStudentFileQueryDTO.getEndTime())
//                .eq(BehaviourRecord::getIsScore, Constant.YES);
//        if (CollUtil.isNotEmpty(behaviourStudentFileQueryDTO.getClassIds())) {
//
//        }
//        List<BehaviourRecord> behaviourRecords = behaviourRecordMapper.selectList(queryWrapper);
//        BehaviourRecordSynthesisScoreVO scoreVO = new BehaviourRecordSynthesisScoreVO();
//        if (ObjectUtil.isNotNull(behaviourRecords)) {
//            //加分
//            BigDecimal addScore =
//                    behaviourRecords.stream().filter(single -> single.getStudentId().equals(behaviourStudentFileQueryDTO.getStudentId())
//                                    && Constant.ONE.equals(single.getScore().compareTo(BigDecimal.ZERO))).collect(Collectors.toList())
//                            .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
//            //减分
//            BigDecimal subtractScore =
//                    behaviourRecords.stream().filter(single -> single.getStudentId().equals(behaviourStudentFileQueryDTO.getStudentId())
//                                    && Constant.NEGATIVE_ONE.equals(single.getScore().compareTo(BigDecimal.ZERO))).collect(Collectors.toList())
//                            .stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
//            //得分
//            BigDecimal sumScore = addScore.add(subtractScore);
//            scoreVO.setSumScore(sumScore).setAddScore(addScore).setSubtractScore(subtractScore);
//        }
//        log.info("获取学生档案 得分 加分 减分 返回结果：[{}]", JSONUtil.toJsonStr(scoreVO));
//        return scoreVO;
//    }

//    /**
//     * =======
//     * >>>>>>> feature-v1.5.12
//     * 获取筛选时间范围内所有周或者月的开始、结束时间
//     *
//     * @param behaviourRecordQueryDTO
//     * @param behaviourGrowthTrendVO
//     * @param submitTimeVOS
//     */
//    private void listWeekMonthTime(BehaviourRecordQueryDTO behaviourRecordQueryDTO,
//                                   BehaviourGrowthTrendVO behaviourGrowthTrendVO,
//                                   ArrayList<SubmitTimeVO> submitTimeVOS) {
//        //周
//        if (behaviourGrowthTrendVO.getWeekOrMonthCode().equals(TimeEnum.WEEK.getCode())) {
//            //起始日开始时间
//            DateTime firstBeginTime = DateUtil.beginOfDay(behaviourRecordQueryDTO.getStartTime());
//            //起始日的周末
//            DateTime firstEndTime = DateUtil.endOfWeek(behaviourRecordQueryDTO.getStartTime());
//            submitTimeVOS.add(new SubmitTimeVO().setStartTime(firstBeginTime).setEndTime(firstEndTime));
//            //获取筛选时间范围内所有周的开始、结束时间
//            while (firstEndTime.isBefore(behaviourRecordQueryDTO.getEndTime())) {
//                //第二周开始时间
//                DateTime nextBeginTime = DateUtil.offsetSecond(firstEndTime, 1);
//                //第二周结束时间
//                DateTime nextEndTime = DateUtil.endOfWeek(nextBeginTime);
//                submitTimeVOS.add(new SubmitTimeVO().setStartTime(nextBeginTime).setEndTime(nextEndTime));
//                firstEndTime = nextEndTime;
//            }
//            //月
//        } else {
//            //起始日开始时间
//            DateTime firstBeginTime = DateUtil.beginOfDay(behaviourRecordQueryDTO.getStartTime());
//            //起始日的月末
//            DateTime firstEndTime = DateUtil.endOfMonth(behaviourRecordQueryDTO.getStartTime());
//            submitTimeVOS.add(new SubmitTimeVO().setStartTime(firstBeginTime).setEndTime(firstEndTime));
//            //获取筛选时间范围内所有月的开始、结束时间
//            while (firstEndTime.isBefore(behaviourRecordQueryDTO.getEndTime())) {
//                //第二月开始时间
//                DateTime nextBeginTime = DateUtil.offsetSecond(firstEndTime, 1);
//                //第二月结束时间
//                DateTime nextEndTime = DateUtil.endOfMonth(nextBeginTime);
//                submitTimeVOS.add(new SubmitTimeVO().setStartTime(nextBeginTime).setEndTime(nextEndTime));
//                firstEndTime = nextEndTime;
//            }
//        }
//    }

//    /**
//     * 获取该学生总分数
//     *
//     * @param behaviourRecordQueryDTO
//     * @param submitBeginTime
//     * @param submitEndTime
//     */
//    private BehaviourRecord getStudentScore(BehaviourRecordQueryDTO behaviourRecordQueryDTO, Date submitBeginTime,
//                                            Date submitEndTime) {
//        QueryWrapper<BehaviourRecord> wrapper = new QueryWrapper<>();
//        //学生分数
//        wrapper.select("sum(score) as score")
//                .eq(StrUtil.isNotBlank(WebUtil.getTenantId()), "tenant_id", WebUtil.getTenantId())
//                .eq(StrUtil.isNotBlank(WebUtil.getSchoolId()), "school_id", WebUtil.getSchoolId())
//                .eq(StrUtil.isNotBlank(WebUtil.getCampusId()), "campus_id", WebUtil.getCampusId())
//                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getClassId()), "class_id",
//                        behaviourRecordQueryDTO.getClassId())
//                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getStudentId()), "student_id",
//                        behaviourRecordQueryDTO.getStudentId())
//                .ge(ObjectUtil.isNotNull(submitBeginTime), "submit_time", submitBeginTime)
//                .le(ObjectUtil.isNotNull(submitEndTime), "submit_time", submitEndTime)
//                .notIn(CollUtil.isNotEmpty(behaviourRecordQueryDTO.getUnNeedBehaviourRecordIds()), "id",
//                        behaviourRecordQueryDTO.getUnNeedBehaviourRecordIds())
//                .eq("is_score", Constant.YES);
//        BehaviourRecord behaviourRecord = behaviourRecordMapper.selectOne(wrapper);
//        log.info("获取该学生总分数 返回结果：[{}]", JSONUtil.toJsonStr(behaviourRecord));
//        return behaviourRecord;
//    }
//
//    /**
//     * 班级人数
//     *
//     * @param behaviourRecordQueryDTO
//     * @return
//     */
//    private Long getClassPeopleCount(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
//        //班级人数
//        QueryWrapper<BehaviourRecord> peopleWrapper = new QueryWrapper<>();
//        peopleWrapper.select("DISTINCT student_id")
//                .eq(StrUtil.isNotBlank(WebUtil.getTenantId()), "tenant_id", WebUtil.getTenantId())
//                .eq(StrUtil.isNotBlank(WebUtil.getSchoolId()), "school_id", WebUtil.getSchoolId())
//                .eq(StrUtil.isNotBlank(WebUtil.getCampusId()), "campus_id", WebUtil.getCampusId())
//                .eq(StrUtil.isNotBlank(behaviourRecordQueryDTO.getClassId()), "class_id",
//                        behaviourRecordQueryDTO.getClassId())
//                .ge(ObjectUtil.isNotNull(behaviourRecordQueryDTO.getStartTime()), "submit_time",
//                        behaviourRecordQueryDTO.getStartTime())
//                .le(ObjectUtil.isNotNull(behaviourRecordQueryDTO.getEndTime()), "submit_time",
//                        behaviourRecordQueryDTO.getEndTime())
//                .eq("is_score", Constant.YES);
//        Long classPeopleCount = behaviourRecordMapper.selectCount(peopleWrapper);
//        log.info("班级人数 返回结果：[{}]", JSONUtil.toJsonStr(classPeopleCount));
//        return classPeopleCount;
//    }

    /**
     * 学生画像--饼图 方法无调用 不设置其他育得分
     *
     * @param dto
     * @return
     */
    @Deprecated
    public BehaviourAnalysisModuleListVO listStudentBehaviour(BehaviourAnalysisDTO dto) {
        StudentBehaviourAnalysisVO analysis = getStudentBehaviourAnalysis(dto);

        // 查当前学校指标
        Map<Long, List<Target>> targetMap = targetService.list(new LambdaQueryWrapper<Target>()
                .eq(Target::getCampusId, WebUtil.getCampusId())).stream().collect(Collectors.groupingBy(Target::getId));
        // 查当前学校指标分组
        Map<Long, List<TargetGroup>> groupMap = targetGroupService.list(new LambdaQueryWrapper<TargetGroup>()
                        .eq(TargetGroup::getCampusId, WebUtil.getCampusId())).stream()
                .collect(Collectors.groupingBy(TargetGroup::getId));

        List<StudentBehaviourAnalysisInnerDetailVO> behaviourList = analysis.getBehaviourList();

        List<StudentBehaviourAnalysisInnerDetailVO> praiseBehaviourList = analysis.getPraiseBehaviourList();

        List<StudentBehaviourAnalysisInnerDetailVO> toImproveBehaviourList = analysis.getToImproveBehaviourList();

        List<StudentBehaviourAnalysisInnerDetailVO> noScoreBehaviourList = analysis.getNoScoreBehaviourList();

        List<StudentBehaviourAnalysisInnerDetailVO> totalBehaviourList = new ArrayList<>();

        setTargetInfo(targetMap, groupMap, behaviourList, 0);
        setTargetInfo(targetMap, groupMap, praiseBehaviourList, 1);
        setTargetInfo(targetMap, groupMap, toImproveBehaviourList, 2);
        setTargetInfo(targetMap, groupMap, noScoreBehaviourList, 3);

        totalBehaviourList.addAll(behaviourList);
        totalBehaviourList.addAll(praiseBehaviourList);
        totalBehaviourList.addAll(toImproveBehaviourList);
        totalBehaviourList.addAll(noScoreBehaviourList);

        return new BehaviourAnalysisModuleListVO(changeToTreeModule(totalBehaviourList));

    }

    /**
     * 转换为树形结构
     *
     * @param totalBehaviourList
     * @return
     */
    private List<BehaviourAnalysisVOInnerModule> changeToTreeModule(
            List<StudentBehaviourAnalysisInnerDetailVO> totalBehaviourList) {

        BehaviourAnalysisVOInnerModule moral =
                new BehaviourAnalysisVOInnerModule().setModuleCode(ModuleEnum.MORAL.getCode())
                        .setModuleName(ModuleEnum.MORAL.getMessage());
        BehaviourAnalysisVOInnerModule wisdom =
                new BehaviourAnalysisVOInnerModule().setModuleCode(ModuleEnum.WISDOM.getCode())
                        .setModuleName(ModuleEnum.WISDOM.getMessage());
        BehaviourAnalysisVOInnerModule sport =
                new BehaviourAnalysisVOInnerModule().setModuleCode(ModuleEnum.SPORT.getCode())
                        .setModuleName(ModuleEnum.SPORT.getMessage());
        BehaviourAnalysisVOInnerModule pretty =
                new BehaviourAnalysisVOInnerModule().setModuleCode(ModuleEnum.PRETTY.getCode())
                        .setModuleName(ModuleEnum.PRETTY.getMessage());
        BehaviourAnalysisVOInnerModule work =
                new BehaviourAnalysisVOInnerModule().setModuleCode(ModuleEnum.WORK.getCode())
                        .setModuleName(ModuleEnum.WORK.getMessage());
        List<BehaviourAnalysisVOInnerModule> moduleList = Lists.newArrayList(moral, wisdom, sport, pretty, work);

        for (StudentBehaviourAnalysisInnerDetailVO behaviour : totalBehaviourList) {
            if (ModuleEnum.MORAL.getCode().equals(behaviour.getModuleCode())) {
                setGoalTarget(behaviour, moral);
                continue;
            }
            if (ModuleEnum.WISDOM.getCode().equals(behaviour.getModuleCode())) {
                setGoalTarget(behaviour, wisdom);
                continue;
            }
            if (ModuleEnum.SPORT.getCode().equals(behaviour.getModuleCode())) {
                setGoalTarget(behaviour, sport);
                continue;
            }
            if (ModuleEnum.PRETTY.getCode().equals(behaviour.getModuleCode())) {
                setGoalTarget(behaviour, pretty);
                continue;
            }
            if (ModuleEnum.WORK.getCode().equals(behaviour.getModuleCode())) {
                setGoalTarget(behaviour, work);
                continue;
            }

        }
        return moduleList;
    }

    /**
     * 把行为放到指定的模块
     *
     * @param behaviour
     * @param behaviourModule
     */
    private void setGoalTarget(StudentBehaviourAnalysisInnerDetailVO behaviour,
                               BehaviourAnalysisVOInnerModule behaviourModule) {
        List<BehaviourAnalysisVOInnerTarget> targetList = behaviourModule.getTargetList();
        if (CollUtil.isEmpty(targetList)) {
            targetList = new ArrayList<>();
            behaviourModule.setTargetList(targetList);
        }
        BehaviourAnalysisVOInnerTarget target;
        // 判断指标列表是否已包含该指标
        List<BehaviourAnalysisVOInnerTarget> goalTargetList =
                targetList.stream().filter(currentTarget -> currentTarget.getTargetId().equals(behaviour.getTargetId()))
                        .collect(Collectors.toList());
        if (CollUtil.isEmpty(goalTargetList)) {
            target = new BehaviourAnalysisVOInnerTarget();
            target.setTargetId(behaviour.getTargetId());
            target.setTargetName(behaviour.getTargetName());

            targetList.add(target);
        } else {
            target = goalTargetList.get(0);
        }

        // 封装指标内部数据
        if (behaviour.getType().equals(0)) {
            // 全部指标
            // 类型转换

            return;
        }
        // 封装指标内部数据
        if (behaviour.getType().equals(1)) {
            // 加分指标
            // 类型转换
            target.getPraiseBehaviourList().add(convert.toBehaviour(behaviour));
            return;
        }
        // 封装指标内部数据
        if (behaviour.getType().equals(2)) {
            // 减分指标
            // 类型转换
            target.getToImproveBehaviourList().add(convert.toBehaviour(behaviour));
            return;
        }
        // 封装指标内部数据
        if (behaviour.getType().equals(3)) {
            // 无分数或0分指标
            // 类型转换

        }
    }

    private void setTargetInfo(Map<Long, List<Target>> targetMap, Map<Long, List<TargetGroup>> groupMap,
                               List<StudentBehaviourAnalysisInnerDetailVO> behaviourList, Integer type) {
        behaviourList.stream().forEach(behaviour -> {
            List<Target> targetList = targetMap.get(behaviour.getTargetId());
            if (CollUtil.isEmpty(targetList)) {
                return;
            }
            Target target = targetList.get(0);
            behaviour.setTargetName(target.getTargetName());
            List<TargetGroup> groupList = groupMap.get(target.getGroupId());
            if (CollUtil.isEmpty(groupList)) {
                return;
            }
            TargetGroup group = groupList.get(0);
            behaviour.setModuleCode(group.getModuleCode());
            behaviour.setModuleName(group.getModuleName());
            behaviour.setGroupId(group.getId());
            behaviour.setGroupName(group.getGroupName());
            behaviour.setType(type);
        });
    }

    /**
     * 学生分析对象
     *
     * @param dto
     * @return
     */
    public StudentBehaviourAnalysisVO getStudentBehaviourAnalysis(BehaviourAnalysisDTO dto) {
        // 获取当前学生在时间段内所有行为信息
        List<BehaviourRecord> list = list(dto);
        // 创建学生行为分析对象
        StudentBehaviourAnalysisVO analysis = new StudentBehaviourAnalysisVO();

        // 设置总提交次数
        analysis.setSubmitCount(list.size());
        // 遍历获取
        list.forEach(behaviour -> {
            // 添加到behaviourMap
            analysis.addBehaviourToMapInnerList(behaviour, analysis.getBehaviourMap());
            // 无分数行为记录map
            if (BehaviourScoreTypeUnionEnum.NO_SCORE.getCode().equals(getScoreTypeUnion(behaviour))) {
                analysis.addBehaviourToMapInnerList(behaviour, analysis.getNoScoreMap());
                // 无分数提交数+1
                analysis.noScoreCountAddOne();
            }
            // 加减分类型类型
            Integer scoreType = getScoreType(behaviour);
            // 单选多选加分
            if (BehaviourScoreTypeEnum.OPTION_OVER_ZERO.getCode().equals(scoreType)) {
                // 添加到选项行为map
                analysis.addBehaviourToMapInnerList(behaviour, analysis.getOptionMap());
                // 加分提交次数+1
                analysis.addScoreCountAddOne();
                // 添加到加分map
                analysis.addBehaviourToMapInnerList(behaviour, analysis.getAddScoreMap());
            }
            // 单选多选减分
            if (BehaviourScoreTypeEnum.OPTION_LESS_ZERO.getCode().equals(scoreType)) {
                // 添加到选项行为map
                analysis.addBehaviourToMapInnerList(behaviour, analysis.getOptionMap());
                // 减分提交次数+1
                analysis.subtractionScoreCountAddOne();
                // 添加到减分map
                analysis.addBehaviourToMapInnerList(behaviour, analysis.getSubtractionScoreMap());
            }
            // 分值控件加分
            if (BehaviourScoreTypeEnum.SCORE_CONTROL_OVER_ZERO.getCode().equals(scoreType)) {
                // 添加到分值控件map
                analysis.addBehaviourToMapInnerList(behaviour, analysis.getScoreControlMap());
                // 加分提交次数+1
                analysis.addScoreCountAddOne();
                // 添加到加分map
                analysis.addBehaviourToMapInnerList(behaviour, analysis.getAddScoreMap());
            }
            // 分值控件减分
            if (BehaviourScoreTypeEnum.SCORE_CONTROL_LESS_ZERO.getCode().equals(scoreType)) {
                // 添加到分值控件map
                analysis.addBehaviourToMapInnerList(behaviour, analysis.getScoreControlMap());
                // 减分提交次数+1
                analysis.subtractionScoreCountAddOne();
                // 处理behaviour的分值为负数
                behaviour.setScore(behaviour.getScore().negate());
                // 添加到减分map
                analysis.addBehaviourToMapInnerList(behaviour, analysis.getSubtractionScoreMap());
            }
        });
        // 统计每个指标分数及提交次数
        analysis.addSubmitCountAndScore(analysis.getBehaviourMap(), analysis.getBehaviourCountMap(),
                analysis.getBehaviourSumScoreMap());
        // 统计加分指标分数及提交次数
        analysis.addSubmitCountAndScore(analysis.getAddScoreMap(), analysis.getAddScoreBehaviourCountMap(),
                analysis.getAddScoreBehaviourSumScoreMap());
        // 统计减分指标分数及提交次数
        analysis.addSubmitCountAndScore(analysis.getSubtractionScoreMap(),
                analysis.getSubtractionScoreBehaviourCountMap(), analysis.getSubtractionScoreBehaviourSumScoreMap());
        // 统计无分数指标提交次数
        analysis.addSubmitCountAndScore(analysis.getNoScoreMap(), analysis.getNoScoreBehaviourCountMap(), null);

        // 所有指标 以指标id+模板id+选项id为key，指标名称或选项名称为value
        analysis.getBehaviourMap().keySet().forEach(key -> analysis.getBehaviourNameMap().put(key,
                analysis.getBehaviourMap().get(key).get(0).getInfoName()));

        // 加分行为 以指标id+模板id+选项id为key，指标名称或选项名称为value
        analysis.getAddScoreMap().keySet().forEach(key -> analysis.getAddScoreBehaviourNameMap().put(key,
                analysis.getAddScoreMap().get(key).get(0).getInfoName()));

        // 减分行为 以指标id+模板id+选项id为key，指标名称或选项名称为value
        analysis.getSubtractionScoreMap().keySet().forEach(key -> analysis.getSubtractionScoreBehaviourNameMap()
                .put(key, analysis.getSubtractionScoreMap().get(key).get(0).getInfoName()));

        // 无分行为  以指标id+模板id+选项id为key，指标名称或选项名称为value
        analysis.getNoScoreMap().keySet().forEach(key -> analysis.getNoScoreBehaviourNameMap().put(key,
                analysis.getNoScoreMap().get(key).get(0).getInfoName()));
        // 保存全部行为、加分、减分、无分对象列表
        analysis.addBehaviourList();
        return analysis;
    }

    /**
     * NO_SCORE(1, "无分或0分"), OVER_ZERO(2, "正分"), LESS_ZERO(3, "负分");
     *
     * @param behaviour
     * @return
     */
    private Integer getScoreTypeUnion(BehaviourRecord behaviour) {
        Integer scoreType = getScoreType(behaviour);
        return BehaviourScoreTypeUnionEnum.getScoreType(scoreType);

    }

    /**
     * 根据是否开启加分，分数类型判断取值规则 NOT_SCORE(1, "无分"), OPTION_ZERO(2, "单选多选0分"), OPTION_OVER_ZERO(3, "单选多选正分"),
     * OPTION_LESS_ZERO(4, "单选多选负分"), SCORE_CONTROL_ZERO(5, "分值控件0分"), SCORE_CONTROL_OVER_ZERO(6, "分值控件正分"),
     * SCORE_CONTROL_LESS_ZERO(7, "分值控件负分");
     *
     * @param
     * @return
     */
    private Integer getScoreType(BehaviourRecord behaviour) {
        // 1: 没有分数
        if (Boolean.FALSE.equals(behaviour.getIsScore())) {
            return BehaviourScoreTypeEnum.NO_SCORE.getCode();
        }
        // 单选多选 2:单选多选分值等于0  3：大于0 4：小于0
        if (ObjectUtil.isNull(behaviour.getScoreType())) {
            int compare = behaviour.getScore().compareTo(new BigDecimal(Constant.ZERO));

            return compare == 0 ? BehaviourScoreTypeEnum.OPTION_ZERO.getCode() : compare > 0 ?
                    BehaviourScoreTypeEnum.OPTION_OVER_ZERO.getCode() :
                    BehaviourScoreTypeEnum.OPTION_LESS_ZERO.getCode();
        }
        // 加分控件
        if (ObjectUtil.isNotNull(behaviour.getScoreType())) {
            // 5: 加分控件分值等于0
            BigDecimal score = behaviour.getScore();
            if (ObjectUtil.isNull(score)) {
                return BehaviourScoreTypeEnum.NO_SCORE.getCode();
            }
            int compare = behaviour.getScore().compareTo(new BigDecimal(Constant.ZERO));
            if (compare == 0) {
                return BehaviourScoreTypeEnum.SCORE_CONTROL_ZERO.getCode();
            }
            return behaviour.getScoreType() > 0 ? BehaviourScoreTypeEnum.SCORE_CONTROL_OVER_ZERO.getCode() :
                    BehaviourScoreTypeEnum.SCORE_CONTROL_LESS_ZERO.getCode();
        }
        // 理论上不存在这种情况
        return 0;
    }

    public List<BehaviourRecord> list(BehaviourAnalysisDTO dto) {
        return this.list(new LambdaQueryWrapper<BehaviourRecord>()
                .eq(BehaviourRecord::getSchoolId, WebUtil.getSchoolId())
                .eq(BehaviourRecord::getCampusId, WebUtil.getCampusId())
                .eq(BehaviourRecord::getStudentId, dto.getStudentId())
                .in(BehaviourRecord::getDataSource, DataSourceEnum.getCodesFromEvaluate())
                .between(BehaviourRecord::getSubmitTime, dto.getStartTime(), dto.getEndTime()));
    }

    /**
     * 五育明细
     *
     * @param dto
     * @return
     */
    /*@Override
    @Deprecated
    public ListModuleDetailVO listModuleDetail(ListModuleDetailDTO dto) {
        ListDateBehaviourDTO listDto = convert.toListDateBehaviourDTO(dto);
        listDto.setCampusId(WebUtil.getCampusId());
        // 获取学生行为记录
        List<BehaviourRecord> behaviourList = behaviourRecordMapper.listDateBehaviour(listDto);

        if (CollUtil.isEmpty(behaviourList)) {
            return null;
        }
        // 获取当前时间范围所有日期（天）
        List<Date> dateList =
                behaviourList.stream().map(BehaviourRecord::getSubmitTime).distinct()
                        .sorted((s1, s2) -> s2.compareTo(s1)).collect(Collectors.toList());

        ListModuleDetailVO detailVO = new ListModuleDetailVO();
        // 设置加分总分和减分总分
        // 加分总分
        BigDecimal addSumScore = behaviourRecordMapper.getSumScoreByCondition(listDto.setType(1));
        // 减分总分
        BigDecimal subtractSumScore = behaviourRecordMapper.getSumScoreByCondition(listDto.setType(2));

        detailVO.setAddSumScore(NumUtil.formatFloatNumber(addSumScore));
        detailVO.setSubtractSumScore(NumUtil.formatFloatNumber(subtractSumScore));

        // behaviourList 处理无效0
        behaviourList.forEach(behaviourRecord -> {
            behaviourRecord.setScore(NumUtil.formatFloatNumber(behaviourRecord.getScore()));
        });
        behaviourList.forEach(behaviourRecord -> {
            behaviourRecord.setScore(NumUtil.formatFloatNumber(behaviourRecord.getScore()));
        });
        // 遍历时间
        for (Date date : dateList) {

            ListModuleDetailVODateInfo dateInfo = new ListModuleDetailVODateInfo();
            detailVO.getDateBehaviourList().add(dateInfo);
            dateInfo.setDate(DateUtil.format(date, "yyyy-MM-dd"));

            List<BehaviourRecord> recordList =
                    behaviourList.stream().filter(behaviour -> date.equals(behaviour.getSubmitTime()))
                            .collect(Collectors.toList());
            // 获取名称为选项的指标名称Map
            Map<Long, String> targetNameMap = new HashMap<>();
            List<Long> targetIds =
                    recordList.stream().filter(s -> InfoTypeEnum.OPTION.getCode().equals(s.getInfoType()))
                            .map(BehaviourRecord::getTargetId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(targetIds)) {
                List<Target> targets = targetMapper.listTargetByIds(targetIds);
                targetNameMap = targets.stream().collect(Collectors.toMap(Target::getId, Target::getTargetName));
            }
            List<ListModuleDetailVOInnerBehaviour> goalBehaviourList =
                    convert.toListModuleDetailVOInnerBehaviourList(recordList);
            Map<Long, String> finalTargetNameMap = targetNameMap;
            goalBehaviourList.forEach(s -> {
                s.setBehaviourId(Convert.toStr(s.getId()));
                // 如果是选项名称的填充指标名称
                if (InfoTypeEnum.OPTION.getCode().equals(s.getInfoType())) {
                    s.setTargetName(finalTargetNameMap.get(s.getTargetId()));
                }
            });
            dateInfo.setBehaviourList(goalBehaviourList);
        }

        log.info("学生画像-五育得分明细 返回结果：[{}]", JSONUtil.toJsonStr(detailVO));
        return detailVO;
    }*/

    @Override
    public ListModuleDetailNewVO listModuleDetailForParent(ListModuleDetailForParentDTO dto) {
        Assert.isTrue(dto.getReviewDetailId() != null, "报告详情id不能为空");

        ListModuleDetailNewVO listModuleDetailNewVO = new ListModuleDetailNewVO();
        List<ReportBehaviourRecord> reportBehaviourRecords =
                reportBehaviourRecordLogic.listByDetailId(dto.getReviewDetailId());
        if (CollUtil.isEmpty(reportBehaviourRecords)) {
            return listModuleDetailNewVO;
        }
        List<Long> behaviourRecordIds = reportBehaviourRecords.stream()
                .map(ReportBehaviourRecord::getBehaviourRecordId).collect(Collectors.toList());
        if (CollUtil.isEmpty(behaviourRecordIds)) {
            return listModuleDetailNewVO;
        }
        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.listByIds(behaviourRecordIds);
        if (CollUtil.isEmpty(behaviourRecords)) {
            return listModuleDetailNewVO;
        }
        return doListModuleDetail(behaviourRecords, 1, dto.getStudentId(), dto.getStartTime(), dto.getEndTime());
    }

    @Override
    public ListModuleDetailNewVO listModuleDetailForCheck(BehaviourRecordQueryDTO dto) {
        ListModuleDetailNewVO listModuleDetailNewVO = new ListModuleDetailNewVO();
        Assert.isTrue(dto.getReviewDetailId() != null, () -> new BizException("审核明细id不能为空"));
        String reviewDetailId = dto.getReviewDetailId();
        List<Long> unNeedBehaviourRecordIds = dto.getUnNeedBehaviourRecordIds();

        Assert.notNull(reviewDetailId, () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL_ID));

        ReportReviewDetail reviewDetail = reportReviewDetailService.getById(reviewDetailId);
        Assert.notNull(reviewDetail, () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL));

        ReportReviewTask reportReviewTask = reportReviewTaskService.getById(reviewDetail.getReviewTaskId());
        Assert.notNull(reportReviewTask, () -> new BizException(ReportErrorEnum.EMPTY_TASK));

        Assert.isTrue(reviewDetail.getPushModule().contains(Convert.toStr(ReportPushRuleEnum.DETAIL.getCode())),
                () -> new BizException(ReportErrorEnum.GET_DETAIL_ERROR));

        //学生行为明细(查全部)
        List<BehaviourRecord> behaviourRecords = this.list(new LambdaQueryWrapper<BehaviourRecord>()
                .in(BehaviourRecord::getStudentId, CollUtil.newArrayList(reviewDetail.getStudentId()))
                .notIn(CollUtil.isNotEmpty(unNeedBehaviourRecordIds), BehaviourRecord::getId, unNeedBehaviourRecordIds)
                .between(BehaviourRecord::getSubmitTime, reviewDetail.getReportStartTime(),
                        reviewDetail.getReportEndTime())
                .orderByDesc(BehaviourRecord::getSubmitTime));

        if (CollUtil.isEmpty(behaviourRecords)) {
            return listModuleDetailNewVO;
        }
        return doListModuleDetail(behaviourRecords, null, null, null, null);
    }

    @Override
    public ListModuleDetailNewVO listModuleDetailNew(ListModuleDetailDTO dto) {

        ListModuleDetailDTO listModuleDetailDTO = convertTime(dto);
        String studentId = dto.getStudentId();

        ListDateBehaviourDTO listDto = convert.toListDateBehaviourDTO(listModuleDetailDTO);
        listDto.setCampusId(WebUtil.getCampusId());
        // 获取学生行为记录
        List<BehaviourRecord> behaviourRecords = behaviourRecordMapper.listDateBehaviour(listDto);
        // 过滤无需统计行为记录
        behaviourRecords = behaviourRecords
                .stream()
                .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                .collect(Collectors.toList());

        ListModuleDetailNewVO listModuleDetailNewVO = doListModuleDetail(behaviourRecords, 1, listModuleDetailDTO.getStudentId(),
                listModuleDetailDTO.getStartTime(), listModuleDetailDTO.getEndTime(), Boolean.TRUE);

        String channelType = dto.getChannelType();
        if ("dailyNotice".equals(channelType)) {
            // 通知钉钉消除小红点
            this.clearDingTalk(studentId);
        }
        return listModuleDetailNewVO;
    }

    private void clearDingTalk(String studentId) {
        String schoolId = Convert.toStr(StudentHolder.getSchoolId());
        Long tenantId = StudentHolder.getTenantId();
        log.info("【清除钉钉每日点评未读数量】-studentId:{},schoolId:{},tenantId:{}", studentId, schoolId, tenantId);
        // 获取这个学校的corpId
        String corpId = dingTalkExchangeHelper.getCorpIdBySchoolIdFromCache(schoolId, tenantId);
        log.info("【清除钉钉每日点评未读数量】-corpId:{}", corpId);
        if (StringUtils.isBlank(corpId)) {
            log.error("【清除钉钉未读数量】-corpId为空");
            return;
        }
        // 获取学生id
        String dingdingStudentId = dingTalkExchangeHelper.getDingdingStudentIdBySaasStudentIdFromCache(studentId, tenantId);
        log.info("【清除钉钉每日点评未读数量】-dingdingStudentId:{}", dingdingStudentId);
        if (StringUtils.isBlank(dingdingStudentId)) {
            log.error("【清除钉钉每日点评未读数量】-dingdingStudentId为空");
            return;
        }
        // 获取accessToken
        String dingdingAccessToken = dingTalkExchangeHelper.getDingdingAccessTokenFromCache(corpId);
        log.info("【清除钉钉每日点评未读数量】-dingdingAccessToken:{}", dingdingAccessToken);
        if (StringUtils.isBlank(dingdingAccessToken)) {
            log.error("【清除钉钉每日点评未读数量】-dingdingAccessToken为空");
            return;
        }
        dailyNoticeClient.clearReadCounts(dingdingAccessToken, dingdingStudentId);
    }


    private List<BehaviourRecord> getHelpBehaviourRecordList(
            List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS) {
        return helpBehaviourRecordPOS.stream().map(helpBehaviour -> {
            BehaviourRecord behaviourRecord = new BehaviourRecord();
            behaviourRecord.setCampusId(helpBehaviour.getCampusId());
            behaviourRecord.setCampusSectionId(helpBehaviour.getCampusSectionId());
            behaviourRecord.setCampusSectionCode(helpBehaviour.getCampusSectionCode());
            behaviourRecord.setGradeId(helpBehaviour.getGradeId());
            behaviourRecord.setGradeCode(helpBehaviour.getGradeCode());
            behaviourRecord.setClassId(helpBehaviour.getClassId());
            behaviourRecord.setStudentId(helpBehaviour.getStudentId());
            behaviourRecord.setInfoName(helpBehaviour.getHelpDesc());
            behaviourRecord.setScoreType(helpBehaviour.getScoreType());
            behaviourRecord.setScore(helpBehaviour.getScore());
            behaviourRecord.setSubmitTime(helpBehaviour.getCreateTime());
            behaviourRecord.setTenantId(helpBehaviour.getTenantId());
            behaviourRecord.setSchoolId(helpBehaviour.getSchoolId());
            behaviourRecord.setAppraisalName(helpBehaviour.getAppraisalName());
            behaviourRecord.setAppraisalType(AppraisalEnum.TEACHER.getCode());
            return behaviourRecord;
        }).collect(Collectors.toList());
    }

    // 在设置 startTime 和 endTime 之后调用这个方法来进行时分秒转换
    public ListModuleDetailDTO convertTime(ListModuleDetailDTO dto) {
        Date startTime = dto.getStartTime();
        Date endTime = dto.getEndTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (isTimeAlreadyConverted(startTime, dateFormat) && isTimeAlreadyConverted(endTime, dateFormat)) {
            startTime = setTimeToZero(startTime, dateFormat);
            endTime = setTimeToMax(endTime, dateFormat);
        }
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        return dto;
    }

    // 判断时间是否已经以 "00:00:00" 结尾
    private boolean isTimeAlreadyConverted(Date date, SimpleDateFormat dateFormat) {
        if (date != null) {
            String dateString = dateFormat.format(date);
            return dateString.endsWith("00:00:00");
        }
        return false;
    }

    // 设置时分秒为 "00:00:00"
    private Date setTimeToZero(Date date, SimpleDateFormat dateFormat) {
        try {
            String dateString = dateFormat.format(date);
            dateString = dateString.substring(0, 11) + "00:00:00";
            return dateFormat.parse(dateString);
        } catch (Exception e) {
            throw new BizException("设置时分秒为 00:00:00 失败");
        }
    }

    // 设置时分秒为 "23:59:59"
    private Date setTimeToMax(Date date, SimpleDateFormat dateFormat) {
        try {
            String dateString = dateFormat.format(date);
            dateString = dateString.substring(0, 11) + "23:59:59";
            return dateFormat.parse(dateString);
        } catch (Exception e) {
            throw new BizException("设置时分秒为 00:00:00 失败");
        }
    }


    /**
     * 如果没有加分项分值,分数信息设置为空
     *
     * @param behaviorRecordId 行为记录id
     * @return
     */
    @Override
    public Boolean updateScoreInfoIsNull(Long behaviorRecordId) {
        return behaviourRecordMapper.updateScoreInfoIsNull(behaviorRecordId);
    }

    /**
     * 查学生奖章信息
     *
     * @param dto
     * @return
     */
    @Override
    public Page<MedalInfoVO> pageStudentMedalInfo(StudentMedalQueryDTO dto) {
        return medalUserAcquireRecordService.pageStudentMedalInfo(dto);
    }

    /**
     * 按条件查询学生指标分数
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, List<StudentBehaviourScoreDTO>> queryStudentBehaviourScore(QueryBehaviourScoreDTO dto) {

        log.info(
                "「成绩报告」-「按条件查询学生指标分数」,入参-「CampusSectionCode:{}」-「SubjectCode:{}」-「StartTime:{}」-「EndTime:{}」-「BusinessType:{}」-「Targets:{}」",
                dto.getCampusSectionCode(),
                dto.getSubjectCode(),
                dto.getStartTime(),
                dto.getEndTime(),
                dto.getBusinessType(),
                dto.getTargets());

        //入参校验
        Assert.isTrue(ObjectUtil.isNotNull(dto.getStartTime()),
                () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));
        Assert.isTrue(ObjectUtil.isNotNull(dto.getEndTime()),
                () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));
        Assert.isTrue(ObjectUtil.isNotNull(dto.getBusinessType()),
                () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));
        Assert.notEmpty(dto.getBusinessIds(),
                () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));
        Assert.notEmpty(dto.getTargets(), () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));

        Map<String, List<StudentBehaviourScoreDTO>> studentIdScoreMap = new HashMap<>();
        //时间范围内不同类型的学生点评项
        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.listRecordByConditions(dto);
        if (CollUtil.isEmpty(behaviourRecords)) {
            return studentIdScoreMap;
        }
        //过滤学科
        behaviourRecords = behaviourRecords
                .stream()
                .filter(s -> s.getSubjectCode().contains(dto.getSubjectCode()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(behaviourRecords)) {
            log.warn("[成绩报告]-[按条件查询学生指标分数],时间范围内不同类型的学生点评项为空，过滤总数:[{}]",
                    behaviourRecords.size());
            return studentIdScoreMap;
        }
        //按学生 id 分组
        Map<String, List<BehaviourRecord>> studentBehaviourMap =
                behaviourRecords
                        .stream()
                        .collect(Collectors.groupingBy(BehaviourRecord::getStudentId));

        studentBehaviourMap.entrySet().stream().forEach(entry -> {
            ArrayList<StudentBehaviourScoreDTO> studentBehaviourScoreDTOS = new ArrayList<>();
            dto.getTargets().stream().forEach(target -> {
                BigDecimal score = BigDecimal.ZERO;
                //获取该选择的分数
                score = getBigDecimal(entry, target, score);
                StudentBehaviourScoreDTO studentBehaviourScoreDTO = new StudentBehaviourScoreDTO();
                studentBehaviourScoreDTO.setDimName(target.getDimName());
                studentBehaviourScoreDTO.setScore(score);
                studentBehaviourScoreDTOS.add(studentBehaviourScoreDTO);
            });
            List<StudentBehaviourScoreDTO> mergedList = StudentBehaviourScoreDTO.mergeScores(studentBehaviourScoreDTOS);
            studentIdScoreMap.put(entry.getKey(), mergedList);
        });

        return studentIdScoreMap;
    }

    @Override
    public Map<String/*studentId*/, List<StudentBehaviourScoreDTO>> queryStudentBehaviourScoreV2(
            QueryBehaviourScoreDTO dto, List<String> subjectCodes) {

        log.info(
                "【成绩计算】-【Step-13.0】-【阶段性评价计算】-【获取点评分数】-【按条件查询学生指标分数】,入参：【CampusSectionCode:{}】-【SubjectCode:{}】-【StartTime:{}】-【EndTime:{}】-【BusinessType:{}】-【Targets:{}】",
                dto.getCampusSectionCode(),
                dto.getSubjectCode(),
                dto.getStartTime(),
                dto.getEndTime(),
                dto.getBusinessType(),
                dto.getTargets());

        //入参校验
        Assert.isTrue(ObjectUtil.isNotNull(dto.getStartTime()),
                () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));
        Assert.isTrue(ObjectUtil.isNotNull(dto.getEndTime()),
                () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));
        Assert.isTrue(ObjectUtil.isNotNull(dto.getBusinessType()),
                () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));
        Assert.notEmpty(dto.getBusinessIds(),
                () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));
        Assert.notEmpty(dto.getTargets(), () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));

        Map<String, List<StudentBehaviourScoreDTO>> studentIdScoreMap = new HashMap<>();

        //时间范围内不同类型的学生点评项
        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.listRecordByConditions(dto);
        if (CollUtil.isEmpty(behaviourRecords)) {
            log.warn(
                    "【成绩计算】-【Step-13.0】-【阶段性评价计算】-【获取点评分数】-【按条件查询学生指标分数】,条件范围内不同类型的学生点评项为空，入参:[{}]",
                    JSONUtil.toJsonStr(dto));
            return studentIdScoreMap;
        }

        //过滤学科
        behaviourRecords = behaviourRecords.stream().filter(s -> {
            if (CharSequenceUtil.isBlank(s.getSubjectCode())) {
                return false;
            }
            String[] subjects = s.getSubjectCode().split(",");
            for (String subjectCode : subjects) {
                if (subjectCodes.contains(subjectCode)) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());

        if (CollUtil.isEmpty(behaviourRecords)) {
            log.warn(
                    "【成绩计算】-【Step-13.0】-【阶段性评价计算】-【获取点评分数】-【按条件查询学生指标分数】,对应学科范围内学生点评项为空，入参:[{}]",
                    JSONUtil.toJsonStr(dto));
            return studentIdScoreMap;
        }

        log.info("【成绩计算】-【Step-13.0】-【阶段性评价计算】-【获取点评分数】-【按条件查询学生指标分数】, 点评数量:{}",
                behaviourRecords.size());

        //按学生 id 分组
        Map<String/*studentId*/, List<BehaviourRecord>> studentBehaviourMap =
                behaviourRecords
                        .stream()
                        .collect(Collectors.groupingBy(BehaviourRecord::getStudentId));

        List<SubjectEvaluationDimTargetBusinessMergePO> dimTargetBusinessMergePOS = dto.getTargets();

        studentBehaviourMap
                .entrySet()
                .stream()
                .forEach(entry -> {

                    ArrayList<StudentBehaviourScoreDTO> studentBehaviourScoreDTOS = new ArrayList<>();

                    dimTargetBusinessMergePOS
                            .stream()
                            .forEach(target -> {
                                BigDecimal score = BigDecimal.ZERO;
                                //获取该选择的分数
                                score = getBigDecimal(entry, target, score);
                                StudentBehaviourScoreDTO studentBehaviourScoreDTO = new StudentBehaviourScoreDTO();
                                studentBehaviourScoreDTO.setDimName(target.getDimName());
                                studentBehaviourScoreDTO.setScore(score);
                                studentBehaviourScoreDTOS.add(studentBehaviourScoreDTO);
                            });
                    List<StudentBehaviourScoreDTO> mergedList = StudentBehaviourScoreDTO.mergeScores(
                            studentBehaviourScoreDTOS);

                    studentIdScoreMap.put(entry.getKey(), mergedList);
                });

        return studentIdScoreMap;
    }


    /**
     * 学生评价记录
     *
     * @param dto
     * @return
     */
    @Override
    public Page<ListModuleDetailVODateNewInfo> pageStudentRecords(PageStudentRecordQueryDTO dto) {
        //入参校验
        Assert.isFalse(BeanUtil.hasNullField(dto, "targetId"),
                () -> new BizException(BizExceptionEnum.PARAMETER_EMPTY_ERROR.getMessage()));

        PageEduStudentQueryDTO pageEduStudentQueryDTO = new PageEduStudentQueryDTO();
        pageEduStudentQueryDTO.setTargetId(dto.getTargetId());
        pageEduStudentQueryDTO.setTimeCode(dto.getTimeCode());
        pageEduStudentQueryDTO.setCampusSectionId(dto.getCampusSectionId());
        TermVo timePro = basicInfoService.getTimePro(pageEduStudentQueryDTO);
        if (BeanUtil.isEmpty(timePro)) {
            log.info("[学生评价记录查询]，获取学期时间失败，入参：【{}】", JSONUtil.toJsonStr(dto));
            return new Page<>();
        }
        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.list(new LambdaQueryWrapper<BehaviourRecord>()
                .eq(BehaviourRecord::getStudentId, dto.getStudentId())
                .between(BehaviourRecord::getSubmitTime,
                        DateUtil.beginOfDay(DateUtil.parseDate(timePro.getStartTime())),
                        DateUtil.endOfDay(DateUtil.parseDate(timePro.getEndTime())))
                .eq(Constant.TWO.equals(dto.getTargetType()) && StrUtil.isNotBlank(dto.getTargetId()),
                        BehaviourRecord::getTargetId, dto.getTargetId())
                .orderByDesc(BehaviourRecord::getSubmitTime)
        );
        if (CollUtil.isEmpty(behaviourRecords)) {
            log.info("学生评价记录查询为空，直接返回");
            return new Page<>();
        }

        ListModuleDetailNewVO listModuleDetailNewVO = doListModuleDetail(behaviourRecords, null, null, null, null);
        List<ListModuleDetailVODateNewInfo> dateBehaviourList = listModuleDetailNewVO.getDateBehaviourList();
        List<ListModuleDetailVODateNewInfo> moduleDetailVODateNewInfos =
                dateBehaviourList.stream().skip((long) (dto.getPageNum() - Constant.ONE) * dto.getPageSize())
                        .limit(dto.getPageSize()).collect(Collectors.toList());
        Page<ListModuleDetailVODateNewInfo> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        page.setRecords(moduleDetailVODateNewInfos);
        page.setTotal(dateBehaviourList.size());
        return page;
    }


    /**
     * 按条件查询学生五育分数 ReportModuleScoreVO对象中只用到了总分 各育分数没有用到 故不设置其他育
     *
     * @param dto
     * @return
     */
    @Override
    public List<ReportModuleScoreVO> queryStudentScore(QueryBehaviourScoreDTO dto) {
        log.info("[按条件查询学生指标分数],入参：【{}】", JSONUtil.toJsonStr(dto));
        //入参校验
        Assert.isFalse(ObjectUtil.isNull(dto.getStartTime()) || ObjectUtil.isNull(dto.getEndTime()),
                () -> new BizException(BizExceptionEnum.PARAMETER_ERROR.getMessage()));

        List<ReportModuleScoreVO> pairs = Lists.newArrayListWithCapacity(dto.getBusinessIds().size());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<BehaviourRecord> behaviourRecords = Lists.newCopyOnWriteArrayList();
        List<String> ids = dto.getBusinessIds();
        List<List<String>> partition = Lists.partition(ids, 15);
        //多线程处理
        partition.parallelStream().forEach(s -> {
                    //时间范围内不同类型的学生点评项
                    behaviourRecords.addAll(behaviourRecordManager.list(new LambdaQueryWrapper<BehaviourRecord>()
                            .select(BehaviourRecord::getStudentId, BehaviourRecord::getAppraisalId,
                                    BehaviourRecord::getModuleCode, BehaviourRecord::getScore)
                            .ge(BehaviourRecord::getSubmitTime, dto.getStartTime())
                            .le(BehaviourRecord::getSubmitTime, dto.getEndTime())
                            .eq(ObjectUtil.isNotNull(dto.getEvaluatorId()), BehaviourRecord::getAppraisalId,
                                    dto.getEvaluatorId())
                            .in(ReportBusinessTypeEnum.STUDENT.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                                    dto.getBusinessIds()), BehaviourRecord::getStudentId, s)
                            .in(ReportBusinessTypeEnum.CLASS.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                                    dto.getBusinessIds()), BehaviourRecord::getClassId, dto.getBusinessIds())
                            .in(ReportBusinessTypeEnum.GRADE.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                                    dto.getBusinessIds()), BehaviourRecord::getGradeId, dto.getBusinessIds())
                            .in(ReportBusinessTypeEnum.SECTION.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                                    dto.getBusinessIds()), BehaviourRecord::getCampusSectionId, dto.getBusinessIds())
                            .in(ReportBusinessTypeEnum.CAMPUS.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                                    dto.getBusinessIds()), BehaviourRecord::getCampusId, dto.getBusinessIds())
                            .eq(BehaviourRecord::getIsScore, Constant.YES)));
                }
        );

        stopWatch.stop();
        log.info("[按条件查询学生五育分数]-sql耗时：【{}】", stopWatch.getLastTaskTimeMillis());
        if (CollUtil.isEmpty(behaviourRecords)) {
            log.info("[按条件查询学生五育分数]-当前查询为空，直接返回，入参：【{}】", JSONUtil.toJsonStr(dto));
            return Collections.emptyList();
        }
        stopWatch.start();
        //按学生 id 分组
        Map<String, List<BehaviourRecord>> studentBehaviourMap =
                behaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecord::getStudentId));

        studentBehaviourMap.forEach((key, records) -> {
            records = records.stream().filter(s -> ObjectUtil.isNotNull(s.getScore())).collect(Collectors.toList());
            //五育得分
            ReportModuleScoreVO reportModuleScoreVo = new ReportModuleScoreVO();
            reportModuleScoreVo.setStudentId(key);
            reportModuleScoreVo.setAppraisalId(dto.getEvaluatorId());
            reportModuleScoreVo.setMoral(
                    records.stream().filter(s -> ModuleEnum.MORAL.getCode().equals(s.getModuleCode()))
                            .map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add));
            reportModuleScoreVo.setWisdom(
                    records.stream().filter(s -> ModuleEnum.WISDOM.getCode().equals(s.getModuleCode()))
                            .map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add));
            reportModuleScoreVo.setSport(
                    records.stream().filter(s -> ModuleEnum.SPORT.getCode().equals(s.getModuleCode()))
                            .map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add));
            reportModuleScoreVo.setPretty(
                    records.stream().filter(s -> ModuleEnum.PRETTY.getCode().equals(s.getModuleCode()))
                            .map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add));
            reportModuleScoreVo.setWork(
                    records.stream().filter(s -> ModuleEnum.WORK.getCode().equals(s.getModuleCode()))
                            .map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add));
            reportModuleScoreVo.setTotalScore(records.stream().map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO,
                    BigDecimal::add));
            pairs.add(reportModuleScoreVo);
        });
        stopWatch.stop();
        log.info("[按条件查询学生五育分数]-内存处理耗时：【{}】", stopWatch.getLastTaskTimeMillis());
        return pairs;
    }

    @Override
    public List<ReportModuleScoreStatisticsVO> queryStudentScoreStatistics(QueryBehaviourScoreDTO dto) {
        log.info("[按条件查询学生五育统计分数],入参：【{}】", JSONUtil.toJsonStr(dto));
        //入参校验
        Assert.isFalse(ObjectUtil.isNull(dto.getStartTime()) || ObjectUtil.isNull(dto.getEndTime()) || CollUtil.isEmpty(
                dto.getBusinessIds()), () -> new BizException(BizExceptionEnum.PARAMETER_ERROR.getMessage()));

        List<ReportModuleScoreStatisticsVO> pairs = new ArrayList<>();
        List<String> subjectCodes = dto.getSubjectCodes();
        if (CollUtil.isEmpty(subjectCodes)) {
            log.warn("[按条件查询学生五育统计分数]-学科为空，直接返回，入参：【{}】", JSONUtil.toJsonStr(dto));
            return pairs;
        }
        //所有行为分值
        QueryWrapper<BehaviourRecord> queryWrapper = new QueryWrapper<BehaviourRecord>()
                .select("id,student_id,module_code ,target_id,template_id,option_id,info_type, info_name,sum(score) " +
                        "as score, score_type, target_name, data_source, not_part_count")
                .eq("campus_id", WebUtil.getCampusId())
                .ge(ObjectUtil.isNotNull(dto.getStartTime()), "submit_time", dto.getStartTime())
                .le(ObjectUtil.isNotNull(dto.getEndTime()), "submit_time", dto.getEndTime())
                .eq(ObjectUtil.isNotNull(dto.getEvaluatorId()), "appraisal_id", dto.getEvaluatorId())
                .in(ReportBusinessTypeEnum.STUDENT.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                        dto.getBusinessIds()), "student_id", dto.getBusinessIds())
                .in(ReportBusinessTypeEnum.CLASS.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                        dto.getBusinessIds()), "class_id", dto.getBusinessIds())
                .in(ReportBusinessTypeEnum.GRADE.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                        dto.getBusinessIds()), "grade_id", dto.getBusinessIds())
                .in(ReportBusinessTypeEnum.SECTION.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                        dto.getBusinessIds()), "campus_section_id", dto.getBusinessIds())
                .in(ReportBusinessTypeEnum.CAMPUS.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                        dto.getBusinessIds()), "campus_id", dto.getBusinessIds())
                .in("module_code", ModuleEnum.normalModuleMap.keySet())
                .eq("is_score", Constant.YES)
                .and(i -> {
                    for (String pattern : subjectCodes) {
                        i.or().like("subject_code", pattern);
                    }
                })
                .groupBy("student_id, module_code, target_id, option_id, info_name,score_type")
                .orderByDesc("score")
                .orderByDesc("submit_time");

        List<BehaviourRecord> behaviourRecords = behaviourRecordMapper.selectList(queryWrapper);
        // 过滤无需统计数据
        behaviourRecords = behaviourRecords
                .stream()
                .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(behaviourRecords)) {
            log.info("[按条件查询学生五育统计分数]-当前查询为空，直接返回，入参：【{}】", JSONUtil.toJsonStr(dto));
            return Collections.emptyList();
        }

        // 使用 LinkedHashMap 来保持插入顺序并去重（德育活动）
        Map<String, BehaviourRecord> seen = new LinkedHashMap<>();
        List<BehaviourRecord> processedRecords = new ArrayList<>();
        for (BehaviourRecord record : behaviourRecords) {
            if (DataSourceEnum.MORAL_CAMPUS.getCode().equals(record.getDataSource())
                    || DataSourceEnum.MORAL_OFF_CAMPUS.getCode().equals(record.getDataSource())) {
                String key = record.getTargetId() + "-" + record.getModuleCode();
                if (!seen.containsKey(key)) {
                    seen.put(key, record);
                    processedRecords.add(record);
                }
            } else {
                // 对于其他 dataSource 值的记录，直接添加到 processedRecords 中
                processedRecords.add(record);
            }
        }
        behaviourRecords = processedRecords;

        //按学生 id 分组
        Map<String, List<BehaviourRecord>> map =
                behaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecord::getStudentId));
        for (Map.Entry<String, List<BehaviourRecord>> entry : map.entrySet()) {
            List<BehaviourRecord> records = entry.getValue();
            if (CollUtil.isEmpty(records)) {
                continue;
            }
            //按五育模块分组
            Map<Integer, List<BehaviourRecord>> moduleMap =
                    records.stream().collect(Collectors.groupingBy(BehaviourRecord::getModuleCode));
            for (Map.Entry<Integer, List<BehaviourRecord>> integerListEntry : moduleMap.entrySet()) {
                List<BehaviourRecord> moduleRecords = integerListEntry.getValue();
                if (CollUtil.isEmpty(moduleRecords)) {
                    continue;
                }

                List<Pair<String, BigDecimal>> plusScoreList = new ArrayList<>();
                List<Pair<String, BigDecimal>> minusScoreList = new ArrayList<>();
                //模块最高分
                List<BehaviourRecord> plusRecordList =
                        moduleRecords.stream().filter(s -> ScoreTypeEnum.PLUS.getCode().equals(s.getScoreType()))
                                .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(plusRecordList)) {
                    BigDecimal plusScore = plusRecordList.get(0).getScore();
                    //筛选出和最高分一样的点评项
                    List<BehaviourRecord> plusRecords = plusRecordList.stream()
                            .filter(s -> plusScore.compareTo(s.getScore()) == 0).collect(Collectors.toList());
                    plusRecords.forEach(s -> {
                        Pair<String, BigDecimal> stringBigDecimalPair = new Pair<>();
                        if (DataSourceEnum.MORAL_CAMPUS.getCode().equals(s.getDataSource())
                                || DataSourceEnum.MORAL_OFF_CAMPUS.getCode().equals(s.getDataSource())) {
                            stringBigDecimalPair.setA(s.getTargetName() + "表现" + s.getInfoName());
                        } else {
                            stringBigDecimalPair.setA(s.getInfoName());
                        }
                        stringBigDecimalPair.setB(s.getScore());
                        plusScoreList.add(stringBigDecimalPair);
                    });
                }

                //模块最低分
                List<BehaviourRecord> reduceRecordList =
                        moduleRecords.stream().filter(s -> ScoreTypeEnum.REDUCE.getCode().equals(s.getScoreType()))
                                .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(reduceRecordList)) {
                    BigDecimal minScore = reduceRecordList.get(reduceRecordList.size() - 1).getScore();
                    //筛选出和最低分一样的点评项
                    List<BehaviourRecord> minRecordList = reduceRecordList.stream()
                            .filter(s -> minScore.compareTo(s.getScore()) == 0).collect(Collectors.toList());
                    minRecordList.forEach(s -> {
                        Pair<String, BigDecimal> stringBigDecimalPair = new Pair<>();
                        if (DataSourceEnum.MORAL_CAMPUS.getCode().equals(s.getDataSource())
                                || DataSourceEnum.MORAL_OFF_CAMPUS.getCode().equals(s.getDataSource())) {
                            stringBigDecimalPair.setA(s.getTargetName() + "表现" + s.getInfoName());
                        } else {
                            stringBigDecimalPair.setA(s.getInfoName());
                        }
                        stringBigDecimalPair.setB(s.getScore());
                        minusScoreList.add(stringBigDecimalPair);
                    });
                }

                ReportModuleScoreStatisticsVO reportModuleScoreStatisticsVO = new ReportModuleScoreStatisticsVO();
                reportModuleScoreStatisticsVO.setStudentId(entry.getKey());
                reportModuleScoreStatisticsVO.setModuleCode(integerListEntry.getKey());
                reportModuleScoreStatisticsVO.setPlusScore(plusScoreList);
                reportModuleScoreStatisticsVO.setMinusScore(minusScoreList);
                pairs.add(reportModuleScoreStatisticsVO);
            }

        }
        return pairs;
    }

    @Override
    public List<ReportModuleScoreStatisticsVO> queryStudentScoreStatisticsByMaster(QueryBehaviourScoreDTO dto) {
        log.info("[按条件查询学生五育统计分数],入参：【{}】", JSONUtil.toJsonStr(dto));
        //入参校验
        Assert.isFalse(ObjectUtil.isNull(dto.getStartTime()) || ObjectUtil.isNull(dto.getEndTime()) || CollUtil.isEmpty(
                dto.getBusinessIds()), () -> new BizException(BizExceptionEnum.PARAMETER_ERROR.getMessage()));

        List<ReportModuleScoreStatisticsVO> pairs = new ArrayList<>();
        //所有行为分值
        QueryWrapper<BehaviourRecord> queryWrapper = new QueryWrapper<BehaviourRecord>()
                .select("id,student_id,module_code ,target_id,template_id,option_id,info_type, info_name,sum(score)" +
                        "as score, score_type, target_name, data_source,not_part_count")
                .ge(ObjectUtil.isNotNull(dto.getStartTime()), "submit_time", dto.getStartTime())
                .le(ObjectUtil.isNotNull(dto.getEndTime()), "submit_time", dto.getEndTime())
//                .eq(ObjectUtil.isNotNull(dto.getEvaluatorId()), "appraisal_id", dto.getEvaluatorId())
                .in(ReportBusinessTypeEnum.STUDENT.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                        dto.getBusinessIds()), "student_id", dto.getBusinessIds())
                .in(ReportBusinessTypeEnum.CLASS.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                        dto.getBusinessIds()), "class_id", dto.getBusinessIds())
                .in(ReportBusinessTypeEnum.GRADE.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                        dto.getBusinessIds()), "grade_id", dto.getBusinessIds())
                .in(ReportBusinessTypeEnum.SECTION.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                        dto.getBusinessIds()), "campus_section_id", dto.getBusinessIds())
                .in(ReportBusinessTypeEnum.CAMPUS.getCode().equals(dto.getBusinessType()) && CollUtil.isNotEmpty(
                        dto.getBusinessIds()), "campus_id", dto.getBusinessIds())
                .in("module_code", ModuleEnum.normalModuleMap.keySet())
                .eq("is_score", Constant.YES)
                .groupBy("student_id, module_code, target_id, option_id, info_name,score_type")
                .orderByDesc("score")
                .orderByDesc("submit_time");
        List<BehaviourRecord> behaviourRecords = behaviourRecordMapper.selectList(queryWrapper);
        // 过滤无需统计数据
        behaviourRecords = behaviourRecords
                .stream()
                .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(behaviourRecords)) {
            log.info("[按条件查询学生五育统计分数]-当前查询为空，直接返回，入参：【{}】", JSONUtil.toJsonStr(dto));
            return Collections.emptyList();
        }

        // 使用 LinkedHashMap 来保持插入顺序并去重（德育活动）
        Map<String, BehaviourRecord> seen = new LinkedHashMap<>();
        List<BehaviourRecord> processedRecords = new ArrayList<>();
        for (BehaviourRecord record : behaviourRecords) {
            if (DataSourceEnum.MORAL_CAMPUS.getCode().equals(record.getDataSource())
                    || DataSourceEnum.MORAL_OFF_CAMPUS.getCode().equals(record.getDataSource())) {
                String key = record.getTargetId() + "-" + record.getModuleCode();
                if (!seen.containsKey(key)) {
                    seen.put(key, record);
                    processedRecords.add(record);
                }
            } else {
                // 对于其他 dataSource 值的记录，直接添加到 processedRecords 中
                processedRecords.add(record);
            }
        }
        behaviourRecords = processedRecords;

        //按学生 id 分组
        Map<String, List<BehaviourRecord>> map =
                behaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecord::getStudentId));
        for (Map.Entry<String, List<BehaviourRecord>> entry : map.entrySet()) {
            List<BehaviourRecord> records = entry.getValue();
            if (CollUtil.isEmpty(records)) {
                continue;
            }
            //按五育模块分组
            Map<Integer, List<BehaviourRecord>> moduleMap =
                    records.stream().collect(Collectors.groupingBy(BehaviourRecord::getModuleCode));
            for (Map.Entry<Integer, List<BehaviourRecord>> integerListEntry : moduleMap.entrySet()) {
                List<BehaviourRecord> moduleRecords = integerListEntry.getValue();
                if (CollUtil.isEmpty(moduleRecords)) {
                    continue;
                }

                List<Pair<String, BigDecimal>> plusScoreList = new ArrayList<>();
                List<Pair<String, BigDecimal>> minusScoreList = new ArrayList<>();
                //模块最高分
                List<BehaviourRecord> plusRecordList =
                        moduleRecords.stream().filter(s -> ScoreTypeEnum.PLUS.getCode().equals(s.getScoreType()))
                                .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(plusRecordList)) {
                    BigDecimal plusScore = plusRecordList.get(0).getScore();
                    //筛选出和最高分一样的点评项
                    List<BehaviourRecord> plusRecords = plusRecordList.stream()
                            .filter(s -> plusScore.compareTo(s.getScore()) == 0).collect(Collectors.toList());
                    plusRecords.forEach(s -> {
                        Pair<String, BigDecimal> stringBigDecimalPair = new Pair<>();
                        if (DataSourceEnum.MORAL_CAMPUS.getCode().equals(s.getDataSource())
                                || DataSourceEnum.MORAL_OFF_CAMPUS.getCode().equals(s.getDataSource())) {
                            stringBigDecimalPair.setA(s.getTargetName() + "表现" + s.getInfoName());
                        } else {
                            stringBigDecimalPair.setA(s.getInfoName());
                        }
                        stringBigDecimalPair.setB(s.getScore());
                        plusScoreList.add(stringBigDecimalPair);
                    });
                }

                //模块最低分
                List<BehaviourRecord> reduceRecordList =
                        moduleRecords.stream().filter(s -> ScoreTypeEnum.REDUCE.getCode().equals(s.getScoreType()))
                                .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(reduceRecordList)) {
                    BigDecimal minScore = reduceRecordList.get(reduceRecordList.size() - 1).getScore();
                    //筛选出和最低分一样的点评项
                    List<BehaviourRecord> minRecordList = reduceRecordList.stream()
                            .filter(s -> minScore.compareTo(s.getScore()) == 0).collect(Collectors.toList());
                    minRecordList.forEach(s -> {
                        Pair<String, BigDecimal> stringBigDecimalPair = new Pair<>();
                        if (DataSourceEnum.MORAL_CAMPUS.getCode().equals(s.getDataSource())
                                || DataSourceEnum.MORAL_OFF_CAMPUS.getCode().equals(s.getDataSource())) {
                            stringBigDecimalPair.setA(s.getTargetName() + "表现" + s.getInfoName());
                        } else {
                            stringBigDecimalPair.setA(s.getInfoName());
                        }
                        stringBigDecimalPair.setB(s.getScore());
                        minusScoreList.add(stringBigDecimalPair);
                    });
                }

                ReportModuleScoreStatisticsVO reportModuleScoreStatisticsVO = new ReportModuleScoreStatisticsVO();
                reportModuleScoreStatisticsVO.setStudentId(entry.getKey());
                reportModuleScoreStatisticsVO.setModuleCode(integerListEntry.getKey());
                reportModuleScoreStatisticsVO.setPlusScore(plusScoreList);
                reportModuleScoreStatisticsVO.setMinusScore(minusScoreList);
                pairs.add(reportModuleScoreStatisticsVO);
            }

        }
        return pairs;
    }

    @Override
    public List<BehaviourRecord> getListByInfoId(String infoId) {
        if (StringUtils.isBlank(infoId)) {
            return Collections.emptyList();
        }
        return Optional.ofNullable(this.lambdaQuery().in(BehaviourRecord::getInfoId, infoId)
                        .eq(BehaviourRecord::getSchoolId, WebUtil.getSchoolId())
                        .eq(BehaviourRecord::getCampusId, WebUtil.getCampusId()).list())
                .orElse(Collections.emptyList());
    }


    /**
     * 获取学生不同指标的分数
     *
     * @param entry
     * @param target
     * @param score
     * @return
     */
    private BigDecimal getBigDecimal(Map.Entry<String, List<BehaviourRecord>> entry,
                                     SubjectEvaluationDimTargetBusinessMergePO target,
                                     BigDecimal score) {

        if (SubmitTypeEnum.MODULE.getCode().equals(target.getSubmitType())) {
            score = entry.getValue().stream().filter(s -> Convert.toInt(target.getSubmitId()).equals(s.getModuleCode()))
                    .map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (SubmitTypeEnum.GROUP.getCode().equals(target.getSubmitType())) {
            List<Long> targetIds = targetService.listTargetIdsByGroupId(Convert.toLong(target.getSubmitId()));
            score =
                    entry.getValue().stream().filter(s -> targetIds.contains(s.getTargetId()))
                            .map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (SubmitTypeEnum.TARGET.getCode().equals(target.getSubmitType())) {
            score =
                    entry.getValue().stream().filter(s -> Convert.toLong(target.getSubmitId()).equals(s.getTargetId()))
                            .map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (SubmitTypeEnum.OPTION.getCode().equals(target.getSubmitType())) {
            score = entry.getValue().stream().filter(s -> Convert.toStr(target.getSubmitId()).equals(s.getOptionId()))
                    .map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return score;
    }


    /**
     * 获取学生行为明细
     *
     * @param behaviourRecords 行为记录
     * @param type             1:学生行为明细 2:学生行为记录
     * @param studentId        学生id
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @return ListModuleDetailNewVO
     */
    public ListModuleDetailNewVO doListModuleDetail(List<BehaviourRecord> behaviourRecords, Integer type,
                                                    String studentId, Date startTime, Date endTime,
                                                    Boolean includeBehaviour) {
        if (includeBehaviour) {
            //获取学生帮扶积分列表
            HelpBehaviourRecordQueryDTO helpBehaviourRecordQueryDTO = new HelpBehaviourRecordQueryDTO();
            helpBehaviourRecordQueryDTO.setStudentId(studentId);
            helpBehaviourRecordQueryDTO.setStartTime(startTime);
            helpBehaviourRecordQueryDTO.setEndTime(endTime);
            List<EvaluateHelpBehaviourRecordPO> evaluateHelpBehaviourRecordPOS = helpBehaviourRecordManager.queryByCondition(
                    helpBehaviourRecordQueryDTO);
            log.info("HelpBehaviourRecords数量:{}", evaluateHelpBehaviourRecordPOS.size());
            if (CollectionUtils.isNotEmpty(evaluateHelpBehaviourRecordPOS)) {
                behaviourRecords.addAll(this.getHelpBehaviourRecordList(evaluateHelpBehaviourRecordPOS));
                Collections.sort(behaviourRecords, new Comparator<BehaviourRecord>() {
                    @Override
                    public int compare(BehaviourRecord o1, BehaviourRecord o2) {
                        return o2.getSubmitTime().compareTo(o1.getSubmitTime());
                    }
                });
            }
        }
        return doListModuleDetail(behaviourRecords, 1, studentId, startTime, endTime);
    }

    /**
     * 获取学生行为明细
     *
     * @param behaviourRecords 行为记录
     * @param type             1:学生行为明细 2:学生行为记录
     * @param studentId        学生id
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @return ListModuleDetailNewVO
     */
    public ListModuleDetailNewVO doListModuleDetail(List<BehaviourRecord> behaviourRecords, Integer type,
                                                    String studentId, Date startTime, Date endTime) {
        // 是否显示点评人
        Boolean showAppraisalFlag = reportPushRuleService.getShowAppraisalFlag(WebUtil.getCampusId());
        ListModuleDetailNewVO listModuleDetailNewVO = new ListModuleDetailNewVO();

        List<BehaviourRecord> behaviourRecordsAll = new ArrayList<>();
        behaviourRecordsAll.addAll(behaviourRecords);

        List<BehaviourRecord> addSumScoreTimes =
                behaviourRecordsAll.stream()
                        .filter(d -> ObjectUtil.isNotNull(d.getScoreType()) && d.getScoreType() == 1)
                        .collect(Collectors.toList());
        List<BehaviourRecord> addRecords =
                addSumScoreTimes.stream().filter(d -> ObjectUtil.isNotEmpty(d.getScore())
                        && d.getScore().compareTo(BigDecimal.ZERO) >= 0).collect(Collectors.toList());
        BigDecimal addScore = NumberUtil.add(ArrayUtil.toArray(CollStreamUtil.toList(addRecords,
                BehaviourRecord::getScore), BigDecimal.class));
        listModuleDetailNewVO.setAddSumScoreTimes(addSumScoreTimes.size());
        ////加分减分次数
        List<BehaviourRecord> subtractSumScoreTimes =
                behaviourRecordsAll.stream()
                        .filter(d -> ObjectUtil.isNotNull(d.getScoreType()) && d.getScoreType() == 2)
                        .collect(Collectors.toList());
        List<BehaviourRecord> subtractRecords =
                subtractSumScoreTimes.stream().filter(d -> ObjectUtil.isNotEmpty(d.getScore())
                        && d.getScore().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());
        BigDecimal subtractScore = NumberUtil.add(ArrayUtil.toArray(CollStreamUtil.toList(subtractRecords,
                BehaviourRecord::getScore), BigDecimal.class));
        //加分次数
        ////加分减分次数
        listModuleDetailNewVO.setSubtractSumScoreTimes(subtractSumScoreTimes.size());
        if (CollUtil.isEmpty(addRecords) && CollUtil.isEmpty(subtractRecords)) {
            listModuleDetailNewVO.setSumScore("-");
        } else {
            if (Objects.isNull(subtractScore)) {
                subtractScore = BigDecimal.valueOf(0);
            }
            if (Objects.isNull(addScore)) {
                addScore = BigDecimal.valueOf(0);
            }
            BigDecimal sumBigDecimal = NumberUtil.add(addScore, subtractScore);
            listModuleDetailNewVO.setSumScore(String.valueOf(NumUtil.formatFloatNumber(sumBigDecimal)));
        }

        // behaviourList 处理无效0
        behaviourRecordsAll.forEach(behaviourRecord -> {
            behaviourRecord.setScore(NumUtil.formatFloatNumber(behaviourRecord.getScore()));
        });
        List<ListModuleDetailVODateNewInfo> dateBehaviourList = new ArrayList<>();
        // 获取名称为选项的指标名称Map
        List<Long> targetIds = behaviourRecords.stream().map(BehaviourRecord::getTargetId).collect(Collectors.toList());
        Map<Long, String> targetNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(targetIds)) {
            List<Target> targets = targetMapper.listTargetByIds(targetIds);
            targetNameMap = targets.stream().collect(Collectors.toMap(Target::getId, Target::getTargetName));
        }

        // 获取当前时间范围所有日期（天）
        List<Date> dateList = behaviourRecords.stream().map(BehaviourRecord::getSubmitTime).distinct().sorted((s1,
                                                                                                               s2) -> s2.compareTo(
                s1)).collect(Collectors.toList());
        // 查询初始分分配的数据
        List<InitialScoreAllocationBehaviourVO> initialScoreAllocationBehaviourVOList = new ArrayList<>();

        listModuleDetailNewVO.setAddSumScore(NumUtil.formatFloatNumber(addScore));
        listModuleDetailNewVO.setSubtractSumScore(NumUtil.formatFloatNumber(subtractScore));

        dateList = dateList.stream().distinct().collect(Collectors.toList());
        List<ListModuleDetailVOBehaviour> dataBehaviourList = new ArrayList<>();
        Set<String> infoSet = new HashSet<>();
        // 遍历时间
        for (Date date : dateList) {
            ListModuleDetailVODateNewInfo dateNewInfo = new ListModuleDetailVODateNewInfo();

            dateNewInfo.setDate(DateUtil.format(date, "yyyy-MM-dd"));

            List<BehaviourRecord> recordList = behaviourRecords.stream()
                    .filter(behaviour -> date.equals(behaviour.getSubmitTime())).collect(Collectors.toList());
            Map<String, List<BehaviourRecord>> behaviourRecordMap = CollStreamUtil.groupByKey(
                    recordList.stream().filter(d -> DataSourceEnum.getCodesFromEvaluate().contains(d.getDataSource()))
                            .collect(Collectors.toList()), BehaviourRecord::getInfoId);
            dataBehaviourList.clear();
            infoSet.clear();
            for (BehaviourRecord d : recordList) {
                ListModuleDetailVOBehaviour listModuleDetailVOBehaviour = new ListModuleDetailVOBehaviour();
                if (DataSourceEnum.getCodesFromEvaluate().contains(d.getDataSource())) {
                    String infoId = d.getInfoId();
                    // 已经统计过了
                    if (infoSet.contains(infoId)) {
                        continue;
                    }
                    if (!behaviourRecordMap.containsKey(infoId)) {
                        log.error("[画像统计]-[五育明细]，数据有误，当前数据：{}", JSONUtil.toJsonStr(d));
                        continue;
                    }
                    List<BehaviourRecord> infoBehaviourRecords = behaviourRecordMap.get(infoId);
                    List<BigDecimal> bigDecimals = CollStreamUtil.toList(infoBehaviourRecords,
                            BehaviourRecord::getScore);

                    BigDecimal realScore = NumberUtil.add(ArrayUtil.toArray(bigDecimals, BigDecimal.class));

                    listModuleDetailVOBehaviour.setInfoId(infoId);
                    listModuleDetailVOBehaviour.setScore(CollUtil.isEmpty(bigDecimals) ? null :
                            Convert.toStr(realScore));
                    listModuleDetailVOBehaviour.setDataSource(d.getDataSource());
                    listModuleDetailVOBehaviour.setTargetId(d.getTargetId());
                    listModuleDetailVOBehaviour.setTargetName(MapUtil.getStr(targetNameMap, d.getTargetId()));
                    listModuleDetailVOBehaviour.setInfoType(d.getInfoType());
                    listModuleDetailVOBehaviour.setCreateTime(d.getCreateTime());
                    String appraisalName = d.getAppraisalName();
                    if (Objects.nonNull(d.getAppraisalType())) {
                        appraisalName = appraisalName + AppraisalEnum.getMessageByCode(d.getAppraisalType());
                    }
                    listModuleDetailVOBehaviour.setAppraisalName(appraisalName);
                    listModuleDetailVOBehaviour.setShowAppraisalFlag(showAppraisalFlag);

                    listModuleDetailVOBehaviour.setDetails(
                            convert.toListModuleDetailVOInnerBehaviourList(infoBehaviourRecords));
                    dataBehaviourList.add(listModuleDetailVOBehaviour);

                    infoSet.add(d.getInfoId());
                    continue;
                }
                if (DataSourceEnum.MORAL_CAMPUS.getCode().equals(d.getDataSource())
                        || DataSourceEnum.MORAL_OFF_CAMPUS.getCode().equals(d.getDataSource())) {
                    listModuleDetailVOBehaviour.setInfoId(null);
                    listModuleDetailVOBehaviour.setScore(Convert.toStr(d.getScore()));
                    listModuleDetailVOBehaviour.setDataSource(d.getDataSource());
                    listModuleDetailVOBehaviour.setTargetId(d.getTargetId());
                    listModuleDetailVOBehaviour.setTargetName(d.getTargetName());
                    listModuleDetailVOBehaviour.setInfoType(d.getInfoType());
                    listModuleDetailVOBehaviour.setCreateTime(d.getSubmitTime());

                    ListModuleDetailVOInnerBehaviour listModuleDetailVOInnerBehaviour =
                            new ListModuleDetailVOInnerBehaviour();
                    listModuleDetailVOInnerBehaviour.setInfoType(d.getInfoType());
                    listModuleDetailVOInnerBehaviour.setDataSource(d.getDataSource());
                    String behaviourName =
                            d.getTargetName() + ModuleEnum.getModuleName(d.getModuleCode()) + d.getInfoName();
                    listModuleDetailVOInnerBehaviour.setBehaviourName(behaviourName);
                    listModuleDetailVOInnerBehaviour.setBehaviourScore(Convert.toStr(d.getScore()));
                    listModuleDetailVOInnerBehaviour.setBehaviourId(Convert.toStr(d.getId()));
                    listModuleDetailVOInnerBehaviour.setCreateTime(d.getSubmitTime());

                    listModuleDetailVOBehaviour.setDetails(CollUtil.newArrayList(listModuleDetailVOInnerBehaviour));

                    dataBehaviourList.add(listModuleDetailVOBehaviour);
                    continue;
                }
                listModuleDetailVOBehaviour.setInfoId(null);
                listModuleDetailVOBehaviour.setScore(Convert.toStr(d.getScore()));
                listModuleDetailVOBehaviour.setDataSource(d.getDataSource());
                listModuleDetailVOBehaviour.setTargetId(d.getTargetId());
                listModuleDetailVOBehaviour.setTargetName(MapUtil.getStr(targetNameMap, d.getTargetId()));
                listModuleDetailVOBehaviour.setInfoType(d.getInfoType());
                String appraisalName = d.getAppraisalName();
                if (Objects.nonNull(d.getAppraisalType())) {
                    appraisalName = appraisalName + AppraisalEnum.getMessageByCode(d.getAppraisalType());
                }
                listModuleDetailVOBehaviour.setAppraisalName(appraisalName);
                listModuleDetailVOBehaviour.setShowAppraisalFlag(showAppraisalFlag);

                List<ListModuleDetailVOInnerBehaviour> details = new ArrayList<>();
                details.add(convert.toListModuleDetailVOInnerBehaviourList(d));
                listModuleDetailVOBehaviour.setDetails(details);

                dataBehaviourList.add(listModuleDetailVOBehaviour);
            }
            // 处理初始分的
            if (CollUtil.isNotEmpty(initialScoreAllocationBehaviourVOList)) {
                List<InitialScoreAllocationBehaviourVO> allocationBehaviourVOS =
                        initialScoreAllocationBehaviourVOList.stream()
                                .filter(behaviour -> date.equals(behaviour.getGroupTime()))
                                .collect(Collectors.toList());
                for (InitialScoreAllocationBehaviourVO initialScoreAllocation : allocationBehaviourVOS) {
                    ListModuleDetailVOBehaviour listModuleDetailVOBehaviour = new ListModuleDetailVOBehaviour();
                    listModuleDetailVOBehaviour.setInfoId(null);
                    listModuleDetailVOBehaviour.setScore(
                            initialScoreAllocation.getInitialScore().stripTrailingZeros().toPlainString());
                    listModuleDetailVOBehaviour.setDataSource(0);
                    listModuleDetailVOBehaviour.setTargetId(null);
                    listModuleDetailVOBehaviour.setTargetName(
                            StrUtil.isBlank(initialScoreAllocation.getInfoName()) ? "学期初始化分值"
                                    : initialScoreAllocation.getInfoName());
                    listModuleDetailVOBehaviour.setInfoType(1);
                    listModuleDetailVOBehaviour.setCreateTime(initialScoreAllocation.getCreateTime());
                    listModuleDetailVOBehaviour.setHaveDetail(0);

                    ListModuleDetailVOInnerBehaviour listModuleDetailVOInnerBehaviour =
                            new ListModuleDetailVOInnerBehaviour();
                    listModuleDetailVOInnerBehaviour.setInfoType(1);
                    listModuleDetailVOInnerBehaviour.setDataSource(0);
                    String behaviourName = "";
                    if (initialScoreAllocation.getScoreType() == 1) {
                        behaviourName =
                                behaviourName + "加" + initialScoreAllocation.getScoreValue().stripTrailingZeros()
                                        .toPlainString() + "分";
                    }
                    if (initialScoreAllocation.getScoreType() == 2) {
                        behaviourName =
                                behaviourName + "减" + initialScoreAllocation.getScoreValue().stripTrailingZeros()
                                        .toPlainString() + "分";
                    }
                    listModuleDetailVOInnerBehaviour.setBehaviourName(behaviourName);
                    listModuleDetailVOInnerBehaviour.setBehaviourScore(
                            initialScoreAllocation.getInitialScore().stripTrailingZeros().toPlainString());

                    listModuleDetailVOBehaviour.setDetails(CollUtil.newArrayList(listModuleDetailVOInnerBehaviour));
                    dataBehaviourList.add(listModuleDetailVOBehaviour);
                }
            }

            List<ListModuleDetailVOBehaviour> collect =
                    dataBehaviourList.stream().sorted(Comparator.comparing(ListModuleDetailVOBehaviour::getCreateTime,
                            Comparator.nullsLast(Date::compareTo)).reversed()).collect(Collectors.toList());
            dateNewInfo.setBehaviourList(collect);

            dateBehaviourList.add(dateNewInfo);
        }
        listModuleDetailNewVO.setDateBehaviourList(dateBehaviourList);

        log.info("学生画像-五育得分明细 返回结果：[{}]", JSONUtil.toJsonStr(listModuleDetailNewVO));
        return listModuleDetailNewVO;
    }

//    /**
//     * 五育明细（饼图）
//     *
//     * @param dto
//     * @return
//     */
//    @Override
//    public BehaviourAnalysisVO getBehaviourAnalysis(BehaviourAnalysisDTO dto) {
//        log.info("方法名：[getBehaviourAnalysis],参数： [{}]", dto);
//        ListStudentBehaviourDTO listStudentBehaviourDTO = convert.toListStudentBehaviourDTO(dto);
//        listStudentBehaviourDTO.setCampusId(WebUtil.getCampusId());
//        List<StudentBehaviourVO> behaviourList = behaviourRecordMapper.listStudentBehaviour(listStudentBehaviourDTO);
//        log.info("指标列表:[{}]", behaviourList);
//        if (CollUtil.isEmpty(behaviourList)) {
//            return null;
//        }
//        // 将平行结构行为指标转换为树形结构，对应模块、指标列表中
//        List<BehaviourAnalysisVOInnerModule> moduleList = convertToModuleList(behaviourList);
//        // 设置每个指标占比和表扬、待提高、无分数列表总占比
//        setModuleInnerTargetRate(moduleList);
//        // 对象转换，去除无用字段
//        List<BehaviourAnalysisVOInnerModuleVO> targetModuleList =
//                convert.toBehaviourAnalysisVOInnerModuleVOList(moduleList);
//        log.info("学生画像-评价占比(饼图) 返回结果：[{}]", JSONUtil.toJsonStr(targetModuleList));
//        return new BehaviourAnalysisVO(targetModuleList);
//    }

    @Override
    public List<BehaviourAnalysisVOInnerModuleVO> getBehaviourAnalysis(List<StuBehaviorOptionVO> optionVOS) {
        if (CollUtil.isEmpty(optionVOS)) {
            return Collections.emptyList();
        }
        // 将平行结构行为指标转换为树形结构，对应模块、指标列表中
        List<BehaviourAnalysisVOInnerModule> moduleList = convertToModuleList(convert.toBehaviourAnalysisVO(optionVOS));
        // 设置每个指标占比和表扬、待提高、无分数列表总占比
        setModuleInnerTargetRate(moduleList);
        // 对象转换，去除无用字段
        return convert.toBehaviourAnalysisVOInnerModuleVOList(moduleList);
    }


    /**
     * 设置指标占比
     */
    private void setModuleInnerTargetRate(List<BehaviourAnalysisVOInnerModule> moduleList) {
        for (BehaviourAnalysisVOInnerModule module : moduleList) {

            List<BehaviourAnalysisVOInnerTarget> targetList = module.getTargetList();
            if (CollUtil.isEmpty(targetList)) {
                continue;
            }
            // 对指标进行排序 先按组sortIndex升序，再按指标sortIndex升序
            targetList =
                    targetList.stream().sorted(Comparator.comparing(BehaviourAnalysisVOInnerTarget::getGroupSortIndex)
                                    .thenComparing(BehaviourAnalysisVOInnerTarget::getTargetSortIndex))
                            .collect(Collectors.toList());
            module.setTargetList(targetList);
            Integer totalSubmitCount;
            for (BehaviourAnalysisVOInnerTarget target : targetList) {

                List<BehaviourAnalysisVOInnerTargetOptionInfo> behaviourList = target.getBehaviourList();
                List<BehaviourAnalysisVOInnerTargetOptionInfo> praiseBehaviourList = target.getPraiseBehaviourList();
                List<BehaviourAnalysisVOInnerTargetOptionInfo> toImproveBehaviourList =
                        target.getToImproveBehaviourList();
                List<BehaviourAnalysisVOInnerTargetOptionInfo> noScoreBehaviourList = target.getNoScoreBehaviourList();
                // 指标计算百分比规则： 当前行为提交次数：当前指标所有行为项提交次数
                totalSubmitCount =
                        behaviourList.stream().mapToInt(BehaviourAnalysisVOInnerTargetOptionInfo::getSubmitCount).sum();
                // 设置每个指标占比
                setTargetRate(behaviourList, totalSubmitCount);
                String praiseRate = setTargetRate(praiseBehaviourList, totalSubmitCount);
                String toImproveRate = setTargetRate(toImproveBehaviourList, totalSubmitCount);
                String noScoreRate = setTargetRate(noScoreBehaviourList, totalSubmitCount);
                // 设置模块百分比
                target.setPraiseScoreRate(praiseRate);
                target.setToImproveScoreRate(toImproveRate);
                target.setNoScoreRate(noScoreRate);
                // 设置模块行为数量
                setTargetBehaviourListRange(target);
                // 设置指标其他指标列表及之前前N项分组
                setTopBehaviourList(target, totalSubmitCount);
            }
        }
    }

    private void setTopBehaviourList(BehaviourAnalysisVOInnerTarget target, Integer totalSubmitCount) {
        // 所有指标取前10个
        List<BehaviourAnalysisVOInnerTargetOptionInfo> behaviourList = target.getBehaviourList();
        // 先按数量排序，再按分数排序，分数排序按绝对值算, 分数为null视为0分
        behaviourList = behaviourList.stream().sorted(Comparator
                        .comparing(BehaviourAnalysisVOInnerTargetOptionInfo::getSubmitCount, Comparator.reverseOrder())
                        .thenComparing(behaviour -> {
                            BigDecimal behaviourScore = behaviour.getBehaviourScore();
                            if (ObjectUtil.isNull(behaviourScore)) {
                                return new BigDecimal(0);
                            }
                            return behaviourScore.abs();
                        }, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        // 获取前10个指标列表
        List<BehaviourAnalysisVOInnerTargetOptionInfo> topBehaviourList =
                behaviourList.stream().limit(BEHAVIOUR_ROUND_LIST_SIZE).collect(Collectors.toList());

        // 获取其他指标列表
        List<BehaviourAnalysisVOInnerTargetOptionInfo> othersBehaviourList =
                behaviourList.stream().skip(BEHAVIOUR_ROUND_LIST_SIZE).collect(Collectors.toList());

        // 当指标数量少于10个，最后一个指标设置其百分比为1 - 其他
        if (topBehaviourList.size() < BEHAVIOUR_ROUND_LIST_SIZE) {
            handleLastBehaviourRate(topBehaviourList);
        }
        // 设置前N项指标到对应集合
        divideTopBehaviourList(target, topBehaviourList);

        // 获取其他指标对象
        BehaviourAnalysisVOInnerTargetOptionInfo othersBehaviour = setOthersBehaviourInfo(othersBehaviourList,
                totalSubmitCount);
        if (ObjectUtil.isNotNull(othersBehaviour)) {
            // 获取其他指标占比（百分比） (1 - topN个指标)
            String otherPercentRate = getOtherPercentRate(topBehaviourList);
            othersBehaviour.setBehaviourRate(otherPercentRate); // NOSONAR
            target.getTopNothersBehaviourList().add(othersBehaviour);
        }

    }

    /**
     * 处理最后一个指标的百分比为1 - 其他
     *
     * @param behaviourList
     */
    private void handleLastBehaviourRate(List<BehaviourAnalysisVOInnerTargetOptionInfo> behaviourList) {
        if (CollUtil.isEmpty(behaviourList)) {
            return;
        }
        int size = behaviourList.size();
        // 行为数目为1、2、5百分比相加等于1，无需计算
        if (size <= 2 || size == 5) {
            return;
        }
        // 前n-1项比例
        List<BehaviourAnalysisVOInnerTargetOptionInfo> topList =
                behaviourList.stream().limit(size - 1).collect(Collectors.toList());
        // 最后一项指标
        BehaviourAnalysisVOInnerTargetOptionInfo behaviourInfo =
                behaviourList.stream().skip(size - 1).collect(Collectors.toList()).get(0);
        // 计算最后一项指标占比（百分比）
        String percentRate = getOtherPercentRate(topList);
        behaviourInfo.setBehaviourRate(percentRate);
    }

    /**
     * 给定行为集合，计算其他指标占比  (1 - 现有指标占比）
     *
     * @param behaviourList
     * @return
     */
    private String getOtherPercentRate(List<BehaviourAnalysisVOInnerTargetOptionInfo> behaviourList) {
        Double sumRate =
                behaviourList.stream().mapToDouble(behaviour -> Convert.toDouble(behaviour.getBehaviourRate())).sum();
        // 最后一项指标百分比为1 - 其他
        BigDecimal rate = BigDecimal.ONE.subtract(new BigDecimal(sumRate.toString()).divide(new BigDecimal(100)));
        // 转换为百分比
        return NumUtil.convertToPercent(rate, BEHAVIOUR_RATE_SIZE);
    }

    /**
     * 设置前N项指标到对应集合
     */
    private void divideTopBehaviourList(BehaviourAnalysisVOInnerTarget target,
                                        List<BehaviourAnalysisVOInnerTargetOptionInfo> topBehaviourList) {
        if (CollUtil.isEmpty(topBehaviourList)) {
            return;
        }
        for (BehaviourAnalysisVOInnerTargetOptionInfo behaviour : topBehaviourList) {
            BigDecimal behaviourScore = behaviour.getBehaviourScore();
            // 无分和0分归统一到无分列表
            if (ObjectUtil.isNull(behaviourScore)) {
                target.getTopNnoScoreBehaviourList().add(behaviour);
                continue;
            }
            int flag = behaviourScore.compareTo(new BigDecimal(0));
            if (flag > 0) {
                target.getTopNpraiseBehaviourList().add(behaviour);
            } else if (flag < 0) {
                target.getTopNtoImproveBehaviourList().add(behaviour);
            } else {
                target.getTopNnoScoreBehaviourList().add(behaviour);
            }

        }
    }

    /**
     * 设置其他指标的次数、分数、占比
     *
     * @param othersBehaviourList
     * @param totalSubmitCount
     * @return
     */
    private BehaviourAnalysisVOInnerTargetOptionInfo setOthersBehaviourInfo(
            List<BehaviourAnalysisVOInnerTargetOptionInfo> othersBehaviourList, Integer totalSubmitCount) {
        if (CollUtil.isEmpty(othersBehaviourList)) {
            return null;
        }

        BehaviourAnalysisVOInnerTargetOptionInfo otherBehaviour = new BehaviourAnalysisVOInnerTargetOptionInfo();
        // 其他指标总提交次数
        int sumSubmitCount =
                othersBehaviourList.stream().mapToInt(BehaviourAnalysisVOInnerTargetOptionInfo::getSubmitCount).sum();
        // 其他指标总分数
        BigDecimal othersBehaviourScore = new BigDecimal(0);
        for (BehaviourAnalysisVOInnerTargetOptionInfo behaviour : othersBehaviourList) {
            BigDecimal behaviourScore = ObjectUtil.isNull(behaviour.getBehaviourScore()) ? new BigDecimal(0) :
                    behaviour.getBehaviourScore();
            othersBehaviourScore = othersBehaviourScore.add(behaviourScore);
        }
        // 计算其他指标总占比
        String otherBehaviourRate = getRateStr(sumSubmitCount, totalSubmitCount, BEHAVIOUR_RATE_SIZE);

        return otherBehaviour.setBehaviourName("其他").setBehaviourRate(otherBehaviourRate)
                .setSubmitCount(sumSubmitCount).setBehaviourScore(othersBehaviourScore);
    }

    private void setTargetBehaviourListRange(BehaviourAnalysisVOInnerTarget target) {
        if (BEHAVIOUR_LIST_SIZE.equals(-1)) {
            return;
        }
        List<BehaviourAnalysisVOInnerTargetOptionInfo> praiseBehaviourList = target.getPraiseBehaviourList();
        List<BehaviourAnalysisVOInnerTargetOptionInfo> toImproveBehaviourList = target.getToImproveBehaviourList();
        List<BehaviourAnalysisVOInnerTargetOptionInfo> noScoreBehaviourList = target.getNoScoreBehaviourList();

        target.setPraiseBehaviourList(
                praiseBehaviourList.stream().limit(BEHAVIOUR_LIST_SIZE).collect(Collectors.toList()));
        target.setToImproveBehaviourList(
                toImproveBehaviourList.stream().limit(BEHAVIOUR_LIST_SIZE).collect(Collectors.toList()));
        target.setNoScoreBehaviourList(
                noScoreBehaviourList.stream().limit(BEHAVIOUR_LIST_SIZE).collect(Collectors.toList()));
    }

    /**
     * 设置每个行为占比及该行为总占比
     *
     * @param behaviourList
     * @param totalSubmitCount
     */
    private String setTargetRate(List<BehaviourAnalysisVOInnerTargetOptionInfo> behaviourList,
                                 Integer totalSubmitCount) {
        if (CollUtil.isEmpty(behaviourList)) {
            return "0%";
        }
        // 获取该组行为总共次数
        Integer sumCount =
                behaviourList.stream().mapToInt(BehaviourAnalysisVOInnerTargetOptionInfo::getSubmitCount).sum();

        // 设置每一项行为百分比
        behaviourList.forEach(behaviour -> {
            String rateStr = getRateStr(behaviour.getSubmitCount(), totalSubmitCount, BEHAVIOUR_RATE_SIZE);
            behaviour.setBehaviourRate(rateStr);
        });

        return getRateStr(sumCount, totalSubmitCount, BEHAVIOUR_GROUP_RATE_SIZE);
    }

    /**
     * 计算百分比
     *
     * @param count
     * @param totalCount
     * @return
     */
    private String getRateStr(Integer count, Integer totalCount, Integer size) {
        // 获取比例
        BigDecimal rate = NumberUtil.div(new BigDecimal(count), new BigDecimal(totalCount));
        // 去除无效0
        String rateStr = rate.stripTrailingZeros().toPlainString();
        // 转换为百分比
        return NumberUtil.formatPercent(Convert.toDouble(rateStr), size);
    }

    /**
     * 将平行结构行为指标转换为树形结构，对应模块、指标列表中
     *
     * @param behaviourList
     * @return
     */
    private List<BehaviourAnalysisVOInnerModule> convertToModuleList(List<StudentBehaviourVO> behaviourList) {
        // 初始化饼图兑现
        BehaviourAnalysisVOInnerModule moral =
                new BehaviourAnalysisVOInnerModule().setModuleCode(ModuleEnum.MORAL.getCode())
                        .setModuleName(ModuleEnum.MORAL.getMessage());
        BehaviourAnalysisVOInnerModule wisdom =
                new BehaviourAnalysisVOInnerModule().setModuleCode(ModuleEnum.WISDOM.getCode())
                        .setModuleName(ModuleEnum.WISDOM.getMessage());
        BehaviourAnalysisVOInnerModule sport =
                new BehaviourAnalysisVOInnerModule().setModuleCode(ModuleEnum.SPORT.getCode())
                        .setModuleName(ModuleEnum.SPORT.getMessage());
        BehaviourAnalysisVOInnerModule pretty =
                new BehaviourAnalysisVOInnerModule().setModuleCode(ModuleEnum.PRETTY.getCode())
                        .setModuleName(ModuleEnum.PRETTY.getMessage());
        BehaviourAnalysisVOInnerModule work =
                new BehaviourAnalysisVOInnerModule().setModuleCode(ModuleEnum.WORK.getCode())
                        .setModuleName(ModuleEnum.WORK.getMessage());
        List<BehaviourAnalysisVOInnerModule> moduleList = Lists.newArrayList(moral, wisdom, sport, pretty, work);

        for (StudentBehaviourVO behaviour : behaviourList) {
            setBehaviourToTarget(behaviour, moduleList);
        }

        return moduleList;
    }

    /**
     * 将指标根据模块code和指标id放到对应模块，对应指标对应集合中
     *
     * @param behaviour
     * @param moduleList
     */
    private void setBehaviourToTarget(StudentBehaviourVO behaviour, List<BehaviourAnalysisVOInnerModule> moduleList) {
        Integer dataSource = behaviour.getDataSource();
        if (DataSourceEnum.MORAL_CAMPUS.getCode().equals(dataSource) || DataSourceEnum.MORAL_OFF_CAMPUS.getCode()
                .equals(dataSource)) {
            // 德育活动的明细重写targetId和targetName
            behaviour.setTargetId(1000L);
            behaviour.setTargetName("活动");
        }
        // 获取指标对应module
        List<BehaviourAnalysisVOInnerModule> collectList =
                moduleList.stream().filter(module -> behaviour.getModuleCode().equals(module.getModuleCode()))
                        .collect(Collectors.toList());
        if (CollUtil.isEmpty(collectList)) {
            return;
        }
        BehaviourAnalysisVOInnerModule module = collectList.get(0);
        // 当前模块下指标列表
        List<BehaviourAnalysisVOInnerTarget> targetList = module.getTargetList();
        // 筛选出当前指标
        List<BehaviourAnalysisVOInnerTarget> goalTargetList =
                targetList.stream().filter(target -> Objects.equals(behaviour.getTargetId(), (target.getTargetId())))
                        .collect(Collectors.toList());

        BehaviourAnalysisVOInnerTarget target;
        if (CollUtil.isEmpty(goalTargetList)) {
            target = new BehaviourAnalysisVOInnerTarget();
            target.setTargetId(behaviour.getTargetId());
            target.setTargetName(behaviour.getTargetName());
            target.setTargetSortIndex(behaviour.getTargetSortIndex());
            target.setGroupSortIndex(behaviour.getGroupSortIndex());
            targetList.add(target);
        } else {
            target = goalTargetList.get(0);
        }

        BehaviourAnalysisVOInnerTargetOptionInfo behaviourInfo =
                convert.toBehaviourAnalysisVOInnerTargetOptionInfo(behaviour);
        // 添加到整体的行为记录集合
        target.getBehaviourList().add(behaviourInfo);
        // 判断属于哪种类型，添加到对应的集合
        if (ObjectUtil.isNull(behaviour.getBehaviourScore())) {
            target.getNoScoreBehaviourList().add(behaviourInfo);
            return;
        }
        // (flag > 0 加分) (flag = 0 归结到无分) (flag < 0 减分)
        int flag = behaviour.getBehaviourScore().compareTo(Convert.toBigDecimal(Constant.ZERO));
        if (flag > 0) {
            target.getPraiseBehaviourList().add(behaviourInfo);
        } else if (flag < 0) {
            target.getToImproveBehaviourList().add(behaviourInfo);
        } else {
            target.getNoScoreBehaviourList().add(behaviourInfo);
        }
    }

    /**
     * 根据校区id列表 获取当天有点评的学生
     *
     * @param campusIdList 校区id列表
     * @return 学生id列表
     */
    @Override
    public List<BehaviourRecord> getTodayBehaviourStudentList(List<String> campusIdList) {
        if (CollUtil.isEmpty(campusIdList)) {
            return Collections.emptyList();
        }
        String zeroTime = DateUtil.today() + " 00:00:00";
        return behaviourRecordMapper.selectList(new LambdaQueryWrapper<BehaviourRecord>()
                .eq(BehaviourRecord::getDeleted, Constant.ZERO)
                .in(BehaviourRecord::getCampusId, campusIdList)
                .ge(BehaviourRecord::getSubmitTime, zeroTime));
    }

    /**
     * 根据校区id和最近一次推送时间 获取最近一次推送时间之后有点评的学生
     *
     * @param campusId     校区id
     * @param lastPushTime 最近一次推送时间
     * @return 学生点评记录列表
     */
    @Override
    public List<BehaviourRecord> getBehaviourStudentListByLastPushTime(String campusId, Date lastPushTime,
                                                                       Date endDate) {
        if (StringUtils.isBlank(campusId) || Objects.isNull(lastPushTime)) {
            return Collections.emptyList();
        }
        return behaviourRecordMapper.selectList(new LambdaQueryWrapper<BehaviourRecord>()
                .eq(BehaviourRecord::getDeleted, Constant.ZERO)
                .eq(BehaviourRecord::getCampusId, campusId)
                .ge(BehaviourRecord::getSubmitTime, lastPushTime)
                .lt(BehaviourRecord::getSubmitTime, endDate));
    }

    @Override
    public List<BehaviourRecord> listByStudentIdAndSubmitTime(List<String> studentIds,
                                                              Date startSubmitTime, Date endSubmitTime) {
        if (CollUtil.isEmpty(studentIds)
                || ObjectUtil.isNull(startSubmitTime)
                || ObjectUtil.isNull(endSubmitTime)) {
            return Collections.emptyList();
        }

        return this.lambdaQuery().select(BehaviourRecord::getStudentId, BehaviourRecord::getScore)
                .in(BehaviourRecord::getStudentId, studentIds)
                .ge(BehaviourRecord::getSubmitTime, startSubmitTime)
                .le(BehaviourRecord::getSubmitTime, endSubmitTime)
                .eq(BehaviourRecord::getIsScore, Boolean.TRUE)
                .list();
    }

    @Override
    public List<BehaviourRecord> listByClassIdAndSubmitTime(QueryBehaviourScoreDTO queryBehaviourScoreDTO) {
        if (Objects.isNull(queryBehaviourScoreDTO.getClassId())
                || ObjectUtil.isNull(queryBehaviourScoreDTO.getStartTime())
                || CollectionUtils.isEmpty(queryBehaviourScoreDTO.getDatasourceList())
                || ObjectUtil.isNull(queryBehaviourScoreDTO.getEndTime())) {
            return Collections.emptyList();
        }

        return this.lambdaQuery().select()
                .in(BehaviourRecord::getDataSource, queryBehaviourScoreDTO.getDatasourceList())
                .ge(BehaviourRecord::getSubmitTime, queryBehaviourScoreDTO.getStartTime())
                .le(BehaviourRecord::getSubmitTime, queryBehaviourScoreDTO.getEndTime())
                .eq(BehaviourRecord::getDeleted, Constant.ZERO)
                .eq(BehaviourRecord::getClassId, queryBehaviourScoreDTO.getClassId())
                .list();
    }

    @Override
    public List<BehaviourRecord> listByClassIdAndAppraisalIdAndSubmitTime(String appraisalId,
                                                                          String classId,
                                                                          Date startSubmitTime,
                                                                          Date endSubmitTime) {
        if (StringUtils.isBlank(appraisalId)
                || ObjectUtil.isNull(startSubmitTime)
                || ObjectUtil.isNull(endSubmitTime)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().select(BehaviourRecord::getStudentId, BehaviourRecord::getScore)
                .eq(BehaviourRecord::getClassId, classId)
                .ge(BehaviourRecord::getSubmitTime, startSubmitTime)
                .le(BehaviourRecord::getSubmitTime, endSubmitTime)
                .eq(BehaviourRecord::getAppraisalId, appraisalId)
                .eq(BehaviourRecord::getIsScore, Boolean.TRUE)
                .list();
    }

    @Override
    public List<BehaviourRecord> countTeacherRecord() {
        DateTime endTime = DateUtil.date();
        DateTime beginTime = DateUtil.offsetDay(endTime, -14);
        QueryWrapper<BehaviourRecord> behaviourRecordQueryWrapper = new QueryWrapper<>();
        behaviourRecordQueryWrapper.select("target_id ,option_id ,info_type ")
                .eq("campus_id", WebUtil.getCampusId())
                .eq("is_score", Constant.YES)
                .eq("appraisal_id", WebUtil.getStaffId())
                .between("submit_time", beginTime, endTime)
                .groupBy("target_id,option_id")
                .orderByDesc("count(0)");

        List<BehaviourRecord> behaviourRecords = behaviourRecordMapper.selectList(behaviourRecordQueryWrapper);
        return behaviourRecords;
    }

    //new QueryWrapper<>().in(CollectionUtils.isNotEmpty(classSubjectQueryDTO.getTargetIdList()),BehaviourRecord.TARGET_ID,classSubjectQueryDTO.getTargetIdList())
    @Override
    public List<BehaviourRecord> getClassSubjectFilterList(BehaviourClassSubjectQueryDTO classSubjectQueryDTO) {
        QueryWrapper<BehaviourRecord> behaviourRecordQueryWrapper = new QueryWrapper<>();
        behaviourRecordQueryWrapper
                .in(CollectionUtils.isNotEmpty(classSubjectQueryDTO.getClassIds()), "class_id",
                        classSubjectQueryDTO.getClassIds())
                .in(CollectionUtils.isNotEmpty(classSubjectQueryDTO.getSubjectCodes()), "subject_code",
                        classSubjectQueryDTO.getSubjectCodes())
                .eq("is_score", Constant.YES)
                .between("submit_time", classSubjectQueryDTO.getStartTime(), classSubjectQueryDTO.getEndTime());
        if (CollectionUtils.isNotEmpty(classSubjectQueryDTO.getTargetIds())
                || CollectionUtils.isNotEmpty(classSubjectQueryDTO.getOptionIds())) {
            behaviourRecordQueryWrapper.and(i -> i.in(CollectionUtils.isNotEmpty(classSubjectQueryDTO.getTargetIds()),
                            BehaviourRecord.TARGET_ID, classSubjectQueryDTO.getTargetIds())
                    .or(CollectionUtils.isNotEmpty(classSubjectQueryDTO.getOptionIds()))
                    .in(CollectionUtils.isNotEmpty(classSubjectQueryDTO.getOptionIds()), BehaviourRecord.OPTION_ID,
                            classSubjectQueryDTO.getOptionIds()));
        }
        return behaviourRecordMapper.selectList(behaviourRecordQueryWrapper);
    }

    @Override
    public List<BehaviourRecord> listByOptionIds(OptionIdQuery query) {
        if (StringUtils.isBlank(query.getCampusSectionId())
                || StringUtils.isBlank(query.getStartTime())
                || StringUtils.isBlank(query.getEndTime())
                || CollectionUtils.isEmpty(query.getNegativeOptionKeys())) {
            return Collections.emptyList();
        }
        // 1.通过扣分选项查询的行为记录
        List<BehaviourRecord> behaviourRecords = behaviourHandleManager.listByOptionIds(query.getCampusSectionId(),
                query.getStartTime(), query.getEndTime(),
                query.getNegativeOptionKeys());
        // 2.通过分值控件查询的行为记录(没有optionId,只有targetId)
        List<BehaviourRecord> behaviourRecords2 = behaviourHandleManager.listByTargetIds(query.getCampusSectionId(),
                query.getStartTime(), query.getEndTime(),
                query.getTargetIds());
        behaviourRecords.addAll(behaviourRecords2);
        return behaviourRecords;
    }

    @Override
    public List<Target> listScoreContinuationFlag() {
        return targetManager.listScoreContinuationFlag();
    }

    @Override
    public void fixData(EvaluateFixDataRequest request, HttpServletResponse response) {

        List<EvaluateFixDataResponse> behaviourRecords = behaviourRecordMapper.selectByCondition(request.getCampusId());
        if(CollectionUtils.isEmpty(behaviourRecords)){
            log.warn("修复数据数据为空，campusId:{}", request.getCampusId());
            return ;
        }
        Map<String, EvaluateFixDataResponse> identityMap = CollStreamUtil.toIdentityMap(behaviourRecords,
                EvaluateFixDataResponse::getId);
        List<String> studentIds = behaviourRecords.stream().map(EvaluateFixDataResponse::getStudentId).distinct()
                .collect(Collectors.toList());
        StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
        studentByIdQuery.setStudentIds(Convert.toList(Long.class, studentIds));
        List<UcStudentClassBffVO> ucStudentClassBffVOS = basicInfoRemote.listByStudentIds(studentByIdQuery);
        Map<Long, List<UcStudentClassBffVO>> studentMap = CollStreamUtil.groupByKey(ucStudentClassBffVOS,
                UcStudentClassBffVO::getStudentId);
        Map<Long, List<UcStudentClassBffVO>> classMap = CollStreamUtil.groupByKey(ucStudentClassBffVOS,
                UcStudentClassBffVO::getClassId);
        List<SubjectInfoPO> subjectInfoPOS = subjectInfoManager.listSubjectInfoAllBySchoolAndCampus(request.getSchoolId(),
                request.getCampusId());
        Map<String, List<SubjectInfoPO>> subjectMap = CollStreamUtil.groupByKey(subjectInfoPOS,
                SubjectInfoPO::getSubjectCode);


        for (EvaluateFixDataResponse behaviourRecord : behaviourRecords) {
            List<UcStudentClassBffVO> studentClassBffVOS = studentMap.getOrDefault(Convert.toLong(behaviourRecord.getStudentId()), new ArrayList<>());
            if (CollUtil.isNotEmpty(studentClassBffVOS)) {
                UcStudentClassBffVO ucStudentClassBffVO = studentClassBffVOS.get(0);
                behaviourRecord.setStudentName(ucStudentClassBffVO.getStudentName());
            }
            List<UcStudentClassBffVO> ucStudentClassBffVOS1 = classMap.getOrDefault(
                    Convert.toLong(behaviourRecord.getClassId()), new ArrayList<>());
            if (CollUtil.isNotEmpty(ucStudentClassBffVOS1)) {
                UcStudentClassBffVO ucStudentClassBffVO = ucStudentClassBffVOS1.get(0);
                behaviourRecord.setClassName(ucStudentClassBffVO.getClassName());
            }
            List<SubjectInfoPO> orDefault = subjectMap.getOrDefault(behaviourRecord.getRecordSubjectCode(),
                    Collections.emptyList());
            if (CollUtil.isNotEmpty(orDefault)) {
                behaviourRecord.setRecordSubjectName(orDefault.get(0).getSubjectName());
            }
        }
        if(request.getType() == 2){
            List<String> ids = behaviourRecords.stream().map(EvaluateFixDataResponse::getId)
                    .collect(Collectors.toList());
            List<Long> idLongs = Convert.toList(Long.class, ids);
            if(CollUtil.isNotEmpty(request.getUnExpectedIds())){
                idLongs.removeAll(request.getUnExpectedIds());
            }
            List<BehaviourRecord> behaviourRecords1 = behaviourRecordMapper.selectBatchIds(idLongs);
            for (BehaviourRecord behaviourRecord : behaviourRecords1) {
                EvaluateFixDataResponse orDefault = identityMap.getOrDefault(Convert.toStr(behaviourRecord.getId()),
                        new EvaluateFixDataResponse());
                behaviourRecord.setSubjectCode(orDefault.getTargetSubjectCode());
                behaviourRecord.setUpdateBy("fixData");
                behaviourRecord.setUpdateTime(new Date());
                String sql = "update evaluate_behaviour_record set subject_code = '" + behaviourRecord.getSubjectCode() + "',"
                        + "update_by = 'fixData'," + "update_time = '" + "2025-07-08 09:00:00" + "'"  +  " where id = " + behaviourRecord.getId() + ";";
                behaviourRecords.stream().filter(item -> item.getId().equals(Convert.toStr(behaviourRecord.getId()))).forEach(item -> item.setSql(sql));
            }
//            behaviourRecordManager.updateBatchById(behaviourRecords1);

        }
        // 导出
        String fileName = "需修复数据";
        try {
            EasyExcelUtil.writeExcel(response, behaviourRecords, fileName, "sheet", EvaluateFixDataResponse.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
