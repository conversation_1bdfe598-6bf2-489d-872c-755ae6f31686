package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.hailiang.model.dto.QueryClassSubjectRelDTO;
import com.hailiang.model.dto.response.TeachSubjectResponse;
import com.hailiang.model.entity.mongo.SubStudentInfo;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.staff.TeacherListByClassIdsQuery;
import com.hailiang.remote.saas.vo.educational.EduClassInfoLinkVO;
import com.hailiang.remote.saas.vo.educational.EduStaffSubjectVO;
import com.hailiang.remote.saas.vo.staff.batch.BatchStaffClassTeacherSubjectVO;
import com.hailiang.remote.saas.vo.staff.batch.BatchStaffClassTeacherVO;
import com.hailiang.remote.saas.vo.staff.batch.BatchStaffClassVO;
import com.hailiang.service.SaasSubjectInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class SaasSubjectInfoServiceImpl implements SaasSubjectInfoService {

    @Resource
    private BasicInfoRemote basicInfoRemote;


    @Override
    public TeachSubjectResponse getRelatedSubjectV2(QueryClassSubjectRelDTO request, String staffId) {

        List<SubStudentInfo> submitValue = request.getSubmitValue();
        if (ObjectUtil.isNull(request) || CollUtil.isEmpty(submitValue)) {
            log.warn("【用户:{}】-【获取点评人关联学科】-【请求参数有误:{}】", staffId, request);
            return null;
        }

        Long classId = Convert.toLong(submitValue.get(0).getClassId());

        TeachSubjectResponse teachSubjectResponse = buildClassBaseInfo(classId);

        TeacherListByClassIdsQuery teacherListByClassIdsQuery = new TeacherListByClassIdsQuery();
        teacherListByClassIdsQuery.setClassIdList(Lists.newArrayList((classId)));

        Map<Long, BatchStaffClassVO> longBatchStaffClassVOMap = basicInfoRemote.queryTeachersByClassIds(teacherListByClassIdsQuery);
        if (MapUtil.isEmpty(longBatchStaffClassVOMap)) {
            log.warn("【获取任教科目】-【该班级下无教职工信息】-【classId：{}】", classId);
            return teachSubjectResponse;
        }

        //此处无需判空，因为根据该ClassId 查询到的是该班级的信息
        BatchStaffClassVO batchStaffClassVO = longBatchStaffClassVOMap.get(classId);
        if (batchStaffClassVO == null) {
            log.error("【获取任教科目】-【该班级无对应的教职工信息，Saas返回错误】-【classId：{}】", classId);
            return teachSubjectResponse;
        }

        BatchStaffClassTeacherVO batchStaffClassTeacherVO = batchStaffClassVO
                .getTeacherList()
                .stream()
                .filter(item -> item.getStaffId().equals(Convert.toLong(staffId)))
                .findFirst()
                .orElse(null);

        if (batchStaffClassTeacherVO == null || CollectionUtil.isEmpty(batchStaffClassTeacherVO.getSubjectList())) {
            log.warn("【获取任教科目】-【该教师未在该班级下任教】-【teacherId：{}，classId：{}】", staffId, classId);
            return teachSubjectResponse;
        }

        List<EduStaffSubjectVO> subjects = new ArrayList<>();


        for (BatchStaffClassTeacherSubjectVO batchStaffClassTeacherSubjectVO : batchStaffClassTeacherVO.getSubjectList()) {

            EduStaffSubjectVO eduStaffSubjectVO = new EduStaffSubjectVO();
            eduStaffSubjectVO.setSubjectCode(batchStaffClassTeacherSubjectVO.getSubjectCode());
            eduStaffSubjectVO.setSubjectName(batchStaffClassTeacherSubjectVO.getSubjectName());
            subjects.add(eduStaffSubjectVO);
        }

        teachSubjectResponse.setSubjects(subjects);
        teachSubjectResponse.setClassId(classId);

        log.info("【用户:{}】-【获取点评人关联学科-结束】-【请求参数:{}，返回：{}】", staffId, request, teachSubjectResponse);

        return teachSubjectResponse;
    }

    /**
     * 组装班级基础信息
     *
     * @param classId
     */
    private TeachSubjectResponse buildClassBaseInfo(Long classId) {

        TeachSubjectResponse teachSubjectResponse = new TeachSubjectResponse();

        //查询班级基础信息
        List<EduClassInfoLinkVO> eduClassInfos = basicInfoRemote.listUnderByClassIds(Lists.newArrayList(classId));

        EduClassInfoLinkVO eduClassInfoLinkVO = eduClassInfos.get(0);

        teachSubjectResponse.setClassId(eduClassInfoLinkVO.getId());
        teachSubjectResponse.setClassType(eduClassInfoLinkVO.getClassType());
        teachSubjectResponse.setClassName(eduClassInfoLinkVO.getClassName());
        teachSubjectResponse.setGradeId(eduClassInfoLinkVO.getGradeId());
        teachSubjectResponse.setGradeCode(eduClassInfoLinkVO.getGradeCode());
        teachSubjectResponse.setGradeName(eduClassInfoLinkVO.getGradeName());
        teachSubjectResponse.setSectionCode(eduClassInfoLinkVO.getSectionCode());
        teachSubjectResponse.setCampusId(eduClassInfoLinkVO.getCampusId());
        teachSubjectResponse.setCampusName(eduClassInfoLinkVO.getCampusName());
        teachSubjectResponse.setSchoolId(eduClassInfoLinkVO.getSchoolId());
        teachSubjectResponse.setCampusSectionId(eduClassInfoLinkVO.getCampusSectionId());

        return teachSubjectResponse;
    }


}
