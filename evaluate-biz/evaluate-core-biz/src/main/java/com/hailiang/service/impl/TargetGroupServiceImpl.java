package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.convert.TargetGroupConvert;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.SubmitRateEnum;
import com.hailiang.enums.SysSwitchConfigEnum;
import com.hailiang.enums.TargetTypeEnum;
import com.hailiang.enums.TargetUserSubmitTypeEnum;
import com.hailiang.enums.TaskUserTypeEnum;
import com.hailiang.enums.medal.MedalTargetTypeEnum;
import com.hailiang.event.center.spring.SpringEventCenter;
import com.hailiang.exception.BizException;
import com.hailiang.manager.StudentPostDetailManager;
import com.hailiang.manager.SubjectInfoManager;
import com.hailiang.internal.InternalDriveRemote;
import com.hailiang.internal.model.request.HandleScoreRequest;
import com.hailiang.manager.SysSwitchConfigManager;
import com.hailiang.manager.SysTargetGroupManager;
import com.hailiang.manager.SysTargetManager;
import com.hailiang.manager.TargetGroupManager;
import com.hailiang.manager.TargetManager;
import com.hailiang.manager.TargetUserManager;
import com.hailiang.mapper.TargetGroupMapper;
import com.hailiang.mapper.mongo.TargetTemplateDao;
import com.hailiang.model.dto.query.TargetGroupListByConditionsDTO;
import com.hailiang.model.dto.query.TargetGroupListByConditionsDaoDTO;
import com.hailiang.model.dto.query.TargetGroupListDTO;
import com.hailiang.model.dto.query.TargetIdQuery;
import com.hailiang.model.dto.query.listAllEvaluateTargetDaoDTO;
import com.hailiang.model.dto.request.InternalConfigRequest;
import com.hailiang.model.dto.response.TargetGroupCommonResponse;
import com.hailiang.model.dto.response.speed.GroupInfoResponse;
import com.hailiang.model.dto.response.speed.ModuleInfoResponse;
import com.hailiang.model.dto.response.speed.TargetInfoResponse;
import com.hailiang.model.dto.save.TargetGroupSaveDTO;
import com.hailiang.model.dto.save.TargetSaveDTO.InnerSubmitObj;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.dto.target.TargetGroupInfoResponse;
import com.hailiang.model.entity.StudentPostDetailPO;
import com.hailiang.model.entity.SysSwitchConfigPO;
import com.hailiang.model.entity.SysTarget;
import com.hailiang.model.entity.SysTargetGroup;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TargetGroup;
import com.hailiang.model.entity.TargetUserPO;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.event.targetGroup.TargetGroupDeletedEvent;
import com.hailiang.model.report.entity.SubjectInfoPO;
import com.hailiang.model.response.InternalConfigResponse;
import com.hailiang.model.vo.ListAllEvaluateTargetVOInnerTargetGroup;
import com.hailiang.model.vo.ListAllEvaluateTargetVOModule;
import com.hailiang.model.vo.ListOrgIdAndRoleIdVO;
import com.hailiang.model.vo.TargetGroupListByConditionsVO;
import com.hailiang.model.vo.TargetGroupListByConditionsVOInnerTarget;
import com.hailiang.model.vo.TargetGroupListByConditionsVOInnerTargetUser;
import com.hailiang.model.vo.TargetGroupListVO;
import com.hailiang.model.vo.TargetGroupTargetListByConditionsVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.dto.educational.EduClassQueryDTO;
import com.hailiang.remote.saas.dto.staff.TchClassStaffRequest;
import com.hailiang.remote.saas.dto.subject.StaffTchSubjectQuery;
import com.hailiang.remote.saas.vo.clazz.TchClassVO;
import com.hailiang.remote.saas.vo.educational.EduClassInfoVO;
import com.hailiang.remote.saas.vo.subject.ClassSubjectConfigVO;
import com.hailiang.remote.saas.dto.school.SchoolQueryDTO;
import com.hailiang.remote.saas.vo.administration.AdminOrgTreeVO;
import com.hailiang.saas.SaasClassManager;
import com.hailiang.saas.SaasSchoolManager;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.TargetGroupService;
import com.hailiang.service.TargetService;
import com.hailiang.service.TargetTemplateService;
import com.hailiang.util.AssertUtil;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 * @description 针对表【evaluate_target_group(指标分组表)】的数据库操作Service实现
 * @createDate 2022-12-29 11:08:23uy 指标分组
 */
@Slf4j
@Service
public class TargetGroupServiceImpl extends ServiceImpl<TargetGroupMapper, TargetGroup>
        implements TargetGroupService {

    @Resource
    private TargetGroupConvert convert;

    @Resource
    private TargetService targetService;

    @Resource
    private TargetGroupMapper targetGroupMapper;

    @Resource
    private BasicInfoService basicInfoService;

    @Resource
    private TargetTemplateService templateService;

    @Resource
    private CacheSaasManager cacheSaasManager;

    @Resource
    private TargetGroupService targetGroupService;

    @Resource
    private SysSwitchConfigManager sysSwitchConfigManager;

    @Resource
    private SpringEventCenter eventCenter;

    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private TargetManager targetManager;

    @Resource
    private TargetGroupManager targetGroupManager;


    @Resource
    private RedisUtil redisUtil;

    @Resource
    private SysTargetManager sysTargetManager;

    @Resource
    private SysTargetGroupManager sysTargetGroupManager;

    @Resource
    private TargetTemplateDao templateDao;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private TargetUserManager targetUserManager;

    @Resource
    private StudentPostDetailManager studentPostDetailManager;

    @Resource
    private SubjectInfoManager subjectInfoManager;

    @Resource
    private InternalDriveRemote internalDriveRemote;

    @Override
    public List<TargetGroupCommonResponse> listGroupIdsByCampusIdAndModuleCode(Integer moduleCode) {

        Set<Integer> moduleCodes = new HashSet<>();
        moduleCodes.add(moduleCode);

        //根据五育Code获取指标分组
        List<TargetGroup> targetGroups = targetGroupManager
                .listGroupIdsByCampusIdAndModuleCode(WebUtil.getCampusId(), moduleCodes);

        if (CollectionUtils.isEmpty(targetGroups)) {
            throw new BizException("当前学校暂无该模块指标分组");
        }

        return targetGroups.stream().map(targetGroup -> TargetGroupCommonResponse
                        .builder()
                        .groupId(targetGroup.getId())
                        .groupType(targetGroup.getGroupType())
                        .groupName(targetGroup.getGroupName())
                        .sortIndex(targetGroup.getSortIndex())
                        .build()).sorted(Comparator.comparing(TargetGroupCommonResponse::getSortIndex))
                .collect(Collectors.toList());
    }

    /**
     * 指标分组--添加、修改
     *
     * @param dto
     */
    @Override
    public void save(TargetGroupSaveDTO dto) {
        Assert.isTrue(dto.getGroupName().length() <= 10, "指标分组名称最多输入10个字符");
        // 根据模块编码获取模块名称
        String moduleName = ModuleEnum.getModuleName(dto.getModuleCode());
        // 判断模块名称是否存在
        if (StrUtil.isBlank(moduleName)) {
            throw new BizException("模块编码输入有误");
        }
        //编辑校验
        if (ObjectUtil.isNotNull(dto.getId())){
            TargetGroup targetGroup = this.getById(dto.getId());
            Assert.notNull(targetGroup, "分组不存在！");
            Assert.isTrue(TargetTypeEnum.DEFAULT.getCode().equals(targetGroup.getGroupType()),"系统默认分组，不可修改！");
        }

        TargetGroup evaluateTargetGroup = convert.toEvaluateTargetGroup(dto);
        evaluateTargetGroup.setId(StrUtil.isNotBlank(dto.getId()) ? Long.parseLong(dto.getId()) : null);
        evaluateTargetGroup.setModuleName(moduleName);

        // 只有添加时才计算排名
        if (StrUtil.isBlank(dto.getId())) {
            // 查询当前学校分组数，用于计算sortIndex，此处包含已删除分组
            int count = (int) this.count(new LambdaQueryWrapper<TargetGroup>()
                    .eq(TargetGroup::getSchoolId, WebUtil.getSchoolId())
                    .eq(TargetGroup::getCampusId, WebUtil.getCampusId())
                    .eq(TargetGroup::getDeleted, Constant.NO));
            evaluateTargetGroup.setSortIndex(count + 1);

            evaluateTargetGroup.setSchoolId(WebUtil.getSchoolId());
            evaluateTargetGroup.setCampusId(WebUtil.getCampusId());
            evaluateTargetGroup.setTenantId(WebUtil.getTenantId());
        }

        this.saveOrUpdate(evaluateTargetGroup);
    }

    /**
     * 指标分组--删除
     *
     * @param groupId
     */
    @Override
    public void delete(String groupId) {
        if (StrUtil.isBlank(groupId)) {
            return;
        }
        Long id = Long.parseLong(groupId);
        TargetGroup targetGroup = this.getById(id);
        if (Objects.isNull(targetGroup)) {
            throw new BizException("当前分组不存在或已删除，请确认后重试");
        }

        Assert.isTrue(TargetTypeEnum.DEFAULT.getCode().equals(targetGroup.getGroupType()),"系统默认分组，不可删除！");

        long count = targetService.count(new LambdaQueryWrapper<Target>()
                .eq(Target::getGroupId, id));

        if (count != 0) {
            throw new BizException("请将组内的指标移到其他分组或删除完，才能删除分组");
        }
        // 分组删除,涉及的活动规则失效
        eventCenter.publish(new TargetGroupDeletedEvent(Lists.newArrayList(targetGroup)));
//        medalActivityRuleService.updateRuleStatusFailure(Convert.toLong(groupId), MedalRuleFailureEnum.GROUP_DELETED.getCode());
        this.removeById(id);
    }

    @Override
    public int deleteDirectByIds(List<Long> groupIds, Long updator) {
        return baseMapper.batchDeleteByIds(groupIds, updator);
    }

    @Override
    public int batchUpdateSortIndex(List<Long> groupIds, List<TargetGroup> targetGroupList) {
        return baseMapper.batchUpdateSortIndex(groupIds, targetGroupList);
    }

    @Override
    public int getMaxSortIndex(String campusId, Integer moduleCode) {
        return baseMapper.getMaxSortIndex(campusId, moduleCode);
    }

    /**
     * 查分组列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<TargetGroupListVO> list(TargetGroupListDTO dto) {
        // 查分组列表
        List<TargetGroup> evaluateTargetGroupList = this.list(new LambdaQueryWrapper<TargetGroup>()
                .eq(TargetGroup::getSchoolId, WebUtil.getSchoolId())
                .eq(TargetGroup::getCampusId, WebUtil.getCampusId())
                .eq(TargetGroup::getModuleCode, dto.getModuleCode())
                .eq(TargetGroup::getDataSource, DataSourceEnum.EVALUATE.getCode())
                .eq(ObjectUtil.isNotNull(dto.getGroupType()), TargetGroup::getGroupType, dto.getGroupType())
                .orderByAsc(TargetGroup::getSortIndex));
        if (CollUtil.isEmpty(evaluateTargetGroupList)) {
            return null;
        }
        List<TargetGroupListVO> evaluateTargetGroupListVOList = convert.toEvaluateTargetGroupListVOList(
                evaluateTargetGroupList);
        // 不查分组指标--返回
        if (!dto.getQueryTargetFlag()) {
            return evaluateTargetGroupListVOList;
        }

        // 查分组指标
        for (TargetGroupListVO group : evaluateTargetGroupListVOList) {
            // 查分组列表
            List<Target> list = targetService.list(new LambdaQueryWrapper<Target>() // NOSONAR
                    .eq(Target::getGroupId, group.getGroupId())
                    .eq(Target::getDeleted, Constant.ZERO)
                    .orderByAsc(Target::getSortIndex));

            if (CollUtil.isEmpty(list)) {
                continue;
            }
            // 设置分组指标
            group.setTargetList(convert.toGroupInnerTargetList(list));
        }

        return evaluateTargetGroupListVOList;
    }

    /**
     * 指标分组--排序
     *
     * @param dtoList
     */
    @Override
    public void sort(List<String> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        List<Long> groupIdList = new ArrayList<>();
        for (String id : dtoList) {
            groupIdList.add(Long.parseLong(id));
        }
        for (int i = 0; i < groupIdList.size(); i++) {
            TargetGroup group = new TargetGroup();
            group.setId(groupIdList.get(i));
            group.setSortIndex(i + 1);
            // 修改当前学校下分组顺序
            this.update(group, new LambdaQueryWrapper<TargetGroup>() // NOSONAR
                    .eq(TargetGroup::getId, groupIdList.get(i))
                    .eq(TargetGroup::getSchoolId, WebUtil.getSchoolId())
                    .eq(TargetGroup::getCampusId, WebUtil.getCampusId())
                    .eq(TargetGroup::getDeleted, Constant.ZERO));
        }
    }

    /**
     * 通过分组id获取分组
     *
     * @param groupId
     * @return
     */
    @Override
    public TargetGroup get(Long groupId) {
        TargetGroup group = this.getById(groupId);
        if (ObjectUtil.isNull(group)) {
            return null;
        }
        if (!WebUtil.getCampusId().equals(group.getCampusId())) {
            return null;
        }
        return group;
    }

    /**
     * 根据学校id和模块编码查分组列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<TargetGroupTargetListByConditionsVO> listByConditions(TargetGroupListByConditionsDTO dto) {
        // 查分组列表
        TargetGroupListByConditionsDaoDTO daoDTO = new TargetGroupListByConditionsDaoDTO(WebUtil.getTenantId(),
                WebUtil.getSchoolId(), WebUtil.getCampusId(), dto.getModuleCode());
        List<TargetGroupListByConditionsVO> groupList = targetGroupMapper.listByConditions(daoDTO);
        if (CollUtil.isEmpty(groupList)) {
            return Lists.newArrayList();
        }
        // 获取华为云链接前缀
        String iconUrlPrefix = targetService.getIconUrlPrefix();
        // 剔除每个分组中已删除的和数据来源不是综合素质评价的
        for (TargetGroupListByConditionsVO group : groupList) {
            // 获取分组指标
            List<TargetGroupListByConditionsVOInnerTarget> targetList = group.getTargetList();
            // 筛选出数据源为综合素质评价且未删除的指标
            List<TargetGroupListByConditionsVOInnerTarget> targetFilterList = targetList.stream().filter(target -> {
                return DataSourceEnum.EVALUATE.getCode().equals(target.getDataSource()) && Constant.FALSE.equals(
                        target.getDeleted())
                        && WebUtil.getSchoolId().equals(target.getSchoolId()) && WebUtil.getCampusId()
                        .equals(target.getCampusId())
                        && WebUtil.getTenantId().equals(target.getTenantId());
            }).sorted((t1, t2) -> t1.getSortIndex().compareTo(t2.getSortIndex())).collect(Collectors.toList());
            // 筛选后为空则将当前分组对应的指标列表设置为空
            if (CollUtil.isEmpty(targetFilterList)) {
                group.setTargetList(targetFilterList);
                continue;
            }
            // 筛选未删除填写人
            for (TargetGroupListByConditionsVOInnerTarget target : targetFilterList) {
                // iconUrl添加华为云链接前缀
                target.setIconUrl(iconUrlPrefix + target.getIconUrl());
                // 设置提交类型名称
                target.setSubmitTypeName(SubmitRateEnum.getSubmitRateName(target.getSubmitType()));
                List<TargetGroupListByConditionsVOInnerTargetUser> userList = target.getSubmitUserNameList().stream()
                        .filter(user -> Constant.FALSE.equals(user.getDeleted())).collect(Collectors.toList());
                target.setSubmitUserNameList(userList);
            }
            // 设置指标列表
            group.setTargetList(targetFilterList);
        }

        return convert.toTargetGroupTargetListByConditionsVOList(groupList);
    }

    @Override
    public List<TargetGroup> listGroupByConditions(TargetGroupListByConditionsDaoDTO dto) {
        return targetGroupMapper.listGroupByConditions(dto);
    }

    /**
     * 获取当前登录人所有指标(老师端)
     *
     * @param userType 用户类型1:老师 2:家长 3:学生
     * @return
     */
    @Override
    public List<ListAllEvaluateTargetVOModule> listAllEvaluateTarget(Integer userType) {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.start("[获取当前登录人所有指标]:" + userType);
        List<ListAllEvaluateTargetVOModule> moduleList = new ArrayList<>();
        //老师
        if (TaskUserTypeEnum.TEACHER.getCode().equals(userType)) {
            moduleList = queryStaffInfoModules(true);
        }
        //家长
        if (TaskUserTypeEnum.PARENT.getCode().equals(userType)) {
            moduleList = queryParentInfoModules();
        }
        //学生
        if (TaskUserTypeEnum.STUDENT.getCode().equals(userType)) {
            moduleList = queryStudentInfoModules();
        }
        if (CollUtil.isEmpty(moduleList)) {
            return new ArrayList<>();
        }

        // 获取华为云链接前缀
        String iconUrlPrefix = targetService.getIconUrlPrefix();
        // 处理指标iconUrl添加华为云链接前缀
        moduleList.parallelStream().forEach(d -> { // NOSONAR
            List<ListAllEvaluateTargetVOInnerTargetGroup> groupList = d.getTargetGroupList();
            groupList.parallelStream().forEach(g -> {
                g.getTargetList().parallelStream()
                        .forEach(target -> target.setIconUrl(iconUrlPrefix + target.getIconUrl()));
            });
        });

        log.info("[获取当前登录人所有指标]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]",
                timeInterval.interval("[获取当前登录人所有指标]"));

        moduleList.sort((o1, o2) -> {
            int moduleCode1 = o1.getModuleCode();
            int moduleCode2 = o2.getModuleCode();

            // 如果o1的moduleCode为0，而o2的moduleCode不为0，则o1应该排在o2之后
            if (moduleCode1 == 0 && moduleCode2 != 0) {
                return 1;
            }
            // 如果o1的moduleCode不为0，而o2的moduleCode为0，则o1应该排在o2之前
            else if (moduleCode1 != 0 && moduleCode2 == 0) {
                return -1;
            }
            // 如果两个对象的moduleCode都为0或都不为0，则保持它们的相对顺序不变（或者可以根据需要进一步比较）
            else {
                return 0;
            }
        });
        return moduleList;
    }


    /**
     * 获取指标点评项
     *
     * @param targetId 指标id
     * @return
     */
    @Override
    public List<TemplateInfoSaveDTO> getTargetOption(Long targetId) {
        TargetTemplate targetTemplate = templateService.getByTargetId(targetId);

        List<TemplateInfoSaveDTO> list = new ArrayList<>();
        for (TemplateInfoSaveDTO templateInfoSaveDTO : targetTemplate.getTemplateInfoList()) {
            // 如果是单选和多选
            if (CollUtil.newArrayList("radio", "checkbox").contains(templateInfoSaveDTO.getType())) {
                list.add(templateInfoSaveDTO);
            }
            // 如果是明细
            if ("card".equals(templateInfoSaveDTO.getType())) {
                if (CollUtil.isEmpty(templateInfoSaveDTO.getList())) {
                    continue;
                }
                // 明细列表
                List<LinkedHashMap> detailList = new ArrayList<>();
                // 判断明细下的控件是否为单选和多选
                for (LinkedHashMap linkedHashMap : templateInfoSaveDTO.getList()) {
                    if (CollUtil.newArrayList("radio", "checkbox").contains(Convert.toStr(linkedHashMap.get("type")))) {
                        detailList.add(linkedHashMap);
                    }
                }
                templateInfoSaveDTO.setList(detailList);
                list.add(templateInfoSaveDTO);
            }
        }
        return list;
    }

    /**
     * 查一下当前校区下有无分组（排除掉系统推荐指标）
     * @param tenantId
     * @param schoolId
     * @param campusId
     * @param code
     * @return
     */

    @Override
    public long countWithDelete(String tenantId, String schoolId, String campusId, Integer code) {
        long count = targetGroupMapper.countWithDelete(tenantId, schoolId, campusId, code);
        return count;
    }

    /**
     * 指标树（不带选项）
     */
    @Override
    public List<ModuleInfoResponse> listGroupTargets() {
        Set<Integer> moduleCodes = ModuleEnum.moduleMap.keySet();

        // 获取分组信息
        List<GroupInfoResponse> groupInfos = listGroupInfo(WebUtil.getTenantId(), WebUtil.getSchoolId(),
                WebUtil.getCampusId());

        List<ModuleInfoResponse> list = new ArrayList<>();
        for (Integer moduleCode : moduleCodes) {
            ModuleInfoResponse ruleModuleInfoVO = new ModuleInfoResponse();
            ruleModuleInfoVO.setId(moduleCode);
            ruleModuleInfoVO.setName(ModuleEnum.getModuleName(moduleCode));
            ruleModuleInfoVO.setModuleCode(moduleCode);
            ruleModuleInfoVO.setModuleName(ModuleEnum.getModuleName(moduleCode));
            ruleModuleInfoVO.setSubmitType(MedalTargetTypeEnum.MODULE_CODE.getCode());
            List<GroupInfoResponse> infoVOS = groupInfos.stream().filter(s -> moduleCode.equals(s.getModuleCode()))
                    .collect(Collectors.toList());
            ruleModuleInfoVO.setChildren(infoVOS);
            list.add(ruleModuleInfoVO);
        }
        return list;
    }

    /**
     * 图文点评设置
     */
    @Override
    public void setPictureEvaluate(TargetIdQuery query) {
        if (BeanUtil.isEmpty(query) || CollUtil.isEmpty(query.getTargetIds())) {
            log.info("[图文点评设置]-[指标列表为空]-直接返回");
            return;
        }
        targetService.setPictureEvaluate(query);
    }

    /**
     * 图文点评指标列表
     */
    @Override
    public List<Long> listPictureEvaluates() {
        return targetService.listPictureEvaluates();
    }


    @Override
    public Integer listPointSwitch(String campusId) {
        List<SysSwitchConfigPO> sysSwitchConfigPOS = sysSwitchConfigManager.listByCampusId(campusId,
                SysSwitchConfigEnum.XWL_CAMPUS_POINT.getCode());
        if (CollUtil.isEmpty(sysSwitchConfigPOS)) {
            return 0;
        }
        return sysSwitchConfigPOS.get(0).getStatus();
    }

    @Override
    public void updatePointSwitchStatusByCampusId(Integer status) {
        String campusId = WebUtil.getCampusId();
        sysSwitchConfigManager.updateStatusByCampusId(campusId, status, SysSwitchConfigEnum.XWL_CAMPUS_POINT.getCode());
    }

    @Override
    public int batchSave(List<TargetGroup> targetGroupList) {
        if (CollectionUtils.isEmpty(targetGroupList)) {
            return 0;
        }
        List<List<TargetGroup>> partition = Lists.partition(targetGroupList, Constant.BATCH_INSERT_SIZE);
        int count = 0;
        for (List<TargetGroup> targetGroups : partition) {
            count += baseMapper.batchInsert(targetGroups);
        }
        return count;
    }

    /**
     * 根据指标id查询指标以及指标分组信息
     *
     * @param targetIds
     * @return
     */
    @Override
    public List<TargetGroupInfoResponse> listTargetGroupInfoByTargetIds(List<Long> targetIds) {
        if (CollUtil.isEmpty(targetIds)) {
            log.info("根据指标id查询指标以及指标分组信息，参数为空，入参：【{}】", JSONUtil.toJsonStr(targetIds));
            return Collections.emptyList();
        }
        List<TargetGroupInfoResponse> responses = targetGroupMapper.listTargetGroupInfoByTargetIds(targetIds);
        return responses;
    }

    @Override
    @Transactional
    public boolean updateInternalConfigByCampusId(InternalConfigRequest request) {
        String campusId = WebUtil.getCampusId();
        Integer allowTeacherCreateTargetFlag = request.getAllowTeacherCreateTargetFlag();
        BigDecimal minPoint = request.getMinPoint();
        BigDecimal maxPoint = request.getMaxPoint();
        Integer allowTeacherTargetScoreExchangeFlag = request.getAllowTeacherTargetScoreExchangeFlag();
        Integer adjustLimitScore = request.getAdjustLimitScore();
        Integer quickLoginFlag = request.getQuickLoginFlag();
        Integer quickLoginDays = request.getQuickLoginDays();
        // 允许老师快捷登录
        HashMap<String, Object> dayMap = new HashMap<>();
        dayMap.put("quickLoginDays", quickLoginDays);
        // 设置是否允许老师自建指标
        Map<String, Object> map = new HashMap<>();
        map.put("minPoint", minPoint);
        map.put("maxPoint", maxPoint);
        // 设置是否允许老师自建指标
        sysSwitchConfigManager.updateStatusByCampusId(campusId, allowTeacherCreateTargetFlag, JSONUtil.toJsonStr(map),
                SysSwitchConfigEnum.XDL_CAMPUS_INTERNAL.getCode());
        // 设置老师自建指标可兑换标志
        sysSwitchConfigManager.updateStatusByCampusId(campusId, allowTeacherTargetScoreExchangeFlag,
                SysSwitchConfigEnum.XWL_CAMPUS_POINT.getCode());
        // 设置是否允许老师快捷登录
        sysSwitchConfigManager.updateStatusByCampusId(campusId, quickLoginFlag, JSONUtil.toJsonStr(dayMap),
                SysSwitchConfigEnum.XDL_QUICK_LOGIN_FLAG.getCode());
        // 处理星动力指标分数
        sysSwitchConfigManager.updateStatusByCampusId(campusId, adjustLimitScore,
                SysSwitchConfigEnum.XDL_HIS_TARGET_LIMIT.getCode());
        if(Objects.equals(request.getAdjustLimitScore(), Constant.ONE)){
            HandleScoreRequest handleScoreRequest = new HandleScoreRequest();

            EduClassQueryDTO eduClassQueryDTO = new EduClassQueryDTO();
            eduClassQueryDTO.setSchoolId(WebUtil.getSchoolIdLong());
            eduClassQueryDTO.setCampusId(WebUtil.getCampusIdLong());
            eduClassQueryDTO.setClassTypes(Arrays.asList("0", "1"));

            List<EduClassInfoVO> eduClassInfoVOS = basicInfoRemote.queryClassInfoList(eduClassQueryDTO);
            List<Long> classIdList = eduClassInfoVOS.stream().map(EduClassInfoVO::getId).collect(Collectors.toList());

            handleScoreRequest.setMaxScore(request.getMaxPoint());
            handleScoreRequest.setMinScore(request.getMinPoint());
            handleScoreRequest.setClassIdList(classIdList);
            internalDriveRemote.handleScore(handleScoreRequest);
        }

        return false;
    }

    @Override
    public InternalConfigResponse getInternalConfig(String campusId) {
        InternalConfigResponse internalConfigResponse = new InternalConfigResponse();
        // 获取是否允许老师自建指标标志
        List<SysSwitchConfigPO> teacherCreateTargetConfigS = sysSwitchConfigManager.listByCampusId(campusId,
                SysSwitchConfigEnum.XDL_CAMPUS_INTERNAL.getCode());
        if (CollUtil.isNotEmpty(teacherCreateTargetConfigS)) {
            SysSwitchConfigPO sysSwitchConfigPO = teacherCreateTargetConfigS.get(0);
            internalConfigResponse.setAllowTeacherCreateTargetFlag(sysSwitchConfigPO.getStatus());

            String configInfo = sysSwitchConfigPO.getConfigInfo();
            if (StrUtil.isNotBlank(configInfo)) {
                JSONObject jsonObject = JSONUtil.parseObj(configInfo);
                internalConfigResponse.setMinPoint(jsonObject.getBigDecimal("minPoint", NumberUtil.toBigDecimal(-5)));
                internalConfigResponse.setMaxPoint(jsonObject.getBigDecimal("maxPoint", NumberUtil.toBigDecimal(5)));
            }
        }

        // 获取老师自建指标可兑换积分标志
        List<SysSwitchConfigPO> teacherTargetScoreExchangeS = sysSwitchConfigManager.listByCampusId(campusId,
                SysSwitchConfigEnum.XWL_CAMPUS_POINT.getCode());

        if (CollUtil.isNotEmpty(teacherTargetScoreExchangeS)) {
            SysSwitchConfigPO teacherTargetScoreExchange = teacherTargetScoreExchangeS.get(0);

            internalConfigResponse.setAllowTeacherTargetScoreExchangeFlag(teacherTargetScoreExchange.getStatus());
        }

        // 是否调整星动力历史指标分值上下限
        List<SysSwitchConfigPO> sysSwitchConfigPOS = sysSwitchConfigManager.listByCampusId(campusId,
                SysSwitchConfigEnum.XDL_HIS_TARGET_LIMIT.getCode());

        if(CollUtil.isNotEmpty(sysSwitchConfigPOS)){
            SysSwitchConfigPO sysSwitchConfigPO = sysSwitchConfigPOS.get(0);

            internalConfigResponse.setAdjustLimitScore(sysSwitchConfigPO.getStatus());
        }

        // 获取星动力允许老师快捷登录配置
        List<SysSwitchConfigPO> sysSwitchConfigPOList = sysSwitchConfigManager
                .listByCampusId(campusId, SysSwitchConfigEnum.XDL_QUICK_LOGIN_FLAG.getCode());
        if (CollUtil.isEmpty(sysSwitchConfigPOList)) {
            internalConfigResponse.setQuickLoginFlag(Constant.YES);
            internalConfigResponse.setQuickLoginDays(7);
            return internalConfigResponse;
        }
        SysSwitchConfigPO sysSwitchConfigPO = sysSwitchConfigPOList.get(0);
        internalConfigResponse.setQuickLoginFlag(sysSwitchConfigPO.getStatus());
        String configInfo = sysSwitchConfigPO.getConfigInfo();
        if (CharSequenceUtil.isNotBlank(configInfo)) {
            internalConfigResponse.setQuickLoginDays(JSONUtil.parseObj(configInfo).getInt("quickLoginDays"));
        } else {
            internalConfigResponse.setQuickLoginDays(7);
        }

        return internalConfigResponse;
    }


    @Override
    public void initializeSystemRecommendTargetGroupAndTarget() {

        String campusId = WebUtil.getCampusId();
        String initialKey = StrUtil.format(RedisKeyConstants.SYSTEM_RECOMMEND_TARGET_INITIAL_KEY, campusId);

        if (redisUtil.get(initialKey) != null) {
            log.info("【指标】-【初始化系统推荐分组指标】-【该校区正在执行中，校区Id:{}】", campusId);
            return;
        }

        redisUtil.set(initialKey, campusId, CacheConstants.HALF_HOUR);

        //系统推荐的分组
        List<SysTargetGroup> sysTargetGroups = sysTargetGroupManager.lambdaQuery()
                .eq(SysTargetGroup::getGroupType, TargetTypeEnum.SYSTEM_RECOMMEND.getCode())
                .eq(SysTargetGroup::getDeleted, Boolean.FALSE)
                .eq(SysTargetGroup::getDataSource, DataSourceEnum.EVALUATE.getCode())
                .list();
        if (CollUtil.isEmpty(sysTargetGroups)) {
            redisUtil.deleteKey(initialKey);
            log.info("【指标】-【初始化系统推荐分组指标】-【不存在系统推荐的分组，校区Id:{}】", campusId);
            return;
        }

        //系统推荐的指标
        List<SysTarget> sysTargets = sysTargetManager.lambdaQuery()
                .in(SysTarget::getGroupId, sysTargetGroups
                        .stream()
                        .map(SysTargetGroup::getId).collect(Collectors.toList()))
                .eq(SysTarget::getTargetType, TargetTypeEnum.SYSTEM_RECOMMEND.getCode())
                .eq(SysTarget::getDeleted, Boolean.FALSE)
                .list();
        if (CollUtil.isEmpty(sysTargets)) {
            redisUtil.deleteKey(initialKey);
            log.info("【指标】-【初始化系统推荐分组指标】-【不存在系统推荐的指标，校区Id:{}】", campusId);
            return;
        }

        //系统指标关联的模板
        List<String> sysTemplateIds = sysTargets
                .stream()
                .map(SysTarget::getTemplateId).collect(Collectors.toList());
        Query mongoQuery = new Query();
        mongoQuery.addCriteria(Criteria.where("id").in(sysTemplateIds)).addCriteria(Criteria.where("deleted").is(0));
        List<TargetTemplate> targetTemplates = templateService.listByTemplateIds(mongoQuery);
        if (CollUtil.isEmpty(targetTemplates)) {
            redisUtil.deleteKey(initialKey);
            log.info("【指标】-【初始化系统推荐分组指标】-【不存在指标模板，校区Id:{}】", campusId);
            return;
        }

        //获取当前校区的分组
        List<TargetGroup> targetGroups = targetGroupManager.lambdaQuery()
                .eq(TargetGroup::getCampusId, campusId)
                .eq(TargetGroup::getDataSource, DataSourceEnum.EVALUATE.getCode())
                .eq(TargetGroup::getDeleted, Boolean.FALSE)
                .list();

        //获取当前校区的指标
        List<Target> targets = Lists.newArrayList();
        if (CollUtil.isNotEmpty(targetGroups)) {
            targets = targetManager.lambdaQuery()
                    .in(Target::getGroupId, targetGroups
                            .stream()
                            .map(TargetGroup::getId).collect(Collectors.toList()))
                    .eq(Target::getDeleted, Boolean.FALSE)
                    .list();
        }

        //比对出需要新增的分组
        List<TargetGroup> needSaveTargetGroups = this.diffNeedSaveTargetGroup(
                targetGroups,
                sysTargetGroups);

        //比对出需要新增的指标
        List<Target> needSaveTargets = this.diffNeedSaveTarget(
                targetGroups,
                needSaveTargetGroups,
                targetTemplates,
                sysTargetGroups,
                targets,
                sysTargets);

        //组装指标填写人
        List<TargetUserPO> needSaveTargetUsers = this.buildTargetUser(needSaveTargets);

        //事务中保存数据
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NonNull TransactionStatus status) {
                try {
                    //分组
                    if (CollUtil.isNotEmpty(needSaveTargetGroups)) {
                        targetGroupService.saveBatch(needSaveTargetGroups);
                    }
                    //指标
                    if (CollUtil.isNotEmpty(needSaveTargets)) {
                        targetService.saveBatch(needSaveTargets);
                    }
                    //指标填写人
                    if (CollUtil.isNotEmpty(needSaveTargetUsers)){
                        targetUserManager.saveBatch(needSaveTargetUsers);
                    }
                    log.info("【指标】-【初始化系统推荐分组指标】-【数据保存成功，校区Id:{},groupSize:{},targetSize:{},targetUserSize:{}】"
                            , campusId, needSaveTargetGroups.size(), needSaveTargets.size(),needSaveTargetUsers.size());
                } catch (Exception e) {
                    status.setRollbackOnly();
                    log.warn("【指标】-【初始化系统推荐分组指标】-【数据保存异常，校区Id:{}】", campusId, e);
                    throw e;
                } finally {
                    redisUtil.deleteKey(initialKey);
                }
            }
        });
    }

    private List<Target> diffNeedSaveTarget(List<TargetGroup> targetGroups,
                                            List<TargetGroup> needSaveTargetGroups,
                                            List<TargetTemplate> targetTemplates,
                                            List<SysTargetGroup> sysTargetGroups,
                                            List<Target> targets, List<SysTarget> sysTargets) {

        List<Target> needSaveTargets = Lists.newArrayList();

        Map<String, TargetGroup> oldTargetGroupMap = targetGroups
                .stream()
                .filter(item -> item.getGroupType().equals(TargetTypeEnum.SYSTEM_RECOMMEND.getCode()))
                .collect(Collectors.toMap(TargetGroup::getGroupName, Function.identity()));

        Map<String, TargetGroup> newTargetGroupMap = needSaveTargetGroups
                .stream()
                .collect(Collectors.toMap(TargetGroup::getGroupName, Function.identity()));

        Map<String, TargetTemplate> targetTemplateMap = targetTemplates
                .stream()
                .collect(Collectors.toMap(TargetTemplate::getId, Function.identity()));

        Map<Long, SysTargetGroup> sysTargetGroupMap = sysTargetGroups
                .stream()
                .collect(Collectors.toMap(SysTargetGroup::getId, Function.identity()));

        //过滤出系统推荐的指标
        List<String> existTargetNames = targets
                .stream()
                .filter(item -> item.getTargetType().equals(TargetTypeEnum.SYSTEM_RECOMMEND.getCode()))
                .map(Target::getTargetName).collect(Collectors.toList());

        int maxSortIndex = targets
                .stream()
                .filter(target -> ObjectUtil.isNotNull(target.getSortIndex()))
                .max(Comparator.comparing(Target::getSortIndex))
                .map(Target::getSortIndex)
                .orElse(0);

        //比对出当前校区缺少的指标（通过名称比对）
        for (SysTarget sysTarget : sysTargets) {
            String sysTargetName = sysTarget.getTargetName();
            if (existTargetNames.contains(sysTargetName)) {
                continue;
            }

            TargetTemplate targetTemplate = targetTemplateMap.get(sysTarget.getTemplateId());
            if (ObjectUtil.isNull(targetTemplate)) {
                continue;
            }

            SysTargetGroup sysTargetGroup = sysTargetGroupMap.get(sysTarget.getGroupId());
            if (ObjectUtil.isNull(sysTargetGroup)) {
                continue;
            }

            TargetGroup oldTargetGroup = oldTargetGroupMap.get(sysTargetGroup.getGroupName());
            TargetGroup newTargetGroup = newTargetGroupMap.get(sysTargetGroup.getGroupName());
            if (ObjectUtil.isNull(oldTargetGroup) && ObjectUtil.isNull(newTargetGroup)) {
                continue;
            }

            Target target = new Target();
            //组织信息
            target.setTenantId(WebUtil.getTenantId());
            target.setSchoolId(WebUtil.getSchoolId());
            target.setCampusId(WebUtil.getCampusId());
            //从系统指标复制的值
            target.setId(SnowFlakeIdUtil.nextId());
            target.setIconUrl(sysTarget.getIconUrl());
            target.setTargetName(sysTarget.getTargetName());
            target.setSubmitType(sysTarget.getSubmitType());
            target.setSubmitDate(sysTarget.getSubmitDate());
            target.setRemindTime(sysTarget.getRemindTime());
            target.setHolidayNoticeFlag(sysTarget.getHolidayNoticeFlag());
            target.setMustSubmitFlag(sysTarget.getMustSubmitFlag());
            target.setUrgeTime(sysTarget.getUrgeTime());
            target.setSendParentFlag(sysTarget.getSendParentFlag());
            target.setDataSource(sysTarget.getDataSource());
            target.setScoreControlName(sysTarget.getScoreControlName());
            target.setScoreControlType(sysTarget.getScoreControlType());
            target.setScore(sysTarget.getScore());
            target.setScoreValue(sysTarget.getScoreValue());
            target.setPictureEvaluateFlag(sysTarget.getPictureEvaluateFlag());
            target.setTargetType(sysTarget.getTargetType());
            //默认值
            target.setCreateBy("SystemRecommend");
            target.setCreateTime(new Date());
            target.setTargetStatus(Constant.NO);
            target.setStaffFullCheckFlag(Constant.YES);
            target.setNotPartExchange(Constant.YES);
            target.setTargetNotPartCount(Constant.YES);
            target.setIsNotifyTeacher(Constant.NO);
            target.setScoreContinuationFlag(Constant.NO);
            target.setParentSubmitLimitFlag(Constant.NO);
            target.setParentSubmitLimitTimes(Constant.ONE);
            target.setParentSubmitReviewFlag(Constant.YES);
            target.setStudentSubmitReviewFlag(Constant.YES);
            //monogo
            targetTemplate.setId(null);
            targetTemplate.setTargetId(target.getId());
            TargetTemplate resultTargetTemplate = templateDao.save(targetTemplate);
            target.setTemplateId(resultTargetTemplate.getId());
            target.setTemplateInfoJson(JSONUtil.toJsonStr(resultTargetTemplate));
            //当前关联的分组
            Long targetGroupId = ObjectUtil.isNotNull(oldTargetGroup) ? oldTargetGroup.getId() : newTargetGroup.getId();
            target.setGroupId(targetGroupId);
            //默认第一个
            target.setSortIndex(++maxSortIndex);
            target.setPictureSortIndex(Constant.ZERO);
            needSaveTargets.add(target);
        }
        return needSaveTargets;
    }

    private List<TargetGroup> diffNeedSaveTargetGroup(List<TargetGroup> targetGroups,
                                                      List<SysTargetGroup> sysTargetGroups) {

        List<TargetGroup> needSaveTargetGroups = Lists.newArrayList();

        //过滤出系统推荐的分组
        List<String> existTargetGroupNames = targetGroups
                .stream()
                .filter(item -> item.getGroupType().equals(TargetTypeEnum.SYSTEM_RECOMMEND.getCode()))
                .map(TargetGroup::getGroupName)
                .collect(Collectors.toList());

        //获取当前校区最大的分组的排序索引
        int groupMaxIndex = targetGroups.stream()
                .filter(group -> ObjectUtil.isNotNull(group.getSortIndex()))
                .max(Comparator.comparing(TargetGroup::getSortIndex))
                .map(TargetGroup::getSortIndex)
                .orElse(0);

        Date currentTime = new Date();
        String operator = "SystemRecommend";

        //比对出当前校区缺少的分组（通过名称比对）
        for (SysTargetGroup sysTargetGroup : sysTargetGroups) {
            String sysGroupName = sysTargetGroup.getGroupName();
            if (existTargetGroupNames.contains(sysGroupName)) {
                continue;
            }

            TargetGroup targetGroup = new TargetGroup();
            //组织信息
            targetGroup.setSchoolId(WebUtil.getSchoolId());
            targetGroup.setCampusId(WebUtil.getCampusId());
            targetGroup.setTenantId(WebUtil.getTenantId());
            //从系统分组复制的值
            targetGroup.setModuleCode(sysTargetGroup.getModuleCode());
            targetGroup.setModuleName(sysTargetGroup.getModuleName());
            targetGroup.setGroupName(sysTargetGroup.getGroupName());
            targetGroup.setDataSource(sysTargetGroup.getDataSource());
            targetGroup.setGroupType(sysTargetGroup.getGroupType());
            //默认值
            targetGroup.setId(SnowFlakeIdUtil.nextId());
            targetGroup.setCreateBy(operator);
            targetGroup.setCreateTime(currentTime);
            targetGroup.setDeleted(Boolean.FALSE);
            //排序索引
            targetGroup.setSortIndex(++groupMaxIndex);
            needSaveTargetGroups.add(targetGroup);
        }
        return needSaveTargetGroups;
    }

    private List<TargetUserPO> buildTargetUser(List<Target> needSaveTargets) {
        if (CollUtil.isEmpty(needSaveTargets)){
            return Collections.emptyList();
        }
        //设置学校机构id，作为指标点评人
        SchoolQueryDTO schoolQueryDTO = new SchoolQueryDTO();
        schoolQueryDTO.setSchoolId(Long.valueOf(WebUtil.getSchoolId()));
        List<AdminOrgTreeVO> adminOrgTreeVOList = basicInfoRemote.listOrgTreeBySchoolId(schoolQueryDTO);
        AssertUtil.checkNotEmpty(adminOrgTreeVOList, "学校机构为空!");
        AdminOrgTreeVO orgTreeVO = CollUtil.getFirst(adminOrgTreeVOList);

        List<TargetUserPO> needSaveTargetUsers = Lists.newArrayList();

        for (Target needSaveTarget : needSaveTargets) {
            TargetUserPO  targetUserPO = new TargetUserPO();
            targetUserPO.setId(SnowFlakeIdUtil.nextId());
            targetUserPO.setTenantId(WebUtil.getTenantId());
            targetUserPO.setSchoolId(WebUtil.getSchoolId());
            targetUserPO.setCampusId(WebUtil.getCampusId());
            targetUserPO.setTargetId(needSaveTarget.getId());
            targetUserPO.setSubmitUserName(orgTreeVO.getOrgName());
            targetUserPO.setSubmitType(TargetUserSubmitTypeEnum.ORG.getCode());
            targetUserPO.setSubmitValue(orgTreeVO.getOrgId());
            targetUserPO.setCreateBy("SystemRecommend");
            targetUserPO.setCreateTime(new Date());
            targetUserPO.setDeleted(Boolean.FALSE);
            needSaveTargetUsers.add(targetUserPO);
        }
        return needSaveTargetUsers;
    }

    /**
     * 获取分组信息
     *
     * @param tenantId
     * @param schoolId
     * @param campusId
     * @return
     */
    private List<GroupInfoResponse> listGroupInfo(String tenantId, String schoolId, String campusId) {
        List<TargetGroup> targetGroups = targetGroupService.list(new LambdaQueryWrapper<TargetGroup>()
                .eq(TargetGroup::getTenantId, tenantId)
                .eq(TargetGroup::getSchoolId, schoolId)
                .in(TargetGroup::getDataSource, CollUtil.newArrayList(DataSourceEnum.EVALUATE.getCode()))
                .eq(TargetGroup::getCampusId, campusId));
        if (CollUtil.isEmpty(targetGroups)) {
            return Collections.emptyList();
        }

        List<GroupInfoResponse> list = new ArrayList<>();
        for (TargetGroup targetGroup : targetGroups) {
            GroupInfoResponse ruleGroupInfoVO = new GroupInfoResponse();
            ruleGroupInfoVO.setId(targetGroup.getId());
            ruleGroupInfoVO.setName(targetGroup.getGroupName());
            ruleGroupInfoVO.setSubmitType(MedalTargetTypeEnum.GROUP_ID.getCode());
            ruleGroupInfoVO.setModuleCode(targetGroup.getModuleCode());
            ruleGroupInfoVO.setModuleName(ModuleEnum.getModuleName(targetGroup.getModuleCode()));
            List<TargetInfoResponse> targetInfos = listTargetInfo(ruleGroupInfoVO);
            if (CollUtil.isNotEmpty(targetInfos)) {
                ruleGroupInfoVO.setChildren(targetInfos);
                list.add(ruleGroupInfoVO);
            }
        }

        return list;
    }

    /**
     * 获取指标信息(只获取综评指标)
     *
     * @param ruleGroupInfoVO
     * @return
     */
    private List<TargetInfoResponse> listTargetInfo(GroupInfoResponse ruleGroupInfoVO) {
        List<Target> targets = targetService.list(new LambdaQueryWrapper<Target>()
                .eq(Target::getGroupId, ruleGroupInfoVO.getId())
                .eq(Target::getTargetStatus, Constant.YES)
                .in(Target::getDataSource, CollUtil.newArrayList(DataSourceEnum.EVALUATE.getCode()))
                .orderByAsc(Target::getSortIndex));
        if (CollUtil.isEmpty(targets)) {
            return Collections.emptyList();
        }
        // 获取华为云链接前缀
        String iconUrlPrefix = targetService.getIconUrlPrefix();

        List<TargetInfoResponse> list = new ArrayList<>();
        for (Target target : targets) {
            TargetInfoResponse ruleTargetInfoVO = new TargetInfoResponse();
            ruleTargetInfoVO.setModuleCode(ruleGroupInfoVO.getModuleCode());
            ruleTargetInfoVO.setModuleName(ruleGroupInfoVO.getModuleName());
            ruleTargetInfoVO.setParentId(ruleGroupInfoVO.getId());
            ruleTargetInfoVO.setParentName(ruleGroupInfoVO.getName());
            ruleTargetInfoVO.setId(target.getId());
            ruleTargetInfoVO.setName(target.getTargetName());
            ruleTargetInfoVO.setIconUrl(iconUrlPrefix + target.getIconUrl());
            ruleTargetInfoVO.setSubmitType(MedalTargetTypeEnum.TARGET_ID.getCode());
            list.add(ruleTargetInfoVO);
        }
        return list;
    }


    /**
     * 查询当前登录人的所有填报指标(老师)
     *
     * @return
     */
    @Override
    public List<ListAllEvaluateTargetVOModule> queryStaffInfoModules(boolean pictureEvaluateFlag) {
//        List<Long> subjectIdList = this.queryTeacherTeachInfo(classId);
        ListOrgIdAndRoleIdVO listOrgRole = basicInfoService.listOrgIdAndRoleId();
        if (ObjectUtil.isNull(listOrgRole)) {
            return null;
        }
        // 合并组织机构id集合和角色id集合
        List<String> orgIdRoleIdList = new ArrayList<>();
        orgIdRoleIdList.addAll(listOrgRole.getOrgIdList());
        orgIdRoleIdList.addAll(listOrgRole.getRoleIdList());
        // 添加当前教职工的staffId
        orgIdRoleIdList.add(WebUtil.getStaffId());
        // 添加当前教职工的任教
//        orgIdRoleIdList.addAll(Convert.toList(String.class, subjectIdList));

        listAllEvaluateTargetDaoDTO query = new listAllEvaluateTargetDaoDTO()
                .setSchoolId(WebUtil.getSchoolId())
                .setCampusId(WebUtil.getCampusId())
                .setSubmitValueList(orgIdRoleIdList)
                .setPictureEvaluateFlag(pictureEvaluateFlag ? Constant.YES : Constant.NO);
        // 当前登录人所有模块，所有分组，所有指标
        List<ListAllEvaluateTargetVOModule> moduleList = targetGroupMapper.listAllEvaluateTarget(query);
        return moduleList;
    }

    /**
     * 查询老师在该校区的所有任教课程
     *
     * @param classId
     * @return
     */
    private List<Long> queryTeacherTeachInfo(Long classId) {
        if (Objects.isNull(classId)) {
            return Collections.emptyList();
        }
        Long staffId = WebUtil.getStaffIdLong();
        StaffTchSubjectQuery request = new StaffTchSubjectQuery(staffId);
        List<ClassSubjectConfigVO> classSubjectConfigVOS = basicInfoRemote.queryAllSubject(request);
        if (CollUtil.isEmpty(classSubjectConfigVOS)) {
            return Collections.emptyList();
        }
        return classSubjectConfigVOS.stream().filter(item -> Objects.equals(item.getClassId(), classId))
                .map(ClassSubjectConfigVO::getSubjectId)
                .collect(Collectors.toList());
    }

    /**
     * 查询老师在该校区的所有填报指标
     */
    @Override
    public List<ListAllEvaluateTargetVOModule> queryStaffInfoModules(Long staffId, String campusId, String schoolId) {
        ListOrgIdAndRoleIdVO listOrgRole = basicInfoService.listOrgIdAndRoleId(staffId, schoolId);
        if (ObjectUtil.isNull(listOrgRole)) {
            log.warn(
                    "[查询老师在该校区的所有填报指标]-[获取组织机构id集合和角色id集合]-[为空]-直接返回，staffId:{},schoolId:{}",
                    staffId, schoolId);
            return Collections.emptyList();
        }
        // 合并组织机构id集合和角色id集合
        List<String> orgIdRoleIdList = new ArrayList<>();
        orgIdRoleIdList.addAll(listOrgRole.getOrgIdList());
        orgIdRoleIdList.addAll(listOrgRole.getRoleIdList());
        // 添加当前教职工的staffId
        orgIdRoleIdList.add(Convert.toStr(staffId));

        listAllEvaluateTargetDaoDTO query = new listAllEvaluateTargetDaoDTO()
                .setSchoolId(schoolId)
                .setCampusId(campusId)
                .setSubmitValueList(orgIdRoleIdList);
        // 当前登录人所有模块，所有分组，所有指标
        List<ListAllEvaluateTargetVOModule> moduleList = targetGroupMapper.listAllEvaluateTarget(query);
        return moduleList;
    }

    /**
     * 查询当前登录人的所有填报指标(家长)
     *
     * @return
     */
    private List<ListAllEvaluateTargetVOModule> queryParentInfoModules() {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.start("[查询当前登录人的所有填报指标(家长)]");

//        List<Long> studentOrgIds = basicInfoService.listRelationInfo(WebUtil.getStudentIdStr());
        List<Long> studentOrgIds = cacheSaasManager.listRelationInfo(WebUtil.getStudentIdStr());

        log.info(
                "[查询当前登录人的所有填报指标(家长)-[根据学生id获取层级关系(有关系的链路id集合)]]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]",
                timeInterval.interval("[查询当前登录人的所有填报指标(家长)]"));
        //去重
        List<String> collect = studentOrgIds.stream().map(Convert::toStr).distinct().collect(Collectors.toList());
        //加上当前学生id
        collect.add(WebUtil.getStudentIdStr());

        listAllEvaluateTargetDaoDTO query = new listAllEvaluateTargetDaoDTO()
                .setSubmitValueList(collect);
        // 当前登录人所有模块，所有分组，所有指标
        // todo 性能优化，注释原代码
//        List<ListAllEvaluateTargetVOModule> moduleList = targetGroupMapper.listParentAllEvaluateTarget(query);

        List<ListAllEvaluateTargetVOModule> moduleList = targetGroupManager.listParentAllEvaluateTarget(query);

        log.info("[查询当前登录人的所有填报指标(家长)]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]",
                timeInterval.interval("[查询当前登录人的所有填报指标(家长)]"));

        return moduleList;
    }

    /**
     * 查询当前登录人的所有填报指标(学生)
     *
     * @return
     */
    private List<ListAllEvaluateTargetVOModule> queryStudentInfoModules() {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.start("[查询当前登录人的所有填报指标(学生)]");

        List<Long> studentOrgIds = cacheSaasManager.listRelationInfo(WebUtil.getStudentIdStr());

        log.info(
                "[查询当前登录人的所有填报指标(家长)-[根据学生id获取层级关系(有关系的链路id集合)]]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]",
                timeInterval.interval("[查询当前登录人的所有填报指标(家长)]"));

        //加上当前学生id
        studentOrgIds.add(WebUtil.getStudentIdLong());

        // 获取所有与当前学生相关的指标信息(未过滤职位信息)
        List<TargetUserPO> targetUsers = targetGroupManager.listStudentTargetUserInfo(studentOrgIds, WebUtil.getCampusId());

        // 过滤出职位的指标id信息
        List<Long> studentPositionIds =  this.handleStudentPositionInfosV2(targetUsers);

        // 过滤出非学生职位的指标id
        List<Long> targetIds = targetUsers.stream().filter(s -> !ObjectUtil.equal(s.getSubmitType(), 20))
                .map(TargetUserPO::getTargetId).collect(Collectors.toList());
        targetIds.addAll(studentPositionIds);

        // 查询所有指标信息
        List<ListAllEvaluateTargetVOModule> targetModules = targetGroupManager.listParentAllEvaluateTargetByTargetIds(targetIds);

        log.info("[查询当前登录人的所有填报指标(学生)]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]",
                timeInterval.interval("[查询当前登录人的所有填报指标(学生)]"));

        return targetModules;
    }

    private List<Long> handleStudentPositionInfos(List<TargetUserPO> targetUsers) {
        // 非职位的指标id集合
        List<Long> targetIds = targetUsers.stream().filter(s -> !ObjectUtil.equal(s.getSubmitType(), 20))
                .map(TargetUserPO::getTargetId).distinct().collect(Collectors.toList());

        List<TargetUserPO> studentPositionInfos = targetUsers.stream().filter(s -> ObjectUtil.equal(s.getSubmitType(), 20))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(studentPositionInfos)){
            return targetIds;
        }

        // 获取当前学生职位信息
        Long studentIdLong = WebUtil.getStudentIdLong();
        Long campusIdLong = WebUtil.getCampusIdLong();
        List<StudentPostDetailPO> studentPostDetails = studentPostDetailManager.listCurrentStudentPositionInfo(studentIdLong,campusIdLong);
        if (CollUtil.isEmpty(studentPostDetails)){
            return targetIds;
        }
        // 过滤出当前学生拥有的职位id
        List<Long> havePostIds = studentPostDetails.stream().map(StudentPostDetailPO::getPostId).distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(havePostIds)){
            return targetIds;
        }

        // 获取最终需要匹配的用户关系数据
        List<TargetUserPO> needFilterTargetUsers = studentPositionInfos.stream().filter(s -> {
            boolean flag = false;
            if (Objects.isNull(s.getSubmitObjectValue())) {
                flag = false;
            }
            InnerSubmitObj bean = JSONUtil.parse(s.getSubmitObjectValue()).toBean(InnerSubmitObj.class);
            if (havePostIds.contains(bean.getPostId())) {
                flag = true;
            }
            return flag;
        }).collect(Collectors.toList());

        if (CollUtil.isEmpty(needFilterTargetUsers)){
            return targetIds;
        }

        // 匹配职位
        List<TargetUserPO> matchSubmits = new ArrayList<>();
        for (TargetUserPO needFilterTargetUser : needFilterTargetUsers) {
            InnerSubmitObj needFilterSubmit = JSONUtil.parse(needFilterTargetUser.getSubmitObjectValue()).toBean(InnerSubmitObj.class);
            // 如果是选择到年级
            if (Objects.nonNull(needFilterSubmit.getSectionId()) && Objects.nonNull(needFilterSubmit.getGradeId())) {
                List<StudentPostDetailPO> matchRecords = studentPostDetails.stream().filter(s -> {
                    boolean flag = false;
                    if (ObjectUtil.equal(s.getGradeId(), needFilterSubmit.getGradeId())
                            && ObjectUtil.equal(s.getSubjectId(), needFilterSubmit.getSubjectId())
                            && ObjectUtil.equal(s.getSubjectType(), needFilterSubmit.getSubjectType())) {
                        flag = true;
                    }
                    return flag;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(matchRecords)) {
                    matchSubmits.add(needFilterTargetUser);
                }
            }
            // 如果是选择到学段
            if (Objects.nonNull(needFilterSubmit.getSectionId()) && Objects.isNull(needFilterSubmit.getGradeId())) {
                List<StudentPostDetailPO> matchRecords = studentPostDetails.stream().filter(s -> {
                    boolean flag = false;
                    if (ObjectUtil.equal(s.getCampusSectionId(), needFilterSubmit.getSectionId())
                            && ObjectUtil.equal(s.getSubjectId(), needFilterSubmit.getSubjectId())
                            && ObjectUtil.equal(s.getSubjectType(), needFilterSubmit.getSubjectType())) {
                        flag = true;
                    }
                    return flag;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(matchRecords)) {
                    matchSubmits.add(needFilterTargetUser);
                }
            }
            // 如果是选择到岗位
            if (Objects.isNull(needFilterSubmit.getSectionId()) && Objects.isNull(needFilterSubmit.getGradeId())) {
                List<StudentPostDetailPO> matchRecords = studentPostDetails.stream().filter(s -> {
                    boolean flag = false;
                    if (ObjectUtil.equal(s.getPostId(), needFilterSubmit.getPostId())
                            && ObjectUtil.equal(s.getSubjectId(), needFilterSubmit.getSubjectId())
                            && ObjectUtil.equal(s.getSubjectType(), needFilterSubmit.getSubjectType())) {
                        flag = true;
                    }
                    return flag;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(matchRecords)) {
                    matchSubmits.add(needFilterTargetUser);
                }
            }
        }
        List<Long> matchTargetIds = matchSubmits.stream().map(TargetUserPO::getTargetId).distinct()
                .collect(Collectors.toList());
        targetIds.addAll(matchTargetIds);
        return targetIds.stream().distinct().collect(Collectors.toList());
    }

    private List<Long> handleStudentPositionInfosV2(List<TargetUserPO> targetUsers) {
        // 非职位的指标id集合
        List<Long> targetIds = targetUsers.stream().filter(s -> !ObjectUtil.equal(s.getSubmitType(), 20))
                .map(TargetUserPO::getTargetId).distinct().collect(Collectors.toList());

        List<TargetUserPO> studentPositionInfos = targetUsers.stream()
                .filter(s -> ObjectUtil.equal(s.getSubmitType(), 20))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(studentPositionInfos)) {
            return targetIds;
        }

        // 获取当前学生职位信息
        Long studentIdLong = WebUtil.getStudentIdLong();
        Long campusIdLong = WebUtil.getCampusIdLong();
        List<StudentPostDetailPO> studentPostDetails = studentPostDetailManager.listCurrentStudentPositionInfo(
                studentIdLong, campusIdLong);
        if (CollUtil.isEmpty(studentPostDetails)) {
            return targetIds;
        }
        // 过滤出当前学生拥有的职位id
        List<Long> havePostIds = studentPostDetails.stream().map(StudentPostDetailPO::getPostId).distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(havePostIds)) {
            return targetIds;
        }

        // 获取最终需要匹配的用户关系数据
        List<TargetUserPO> needFilterTargetUsers = studentPositionInfos.stream().filter(s -> {
            boolean flag = false;
            if (Objects.isNull(s.getSubmitObjectValue())) {
                flag = false;
            }
            InnerSubmitObj bean = JSONUtil.parse(s.getSubmitObjectValue()).toBean(InnerSubmitObj.class);
            if (havePostIds.contains(bean.getPostId())) {
                flag = true;
            }
            return flag;
        }).collect(Collectors.toList());

        if (CollUtil.isEmpty(needFilterTargetUsers)) {
            return targetIds;
        }

        // 获取指标配置中科目下的课程信息map
        Map<Long, List<Long>> disciplineSubjectMap = this.getDisciplineSubjectMap(needFilterTargetUsers);

        // 匹配职位
        List<TargetUserPO> matchSubmits = new ArrayList<>();
        for (TargetUserPO needFilterTargetUser : needFilterTargetUsers) {
            InnerSubmitObj needFilterSubmit = JSONUtil.parse(needFilterTargetUser.getSubmitObjectValue())
                    .toBean(InnerSubmitObj.class);
            // 如果是选择到年级
            if (Objects.nonNull(needFilterSubmit.getSectionId()) && Objects.nonNull(needFilterSubmit.getGradeId())) {
                List<StudentPostDetailPO> matchRecords = studentPostDetails.stream().filter(studentPostDetail -> {
                    boolean flag = false;
                    if (ObjectUtil.equal(studentPostDetail.getGradeId(), needFilterSubmit.getGradeId())) {
                        // 科目匹配
                        flag = this.matchSubject(needFilterSubmit, studentPostDetail, disciplineSubjectMap);
                    }
                    return flag;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(matchRecords)) {
                    matchSubmits.add(needFilterTargetUser);
                }
            }
            // 如果是选择到学段
            if (Objects.nonNull(needFilterSubmit.getSectionId()) && Objects.isNull(needFilterSubmit.getGradeId())) {
                List<StudentPostDetailPO> matchRecords = studentPostDetails.stream().filter(studentPostDetail -> {
                    boolean flag = false;
                    if (ObjectUtil.equal(studentPostDetail.getCampusSectionId(), needFilterSubmit.getSectionId())) {
                        // 科目匹配
                        flag = this.matchSubject(needFilterSubmit, studentPostDetail, disciplineSubjectMap);
                    }
                    return flag;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(matchRecords)) {
                    matchSubmits.add(needFilterTargetUser);
                }
            }
            // 如果是选择到岗位
            if (Objects.isNull(needFilterSubmit.getSectionId()) && Objects.isNull(needFilterSubmit.getGradeId())) {
                List<StudentPostDetailPO> matchRecords = studentPostDetails.stream().filter(studentPostDetail -> {
                    boolean flag = false;
                    if (ObjectUtil.equal(studentPostDetail.getPostId(), needFilterSubmit.getPostId())) {
                        // 科目匹配
                        flag = this.matchSubject(needFilterSubmit, studentPostDetail, disciplineSubjectMap);
                    }
                    return flag;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(matchRecords)) {
                    matchSubmits.add(needFilterTargetUser);
                }
            }
        }
        List<Long> matchTargetIds = matchSubmits.stream().map(TargetUserPO::getTargetId).distinct()
                .collect(Collectors.toList());
        targetIds.addAll(matchTargetIds);
        return targetIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 当指标配的是科目，则底下课程的课代表都有权限，当指标配置的是课程，则该科目下的其他课程不应该有权限
     *
     * @param needFilterSubmit 指标配置的职位信息
     * @param studentPostDetail 学生拥有的职位
     * @param disciplineSubjectMap 科目下所有的课程信息map
     * @return
     */
    private boolean matchSubject(InnerSubmitObj needFilterSubmit, StudentPostDetailPO studentPostDetail,
                                 Map<Long, List<Long>> disciplineSubjectMap) {
        boolean flag = false;
        // 如果配置的是课程,则必须课程相等
        if (ObjectUtil.equal(needFilterSubmit.getSubjectType(), 1)) {
            if (ObjectUtil.equal(studentPostDetail.getSubjectId(), needFilterSubmit.getSubjectId())
                    && ObjectUtil.equal(studentPostDetail.getSubjectType(), needFilterSubmit.getSubjectType())) {
                flag = true;
            }
        }
        // 如果配置的是科目,则科目下的课程也算匹配
        if (ObjectUtil.equal(needFilterSubmit.getSubjectType(), 2)) {
            // 获取到这个配置下的所有科目和课程信息
            List<Long> needMatchSubjectIds = CollUtil.newArrayList(needFilterSubmit.getSubjectId());
            if (CollUtil.isNotEmpty(disciplineSubjectMap.get(needFilterSubmit.getSubjectId()))) {
                needMatchSubjectIds.addAll(disciplineSubjectMap.get(needFilterSubmit.getSubjectId()));
            }
            if (needMatchSubjectIds.contains(studentPostDetail.getSubjectId())) {
                flag = true;
            }
        }

        // 其他情况匹配是否相等
        if (!CollUtil.newArrayList(1, 2).contains(studentPostDetail.getSubjectType())) {
            // 如果不是科目职位
            if (ObjectUtil.equal(needFilterSubmit.getSubjectType(), studentPostDetail.getSubjectType())
                    && ObjectUtil.equal(studentPostDetail.getSubjectId(), needFilterSubmit.getSubjectId())) {
                flag = true;
            }
        }

        return flag;
    }

    /**
     * 获取指标配置中科目下的课程信息map
     *
     * @param needFilterTargetUsers
     * @return
     */
    private Map<Long, List<Long>> getDisciplineSubjectMap(List<TargetUserPO> needFilterTargetUsers) {
        // 获取配置的科目id集合
        List<Long> disciplineIds = needFilterTargetUsers.stream()
                .map(s -> JSONUtil.parse(s.getSubmitObjectValue()).toBean(InnerSubmitObj.class))
                .filter(s -> ObjectUtil.equal(s.getSubjectType(), 2)).map(InnerSubmitObj::getSubjectId).distinct().collect(
                        Collectors.toList());
        // 根据科目id查询科目以及科目下的课程信息
        List<SubjectInfoPO> subjectInfos = subjectInfoManager.listByCampusIdAndDisciplineIds(WebUtil.getCampusIdLong(),disciplineIds);
        if (CollUtil.isEmpty(subjectInfos)){
            return new HashMap<>();
        }
        // 按 parentDisciplineId 分组，并收集每个分组下的所有 subjectId
        return subjectInfos.stream()
                .filter(s-> CharSequenceUtil.isNotBlank(s.getParentDisciplineId()))
                .collect(Collectors.groupingBy(
                        s->Convert.toLong(s.getParentDisciplineId()),
                        Collectors.mapping(s->Convert.toLong(s.getSubjectId()), Collectors.toList())
                ));
    }
}




