package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.dto.query.BehaviourRecordQueryDTO;
import com.hailiang.model.dto.query.ReportReviewQueryDTO;
import com.hailiang.model.dto.query.ReportSendDTO;
import com.hailiang.model.vo.*;
import com.hailiang.util.R;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/13 15:15
 */
public interface ReportService {

    /**
     * 班主任分页获取审核列表
     *
     * @param query
     * @return
     */
    Page<ReportReviewInfoVO> pageReportReview(ReportReviewQueryDTO query);

    /**
     * 获取班级未审核数据列表
     *
     * @param reviewTaskId   审核任务id
     * @param reviewDetailId 审核详情id(获取审核前后节点时传入调用,正常查询不需要)
     * @return {@link List}<{@link ReportNoReviewDetailVO}>
     */
    List<ReportNoReviewDetailVO> listReportReviewDetail(Long reviewTaskId, Long reviewDetailId);

    /**
     * 获取班级已发送数据列表
     *
     * @param reviewTaskId 审查任务id
     * @return {@link List}<{@link ReportSendDetailInfoVO}>
     */
    List<ReportSendDetailInfoVO> listSendReviewDetail(Long reviewTaskId);

    /**
     * 获取默认推送规则
     *
     * @param reviewTaskId 审核任务id
     * @return {@link ReportReviewRuleVO}
     */
    ReportReviewRuleVO getReportReviewRule(Long reviewTaskId);

    /**
     * 获取行为明细
     *
     * @param reviewDetailId 审核详情id
     * @return {@link List}<{@link ReportBehaviorDetailVO}>
     */
    List<ReportBehaviorDetailVO> listReportBehaviorDetail(Long reviewDetailId,  List<Long> unNeedBehaviourRecordIds);

    /**
     * 部分推送
     *
     * @param dto dto
     */
    ReportSendResultVO reportPartSend(ReportSendDTO dto);

    /**
     * 全部推送
     *
     * @param dto dto
     */
    ReportSendResultVO teacherApprovalReport(ReportSendDTO dto);

    /**
     * 自动审核推送
     * @param dto
     * @return
     */
    ReportSendResultVO autoApprovalReportParamCheck(ReportSendDTO dto);

    /**
     * 获取学生详情的前后节点
     *
     * @param reviewDetailId 审核详情id
     * @param sendType       是否已发送 1：是  0：否
     * @return {@link R}<{@link ReportPreNextNodeVO}>
     */
    ReportPreNextNodeVO getReportPreNextNode(Long reviewDetailId, Integer sendType);

    List<ReportBehaviorDetailVO> listNoSendBehaviorRecord(BehaviourRecordQueryDTO behaviourRecordQueryDTO);
}
