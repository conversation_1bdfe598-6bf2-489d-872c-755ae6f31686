package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.dto.query.GetEvaluateInfoDetailQueryDTO;
import com.hailiang.model.processeva.request.MoralSportDetailRequest;
import com.hailiang.model.processeva.request.ProcessEvaRecordIdsRequest;
import com.hailiang.model.processeva.request.ProcessEvaRecordRequest;
import com.hailiang.model.processeva.request.SpeedInfoDetailRequest;
import com.hailiang.model.processeva.response.ProcessEvaRecordResponse;
import com.hailiang.model.vo.GetEvaluateInfoDetailVO;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 过程性评价
 * @Author: huyouting
 * @Date: Created in 2024-12-02
 * @Version: v2.1.0
 */
public interface ProcessEvaService {

    /**
     * 分页查询过程性评价明细记录
     *
     * @param request
     * @return
     */
    Page<ProcessEvaRecordResponse> pageProcessEvaRecord(ProcessEvaRecordRequest request);

    /**
     * 导出过程性评价明细记录
     *
     * @param request
     * @param response
     */
    void exportProcessEvaRecord(Page<ProcessEvaRecordResponse> data, HttpServletResponse response);

    /**
     * 批量删除过程性评价明细记录
     *
     * @param request
     */
    void batchDeletedProcessEvaRecord(ProcessEvaRecordIdsRequest request);

    /**
     * 获取极速点评详情(单个学生)
     *
     * @param request
     * @return
     */
    GetEvaluateInfoDetailVO getSpeedInfoDetail(SpeedInfoDetailRequest request);

    /**
     * 获取德育活动、体测详情
     *
     * @param request
     * @return
     */
    GetEvaluateInfoDetailVO getMoralSportInfoDetail(MoralSportDetailRequest request);
}
