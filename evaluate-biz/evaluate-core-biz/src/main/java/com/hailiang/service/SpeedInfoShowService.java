package com.hailiang.service;

import com.hailiang.internal.model.request.MessagePlanRequest;
import com.hailiang.internal.model.request.PlanInitialRequest;
import com.hailiang.internal.model.request.PlanListsRequest;
import com.hailiang.internal.model.request.PlanTagListsRequest;
import com.hailiang.internal.model.response.PlanListsResponse;
import com.hailiang.model.dto.request.IdListRequest;
import com.hailiang.model.response.speed.SpeedGroupDetailResponse;

import com.hailiang.model.vo.GetEvaluateTaskDetailVO;
import java.util.List;
import javax.validation.Valid;

/**
 * 极速点评
 *
 * @Description: 极速点评
 * @Author: JiangJunLi
 * @Date: Created in 2024-01-26
 * @Version: 1.6.0
 */
public interface SpeedInfoShowService {

    /**
     * 极速点评分组及详情
     *
     * @return
     */
    List<SpeedGroupDetailResponse> listSpeedTargetGroupDetail(PlanTagListsRequest request);

    /**
     *  积分版列表
     */
    List<PlanListsResponse> listPlans(PlanListsRequest request);


    PlanListsResponse initialPlan(PlanInitialRequest planListsRequest);

    /**
     * 根据staffId和校区查询积分板
     * @param request
     * @return
     */
    List<PlanListsResponse> listPlansByCampusId(MessagePlanRequest request);

    /**
     * 指标中的必填textarea详情
     * @param req
     * @return
     */
    List<GetEvaluateTaskDetailVO> textareaList(@Valid IdListRequest req);
}
