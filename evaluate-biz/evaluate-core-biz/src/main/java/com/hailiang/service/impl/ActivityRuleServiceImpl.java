package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.hailiang.constant.Constant;
import com.hailiang.convert.TaskRuleConvert;
import com.hailiang.enums.*;
import com.hailiang.enums.medal.*;
import com.hailiang.exception.BizException;
import com.hailiang.manager.*;
import com.hailiang.mapper.BehaviourRecordMapper;
import com.hailiang.mapper.MedalInfoMapper;
import com.hailiang.mapper.MedalTaskRuleTargetMapper;
import com.hailiang.model.dto.JobIssueActivityMedalDTO;
import com.hailiang.model.dto.activity.detail.MedalFixDTO;
import com.hailiang.model.dto.activity.rule.save.MedalTaskRuleTargetDTO;
import com.hailiang.model.dto.activity.rule.query.*;
import com.hailiang.model.dto.activity.rule.save.*;
import com.hailiang.model.dto.activity.rule.query.ActivityTaskQuery;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.entity.*;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.medal.vo.StudentMedalContentVO;
import com.hailiang.model.vo.activity.rule.ActivityTaskRuleVO;
import com.hailiang.remote.ding.utils.DingDingMsgUtil;
import com.hailiang.remote.feishu.domain.FeiMsgReqDTO;
import com.hailiang.remote.feishu.domain.FeiShuResultDTO;
import com.hailiang.remote.feishu.utils.FeiShuMsgUtil;
import com.hailiang.remote.hai.SendMsgManager;
import com.hailiang.remote.hai.domain.dto.request.MsgContent;
import com.hailiang.remote.hai.domain.dto.request.SendMsgRequestDTO;
import com.hailiang.remote.hai.domain.dto.response.SendMsgResponseDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.pojo.educational.EduParentInfoPojo;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.student.StudentVO;
import com.hailiang.service.*;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/5 13:36
 */
@Slf4j
@Service
public class ActivityRuleServiceImpl implements ActivityRuleService {

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private BehaviourRecordMapper behaviourRecordMapper;

    @Resource
    private TargetTemplateService templateService;

    @Resource
    private MedalTaskManager medalTaskManager;

    @Resource
    private MedalTaskRuleManager medalTaskRuleManager;

    @Resource
    private MedalTaskRuleTargetManager medalTaskRuleTargetManager;

    @Resource
    private TaskRuleConvert taskRuleConvert;

    @Resource
    private TargetService targetService;

    @Resource
    private MedalActivityService medalActivityService;

    @Resource
    private TargetGroupService targetGroupService;

    @Resource
    private MedalInfoMapper medalInfoMapper;

    @Resource
    private MedalUserAcquireRecordManager medalUserAcquireRecordManager;

    @Resource
    private MedalTaskCompletionManager medalTaskCompletionManager;

    @Resource
    private MedalInfoManager medalInfoManager;

    @Resource
    private MedalActivityRuleService medalActivityRuleService;

    @Autowired
    private MedalUserAcquireRecordService medalUserAcquireRecordService;

    @Resource
    private SendMsgManager sendMsgManager;

    @Resource
    private BasicInfoService basicInfoService;

    @Resource
    private MessageLogService messageLogService;

    @Resource
    private MedalTaskRuleTargetMapper medalTaskRuleTargetMapper;

    @Resource
    private MedalRuleMatchRecordManager medalRuleMatchRecordManager;

    @Resource
    private ActivityRuleMatchService activityRuleMatchService;
    @Value("${third.ding.messageUrl}")
    private String messageUrl;
    @Value("${third.ding.titleColor}")
    private String titleColor;
    @Resource
    private FeiShuMsgUtil feiShuMsgUtil;
    @Resource
    private DingDingMsgUtil dingDingMsgUtil;


    /**
     * 保存任务
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean saveTaskRule(SaveTaskRuleDTO dto) {
        if (Objects.isNull(dto.getActivityId())) {
            return false;
        }
        if (CollUtil.isEmpty(dto.getTaskList())) {
            return false;
        }
        Assert.isTrue(dto.getTaskList().size() <= 10, () -> new BizException("最多添加十个争章任务"));

        // 奖章数量
        List<Long> medalIds = dto.getTaskList().stream().map(TaskContentDTO::getMedalInfoId).distinct().collect(Collectors.toList());
        Assert.isTrue(ObjectUtil.equals(dto.getTaskList().size(), medalIds.size()), () -> new BizException("存在相同的争章任务"));

        // 处理删除的任务
        // 本次修改的任务集合
        List<Long> taskIds = dto.getTaskList().stream().filter(s -> Objects.nonNull(s.getMedalTaskId())).map(TaskContentDTO::getMedalTaskId).distinct().collect(Collectors.toList());
        // 数据库中存在的任务
        List<MedalTask> existTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>().eq(MedalTask::getMedalActivityId, dto.getActivityId()));
        List<Long> existTaskIds = existTasks.stream().map(MedalTask::getId).distinct().collect(Collectors.toList());
        // 筛选出本次删除的任务
        existTaskIds.removeAll(taskIds);
        if (CollUtil.isNotEmpty(existTaskIds)) {
            medalTaskManager.removeBatchByIds(existTaskIds);
        }


        // 处理删除的规则
        List<TaskRuleBasicInfoDTO> detailRules = new ArrayList<>();
        for (TaskContentDTO taskContentDTO : dto.getTaskList()) {
            if (Objects.isNull(taskContentDTO.getMedalActivityId())) {
                continue;
            }
            if (Objects.isNull(taskContentDTO.getMedalTaskId())) {
                continue;
            }
            if (Objects.isNull(taskContentDTO.getRule())) {
                continue;
            }
            // 判断是否删除二级任务
            if (Objects.isNull(taskContentDTO.getRule().getId())) {
                medalTaskRuleManager.remove(new LambdaQueryWrapper<MedalTaskRule>() // NOSONAR
                        .eq(MedalTaskRule::getMedalActivityId, taskContentDTO.getMedalActivityId())
                        .eq(MedalTaskRule::getType, MedalRuleTypeEnum.COMPLETION_NUM.getCode()));
            }

            detailRules.addAll(taskContentDTO.getDetailRules());
        }
        if (CollUtil.isNotEmpty(detailRules)) {
            // 本次修改的规则集合
            List<Long> taskRuleIds = detailRules.stream().filter(s -> Objects.nonNull(s.getId())).map(TaskRuleBasicInfoDTO::getId).distinct().collect(Collectors.toList());
            // 数据库中存在的任务规则(一级任务)
            List<MedalTaskRule> existTaskRules = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>()
                    .in(MedalTaskRule::getMedalActivityId, dto.getActivityId())
                    .eq(MedalTaskRule::getLevel, Constant.ONE));
            List<Long> existTaskRuleIds = existTaskRules.stream().map(MedalTaskRule::getId).distinct().collect(Collectors.toList());
            // 筛选出本次删除的规则
            existTaskRuleIds.removeAll(taskRuleIds);
            if (CollUtil.isNotEmpty(existTaskRuleIds)) {
                medalTaskRuleManager.removeBatchByIds(existTaskRuleIds);
                // 删除填写项内容
                medalTaskRuleTargetManager.remove(new LambdaQueryWrapper<MedalTaskRuleTarget>().in(MedalTaskRuleTarget::getMedalTaskRuleId, existTaskRuleIds));
            }

        }

        // 新增/修改具体任务详情
        for (TaskContentDTO taskContentDTO : dto.getTaskList()) {
            saveSingleTaskRule(taskContentDTO);
        }

        if (Constant.TWO.equals(dto.getHandleStatus())) {
            medalActivityService.publishMedalActivity(dto.getActivityId());
        }
        return Boolean.TRUE;
    }

    /**
     * 保存单个活动任务
     *
     * @param dto 活动任务详情
     */
    @Transactional
    public Boolean saveSingleTaskRule(TaskContentDTO dto) {
        Assert.isTrue(CollUtil.isNotEmpty(dto.getDetailRules()), () -> new BizException("规则详情数据不能为空"));
        Assert.notNull(dto.getRule(), () -> new BizException("规则完成条件不能为空"));
        Assert.notNull(dto.getRule().getDetailTaskNum(), () -> new BizException("规则任务完成数不能为空"));
        Integer detailTaskNum = dto.getRule().getDetailTaskNum();
        Assert.isTrue(dto.getDetailRules().size() <= 10, () -> new BizException("最多添加10个任务"));
        Assert.isTrue(detailTaskNum <= dto.getDetailRules().size(), () -> new BizException("规则任意完成数需小于等于规则数"));

        for (TaskRuleBasicInfoDTO detailRule : dto.getDetailRules()) {
            if (Objects.isNull(detailRule.getContent())) {
                continue;
            }
            MedalTaskRuleTargetDTO medalTaskRuleTargetDTO = detailRule.getContent();
            Assert.notNull(medalTaskRuleTargetDTO.getTargetValue(), () -> new BizException("获取规则值不能为空"));
            Assert.isTrue(medalTaskRuleTargetDTO.getTargetValue().compareTo(BigDecimal.ZERO) > 0, () -> new BizException("获取规则值支持正常输入大于0的正整数,最大输入9999"));
            Assert.isTrue(medalTaskRuleTargetDTO.getTargetValue().compareTo(Convert.toBigDecimal(9999)) < 1, () -> new BizException("获取规则值支持正常输入大于0的正整数,最大输入9999"));
        }

        MedalTask medalTask = new MedalTask();
        if (Objects.nonNull(dto.getMedalTaskId())) {
            medalTask = medalTaskManager.getById(dto.getMedalTaskId());
        }

        medalTask.setMedalActivityId(dto.getMedalActivityId());
        medalTask.setMedalInfoId(dto.getMedalInfoId());
        medalTask.setCycleNum(dto.getCycleNum());
        medalTask.setCampusId(WebUtil.getCampusId());
        medalTask.setSchoolId(WebUtil.getSchoolId());
        medalTask.setTenantId(WebUtil.getTenantId());
        // 修改任务
        if (Objects.nonNull(dto.getMedalTaskId())) {
            medalTaskManager.updateById(medalTask);
        } else {
            medalTask.setStatus(Constant.NO);
            medalTaskManager.save(medalTask);
        }


        // 保存一级任务规则内容
        for (TaskRuleBasicInfoDTO detailRule : dto.getDetailRules()) {
//            Assert.notNull(detailRule.getContent(), () -> new BizException("规则content不能为空"));
//            Assert.notBlank(detailRule.getContent().getSubmitId(), () -> new BizException("submitId不能为空"));
//            Assert.notNull(detailRule.getContent().getSubmitType(), () -> new BizException("submitType不能为空"));

            MedalTaskRule medalTaskRule = new MedalTaskRule();
            medalTaskRule.setLevel(MedalRuleLevelEnum.SUB_LEVEL.getCode());
            medalTaskRule.setType(detailRule.getType());
            medalTaskRule.setMedalTaskId(medalTask.getId());
            medalTaskRule.setMedalActivityId(dto.getMedalActivityId());
            medalTaskRule.setTenantId(WebUtil.getTenantId());
            medalTaskRule.setSchoolId(WebUtil.getSchoolId());
            medalTaskRule.setCampusId(WebUtil.getCampusId());
            medalTaskRule.setContent(JSONUtil.toJsonStr(detailRule.getContent()));
            // 新增规则
            if (Objects.isNull(detailRule.getId())) {
                medalTaskRule.setStatus(Constant.NO);
                medalTaskRuleManager.save(medalTaskRule);
            } else {
                medalTaskRule.setId(detailRule.getId());
                medalTaskRuleManager.updateById(medalTaskRule);
            }

            if (Objects.isNull(detailRule.getContent())) {
                continue;
            }
            // 新增的指标详情数据
            if (CollUtil.newArrayList(MedalRuleTypeEnum.COMMENT_NUM.getCode(), MedalRuleTypeEnum.COMMENT_TOTAL.getCode()).contains(detailRule.getType()) && Objects.isNull(detailRule.getContent().getId())) {
                MedalTaskRuleTarget medalTaskTarget = new MedalTaskRuleTarget();
                BeanUtils.copyProperties(detailRule.getContent(), medalTaskTarget);
                medalTaskTarget.setTenantId(WebUtil.getTenantId());
                medalTaskTarget.setSchoolId(WebUtil.getSchoolId());
                medalTaskTarget.setCampusId(WebUtil.getCampusId());
                medalTaskTarget.setMedalTaskId(medalTask.getId());
                medalTaskTarget.setMedalTaskRuleId(medalTaskRule.getId());
                TargetLevelDTO targetLevelDTO = encapsulationLevel(medalTaskTarget.getSubmitId(), medalTaskTarget.getSubmitType(), medalTaskTarget.getTargetId());
                medalTaskTarget.setModuleCode(targetLevelDTO.getModuleCode());
                medalTaskTarget.setGroupId(targetLevelDTO.getGroupId());
                medalTaskTarget.setTargetId(targetLevelDTO.getTargetId());
                medalTaskTarget.setOptionId(targetLevelDTO.getOptionId());
                medalTaskRuleTargetManager.save(medalTaskTarget);
            }

            // 修改的指标详情数据
            if (CollUtil.newArrayList(MedalRuleTypeEnum.COMMENT_NUM.getCode(), MedalRuleTypeEnum.COMMENT_TOTAL.getCode()).contains(detailRule.getType()) && Objects.nonNull(detailRule.getContent().getId())) {
                MedalTaskRuleTarget medalTaskRuleTarget = medalTaskRuleTargetManager.getById(detailRule.getContent().getId());

                MedalTaskRuleTargetDTO newContent = detailRule.getContent();
                // 如果修改了失效的规则内容,且规则内容不一样,需要将失效状态置为未失效
                if (Objects.nonNull(newContent) && StrUtil.isNotBlank(newContent.getSubmitId()) && Objects.nonNull(newContent.getSubmitType())) {
                    if (!newContent.getSubmitId().equals(medalTaskRuleTarget.getSubmitId()) || !newContent.getSubmitType().equals(medalTaskRuleTarget.getSubmitType())) {
                        medalTaskRule.setStatus(Constant.NO);
                        medalTaskRuleManager.updateById(medalTaskRule);
                    }
                }
                // 如果本来是失效的规则,最新保存的规则为空,也需要将失效状态置为未失效
                if (Objects.isNull(newContent)) {
                    medalTaskRule.setStatus(Constant.NO);
                    medalTaskRuleManager.updateById(medalTaskRule);
                }


                BeanUtils.copyProperties(detailRule.getContent(), medalTaskRuleTarget);
                TargetLevelDTO targetLevelDTO = encapsulationLevel(medalTaskRuleTarget.getSubmitId(), medalTaskRuleTarget.getSubmitType(), medalTaskRuleTarget.getTargetId());
                if (Objects.isNull(targetLevelDTO)) {
                    medalTaskRuleTargetMapper.updateRuleTargetIsNull(medalTaskRuleTarget.getId());
                } else {
                    medalTaskRuleTarget.setModuleCode(targetLevelDTO.getModuleCode());
                    medalTaskRuleTarget.setGroupId(targetLevelDTO.getGroupId());
                    medalTaskRuleTarget.setTargetId(targetLevelDTO.getTargetId());
                    medalTaskRuleTarget.setOptionId(targetLevelDTO.getOptionId());
                    medalTaskRuleTargetManager.updateById(medalTaskRuleTarget);
                }
            }
        }
        // 保存二级任务规则内容
        MedalTaskRule activityTaskRule = new MedalTaskRule();
        activityTaskRule.setTenantId(WebUtil.getTenantId());
        activityTaskRule.setSchoolId(WebUtil.getSchoolId());
        activityTaskRule.setCampusId(WebUtil.getCampusId());
        activityTaskRule.setMedalActivityId(dto.getMedalActivityId());
        activityTaskRule.setLevel(MedalRuleLevelEnum.TOP_LEVEL.getCode());
        activityTaskRule.setMedalTaskId(medalTask.getId());
        activityTaskRule.setType(MedalRuleTypeEnum.COMPLETION_NUM.getCode());
        OuterLayerRuleContentDTO outerLayerRuleContentDTO = new OuterLayerRuleContentDTO();
        outerLayerRuleContentDTO.setType(MedalRuleTypeEnum.COMPLETION_NUM.getCode());
        outerLayerRuleContentDTO.setDetailTaskNum(dto.getRule().getDetailTaskNum());
        activityTaskRule.setContent(JSONUtil.toJsonStr(outerLayerRuleContentDTO));

        // 筛选出新增/删除的规则
        if (Objects.isNull(dto.getRule().getId())) {
            activityTaskRule.setStatus(Constant.NO);
            medalTaskRuleManager.save(activityTaskRule);
        } else {
            activityTaskRule.setId(dto.getRule().getId());
            medalTaskRuleManager.updateById(activityTaskRule);
        }

        // 如果是失效任务,判断任务下的所有规则是否失效,如果未全部失效,则修改任务状态为正常
        if (Objects.nonNull(dto.getMedalTaskId())) {
            List<MedalTaskRule> taskRuleList = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>()
                    .eq(MedalTaskRule::getMedalTaskId, dto.getMedalTaskId())
                    .eq(MedalTaskRule::getLevel, MedalRuleLevelEnum.SUB_LEVEL.getCode()));

            if (CollUtil.isEmpty(taskRuleList)) {
                return Boolean.TRUE;
            }
            // 过滤出失效的一级规则
            List<MedalTaskRule> issueRules = taskRuleList.stream().filter(s -> Constant.YES.equals(s.getStatus())).collect(Collectors.toList());
            // 如果没有全部失效
            if (taskRuleList.size() != issueRules.size()) {
                if (Objects.isNull(dto.getMedalInfoId())) {
                    return Boolean.TRUE;
                }
                // 判断奖章是否存在,如果奖章被删除了,任务也是失效
                MedalInfo medalInfo = medalInfoManager.getById(dto.getMedalInfoId());
                if (Objects.isNull(medalInfo)) {
                    return Boolean.TRUE;
                }

                MedalTask task = medalTaskManager.getById(dto.getMedalTaskId());
                task.setStatus(Constant.NO);
                medalTaskManager.updateById(task);
            }
            // 如果全部失效
            if (taskRuleList.size() == issueRules.size()) {
                MedalTask task = medalTaskManager.getById(dto.getMedalTaskId());
                task.setStatus(Constant.YES);
                medalTaskManager.updateById(task);
            }
        }

        return Boolean.TRUE;
    }

    /**
     * 根据分组指标填写项id反查上层id
     *
     * @param submitId   选择的最后一级id
     * @param submitType 选择的最后一级类型 1:五育code 2:分组id 3:指标id 4:点评项id
     * @param targetId   指标id,如果最后一级是填写项时需要传入
     * @return
     */
    @Override
    public TargetLevelDTO encapsulationLevel(String submitId, Integer submitType, Long targetId) {
        TargetLevelDTO targetLevelDTO = new TargetLevelDTO();
        if (MedalTargetTypeEnum.MODULE_CODE.getCode().equals(submitType)) {
            targetLevelDTO.setModuleCode(Convert.toInt(submitId));
            targetLevelDTO.setModuleName(ModuleEnum.getModuleName(Convert.toInt(submitId)));
        }
        if (MedalTargetTypeEnum.GROUP_ID.getCode().equals(submitType)) {
            TargetGroup targetGroup = behaviourRecordMapper.getTargetGroup(Convert.toLong(submitId));
            Assert.notNull(targetGroup, () -> new BizException("任务数据已被删除，活动已失效"));

            targetLevelDTO.setModuleCode(targetGroup.getModuleCode());
            targetLevelDTO.setModuleName(ModuleEnum.getModuleName(Convert.toInt(submitId)));
            targetLevelDTO.setGroupId(targetGroup.getId());
            targetLevelDTO.setGroupName(targetGroup.getGroupName());
        }
        if (MedalTargetTypeEnum.TARGET_ID.getCode().equals(submitType)) {
            targetLevelDTO = behaviourRecordMapper.encapsulationLevel(Convert.toLong(submitId));
            Assert.notNull(targetLevelDTO, () -> new BizException("任务数据已被删除，活动已失效"));
        }
        if (MedalTargetTypeEnum.OPTION_ID.getCode().equals(submitType) && Objects.nonNull(targetId)) {
            targetLevelDTO = behaviourRecordMapper.encapsulationLevel(Convert.toLong(targetId));
            Assert.notNull(targetLevelDTO, () -> new BizException("任务数据已被删除，活动已失效"));

            targetLevelDTO.setOptionId(submitId);
            List<RuleOptionInfoDTO> optionInfos = medalActivityRuleService.listTargetOptions(targetId);
            Map<String, String> optionNameMap = optionInfos.stream().collect(Collectors.toMap(RuleOptionInfoDTO::getOptionKey, RuleOptionInfoDTO::getOptionName));
            if (StrUtil.isNotBlank(optionNameMap.get(submitId))) {
                targetLevelDTO.setOptionName(optionNameMap.get(submitId));
            }
        }

        return targetLevelDTO;
    }

    /**
     * 查询活动任务
     *
     * @param dto
     * @return
     */
    @Override
    public List<ActivityTaskRuleVO> listTaskRule(ActivityTaskQuery dto) {
        if (Objects.isNull(dto.getActivityId())) {
            return Collections.emptyList();
        }

        List<MedalTask> activityTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>().eq(MedalTask::getMedalActivityId, dto.getActivityId()));
        if (CollUtil.isEmpty(activityTasks)) {
            return Collections.emptyList();
        }


        List<Long> taskIds = activityTasks.stream().map(MedalTask::getId).distinct().collect(Collectors.toList());
        List<MedalTaskRule> taskRules = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>()
                .in(MedalTaskRule::getMedalTaskId, taskIds));
        Map<Long, List<MedalTaskRule>> taskRuleMap = taskRules.stream().collect(Collectors.groupingBy(MedalTaskRule::getMedalTaskId));

        // 获取华为云链接前缀
        String iconUrlPrefix = targetService.getIconUrlPrefix();

        List<ActivityTaskRuleVO> list = new ArrayList<>();
        for (MedalTask activityTask : activityTasks) {
            ActivityTaskRuleVO activityTaskRuleVO = new ActivityTaskRuleVO();
            activityTaskRuleVO.setStatus(activityTask.getStatus());
            activityTaskRuleVO.setTaskId(activityTask.getId());
            activityTaskRuleVO.setMedalActivityId(activityTask.getMedalActivityId());
            activityTaskRuleVO.setMedalInfoId(activityTask.getMedalInfoId());
            activityTaskRuleVO.setCycleNum(activityTask.getCycleNum());

            MedalInfoDTO medalInfo = medalInfoMapper.getMedalInfo(activityTask.getMedalInfoId());
            if (Objects.nonNull(medalInfo)) {
                activityTaskRuleVO.setMedalInfoName(medalInfo.getName());
                activityTaskRuleVO.setMedalLogoUrl(iconUrlPrefix + medalInfo.getLogoUrl());
            }

            if (CollUtil.isEmpty(taskRuleMap.get(activityTask.getId()))) {
                continue;
            }
            List<MedalTaskRule> rules = taskRuleMap.get(activityTask.getId());
            // 一级任务
            List<MedalTaskRule> detailRules = rules.stream().filter(s -> Constant.ONE.equals(s.getLevel())).collect(Collectors.toList());
            List<TaskRuleBasicInfoVO> detailRuleList = new ArrayList<>();
            for (MedalTaskRule detailRule : detailRules) {
                TaskRuleBasicInfoVO taskRuleBasicInfoDTO = new TaskRuleBasicInfoVO();
                taskRuleBasicInfoDTO.setId(detailRule.getId());
                taskRuleBasicInfoDTO.setType(detailRule.getType());
                taskRuleBasicInfoDTO.setStatus(detailRule.getStatus());
                MedalTaskRuleTarget taskRuleTarget = medalTaskRuleTargetManager.getOne(new LambdaQueryWrapper<MedalTaskRuleTarget>().eq(MedalTaskRuleTarget::getMedalTaskRuleId, detailRule.getId())); // NOSONAR
                // 实时查询并封装最新的submitName
                encapsulationSubmitName(taskRuleTarget);

                if (Objects.nonNull(taskRuleTarget)) {
                    taskRuleBasicInfoDTO.setContent(taskRuleConvert.toMedalTaskRuleTargetVO(taskRuleTarget));
                    detailRuleList.add(taskRuleBasicInfoDTO);
                }
            }
            activityTaskRuleVO.setDetailRules(detailRuleList);

            // 二级任务
            List<MedalTaskRule> parentRules = rules.stream().filter(s -> Constant.TWO.equals(s.getLevel())).collect(Collectors.toList());
            if (CollUtil.isEmpty(parentRules)) {
                list.add(activityTaskRuleVO);
                continue;
            }
            MedalTaskRule parentRule = CollUtil.getFirst(parentRules);
            OuterLayerRuleDTO outerLayerRuleDTO = JSONUtil.toBean(parentRule.getContent(), OuterLayerRuleDTO.class);
            outerLayerRuleDTO.setId(parentRule.getId());
            activityTaskRuleVO.setRule(outerLayerRuleDTO);
            list.add(activityTaskRuleVO);
        }

        return list;
    }

    /**
     * 实时查询并封装最新的submitName
     *
     * @param taskRuleTarget 规则详情
     */
    private void encapsulationSubmitName(MedalTaskRuleTarget taskRuleTarget) {
        TargetLevelDTO targetLevelDTO = encapsulationLevel(taskRuleTarget.getSubmitId(), taskRuleTarget.getSubmitType(), taskRuleTarget.getTargetId());
        if (Objects.isNull(targetLevelDTO)) {
            return;
        }
        List<String> submitNames = new ArrayList<>();
        if (StrUtil.isNotBlank(targetLevelDTO.getModuleName())) {
            submitNames.add(targetLevelDTO.getModuleName());
        }
        if (StrUtil.isNotBlank(targetLevelDTO.getGroupName())) {
            submitNames.add(targetLevelDTO.getGroupName());
        }
        if (StrUtil.isNotBlank(targetLevelDTO.getTargetName())) {
            submitNames.add(targetLevelDTO.getTargetName());
        }
        if (StrUtil.isNotBlank(targetLevelDTO.getOptionName())) {
            submitNames.add(targetLevelDTO.getOptionName());
        }
        taskRuleTarget.setSubmitName(StrUtil.join(StrPool.SLASH, submitNames));
    }

    public List<TemplateInfoSaveDTO> getTargetOption(Long targetId) {
        TargetTemplate targetTemplate = templateService.getByTargetId(targetId);
        if (Objects.isNull(targetTemplate)) {
            return Collections.emptyList();
        }
        if (CollUtil.isEmpty(targetTemplate.getTemplateInfoList())) {
            return Collections.emptyList();
        }
        // 如果存在加分控件,返回指标
        List<String> types = targetTemplate.getTemplateInfoList().stream().filter(s -> StrUtil.isNotBlank(s.getType())).map(TemplateInfoSaveDTO::getType).collect(Collectors.toList());
        if (types.contains("score")) {
            return Collections.emptyList();
        }

        List<TemplateInfoSaveDTO> list = new ArrayList<>();
        for (TemplateInfoSaveDTO templateInfoSaveDTO : targetTemplate.getTemplateInfoList()) {
            templateInfoSaveDTO.setTargetId(targetId);
            templateInfoSaveDTO.setSubmitType(MedalTargetTypeEnum.OPTION_ID.getCode());
            // 如果是单选和多选
            if (CollUtil.newArrayList("radio", "checkbox").contains(templateInfoSaveDTO.getType())) {
                list.add(templateInfoSaveDTO);
            }
            TemplateInfoSaveDTO.InnerSubmitOptionInfoSave options = templateInfoSaveDTO.getOptions();
            if (CollUtil.isNotEmpty(options.getOptions())) {
                for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave option : options.getOptions()) {
                    option.setTargetId(Convert.toStr(targetId));
                }
            }

            // 如果是明细
            if ("card".equals(templateInfoSaveDTO.getType())) {
                // 明细列表
                List<LinkedHashMap> detailList = new ArrayList<>();
                for (LinkedHashMap linkedHashMap : templateInfoSaveDTO.getList()) {
                    if (Objects.nonNull(linkedHashMap.get("options"))) {
                        HashMap innerOption = (HashMap) linkedHashMap.get("options");
                        List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> innerOptions = JSONUtil.parseArray(innerOption.get("options")).toList(TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave.class);
                        if (CollUtil.isNotEmpty(innerOptions)) {
                            for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave option : innerOptions) {
                                option.setTargetId(Convert.toStr(targetId));
                            }
                        }
                        innerOption.put("options", innerOptions);
                    }

                    // 判断明细下的控件是否为单选和多选
                    if (CollUtil.newArrayList("radio", "checkbox").contains(Convert.toStr(linkedHashMap.get("type")))) {
                        detailList.add(linkedHashMap);
                    }
                }
                templateInfoSaveDTO.setList(detailList);
                list.add(templateInfoSaveDTO);
            }
        }
        return list;
    }

    /**
     * 获取五育明细下的所有指标
     *
     * @return
     */
    public List<RuleModuleInfoVO> listRuleModuleInfo() {
        Set<Integer> moduleCodes = ModuleEnum.normalModuleMap.keySet();

        List<RuleModuleInfoVO> list = new ArrayList<>();
        for (Integer moduleCode : moduleCodes) {
            RuleModuleInfoVO ruleModuleInfoVO = new RuleModuleInfoVO();
            ruleModuleInfoVO.setModuleCode(moduleCode);
            ruleModuleInfoVO.setModuleName(ModuleEnum.getModuleName(moduleCode));
            ruleModuleInfoVO.setSubmitType(MedalTargetTypeEnum.MODULE_CODE.getCode());
            List<RuleGroupInfoVO> groupInfos = getRuleGroupInfo(moduleCode, WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId());
            ruleModuleInfoVO.setGroupInfos(groupInfos);

            list.add(ruleModuleInfoVO);
        }


        return list;
    }

    /**
     * 根据五育code获取分组信息
     *
     * @param moduleCode
     * @param tenantId
     * @param schoolId
     * @param campusId
     * @return
     */
    private List<RuleGroupInfoVO> getRuleGroupInfo(Integer moduleCode, String tenantId, String schoolId, String campusId) {
        List<TargetGroup> targetGroups = targetGroupService.list(new LambdaQueryWrapper<TargetGroup>()
                .eq(TargetGroup::getModuleCode, moduleCode)
                .eq(TargetGroup::getTenantId, tenantId)
                .eq(TargetGroup::getSchoolId, schoolId)
                .eq(TargetGroup::getCampusId, campusId));
        if (CollUtil.isEmpty(targetGroups)) {
            return Collections.emptyList();
        }

        List<RuleGroupInfoVO> list = new ArrayList<>();
        for (TargetGroup targetGroup : targetGroups) {
            RuleGroupInfoVO ruleGroupInfoVO = new RuleGroupInfoVO();
            ruleGroupInfoVO.setGroupId(targetGroup.getId());
            ruleGroupInfoVO.setGroupName(targetGroup.getGroupName());
            ruleGroupInfoVO.setSubmitType(MedalTargetTypeEnum.GROUP_ID.getCode());
            List<RuleTargetInfoVO> targetInfos = getRuleTargetInfo(ruleGroupInfoVO);
            ruleGroupInfoVO.setTargetInfos(targetInfos);
            list.add(ruleGroupInfoVO);
        }

        return list;
    }

    private List<RuleTargetInfoVO> getRuleTargetInfo(RuleGroupInfoVO ruleGroupInfoVO) {
        List<Target> targets = targetService.list(new LambdaQueryWrapper<Target>()
                .eq(Target::getGroupId, ruleGroupInfoVO.getGroupId())
                .eq(Target::getTargetStatus, Constant.YES)
                .orderByAsc(Target::getSortIndex));
        if (CollUtil.isEmpty(targets)) {
            return Collections.emptyList();
        }
        // 获取华为云链接前缀
        String iconUrlPrefix = targetService.getIconUrlPrefix();

        List<RuleTargetInfoVO> list = new ArrayList<>();
        for (Target target : targets) {
            RuleTargetInfoVO ruleTargetInfoVO = new RuleTargetInfoVO();
            ruleTargetInfoVO.setTargetId(target.getId());
            ruleTargetInfoVO.setTargetName(target.getTargetName());
            ruleTargetInfoVO.setIconUrl(iconUrlPrefix + target.getIconUrl());
            ruleTargetInfoVO.setSubmitType(MedalTargetTypeEnum.TARGET_ID.getCode());
            List<TemplateInfoSaveDTO> targetOption = getTargetOption(target.getId());
            ruleTargetInfoVO.setTargetOptionsList(targetOption);
            list.add(ruleTargetInfoVO);
        }

        return list;
    }

    /**
     * 早上八点定时任务颁发奖章
     *
     * @param jobIssueActivityMedalDTO dto
     * @return
     */
    @Override
    public Boolean issueActivityMedal(JobIssueActivityMedalDTO jobIssueActivityMedalDTO) {
        // 颁发时间,默认当天
        Date issueDate = new Date();
        if (Objects.nonNull(jobIssueActivityMedalDTO.getIssueDate())) {
            // 自定义时间
            issueDate = jobIssueActivityMedalDTO.getIssueDate();
        }
        log.info("定时任务颁发奖章-请求参数信息:{}", jobIssueActivityMedalDTO);

        // 获取所有待颁发的记录
        List<MedalUserAcquireRecord> medalUserAcquireRecords = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .lt(MedalUserAcquireRecord::getCreateTime, DateUtil.beginOfDay(issueDate))
                .eq(StrUtil.isNotBlank(jobIssueActivityMedalDTO.getCampusId()), MedalUserAcquireRecord::getCampusId, jobIssueActivityMedalDTO.getCampusId())
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_ISSUE.getCode()));
        if (CollUtil.isEmpty(medalUserAcquireRecords)) {
            log.info("[争章活动]-[每日早上8点]-[奖章颁发]-无待颁发数据-[跳过]");
            return Boolean.TRUE;
        }

        // 更改状态,颁发奖章
        medalUserAcquireRecords.forEach(s -> {
            s.setAwardStatus(MedalAwardStatusEnum.IS_COMPLETION.getCode());
        });
        medalUserAcquireRecordManager.updateBatchById(medalUserAcquireRecords);
        log.info("定时任务颁发奖章-颁章明细状态修改成功");

        // 封装通知内容信息
        List<StudentMedalContentVO> studentMedalContents = medalUserAcquireRecordService.listStudentMedalContent(medalUserAcquireRecords);
        if (CollUtil.isEmpty(studentMedalContents)) {
            log.info("定时任务颁发奖章-获取消息内容-学生内容为空无需发送，直接返回");
            return Boolean.TRUE;
        }
        Map<Long, StudentMedalContentVO> messageContentMap = studentMedalContents.stream().collect(Collectors.toMap(StudentMedalContentVO::getId, Function.identity()));

        List<MessageLog> messageLogs = new ArrayList<>();
        log.info("执行【推送消息】--------推送家长消息----争章消息------当前要处理的获章数据条数:{},", medalUserAcquireRecords.size());
        medalUserAcquireRecords.forEach(s -> {
            String studentId = s.getStudentId();
            StudentMedalContentVO studentMedalContentVO = messageContentMap.get(s.getId());
//            log.info("执行【钉钉-飞书消息】--------推送家长消息----争章消息------当前学生:{},", studentMedalContentVO);
            //通过学生id获取家长信息
            List<EduParentInfoPojo> parentInfo = basicInfoService.getParentInfoList(Convert.toLong(studentId));
            log.info("执行【推送消息】--------推送家长消息----争章消息------当前学生studentId：{}, studentName:{}, 关联家长:{},", studentId, studentMedalContentVO.getStudentName(), JSONUtil.toJsonStr(parentInfo));
            if (CollUtil.isNotEmpty(parentInfo)) {
                for (EduParentInfoPojo parent : parentInfo) {
                    //推钉钉争章消息
                    sendDingDingIssueMsg(s, messageLogs, studentMedalContentVO, parent);
                    //推飞书争章消息
//                    sendFeiShuIssueMsg(s, messageLogs, studentMedalContentVO, parent);
                    // 推送微信消息
                    sendIssueMsg(s, messageContentMap, messageLogs, parent.getMobile());
                }
            }
        });
        messageLogService.saveMessageBatch(messageLogs);

        List<Long> completionIds = medalUserAcquireRecords.stream().map(MedalUserAcquireRecord::getCompletionId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(completionIds)) {
            List<MedalTaskCompletion> taskCompletions = medalTaskCompletionManager.list(new LambdaQueryWrapper<MedalTaskCompletion>()
                    .eq(MedalTaskCompletion::getLevel, MedalRuleLevelEnum.TOP_LEVEL.getCode())
                    .in(MedalTaskCompletion::getId, completionIds));
            if (CollUtil.isNotEmpty(taskCompletions)) {
                taskCompletions.forEach(s -> s.setStatus(MedalTaskCompleteStatusEnum.IS_FROZEN.getCode()));
                medalTaskCompletionManager.updateBatchById(taskCompletions);
                log.info("定时任务颁发奖章-颁章明细的二级任务完成状态修改成功");
            }
        }


        return Boolean.TRUE;
    }

    /**
     * 发送hai家校消息
     *
     * @param medalUserAcquireRecord
     * @param messageContentMap
     */
    private void sendIssueMsg(MedalUserAcquireRecord medalUserAcquireRecord, Map<Long, StudentMedalContentVO> messageContentMap, List<MessageLog> messageLogs, String parentMobile) {
        try {
            StudentMedalContentVO studentMedalContentVO = messageContentMap.get(medalUserAcquireRecord.getId());

            // 推家校消息
            MsgContent content = new MsgContent();
            content.setKeyword1(studentMedalContentVO.getClassName());
            content.setKeyword2(studentMedalContentVO.getAwardUserName());
            content.setKeyword3(DateUtil.formatDateTime(DateUtil.date()));
            content.setKeyword4("您的孩子获得" + studentMedalContentVO.getMedalInfoName());

            SendMsgRequestDTO request = new SendMsgRequestDTO();
            request.setContent(content);
            request.setMobile(parentMobile);
            request.setStudentCode(studentMedalContentVO.getStudentCode());
            request.setStudentName(studentMedalContentVO.getStudentName());
            request.setStudentId(medalUserAcquireRecord.getStudentId());
            SendMsgResponseDTO sendMsgResponseDTO = sendMsgManager.sendIssueActivityMedalMsg(request);

            MessageLog messageLog = new MessageLog();
            messageLog.setMobile(parentMobile);
            messageLog.setContent(JSONUtil.toJsonStr(content));
            messageLog.setBusinessId(medalUserAcquireRecord.getId());
            messageLog.setBusinessType(MessageLogBusinessTypeEnum.MEDAL_ISSUE.getCode());
            messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.MEDAL_ISSUE.getMessage());
            messageLog.setMessageId(Convert.toLong(sendMsgResponseDTO.getResponseCode()));
            messageLog.setMessageType(MessageLogMessageTypeEnum.WX_MESSAGE_REMIND.getCode());
            messageLog.setTermintalType(MessageLogTerminalTypeEnum.WECHAT.getCode());
            messageLog.setSchoolId(medalUserAcquireRecord.getSchoolId());
            messageLog.setCreateBy(Convert.toStr(medalUserAcquireRecord.getStudentId()));
            messageLog.setCampusId(medalUserAcquireRecord.getCampusId());
            messageLog.setUserId(Long.parseLong(studentMedalContentVO.getStudentId()));
            messageLog.setUserType(TaskRoleTypeEnum.PARENT.getCode());
            messageLog.setTenantId(medalUserAcquireRecord.getTenantId());
            messageLog.setRequestParam(sendMsgResponseDTO.getRequestParam());
            messageLogs.add(messageLog);
        } catch (Exception e) {
            log.error("早日八点奖章颁发-hai家校消息发送失败-获章明细信息:「{}」，错误信息：「{}」", medalUserAcquireRecord, e);
        }
    }

    /**
     * 发送钉钉争章消息
     *
     * @param medalUserAcquireRecord
     * @param
     */
    private void sendDingDingIssueMsg(MedalUserAcquireRecord medalUserAcquireRecord, List<MessageLog> messageLogs, StudentMedalContentVO studentMedalContentVO, EduParentInfoPojo parent) {
        try {
            // 推钉钉消息
            OapiMessageCorpconversationAsyncsendV2Request.OA oa = new OapiMessageCorpconversationAsyncsendV2Request.OA();
            OapiMessageCorpconversationAsyncsendV2Request.Body body = new OapiMessageCorpconversationAsyncsendV2Request.Body();
            OapiMessageCorpconversationAsyncsendV2Request.Head head = new OapiMessageCorpconversationAsyncsendV2Request.Head();
            //钉钉消息头
            oa.setHead(head);
            head.setText("综合素质评估");
            head.setBgcolor(titleColor);
            //钉钉消息内容
            oa.setBody(body);
            body.setTitle("活动奖章颁发");
            body.setContent("您的孩子获得" + studentMedalContentVO.getMedalInfoName());
            //钉钉消息链接
            oa.setMessageUrl(messageUrl + "/home/<USER>"
                    + "&tenantId=" + medalUserAcquireRecord.getTenantId()
                    + "&studentName=" + studentMedalContentVO.getStudentName()
                    + "&studentCode=" + studentMedalContentVO.getStudentCode()
                    + "&studentId=" + studentMedalContentVO.getStudentId()
                    + "&entry=parents");
            Long ddTaskId = 0L;
            try {
                //发送消息---接收消息目标手机号
//                ddTaskId = dingDingMsgUtil.sendDingOaMessageNew(oa, parent.getMobile(), medalUserAcquireRecord.getTenantId());
                ddTaskId = dingDingMsgUtil.sendDingOaMessageNewParent(oa, parent.getMobile(), medalUserAcquireRecord.getTenantId());
            } catch (Exception e) {
                log.warn("[{}-家长钉钉消息-争章消息]，sendDingDingIssueMsg发生异常", ModuleNameEnum.DINGDING.getMessage(), e);
            }
            MessageLog messageLog = new MessageLog();
            messageLog.setMobile(parent.getMobile());
            messageLog.setContent(oa.getBody().getContent());
            messageLog.setBusinessId(medalUserAcquireRecord.getId());
            messageLog.setBusinessType(MessageLogBusinessTypeEnum.MEDAL_ISSUE.getCode());
            messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.MEDAL_ISSUE.getMessage());
            messageLog.setMessageId(ddTaskId);
            messageLog.setMessageType(MessageLogMessageTypeEnum.WX_MESSAGE_REMIND.getCode());
            messageLog.setTermintalType(MessageLogTerminalTypeEnum.DINGDING.getCode());
            messageLog.setSchoolId(medalUserAcquireRecord.getSchoolId());
            messageLog.setCreateBy(Convert.toStr(medalUserAcquireRecord.getStudentId()));
            messageLog.setCampusId(medalUserAcquireRecord.getCampusId());
            messageLog.setUserId(Long.parseLong(studentMedalContentVO.getStudentId()));
            messageLog.setUserType(TaskRoleTypeEnum.PARENT.getCode());
            messageLog.setTenantId(medalUserAcquireRecord.getTenantId());
            messageLogs.add(messageLog);
        } catch (Exception e) {
            log.error("早日八点奖章颁发-钉钉消息发送失败-获章明细信息:「{}」，错误信息：「{}」", medalUserAcquireRecord, e.getMessage(), e);
        }
    }

    /**
     * 发送飞书争章消息
     *
     * @param medalUserAcquireRecord
     * @param
     */
    private void sendFeiShuIssueMsg(MedalUserAcquireRecord medalUserAcquireRecord, List<MessageLog> messageLogs, StudentMedalContentVO studentMedalContentVO, EduParentInfoPojo parent) {
        try {
            // 推飞书消息
            // 生成一个唯一回调ID，通常用于追踪消息发送状态
            String callBackId = SnowFlakeIdUtil.nextIdStr();
            FeiMsgReqDTO feiMsgReqDTO = new FeiMsgReqDTO();
            //设置飞书消息标题
            feiMsgReqDTO.setTitle("活动奖章颁发");
            //构建飞书消息URL
            feiMsgReqDTO.setMsgUrl(messageUrl + "/home/<USER>"
                    + "&tenantId=" + medalUserAcquireRecord.getTenantId()
                    + "&studentName=" + studentMedalContentVO.getStudentName()
                    + "&studentCode=" + studentMedalContentVO.getStudentCode()
                    + "&studentId=" + studentMedalContentVO.getStudentId()
                    + "&entry=parents");
            //设置飞书消息内容
            feiMsgReqDTO.setContext("您的孩子获得" + studentMedalContentVO.getMedalInfoName());
            // 设置接收者（家长）的手机号
            feiMsgReqDTO.setMobile(parent.getMobile());
            feiMsgReqDTO.setCallBackId(callBackId);
            FeiShuResultDTO feiShuResultDTO = new FeiShuResultDTO();
            try {
                // 尝试发送飞书消息并获取结果
                feiShuResultDTO = feiShuMsgUtil.sendTextMessage(feiMsgReqDTO);
            } catch (Exception e) {
                log.warn("[{}-家长飞书消息-争章消息]，sendFeiShuIssueMsg发生异常", ModuleNameEnum.FEISHU.getMessage(), e);
            }
            MessageLog messageLog = new MessageLog();
            messageLog.setMobile(parent.getMobile());
            messageLog.setContent(feiShuResultDTO.getContext());
            messageLog.setBusinessId(medalUserAcquireRecord.getId());
            messageLog.setBusinessType(MessageLogBusinessTypeEnum.MEDAL_ISSUE.getCode());
            messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.MEDAL_ISSUE.getMessage());
            messageLog.setMessageId(Convert.toLong(callBackId));
            messageLog.setMessageType(MessageLogMessageTypeEnum.WX_MESSAGE_REMIND.getCode());
            messageLog.setTermintalType(MessageLogTerminalTypeEnum.FEISHU.getCode());
            messageLog.setSchoolId(medalUserAcquireRecord.getSchoolId());
            messageLog.setCreateBy(Convert.toStr(medalUserAcquireRecord.getStudentId()));
            messageLog.setCampusId(medalUserAcquireRecord.getCampusId());
            messageLog.setUserId(Long.parseLong(studentMedalContentVO.getStudentId()));
            messageLog.setUserType(TaskRoleTypeEnum.PARENT.getCode());
            messageLog.setTenantId(medalUserAcquireRecord.getTenantId());
            messageLogs.add(messageLog);
        } catch (Exception e) {
            log.error("早日八点奖章颁发-飞书消息发送失败-获章明细信息:「{}」，错误信息：「{}」", medalUserAcquireRecord, e.getMessage(), e);
        }
    }

    /**
     * 修复颁章明细相关数据(新增二级任务详情表数据,新增颁章明细操作记录表数据,颁章明细completion_id字段为空数据)
     *
     * @param medalFixDTO 数据库主键id集合
     * @return
     */
//    @Override
//    public void fixMedalUserAcquireData(MedalFixDTO medalFixDTO) {
//        log.info("修复颁章明细相关数据-[当前环境:{}]", env);
//        List<MedalRuleMatchRecord> medalRuleMatchRecordList = new ArrayList<>();
//
//        if (CollUtil.newArrayList("dev", "test").contains(env)) {
//            if (CollUtil.isEmpty(medalFixDTO.getIds())) {
//                log.info("修复颁章明细相关数据-[参数id为空]-[不做任何处理]");
//                return;
//            }
//            // 开发测试环境id必须得传
//            medalRuleMatchRecordList = medalRuleMatchRecordManager.list(new LambdaQueryWrapper<MedalRuleMatchRecord>()
//                    .in(CollUtil.isNotEmpty(medalFixDTO.getIds()), MedalRuleMatchRecord::getId, medalFixDTO.getIds()));
//            log.info("修复颁章明细相关数据-获取测试-[medal_rule_match_record]表中的数据信息:{}", medalRuleMatchRecordList);
//        } else {
//            // 线上只能获取二期上线以前的数据,id不传获取全部数据
//            medalRuleMatchRecordList = medalRuleMatchRecordManager.list(new LambdaQueryWrapper<MedalRuleMatchRecord>()
//                    .in(CollUtil.isNotEmpty(medalFixDTO.getIds()), MedalRuleMatchRecord::getId, medalFixDTO.getIds())
//                    .lt(MedalRuleMatchRecord::getCreateTime, DateUtil.parseDateTime("2023-07-10 18:00:00")));
//            log.info("修复颁章明细相关数据-获取线上-[medal_rule_match_record]表中的数据信息:{}", medalRuleMatchRecordList);
//        }
//        if (CollUtil.isEmpty(medalRuleMatchRecordList)) {
//            log.info("修复颁章明细相关数据-[获取数据为空]-[不做任何处理]");
//            return;
//        }
//        List<Long> behaviourIds = medalRuleMatchRecordList.stream().map(MedalRuleMatchRecord::getBehaviourRecordId).filter(Objects::nonNull).collect(Collectors.toList());
//        if (CollUtil.isEmpty(behaviourIds)) {
//            log.info("修复颁章明细相关数据-[行为id为空]-[不做任何处理]");
//            return;
//        }
//        activityRuleMatchService.matchProcessFix(CollUtil.newArrayList(behaviourIds));
//    }

//    /**
//     * 修复颁章明细班级id,名称错误数据
//     *
//     * @param medalFixDTO 数据库主键id集合
//     * @return
//     */
//    @Override
//    public void fixMedalUserAcquireClassIdData(MedalFixDTO medalFixDTO) {
//        log.info("修复颁章明细相关数据-[当前环境:{}]", env);
//        List<MedalUserAcquireRecord> medalUserAcquireRecordList = new ArrayList<>();
//        if (CollUtil.newArrayList("dev", "test").contains(env)) {
//            if (CollUtil.isEmpty(medalFixDTO.getIds())) {
//                log.info("修复颁章明细b班级id,名称错误数据-[参数id为空]-[不做任何处理]");
//                return;
//            }
//            // 开发测试环境id必须得传
//            medalUserAcquireRecordList = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
//                    .in(CollUtil.isNotEmpty(medalFixDTO.getIds()), MedalUserAcquireRecord::getId, medalFixDTO.getIds()));
//            log.info("修复颁章明细b班级id,名称错误数据-获取测试-[medal_user_acquire_record]表中的数据信息:{}", medalUserAcquireRecordList);
//        } else {
//            // 线上只能获取二期上线以前的数据,id不传获取全部数据
//            medalUserAcquireRecordList = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
//                    .in(CollUtil.isNotEmpty(medalFixDTO.getIds()), MedalUserAcquireRecord::getId, medalFixDTO.getIds())
//                    .lt(MedalUserAcquireRecord::getCreateTime, DateUtil.parseDateTime("2023-07-10 18:00:00")));
//            log.info("修复颁章明细b班级id,名称错误数据-获取线上-[medal_user_acquire_record]表中的数据信息:{}", medalUserAcquireRecordList);
//        }
//        if (CollUtil.isEmpty(medalUserAcquireRecordList)) {
//            log.info("修复颁章明细b班级id,名称错误数据-[获取数据为空]-[不做任何处理]");
//            return;
//        }
//        for (MedalUserAcquireRecord medalUserAcquireRecord : medalUserAcquireRecordList) {
//            StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(medalUserAcquireRecord.getSchoolId()), Convert.toLong(medalUserAcquireRecord.getStudentId()));
//            medalUserAcquireRecord.setClassId(Convert.toStr(studentVO.getClassId()));
//            medalUserAcquireRecord.setClassName(studentVO.getClassName());
//            medalUserAcquireRecordManager.updateById(medalUserAcquireRecord);
//        }
//
//    }

}
