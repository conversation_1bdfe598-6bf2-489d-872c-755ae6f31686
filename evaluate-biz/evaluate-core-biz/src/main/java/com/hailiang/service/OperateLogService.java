package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.save.OperateLogSaveDTO;
import com.hailiang.model.entity.OperateLog;

import java.util.Date;
import java.util.List;


/**
 * 接口
 *
 * <AUTHOR> 2022-03-21 10:50:57
 */
public interface OperateLogService extends IService<OperateLog>{

    boolean saveOperateLog(OperateLogSaveDTO operateLogSaveDTO);

    List<OperateLog> listOperateLogSpeed(List<String> staffIds, Integer operateType, Date startTime, Date endTime);

    List<OperateLog> listOperateLogPic(List<String> staffIds, Integer operateType, Date startTime, Date endTime);
}