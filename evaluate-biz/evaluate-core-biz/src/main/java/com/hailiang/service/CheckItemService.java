package com.hailiang.service;

import com.hailiang.model.dto.SaveCheckItemDTO;
import com.hailiang.model.dto.UpdateCheckItemDTO;
import com.hailiang.model.entity.CheckItemPO;
import com.hailiang.model.ext.CheckItemExt;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/20 11:25
 */
public interface CheckItemService {

    void throwAnExceptionIfItExists(String groupId);

    List<CheckItemPO> queryOrderedItemsForGroups(List<Long> groupIds);

    /**
     * 转换并保存检查项
     * @param dto
     * @param templateId
     * @return
     */
    Long convertAndCreateItem(SaveCheckItemDTO dto, String templateId);

    Long convertAndCreateSysItem(SaveCheckItemDTO dto, String templateId);


    /**
     * 更新检查项
     * @param item
     * @param dto
     * @param templateId
     */
    void updateItem(CheckItemPO item, UpdateCheckItemDTO dto, String templateId);

    void verifyItemExists(Long id);

    CheckItemExt parseExt(String ext);

    Map<Long, String> queryCheckItemIdNameMap(String campusId, String sourceType);

    List<CheckItemPO> queryCheckItemList(String campusId, String sourceType);
}
