package com.hailiang.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.common.lock.annotation.RLock;
import com.hailiang.constant.Constant;
import com.hailiang.constant.TargetConstant;
import com.hailiang.convert.TargetConvert;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.SaaSUserTypeEnum;
import com.hailiang.enums.SaasUserTypeQueryEnum;
import com.hailiang.enums.ScoreTypeEnum;
import com.hailiang.enums.SubmitRateEnum;
import com.hailiang.enums.TargetReviewTypeEnum;
import com.hailiang.enums.TargetTypeEnum;
import com.hailiang.enums.TargetUserSubmitTypeEnum;
import com.hailiang.enums.TaskRoleTypeEnum;
import com.hailiang.enums.target.TargetBatchOperateExistDealType;
import com.hailiang.enums.target.TargetBatchSaveColumnEnum;
import com.hailiang.enums.target.TargetTemplateInfoTypeEnum;
import com.hailiang.event.center.spring.SpringEventCenter;
import com.hailiang.exception.BizException;
import com.hailiang.logic.TargetCopyLogic;
import com.hailiang.manager.EvaluateTargetReviewTeacherManager;
import com.hailiang.manager.SubjectInfoManager;
import com.hailiang.manager.TargetGroupManager;
import com.hailiang.manager.TargetUserManager;
import com.hailiang.mapper.TargetGroupMapper;
import com.hailiang.mapper.TargetMapper;
import com.hailiang.mapper.mongo.TargetTemplateDao;
import com.hailiang.model.dto.TargetCopyDTO;
import com.hailiang.model.dto.query.BasicInfoListAllUserDTO;
import com.hailiang.model.dto.query.ListTargetWritePeopleDaoDTO;
import com.hailiang.model.dto.query.TargetGroupListByConditionsDaoDTO;
import com.hailiang.model.dto.query.TargetIdQuery;
import com.hailiang.model.dto.response.SubjectCommonResponse;
import com.hailiang.model.dto.save.TargetSaveDTO;
import com.hailiang.model.dto.save.TargetSaveDTO.InnerEvaluateTargetUser;
import com.hailiang.model.dto.save.TargetSaveDTO.InnerTargetReviewTeacher;
import com.hailiang.model.dto.save.TargetSortDTO;
import com.hailiang.model.dto.save.TargetSortDTO.InnerSort;
import com.hailiang.model.dto.save.TargetTemplateSaveDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.dto.target.TargetBatchOperateQueryDTO;
import com.hailiang.model.dto.target.TargetBatchOperateSaveDTO;
import com.hailiang.model.dto.target.TargetBatchOperateSaveRecordDTO;
import com.hailiang.model.entity.EvaluateTargetReviewTeacherPO;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TargetGroup;
import com.hailiang.model.entity.TargetUserPO;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.event.target.BaseTargetEvent;
import com.hailiang.model.event.target.TargetDeletedEvent;
import com.hailiang.model.event.target.TargetUpdateEvent;
import com.hailiang.model.event.target.TargetUpdateStatusEvent;
import com.hailiang.model.event.targetGroup.TargetGroupDeletedEvent;
import com.hailiang.model.qo.target.TargetQO;
import com.hailiang.model.report.entity.SubjectInfoPO;
import com.hailiang.model.request.StringRequest;
import com.hailiang.model.request.SubjectLikeRequest;
import com.hailiang.model.response.TargetReviewTeacherResponse;
import com.hailiang.model.response.target.batch.TargetBatchSaveFailRecordResponse;
import com.hailiang.model.response.target.batch.TargetBatchSaveResponse;
import com.hailiang.model.vo.BasicInfoListAllUserVO;
import com.hailiang.model.vo.SportGroupTargetVO;
import com.hailiang.model.vo.TargetDetailVO;
import com.hailiang.model.vo.TargetDetailVO.InnerReviewTeacherInfo;
import com.hailiang.model.vo.TargetDetailVO.InnerSubmitObj;
import com.hailiang.model.vo.TargetDetailVO.InnerTargetUser;
import com.hailiang.model.vo.TargetListTargetUserVO;
import com.hailiang.model.vo.TargetTargetUserInnerTargetUserVO;
import com.hailiang.model.vo.TargetUserQueryVO;
import com.hailiang.model.vo.TargetWriteUserVO;
import com.hailiang.model.vo.target.TargetBatchOperateVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.auth.MenuAuthCheckDTO;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.dto.org.TcOrgQueryDTO;
import com.hailiang.remote.saas.dto.school.SchoolQueryDTO;
import com.hailiang.remote.saas.dto.staff.SchoolStaffQueryDTO;
import com.hailiang.remote.saas.dto.staff.StaffInfoQueryDTO;
import com.hailiang.remote.saas.enums.OrgTypeEnum;
import com.hailiang.remote.saas.vo.administration.AdminOrgTreeVO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.role.ResRoleBffVO;
import com.hailiang.remote.saas.vo.staff.StaffBatchVO;
import com.hailiang.remote.saas.vo.user.UserInfoVO;
import com.hailiang.saas.SaasSubjectManager;
import com.hailiang.saas.model.dto.subject.SaasSubjectDTO;
import com.hailiang.saas.model.vo.subject.SassSubjectVO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.MedalActivityRuleService;
import com.hailiang.service.TargetGroupService;
import com.hailiang.service.TargetService;
import com.hailiang.service.TargetTemplateService;
import com.hailiang.util.AssertUtil;
import com.hailiang.util.HolidayUtil;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * @description 针对表【evaluate_target(指标表)】的数据库操作Service实现
 * @createDate 2022-12-29 11:08:23
 */
@Service
@RefreshScope
@Slf4j
public class TargetServiceImpl extends ServiceImpl<TargetMapper, Target>
        implements TargetService {

    private final String separator = ",";
    @Autowired
    private TargetUserManager targetUserManager;
    @Autowired
    private TargetGroupService targetGroupService;
    @Autowired
    private TargetConvert convert;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Autowired
    private TargetTemplateService templateService;
    @Autowired
    private TargetMapper targetMapper;
    @Autowired
    private TargetGroupMapper targetGroupMapper;
    @Autowired
    private BasicInfoService basicInfoService;
    @Resource
    private TargetTemplateDao templateDao;
    @Autowired
    private HolidayUtil holidayUtil;
    @Autowired
    private TargetCopyLogic targetCopyLogic;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private SpringEventCenter eventCenter;
    @Autowired
    private MedalActivityRuleService medalActivityRuleService;
    @Resource
    private SubjectInfoManager subjectInfoManager;
    @Resource
    private EvaluateTargetReviewTeacherManager evaluateTargetReviewTeacherManager;
    @Resource
    private TargetGroupManager targetGroupManager;
    @Resource
    private SaasSubjectManager saasSubjectManager;

    @Value("${evaluate.target.icon.http:https}")
    private String targetIconHttp;
    @Value("${evaluate.target.icon.count}")
    private Integer targetIconCount;
    @Value("${evaluate.target.icon.prefix}")
    private String targetIconPrefix;
    @Value("${evaluate.target.icon.suffix}")
    private String targetIconSuffix;
    @Value("${evaluate.target.icon.path}")
    private String targetIconPath;
    @Value("${evaluate.target.icon.default}")
    private String defaultIcon;
    @Value("${oss.config.bucketName}")
    private String bucketName;
    @Value("${oss.config.endPoint}")
    private String endPoint;
    //指标批量导入按钮的接口code
    @Value("${button.code.target.batchImport}")
    private String batchImportButtonCode;
    /**
     * saas组织机构、教职工头像华为云链接前缀
     */
    @Value("${open.saasIconPrefix}")
    private String saasIconPrefix;


    /**
     * 添加指标
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(TargetSaveDTO dto) {
        this.preHandle(dto);
        // 如果指标提交不限定频率，不限制家长提交次数
        if (SubmitRateEnum.NO_RATE.getSubmitType().equals(dto.getSubmitType())) {
            dto.setParentSubmitLimitFlag(Constant.NO);
        }
        if (SubmitRateEnum.TERM.getSubmitType().equals(dto.getSubmitType())){
            dto.setParentSubmitLimitFlag(Constant.ONE);
            dto.setParentSubmitLimitTimes(Constant.ONE);
        }

        if (ObjectUtil.isNull(dto.getId())) {
            add(dto);
            return;
        }
        update(dto);
    }

    /**
     * 配置了任教则默认勾选教师角色
     * @param targetSaveDTO
     */
    private void preHandle(TargetSaveDTO targetSaveDTO) {
        List<InnerEvaluateTargetUser> targetUserList = targetSaveDTO.getTargetUserList();
        InnerEvaluateTargetUser innerEvaluateTargetUser = targetUserList.stream()
                .filter(item -> Objects.equals(item.getSubmitType(), 10)).findFirst().orElse(null);

        if (Objects.isNull(innerEvaluateTargetUser)) {
            return;
        }
        TcOrgQueryDTO tcOrgQueryDTO = new TcOrgQueryDTO();
        tcOrgQueryDTO.setType(2);
        tcOrgQueryDTO.setId(WebUtil.getSchoolIdLong());

        List<ResRoleBffVO> resRoleBffVOS = basicInfoRemote.queryOrgRoleList(tcOrgQueryDTO);

        List<InnerEvaluateTargetUser> innerEvaluateTargetUsers = targetUserList.stream()
                .filter(item -> Objects.equals(item.getSubmitType(), 2)).collect(Collectors.toList());
        ResRoleBffVO xs1006 = resRoleBffVOS.stream().filter(item -> Objects.equals(item.getRoleCode(), "xs1006"))
                .findFirst().orElse(null);
        if (Objects.isNull(xs1006)) {
            return;
        }
        InnerEvaluateTargetUser evaluateTargetUser = innerEvaluateTargetUsers.stream()
                .filter(item -> Objects.equals(item.getSubmitValue(), xs1006.getRoleId())).findFirst().orElse(null);
        if(Objects.nonNull(evaluateTargetUser)){
            return;
        }
        InnerEvaluateTargetUser innerEvaluateTargetUser1 = new InnerEvaluateTargetUser();
        innerEvaluateTargetUser1.setSubmitType(2);
        innerEvaluateTargetUser1.setSubmitValue(xs1006.getRoleId());
        innerEvaluateTargetUser1.setSubmitUserName(xs1006.getRoleName());
        innerEvaluateTargetUser1.setUserCount(xs1006.getStaffNum());
        targetUserList.add(innerEvaluateTargetUser1);
    }

    /**
     * 添加指标
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(TargetSaveDTO dto) {
        // 判断指标id校区是否与当前校区是否一致
        Target targetCampus = this.getById(dto.getId());
        if (Objects.nonNull(targetCampus)) {
            log.info("判断指标id校区是否与当前校区是否一致,当前校区:[{}]指标信息:{}", WebUtil.getCampusId(),
                    targetCampus);
            Assert.isTrue(targetCampus.getCampusId().equals(WebUtil.getCampusId()),
                    () -> new BizException("已选择的可填写此指标人员不属于指标所属校区"));
        }
        //分组校验
        Long groupId = dto.getGroupId();
        TargetGroup targetGroup = targetGroupManager.getById(groupId);
        Assert.notNull(targetGroup, "分组不存在！");
        Assert.isTrue(TargetTypeEnum.DEFAULT.getCode().equals(targetGroup.getGroupType()),
                "不可在系统默认分组下新建指标！");

        // 去除iconUrl华为云链接前缀
        String iconUrl = dto.getIconUrl().substring(getIconUrlPrefix().length());
        dto.setIconUrl(iconUrl);
        Target target = convert.toEvaluateTarget(dto);

        // 不限定频次时，可不输入提醒时间、法定节假日、寒暑假是否通知、是否每次必须提交
        checkParams(dto);

        // 插入表单到MongoDB
        TargetTemplateSaveDTO templateSaveDTO = new TargetTemplateSaveDTO()
                .setTemplateInfoList(dto.getTemplateInfoList());
        TargetTemplate template = templateService.save(templateSaveDTO);

        // 查当前学校当前分组下有多少指标
        int count = (int) this.count(new LambdaQueryWrapper<Target>()
                .eq(Target::getGroupId, dto.getGroupId()));
        target.setSortIndex(count + 1);
        target.setTargetStatus(Constant.YES);
        target.setSchoolId(WebUtil.getSchoolId());
        target.setCampusId(WebUtil.getCampusId());
        target.setTenantId(WebUtil.getTenantId());
        target.setTargetNotPartCount(dto.getTargetNotPartCount());
        target.setNotPartExchange(dto.getNotPartExchange());

        target.setTemplateId(template.getId());
        target.setTemplateInfoJson(JSONObject.toJSONString(template));
        // 加分控件
        TargetSaveDTO.InnerScoreControl scoreControl = dto.getScoreControl();
        if (ObjectUtil.isNotNull(scoreControl)) {
            // 设置加分控件信息
            target.setScoreControlName(scoreControl.getScoreControlName());
            target.setScoreControlType(scoreControl.getScoreControlType());
            target.setScore(scoreControl.getScore());
            BigDecimal score = scoreControl.getScore();
            BigDecimal scoreValue = ObjectUtil.isNull(score) ? null : score.abs();
            target.setScoreValue(scoreValue);
            if (ScoreTypeEnum.REDUCE.getCode().equals(scoreControl.getScoreControlType()) && ObjectUtil.isNotNull(
                    scoreControl.getScore())) {
                target.setScore(scoreControl.getScore().abs().negate());
            }

        }

        // 判断是否扣分延续指标
        this.scoreContinuationFlag(target, dto);

        // 保存指标
        this.saveOrUpdate(target);
        // 将指标id保存到MongoDB（和表单绑定）
        TargetTemplateSaveDTO templateSave = new TargetTemplateSaveDTO()
                .setTemplateId(template.getId())
                .setTargetId(target.getId());
        templateService.save(templateSave);

        List<TargetUserPO> taskUserList = getTaskUserList(target, dto.getTargetUserList());
        // 批量保存可填写指标人
        targetUserManager.saveBatch(taskUserList);

        // 构建审核人PO(家长)
        List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS = this.buildReviewTeacherPO(dto, target);
        // 构建审核人PO(学生)
        List<EvaluateTargetReviewTeacherPO> studentReviewTeacherPOS = this.buildStudentReviewTeacherPO(dto, target);
        reviewTeacherPOS.addAll(studentReviewTeacherPOS);
        // 批量保存审核人
        if (CollUtil.isNotEmpty(reviewTeacherPOS)) {
            evaluateTargetReviewTeacherManager.saveBatch(reviewTeacherPOS);
        }

    }

    /**
     * 判断是否扣分延续指标
     *
     * @param target
     * @param dto
     */
    private void scoreContinuationFlag(Target target, TargetSaveDTO dto) {
        List<TemplateInfoSaveDTO> templateInfoList = dto.getTemplateInfoList();
        if (CollUtil.isEmpty(templateInfoList)){
            return;
        }
        // 判断普通控件
        TemplateInfoSaveDTO templateInfoSaveDTO = templateInfoList.stream()
                .filter(s -> Boolean.TRUE.equals(s.getIsContinuousRecording())).distinct().findFirst().orElse(null);
        // 判断明细
        List<LinkedHashMap> detailList = templateInfoList.stream()
                .filter(s -> "card".equals(s.getType()) && CollUtil.isNotEmpty(s.getList())).map(
                        TemplateInfoSaveDTO::getList).flatMap(Collection::stream).collect(Collectors.toList());
        boolean detailFlag = false;
        if (CollUtil.isNotEmpty(detailList)){
            for (LinkedHashMap linkedHashMap : detailList) {
                if (Objects.isNull(linkedHashMap.get("isContinuousRecording"))){
                    continue;
                }
                boolean isContinuousRecording = (boolean)linkedHashMap.get("isContinuousRecording");
                if (isContinuousRecording){
                    detailFlag = true;
                    break;
                }
            }
        }
        // 控件和明细都没有勾选扣分延续
        if (Objects.isNull(templateInfoSaveDTO)&& Boolean.FALSE.equals(detailFlag)){
            return;
        }
        if (Objects.nonNull(templateInfoSaveDTO) && Boolean.TRUE.equals(
                templateInfoSaveDTO.getIsContinuousRecording())) {
            target.setScoreContinuationFlag(Constant.YES);
        }
        if (detailFlag){
            target.setScoreContinuationFlag(Constant.YES);
        }

    }

    /**
     * 构建审核人PO(家长)
     */
    private List<EvaluateTargetReviewTeacherPO> buildReviewTeacherPO(TargetSaveDTO dto, Target target) {
        if(Constant.NO.equals(target.getParentSubmitReviewFlag())){
            log.warn("【修改指标】,【家长提交无需审核】,target:{}", JSONUtil.toJsonStr(target));
            return new ArrayList<>();
        }
        // 空值防御校验
        if (ObjectUtil.isNull(dto) || ObjectUtil.isNull(target)) {
            log.warn("【指标保存更新】【构建审核人PO】参数不可为空 dto:{} target:{}", JSONUtil.toJsonStr(dto),
                    JSONUtil.toJsonStr(target));
            return new ArrayList<>();
        }

        List<EvaluateTargetReviewTeacherPO> reviewTeacherResult = new ArrayList<>();
        // 初始化基础参数
        Integer reviewType = dto.getReviewType();
        List<InnerTargetReviewTeacher> innerReviewTeachers = dto.getReviewTeachers();
        InnerTargetReviewTeacher classTeacher = dto.getClassTeacher();

        // 空集合提前返回
        if (isInvalidInput(reviewType, innerReviewTeachers, classTeacher)) {
            log.warn("【指标保存更新】【构建审核人PO】指标审核人列表为空，无需保存，dto:{}", JSONUtil.toJsonStr(dto));
            return new ArrayList<>();
        }

        // 核心构建逻辑
        List<EvaluateTargetReviewTeacherPO> reviewTeacherInfos = TargetReviewTypeEnum.APPOINTED_TEACHER.getCode().equals(reviewType)
                ? this.buildAppointedTeachers(target, innerReviewTeachers, reviewType)
                : this.buildClassTeacher(target, classTeacher, reviewType);
        if (CollUtil.isNotEmpty(reviewTeacherInfos)){
            reviewTeacherInfos.forEach(s->s.setBusinessType(1));
            reviewTeacherResult.addAll(reviewTeacherInfos);
        }
        return reviewTeacherResult;
    }

    /**
     * 构建审核人PO(学生)
     */
    private List<EvaluateTargetReviewTeacherPO> buildStudentReviewTeacherPO(TargetSaveDTO dto, Target target) {
        if(Constant.NO.equals(target.getStudentSubmitReviewFlag())){
            log.warn("【修改指标】,【学生提交无需审核】,target:{}", JSONUtil.toJsonStr(target));
            return new ArrayList<>();
        }
        // 空值防御校验
        if (ObjectUtil.isNull(dto) || ObjectUtil.isNull(target)) {
            log.warn("【指标保存更新】【构建审核人PO】参数不可为空 dto:{} target:{}", JSONUtil.toJsonStr(dto),
                    JSONUtil.toJsonStr(target));
            return new ArrayList<>();
        }

        List<EvaluateTargetReviewTeacherPO> reviewTeacherResult = new ArrayList<>();
        // 初始化基础参数
        Integer reviewType = dto.getStudentReviewType();
        List<InnerTargetReviewTeacher> innerReviewTeachers = dto.getStudentReviewTeachers();
        InnerTargetReviewTeacher classTeacher = dto.getStudentClassTeacher();

        // 空集合提前返回
        if (isInvalidInput(reviewType, innerReviewTeachers, classTeacher)) {
            log.warn("【指标保存更新】【构建审核人PO】指标审核人列表为空，无需保存，dto:{}", JSONUtil.toJsonStr(dto));
            return new ArrayList<>();
        }

        // 核心构建逻辑
        List<EvaluateTargetReviewTeacherPO> reviewTeacherInfos = TargetReviewTypeEnum.APPOINTED_TEACHER.getCode().equals(reviewType)
                ? this.buildAppointedTeachers(target, innerReviewTeachers, reviewType)
                : this.buildClassTeacher(target, classTeacher, reviewType);
        if (CollUtil.isNotEmpty(reviewTeacherInfos)){
            reviewTeacherInfos.forEach(s->s.setBusinessType(2));
            reviewTeacherResult.addAll(reviewTeacherInfos);
        }
        return reviewTeacherResult;
    }


    /**
     * 参数有效性校验
     */
    private boolean isInvalidInput(Integer reviewType,
                                   List<InnerTargetReviewTeacher> teachers,
                                   InnerTargetReviewTeacher classTeacher) {
        return (TargetReviewTypeEnum.APPOINTED_TEACHER.getCode().equals(reviewType)
                && CollUtil.isEmpty(teachers))
                || (!TargetReviewTypeEnum.APPOINTED_TEACHER.getCode().equals(reviewType)
                && ObjectUtil.isNull(classTeacher));
    }

    /**
     * 构建指定老师是审核人列表
     */
    private List<EvaluateTargetReviewTeacherPO> buildAppointedTeachers(Target target,
                                                                       List<InnerTargetReviewTeacher> teachers,
                                                                       Integer reviewType) {
        return teachers.stream()
                .filter(teacher -> !BeanUtil.hasNullField(teacher))
                .map(teacher -> this.buildReviewTeacherPO(target, teacher, reviewType))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 构建审核人是班主任或者班级内的学科老师
     */
    private List<EvaluateTargetReviewTeacherPO> buildClassTeacher(Target target,
                                                                  InnerTargetReviewTeacher classTeacher,
                                                                  Integer reviewType) {

        EvaluateTargetReviewTeacherPO po = this.buildReviewTeacherPO(target, classTeacher, reviewType);
        return po != null ? Collections.singletonList(po) : Collections.emptyList();
    }

    /**
     * 统一PO构建方法
     */
    private EvaluateTargetReviewTeacherPO buildReviewTeacherPO(Target target,
                                                               InnerTargetReviewTeacher teacher,
                                                               Integer reviewType) {
        if (ObjectUtil.isNull(teacher) || ObjectUtil.isNull(target)) {
            log.warn("【指标保存更新】【构建审核人PO】统一PO构建方法入参为空，target:{}, teacher:{}",
                    JSONUtil.toJsonStr(target), JSONUtil.toJsonStr(teacher));
            return null;
        }

        EvaluateTargetReviewTeacherPO po = new EvaluateTargetReviewTeacherPO();
        po.setBusinessId(teacher.getBusinessId());
        po.setBusinessName(teacher.getBusinessName());
        po.setReviewType(reviewType);
        po.setSubmitType(teacher.getSubmitType());
        this.setCommonFields(po, target);  // 公共字段设置
        return po;
    }

    /**
     * 公共字段设置
     */
    private void setCommonFields(EvaluateTargetReviewTeacherPO po, Target target) {
        po.setTenantId(WebUtil.getTenantIdLong());
        po.setSchoolId(WebUtil.getSchoolIdLong());
        po.setCampusId(WebUtil.getCampusIdLong());
        po.setTargetId(target.getId());
    }


    private void checkParams(TargetSaveDTO dto) {
        Assert.isTrue(dto.getTargetName().length() <= 10, "指标名称不能超过10个字符");
        // 不限定频次时，可不输入提醒时间、法定节假日、寒暑假是否通知、是否每次必须提交
        if (SubmitRateEnum.isRemind(dto.getSubmitType())) {
            Assert.isTrue(StrUtil.isNotBlank(dto.getRemindTime()), "提醒时间不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(dto.getHolidayNoticeFlag()), "法定节假日、寒暑假通知flag不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(dto.getMustSubmitFlag()), "是否每次必须提交flag不能为空");
        }
    }

    /**
     * 修改指标
     *
     * @param dto
     */
    public void update(TargetSaveDTO dto) {
        // 判断指标id校区是否与当前校区是否一致
        Target targetCampus = this.getById(dto.getId());

        if (targetCampus == null) {
            throw new BizException("该指标已被删除，请退回列表重新操作");
        }

        if (Objects.nonNull(targetCampus)) {
            log.info("判断指标id校区是否与当前校区是否一致,当前校区:[{}]指标信息:{}", WebUtil.getCampusId(),
                    targetCampus);
            Assert.isTrue(targetCampus.getCampusId().equals(WebUtil.getCampusId()),
                    () -> new BizException("已选择的可填写此指标人员不属于指标所属校区"));
        }

        // 去除iconUrl华为云链接前缀
        String iconUrl = dto.getIconUrl().substring(getIconUrlPrefix().length());
        dto.setIconUrl(iconUrl);

        Target target = convert.toEvaluateTarget(dto);

        // 不限定频次时，可不输入提醒时间、法定节假日、寒暑假是否通知、是否每次必须提交
        checkParams(dto);

        // 修改指标时判断表单是否发送变化
        boolean theSameTemplate = isTheSameTemplate(dto.getId(), dto.getTemplateInfoList());
        //系统推荐指标校验
        if (TargetTypeEnum.SYSTEM_RECOMMEND.getCode().equals(targetCampus.getTargetType())) {
            String oldTargetName = targetCampus.getTargetName();
            String newTargetName = dto.getTargetName();
            Assert.isTrue(oldTargetName.equals(newTargetName), "系统推荐指标名称不可修改！");

            Long oldGroupId = targetCampus.getGroupId();
            Long newGroupId = dto.getGroupId();
            Assert.isTrue(oldGroupId.equals(newGroupId), "系统推荐指标分组不可修改！");
        } else if (TargetTypeEnum.SYSTEM_INNER.getCode().equals(targetCampus.getTargetType())){
            String oldTargetName = targetCampus.getTargetName();
            String newTargetName = dto.getTargetName();
            Assert.isTrue(oldTargetName.equals(newTargetName), "系统内置默认指标名称不可修改！");

            Long oldGroupId = targetCampus.getGroupId();
            Long newGroupId = dto.getGroupId();
            Assert.isTrue(oldGroupId.equals(newGroupId), "系统内置默认指标分组不可修改！");
        } else {
            Long newGroupId = dto.getGroupId();
            TargetGroup newGroup = targetGroupManager.getById(newGroupId);
            Assert.isTrue(TargetTypeEnum.DEFAULT.getCode().equals(newGroup.getGroupType()),
                    "当前指标不可更换到系统默认分组下！");
        }

        // 表单发送修改才生成新表单
        TargetTemplate template = null;
        if (!theSameTemplate) {
            // 插入表单到MongoDB
            TargetTemplateSaveDTO templateSaveDTO = new TargetTemplateSaveDTO()
                    .setTemplateInfoList(dto.getTemplateInfoList());
            template = templateService.save(templateSaveDTO);

            target.setTemplateId(template.getId());
            target.setTemplateInfoJson(JSONObject.toJSONString(template));
        }

        // 加分控件
        TargetSaveDTO.InnerScoreControl scoreControl = dto.getScoreControl();
        if (ObjectUtil.isNotNull(scoreControl)) {
            // 设置加分控件信息
            target.setScoreControlName(scoreControl.getScoreControlName());
            target.setScoreControlType(scoreControl.getScoreControlType());
            target.setScore(scoreControl.getScore());
            BigDecimal score = scoreControl.getScore();
            BigDecimal scoreValue = ObjectUtil.isNull(score) ? null : score.abs();
            target.setScoreValue(scoreValue);
            if (ScoreTypeEnum.REDUCE.getCode().equals(scoreControl.getScoreControlType()) && ObjectUtil.isNotNull(
                    scoreControl.getScore())) {
                target.setScore(scoreControl.getScore().abs().negate());
            }
        } else {
            target.setScore(null);
            target.setScoreValue(null);
            target.setScoreControlName(null);
            target.setScoreControlType(null);
        }

        // 判断是否扣分延续指标
        this.scoreContinuationFlag(target, dto);

        update(target, new LambdaUpdateWrapper<Target>()
                .eq(Target::getId, target.getId())
                .set(Target::getScore, target.getScore())
                .set(Target::getScoreValue, target.getScoreValue())
                .set(Target::getScoreControlName, target.getScoreControlName())
                .set(Target::getScoreControlType, target.getScoreControlType())
                .set(Target::getScoreContinuationFlag, target.getScoreContinuationFlag())
                .set(Target::getIsNotifyTeacher, target.getIsNotifyTeacher())
        );
        // 指标发生改变才需要重新绑定
        if (!theSameTemplate) {
            // 将指标id保存到MongoDB（和表单绑定）
            TargetTemplateSaveDTO templateSave = new TargetTemplateSaveDTO()
                    .setTemplateId(template.getId())
                    .setTargetId(target.getId());
            templateService.save(templateSave);
        }

        List<TargetUserPO> taskUserList = getTaskUserList(target, dto.getTargetUserList());
        // 批量保存可填写指标人
        targetUserManager.saveBatch(taskUserList);

        // 如果修改了指标,涉及的活动规则一级任务内容设置为填写项的,需要将任务规则状态置为失效
//        medalActivityRuleService.updateRuleStatusFailure(dto.getId(), 1);
        eventCenter.publish(new TargetUpdateEvent(Lists.newArrayList(target)));

        // 删除已有的审核人
        evaluateTargetReviewTeacherManager.deleteByTargetId(target.getId(), WebUtil.getStaffId());

        // 构建审核人PO(家长)
        List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS = this.buildReviewTeacherPO(dto, target);
        // 构建审核人PO(学生)
        List<EvaluateTargetReviewTeacherPO> studentReviewTeacherPOS = this.buildStudentReviewTeacherPO(dto, target);
        reviewTeacherPOS.addAll(studentReviewTeacherPOS);
        if (CollUtil.isEmpty(reviewTeacherPOS)) {
            log.warn("【修改指标】,【没有审核人无需保存】,targetId:{}", target.getId());
            return;
        }
        // 批量保存审核人
        evaluateTargetReviewTeacherManager.saveBatch(reviewTeacherPOS);
    }

    /**
     * 获取指标填写人
     *
     * @param target
     * @param targetUserList
     * @return
     */
    private List<TargetUserPO> getTaskUserList(Target target,
                                               List<TargetSaveDTO.InnerEvaluateTargetUser> targetUserList) {
        if (CollUtil.isEmpty(targetUserList)) {
            return Lists.newArrayList();
        }

        List<TargetUserPO> evaluateTargetUserPOList = new ArrayList<>();
        for (InnerEvaluateTargetUser innerEvaluateTargetUser : targetUserList) {
            TargetUserPO evaluateTargetUser = convert.toEvaluateTargetUser(innerEvaluateTargetUser);
            // 如果是学生任职
            if (ObjectUtil.equal(innerEvaluateTargetUser.getSubmitType(), 20)){
                evaluateTargetUser.setSubmitObjectValue(JSONUtil.toJsonStr(innerEvaluateTargetUser.getSubmitObj()));
            }
            evaluateTargetUserPOList.add(evaluateTargetUser);
        }

        // 修改时先删除当前指标所有可提交人
        if (ObjectUtil.isNotNull(target.getId())) {
            targetUserManager.remove(new LambdaQueryWrapper<TargetUserPO>()
                    .eq(TargetUserPO::getSchoolId, WebUtil.getSchoolId())
                    .eq(TargetUserPO::getCampusId, WebUtil.getCampusId())
                    .eq(TargetUserPO::getTargetId, target.getId()));
        }

        for (TargetUserPO user : evaluateTargetUserPOList) {
            user.setSchoolId(WebUtil.getSchoolId());
            user.setCampusId(WebUtil.getCampusId());
            user.setTargetId(target.getId());
            user.setTenantId(WebUtil.getTenantId());
            // 头像去除华为云链接前缀
            user.setIconUrl(StrUtil.isBlank(user.getIconUrl()) ? user.getIconUrl()
                    : user.getIconUrl().substring(saasIconPrefix.length() + 1));

            // 教职工和家长，userCount处理为1
            Boolean flag = TargetUserSubmitTypeEnum.STAFF.getCode().equals(user.getSubmitType())
                    || TargetUserSubmitTypeEnum.STUDENT.getCode().equals(user.getSubmitType());
            user.setUserCount(flag ? Constant.ONE : user.getUserCount());
        }

        return evaluateTargetUserPOList;
    }

    /**
     * 判断指标模板是否发生变化
     *
     * @param targetId
     * @param templateInfoList
     * @return
     */
    private boolean isTheSameTemplate(Long targetId, List<TemplateInfoSaveDTO> templateInfoList) {
        if (ObjectUtil.isNull(targetId) || CollUtil.isEmpty(templateInfoList)) {
            return false;
        }
        // 获取模板
        TargetTemplate targetTemplate = templateService.getByTargetId(targetId);
        if (ObjectUtil.isNull(targetTemplate) || CollUtil.isEmpty(targetTemplate.getTemplateInfoList())) {
            return false;
        }

        JSON newTemplateJson = JSONUtil.parse(templateInfoList);
        JSON oldTemplateJson = JSONUtil.parse(targetTemplate.getTemplateInfoList());

        return newTemplateJson.equals(oldTemplateJson);

    }

    /**
     * 指标删除
     *
     * @param targetId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long targetId) {
        Assert.isTrue(ObjectUtil.isNotNull(targetId), "指标id不能为空");
        Target target = this.getById(targetId);
        Assert.isTrue(ObjectUtil.isNotNull(target), "当前指标不存在或已被删除，请确认后重试");
        Assert.isFalse(TargetTypeEnum.SYSTEM_RECOMMEND.getCode().equals(target.getTargetType()),"系统推荐指标不可删除！");

        // 删除指标的可填写人
        targetUserManager.remove(new LambdaQueryWrapper<TargetUserPO>()

                .eq(TargetUserPO::getCampusId, WebUtil.getCampusId())
                .eq(TargetUserPO::getTargetId, targetId));
        // 删除指标
        this.removeById(targetId);

        // 如果禁用删除,对应活动规则失效
        eventCenter.publish(new TargetDeletedEvent(Lists.newArrayList(target)));
//        medalActivityRuleService.updateRuleStatusFailure(targetId, 2);

        // 删除指标审核人
        evaluateTargetReviewTeacherManager.deleteByTargetId(targetId, WebUtil.getStaffId());
    }

    /**
     * 禁用启用
     *
     * @param targetId 指标id
     * @param opType   操作类型 0：禁用  1：启用
     */
    @Override
    public void changeStatus(Long targetId, Integer opType) {
        Assert.isTrue(!ObjectUtil.hasNull(targetId, opType), "targetId、opType不能为空！");
        Assert.isTrue(opType.equals(Constant.ZERO) || opType.equals(Constant.ONE), "opType输入错误！");
        Target target = this.getById(targetId);
        Assert.isTrue(ObjectUtil.isNotNull(target), "当前指标不存在或已被删除，请确认后重试");
        this.update(null, new LambdaUpdateWrapper<Target>()
                .eq(Target::getId, targetId)
                .set(Target::getTargetStatus, opType)
                .set(Target::getUpdateBy, WebUtil.getStaffId())
                .set(Target::getUpdateTime, DateUtil.date()));
        // 如果禁用指标,对应活动规则失效
        if (Constant.NO.equals(opType)) {
            target.setTargetStatus(opType);
            eventCenter.publish(new TargetUpdateStatusEvent(Lists.newArrayList(target)));
//            medalActivityRuleService.updateRuleStatusFailure(targetId, 3);
        }
    }

    /**
     * 指标管理--排序
     *
     * @param sortList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(List<TargetSortDTO.InnerSort> sortList) {
        Assert.isTrue(CollUtil.isNotEmpty(sortList), "sortList不能为空");

        checkTargetAndGroupType(sortList);

        for (TargetSortDTO.InnerSort d : sortList) {
            this.update(null, new LambdaUpdateWrapper<Target>() // NOSONAR
                    .eq(Target::getId, d.getTargetId())
                    .set(Target::getSortIndex, d.getSortIndex())
                    .set(Target::getGroupId, d.getGroupId())
                    .set(Target::getUpdateBy, WebUtil.getStaffId())
                    .set(Target::getUpdateTime, DateUtil.date()));
        }
    }

    private void checkTargetAndGroupType(List<InnerSort> sortList) {
        List<Long> targetIdList = sortList
                .stream()
                .map(InnerSort::getTargetId).distinct().collect(Collectors.toList());

        List<Long> groupIdList = sortList
                .stream()
                .map(InnerSort::getGroupId).distinct().collect(Collectors.toList());

        List<Target> targets = this.listByIds(targetIdList);
        Assert.notEmpty(targets,"指标不存在！");

        List<TargetGroup> targetGroups = targetGroupManager.listByIds(groupIdList);
        Assert.notEmpty(targetGroups,"分组不存在！");

        Map<Long, Target> targetMap = targets
                .stream()
                .collect(Collectors.toMap(Target::getId, Function.identity()));

        Map<Long, TargetGroup> targetGroupMap = targetGroups
                .stream()
                .collect(Collectors.toMap(TargetGroup::getId, Function.identity()));

        for (InnerSort innerSort : sortList) {
            Long targetId = innerSort.getTargetId();
            Long groupId = innerSort.getGroupId();

            Target target = targetMap.get(targetId);
            TargetGroup targetGroup = targetGroupMap.get(groupId);

            Assert.notNull(target,"指标不存在！");
            Assert.notNull(targetGroup,"分组不存在！");

            Assert.isTrue(target.getTargetType().equals(targetGroup.getGroupType()),"指标与分组类型不匹配！");

        }
    }

    /**
     * 查指标详情
     *
     * @param targetId
     * @return
     */
    @Override
    public TargetDetailVO detail(Long targetId) {
        Assert.isTrue(ObjectUtil.isNotNull(targetId), "targetId不能为空");
        // 查指标
        Target target = get(targetId);
        if (ObjectUtil.isNull(target)) {
            return null;
        }
        target.setIconUrl(getIconUrlPrefix() + target.getIconUrl());
        TargetDetailVO targetDetailVO = convert.toTargetDetailVO(target);

        // 构建审核人信息(家长)
        this.buildReviewTeachers(target, targetDetailVO);
        // 构建审核人信息(学生)
        this.buildStudentReviewTeachers(target, targetDetailVO);

        // 设置表单json字符串
        String templateJsonStr = templateService.getTemplateJsonStrByTargetId(target.getId());
        targetDetailVO.setTemplateInfoJsonStr(templateJsonStr);

        // 设置表单填写人
        List<TargetUserPO> targetUserPOList = targetUserManager.listByTargetId(targetId);
        // 表单填写人头像添加华为云前缀
        targetUserPOList.parallelStream().forEach(user -> {
            if (StrUtil.isNotBlank(user.getIconUrl())) {
                user.setIconUrl(saasIconPrefix + StrPool.SLASH + user.getIconUrl());
            }
        });

        List<TargetDetailVO.InnerTargetUser> targetUserList = new ArrayList<>();
        for (TargetUserPO targetUserPO : targetUserPOList) {
            // 非学生任职数据
            if (!ObjectUtil.equal(targetUserPO.getSubmitType(), 20)){
                targetUserList.add(convert.toInnerTargetUser(targetUserPO));
            }
            // 学生任职数据
            if (ObjectUtil.equal(targetUserPO.getSubmitType(), 20)){
                TargetDetailVO.InnerTargetUser innerTargetUserStudent = new TargetDetailVO.InnerTargetUser();
                innerTargetUserStudent.setSubmitUserName(targetUserPO.getSubmitUserName());

                innerTargetUserStudent.setSubmitType(targetUserPO.getSubmitType());
                innerTargetUserStudent.setSubmitValue(targetUserPO.getSubmitValue());
                if (CharSequenceUtil.isNotBlank(targetUserPO.getSubmitObjectValue())){
                    InnerSubmitObj innerSubmitObj = JSONUtil.parse(targetUserPO.getSubmitObjectValue()).toBean(InnerSubmitObj.class);
                    innerTargetUserStudent.setSubmitObj(innerSubmitObj);
                }
                innerTargetUserStudent.setIconUrl(targetUserPO.getIconUrl());
                innerTargetUserStudent.setUserCount(targetUserPO.getUserCount());
                targetUserList.add(innerTargetUserStudent);
            }
        }
        targetDetailVO.setTargetUserList(targetUserList);

        return targetDetailVO;
    }

    /**
     * 构建审核人信息
     */
    private void buildReviewTeachers(Target target, TargetDetailVO targetDetailVO) {
        // 1. 校验指标对象是否为空
        Assert.notNull(target, "构建审核人信息指标不能为空");

        // 2. 获取审核人信息
        List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS = evaluateTargetReviewTeacherManager.listByTargetId(
                target.getId(),1);

        // 3. 构建审核人
        if (CollUtil.isEmpty(reviewTeacherPOS)) {
            // 3.1 处理空列表情况：设置班主任默认值
            targetDetailVO.setClassTeacher(this.createClassTeacher(-1L, TargetReviewTypeEnum.HEADER_MASTER.getName()));
            targetDetailVO.setReviewType(TargetReviewTypeEnum.HEADER_MASTER.getCode());
            return;
        }

        // 3.2 非空列表处理
        EvaluateTargetReviewTeacherPO firstTeacher = reviewTeacherPOS.get(0);
        Integer reviewType = firstTeacher.getReviewType();

        if (TargetReviewTypeEnum.APPOINTED_TEACHER.getCode().equals(reviewType)) {
            // 3.3 审核人是指定老师
            targetDetailVO.setReviewType(reviewType);
            targetDetailVO.setReviewTeachers(this.convertToReviewTeacherList(reviewTeacherPOS));
            return;
        }
        // 3.4 审核人是班主任或者班级内的课程老师
        targetDetailVO.setClassTeacher(
                this.createClassTeacher(firstTeacher.getBusinessId(), firstTeacher.getBusinessName()));
        targetDetailVO.setReviewType(reviewType);

    }

    private void buildStudentReviewTeachers(Target target, TargetDetailVO targetDetailVO) {
        // 1. 校验指标对象是否为空
        Assert.notNull(target, "构建审核人信息指标不能为空");

        // 2. 获取审核人信息
        List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS = evaluateTargetReviewTeacherManager.listByTargetId(
                target.getId(),2);

        // 3. 构建审核人
        if (CollUtil.isEmpty(reviewTeacherPOS)) {
            // 3.1 处理空列表情况：设置班主任默认值
            targetDetailVO.setStudentClassTeacher(this.createClassTeacher(-1L, TargetReviewTypeEnum.HEADER_MASTER.getName()));
            targetDetailVO.setStudentReviewType(TargetReviewTypeEnum.HEADER_MASTER.getCode());
            return;
        }

        // 3.2 非空列表处理
        EvaluateTargetReviewTeacherPO firstTeacher = reviewTeacherPOS.get(0);
        Integer reviewType = firstTeacher.getReviewType();

        if (TargetReviewTypeEnum.APPOINTED_TEACHER.getCode().equals(reviewType)) {
            // 3.3 审核人是指定老师
            targetDetailVO.setStudentReviewType(reviewType);
            targetDetailVO.setStudentReviewTeachers(this.convertToReviewTeacherList(reviewTeacherPOS));
            return;
        }
        // 3.4 审核人是班主任或者班级内的课程老师
        targetDetailVO.setStudentClassTeacher(
                this.createClassTeacher(firstTeacher.getBusinessId(), firstTeacher.getBusinessName()));
        targetDetailVO.setStudentReviewType(reviewType);

    }


    /**
     * 构建指定老师
     */
    private List<InnerReviewTeacherInfo> convertToReviewTeacherList(List<EvaluateTargetReviewTeacherPO> pos) {
        if (CollUtil.isEmpty(pos)) {
            log.warn("【指标审核人】【构建审核人信息】指定老师为空");
            return Collections.emptyList();
        }
        return pos.stream().map(po -> {
            InnerReviewTeacherInfo info = new InnerReviewTeacherInfo();
            info.setBusinessId(po.getBusinessId());
            info.setBusinessType(po.getBusinessType());
            info.setBusinessName(po.getBusinessName());
            info.setSubmitType(po.getSubmitType());
            return info;
        }).collect(Collectors.toList());
    }

    /**
     * 创建班主任或者班级学科老师
     */
    private InnerReviewTeacherInfo createClassTeacher(Long id, String name) {
        InnerReviewTeacherInfo teacher = new InnerReviewTeacherInfo();
        teacher.setBusinessId(id);
        teacher.setBusinessName(name);
        return teacher;
    }

    /**
     * 根据指标id获取指标
     *
     * @param targetId
     * @return
     */
    @Override
    public Target get(Long targetId) {
        if (ObjectUtil.isNull(targetId)) {
            return null;
        }
        Target target = this.getOne(new LambdaQueryWrapper<Target>()
                .eq(Target::getId, targetId)
                .eq(Target::getSchoolId, WebUtil.getSchoolId())
                .eq(Target::getCampusId, WebUtil.getCampusId()));
        return target;
    }

    /**
     * 获取图标列表
     *
     * @return
     */
    @Override
    public List<String> listIcon() {
        List<String> iconUrl = new ArrayList<>();
        for (int i = 1; i <= targetIconCount; i++) {
            iconUrl.add(
                    targetIconHttp + "://" + bucketName + StrPool.DOT + endPoint + targetIconPath + targetIconPrefix + i
                            + targetIconSuffix);
        }
        return iconUrl;

    }

    /**
     * 指标填写人列表
     *
     * @return
     */
    @Override
    public List<TargetListTargetUserVO> listTargetWritePeople(ListTargetWritePeopleDaoDTO dto) {
        // 获取今天需要发送的指标集合
        List<TargetWriteUserVO> targetList = listTodaySendMessageTarget(dto);
        if (CollUtil.isEmpty(targetList)) {
            return Lists.newArrayList();
        }

        List<TargetListTargetUserVO> userList = new ArrayList<>();
        // 遍历获取指标填写人
        for (TargetWriteUserVO target : targetList) {

            // 指标填写人为空，不发送
            List<TargetTargetUserInnerTargetUserVO> targetUserList = target.getTargetUserList();
            if (CollUtil.isEmpty(targetUserList)) {
                continue;
            }

            TargetListTargetUserVO targetUser = convert.toTargetListTargetUserVO(target);
            // 设置提醒时间
            // 提醒时间转换为日期格式
            Date remindTime = DateUtil.parse(
                    DateUtil.format(new Date(), "yyyyMMdd") + target.getRemindTimeStr() + "00");
            // 设置催办时间
            Date urgeTime = DateUtil.offsetHour(remindTime, target.getUrgeTimeInt());
            targetUser.setRemindTime(remindTime);
            targetUser.setUrgeTime(urgeTime);

            // 如果是非必填任务提醒,提前判断数据是否需要推送,避免非推送数据频繁调用saas接口
            if (Constant.NO.equals(dto.getMustSubmitFlag())) {
                Date now = DateUtil.date();
                DateTime nowMin = DateUtil.beginOfMinute(now);
                DateTime preNowMin = DateUtil.offsetMinute(nowMin, -1);
                boolean flag = Boolean.FALSE;
                if (nowMin.isAfterOrEquals(targetUser.getRemindTime()) && preNowMin.isBefore(
                        targetUser.getRemindTime())) {
                    flag = Boolean.TRUE;
                }
                if (!flag) {
                    continue;
                }
            }

            // 通过行政组织id获取到老师信息(老师)
            List<TargetListTargetUserVO.InnerTargetUser> targetStaffInfoList = queryStaffInfo(targetUserList);

            // 通过教务组织id获取到学生信息(家长)
            List<TargetListTargetUserVO.InnerTargetUser> targetStudentInfoList = queryStudentInfo(targetUserList,
                    Convert.toLong(target.getSchoolId()));

            // 填写人信息合并(老师+学生)
            targetStaffInfoList.addAll(targetStudentInfoList);

            // 设置指标填写人
            targetUser.setTargetUserList(targetStaffInfoList);
            userList.add(targetUser);
        }
        return userList;
    }

    /**
     * 通过行政组织id获取到老师信息
     *
     * @param targetUserList
     * @return
     */
    private List<TargetListTargetUserVO.InnerTargetUser> queryStaffInfo(
            List<TargetTargetUserInnerTargetUserVO> targetUserList) {
        log.info("通过行政组织id获取到老师信息,入参:[{}]", targetUserList);
        try {
            // 类型1:数据库中存的是行政组织id的,筛选出填写人类型为行政组织id或角色的
            List<TargetTargetUserInnerTargetUserVO> orgRoleTargetUserList = targetUserList.stream()
                    .filter(user -> SaaSUserTypeEnum.ORGANIZATION.getCode().equals(user.getSubmitType())
                            || SaaSUserTypeEnum.ROLE.getCode().equals(user.getSubmitType()))
                    .collect(Collectors.toList());
            List<BasicInfoListAllUserDTO> queryVO = convert.toBasicInfoListAllUserDTOList(orgRoleTargetUserList);
            // 获取saas人员列表
            List<BasicInfoListAllUserVO> saasUserList = basicInfoService.listAllUser(queryVO);
            List<TargetListTargetUserVO.InnerTargetUser> goalUserList = convert.toTargetInnerTargetUserList(
                    saasUserList);
            log.info("类型1:通过数据库中存的是行政组织id的来获取saas教职工信息集合:{}", saasUserList);

            // 类型2:数据库中存的就是教师id的
            List<Long> staffIdList = targetUserList.stream()
                    .filter(user -> SaaSUserTypeEnum.STAFF.getCode().equals(user.getSubmitType()))
                    .map(TargetTargetUserInnerTargetUserVO::getSubmitValue).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(staffIdList)) {
                // 查人员信息(老师)
                List<StaffBatchVO> staffList = basicInfoService.queryBasicInfoByStaffIds(
                        new StaffInfoQueryDTO().setStaffIds(staffIdList));
                List<BasicInfoListAllUserVO> basicInfoListAllUsers = convert.toBasicInfoListAllUserVOList(staffList);
                List<TargetListTargetUserVO.InnerTargetUser> targetStaffList = convert.toTargetInnerTargetUserList(
                        basicInfoListAllUsers);
                targetStaffList.forEach(staff -> staff.setUserType(SaasUserTypeQueryEnum.STAFF.getCode()));

                log.info("类型2:数据库中存的就是教师id的来获取saas教职工信息集合:{}", targetStaffList);
                goalUserList.addAll(targetStaffList);
            }

            log.info("获取指标下所有老师信息,老师信息集合:[{}]", goalUserList);

            // 人员去重
            return CollUtil.isEmpty(goalUserList) ? goalUserList
                    : goalUserList.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("通过教务组织id获取到老师信息异常,返回空集合", e);
        }
        return new ArrayList<>();
    }

    /**
     * 通过教学组织id获取到学生信息
     *
     * @param targetUserList
     * @param schoolId
     * @return {@link List}<{@link TargetListTargetUserVO.InnerTargetUser}>
     */
    private List<TargetListTargetUserVO.InnerTargetUser> queryStudentInfo(
            List<TargetTargetUserInnerTargetUserVO> targetUserList, Long schoolId) {
        log.info("通过教学组织id获取到学生信息,入参:[{}],schoolId:{}", targetUserList, schoolId);
        try {
            // 类型1:数据库中存的是教学组织id的,筛选出填写人类型为教学组织id
            List<TargetTargetUserInnerTargetUserVO> parentTargetUserList = targetUserList.stream()
                    .filter(user -> OrgTypeEnum.getCodes().contains(Convert.toInt(user.getSubmitType())))
                    .collect(Collectors.toList());
            List<EduOrgTreeVO> eduOrgTrees = new ArrayList<>();
            parentTargetUserList.forEach(s -> {
                EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
                eduOrgQueryDTO.setCurrentId(s.getSubmitValue());
                eduOrgQueryDTO.setEndType(5);
                eduOrgQueryDTO.setCurrentIdType(OrgTypeEnum.getByCode(s.getSubmitType()));
                eduOrgQueryDTO.setIsTree(Constant.NO);
                //根据组织机构查询出班级
                List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
                List<EduOrgTreeVO> collect = eduOrgTreeVOS.stream().filter(t -> t.getType().equals(5))
                        .collect(Collectors.toList());
                eduOrgTrees.addAll(collect);
            });
            //过滤出所有班级id
            List<Long> classIds = eduOrgTrees.stream().filter(s -> s.getType().equals(5)).map(EduOrgTreeVO::getId)
                    .distinct().collect(Collectors.toList());
            log.info("类型1:通过教务组织id获取到班级id信息,班级id信息集合:{}", classIds);

            //根据班级查询所有学生信息
            List<EduStudentInfoVO> eduStudentInfoByClassIds = new ArrayList<>();
            for (Long classId : classIds) {
                EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
                eduStudentPageQueryDTO.setSchoolId(schoolId);
                eduStudentPageQueryDTO.setClassId(classId);
                eduStudentPageQueryDTO.setGraduationStatus("0");
                List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
                eduStudentInfoByClassIds.addAll(eduStudentInfoVOS);
            }
            log.info("类型1:通过班级id集合获取到学生信息,学生信息集合:{}", eduStudentInfoByClassIds);

            // 类型2:根据数据库中原本值为学生id的数据去saas查询出这些学生的信息
            List<Long> studentIds = targetUserList.stream()
                    .filter(user -> SaaSUserTypeEnum.PARENT.getCode().equals(user.getSubmitType()))
                    .map(TargetTargetUserInnerTargetUserVO::getSubmitValue).collect(Collectors.toList());
            // 通过学生id集合查询学生信息
            List<EduStudentInfoVO> eduStudentInfoByStudentIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(studentIds)) {
                EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
                eduStudentPageQueryDTO.setSchoolId(schoolId);
                eduStudentPageQueryDTO.setStudentIds(studentIds);
                eduStudentInfoByStudentIds = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
            }
            log.info("类型2:根据数据库中原本值为学生id的数据去saas查询出这些学生的信息,学生信息集合:{}",
                    eduStudentInfoByStudentIds);

            //数据信息合并去重
            eduStudentInfoByClassIds.addAll(eduStudentInfoByStudentIds);
            List<EduStudentInfoVO> collect = eduStudentInfoByClassIds.stream().distinct().collect(Collectors.toList());

            //封装数据
            List<TargetListTargetUserVO.InnerTargetUser> targetStudentList = new ArrayList<>();
            collect.forEach(s -> {
                TargetListTargetUserVO.InnerTargetUser innerTargetUser = new TargetListTargetUserVO.InnerTargetUser();
                innerTargetUser.setStaffId(Convert.toStr(s.getId()));
                innerTargetUser.setUserType(TaskRoleTypeEnum.PARENT.getCode());
                targetStudentList.add(innerTargetUser);
            });
            log.info("查询指标下所有学生信息集合:{}", targetStudentList);
            return targetStudentList;
        } catch (Exception e) {
            log.warn("通过教务组织id获取到学生信息异常,返回空集合", e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取今天需要发送的指标集合
     *
     * @return
     */
    private List<TargetWriteUserVO> listTodaySendMessageTarget(ListTargetWritePeopleDaoDTO dto) {
        if (ObjectUtil.isNull(dto)) {
            // 默认查每次必须提交的
            dto = new ListTargetWritePeopleDaoDTO().setMustSubmitFlag(Constant.YES);
        }
        List<TargetWriteUserVO> targetList = targetMapper.listTargetWritePeople(dto);
        if (CollUtil.isEmpty(targetList)) {
            return Collections.emptyList();
        }
        return targetList.stream().filter(target -> {
            // 过滤出提醒时间、催办时间不为空且今天提醒的
            boolean flag = StrUtil.isBlank(target.getRemindTimeStr()) || ObjectUtil.isNull(target.getUrgeTimeInt());
            if (flag) {
                return false;
            }
            return todayRemind(target.getHolidayNoticeFlag(), target.getSubmitType(), target.getSubmitDate());

        }).collect(Collectors.toList());
    }

    /**
     * 查询今天是否通知
     *
     * @param holidayNoticeFlag
     * @param submitType
     * @param submitDate
     * @return
     */
    private boolean todayRemind(Integer holidayNoticeFlag, Integer submitType, String submitDate) {
        if (ObjectUtil.hasNull(holidayNoticeFlag, submitType)) {
            return false;
        }
        // 类型是工作日每天，提醒日期可以为空，其他类型不可以为空
        boolean holidayFlag =
                !SubmitRateEnum.WORK_DAY.getSubmitType().equals(submitType) && !SubmitRateEnum.NO_RATE.getSubmitType()
                        .equals(submitType);
        if (holidayFlag) {
            if (StrUtil.isBlank(submitDate)) {
                return false;
            }
        }
        // 没有该类型则不发送
        String submitRateName = SubmitRateEnum.getSubmitRateName(submitType);
        if (StrUtil.isEmpty(submitRateName)) {
            return false;
        }
        // 寒暑假、节假日不通知
        if (Constant.NO.equals(holidayNoticeFlag)) {
            // 获取今天是否寒暑假、节假日
            Boolean todayIsHoliday = holidayUtil.todayIsHoliday();
            // 是则不发送
            if (todayIsHoliday) {
                return false;
            }
        }
        // 根据提醒类型判定今天是否要发送
        if (submitType.equals(SubmitRateEnum.WORK_DAY.getSubmitType())) {
            // 工作日每天--周一到周五为工作日
            return !DateUtil.isWeekend(new Date());

        } else if (submitType.equals(SubmitRateEnum.EVERY_WEEK.getSubmitType())) {
            // 每周
            // Hutool工具类dayOfWeek方法： 获得指定日期是星期几，1表示周日，2表示周一
            int day = DateUtil.dayOfWeek(new Date());
            day = day == 1 ? 7 : day - 1;
            // 提交日期包含今天则提醒
            return submitDate.contains(String.valueOf(day));
        } else if (submitType.equals(SubmitRateEnum.EVERY_MONTH.getSubmitType())) {
            // 每月
            // 今天是每月第几天
            int day = DateUtil.thisDayOfMonth();
            // 提交日期包含今天则发送
            if (submitDate.contains(String.valueOf(day))) {
                return true;
            }
            // 获取本月最后一天
            int endDay = DateUtil.dayOfMonth(DateUtil.endOfMonth(new Date()));
            if (31 == endDay) {
                return false;
            }
            // 只要提交日期有比本月最后一天的则发送
            long count = Stream.of(submitDate.split(StrPool.COMMA)).filter(date -> Integer.parseInt(date) > endDay)
                    .count();
            return count > 0L;

        } else if (submitType.equals(SubmitRateEnum.EVERY_YEAR.getSubmitType())) {
            // 每年
            // 获取今天得月日 比如：0107 一月7日
            String day = DateUtil.format(new Date(), "MMdd");
            // 如果提交日期包含今天则发送
            List<String> dayArray = Arrays.asList(submitDate.split(StrPool.COMMA));
            boolean contains = dayArray.contains(day);
            if (contains) {
                return true;
            }
            // 如果今年是平年，今天是2月28日，提交日期包含2月29，则提示发送
            // 今年是否平年
            boolean leapYear = DateUtil.isLeapYear(DateUtil.thisYear());
            // 闰年则返回不提醒
            if (leapYear) {
                return false;
            }
            // 今天不是2月28则不提醒
            if (!"0228".equals(day)) {
                return false;
            }
            // 如果提醒时间包含2月29日则提醒
            return dayArray.contains("0229");
        }
        return false;
    }

    /**
     * 获取iconurl 华为云链接前缀
     *
     * @return
     */
    @Override
    public String getIconUrlPrefix() {
        return targetIconHttp + "://" + bucketName + StrPool.DOT + endPoint + StrPool.SLASH;
    }

    @Override
    public Page<TargetUserQueryVO> queryStaffListBySchool(SchoolStaffQueryDTO schoolStaffQueryDTO) {
        return null;
    }

    /**
     * 检查体测分组和指标
     *
     * @return {@link SportGroupTargetVO}
     */
    @Override
//    @RLock
    public SportGroupTargetVO checkSportGroupAndTarget() {
        String campusId = WebUtil.getCampusId();
        SportGroupTargetVO orAdd = redisUtil.getOrAdd(RedisKeyConstants.SPORT_DATA_GROUP_ID + campusId,
                this::getSportGroupTargetVO, CacheConstants.ONE_HOUR);
        return orAdd;
    }

    private SportGroupTargetVO getSportGroupTargetVO() {
        SportGroupTargetVO sportGroupTargetVO = new SportGroupTargetVO();
        // 获取或创建体测分组Id
        Long sportGroupId = getOrCreateSportGroupId();
        // 获取或创建体测指标Id
        Long sportTargetId = getOrCreateSportTargetId(sportGroupId);
        sportGroupTargetVO.setGroupId(sportGroupId);
        sportGroupTargetVO.setTargetId(sportTargetId);

        return sportGroupTargetVO;
    }

    @Override
    @RLock(keys = {"#targetCopyDTO.targetId"})
    @Transactional(rollbackFor = Exception.class, timeout = 60)
    public boolean copy(TargetCopyDTO targetCopyDTO) {
        Assert.isTrue(targetCopyDTO.getTargetName().length() <= 6, "指标名称不能超过6个字符");
        // 查指标
        Target target = get(targetCopyDTO.getTargetId());
        if (ObjectUtil.isNull(target)) {
            log.info("[复制指标]-指标不存在，信息：{}", JSONUtil.toJsonStr(targetCopyDTO));
            return false;
        }
        Assert.isFalse(TargetTypeEnum.SYSTEM_RECOMMEND.getCode().equals(target.getTargetType()),"系统推荐指标不可复制！");
        // 复制表单
        String oldTemplateId = target.getTemplateId();
        TargetTemplate newTemplate = targetCopyLogic.copyTemplate(oldTemplateId);
        // 复制指标
        Long newTargetId = targetCopyLogic.copyTarget(target, targetCopyDTO, newTemplate);
        // 复制指标user
        targetCopyLogic.copyTargetUser(target.getId(), newTargetId);

        return true;
    }

    @Override
    public List<Long> listTargetIdsByGroupId(Long groupId) {
        return targetMapper.selectList(
                        new LambdaQueryWrapper<Target>().select(Target::getId).eq(Target::getGroupId, groupId)).stream()
                .map(Target::getId).collect(Collectors.toList());
    }

    /**
     * 根据指标ids批量列表查询指标
     *
     * @param targetIds
     * @return
     */
    @Override
    public List<Target> listByTargetIds(List<Long> targetIds) {
        if (CollUtil.isEmpty(targetIds)) {
            log.info("根据指标ids批量查询指标信息，参数为空，入参：【{}】", JSONUtil.toJsonStr(targetIds));
            return Collections.emptyList();
        }
        return targetMapper.listTargetByIds(targetIds);
    }

    /**
     * 图文点评设置
     *
     * @param query
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setPictureEvaluate(TargetIdQuery query) {
        if (BeanUtil.isEmpty(query) || CollUtil.isEmpty(query.getTargetIds())) {
            log.info("图文点评设置，参数为空，直接返回");
            return;
        }

        //原图文点评
        List<Long> originalTargetIds = this.listPictureEvaluates();
        // 设置的targetIds是否与原图文点评的targetIds相同，如果相同，无需处理
        if (query.getTargetIds() == originalTargetIds) {
            return;
        }
        if (CollUtil.isNotEmpty(originalTargetIds)) {
            //清空原图文点评
            targetMapper.update(null, new LambdaUpdateWrapper<Target>()
                    .in(Target::getId, originalTargetIds)
                    .set(Target::getPictureEvaluateFlag, Constant.NO)
                    .set(Target::getUpdateBy, WebUtil.getStaffId())
                    .set(Target::getUpdateTime, DateUtil.now())
            );
        }
        //新增图文点评
        List<Target> targets = targetMapper.selectList(
                new LambdaQueryWrapper<Target>().in(Target::getId, query.getTargetIds()));
        targets.forEach(target -> {
            target.setPictureEvaluateFlag(Constant.YES);
            target.setPictureSortIndex(query.getTargetIds().indexOf(target.getId()) + 1);
            target.setUpdateBy(WebUtil.getStaffId());
            target.setUpdateTime(DateUtil.date());
        });
        this.updateBatchById(targets);
    }


    /**
     * 图文点评指标列表
     */
    @Override
    public List<Long> listPictureEvaluates() {
        List<Target> targets = targetMapper.selectList(new LambdaQueryWrapper<Target>()
                .select(Target::getId)
                .eq(Target::getCampusId, WebUtil.getCampusId())
                .eq(Target::getPictureEvaluateFlag, Constant.YES)
                .orderByAsc(Target::getPictureSortIndex));
        if (CollUtil.isEmpty(targets)) {
            return Collections.emptyList();
        }
        return targets.stream().map(Target::getId).collect(Collectors.toList());
    }

    @Override
    public List<Target> queryByCondition(TargetQO targetQO) {
        return targetMapper.queryByCondition(targetQO);
    }

    @Override
    public List<TargetBatchOperateVO> batchOperateQueryByModule(TargetBatchOperateQueryDTO batchOperateQueryDTO) {
        //1.查询所有的指标分组
        TargetGroupListByConditionsDaoDTO targetGroupListByConditionsDaoDTO = new TargetGroupListByConditionsDaoDTO();
        targetGroupListByConditionsDaoDTO.setCampusId(WebUtil.getCampusId());
        targetGroupListByConditionsDaoDTO.setModuleCodeList(batchOperateQueryDTO.getModuleCodes());
        targetGroupListByConditionsDaoDTO.setDatasource(DataSourceEnum.EVALUATE.getCode());
        List<TargetGroup> targetGroups = targetGroupService.listGroupByConditions(targetGroupListByConditionsDaoDTO);
        if (CollectionUtils.isEmpty(targetGroups)) {
            return Collections.emptyList();
        }
        List<Long> groupIdList = new ArrayList<>();
        Map<Long, TargetGroup> targetGroupMap = new HashMap<>();
        for (TargetGroup targetGroup : targetGroups) {
            targetGroupMap.put(targetGroup.getId(), targetGroup);
            groupIdList.add(targetGroup.getId());
        }
        //2.根据条件查询所有指标
        Map<Long, Target> targetMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            TargetQO targetQO = new TargetQO();
            targetQO.setStatus(Constant.ONE);
            targetQO.setDataSource(DataSourceEnum.EVALUATE.getCode());
            targetQO.setTargetGroupIdList(groupIdList);
            List<Target> targets = targetMapper.queryByCondition(targetQO);
            for (Target target : targets) {
                targetMap.put(target.getId(), target);
            }
        }
        //3.查询指标模板信息
        List<TargetTemplate> targetTemplates = Collections.emptyList();
        if (!targetMap.isEmpty()) {
            targetTemplates = templateDao.listByTargetIds(targetMap.keySet());
            if (CollectionUtils.isEmpty(targetTemplates)) {
                return Collections.emptyList();
            }
        }
        List<TargetBatchOperateVO> resultList = new ArrayList<>();
        //4.根据指标模板信息构建返回数据
        for (TargetTemplate targetTemplate : targetTemplates) {
            Long targetId = targetTemplate.getTargetId();
            Target target = targetMap.get(targetId);
            if (Objects.isNull(target)) {
                continue;
            }
            TargetGroup targetGroup = targetGroupMap.get(target.getGroupId());
            if (Objects.isNull(targetGroup)) {
                continue;
            }
            String moduleName = ModuleEnum.getModuleName(targetGroup.getModuleCode());
            List<TemplateInfoSaveDTO> templateInfoList = targetTemplate.getTemplateInfoList();
            //循环获取每个选项的值
            for (TemplateInfoSaveDTO templateInfoSaveDTO : templateInfoList) {
                //判断是否为批量操作允许类型
                TargetTemplateInfoTypeEnum targetTemplateInfoTypeEnum = TargetTemplateInfoTypeEnum.batchOperateTypeAllowMap.get(
                        templateInfoSaveDTO.getType());
                if (Objects.isNull(targetTemplateInfoTypeEnum)) {
                    continue;
                }
                if (Objects.isNull(templateInfoSaveDTO.getOptions())) {
                    continue;
                }
                List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> options = templateInfoSaveDTO.getOptions()
                        .getOptions();
                if (CollUtil.isEmpty(options)) {
                    continue;
                }
                //构建每个选项返回记录
                for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave option : options) {
                    TargetBatchOperateVO targetBatchOperateVO = new TargetBatchOperateVO();
                    targetBatchOperateVO.setModuleName(moduleName);
                    targetBatchOperateVO.setTargetName(target.getTargetName());
                    targetBatchOperateVO.setGroupName(targetGroup.getGroupName());
                    targetBatchOperateVO.setTemplateInfoName(templateInfoSaveDTO.getName());
                    targetBatchOperateVO.setTypeName(targetTemplateInfoTypeEnum.getDesc());
                    targetBatchOperateVO.setOption(option.getLabel());
                    targetBatchOperateVO.setScore(option.getValue());
                    resultList.add(targetBatchOperateVO);
                }
            }
        }
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 30000)
    public TargetBatchSaveResponse batchOperateSaveTarget(TargetBatchOperateSaveDTO batchOperateSaveDTO) {
        //1.批量导入参数校验
        TargetBatchSaveResponse response = validateBatchRecord(batchOperateSaveDTO);
        if (!response.getOperateSuccess()) {
            //操作失败，则直接返回
            return response;
        }
        //2.构建新增指标和指标分组
        List<TargetBatchOperateSaveRecordDTO> recordList = batchOperateSaveDTO.getRecordList();
        //新分组集合
        Map<String/*五育名称*/, Map<String/*分组名称*/, TargetGroup>> groupMap = new HashMap<>();
        //新指标Map
        Map<String/*五育名称+分组名称*/, Map<String/*指标名称*/, List<TargetBatchOperateSaveRecordDTO>>> targetMap = new LinkedHashMap<>();
        for (TargetBatchOperateSaveRecordDTO targetBatchOperateSaveRecordDTO : recordList) {
            //构建并填充分组对象
            buildAndFillTartgetGroup(targetBatchOperateSaveRecordDTO.getGroupName(),
                    targetBatchOperateSaveRecordDTO.getModuleName(), groupMap, batchOperateSaveDTO);
            //构建唯一指标分组标识（五育+分组名称）
            String targetKey = String.join(Constant.ROD, targetBatchOperateSaveRecordDTO.getModuleName(),
                    targetBatchOperateSaveRecordDTO.getGroupName());
            Map<String, List<TargetBatchOperateSaveRecordDTO>> targetNameMap = targetMap.computeIfAbsent(targetKey,
                    k -> new LinkedHashMap<>());
            targetNameMap.computeIfAbsent(targetBatchOperateSaveRecordDTO.getTargetName(), (key) -> new ArrayList<>())
                    .add(targetBatchOperateSaveRecordDTO);
        }
        //新增分组
        List<TargetGroup> groupInsertList = new ArrayList<>();
        for (Map.Entry<String, Map<String, TargetGroup>> targetGroupEntry : groupMap.entrySet()) {
            Map<String, TargetGroup> targetGroupMap = targetGroupEntry.getValue();
            groupInsertList.addAll(targetGroupMap.values());
        }

        //3.现存指标处理,并发送现存指标/指标分组删除/停用事件
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        dealExistTargetAndGroup(batchOperateSaveDTO, groupInsertList);
        log.info("【web】-【指标模块】-【批量导入指标数据】批量处理现存指标和分组耗时:{}", TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        //4.批量保存新指标记录
        batchSaveTargetRecords(batchOperateSaveDTO, targetMap, groupInsertList);
        log.info("【web】-【指标模块】-【批量导入指标数据】批量导入指标和分组耗时:{}", TIME_INTERVAL.interval());
        return response;
    }

    /**
     * 班级内的课程老师列表，支持模糊搜索
     */
    @Override
    public List<TargetReviewTeacherResponse> listClassTeacher(StringRequest request) {
        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();
        String subjectName = Optional.ofNullable(request).map(StringRequest::getStr).orElse(null);

        List<TargetReviewTeacherResponse> responses = new ArrayList<>();

        // 获取当前校区内的所有课程
        List<SubjectInfoPO> subjectInfoPOS = subjectInfoManager
                .listSubjectsByCondition(schoolId, campusId, subjectName);
        if (CollUtil.isEmpty(subjectInfoPOS)) {
            log.warn("【指标模块】【班级内的课程老师列表】未查询到课程老师列表，schoolId:{}, campusId:{}, subjectName:{}",
                    schoolId, campusId, subjectName);
            return responses;
        }

        subjectInfoPOS.forEach(subjectInfoPO ->
                responses.add(new TargetReviewTeacherResponse(subjectInfoPO.getSubjectId(),
                        String.format("班级中的%s老师", subjectInfoPO.getSubjectName()),
                        TargetReviewTypeEnum.CLASS_TEACHER.getCode()
                        )
                ));

        return responses.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public Map<Long, Boolean> checkTargetFromSport(List<Long> targetIds) {
        // 查询指标（包含逻辑删除的）
        List<Target> targets = targetMapper.listTargetByIds(targetIds);
        if (CollUtil.isEmpty(targets)) {
            return Collections.emptyMap();
        }
        //查询分组（包含逻辑删除的）
        List<Long> groupIds = targets.stream()
                .map(Target::getGroupId)
                .distinct().collect(Collectors.toList());
        List<TargetGroup> targetGroups = targetGroupMapper.listWithOutDeleted(groupIds);
        if (CollUtil.isEmpty(targetGroups)) {
            return Collections.emptyMap();
        }

        return transform(targetIds, targets, targetGroups);
    }

    private Map<Long, Boolean> transform(List<Long> targetIds, List<Target> targets,
                                                        List<TargetGroup> targetGroups) {
        Map<Long, Target> targetMap = targets
                .stream()
                .collect(Collectors.toMap(Target::getId, Function.identity()));

        Map<Long, TargetGroup> targetGroupMap = targetGroups
                .stream()
                .collect(Collectors.toMap(TargetGroup::getId, Function.identity()));

        Map<Long, Boolean> result = Maps.newHashMap();

        for (Long targetId : targetIds) {

            Target target = targetMap.get(targetId);
            if (ObjectUtil.isNull(target)) {
                continue;
            }

            TargetGroup targetGroup = targetGroupMap.get(target.getGroupId());
            if (ObjectUtil.isNull(targetGroup)) {
                continue;
            }

            boolean sportTarget;
            //体测项目
            Integer systemRecommendCode = TargetTypeEnum.SYSTEM_RECOMMEND.getCode();
            if (targetGroup.getModuleCode().equals(ModuleEnum.SPORT.getCode())
                    && systemRecommendCode.equals(target.getTargetType())
                    && systemRecommendCode.equals(targetGroup.getGroupType())) {

                sportTarget = Boolean.TRUE;
            } else {
                sportTarget = Boolean.FALSE;
            }

            result.put(targetId, sportTarget);
        }

        return result;
    }

    @Override
    public List<SubjectCommonResponse> listSubject(SubjectLikeRequest req) {
        SaasSubjectDTO saasSubjectDTO = new SaasSubjectDTO();
        saasSubjectDTO.setSchoolId(WebUtil.getSchoolIdLong());
        List<SassSubjectVO> sassSubjectVOS = saasSubjectManager.listSubject(saasSubjectDTO);
        Set<Long> subjectIds = new HashSet<>();

        return sassSubjectVOS.stream().flatMap(sassSubjectVO -> sassSubjectVO.getSubjects().stream()).map(item -> {
            Long subjectId = item.getId();
            if (subjectIds.contains(subjectId)) {
                return null;
            }
            subjectIds.add(subjectId);
            String subjectName = item.getSubjectName();
            String subjectCode = item.getSubjectCode();
            return new SubjectCommonResponse(subjectId, subjectCode,
                    subjectName);
        }).filter(Objects::nonNull).filter(s->{
            if (StringUtils.isBlank(req.getSubjectName())){
                return true;
            }
            return s.getSubjectName().contains(req.getSubjectName());
        }).collect(Collectors.toList());
    }

    /**
     * 批量保存新指标
     *
     * @param batchOperateSaveDTO
     */
    private void batchSaveTargetRecords(TargetBatchOperateSaveDTO batchOperateSaveDTO,
            Map<String/**五育名称+分组名称*/, Map<String/**指标名称*/, List<TargetBatchOperateSaveRecordDTO>>> targetMap,
            List<TargetGroup> groupInsertList) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        //批量保存指标分组
        targetGroupService.batchSave(groupInsertList);
        log.info("【web】-【指标模块】-【批量导入指标数据】批量新增指标分组记录数:{},耗时:{}", groupInsertList.size(),
                TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        //批量构建指标和指标模板信息
        batchBuildAndSaveTarget(targetMap, batchOperateSaveDTO, groupInsertList);
        log.info("【web】-【指标模块】-【批量导入指标数据】批量新增指标和指标点评人耗时:{}", TIME_INTERVAL.interval());
    }

    /**
     * 构建并保存指标相关信息
     *
     * @param targetMap
     * @param groupInsertList
     */
    private void batchBuildAndSaveTarget(
            Map<String/*五育名称+分组名称*/, Map<String/*指标名称*/, List<TargetBatchOperateSaveRecordDTO>>> targetMap,
            TargetBatchOperateSaveDTO batchOperateSaveDTO, List<TargetGroup> groupInsertList) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        Map<String/*五育分类+分组名称*/, TargetGroup> groupMap = new HashMap<>();
        for (TargetGroup targetGroup : groupInsertList) {
            groupMap.put(String.join(Constant.ROD, targetGroup.getModuleName(), targetGroup.getGroupName()),
                    targetGroup);
        }
        //新增指标集合
        List<Target> insertTargetList = new ArrayList<>();
        //指标点评人
        List<TargetUserPO> targetUserPOList = new ArrayList<>();
        //新增指标模板集合
        List<TargetTemplate> targetTemplateSaveDTOList = new ArrayList<>();
        //遍历分组中所有指标
        for (Map.Entry<String/*五育名称+分组名称*/, Map<String/*指标名称*/, List<TargetBatchOperateSaveRecordDTO>>> targetEntry : targetMap.entrySet()) {
            Map<String/*指标名称*/, List<TargetBatchOperateSaveRecordDTO>> targetNameMap = targetEntry.getValue();
            //指标排序
            AtomicInteger targetSortIndex = new AtomicInteger(1);
            //遍历指标中的所有选项
            for (Map.Entry<String, List<TargetBatchOperateSaveRecordDTO>> targetNameMapEntry : targetNameMap.entrySet()) {
                //指标对应所有选项信息
                List<TargetBatchOperateSaveRecordDTO> targetBatchOperateSaveRecordDTOS = targetNameMapEntry.getValue();
                TargetBatchOperateSaveRecordDTO saveRecordDTO = targetBatchOperateSaveRecordDTOS.get(0);
                String groupName = saveRecordDTO.getGroupName();
                TargetGroup targetGroup = groupMap.get(
                        String.join(Constant.ROD, saveRecordDTO.getModuleName(), saveRecordDTO.getGroupName()));
                if (Objects.isNull(targetGroup)) {
                    log.error("【web】-【指标模块】-【批量导入指标数据】指标分组不存在，分组名称:{}", groupName);
                    throw new BizException("指标分组不存在");
                }
                //构建target对象
                Target target = buildDefaultTarget(targetGroup, saveRecordDTO.getTargetName(), batchOperateSaveDTO,
                        targetSortIndex);
                insertTargetList.add(target);
                //构建targetUser对象
                targetUserPOList.addAll(buildDefaultTargetUser(target, batchOperateSaveDTO));
                List<TemplateInfoSaveDTO> templateInfoList = new ArrayList<>();
                //TemplateInfoSaveDTO中的key 组成 {雪花id}+{index}
                //key的雪花id
                long keySnowFlake = SnowFlakeIdUtil.nextId();
                //key后缀起始值
                AtomicInteger keyIndex = new AtomicInteger(TargetConstant.TEMPLATE_INFO_KEY_FROM_INDEX);
                //构建指标表单模板信息
                buildTargetTemplateInfo(keySnowFlake, keyIndex, templateInfoList, targetBatchOperateSaveRecordDTOS,
                        batchOperateSaveDTO, target, targetTemplateSaveDTOList);
            }
        }
        //批量插入指标表单数据
        Collection<TargetTemplate> targetTemplates = templateService.bulkSave(targetTemplateSaveDTOList);
        log.info("【web】-【指标模块】-【批量导入指标数据】批量保存指标表单数据:{},耗时:{}", targetTemplateSaveDTOList.size(),
                TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        if (CollectionUtils.isEmpty(targetTemplates)) {
            return;
        }
        Map<Long/**targetId*/, TargetTemplate> targetTemplateMap = new HashMap<>();
        for (TargetTemplate targetTemplate : targetTemplates) {
            targetTemplateMap.put(targetTemplate.getTargetId(), targetTemplate);
        }
        if (CollectionUtils.isEmpty(insertTargetList)) {
            return;
        }
        //设置templateId
        for (Target target : insertTargetList) {
            TargetTemplate targetTemplate = targetTemplateMap.get(target.getId());
            if (Objects.isNull(targetTemplate)) {
                log.error("【web】-【指标模块】-【批量导入指标数据】指标表单模板信息不存在，指标信息:{}",
                        JSONObject.toJSONString(target));
                throw new BizException("保存失败");
            }
            target.setTemplateId(targetTemplate.getId());
            target.setTemplateInfoJson(JSONObject.toJSONString(targetTemplate));
        }
        //批量保存指标信息
        List<List<Target>> targetPartition = Lists.partition(insertTargetList, TargetConstant.TARGET_BATCH_INSERT_SIZE);
        for (List<Target> targets : targetPartition) {
            this.saveBatch(targets);
        }
        log.info("【web】-【指标模块】-【批量导入指标数据】批量新增指标记录数:{},耗时:{}", insertTargetList.size(),
                TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        //批量保存指标可点评人信息
        List<List<TargetUserPO>> targetUserPartition = Lists.partition(targetUserPOList, Constant.BATCH_INSERT_SIZE);
        for (List<TargetUserPO> targetUserPOS : targetUserPartition) {
            targetUserManager.batchSave(targetUserPOS);
        }
        log.info("【web】-【指标模块】-【批量导入指标数据】批量新增指标可点评人记录数:{},耗时:{}", targetUserPOList.size(),
                TIME_INTERVAL.interval());
    }

    /**
     * 构建指标表单模板信息
     *
     * @param keySnowFlake
     * @param keyIndex
     * @param templateInfoList
     * @param targetBatchOperateSaveRecordDTOS
     * @param target
     * @param targetTemplateSaveDTOList
     */
    private void buildTargetTemplateInfo(long keySnowFlake, AtomicInteger keyIndex,
            List<TemplateInfoSaveDTO> templateInfoList,
            List<TargetBatchOperateSaveRecordDTO> targetBatchOperateSaveRecordDTOS,
            TargetBatchOperateSaveDTO batchOperateSaveDTO,
            Target target, List<TargetTemplate> targetTemplateSaveDTOList) {
        //构建学生控件
        TemplateInfoSaveDTO templateInfoSaveDTO = buildDefaultStudentTemplateInfo(keySnowFlake, keyIndex);
        templateInfoList.add(templateInfoSaveDTO);
        //指标控件分类map
        Map<String/*选项名称+选项类型*/, List<TargetBatchOperateSaveRecordDTO>> templateInfoMap = new LinkedHashMap<>();
        //对控件分类
        for (TargetBatchOperateSaveRecordDTO targetBatchOperateSaveRecordDTO : targetBatchOperateSaveRecordDTOS) {
            String key = String.join(Constant.ROD, targetBatchOperateSaveRecordDTO.getTemplateInfoName(),
                    targetBatchOperateSaveRecordDTO.getTypeName());
            templateInfoMap.computeIfAbsent(key, (k) -> new ArrayList<>()).add(targetBatchOperateSaveRecordDTO);
        }
        //遍历控件中的选项
        for (Map.Entry<String, List<TargetBatchOperateSaveRecordDTO>> targetTemplateEntry : templateInfoMap.entrySet()) {
            List<TargetBatchOperateSaveRecordDTO> optionList = targetTemplateEntry.getValue();
            //构建默认模板信息
            TemplateInfoSaveDTO defaultTemplateInfo = buildDefaultTemplateInfo(keySnowFlake, keyIndex,
                    optionList.get(0));
            //构建表单控件所有选项
            defaultTemplateInfo.setOptions(buildTemplateOptionList(optionList, keySnowFlake, keyIndex));
            templateInfoList.add(defaultTemplateInfo);
        }
        //构建指标表单信息
        TargetTemplate templateSave = new TargetTemplate()
                .setTemplateInfoList(templateInfoList)
                .setTargetId(target.getId());
        templateSave.setCreateBy(batchOperateSaveDTO.getOperatorId() + "");
        templateSave.setCreateTime(DateUtil.date());
        templateSave.setUpdateTime(DateUtil.date());
        templateSave.setDeleted(Constant.ZERO);
        targetTemplateSaveDTOList.add(templateSave);
    }

    /**
     * 构建默认指标点评人
     *
     * @param target
     * @param batchOperateSaveDTO
     * @return
     */
    private List<TargetUserPO> buildDefaultTargetUser(Target target, TargetBatchOperateSaveDTO batchOperateSaveDTO) {

        List<TargetUserPO> evaluateTargetUserPOList = convert.toEvaluateTargetUserList(batchOperateSaveDTO.getTargetUserList());

        for (TargetUserPO targetUserPO : evaluateTargetUserPOList) {
            targetUserPO.setTargetId(target.getId());
            targetUserPO.setId(SnowFlakeIdUtil.nextId());
            targetUserPO.setTenantId(batchOperateSaveDTO.getTenantId());
            targetUserPO.setSchoolId(batchOperateSaveDTO.getSchoolId());
            targetUserPO.setCampusId(batchOperateSaveDTO.getCampusId());
            targetUserPO.setTargetId(target.getId());
            // 头像去除华为云链接前缀
            targetUserPO.setIconUrl(StrUtil.isBlank(targetUserPO.getIconUrl()) ? targetUserPO.getIconUrl()
                    : targetUserPO.getIconUrl().substring(saasIconPrefix.length() + 1));

            // 教职工和家长，userCount处理为1
            Boolean flag = TargetUserSubmitTypeEnum.STAFF.getCode().equals(targetUserPO.getSubmitType())
                    || TargetUserSubmitTypeEnum.STUDENT.getCode().equals(targetUserPO.getSubmitType());
            targetUserPO.setUserCount(flag ? Constant.ONE : targetUserPO.getUserCount());
            targetUserPO.setCreateBy(batchOperateSaveDTO.getOperatorId() + "");
            targetUserPO.setUpdateBy(batchOperateSaveDTO.getOperatorId() + "");
            targetUserPO.setDeleted(Boolean.FALSE);
        }

        return evaluateTargetUserPOList;
    }

    /**
     * 构建默认的指标对象
     *
     * @param targetGroup
     * @param targetName
     * @param batchOperateSaveDTO
     * @param targetSortIndex
     * @return
     */
    private Target buildDefaultTarget(TargetGroup targetGroup, String targetName,
                                      TargetBatchOperateSaveDTO batchOperateSaveDTO, AtomicInteger targetSortIndex) {
        Target target = new Target();
        target.setId(SnowFlakeIdUtil.nextId());
        target.setGroupId(targetGroup.getId());
        target.setIconUrl(defaultIcon);
        target.setTargetName(targetName);
        target.setTargetStatus(Constant.YES);
        target.setTenantId(batchOperateSaveDTO.getTenantId());
        target.setSchoolId(batchOperateSaveDTO.getSchoolId());
        target.setCampusId(batchOperateSaveDTO.getCampusId());
        target.setSortIndex(targetSortIndex.getAndIncrement());
        target.setDataSource(DataSourceEnum.EVALUATE.getCode());
        target.setHolidayNoticeFlag(Constant.YES);
        target.setMustSubmitFlag(Constant.NO);
        target.setUrgeTime(Constant.TWENTY_FOUR);
        target.setSendParentFlag(Constant.YES);
        target.setCreateBy(batchOperateSaveDTO.getOperatorId() + "");
        target.setUpdateBy(batchOperateSaveDTO.getOperatorId() + "");
        target.setDeleted(Boolean.FALSE);
        //导入用户传入参数
        Integer submitFrequencyFlag = batchOperateSaveDTO.getSubmitFrequencyFlag();
        target.setSubmitType(Constant.ONE.equals(submitFrequencyFlag) ? SubmitRateEnum.TERM.getSubmitType() :
                SubmitRateEnum.NO_RATE.getSubmitType());
        if (SubmitRateEnum.TERM.getSubmitType().equals(submitFrequencyFlag)){
            target.setParentSubmitLimitFlag(Constant.ONE);
            target.setParentSubmitLimitTimes(Constant.ONE);
        }
        target.setStaffFullCheckFlag(batchOperateSaveDTO.getStaffFullCheckFlag());
        target.setNotPartExchange(batchOperateSaveDTO.getNotPartExchange());
        target.setTargetNotPartCount(batchOperateSaveDTO.getTargetNotPartCount());

        return target;
    }

    /**
     * 构建指标模板信息中控件对应的选项
     *
     * @param optionRecordList
     * @return
     */
    private TemplateInfoSaveDTO.InnerSubmitOptionInfoSave buildTemplateOptionList(
            List<TargetBatchOperateSaveRecordDTO> optionRecordList, long keySnowFlake, AtomicInteger keyIndex) {
        TemplateInfoSaveDTO.InnerSubmitOptionInfoSave innerSubmitOptionInfoSave = new TemplateInfoSaveDTO.InnerSubmitOptionInfoSave();
        List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> options = new ArrayList<>();
        //遍历构建指标控件对应的选项
        for (TargetBatchOperateSaveRecordDTO targetBatchOperateSaveRecordDTO : optionRecordList) {
            TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave innerSubmitOptionInfoSubSave = new TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave();
            innerSubmitOptionInfoSubSave.setKey(
                    String.join(Constant.ROD, keySnowFlake + "", keyIndex.getAndIncrement() + ""));
            innerSubmitOptionInfoSubSave.setLabel(targetBatchOperateSaveRecordDTO.getOption());
            innerSubmitOptionInfoSubSave.setValue(targetBatchOperateSaveRecordDTO.getScore());
            options.add(innerSubmitOptionInfoSubSave);
        }
        innerSubmitOptionInfoSave.setOptions(options);
        return innerSubmitOptionInfoSave;
    }

    /**
     * 构建默认的控件
     *
     * @param keySnowFlake
     * @param keyIndex
     */
    private TemplateInfoSaveDTO buildDefaultTemplateInfo(long keySnowFlake, AtomicInteger keyIndex,
            TargetBatchOperateSaveRecordDTO targetBatchOperateSaveRecordDTO) {
        TemplateInfoSaveDTO templateInfoSaveDTO = new TemplateInfoSaveDTO();
        TargetTemplateInfoTypeEnum templateInfoTypeEnum = TargetTemplateInfoTypeEnum.getByDesc(
                targetBatchOperateSaveRecordDTO.getTypeName());
        if (Objects.isNull(templateInfoTypeEnum)) {
            log.error("【web】-【指标模块】-【批量导入指标数据】选项不存在，选项名称:{}",
                    targetBatchOperateSaveRecordDTO.getTypeName());
            throw new BizException("选项不存在");
        }
        templateInfoSaveDTO.setType(templateInfoTypeEnum.getType());
        templateInfoSaveDTO.setTypeName(templateInfoTypeEnum.getDesc());
        templateInfoSaveDTO.setName(targetBatchOperateSaveRecordDTO.getTemplateInfoName());
        templateInfoSaveDTO.setNameMaxlength(Constant.HUNDRED);
        templateInfoSaveDTO.setRequired(Boolean.TRUE);
        templateInfoSaveDTO.setIsScore(Boolean.TRUE);
        templateInfoSaveDTO.setIsAdjustScore(Boolean.FALSE);
        templateInfoSaveDTO.setAdjustScore(BigDecimal.ONE);
        templateInfoSaveDTO.setKey(String.join(Constant.ROD, keySnowFlake + "", keyIndex.getAndIncrement() + ""));
        return templateInfoSaveDTO;
    }

    /**
     * 构建默认的学生控件
     *
     * @param keySnowFlake
     * @param keyIndex
     */
    private TemplateInfoSaveDTO buildDefaultStudentTemplateInfo(long keySnowFlake, AtomicInteger keyIndex) {
        TemplateInfoSaveDTO templateInfoSaveDTO = new TemplateInfoSaveDTO();
        templateInfoSaveDTO.setType(TargetTemplateInfoTypeEnum.STUDENT.getType());
        templateInfoSaveDTO.setTypeName(TargetTemplateInfoTypeEnum.STUDENT.getDesc());
        templateInfoSaveDTO.setName(TargetTemplateInfoTypeEnum.STUDENT.getDesc());
        templateInfoSaveDTO.setNameMaxlength(Constant.TEN);
        templateInfoSaveDTO.setRequired(Boolean.TRUE);
        templateInfoSaveDTO.setIsMultiple(Boolean.TRUE);
        templateInfoSaveDTO.setKey(String.join(Constant.ROD, keySnowFlake + "", keyIndex.getAndIncrement() + ""));
        templateInfoSaveDTO.setOptions(new TemplateInfoSaveDTO.InnerSubmitOptionInfoSave());
        return templateInfoSaveDTO;
    }

    /**
     * 构建并填充分组
     */
    private void buildAndFillTartgetGroup(String groupName, String moduleName,
            Map<String, Map<String, TargetGroup>> groupMap, TargetBatchOperateSaveDTO batchOperateSaveDTO) {
        ModuleEnum moduleEnum = ModuleEnum.getNormalModuleByModuleName(moduleName);
        if (Objects.isNull(moduleEnum)) {
            throw new BizException("五育模块选择错误");
        }
        Map<String, TargetGroup> targetGroups = groupMap.computeIfAbsent(moduleEnum.getMessage(),
                (key) -> new HashMap<>());
        TargetGroup existGroup = targetGroups.get(groupName);
        if (Objects.nonNull(existGroup)) {
            return;
        }
        int sortIndex = targetGroups.size() + 1;
        TargetGroup targetGroup = new TargetGroup();
        targetGroup.setId(SnowFlakeIdUtil.nextId());
        targetGroup.setGroupName(groupName);
        targetGroup.setModuleCode(moduleEnum.getCode());
        targetGroup.setModuleName(moduleEnum.getMessage());
        targetGroup.setSortIndex(sortIndex);
        targetGroup.setDeleted(Boolean.FALSE);
        targetGroup.setTenantId(batchOperateSaveDTO.getTenantId());
        targetGroup.setDataSource(DataSourceEnum.EVALUATE.getCode());
        targetGroup.setSchoolId(batchOperateSaveDTO.getSchoolId());
        targetGroup.setCampusId(batchOperateSaveDTO.getCampusId());
        targetGroup.setCreateBy(batchOperateSaveDTO.getOperatorId() + "");
        targetGroup.setUpdateBy(batchOperateSaveDTO.getOperatorId() + "");
        targetGroups.put(groupName, targetGroup);
    }

    /**
     * 处理现存指标
     *
     * @param batchOperateSaveDTO
     */
    private void dealExistTargetAndGroup(TargetBatchOperateSaveDTO batchOperateSaveDTO,
            List<TargetGroup> groupInsertList) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        //1.查询对应分组
        TargetGroupListByConditionsDaoDTO targetGroupListByConditionsDaoDTO = new TargetGroupListByConditionsDaoDTO();
        targetGroupListByConditionsDaoDTO.setCampusId(batchOperateSaveDTO.getCampusId());
        targetGroupListByConditionsDaoDTO.setModuleCodeList(batchOperateSaveDTO.getModuleCodes());
        targetGroupListByConditionsDaoDTO.setDatasource(DataSourceEnum.EVALUATE.getCode());
        targetGroupListByConditionsDaoDTO.setGroupType(TargetTypeEnum.DEFAULT.getCode());
        List<TargetGroup> targetGroups = targetGroupService.listGroupByConditions(targetGroupListByConditionsDaoDTO);
        log.info("【web】-【指标模块】-【批量导入指标数据】查询理已存在指标分组数:{},耗时:{}", targetGroups.size(),
                TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        if (CollectionUtils.isEmpty(targetGroups)) {
            //不存在分组，即不存在指标
            return;
        }
        List<Long> targetGroupIdList = targetGroups.stream().map(TargetGroup::getId).collect(Collectors.toList());
        //2.修改或删除指标/指标分组，并发送事件通知
        dealExistTargetGroup(targetGroups, batchOperateSaveDTO, groupInsertList);
        log.info("【web】-【指标模块】-【批量导入指标数据】处理已存在指标分组数:{},耗时:{}", targetGroups.size(),
                TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        dealExistTarget(targetGroupIdList, batchOperateSaveDTO);
        log.info("【web】-【指标模块】-【批量导入指标数据】处理已存在指标数耗时:{}", TIME_INTERVAL.interval());
    }

    /**
     * 处理已存在的指标
     *
     * @param targetGroupIdList
     * @param batchOperateSaveDTO
     */
    private void dealExistTarget(List<Long> targetGroupIdList, TargetBatchOperateSaveDTO batchOperateSaveDTO) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        BaseTargetEvent<Target> baseEvent = null;
        //根据分组查询对应生效的指标
        TargetQO targetQO = new TargetQO();
        targetQO.setDataSource(DataSourceEnum.EVALUATE.getCode());
        targetQO.setTargetGroupIdList(targetGroupIdList);
        targetQO.setTargetType(TargetTypeEnum.DEFAULT.getCode());
        if (Objects.equals(batchOperateSaveDTO.getExistDealType(),
                TargetBatchOperateExistDealType.FORBIDDEN.getType())) {
            //禁用指标，只查询目前生效的指标
            targetQO.setStatus(Constant.ONE);
        }
        List<Target> targets = targetMapper.queryByCondition(targetQO);
        log.info("【web】-【指标模块】-【批量导入指标数据】根据分组查询对应生效的指标:{},耗时:{}", targets.size(),
                TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        if (CollectionUtils.isEmpty(targets)) {
            //不存在指标
            return;
        }
        List<Long> targetIdList = targets.stream().map(Target::getId).collect(Collectors.toList());
        if (Objects.equals(batchOperateSaveDTO.getExistDealType(), TargetBatchOperateExistDealType.DELETED.getType())) {
            //批量设置删除指标
            baseMapper.batchDeleteByIds(targetIdList, batchOperateSaveDTO.getOperatorId());
            //批量设置指标可点评人的删除标识
            targetUserManager.batchDeletedByTargetIds(targetIdList, batchOperateSaveDTO.getOperatorId());
            log.info("【web】-【指标模块】-【批量导入指标数据】删除已存在指标数:{},耗时:{}", targetIdList.size(),
                    TIME_INTERVAL.interval());
            TIME_INTERVAL.restart();
            baseEvent = new TargetDeletedEvent(targets);
        } else if (Objects.equals(batchOperateSaveDTO.getExistDealType(),
                TargetBatchOperateExistDealType.FORBIDDEN.getType())) {
            //批量设置禁用指标
            baseMapper.batchUpdateStatus(targetIdList, Constant.ZERO, batchOperateSaveDTO.getOperatorId());
            targets.forEach(tar -> tar.setTargetStatus(Constant.ZERO));
            baseEvent = new TargetUpdateStatusEvent(targets);
            log.info("【web】-【指标模块】-【批量导入指标数据】禁用已存在指标数:{},耗时:{}", targetIdList.size(),
                    TIME_INTERVAL.interval());
            TIME_INTERVAL.restart();
        }
        if (Objects.nonNull(baseEvent)) {
            //发送删除或禁用事件
            eventCenter.publish(baseEvent);
            log.info("【web】-【指标模块】-【批量导入指标数据】指标删除或禁用事件处理耗时:{}", TIME_INTERVAL.interval());
        }
    }

    /**
     * 处理已存在的指标分组
     *
     * @param targetGroups
     * @param batchOperateSaveDTO
     */
    private void dealExistTargetGroup(List<TargetGroup> targetGroups, TargetBatchOperateSaveDTO batchOperateSaveDTO,
            List<TargetGroup> groupInsertList) {
        if (CollectionUtils.isEmpty(targetGroups)) {
            return;
        }
        List<Long> groupIds = targetGroups.stream().map(TargetGroup::getId).collect(Collectors.toList());
        if (Objects.equals(batchOperateSaveDTO.getExistDealType(),
                TargetBatchOperateExistDealType.FORBIDDEN.getType())) {
            //指标禁用的情况
            Map<Integer, List<TargetGroup>> targetGroupModuleCodeMap = targetGroups.stream()
                    .collect(Collectors.groupingBy(TargetGroup::getModuleCode));
            //要新增的指标分组五育对应情况
            Map<Integer, List<TargetGroup>> insertGroupModuleCodeMap = groupInsertList.stream()
                    .collect(Collectors.groupingBy(TargetGroup::getModuleCode));
            for (Map.Entry<Integer, List<TargetGroup>> moduleEntry : targetGroupModuleCodeMap.entrySet()) {
                Integer moduleCode = moduleEntry.getKey();
                List<TargetGroup> targetGroupsModuleList = moduleEntry.getValue();
                if (CollectionUtils.isEmpty(targetGroupsModuleList)) {
                    continue;
                }
                List<TargetGroup> insertTargetGroups = insertGroupModuleCodeMap.get(moduleCode);
                if (CollectionUtils.isEmpty(insertTargetGroups)) {
                    //如果对应五育下没有要新增的分组，无需更改原分组的排名信息
                    continue;
                }
                //查询对应五育下分组的最大排序序号
                int maxSortIndex = insertTargetGroups.size();
                for (TargetGroup targetGroup : targetGroupsModuleList) {
                    targetGroup.setSortIndex(++maxSortIndex);
                }
                List<Long> moduleGroupIdList = targetGroupsModuleList.stream().map(TargetGroup::getId)
                        .collect(Collectors.toList());
                //批量更新禁用的序号
                targetGroupService.batchUpdateSortIndex(moduleGroupIdList, targetGroupsModuleList);
            }
            return;
        }
        //只有删除的时候需要同时删除分组
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        targetGroupService.deleteDirectByIds(groupIds, batchOperateSaveDTO.getOperatorId());
        log.info("【web】-【指标模块】-【批量导入指标数据】删除指标分组数:{},耗时:{}", targetGroups.size(),
                TIME_INTERVAL.interval());
        TIME_INTERVAL.restart();
        //发布指标分组批量删除事件
        eventCenter.publish(new TargetGroupDeletedEvent(targetGroups));
        log.info("【web】-【指标模块】-【批量导入指标数据】指标分组删除事件处理耗时:{}", TIME_INTERVAL.interval());
    }

    /**
     * 校验批量导入参数
     *
     * @param batchOperateSaveDTO
     */
    private TargetBatchSaveResponse validateBatchRecord(TargetBatchOperateSaveDTO batchOperateSaveDTO) {
        //根据token获取userId
        UserInfoVO userInfo = basicInfoService.getUserInfoFromToken(batchOperateSaveDTO.getToken());
        if (Objects.isNull(userInfo) || Objects.isNull(userInfo.getId())) {
            throw new BizException("系统繁忙，请稍后重试");
        }
        MenuAuthCheckDTO menuAuthCheckDTO = new MenuAuthCheckDTO();
        menuAuthCheckDTO.setAuthCode(batchImportButtonCode);
        menuAuthCheckDTO.setUserId(userInfo.getId());
        menuAuthCheckDTO.setTenantId(Long.valueOf(batchOperateSaveDTO.getTenantId()));
        //校验用户是否有批量上传权限
        if (!basicInfoService.checkMenuAuth(menuAuthCheckDTO)) {
            throw new BizException("无权限操作");
        }
        List<TargetBatchOperateSaveRecordDTO> recordList = batchOperateSaveDTO.getRecordList();
        if (CollectionUtils.isEmpty(recordList)) {
            recordList = Collections.emptyList();
        }
        if (recordList.size() > 3000) {
            throw new BizException("最多支持导入3000条记录");
        }
        if (Objects.isNull(batchOperateSaveDTO.getExistDealType())) {
            throw new BizException("请先选择现存指标的处理方式");
        }
        if (CollectionUtils.isEmpty(batchOperateSaveDTO.getModuleCodes())) {
            throw new BizException("请选择要操作的五育模块");
        }
        if (CollUtil.isEmpty(batchOperateSaveDTO.getTargetUserList())){
            throw new BizException("请选择指标填写人");
        }
        List<Integer> moduleCodes = batchOperateSaveDTO.getModuleCodes();
        Set<ModuleEnum> selectedModuleSet = new HashSet<>();
        List<String> moduleNameList = new ArrayList<>();
        for (Integer moduleCode : moduleCodes) {
            ModuleEnum moduleEnum = ModuleEnum.getNormalModuleByModuleCode(moduleCode);
            if (Objects.isNull(moduleEnum)) {
                throw new BizException("请选择正确的五育模块范围");
            }
            selectedModuleSet.add(moduleEnum);
            moduleNameList.add(moduleEnum.getMessage());
        }
        String moduleErrorMsg = "不在选择的" + StringUtils.join(Constant.CHINESE_COMMA, moduleNameList) + "范围内";
        //设置学校机构id，作为指标点评人
        SchoolQueryDTO schoolQueryDTO = new SchoolQueryDTO();
        schoolQueryDTO.setSchoolId(Long.valueOf(batchOperateSaveDTO.getSchoolId()));
        List<AdminOrgTreeVO> adminOrgTreeVOList = basicInfoRemote.listOrgTreeBySchoolId(schoolQueryDTO);
        AssertUtil.checkNotNull(adminOrgTreeVOList, "学校机构为空!");
        AdminOrgTreeVO orgTreeVO = CollUtil.getFirst(adminOrgTreeVOList);
        batchOperateSaveDTO.setSchoolOrgId(orgTreeVO.getOrgId());
        batchOperateSaveDTO.setSchoolOrgName(orgTreeVO.getOrgName());
        //构建错误返回值
        TargetBatchSaveResponse targetBatchSaveResponse = new TargetBatchSaveResponse();
        List<TargetBatchSaveFailRecordResponse> recordResponses = new ArrayList<>();
        //校验批量导入指标记录
        checkTargetRecordList(recordList, recordResponses, moduleErrorMsg, selectedModuleSet);
        targetBatchSaveResponse.setOperateSuccess(Boolean.TRUE);
        //存在错误记录，则设置操作失败
        if (CollectionUtils.isNotEmpty(recordResponses)) {
            targetBatchSaveResponse.setOperateSuccess(Boolean.FALSE);
        }
        targetBatchSaveResponse.setFailRecords(recordResponses);
        return targetBatchSaveResponse;
    }

    private void checkTargetRecordList(List<TargetBatchOperateSaveRecordDTO> recordList,
            List<TargetBatchSaveFailRecordResponse> recordResponses, String moduleErrorMsg,
            Set<ModuleEnum> selectedModuleSet) {
        for (TargetBatchOperateSaveRecordDTO targetBatchOperateSaveRecordDTO : recordList) {
            if (StringUtils.isBlank(targetBatchOperateSaveRecordDTO.getModuleName())) {
                recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                        TargetBatchSaveColumnEnum.MODULE_NAME.getIndex(),
                        TargetBatchSaveColumnEnum.MODULE_NAME.getKey(), "五育分类不能为空"));
            } else {
                targetBatchOperateSaveRecordDTO.setModuleName(targetBatchOperateSaveRecordDTO.getModuleName().trim());
                ModuleEnum moduleEnum = ModuleEnum.getNormalModuleByModuleName(
                        targetBatchOperateSaveRecordDTO.getModuleName());
                if (Objects.isNull(moduleEnum)) {
                    recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                            TargetBatchSaveColumnEnum.MODULE_NAME.getIndex(),
                            TargetBatchSaveColumnEnum.MODULE_NAME.getKey(), moduleErrorMsg));
                }
                if (!selectedModuleSet.contains(moduleEnum)) {
                    recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                            TargetBatchSaveColumnEnum.MODULE_NAME.getIndex(),
                            TargetBatchSaveColumnEnum.MODULE_NAME.getKey(), moduleErrorMsg));
                }
            }
            if (StringUtils.isBlank(targetBatchOperateSaveRecordDTO.getGroupName())) {
                recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                        TargetBatchSaveColumnEnum.GROUP_NAME.getIndex(), TargetBatchSaveColumnEnum.GROUP_NAME.getKey(),
                        "分组名称不能为空"));
            } else {
                targetBatchOperateSaveRecordDTO.setGroupName(targetBatchOperateSaveRecordDTO.getGroupName().trim());
                if (targetBatchOperateSaveRecordDTO.getGroupName().length() > 10) {
                    recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                            TargetBatchSaveColumnEnum.GROUP_NAME.getIndex(),
                            TargetBatchSaveColumnEnum.GROUP_NAME.getKey(), "分组名称不能超过10个字"));
                }
            }
            if (StringUtils.isBlank(targetBatchOperateSaveRecordDTO.getTargetName())) {
                recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                        TargetBatchSaveColumnEnum.TARGET_NAME.getIndex(),
                        TargetBatchSaveColumnEnum.TARGET_NAME.getKey(), "指标名称不能为空"));
            } else {
                targetBatchOperateSaveRecordDTO.setTargetName(targetBatchOperateSaveRecordDTO.getTargetName().trim());
                if (targetBatchOperateSaveRecordDTO.getTargetName().length() > 6) {
                    recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                            TargetBatchSaveColumnEnum.TARGET_NAME.getIndex(),
                            TargetBatchSaveColumnEnum.TARGET_NAME.getKey(), "指标名称不能超过6个字"));
                }
            }
            if (StringUtils.isBlank(targetBatchOperateSaveRecordDTO.getTemplateInfoName())) {
                recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                        TargetBatchSaveColumnEnum.TEMPLATE_INFO_NAME.getIndex(),
                        TargetBatchSaveColumnEnum.TEMPLATE_INFO_NAME.getKey(), "选项名称不能为空"));
            } else {
                targetBatchOperateSaveRecordDTO.setTemplateInfoName(
                        targetBatchOperateSaveRecordDTO.getTemplateInfoName().trim());
                if (targetBatchOperateSaveRecordDTO.getTemplateInfoName().length() > 100) {
                    recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                            TargetBatchSaveColumnEnum.TEMPLATE_INFO_NAME.getIndex(),
                            TargetBatchSaveColumnEnum.TEMPLATE_INFO_NAME.getKey(), "选项名称不能超过100个字"));
                }
            }
            if (StringUtils.isBlank(targetBatchOperateSaveRecordDTO.getTypeName())) {
                recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                        TargetBatchSaveColumnEnum.TYPE_NAME.getIndex(), TargetBatchSaveColumnEnum.TYPE_NAME.getKey(),
                        "选项类型不能为空"));
            } else {
                targetBatchOperateSaveRecordDTO.setTypeName(targetBatchOperateSaveRecordDTO.getTypeName().trim());
                TargetTemplateInfoTypeEnum infoTypeEnum = TargetTemplateInfoTypeEnum.getByDesc(
                        targetBatchOperateSaveRecordDTO.getTypeName());
                if (Objects.equals(infoTypeEnum.getType(), TargetTemplateInfoTypeEnum.NULL.getType())) {
                    recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                            TargetBatchSaveColumnEnum.TYPE_NAME.getIndex(),
                            TargetBatchSaveColumnEnum.TYPE_NAME.getKey(), "选项类型只能为单选或者多选"));
                }
            }
            if (StringUtils.isBlank(targetBatchOperateSaveRecordDTO.getOption())) {
                recordResponses.add(
                        buildErrorRecord(targetBatchOperateSaveRecordDTO, TargetBatchSaveColumnEnum.OPTION.getIndex(),
                                TargetBatchSaveColumnEnum.OPTION.getKey(), "点评项不能为空"));
            } else {
                targetBatchOperateSaveRecordDTO.setOption(targetBatchOperateSaveRecordDTO.getOption().trim());
                if (targetBatchOperateSaveRecordDTO.getOption().length() > 100) {
                    recordResponses.add(buildErrorRecord(targetBatchOperateSaveRecordDTO,
                            TargetBatchSaveColumnEnum.OPTION.getIndex(), TargetBatchSaveColumnEnum.OPTION.getKey(),
                            "点评项不能超过100个字"));
                }
            }
            if (Objects.isNull(targetBatchOperateSaveRecordDTO.getScore())) {
                targetBatchOperateSaveRecordDTO.setScore(BigDecimal.ZERO);
            }
            if (targetBatchOperateSaveRecordDTO.getScore().compareTo(TargetConstant.SCORE_MAX_VALUE) > 0) {
                //大于最大值
                recordResponses.add(
                        buildErrorRecord(targetBatchOperateSaveRecordDTO, TargetBatchSaveColumnEnum.SCORE.getIndex(),
                                TargetBatchSaveColumnEnum.SCORE.getKey(), "分数取值范围为-9999～9999"));
            } else if (targetBatchOperateSaveRecordDTO.getScore().compareTo(TargetConstant.SCORE_MIN_VALUE) < 0) {
                //小于最小值
                recordResponses.add(
                        buildErrorRecord(targetBatchOperateSaveRecordDTO, TargetBatchSaveColumnEnum.SCORE.getIndex(),
                                TargetBatchSaveColumnEnum.SCORE.getKey(), "分数取值范围为-9999～9999"));
            }
            if (Objects.nonNull(targetBatchOperateSaveRecordDTO.getScore())) {
                targetBatchOperateSaveRecordDTO.setScore(
                        targetBatchOperateSaveRecordDTO.getScore().setScale(2, RoundingMode.HALF_UP));
            }
        }
    }

    /**
     * 构建导入的错误记录返回值
     *
     * @param targetBatchOperateSaveRecordDTO
     * @param columnIndex
     * @param errorMsg
     * @return
     */
    private TargetBatchSaveFailRecordResponse buildErrorRecord(
            TargetBatchOperateSaveRecordDTO targetBatchOperateSaveRecordDTO, int columnIndex, String columnKey,
            String errorMsg) {
        return TargetBatchSaveFailRecordResponse
                .builder()
                .rowIndex(targetBatchOperateSaveRecordDTO.getRowIndex())
                .columnIndex(columnIndex)
                .columnKey(columnKey)
                .errorMsg(errorMsg).build();
    }

    /**
     * 获取体测分组Id，如果不存在就创建
     *
     * @return {@link Long}
     */
    private Long getOrCreateSportGroupId() {
        String tenantId = WebUtil.getTenantId();
        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();
        // 查看表里有没有分组，没有自动创建，有的话 使用表里面的分组
        List<TargetGroup> targetGroups = targetGroupService.list(new LambdaQueryWrapper<TargetGroup>()
                .eq(TargetGroup::getModuleCode, ModuleEnum.SPORT.getCode())
                .eq(TargetGroup::getCampusId, campusId)
                .eq(TargetGroup::getGroupName, DataSourceEnum.SPORT.getMessage())
                .eq(TargetGroup::getDataSource, DataSourceEnum.SPORT.getCode())
                .orderByAsc(TargetGroup::getCreateTime));

        if (CollUtil.isNotEmpty(targetGroups)) {
            if (targetGroups.size() > 1) {
                log.warn("请注意，体测分组有多个，校区id：{}", campusId);
            }
            log.info("已有体测分组, id：{}", targetGroups.get(0).getId());
            return targetGroups.get(0).getId();
        }

        TargetGroup targetGroup = new TargetGroup();
        targetGroup.setTenantId(tenantId);
        targetGroup.setSchoolId(schoolId);
        targetGroup.setCampusId(campusId);
        targetGroup.setModuleCode(ModuleEnum.SPORT.getCode());
        targetGroup.setModuleName(ModuleEnum.SPORT.getMessage());
        targetGroup.setGroupName(DataSourceEnum.SPORT.getMessage());
        targetGroup.setSortIndex(999);
        targetGroup.setDataSource(DataSourceEnum.SPORT.getCode());
        targetGroup.setCreateBy("system");
        targetGroup.setCreateTime(DateUtil.date());

        targetGroupService.save(targetGroup);
        log.info("无体测分组，已新建，id：{}", targetGroup.getId());

        return targetGroup.getId();
    }

    /**
     * 获取或创建体测指标Id
     *
     * @param groupId 分组Id
     * @return {@link Long}
     */
    private Long getOrCreateSportTargetId(Long groupId) {
        String tenantId = WebUtil.getTenantId();
        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();
        // 查看表里有没有分组，没有自动创建，有的话 使用表里面的分组
        List<Target> targets = this.list(new LambdaQueryWrapper<Target>()
                .eq(Target::getTargetName, DataSourceEnum.SPORT.getMessage())
                .eq(Target::getCampusId, campusId)
                .eq(Target::getDataSource, DataSourceEnum.SPORT.getCode())
                .orderByAsc(Target::getCreateTime));

        if (CollUtil.isNotEmpty(targets)) {
            if (targets.size() > 1) {
                log.warn("请注意，体测指标有多个，校区id：{}", campusId);
            }
            log.info("已有体测指标, id：{}", targets.get(0).getId());
            return targets.get(0).getId();
        }
        Target target = new Target();
        target.setTenantId(tenantId);
        target.setSchoolId(schoolId);
        target.setCampusId(campusId);
        target.setTargetName(DataSourceEnum.SPORT.getMessage());
        target.setTargetStatus(1);
        target.setGroupId(groupId);
        target.setDataSource(DataSourceEnum.SPORT.getCode());
        target.setSortIndex(999);
        // staffId转换
        target.setCreateBy("system");
        target.setCreateTime(DateUtil.date());
        // 默认字段
        target.setSubmitType(5);
        target.setSendParentFlag(1);

        this.save(target);
        log.info("无体测指标，已新建，id：{}", target.getId());

        return target.getId();
    }
}




