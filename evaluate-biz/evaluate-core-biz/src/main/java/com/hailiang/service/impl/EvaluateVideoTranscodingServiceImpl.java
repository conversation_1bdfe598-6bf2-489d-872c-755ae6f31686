package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.json.JSONUtil;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.common.mybatis.base.TransactionAfterCommit;
import com.hailiang.enums.report.ReportTranscodeStatusEnum;
import com.hailiang.huawei.TranscodeManager;
import com.hailiang.manager.ReportVideoFileManager;
import com.hailiang.model.entity.ReportVideoFilePO;
import com.hailiang.service.EvaluateVideoTranscodingService;
import com.hailiang.util.AssertUtil;
import com.huaweicloud.sdk.mpc.v1.model.QueryTranscodingsTaskResponse;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 视频转码服务实现
 *
 * @Description: 视频转码服务实现
 * @Author: TanJian
 * @Date: Created in 2025-01-07
 * @Version: 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EvaluateVideoTranscodingServiceImpl implements EvaluateVideoTranscodingService {

    private final TranscodeManager transcodeManager;
    private final RedisUtil redisUtil;
    private final ThreadPoolTaskExecutor evaluateExecutor;
    private final ReportVideoFileManager reportVideoFileManager;

    public static final String MPC_10152 = "MPC.10152";

    @Override
    public void createTranscodingTask(ReportVideoFilePO reportVideoFilePO) {
        AssertUtil.checkNotNull(reportVideoFilePO, "reportVideoFilePO is null");
        AssertUtil.checkNotNull(reportVideoFilePO.getId(), "reportVideoFilePO.getId() is null");
        AssertUtil.checkNotBlank(reportVideoFilePO.getOriginalFileUrl(),
                "reportVideoFilePO.getOriginalFileUrl() is blank");
        AssertUtil.checkIsTrue(
                ReportTranscodeStatusEnum.INITIALIZE.getCode().equals(reportVideoFilePO.getTranscodeStatus()),
                "transcodeStatus is not INITIALIZE");

        Runnable task = () -> {
            Integer taskId = transcodeManager.createEvaluateTranscodeTask(reportVideoFilePO.getOriginalFileUrl());
            if (taskId == null) {
                log.error("出现转码未调用成功的视频, file:{}", JSONUtil.toJsonStr(reportVideoFilePO));
                return;
            }
            reportVideoFileManager.updateTranscodeStatusById(reportVideoFilePO.getId(),
                    ReportTranscodeStatusEnum.UNDER_TRANSCODE.getCode());
            redisUtil.put(RedisKeyConstants.EVALUATE_FILE_TRANSCODING, Convert.toStr(reportVideoFilePO.getId()),
                    Convert.toStr(taskId));
        };
        // 在事务中则事务提交后运行
        boolean isInTransaction = TransactionSynchronizationManager.isActualTransactionActive();
        if (isInTransaction) {
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionAfterCommit(() -> evaluateExecutor.execute(task)));
        } else {
            evaluateExecutor.execute(task);
        }
    }

    @Override
    public void syncTranscodingResultHandle() {
        //查询redis所有需要定时轮询状态的数据
        Map<Object, Object> hashEntries = redisUtil.getHashEntries(RedisKeyConstants.EVALUATE_FILE_TRANSCODING);
        if (hashEntries.isEmpty()) {
            return;
        }
        Set<Long> fileIdSet = hashEntries.keySet().stream().map(Convert::toLong).collect(Collectors.toSet());
        List<ReportVideoFilePO> list = reportVideoFileManager.listByIds(fileIdSet);
        for (ReportVideoFilePO filePO : list) {
            fileIdSet.remove(filePO.getId());
            Integer taskId = Convert.toInt(hashEntries.get(Convert.toStr(filePO.getId())));
            QueryTranscodingsTaskResponse task = transcodeManager.getTask(taskId);
            //查询任务出错，忽略下次处理
            if (task == null) {
                continue;
            }
            //查询任务在处理中，忽略下次处理
            if (QueryTranscodingsTaskResponse.StatusEnum.TRANSCODING.equals(task.getStatus()) ||
                    QueryTranscodingsTaskResponse.StatusEnum.NEED_TO_BE_AUDIT.equals(task.getStatus())) {
                logErrorWhenTooLong(filePO, taskId);
                continue;
            }
            //返回失败状态，更新状态
            if (QueryTranscodingsTaskResponse.StatusEnum.NO_TASK.equals(task.getStatus()) ||
                    QueryTranscodingsTaskResponse.StatusEnum.FAILED.equals(task.getStatus()) ||
                    QueryTranscodingsTaskResponse.StatusEnum.CANCELED.equals(task.getStatus())) {
                reportVideoFileManager.updateTranscodeStatusById(filePO.getId(),
                        ReportTranscodeStatusEnum.TRANSCODE_FAILED.getCode());
                redisUtil.delete(RedisKeyConstants.EVALUATE_FILE_TRANSCODING, Convert.toStr(filePO.getId()));
                if(!Objects.equals(task.getErrorCode(), MPC_10152)){
                    log.error("出现转码失败结果, taskId:{}，filePO:{}", taskId, JSONUtil.toJsonStr(filePO));
                }else {
                    log.warn("出现转码失败结果，失败原因是源文件格式不支持, taskId:{}，filePO:{}", taskId,
                            JSONUtil.toJsonStr(filePO));
                }
            }
            //返回成功状态，更新状态和fileUrl
            if (QueryTranscodingsTaskResponse.StatusEnum.SUCCEEDED.equals(task.getStatus())) {
                String fileUrl = StrPool.SLASH + task.getOutput().getObject() + StrPool.SLASH + CollUtil.getFirst(
                        task.getOutputFileName());
                reportVideoFileManager.updateTranscodeStatusById(filePO.getId(),
                        ReportTranscodeStatusEnum.TRANSCODE_SUCCESS.getCode(),
                        fileUrl);
                redisUtil.delete(RedisKeyConstants.EVALUATE_FILE_TRANSCODING, Convert.toStr(filePO.getId()));
            }
        }
        // 删除redis里存在而数据库不存在的
        fileIdSet.forEach(id -> redisUtil.delete(RedisKeyConstants.EVALUATE_FILE_TRANSCODING, Convert.toStr(id)));
    }

    private void logErrorWhenTooLong(ReportVideoFilePO filePO, Integer taskId) {
        DateTime twelveHourAgo = DateUtil.offsetHour(DateUtil.date(), -12);
        if (filePO.getCreateTime().before(twelveHourAgo)) {
            log.error("转码时间超过12个小时，联系华为云排查问题, file:{}, taskId:{}", JSONUtil.toJsonStr(filePO), taskId);
        }
    }
}
