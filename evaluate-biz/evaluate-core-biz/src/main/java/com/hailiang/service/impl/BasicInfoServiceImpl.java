package com.hailiang.service.impl;

import static com.hailiang.constant.RoleConstant.SCHOOL_HEAD;
import static com.hailiang.enums.SaasCurrentIdTypeEnum.CAMPUS;
import static com.hailiang.enums.SaasCurrentIdTypeEnum.GRADE;
import static com.hailiang.enums.SaasCurrentIdTypeEnum.SECTION;
import static com.hailiang.exception.BaseErrorCode.NON_AUTH_FOR_UPDATED_CLASS_RESPONSE_ERROR;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hailiang.annotation.MethodExecuteTime;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.constant.RoleConstant;
import com.hailiang.convert.BasicConvert;
import com.hailiang.enums.ExamTimeTypeEnum;
import com.hailiang.enums.ModuleCodeEnum;
import com.hailiang.enums.SaaSBedTypeEnum;
import com.hailiang.enums.SaaSBuildingTypeEnum;
import com.hailiang.enums.SaaSOnDutyRoleEnum;
import com.hailiang.enums.SaaSRoleEnum;
import com.hailiang.enums.SaaSStudentRelationEnum;
import com.hailiang.enums.SaasClassOrgTypeEnum;
import com.hailiang.enums.SaasClassTypeEnum;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.enums.SaasOrgQueryEnum;
import com.hailiang.enums.SaasQueryTypeEnum;
import com.hailiang.enums.SaasRoomOrgTypeEnum;
import com.hailiang.enums.SaasStaffStateQueryEnum;
import com.hailiang.enums.SaasUserTypeQueryEnum;
import com.hailiang.exception.BaseErrorCode;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.logic.BaseInfoLogic;
import com.hailiang.logic.RoleLogic;
import com.hailiang.logic.TermLogic;
import com.hailiang.model.datastatistics.vo.CheckPermissionVo;
import com.hailiang.model.dto.StudentIdRequest;
import com.hailiang.model.dto.query.BasicInfoListAllUserDTO;
import com.hailiang.model.dto.query.BehaviourStudentFileQueryDTO;
import com.hailiang.model.dto.query.CampusClassListDTO;
import com.hailiang.model.dto.query.ClassLevelListWithAuthDTO;
import com.hailiang.model.dto.query.ClassListDTO;
import com.hailiang.model.dto.query.QueryGradeStudentTreeDTO;
import com.hailiang.model.dto.query.QueryStudentListDTO;
import com.hailiang.model.dto.query.QueryStudentListWithClassDTO;
import com.hailiang.model.dto.query.QueryStudentListWithRoomDTO;
import com.hailiang.model.dto.query.SectionSchoolYearQuery;
import com.hailiang.model.dto.query.YardRoomListDTO;
import com.hailiang.model.dto.query.YardTreeDTO;
import com.hailiang.model.query.StudentScoreQuery;
import com.hailiang.model.request.IdRequest;
import com.hailiang.model.response.StudentClassResponse;
import com.hailiang.model.response.StudentScoreDetailResponse;
import com.hailiang.model.vo.BasicInfoListAllUserVO;
import com.hailiang.model.vo.BasicInfoListOrgInfoByStaffIdVO;
import com.hailiang.model.vo.BehaviourStudentFileVO;
import com.hailiang.model.vo.ClassGradeListVO;
import com.hailiang.model.vo.ClassLevelListVO;
import com.hailiang.model.vo.ListOrgIdAndRoleIdVO;
import com.hailiang.model.vo.QueryClassBaseInfoListVO;
import com.hailiang.model.vo.QueryClassLevelListVO;
import com.hailiang.model.vo.RoomClassInfoListVO;
import com.hailiang.model.vo.RoomClassInfoVO;
import com.hailiang.model.vo.SaaSOnDutyRoleVO;
import com.hailiang.model.vo.SchoolYearVo;
import com.hailiang.model.vo.SectionGradeVO;
import com.hailiang.model.vo.SectionSchoolYearVo;
import com.hailiang.model.vo.StudentInfoVO;
import com.hailiang.model.vo.StudentTreeClassVO;
import com.hailiang.model.vo.StudentTreeStudentVO;
import com.hailiang.model.vo.StudentTreeVO;
import com.hailiang.model.vo.StudentWithClassGradeInfoSortListVO;
import com.hailiang.model.vo.StudentWithClassInfoListVO;
import com.hailiang.model.vo.StudentWithRoomInfoListVO;
import com.hailiang.model.vo.StudentWithRoomInfoSortListVO;
import com.hailiang.model.vo.TreeYardVO;
import com.hailiang.model.vo.YardRoomVO;
import com.hailiang.mp.commonsource.api.PageResult;
import com.hailiang.remote.hai.domain.dto.request.HaiStudentIdRequest;
import com.hailiang.remote.hai.domain.dto.response.StudentLeaveStatusResponse;
import com.hailiang.remote.hai.http.HaiInfoRemote;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.SaaSAPI;
import com.hailiang.remote.saas.SaaSClassInfoAPI;
import com.hailiang.remote.saas.SaaSYardAPI;
import com.hailiang.remote.saas.dto.administration.OrgQueryDTO;
import com.hailiang.remote.saas.dto.auth.MenuAuthCheckDTO;
import com.hailiang.remote.saas.dto.educational.EduClassQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduDutyRoleQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduGradeClassQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduOrganizationalQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduParentQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStaffClassQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentClassQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.dto.educational.PageEduStudentQueryDTO;
import com.hailiang.remote.saas.dto.role.ResRoleQueryDTO;
import com.hailiang.remote.saas.dto.school.SchoolQueryDTO;
import com.hailiang.remote.saas.dto.school.SchoolStaffIdQueryDTO;
import com.hailiang.remote.saas.dto.school.UserSchoolQueryDTO;
import com.hailiang.remote.saas.dto.staff.OrgStaffQueryDTO;
import com.hailiang.remote.saas.dto.staff.SchoolStaffQueryDTO;
import com.hailiang.remote.saas.dto.staff.SchoolStaffStudentDTO;
import com.hailiang.remote.saas.dto.staff.StaffInfoQueryDTO;
import com.hailiang.remote.saas.dto.staff.StaffOrgQueryDTO;
import com.hailiang.remote.saas.dto.staff.StaffRoleQueryDTO;
import com.hailiang.remote.saas.dto.staff.TchClassStaffRequest;
import com.hailiang.remote.saas.dto.student.StudentByIdQuery;
import com.hailiang.remote.saas.dto.student.StudentByParentQueryDTO;
import com.hailiang.remote.saas.dto.teachingorg.ClassHistoryYearQuery;
import com.hailiang.remote.saas.dto.teachingorg.ClassLevelQuery;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.dto.user.UserInfoByTokenDTO;
import com.hailiang.remote.saas.dto.yard.SchoolYardRoomQueryDTO;
import com.hailiang.remote.saas.dto.yard.YardTreeQueryDTO;
import com.hailiang.remote.saas.enums.DutyUserTypeEnum;
import com.hailiang.remote.saas.pojo.educational.EduClassBaseInfoPojo;
import com.hailiang.remote.saas.pojo.educational.EduParentInfoPojo;
import com.hailiang.remote.saas.pojo.role.ResRoleInfoPojo;
import com.hailiang.remote.saas.vo.administration.AdminOrgAndFullPathVO;
import com.hailiang.remote.saas.vo.administration.AdminOrgTreeVO;
import com.hailiang.remote.saas.vo.administration.SchoolOrgVO;
import com.hailiang.remote.saas.vo.campus.CampusInfoVO;
import com.hailiang.remote.saas.vo.campus.TchCampusBaseInfoVO;
import com.hailiang.remote.saas.vo.campus.TchCampusInfoVO;
import com.hailiang.remote.saas.vo.clazz.TchClassVO;
import com.hailiang.remote.saas.vo.educational.ClassSimpleVO;
import com.hailiang.remote.saas.vo.educational.EduAuthVO;
import com.hailiang.remote.saas.vo.educational.EduClassInfoLinkVO;
import com.hailiang.remote.saas.vo.educational.EduClassInfoVO;
import com.hailiang.remote.saas.vo.educational.EduDutyRoleVO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.remote.saas.vo.educational.EduParentInfoVO;
import com.hailiang.remote.saas.vo.educational.EduStaffClassVO;
import com.hailiang.remote.saas.vo.educational.EduStaffGradeVO;
import com.hailiang.remote.saas.vo.educational.EduStaffMasterClassVO;
import com.hailiang.remote.saas.vo.educational.EduStaffSubjectVO;
import com.hailiang.remote.saas.vo.educational.EduStaffTeachClassVO;
import com.hailiang.remote.saas.vo.educational.EduStudentClassTeacherParentVO;
import com.hailiang.remote.saas.vo.educational.EduStudentClassVO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.educational.GlobalEduOrgTreeVO;
import com.hailiang.remote.saas.vo.educational.RoomBedVO;
import com.hailiang.remote.saas.vo.educational.SaaSEduStudentInfoVO;
import com.hailiang.remote.saas.vo.educational.StudentEduInfoVO;
import com.hailiang.remote.saas.vo.menu.MenuAuthVO;
import com.hailiang.remote.saas.vo.role.ResStaffRoleVO;
import com.hailiang.remote.saas.vo.school.UserSchoolCampusListVO;
import com.hailiang.remote.saas.vo.school.UserSchoolVO;
import com.hailiang.remote.saas.vo.staff.StaffBatchQueryDTO;
import com.hailiang.remote.saas.vo.staff.StaffBatchVO;
import com.hailiang.remote.saas.vo.staff.StaffNOVO;
import com.hailiang.remote.saas.vo.staff.StaffOrgsVO;
import com.hailiang.remote.saas.vo.staff.StaffStudentVO;
import com.hailiang.remote.saas.vo.staff.StaffVO;
import com.hailiang.remote.saas.vo.student.SaasStudentVO;
import com.hailiang.remote.saas.vo.student.StudentClassInfoVO;
import com.hailiang.remote.saas.vo.student.StudentVO;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.remote.saas.vo.teachingorg.ClassLevelVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.remote.saas.vo.user.UserInfoVO;
import com.hailiang.remote.saas.vo.yard.SchoolYardRoomVO;
import com.hailiang.remote.saas.vo.yard.YardTreeVO;
import com.hailiang.saas.SaasHistoryTeacherRelationCacheManager;
import com.hailiang.saas.SaasSchoolCacheManager;
import com.hailiang.saas.SaasSchoolManager;
import com.hailiang.saas.SaasStaffManager;
import com.hailiang.saas.SaasStudentCacheManager;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.common.base.CommonResult;
import com.hailiang.saas.model.dto.ParentQueryDTO;
import com.hailiang.saas.model.dto.StudentClassQueryDTO;
import com.hailiang.saas.model.dto.StudentPageQueryDTO;
import com.hailiang.saas.model.dto.school.EduOrgQueryV2DTO;
import com.hailiang.saas.model.dto.school.SchoolIdsRequest;
import com.hailiang.saas.model.pojo.ClassBaseInfoPojo;
import com.hailiang.saas.model.pojo.ParentInfoPojo;
import com.hailiang.saas.model.vo.ParentInfoVO;
import com.hailiang.saas.model.vo.StudentClassVO;
import com.hailiang.saas.model.vo.StudentInfo1VO;
import com.hailiang.saas.model.vo.TeacherClassRelationVO;
import com.hailiang.saas.model.vo.school.EduOrgTreeV2VO;
import com.hailiang.saas.model.vo.school.SchoolBaseVO;
import com.hailiang.saas.model.vo.school.SchoolCampusPojo;
import com.hailiang.saas.model.vo.staff.ClassFullInfoVO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.BehaviourRecordService;
import com.hailiang.util.AssertUtil;
import com.hailiang.util.ParamUtil;
import com.hailiang.util.RequestUtil;
import com.hailiang.util.WebUtil;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;


/**
 * saas接口整合
 */
@Service
@Slf4j
public class BasicInfoServiceImpl implements BasicInfoService {

    /**
     * 体育老师在SaaS侧没有角色code，暂时通过角色名称校验
     */
    private static final List<String> SPORT_TEACHER_LIST = Arrays.asList("体育老师", "体育带头人");
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private CacheSaasManager cacheSaasManager;
    @Resource
    private RoleLogic roleLogic;
    @Resource
    private BasicConvert basicConvert;
    @Resource
    private SaaSAPI saaSAPI;
    @Resource
    private SaaSYardAPI saaSYardAPI;
    @Resource
    private SaaSClassInfoAPI saaSClassInfoAPI;
    @Resource
    private SaasStudentCacheManager saasStudentCacheManager;
    @Resource
    private SaasStudentManager saasStudentManager;
    @Resource
    private SaasSchoolManager saasSchoolManager;
    @Resource
    private SaasStaffManager saasStaffManager;
    @Resource
    private BehaviourRecordService behaviourRecordService;
    @Resource
    private BaseInfoLogic baseInfoLogic;
    @Resource
    private SaasSchoolCacheManager saasSchoolCacheManager;
    @Resource
    private SaasHistoryTeacherRelationCacheManager saasHistoryTeacherRelationCacheManager;
    @Resource
    private TermLogic termLogic;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private HaiInfoRemote haiInfoRemote;

    @Override
    public List<TermVo> listNowAndHistoryTermsByCampusSectionId(String campusSectionId) {

        //1、构建查询条件
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
        termQuery.setCampusId(Convert.toLong(WebUtil.getCampusId()));
        termQuery.setCampusSectionId(campusSectionId);

        //2、调用远程接口，查询学期列表
        List<TermVo> termVoList = basicInfoRemote.queryTermList(termQuery);
        if (CollectionUtil.isEmpty(termVoList)) {
            return null;
        }

        //3、筛选出开始时间在当前时间之前的学期，包括现在学期和之前的学期
        termVoList = termVoList.stream().filter(s -> DateUtil.parse(s.getStartTime()).before(DateUtil.date()))
                .collect(Collectors.toList());

        //4、初始化日期映射和排序列表
        Map<String, Date> endDatesMap = new HashMap<>();
        List<Date> sortedStartDates = new ArrayList<>();

        //5、处理每个学期，计算结束时间
        for (TermVo term : termVoList) {
            Date startDate = DateUtil.parse(term.getStartTime());
            Date endDate = DateUtil.parse(term.getEndTime());
            endDatesMap.put(term.getEndTime(), endDate);
            sortedStartDates.add(startDate);
        }

        //6、对开始时间进行排序
        Collections.sort(sortedStartDates);

        //7、设置每个学期的结束时间（考虑假期）
        for (TermVo term : termVoList) {
            Date currentEndDate = endDatesMap.get(term.getEndTime());
            Date nextStartDate = getNextStartDate(currentEndDate, sortedStartDates);

            if (nextStartDate != null) {
                term.setEndTimeWithVacation(
                        DateUtil.format(DateUtil.offsetDay(nextStartDate, -1), DatePattern.NORM_DATE_PATTERN));
            } else {
                term.setEndTimeWithVacation(term.getEndTime());
            }
        }

        return termVoList;
    }

    /**
     * 获取下一个开始时间
     *
     * @param currentEndDate
     * @param sortedStartDates
     * @return
     */
    private Date getNextStartDate(Date currentEndDate, List<Date> sortedStartDates) {
        for (Date startDate : sortedStartDates) {
            if (startDate.after(currentEndDate)) {
                return startDate;
            }
        }
        return null;
    }

    /**
     * 获取当前登录教职工所有组织机构id和角色id
     */
    @Override
    public ListOrgIdAndRoleIdVO listOrgIdAndRoleId() {
        return listOrgIdAndRoleIdByStaffId(WebUtil.getStaffIdLong(), WebUtil.getSchoolIdLong());
    }

    /**
     * 获取当前登录教职工所有组织机构id和角色id
     */
    @Override
    public ListOrgIdAndRoleIdVO listOrgIdAndRoleId(Long staffId, String schoolId) {
        return listOrgIdAndRoleIdByStaffId(staffId, Convert.toLong(schoolId));
    }

    /**
     * 获取教职工所有组织机构id和角色id
     */
    private ListOrgIdAndRoleIdVO listOrgIdAndRoleIdByStaffId(Long staffId, Long schoolId) {
        log.info("获取教职工所有组织机构id和角色id-开始");
        if (ObjectUtil.hasNull(staffId, schoolId)) {
            log.info("staffId或schoolId为空，直接返回空");
            return null;
        }
        BasicInfoListOrgInfoByStaffIdVO basicInfo = listOrgInfoByStaffId(staffId, schoolId);
        if (ObjectUtil.isNull(basicInfo)) {
            log.info("basicInfo为空，直接返回空");
            return null;
        }
        // 组织机构集合
        List<StaffOrgsVO> orgList = basicInfo.getStaffOrgsVOList();
        // 角色集合
        List<ResStaffRoleVO> roleList = basicInfo.getResStaffRoleVOList();
        if (CollUtil.isEmpty(orgList) && CollUtil.isEmpty(roleList)) {
            log.info("orgList或roleList为空，直接返回空");
            return null;
        }
        // 教职工所有组织机构id集合
        List<String> orgIdList = new ArrayList<>();
        //教职工所有组织机构name集合
        List<String> orgNameList = new ArrayList<>();
        // 教职工所有角色id集合
        List<String> roleIdList = new ArrayList<>();
        // 教职工所有角色code集合
        List<String> roleCodeList = new ArrayList<>();
        // 教职工所有角色name集合
        List<String> roleNameList = new ArrayList<>();

        // 教职工所有组织机构id集合
        if (CollUtil.isNotEmpty(orgList)) {
            for (StaffOrgsVO org : orgList) {
                if (CollUtil.isEmpty(org.getOrglevelPaths())) {
                    continue;
                }
                orgIdList.addAll(org.getOrglevelPaths().stream().map(item -> Convert.toStr(item.getOrgId()))
                        .collect(Collectors.toList()));
                List<AdminOrgAndFullPathVO> orglevelPathList = org.getOrglevelPaths();
                for (AdminOrgAndFullPathVO orgAndFullPathVO : orglevelPathList) {
                    List<String> orgLevelIdList = orgAndFullPathVO.getLevelPathVOs().stream()
                            .map(item -> Convert.toStr(item.getLevelPathId())).collect(Collectors.toList());
                    orgIdList.addAll(orgLevelIdList);
                }
                orgNameList = org.getOrglevelPaths().stream().map(AdminOrgAndFullPathVO::getOrgName)
                        .collect(Collectors.toList());
            }
        }

        // 教职工所有角色id集合
        if (CollUtil.isNotEmpty(roleList)) {
            for (ResStaffRoleVO role : roleList) {
                if (CollUtil.isEmpty(role.getRoles())) {
                    continue;
                }
                roleIdList.addAll(role.getRoles().stream().map(item -> Convert.toStr(item.getRoleId()))
                        .collect(Collectors.toList()));
                roleCodeList = role.getRoles().stream().map(ResRoleInfoPojo::getRoleCode).collect(Collectors.toList());
                roleNameList = role.getRoles().stream().map(ResRoleInfoPojo::getRoleName).collect(Collectors.toList());
            }
        }

        return new ListOrgIdAndRoleIdVO().setOrgIdList(orgIdList).setRoleIdList(roleIdList).setOrgNameList(orgNameList)
                .setRoleCodeList(roleCodeList).setRoleNameList(roleNameList);
    }


    /**
     * 根据教职工id查其所属各组织机构id及其所有角色code
     */
    private BasicInfoListOrgInfoByStaffIdVO listOrgInfoByStaffId(Long staffId, Long schoolId) {
        log.info("【BasicInfoService层saas服务调用】====根据教职工id查其所属各组织机构id及其所有角色code");
        if (Objects.isNull(staffId) || Objects.isNull(schoolId)) {
            return new BasicInfoListOrgInfoByStaffIdVO();
        }
        BasicInfoListOrgInfoByStaffIdVO basicInfoListOrgInfoByStaffIdVO = new BasicInfoListOrgInfoByStaffIdVO();
        StaffOrgQueryDTO staffOrgQueryDTO = new StaffOrgQueryDTO();
        List<Long> staffIdList = new ArrayList<>();
        staffIdList.add(staffId);
        staffOrgQueryDTO.setStaffIds(staffIdList);
        staffOrgQueryDTO.setId(schoolId);
        //入参类型：学校id
        staffOrgQueryDTO.setType(SaasOrgQueryEnum.SCHOOL_ID.getCode());
        List<StaffOrgsVO> staffOrgsVOList = basicInfoRemote.queryStaffBelongToOrgs(staffOrgQueryDTO);
        basicInfoListOrgInfoByStaffIdVO.setStaffOrgsVOList(staffOrgsVOList);
        ResRoleQueryDTO resRoleQueryDTO = new ResRoleQueryDTO();
        resRoleQueryDTO.setStaffIds(staffIdList);
        resRoleQueryDTO.setId(schoolId);
        //入参类型：学校id
        resRoleQueryDTO.setType(SaasOrgQueryEnum.SCHOOL_ID.getCode());
        List<ResStaffRoleVO> resStaffRoleVOList = basicInfoRemote.queryStaffRoleList(resRoleQueryDTO);
        basicInfoListOrgInfoByStaffIdVO.setResStaffRoleVOList(resStaffRoleVOList);
        return basicInfoListOrgInfoByStaffIdVO;
    }

    /**
     * 基础数据-通过组织机构、角色获取所有教职工
     */
    @Override
    public List<BasicInfoListAllUserVO> listAllUser(List<BasicInfoListAllUserDTO> basicInfoListAllUserDTOList) {
        log.info("【BasicInfoService层saas服务调用】====基础数据-通过组织机构、角色获取所有教职工");
        List<BasicInfoListAllUserVO> resultList = new ArrayList<>();
        basicInfoListAllUserDTOList.forEach(basicInfoListAllUserDTO -> {
            List<StaffVO> staffVOList = new ArrayList<>();
            //通过组织机构查询教职工
            if (SaasQueryTypeEnum.ORG.getCode().equals(basicInfoListAllUserDTO.getSubmitType())) {
                OrgStaffQueryDTO orgStaffQueryDTO = new OrgStaffQueryDTO();
                orgStaffQueryDTO.setOrgId(basicInfoListAllUserDTO.getSubmitValue());
                orgStaffQueryDTO.setType(basicInfoListAllUserDTO.getQueryType());
                staffVOList = basicInfoRemote.queryStaffListByOrg(orgStaffQueryDTO);
            }
            //通过角色获取教职工
            if (SaasQueryTypeEnum.ROLE.getCode().equals(basicInfoListAllUserDTO.getSubmitType())) {
                StaffRoleQueryDTO staffRoleQueryDTO = new StaffRoleQueryDTO();
                staffRoleQueryDTO.setRoleId(basicInfoListAllUserDTO.getSubmitValue());
                staffVOList = basicInfoRemote.queryStaffListByRole(staffRoleQueryDTO);
            }
            for (StaffVO staffVO : staffVOList) {
                BasicInfoListAllUserVO basicInfoListAllUserVO = new BasicInfoListAllUserVO();
                BeanUtils.copyProperties(staffVO, basicInfoListAllUserVO);
                basicInfoListAllUserVO.setUserType(SaasUserTypeQueryEnum.STAFF.getCode());
                resultList.add(basicInfoListAllUserVO);
            }
        });
        return resultList;
    }

    /**
     * 获取组织机构树形
     */
    @Override
    public List<AdminOrgTreeVO> listOrgTree(OrgQueryDTO orgQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====获取组织机构树形");
        List<AdminOrgTreeVO> adminOrgTreeVOList = basicInfoRemote.listOrgTree(orgQueryDTO);
        for (AdminOrgTreeVO adminOrgTreeVO : adminOrgTreeVOList) {
            OrgStaffQueryDTO orgStaffQueryDTO = new OrgStaffQueryDTO();
            orgStaffQueryDTO.setOrgId(orgQueryDTO.getOrgId());
            List<StaffVO> staffVOList = basicInfoRemote.queryStaffListByOrg(orgStaffQueryDTO);
            adminOrgTreeVO.setStaffVOList(solveStaffVO(staffVOList));
        }
        return adminOrgTreeVOList;
    }

    /**
     * 整合教职工信息
     */
    private List<StaffVO> solveStaffVO(List<StaffVO> staffVOList) {
        log.info("【BasicInfoService层saas服务调用】====整合教职工信息");

        if (staffVOList.isEmpty()) {
            return staffVOList;
        }

        //获取教职工所属行政组织机构
        List<Long> staffIdList = staffVOList.stream().map(StaffVO::getStaffId).collect(Collectors.toList());

        if (CollUtil.isEmpty(staffIdList)) {
            log.warn("整合教职工信息异常,教职工id集合为空");
            return new ArrayList<>();
        }
        StaffOrgQueryDTO staffOrgQueryDTO = new StaffOrgQueryDTO();
        staffOrgQueryDTO.setStaffIds(staffIdList);
        staffOrgQueryDTO.setType(SaasOrgQueryEnum.SCHOOL_ID.getCode());
        staffOrgQueryDTO.setId(WebUtil.getSchoolIdLong());
        Map<Long, List<AdminOrgAndFullPathVO>> staffMap = basicInfoRemote.queryStaffBelongToOrgs(staffOrgQueryDTO)
                .stream().collect(Collectors.toMap(StaffOrgsVO::getStaffId, StaffOrgsVO::getOrglevelPaths));

        //获取教职工员工任教情况
        List<EduStaffClassVO> eduStaffClassVOs = new ArrayList<>();
        List<List<Long>> staffIdLists = ListUtil.partition(staffIdList, 1000);
        log.info("staffIdList数据条数：{}", staffIdList.size());
        for (List<Long> d : staffIdLists) {
            log.info("开始循环获取员工数据：条数{}", d.size());
            List<EduStaffClassVO> eduStaffClassVOList = this.listStaffTeachInfo(d, Long.valueOf(WebUtil.getSchoolId()));
            eduStaffClassVOs.addAll(eduStaffClassVOList);
        }
        log.info("循环结束, 结果总条数{}", eduStaffClassVOs.size());
        Map<Long, List<EduStaffTeachClassVO>> eduStaffClassVOMap = eduStaffClassVOs.stream()
                .collect(Collectors.toMap(EduStaffClassVO::getStaffId, EduStaffClassVO::getTeachClassInfos));
        staffVOList.forEach(staffVO -> {
            staffVO.setAdminOrgAndFullPathVOS(staffMap.get(staffVO.getStaffId()));
            staffVO.setTeachClassInfos(eduStaffClassVOMap.get(staffVO.getStaffId()));
            //拼接岗位信息
            staffVO.setSectionSubjectInfo(solveSectionSubjectInfo(eduStaffClassVOMap.get(staffVO.getStaffId())));
        });

        return staffVOList;
    }

    /**
     * @param staffIdList 注意：不能超过1000
     * @param schoolId
     * @return
     */
    private List<EduStaffClassVO> listStaffTeachInfo(List<Long> staffIdList, Long schoolId) {
        //获取教职工员工任教情况
        EduStaffClassQueryDTO eduStaffClassQueryDTO = new EduStaffClassQueryDTO();
        eduStaffClassQueryDTO.setStaffIds(staffIdList);
        eduStaffClassQueryDTO.setSchoolId(schoolId);
        return basicInfoRemote.queryStaffTeachInfo(eduStaffClassQueryDTO);
    }

    /**
     * 处理任教信息
     */
    private List<String> solveSectionSubjectInfo(List<EduStaffTeachClassVO> eduStaffTeachClassVOList) {

        if (Objects.isNull(eduStaffTeachClassVOList) || eduStaffTeachClassVOList.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> resultList = new ArrayList<>();
        for (EduStaffTeachClassVO eduStaffTeachClassVO : eduStaffTeachClassVOList) {
            String sectionName = eduStaffTeachClassVO.getSectionName();
            if (StrUtil.isNotEmpty(sectionName)) {
                List<EduStaffSubjectVO> eduStaffSubjectVOS = eduStaffTeachClassVO.getSubjects();
                if (Objects.nonNull(eduStaffSubjectVOS) && !eduStaffSubjectVOS.isEmpty()) {
                    eduStaffSubjectVOS.forEach(eduStaffSubjectVO -> {
                        if (StrUtil.isNotEmpty(eduStaffSubjectVO.getSubjectName())) {
                            resultList.add(sectionName + eduStaffSubjectVO.getSubjectName() + "教师");
                        }
                    });
                }
            }
        }

        return resultList;
    }


    /**
     * 通过学校id获取组织机构树形
     */
    @Override
    public List<AdminOrgTreeVO> listOrgTreeBySchoolId(SchoolQueryDTO schoolQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====通过学校id获取组织机构树形");
        List<AdminOrgTreeVO> adminOrgTreeVOList = basicInfoRemote.listOrgTreeBySchoolId(schoolQueryDTO);
        for (AdminOrgTreeVO adminOrgTreeVO : adminOrgTreeVOList) {
            OrgStaffQueryDTO orgStaffQueryDTO = new OrgStaffQueryDTO();
            orgStaffQueryDTO.setOrgId(adminOrgTreeVO.getOrgId());
            List<StaffVO> staffVOList = basicInfoRemote.queryStaffListByOrg(orgStaffQueryDTO);
            adminOrgTreeVO.setStaffVOList(solveStaffVO(staffVOList));
        }
        return adminOrgTreeVOList;
    }

    /**
     * 通过学校id获取学校的组织机构id
     */
    @Override
    public SchoolOrgVO getOrgIdBySchoolId(SchoolQueryDTO schoolQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====通过学校id获取组织机构树形");
        List<AdminOrgTreeVO> adminOrgTreeVOList = basicInfoRemote.listOrgTreeBySchoolId(schoolQueryDTO);
        AssertUtil.checkNotNull(adminOrgTreeVOList, "学校机构为空!");
        AdminOrgTreeVO orgTreeVO = CollUtil.getFirst(adminOrgTreeVOList);
        SchoolOrgVO schoolOrgVO = new SchoolOrgVO();
        schoolOrgVO.setOrgId(orgTreeVO.getOrgId());
        schoolOrgVO.setOrgName(orgTreeVO.getOrgName());
        schoolOrgVO.setOrgType(orgTreeVO.getOrgType());
        return schoolOrgVO;
    }


    /**
     * 分页查询组织机构下的教职工列表
     */
    @Override
    public List<StaffVO> queryStaffListByOrg(OrgStaffQueryDTO orgStaffQueryDTO) {
        return solveStaffVO(basicInfoRemote.queryStaffListByOrg(orgStaffQueryDTO));
    }

    /**
     * 获取教职工所属行政班信息
     */
    private List<EduStaffClassVO> listEduStaffClass() {
        EduStaffClassQueryDTO eduStaffClassQueryDTO = new EduStaffClassQueryDTO();

        List<Long> staffIdList = new ArrayList<>();
        staffIdList.add(WebUtil.getStaffIdLong());

        List<String> classTypeList = new ArrayList<>();
        classTypeList.add(SaasClassTypeEnum.XINGZHENG.getCode());

        if (CollUtil.isEmpty(staffIdList)) {
            log.warn("获取教职工所属行政班信息异常,教职工id集合为空");
            return new ArrayList<>();
        }

        eduStaffClassQueryDTO.setStaffIds(staffIdList);
        eduStaffClassQueryDTO.setSchoolId(WebUtil.getSchoolIdLong());
        eduStaffClassQueryDTO.setClassTypes(classTypeList);
        eduStaffClassQueryDTO.setGraduationStatus(SaasClassTypeEnum.XINGZHENG.getCode());

        return basicInfoRemote.queryStaffTeachInfo(eduStaffClassQueryDTO);
    }

    /**
     * 获取教职工所属班级，包含走班和行政班
     */
    private List<EduStaffClassVO> listEduStaffAllClass() {
        EduStaffClassQueryDTO eduStaffClassQueryDTO = new EduStaffClassQueryDTO();

        List<Long> staffIdList = new ArrayList<>();
        staffIdList.add(WebUtil.getStaffIdLong());

        if (CollUtil.isEmpty(staffIdList)) {
            log.warn("获取教职工所属班级信息异常,教职工id集合为空");
            return new ArrayList<>();
        }
        eduStaffClassQueryDTO.setStaffIds(staffIdList);
        eduStaffClassQueryDTO.setSchoolId(WebUtil.getSchoolIdLong());
        eduStaffClassQueryDTO.setGraduationStatus(Constant.ZERO.toString());

        return basicInfoRemote.queryStaffTeachInfo(eduStaffClassQueryDTO);
    }

    /**
     * 获取教职工所属班信息（行政班和走班）
     */
    private List<EduStaffClassVO> listEduStaffClassALL() {
        EduStaffClassQueryDTO eduStaffClassQueryDTO = new EduStaffClassQueryDTO();

        List<Long> staffIdList = new ArrayList<>();
        staffIdList.add(WebUtil.getStaffIdLong());

        if (CollUtil.isEmpty(staffIdList)) {
            log.warn("获取获取教职工所属班信息异常,教职工id集合为空");
            return new ArrayList<>();
        }

        eduStaffClassQueryDTO.setStaffIds(staffIdList);
        eduStaffClassQueryDTO.setSchoolId(WebUtil.getSchoolIdLong());
        eduStaffClassQueryDTO.setGraduationStatus(Constant.ZERO.toString());

        return basicInfoRemote.queryStaffTeachInfo(eduStaffClassQueryDTO);
    }

    /**
     * 获取学校下的校区列表
     */
    @Override
    public List<EduOrgTreeVO> queryEducationalOrgTree(EduOrgQueryDTO eduOrgQueryDTO) {

        List<EduOrgTreeVO> eduOrgTreeVOList = new ArrayList<>();
        //记录接口的时间消耗
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = listOrgIdAndRoleId();

        log.info("【获取教学关系组织树】-获取登录人的机构、角色列表，消耗时长：【{}】ms", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        List<String> roleCodeList;

        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())
                && !listOrgIdAndRoleIdVO.getOrgIdList().isEmpty()) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            log.info("【获取教学关系组织树】-当前登录人未配置角色，直接返回空值,listOrgIdAndRoleIdVO:{}",
                    JSONUtil.toJsonStr(listOrgIdAndRoleIdVO));
            return new ArrayList<>();
        }
        log.info("【获取教学关系组织树】-当前登录人具有的角色{}", JSONUtil.toJsonStr(roleCodeList));

        //saas默认角色code未配置，暂时先采用名称匹配，若saas后续配置，更换为code
        List<EduOrgTreeVO> superTreeList = checkSupPermission(roleCodeList, eduOrgQueryDTO);

        if (CollUtil.isNotEmpty(superTreeList)) {
            log.info("【获取教学关系组织树】-【管理员】角色");
            return superTreeList;
        }
        List<EduOrgTreeVO> ordinaryTreeList = checkOrdinaryPermission(roleCodeList, eduOrgQueryDTO);
        if (CollUtil.isNotEmpty(ordinaryTreeList)) {
            log.info("【获取教学关系组织树】-【普通】角色");
            return ordinaryTreeList;
        }

        return eduOrgTreeVOList;
    }

    @Override
    public List<EduOrgTreeVO> queryEducationalOrgTreeV2(EduOrgQueryDTO eduOrgQueryDTO) {

        //记录接口的时间消耗
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = listOrgIdAndRoleId();

        log.info("【获取教学关系组织树】-获取登录人的机构、角色列表，消耗时长：【{}】ms", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        List<String> roleCodeList;

        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())
                && !listOrgIdAndRoleIdVO.getOrgIdList().isEmpty()) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            log.info("【获取教学关系组织树】-当前登录人未配置角色，直接返回空值,listOrgIdAndRoleIdVO:{}",
                    JSONUtil.toJsonStr(listOrgIdAndRoleIdVO));
            return new ArrayList<>();
        }
        log.info("【获取教学关系组织树】-当前登录人具有的角色{}", JSONUtil.toJsonStr(roleCodeList));

        //动态判断管理员角色
        if (CollUtil.isNotEmpty(CollUtil.intersection(roleCodeList,
                ModuleCodeEnum.listAdminRoleByCode(eduOrgQueryDTO.getModuleCode())))) {
            log.info("【获取教学关系组织树】-【管理员】角色");
            List<EduOrgTreeVO> eduOrgTreeVOList = cacheSaasManager.queryEducationalOrgTree(eduOrgQueryDTO);
            if (CollUtil.isEmpty(eduOrgTreeVOList) || CollUtil.isEmpty(eduOrgQueryDTO.getShowViewTypeList())) {
                return eduOrgTreeVOList;
            }
            return this.filterShowViewTree(eduOrgTreeVOList, eduOrgQueryDTO.getShowViewTypeList());
        }

        //非管理员角色
        if (roleLogic.isHaveOrdinaryRole(roleCodeList)) {
            log.info("【获取教学关系组织树】-【普通】角色");
            eduOrgQueryDTO.setIsTree(0);
            List<EduOrgTreeVO> eduOrgTreeVOList = cacheSaasManager.queryEducationalOrgTree(eduOrgQueryDTO);
            //年级主任、班主任、学科老师需要过滤数据权限
            eduOrgTreeVOList = filterAuth(eduOrgTreeVOList, eduOrgQueryDTO);

            if (CollUtil.isEmpty(eduOrgTreeVOList) || CollUtil.isEmpty(eduOrgQueryDTO.getShowViewTypeList())) {
                return eduOrgTreeVOList;
            }
            return this.filterShowViewTree(eduOrgTreeVOList, eduOrgQueryDTO.getShowViewTypeList());
        }

        return Collections.emptyList();
    }

    /**
     * 获取组织机构树
     *
     * @return
     */
    @Override
    public List<GlobalEduOrgTreeVO> queryGlobalEducationalOrgTree(EduOrgQueryDTO eduOrgQueryDTO) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();
        // 获取当前登录教职工所有组织机构角色id
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = cacheSaasManager.listOrgIdAndRoleId();
        log.info(
                "BasicInfoServiceImpl.queryGlobalEducationalOrgTree【获取教学关系组织树】-获取登录人的机构、角色列表，消耗时长：【{}】ms",
                TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        List<String> roleCodeList;

        //特殊逻辑处理：用于值日记录，值日学生直接赋予校长角色
        if (StringUtils.isBlank(WebUtil.getStaffId()) && StringUtils.isNotBlank(WebUtil.getStudentIdStr())) {
            listOrgIdAndRoleIdVO = new ListOrgIdAndRoleIdVO();
            listOrgIdAndRoleIdVO.setRoleCodeList(Collections.singletonList(SCHOOL_HEAD));
            //此处为了不进入下面的Else逻辑返回空值，所以设置orgIdList为ADMIN
            listOrgIdAndRoleIdVO.setOrgIdList(Collections.singletonList("ADMIN"));
        }
        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())
                && !listOrgIdAndRoleIdVO.getOrgIdList().isEmpty()) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            log.info(
                    "BasicInfoServiceImpl.queryGlobalEducationalOrgTree【获取教学关系组织树】-当前登录人未配置角色，直接返回空值，listOrgIdAndRoleIdVO:{}",
                    JSONUtil.toJsonStr(listOrgIdAndRoleIdVO));
            return new ArrayList<>();
        }
        log.info("BasicInfoServiceImpl.queryGlobalEducationalOrgTree【获取教学关系组织树】-当前登录人具有的角色：{}",
                JSONUtil.toJsonStr(roleCodeList));
        List<EduOrgTreeVO> eduOrgTreeVOList = this.checkPermission(roleCodeList, eduOrgQueryDTO);
        log.info("BasicInfoServiceImpl.queryGlobalEducationalOrgTree【获取教学关系组织树】，消耗时长：【{}】ms",
                TIME_INTERVAL.intervalMs());
        return BasicConvert.INSTANCE.toGlobalEduOrgTreeList(eduOrgTreeVOList);
    }

    @Override
    @MethodExecuteTime
    public EduAuthVO getCurrentStaffAuth(String staffId, String schoolYear) {
        // 前端页面对应学期选择项
        Assert.notBlank(schoolYear, "请选择学期");

        EduAuthVO eduAuthVO = this.getCurrentStaffAuth(staffId, schoolYear, null, null);

        Assert.isTrue(ObjectUtil.isNotEmpty(eduAuthVO), () -> new BizException(BaseErrorCode.NO_DATA_AUTH));

        return eduAuthVO;
    }

    /**
     * 权限，包含走班
     */
    @Override
    public EduAuthVO getCurrentStaffAuthAll(String staffId, String schoolYear, String termName, String moduleCode
            , List<Integer> classTypes) {

        String campusId = WebUtil.getCampusId();
        List<String> currentStaffRoleCodeList = getCurrentStaffRole();

        List<String> defaultAdminRole = ModuleCodeEnum.listAdminRoleByCode(moduleCode);
        log.info("【获取当前用户权限，含走班】-当前登陆人角色:{},管理员默认角色:{}",
                JSONUtil.toJsonStr(currentStaffRoleCodeList),
                JSON.toJSONString(defaultAdminRole));
        // 去掉currentStaffRoleCodeList的空指
        currentStaffRoleCodeList = currentStaffRoleCodeList.stream().filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 管理员直接返回
        if (CollUtil.isNotEmpty(CollUtil.intersection(currentStaffRoleCodeList, defaultAdminRole))) {
            EduAuthVO eduAuthVO = new EduAuthVO();
            eduAuthVO.setIsAdmin(true);
            eduAuthVO.setRoles(currentStaffRoleCodeList);
            eduAuthVO.setRoleNames(SaaSRoleEnum.listRoleNameByCode(currentStaffRoleCodeList));

            return eduAuthVO;

        }
        // 非管理员 查一下班级和年级权限
        String redisKey = CharSequenceUtil.format("getCurrentStaffAuth:{}:{}", campusId, staffId);
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setSchoolYear(schoolYear);
        eduOrgQueryDTO.setTermName(termName);
        eduOrgQueryDTO.setCurrentId(Convert.toLong(campusId));
        eduOrgQueryDTO.setCurrentIdType(2);
        eduOrgQueryDTO.setEndType(5);
        eduOrgQueryDTO.setIsTree(0);
        if (CollUtil.isNotEmpty(classTypes)) {
            eduOrgQueryDTO.setClassTypes(classTypes);
        }
        List<GlobalEduOrgTreeVO> globalEduOrgTreeVOS = redisUtil
                .getOrAdd(redisKey, () -> this.queryGlobalEducationalOrgTreeV3(eduOrgQueryDTO), 2);
        EduAuthVO eduAuthVO = new EduAuthVO();
        List<Long> classIds = new ArrayList<>();
        List<String> classNames = new ArrayList<>();
        List<Long> gradeIds = new ArrayList<>();
        List<String> gradeNames = new ArrayList<>();
        for (GlobalEduOrgTreeVO d : globalEduOrgTreeVOS) {
            if (d.getType() == 5) {
                classIds.add(d.getId());
                classNames.add(d.getName());
            } else if (d.getType() == 4) {
                gradeIds.add(d.getId());
                gradeNames.add(d.getName());
            }
        }
        eduAuthVO.setClassIds(classIds);
        eduAuthVO.setClassIdStrs(classIds.stream().map(String::valueOf).collect(Collectors.toList()));
        eduAuthVO.setClassNames(classNames);
        eduAuthVO.setGradeIds(gradeIds);
        eduAuthVO.setGradeIdStrs(gradeIds.stream().map(String::valueOf).collect(Collectors.toList()));
        eduAuthVO.setGradeNames(gradeNames);
        eduAuthVO.setRoles(currentStaffRoleCodeList);
        eduAuthVO.setRoleNames(SaaSRoleEnum.listRoleNameByCode(currentStaffRoleCodeList));
        eduAuthVO.setIsAdmin(false);

        log.info("【获取当前用户权限，含走班】：{}", JSONUtil.toJsonStr(eduAuthVO));

        return eduAuthVO;
    }

    /**
     * 权限，包含走班
     */
    @Override
    public EduAuthVO getCurrentStaffAuthAll(String staffId, String schoolYear, String termName,
                                            List<Integer> classTypes) {
        return this.getCurrentStaffAuthAll(staffId, schoolYear, termName, null, classTypes);
    }

    @Override
    @MethodExecuteTime
    public EduAuthVO getCurrentStaffAuth(String staffId, String schoolYear, String termName) {
        return this.getCurrentStaffAuth(staffId, schoolYear, termName, null);
    }

    @Override
    @MethodExecuteTime
    public EduAuthVO getCurrentStaffAuth(String staffId, String schoolYear, String termName, String moduleCode) {
        String campusId = WebUtil.getCampusId();
        List<String> currentStaffRoleCodeList = getCurrentStaffRole();

        List<String> defaultAdminRole = ModuleCodeEnum.listAdminRoleByCode(moduleCode);
        log.info("【获取当前用户权限】-当前登陆人角色:{},管理员默认角色:{}", JSONUtil.toJsonStr(currentStaffRoleCodeList),
                JSON.toJSONString(defaultAdminRole));
        // 去掉currentStaffRoleCodeList的空指
        currentStaffRoleCodeList = currentStaffRoleCodeList.stream().filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 管理员直接返回
        if (CollUtil.isNotEmpty(CollUtil.intersection(currentStaffRoleCodeList, defaultAdminRole))) {
            EduAuthVO eduAuthVO = new EduAuthVO();
            eduAuthVO.setIsAdmin(true);
            eduAuthVO.setRoles(currentStaffRoleCodeList);
            eduAuthVO.setRoleNames(SaaSRoleEnum.listRoleNameByCode(currentStaffRoleCodeList));

            return eduAuthVO;

        }
        // 非管理员 查一下班级和年级权限
        String redisKey = StrUtil.format("getCurrentStaffAuth:{}:{}", campusId, staffId);
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setSchoolYear(schoolYear);
        eduOrgQueryDTO.setTermName(termName);
        eduOrgQueryDTO.setCurrentId(Convert.toLong(campusId));
        eduOrgQueryDTO.setCurrentIdType(2);
        eduOrgQueryDTO.setEndType(5);
        eduOrgQueryDTO.setIsTree(0);
        List<GlobalEduOrgTreeVO> globalEduOrgTreeVOS = redisUtil.getOrAdd(redisKey,
                () -> this.queryGlobalEducationalOrgTreeV2(eduOrgQueryDTO), 2);
        EduAuthVO eduAuthVO = new EduAuthVO();
        List<Long> classIds = new ArrayList<>();
        List<String> classNames = new ArrayList<>();
        List<Long> gradeIds = new ArrayList<>();
        List<String> gradeNames = new ArrayList<>();
        for (GlobalEduOrgTreeVO d : globalEduOrgTreeVOS) {
            if (d.getType() == 5) {
                classIds.add(d.getId());
                classNames.add(d.getName());
            } else if (d.getType() == 4) {
                gradeIds.add(d.getId());
                gradeNames.add(d.getName());
            }
        }
        eduAuthVO.setClassIds(classIds);
        eduAuthVO.setClassIdStrs(classIds.stream().map(String::valueOf).collect(Collectors.toList()));
        eduAuthVO.setClassNames(classNames);
        eduAuthVO.setGradeIds(gradeIds);
        eduAuthVO.setGradeIdStrs(gradeIds.stream().map(String::valueOf).collect(Collectors.toList()));
        eduAuthVO.setGradeNames(gradeNames);
        eduAuthVO.setRoles(currentStaffRoleCodeList);
        eduAuthVO.setRoleNames(SaaSRoleEnum.listRoleNameByCode(currentStaffRoleCodeList));
        eduAuthVO.setIsAdmin(false);

        log.info("【获取当前用户权限】：{}", JSONUtil.toJsonStr(eduAuthVO));

        return eduAuthVO;
    }

    @Override
    public List<GlobalEduOrgTreeVO> queryGlobalEducationalOrgTreeV2(EduOrgQueryDTO eduOrgQueryDTO) {
        Integer isTree = ObjectUtil.isNull(eduOrgQueryDTO.getIsTree()) ? 1 : eduOrgQueryDTO.getIsTree();
        //1.查询教务组织信息（包含历史）
        eduOrgQueryDTO.setIsTree(Constant.ZERO);//先平铺利于后面处理
        List<EduOrgTreeVO> eduOrgTreeVOList = this.queryHistoryEducationalOrgTree(eduOrgQueryDTO);
        if (CollUtil.isEmpty(eduOrgTreeVOList)) {
            log.warn("【查询教务组织信息】-【查询失败，教务组织信息为空】-【请求参数:{}】", JSON.toJSONString(eduOrgQueryDTO));
            return Collections.emptyList();
        }
        List<String> currentStaffRoleCodeList = getCurrentStaffRole();

        List<String> defaultAdminRole = ModuleCodeEnum.listAdminRoleByCode(eduOrgQueryDTO.getModuleCode());
        log.info("【查询教务组织信息】-【当前登陆人角色:{},管理员默认角色:{}】",
                JSONUtil.toJsonStr(currentStaffRoleCodeList),
                JSON.toJSONString(defaultAdminRole));

        //4.是否为管理员权限，管理员可以查看全部数据（包含历史权限）
        if (CollUtil.isNotEmpty(CollUtil.intersection(currentStaffRoleCodeList, defaultAdminRole))) {
            List<EduOrgTreeVO> tree = this.toTree(eduOrgTreeVOList, eduOrgQueryDTO);
            return BasicConvert.INSTANCE.toGlobalEduOrgTreeList(
                    this.filterShowViewTree(tree, eduOrgQueryDTO.getShowViewTypeList()));
        }

        Map<Long, SchoolYearVo> lastSchoolYearMap = cacheSaasManager.getLastSchoolYear();
        SchoolYearVo schoolYearVo = new SchoolYearVo();
        if (SECTION.getCode().equals(eduOrgQueryDTO.getCurrentIdType())) {
            schoolYearVo = lastSchoolYearMap.get(Convert.toLong(eduOrgQueryDTO.getCurrentId()));
        }
        //是否当前学年
        boolean isCurrentSchoolYear = schoolYearVo == null || schoolYearVo.isCurrentSchoolYear();

        List<Long> staffTeachClassList = this.listCurrentStaffTeachClass(currentStaffRoleCodeList, isCurrentSchoolYear);

        if (!isCurrentSchoolYear && this.checkTeacherPermission(currentStaffRoleCodeList)) {
            Long campusId = SaasCurrentIdTypeEnum.CAMPUS.getCode().equals(eduOrgQueryDTO.getCurrentIdType())
                    ? eduOrgQueryDTO.getCurrentId() : null;
            Map<Long, TeacherClassRelationVO> longTeacherClassRelationVOMap = saasHistoryTeacherRelationCacheManager.listAllTeacherClassIdsRelation(
                    WebUtil.getSchoolIdLong(), eduOrgQueryDTO.getSchoolYear(), campusId, null, null, null);
            Long staffId = WebUtil.getStaffIdLong();
            if (longTeacherClassRelationVOMap.containsKey(staffId)) {
                TeacherClassRelationVO teacherClassRelationVO = longTeacherClassRelationVOMap.get(staffId);
                if (ObjectUtil.isNotEmpty(teacherClassRelationVO)) {
                    CollUtil.addAll(staffTeachClassList, teacherClassRelationVO.getClassIdList());
                }
            }
        }

        if (CollUtil.isEmpty(staffTeachClassList)) {
            log.warn("【查询教务组织信息】-【查询失败，任教信息为空】-【请求参数:{},角色信息:{}】",
                    JSON.toJSONString(eduOrgQueryDTO), JSON.toJSONString(currentStaffRoleCodeList));
            return Collections.emptyList();
        }
        //6.过滤数据
        Map<Long, Long> idParentIdMap = eduOrgTreeVOList.stream()
                .collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getParentId));
        Set<Long> orgIdSet = getOrgIdSet(staffTeachClassList, new TreeSet<>(), idParentIdMap);
        List<EduOrgTreeVO> resultList = this.filterList(orgIdSet, eduOrgTreeVOList, new HashMap<>());
        log.info("【查询教务组织信息】-【当前教职工任教班级:{},请求参数:{}】", JSON.toJSONString(staffTeachClassList),
                JSON.toJSONString(eduOrgQueryDTO));
        if (isTree == 0) {
            return BasicConvert.INSTANCE.toGlobalEduOrgTreeList(resultList);
        }
        List<EduOrgTreeVO> tree = this.toTree(resultList, eduOrgQueryDTO);

        return BasicConvert.INSTANCE.toGlobalEduOrgTreeList(
                this.filterShowViewTree(tree, eduOrgQueryDTO.getShowViewTypeList()));
    }

    /**
     * 获取学校下的教务组织架构树，包含走班
     */
    @Override
    public List<GlobalEduOrgTreeVO> queryGlobalEducationalOrgTreeV3(EduOrgQueryDTO eduOrgQueryDTO) {
        Integer isTree = ObjectUtil.isNull(eduOrgQueryDTO.getIsTree()) ? 1 : eduOrgQueryDTO.getIsTree();
        //1.查询教务组织信息（包含历史）
        eduOrgQueryDTO.setIsTree(Constant.ZERO);//先平铺利于后面处理
        List<EduOrgTreeVO> eduOrgTreeVOList = this.queryHistoryEducationalOrgTreeV3(eduOrgQueryDTO);
        if (CollUtil.isEmpty(eduOrgTreeVOList)) {
            log.warn("【查询教务组织信息】-【查询失败，教务组织信息为空】-【请求参数:{}】", JSON.toJSONString(eduOrgQueryDTO));
            return Collections.emptyList();
        }
        List<String> currentStaffRoleCodeList = getCurrentStaffRole();

        List<String> defaultAdminRole = ModuleCodeEnum.listAdminRoleByCode(eduOrgQueryDTO.getModuleCode());
        log.info("【查询教务组织信息】-【当前登陆人角色:{},管理员默认角色:{}】",
                JSONUtil.toJsonStr(currentStaffRoleCodeList),
                JSON.toJSONString(defaultAdminRole));

        //4.是否为管理员权限，管理员可以查看全部数据（包含历史权限）
        if (CollUtil.isNotEmpty(CollUtil.intersection(currentStaffRoleCodeList, defaultAdminRole))) {
            List<EduOrgTreeVO> tree = this.toTree(eduOrgTreeVOList, eduOrgQueryDTO);
            return BasicConvert.INSTANCE.toGlobalEduOrgTreeList(
                    this.filterShowViewTree(tree, eduOrgQueryDTO.getShowViewTypeList()));
        }

        Map<Long, SchoolYearVo> lastSchoolYearMap = cacheSaasManager.getLastSchoolYear();
        SchoolYearVo schoolYearVo = new SchoolYearVo();
        if (SECTION.getCode().equals(eduOrgQueryDTO.getCurrentIdType())) {
            schoolYearVo = lastSchoolYearMap.get(Convert.toLong(eduOrgQueryDTO.getCurrentId()));
        }
        //是否当前学年
        boolean isCurrentSchoolYear = schoolYearVo == null || schoolYearVo.isCurrentSchoolYear();

        List<Long> staffTeachClassList = this.listCurrentStaffTeachClass(currentStaffRoleCodeList, isCurrentSchoolYear);

        if (!isCurrentSchoolYear && this.checkTeacherPermission(currentStaffRoleCodeList)) {
            Long campusId = SaasCurrentIdTypeEnum.CAMPUS.getCode().equals(eduOrgQueryDTO.getCurrentIdType())
                    ? eduOrgQueryDTO.getCurrentId() : null;
            Map<Long, TeacherClassRelationVO> longTeacherClassRelationVOMap = saasHistoryTeacherRelationCacheManager
                    .listAllTeacherClassIdsRelation(WebUtil.getSchoolIdLong(), eduOrgQueryDTO.getSchoolYear(),
                            campusId, null, null, null);
            Long staffId = WebUtil.getStaffIdLong();
            if (longTeacherClassRelationVOMap.containsKey(staffId)) {
                TeacherClassRelationVO teacherClassRelationVO = longTeacherClassRelationVOMap.get(staffId);
                if (ObjectUtil.isNotEmpty(teacherClassRelationVO)) {
                    CollUtil.addAll(staffTeachClassList, teacherClassRelationVO.getClassIdList());
                }
            }
        }

        if (CollUtil.isEmpty(staffTeachClassList)) {
            log.warn("【查询教务组织信息】-【查询失败，任教信息为空】-【请求参数:{},角色信息:{}】",
                    JSON.toJSONString(eduOrgQueryDTO), JSON.toJSONString(currentStaffRoleCodeList));
            return Collections.emptyList();
        }
        //6.过滤数据
        Map<Long, Long> idParentIdMap = eduOrgTreeVOList.stream()
                .collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getParentId));
        Set<Long> orgIdSet = getOrgIdSet(staffTeachClassList, new TreeSet<>(), idParentIdMap);
        List<EduOrgTreeVO> resultList = this.filterList(orgIdSet, eduOrgTreeVOList, new HashMap<>());
        log.info("【查询教务组织信息】-【当前教职工任教班级:{},请求参数:{}】", JSON.toJSONString(staffTeachClassList),
                JSON.toJSONString(eduOrgQueryDTO));
        if (isTree == 0) {
            return BasicConvert.INSTANCE.toGlobalEduOrgTreeList(resultList);
        }
        List<EduOrgTreeVO> tree = this.toTree(resultList, eduOrgQueryDTO);

        return BasicConvert.INSTANCE.toGlobalEduOrgTreeList(
                this.filterShowViewTree(tree, eduOrgQueryDTO.getShowViewTypeList()));
    }

    /**
     * 判断当前用户是否拥有管理员权限 (校长、学生处主任、产品运营、教务主任)
     *
     * @return
     */
    @Override
    public Boolean judgeCurrentUserAdminAuth() {
        List<String> currentStaffRoleCodeList = getCurrentStaffRole();

        List<String> defaultAdminRole = ModuleCodeEnum.listAdminRoleByCode(null);
        log.info("【查询教务组织信息】-【当前登陆人角色:{},管理员默认角色:{}】",
                JSONUtil.toJsonStr(currentStaffRoleCodeList),
                JSON.toJSONString(defaultAdminRole));

        //4.是否为管理员权限，管理员可以查看全部数据（包含历史权限）
        if (CollUtil.isNotEmpty(CollUtil.intersection(currentStaffRoleCodeList, defaultAdminRole))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    private List<String> getCurrentStaffRole() {

        //2.特殊逻辑处理：用于值日记录，值日学生直接赋予校长角色
        if (StringUtils.isBlank(WebUtil.getStaffId()) && StringUtils.isNotBlank(WebUtil.getStudentIdStr())) {
            return Collections.singletonList(SCHOOL_HEAD);
        }

        //3.查询教职工所有组织机构角色信息
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = cacheSaasManager.listOrgIdAndRoleId();
        if (ObjectUtil.isNull(listOrgIdAndRoleIdVO) || CollUtil.isEmpty(listOrgIdAndRoleIdVO.getRoleCodeList())) {
            log.warn("【查询教务组织信息】-【查询失败，角色信息为空】-【返回结果:{}】",
                    JSONUtil.toJsonStr(listOrgIdAndRoleIdVO));
            return Collections.emptyList();
        }

        return listOrgIdAndRoleIdVO.getRoleCodeList();
    }

    /**
     * 获取历史教职工任教班级信息 1.班主任、教师、体育老师：历史任教班级
     */
    private List<Long> listHistoryStaffTeachClass(List<String> currentStaffRoleCodeList, EduOrgQueryDTO eduOrgQueryDTO,
                                                  boolean isPeTeacher) {

        if (!currentStaffRoleCodeList.contains(RoleConstant.CLASS_TEACHER) && !currentStaffRoleCodeList.contains(
                RoleConstant.TEACHER) && !isPeTeacher) {
            return Collections.emptyList();
        }

        Map<Long, TeacherClassRelationVO> teacherClassRelationMap = saasHistoryTeacherRelationCacheManager.listAllTeacherClassIdsRelation(
                WebUtil.getSchoolIdLong(), eduOrgQueryDTO.getSchoolYear(), WebUtil.getCampusIdLong(), null, null, null);
        if (CollUtil.isEmpty(teacherClassRelationMap)) {
            log.warn("【查询教务组织信息】-【获取历史任教信息为空】-【请求参数,schoolId:{}, schoolYear:{}】",
                    WebUtil.getSchoolId(), eduOrgQueryDTO.getSchoolYear());
            return Collections.emptyList();
        }

        TeacherClassRelationVO teacherClassRelationVO = teacherClassRelationMap.get(WebUtil.getStaffIdLong());
        if (ObjectUtil.isNull(teacherClassRelationVO)) {
            log.warn("【查询教务组织信息】-【当前教职工历史任教关系为空】-【请求参数,staffId:{}】", WebUtil.getStaffIdLong());
            return Collections.emptyList();
        }

        return teacherClassRelationVO.getClassIdList();
    }

    /**
     * 获取当前教职工任教班级信息 1. 年级组长：任职年级下的所有班级 2. 教师、值日老师、体育老师：任教班级 3. 班主任：担任班主任的班级
     */
    private List<Long> listCurrentStaffTeachClass(List<String> currentStaffRoleCodeList, boolean isCurrentSchoolYear) {
        /*if (!isCurrentSchoolYear) {
            return Collections.emptyList();
        }*/

        //查询教职工任教情况(行政班级、未毕业)
        EduStaffClassQueryDTO requestEduDTO = this.buildQueryEduClassDTO();
        List<EduStaffClassVO> eduStaffClassVOS = basicInfoRemote.queryStaffTeachInfo(requestEduDTO);
        if (CollUtil.isEmpty(eduStaffClassVOS)) {
            log.warn("【查询教务组织信息】-【获取教职工任教情况为空】-【请求参数:{}】", JSON.toJSONString(requestEduDTO));
            return Collections.emptyList();
        }

        EduStaffClassVO staffClassVO = CollUtil.getFirst(eduStaffClassVOS);
        //员工担任年级组长的年级集合
        List<Long> gradeIdList = staffClassVO.getLeaderGradeInfos().stream().map(EduStaffGradeVO::getGradeId)
                .collect(Collectors.toList());
        //员工授课班级集合
        List<Long> classIdList = staffClassVO.getTeachClassInfos().stream().map(EduStaffTeachClassVO::getClassId)
                .collect(Collectors.toList());
        //员工担任班主任的班级集合
        List<Long> masterClassIdList = staffClassVO.getLeaderClassInfos().stream()
                .map(EduStaffMasterClassVO::getClassId).collect(Collectors.toList());

        List<Long> resultClassIdList = new ArrayList<>();

        //年级组长：获取年级下所有班级（行政班、未毕业）
        if (currentStaffRoleCodeList.contains(RoleConstant.GRADE_LEADER)) {
            resultClassIdList.addAll(this.listClassIdByGradeIdList(gradeIdList));
        }
        //值日老师
        if (currentStaffRoleCodeList.contains(RoleConstant.DUTY_TEACHER)) {
            resultClassIdList.addAll(classIdList);
        }

        //班主任当前学年
        if (currentStaffRoleCodeList.contains(RoleConstant.CLASS_TEACHER) && isCurrentSchoolYear) {
            resultClassIdList.addAll(masterClassIdList);
        }
        //教师、体育老师当前学年
        if ((this.checkPeTeacherRole() || currentStaffRoleCodeList.contains(RoleConstant.TEACHER))
                && isCurrentSchoolYear) {
            resultClassIdList.addAll(classIdList);
        }
        return resultClassIdList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 校验当前教职工是否为体育老师 判断逻辑：然后判断角色名称是否为（体育老师、体育带头人）
     */
    private boolean checkPeTeacherRole() {
        ResRoleQueryDTO resRoleQueryDTO = new ResRoleQueryDTO();
        resRoleQueryDTO.setStaffIds(CollUtil.newArrayList(WebUtil.getStaffIdLong()));
        resRoleQueryDTO.setId(WebUtil.getSchoolIdLong());
        resRoleQueryDTO.setType(SaasOrgQueryEnum.SCHOOL_ID.getCode());
        List<ResStaffRoleVO> staffRoleList = basicInfoRemote.queryStaffRoleList(resRoleQueryDTO);
        return CollUtil.isNotEmpty(staffRoleList) && ObjectUtil.isNotEmpty(staffRoleList.get(0)) && CollUtil.isNotEmpty(
                staffRoleList.get(0).getRoles()) && staffRoleList.get(0).getRoles().stream()
                .anyMatch(item -> SPORT_TEACHER_LIST.contains(item.getRoleName()));
    }

    /**
     * 查询年级下所有班级信息 （行政班、未毕业）
     */
    private List<Long> listClassIdByGradeIdList(List<Long> gradeIdList) {

        if (CollUtil.isEmpty(gradeIdList)) {
            return Collections.emptyList();
        }

        List<Long> result = Lists.newArrayList();

        for (Long gradeId : gradeIdList) {
            EduClassQueryDTO request = new EduClassQueryDTO();
            request.setGradeId(gradeId);
            request.setSchoolId(WebUtil.getSchoolIdLong());
            request.setGraduationStatus(Constant.STR_ZERO);
            List<String> classTypes = new ArrayList<>();
            classTypes.add(SaasClassTypeEnum.XINGZHENG.getCode());
            request.setClassTypes(classTypes);
            //暂时没有通过年级id批量获取的方法
            List<EduClassInfoVO> response = basicInfoRemote.queryClassInfoList(request);
            result.addAll(response.stream().map(EduClassInfoVO::getId).collect(Collectors.toList()));
        }

        return result;
    }

    private EduStaffClassQueryDTO buildQueryEduClassDTO() {
        EduStaffClassQueryDTO requestDTO = new EduStaffClassQueryDTO();
        List<Long> staffIdList = new ArrayList<>();
        staffIdList.add(WebUtil.getStaffIdLong());
        List<String> classTypeList = new ArrayList<>();
        classTypeList.add(SaasClassTypeEnum.XINGZHENG.getCode());
        requestDTO.setStaffIds(staffIdList);
        requestDTO.setSchoolId(WebUtil.getSchoolIdLong());
        requestDTO.setClassTypes(classTypeList);
        requestDTO.setGraduationStatus(Constant.STR_ZERO);
        return requestDTO;
    }


    private List<EduOrgTreeVO> checkPermission(List<String> roleCodeList, EduOrgQueryDTO eduOrgQueryDTO) {
        if (CollUtil.isEmpty(roleCodeList)) {
            return Collections.emptyList();
        }
        // 校长、学生处主任、教务处主任
        if (roleLogic.isHaveAllDataMasterRole(roleCodeList)) {
            List<EduOrgTreeVO> allDataList = this.checkAllDataPermission(eduOrgQueryDTO);
            if (CollUtil.isNotEmpty(allDataList)) {
                return allDataList;
            }
        }
        // 年级组长
        if (roleLogic.isHaveGradeStaffRole(roleCodeList)) {
            List<EduOrgTreeVO> gradeStaffList = this.checkGradeStaffPermission(eduOrgQueryDTO);
            if (CollUtil.isNotEmpty(gradeStaffList)) {
                return gradeStaffList;
            }
        }
        // 老师历史授课关系，班主任、任教老师、体育老师
        if (this.checkTeacherPermission(roleCodeList)) {
            List<EduOrgTreeVO> teachingStaffList = this.checkTeachingStaffPermission(eduOrgQueryDTO);
            if (CollUtil.isNotEmpty(teachingStaffList)) {
                return teachingStaffList;
            }
        }
        // 值日老师
        if (roleLogic.isHaveDutyTeacherMasterRole(roleCodeList)) {
            return this.checkOrdinaryPermission(eduOrgQueryDTO);
        }
        return new ArrayList<>();
    }

    @Override
    public List<EduOrgTreeVO> queryGlobalEducationalOrgTree(Boolean isGuidanceTeacher, EduOrgQueryDTO eduOrgQueryDTO) {
        log.info(
                "BasicInfoServiceImpl.queryGlobalEducationalOrgTree【全局筛选列表】isGuidanceTeacher:{}, eduOrgQueryDTO:{}",
                isGuidanceTeacher, Convert.toStr(eduOrgQueryDTO));
        return this.checkOrdinaryPermission(eduOrgQueryDTO);
    }

    private boolean checkTeacherPermission(List<String> roleCodeList) {
        // 老师历史授课关系，班主任、任教老师
        if (roleLogic.isHaveTeachingStaffMasterRole(roleCodeList)) {
            return true;
        }
        // 体育老师 角色权限判断（使用当前登录人查询角色信息，然后判断角色名称是否为【体育老师、体育带头人】）
        ResRoleQueryDTO resRoleQueryDTO = new ResRoleQueryDTO();
        resRoleQueryDTO.setStaffIds(CollUtil.newArrayList(WebUtil.getStaffIdLong()));
        resRoleQueryDTO.setId(WebUtil.getSchoolIdLong());
        resRoleQueryDTO.setType(SaasOrgQueryEnum.SCHOOL_ID.getCode());
        List<ResStaffRoleVO> staffRoleList = basicInfoRemote.queryStaffRoleList(resRoleQueryDTO);
        return CollUtil.isNotEmpty(staffRoleList) && ObjectUtil.isNotEmpty(staffRoleList.get(0)) && CollUtil.isNotEmpty(
                staffRoleList.get(0).getRoles()) && staffRoleList.get(0).getRoles().stream()
                .anyMatch(item -> SPORT_TEACHER_LIST.contains(item.getRoleName()));
    }

    @Override
    public CheckPermissionVo checkPermission(Long campusSectionId, String schoolYear, Boolean isCurrentYear,
                                             String gradeId, String classId, Date checkTime) {

        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();

        // 当前老师任教过的班级列表
        Map<Long/*staffId*/, TeacherClassRelationVO> longTeacherClassRelationVOMap = saasHistoryTeacherRelationCacheManager.listAllTeacherClassIdsRelation(
                WebUtil.getSchoolIdLong(), schoolYear, WebUtil.getCampusIdLong(), campusSectionId,
                Convert.toLong(gradeId), Convert.toLong(classId));

        Long staffId = WebUtil.getStaffIdLong();
        CheckPermissionVo checkPermissionVo = new CheckPermissionVo();
        // 获取当前登录教职工所有组织机构角色id
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = cacheSaasManager.listOrgIdAndRoleId();
        log.info("BasicInfoServiceImpl.checkPermission【权限校验】- 获取登录人的机构、角色列表，消耗时长：【{}】ms",
                TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        List<String> roleCodeList;
        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            return checkPermissionVo;
        }
        Set<String> gradeIdList = new HashSet<>();
        List<String> classIdList = new ArrayList<>();
        // 所有权限：校长、学生处主任、教务处主任
        if (roleLogic.isHaveAllDataMasterRole(roleCodeList)) {
            return this.checkPermission(campusSectionId, schoolYear, gradeId, classId, gradeIdList, classIdList);
        }
        // 年级组长权限
        if (roleLogic.isHaveGradeStaffRole(roleCodeList)) {
            List<com.hailiang.saas.model.vo.educational.EduStaffGradeVO> eduStaffGradeVOS = saasStaffManager.queryStaffGradeInfo(
                    WebUtil.getSchoolIdLong(), WebUtil.getStaffIdLong());
            if (CollUtil.isNotEmpty(eduStaffGradeVOS)) {
                gradeIdList.addAll(eduStaffGradeVOS.stream().map(item -> Convert.toStr(item.getGradeId()))
                        .collect(Collectors.toSet()));
            }
        }
        // 任教老师（包含班主任、体育老师、值日老师）权限：历史所教班级和当前所教班级历史数据
        if (this.checkTeacherPermission(roleCodeList) || roleLogic.isHaveDutyTeacherMasterRole(roleCodeList)) {
            TeacherClassRelationVO teacherClassRelationVO = longTeacherClassRelationVOMap.get(staffId);
            classIdList = this.listTeacherClassRelation(teacherClassRelationVO, gradeIdList, isCurrentYear);
        }

        return this.checkPermission(campusSectionId, schoolYear, gradeId, classId, gradeIdList, classIdList);
    }

    private List<String> listTeacherClassRelation(TeacherClassRelationVO teacherClassRelationVO,
                                                  Set<String> gradeIdList, Boolean isCurrentYear) {
        List<String> classIdList = new ArrayList<>();
        if (BeanUtil.isEmpty(teacherClassRelationVO)) {
            log.warn("BasicInfoServiceImpl.listTeacherClassRelation，入参为空");
            return classIdList;
        }
        if (Objects.equals(Boolean.TRUE, isCurrentYear)) {
            // 当前学年
            if (CollUtil.isNotEmpty(teacherClassRelationVO.getCurrentClassList())) {
                classIdList = teacherClassRelationVO.getCurrentClassList().stream().map(current -> {
                    if (Objects.isNull(current)) {
                        return null;
                    }
                    if (Objects.nonNull(current.getGradeId())) {
                        gradeIdList.add(Convert.toStr(current.getGradeId()));
                    }
                    return Objects.isNull(current.getClassId()) ? null : Convert.toStr(current.getClassId());
                }).filter(Objects::nonNull).collect(Collectors.toList());
            }
        } else if (CollUtil.isNotEmpty(teacherClassRelationVO.getHisClassList())) {
            classIdList = teacherClassRelationVO.getHisClassList().stream().map(history -> {
                if (Objects.isNull(history)) {
                    return null;
                }
                if (Objects.nonNull(history.getGradeId())) {
                    gradeIdList.add(Convert.toStr(history.getGradeId()));
                }
                return Objects.isNull(history.getClassId()) ? null : Convert.toStr(history.getClassId());
            }).filter(Objects::nonNull).collect(Collectors.toList());
        } else if (CollUtil.isNotEmpty(teacherClassRelationVO.getClassIdList())) {
            return teacherClassRelationVO.getClassIdList().stream().map(Convert::toStr).collect(Collectors.toList());
        } else {
            log.error("BasicInfoServiceImpl.listTeacherClassRelation【年级发生变更，没有了数据权限】schoolYear:{}",
                    isCurrentYear);
            throw new BizException(NON_AUTH_FOR_UPDATED_CLASS_RESPONSE_ERROR);
        }
        return classIdList;
    }


    private CheckPermissionVo checkPermission(Long campusSectionId, String schoolYear, String gradeId, String classId,
                                              Set<String> gradeIdList, List<String> classIdList) {

        CheckPermissionVo checkPermissionVo = new CheckPermissionVo();

        if (StringUtils.isEmpty(gradeId) || Constant.MINUS_ONE.equals(gradeId)) {
            checkPermissionVo.setGradeIdList(new ArrayList<>(gradeIdList));
        } else if (CollUtil.isEmpty(gradeIdList) || gradeIdList.contains(gradeId)) {
            checkPermissionVo.setGradeIdList(CollUtil.newArrayList(gradeId));
        } else {
            log.error(
                    "BasicInfoServiceImpl.checkPermission【年级发生变更，没有了数据权限】campusSectionId:{}, schoolYear:{}, gradeId:{}, classId:{}",
                    campusSectionId, schoolYear, gradeId, classId);
            throw new BizException(NON_AUTH_FOR_UPDATED_CLASS_RESPONSE_ERROR);
        }

        if (StringUtils.isEmpty(classId) || Constant.MINUS_ONE.equals(classId)) {
            checkPermissionVo.setClassIdList(classIdList);
            checkPermissionVo.setHasPermission(Boolean.TRUE);
            return checkPermissionVo;
        } else if (CollUtil.isEmpty(classIdList) || classIdList.contains(classId)) {
            checkPermissionVo.setClassIdList(CollUtil.newArrayList(classId));
        } else {
            log.error(
                    "BasicInfoServiceImpl.checkPermission【班级发生变更，没有了数据权限】campusSectionId:{}, schoolYear:{}, gradeId:{}, classId:{}",
                    campusSectionId, schoolYear, gradeId, classId);
            throw new BizException(NON_AUTH_FOR_UPDATED_CLASS_RESPONSE_ERROR);
        }
        checkPermissionVo.setHasPermission(Boolean.TRUE);

        return checkPermissionVo;
    }

    /**
     * 获取学校下的年级权限
     */
    @Override
    public List<EduOrgTreeVO> queryEducationalOrgTreeForGrade(EduOrgQueryDTO eduOrgQueryDTO) {
        List<EduOrgTreeVO> eduOrgTreeVOList = new ArrayList<>();
        //记录接口的时间消耗
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = listOrgIdAndRoleId();
        log.info("【获取教学关系组织树】-获取登录人的机构、角色列表，消耗时长：【{}】", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        List<String> roleCodeList;

        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())
                && !listOrgIdAndRoleIdVO.getOrgIdList().isEmpty()) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            log.info("【获取教学关系组织树】-当前登录人未配置角色，直接返回空值,listOrgIdAndRoleIdVO:{}",
                    JSONUtil.toJsonStr(listOrgIdAndRoleIdVO));
            return new ArrayList<>();
        }
        log.info("【获取教学关系组织树】-当前登录人具有的角色{}", JSONUtil.toJsonStr(roleCodeList));
        //saas默认角色code未配置，暂时先采用名称匹配，若saas后续配置，更换为code
        List<EduOrgTreeVO> superTreeList = checkSupPermission(roleCodeList, eduOrgQueryDTO);
        if (CollUtil.isNotEmpty(superTreeList)) {
            log.info("【获取教学关系组织树】-【管理员】角色");
            return superTreeList;
        }

        // 年级组长
        if (roleLogic.isHaveGradeStaffRole(roleCodeList)) {
            List<EduOrgTreeVO> ordinaryTreeList = this.checkGradeStaffPermission(eduOrgQueryDTO);
            if (CollUtil.isNotEmpty(ordinaryTreeList)) {
                log.info("【获取教学关系组织树】-【年级组长】角色");
                return ordinaryTreeList;
            }
        }
        return eduOrgTreeVOList;
    }

    /**
     * 查学校组织树形 如果传入学校id则默认筛选出当前校区下数据，支持叶子节点名称模糊搜索
     */
    @Override
    public List<EduOrgTreeVO> querySchoolOrgTree(EduGradeClassQueryDTO param) {

        boolean errorShowViewFlag = param.getShowViewTypeList().stream()
                .anyMatch(item -> !SaasClassOrgTypeEnum.isClassType(item));
        AssertUtil.checkIsFalse(errorShowViewFlag, "展示层级列表输入有误，请检查");

        EduOrganizationalQueryDTO query = new EduOrganizationalQueryDTO().setCurrentId(param.getCurrentId())
                .setCurrentIdType(param.getCurrentIdType()).setEndType(param.getEndType()).setName(param.getName())
                .setShowViewTypeList(param.getShowViewTypeList());

        return this.queryEducationalOrgTree(query);
    }

    /**
     * 根据层级范围查询组织机构信息，支持名称对叶子节点模糊搜索 支持只展示某些层级
     */
    @Override
    public List<EduOrgTreeVO> queryEducationalOrgTree(EduOrganizationalQueryDTO param) {
        AssertUtil.checkIsTrue(param.getCurrentIdType() <= param.getEndType(), "endType输入错误，请检查");
        List<Integer> showViewTypeList = param.getShowViewTypeList().stream()
                .filter(item -> item >= param.getCurrentIdType() && item <= param.getEndType()).distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(showViewTypeList)) {
            return Collections.emptyList();
        }
        param.setShowViewTypeList(showViewTypeList);

        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setIsTree(Constant.YES).setCurrentId(param.getCurrentId())
                .setCurrentIdType(param.getCurrentIdType()).setEndType(param.getEndType());
        List<EduOrgTreeVO> orgTreeList = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        if (CollUtil.isEmpty(orgTreeList)) {
            return Collections.emptyList();
        }
        // 如果查询层级是学校，过滤出当前校区的数据
        if (SaasCurrentIdTypeEnum.SCHOOL.getCode().equals(param.getCurrentIdType())) {
            this.filterCurrentCampusInfo(orgTreeList, WebUtil.getCampusIdLong());
        }

        // 过滤出展示层级
        List<EduOrgTreeVO> viewTreeList = this.filterShowViewTree(orgTreeList, param.getShowViewTypeList());
        if (StrUtil.isBlank(param.getName())) {
            return viewTreeList;
        }

        // 按名称过滤
        return this.filterTreeList(viewTreeList, param.getEndType(), ParamUtil.transform(param.getName()));
    }

    /**
     * 过滤出当前学校的数据
     *
     * @param orgTreeList
     * @param campusId
     */
    private void filterCurrentCampusInfo(List<EduOrgTreeVO> orgTreeList, Long campusId) {
        EduOrgTreeVO school = orgTreeList.get(Constant.ZERO);
        if (!SaasCurrentIdTypeEnum.SCHOOL.getCode().equals(school.getType())) {
            return;
        }
        if (CollUtil.isEmpty(school.getChildren())) {
            return;
        }
        // 当前层级不是校区则不处理
        Integer currentType = school.getChildren().get(0).getType();
        if (!SaasCurrentIdTypeEnum.CAMPUS.getCode().equals(currentType)) {
            return;
        }
        List<EduOrgTreeVO> campusInfoList = school.getChildren().stream().filter(item -> campusId.equals(item.getId()))
                .collect(Collectors.toList());
        school.setChildren(campusInfoList);
    }

    /**
     * 过滤出需要的层级
     *
     * @param treeList
     * @param showViewTypeList
     * @return
     */
    private List<EduOrgTreeVO> filterShowViewTree(List<EduOrgTreeVO> treeList, List<Integer> showViewTypeList) {
        if (CollUtil.isEmpty(showViewTypeList)) {
            return treeList;
        }
        Integer topViewType = CollUtil.min(showViewTypeList);
        // 过滤出顶层的节点
        List<EduOrgTreeVO> goalList = this.filterHeadShowViewTree(treeList, topViewType);
        if (CollUtil.isEmpty(goalList)) {
            return Collections.emptyList();
        }
        // 只展示一层
        if (CollUtil.size(showViewTypeList) == Constant.ONE) {
            goalList.stream().forEach(item -> item.setChildren(null));
            return goalList;
        }

        for (EduOrgTreeVO item : goalList) {
            this.filterNextShowViewTree(item, showViewTypeList);
        }

        return goalList;
    }

    /**
     * 获取顶层节点列表
     *
     * @param treeList
     * @param topViewType 根节点类型
     * @return
     */
    private List<EduOrgTreeVO> filterHeadShowViewTree(List<EduOrgTreeVO> treeList, Integer topViewType) {
        if (CollUtil.isEmpty(treeList)) {
            return treeList;
        }
        Integer currentType = treeList.get(0).getType();
        if (topViewType.equals(currentType)) {
            return treeList;
        }
        List<EduOrgTreeVO> resultList = Lists.newArrayList();
        for (EduOrgTreeVO item : treeList) {
            if (CollUtil.isNotEmpty(item.getChildren())) {
                resultList.addAll(item.getChildren());
            }
        }
        return this.filterHeadShowViewTree(resultList, topViewType);
    }

    /**
     * 处理子层级--过滤出需要的层级信息
     *
     * @param tree
     * @param showViewTypeList
     */
    private void filterNextShowViewTree(EduOrgTreeVO tree, List<Integer> showViewTypeList) {
        Integer currentType = tree.getType();
        Integer leafType = CollUtil.max(showViewTypeList);
        if (leafType.equals(currentType)) {
            tree.setChildren(null);
            return;
        }
        List<EduOrgTreeVO> children = tree.getChildren();
        if (CollUtil.isEmpty(children)) {
            return;
        }
        Integer nextType = children.get(0).getType();

        if (!showViewTypeList.contains(nextType)) {
            this.handleTree(tree, tree.getChildren(), showViewTypeList);
        }
        for (EduOrgTreeVO item : tree.getChildren()) {
            this.filterNextShowViewTree(item, showViewTypeList);
        }

    }

    private void handleTree(EduOrgTreeVO tree, List<EduOrgTreeVO> childList, List<Integer> showViewTypeList) {
        if (CollUtil.isEmpty(childList)) {
            return;
        }
        List<EduOrgTreeVO> list = Lists.newArrayList();
        for (EduOrgTreeVO item : childList) {
            if (CollUtil.isNotEmpty(item.getChildren())) {
                list.addAll(item.getChildren());
            }
        }
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Integer currentType = list.get(0).getType();
        if (showViewTypeList.contains(currentType)) {
            tree.setChildren(list);
            return;
        }
        this.handleTree(tree, list, showViewTypeList);

    }

    /**
     * 按名称模糊搜索叶子节点组织机构
     *
     * @param orgTreeList
     * @param endType
     * @param leafName
     * @return
     */
    private List<EduOrgTreeVO> filterTreeList(List<EduOrgTreeVO> orgTreeList, Integer endType, String leafName) {
        if (CollUtil.isEmpty(orgTreeList)) {
            return orgTreeList;
        }
        if (endType.equals(orgTreeList.get(0).getType())) {
            return orgTreeList.stream()
                    .filter(item -> StrUtil.isNotBlank(item.getName()) && item.getName().contains(leafName))
                    .collect(Collectors.toList());
        }
        for (EduOrgTreeVO item : orgTreeList) {
            List<EduOrgTreeVO> resultList = this.filterTreeList(item.getChildren(), endType, leafName);
            item.setChildren(resultList);
        }

        return orgTreeList.stream().filter(item -> CollUtil.isNotEmpty(item.getChildren()))
                .collect(Collectors.toList());
    }

    /**
     * 判断校长,学生处主任,教务主任,运用人员权限
     *
     * @param roleCodeList
     * @return
     */
    @Override
    public List<EduOrgTreeVO> checkSupPermission(List<String> roleCodeList, EduOrgQueryDTO eduOrgQueryDTO) {

        if (!roleLogic.isHaveMaterRole(roleCodeList)) {
            return Collections.emptyList();
        }

        List<EduOrgTreeVO> eduOrgTreeVOList = cacheSaasManager.queryEducationalOrgTree(eduOrgQueryDTO);
        if (CollUtil.isEmpty(eduOrgTreeVOList) || CollUtil.isEmpty(eduOrgQueryDTO.getShowViewTypeList())) {
            return eduOrgTreeVOList;
        }

        return this.filterShowViewTree(eduOrgTreeVOList, eduOrgQueryDTO.getShowViewTypeList());
    }

    /**
     * 判断普通角色权限
     *
     * @param roleCodeList
     * @return
     */
    @Override
    public List<EduOrgTreeVO> checkOrdinaryPermission(List<String> roleCodeList, EduOrgQueryDTO eduOrgQueryDTO) {
        if (!roleLogic.isHaveOrdinaryRole(roleCodeList)) {
            return Collections.emptyList();
        }
        return this.checkOrdinaryPermission(eduOrgQueryDTO);
    }

    private List<EduOrgTreeVO> checkOrdinaryPermission(EduOrgQueryDTO eduOrgQueryDTO) {
        eduOrgQueryDTO.setIsTree(0);
        List<EduOrgTreeVO> eduOrgTreeVOList = cacheSaasManager.queryEducationalOrgTree(eduOrgQueryDTO);
        //年级主任、班主任、学科老师需要过滤数据权限
        eduOrgTreeVOList = filterAuth(eduOrgTreeVOList, eduOrgQueryDTO);

        if (CollUtil.isEmpty(eduOrgTreeVOList) || CollUtil.isEmpty(eduOrgQueryDTO.getShowViewTypeList())) {
            return eduOrgTreeVOList;
        }
        return this.filterShowViewTree(eduOrgTreeVOList, eduOrgQueryDTO.getShowViewTypeList());
    }

    private List<EduOrgTreeVO> checkAllDataPermission(EduOrgQueryDTO eduOrgQueryDTO) {
        List<EduOrgTreeVO> eduOrgTreeVOList = this.queryHistoryEducationalOrgTree(eduOrgQueryDTO);
        if (CollUtil.isEmpty(eduOrgTreeVOList) || CollUtil.isEmpty(eduOrgQueryDTO.getShowViewTypeList())) {
            return eduOrgTreeVOList;
        }
        return this.filterShowViewTree(eduOrgTreeVOList, eduOrgQueryDTO.getShowViewTypeList());
    }

    /**
     * 过滤年级组长的年级
     *
     * @param eduOrgQueryDTO
     * @return
     */
    private List<EduOrgTreeVO> checkGradeStaffPermission(EduOrgQueryDTO eduOrgQueryDTO) {
        List<EduOrgTreeVO> eduOrgTreeVOList = this.queryHistoryEducationalOrgTree(eduOrgQueryDTO);
        // 年级组长需要过滤数据权限
        if (eduOrgQueryDTO.getRoleFilter()) {
            eduOrgTreeVOList = baseInfoLogic.filterGradeStaffAuth(eduOrgTreeVOList, eduOrgQueryDTO);
        }
        log.info("当前用户包含【年级组长】角色-需要过滤数据权限，返回结果{}", JSONUtil.toJsonStr(eduOrgTreeVOList));
        return eduOrgTreeVOList;
    }

    /**
     * 校验班主任、老师、体育老师权限
     *
     * @param eduOrgQueryDTO
     * @return
     */
    private List<EduOrgTreeVO> checkTeachingStaffPermission(EduOrgQueryDTO eduOrgQueryDTO) {
        List<EduOrgTreeVO> eduOrgTreeVOList = this.queryHistoryEducationalOrgTree(eduOrgQueryDTO);
        //年级主任、班主任、学科老师需要过滤数据权限
        if (eduOrgQueryDTO.getRoleFilter()) {
            eduOrgTreeVOList = filterAuthNew(eduOrgTreeVOList, eduOrgQueryDTO);
        }

        if (CollUtil.isEmpty(eduOrgTreeVOList) || CollUtil.isEmpty(eduOrgQueryDTO.getShowViewTypeList())) {
            return eduOrgTreeVOList;
        }
        return this.filterShowViewTree(eduOrgTreeVOList, eduOrgQueryDTO.getShowViewTypeList());
    }

    private List<EduOrgTreeVO> queryHistoryEducationalOrgTree(EduOrgQueryDTO eduOrgQueryDTO) {
        EduOrgQueryV2DTO eduOrgQueryV2DTO = new EduOrgQueryV2DTO();
        eduOrgQueryV2DTO.setCurrentId(eduOrgQueryDTO.getCurrentId());
        eduOrgQueryV2DTO.setCurrentIdType(eduOrgQueryDTO.getCurrentIdType());
        eduOrgQueryV2DTO.setSchoolYear(eduOrgQueryDTO.getSchoolYear());
        List<EduOrgTreeV2VO> eduOrgTreeV2VOList = saasSchoolCacheManager.queryHistoryEducationalOrgTree(
                eduOrgQueryV2DTO);
        return BasicConvert.INSTANCE.toEduOrgTreeVOList(eduOrgTreeV2VOList);
    }

    /**
     * saas 查询组织树
     */
    private List<EduOrgTreeVO> queryHistoryEducationalOrgTreeV3(EduOrgQueryDTO eduOrgQueryDTO) {
        EduOrgQueryV2DTO eduOrgQueryV2DTO = new EduOrgQueryV2DTO();
        eduOrgQueryV2DTO.setCurrentId(eduOrgQueryDTO.getCurrentId());
        eduOrgQueryV2DTO.setCurrentIdType(eduOrgQueryDTO.getCurrentIdType());
        eduOrgQueryV2DTO.setSchoolYear(eduOrgQueryDTO.getSchoolYear());
        List<EduOrgTreeV2VO> eduOrgTreeV2VOList = saasSchoolCacheManager.queryHistoryEducationalOrgTreeV3(
                eduOrgQueryV2DTO);
        return BasicConvert.INSTANCE.toEduOrgTreeVOList(eduOrgTreeV2VOList);
    }

    /**
     * 过滤数据权限
     */
    private List<EduOrgTreeVO> filterAuth(List<EduOrgTreeVO> eduOrgTreeVOList, EduOrgQueryDTO eduOrgQueryDTO) {
        //登录人能查看的年级列表
        List<Long> gradeIdList = new ArrayList<>();
        //员工授课班级集合
        List<Long> classIdList = new ArrayList<>();
        //员工担任班主任的班级集合
        List<Long> masterClassIdList = new ArrayList<>();
        List<EduStaffClassVO> eduStaffClassVOList = new ArrayList<>();
        if (Constant.NINETY_NINE.equals(eduOrgQueryDTO.getClassType())) {
            //查询所有班级,包括走班
            eduStaffClassVOList = listEduStaffClassALL();
        } else {
            //查询行政班
            eduStaffClassVOList = listEduStaffClass();
        }

        if (Objects.nonNull(eduStaffClassVOList) && !eduStaffClassVOList.isEmpty()) {
            EduStaffClassVO eduStaffClassVO = eduStaffClassVOList.get(0);
            gradeIdList = eduStaffClassVO.getLeaderGradeInfos().stream().map(EduStaffGradeVO::getGradeId)
                    .collect(Collectors.toList());
            classIdList = eduStaffClassVO.getTeachClassInfos().stream().map(EduStaffTeachClassVO::getClassId)
                    .collect(Collectors.toList());
            masterClassIdList = eduStaffClassVO.getLeaderClassInfos().stream().map(EduStaffMasterClassVO::getClassId)
                    .collect(Collectors.toList());
        }
        log.info("登录人能查看的年级列表，{}", JSONUtil.toJsonStr(gradeIdList));
        log.info("员工授课班级集合，{}", JSONUtil.toJsonStr(classIdList));
        log.info("员工担任班主任的班级集合，{}", JSONUtil.toJsonStr(masterClassIdList));
        classIdList.addAll(masterClassIdList);
        classIdList.addAll(listClassIdByGradeIdList(gradeIdList, classIdList));
        classIdList = classIdList.stream().distinct().collect(Collectors.toList());
        Map<Long, Long> idParentIdMap = eduOrgTreeVOList.stream()
                .collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getParentId));
        Set<Long> orgIdSet = getOrgIdSet(classIdList, new TreeSet<>(), idParentIdMap);
        //过滤数据权限
        List<EduOrgTreeVO> resultList = filterList(orgIdSet, eduOrgTreeVOList, new HashMap<>());
        List<EduOrgTreeVO> treeList = toTree(resultList, eduOrgQueryDTO);
        log.info("过滤后的最终结果，{}", JSONUtil.toJsonStr(treeList));

        return treeList;
    }

    /**
     * 过滤数据权限
     */
    private List<EduOrgTreeVO> filterAuthNew(List<EduOrgTreeVO> eduOrgTreeVOList, EduOrgQueryDTO eduOrgQueryDTO) {
        //登录人能查看的年级列表
        List<Long> gradeIdList = new ArrayList<>();
        //员工授课班级集合
        List<Long> classIdList = new ArrayList<>();
        //员工担任班主任的班级集合
        List<Long> masterClassIdList = new ArrayList<>();
        List<EduStaffClassVO> eduStaffClassVOList = new ArrayList<>();
        Map<Long, SchoolYearVo> lastSchoolYearMap = cacheSaasManager.getLastSchoolYear();
        SchoolYearVo schoolYearVo = new SchoolYearVo();
        if (SECTION.getCode().equals(eduOrgQueryDTO.getCurrentIdType())) {
            schoolYearVo = lastSchoolYearMap.get(Convert.toLong(eduOrgQueryDTO.getCurrentId()));
        }
        if (schoolYearVo == null || schoolYearVo.isCurrentSchoolYear()) {
            // 当前学年
            if (Constant.NINETY_NINE.equals(eduOrgQueryDTO.getClassType())) {
                //查询所有班级,包括走班
                eduStaffClassVOList = listEduStaffClassALL();
            } else {
                //查询行政班
                eduStaffClassVOList = listEduStaffClass();
            }
        } else {
            // 历史学年
            Long campusId = SaasCurrentIdTypeEnum.CAMPUS.getCode().equals(eduOrgQueryDTO.getCurrentIdType())
                    ? eduOrgQueryDTO.getCurrentId() : null;
            Map<Long, TeacherClassRelationVO> longTeacherClassRelationVOMap = saasHistoryTeacherRelationCacheManager.listAllTeacherClassIdsRelation(
                    WebUtil.getSchoolIdLong(), eduOrgQueryDTO.getSchoolYear(), campusId, null, null, null);
            Long staffId = WebUtil.getStaffIdLong();
            if (longTeacherClassRelationVOMap.containsKey(staffId)) {
                TeacherClassRelationVO teacherClassRelationVO = longTeacherClassRelationVOMap.get(staffId);
                classIdList = teacherClassRelationVO.getClassIdList();
            }
        }

        if (Objects.nonNull(eduStaffClassVOList) && !eduStaffClassVOList.isEmpty()) {
            EduStaffClassVO eduStaffClassVO = eduStaffClassVOList.get(0);
            gradeIdList = eduStaffClassVO.getLeaderGradeInfos().stream().map(EduStaffGradeVO::getGradeId)
                    .collect(Collectors.toList());
            classIdList = eduStaffClassVO.getTeachClassInfos().stream().map(EduStaffTeachClassVO::getClassId)
                    .collect(Collectors.toList());
            masterClassIdList = eduStaffClassVO.getLeaderClassInfos().stream().map(EduStaffMasterClassVO::getClassId)
                    .collect(Collectors.toList());
        }
        log.info("登录人能查看的年级列表，{}", JSONUtil.toJsonStr(gradeIdList));
        log.info("员工授课班级集合，{}", JSONUtil.toJsonStr(classIdList));
        log.info("员工担任班主任的班级集合，{}", JSONUtil.toJsonStr(masterClassIdList));
        classIdList.addAll(masterClassIdList);
        classIdList.addAll(listClassIdByGradeIdList(gradeIdList, classIdList));
        classIdList = classIdList.stream().distinct().collect(Collectors.toList());
        Map<Long, Long> idParentIdMap = eduOrgTreeVOList.stream()
                .collect(Collectors.toMap(EduOrgTreeVO::getId, EduOrgTreeVO::getParentId));
        Set<Long> orgIdSet = getOrgIdSet(classIdList, new TreeSet<>(), idParentIdMap);
        //过滤数据权限
        List<EduOrgTreeVO> resultList = filterList(orgIdSet, eduOrgTreeVOList, new HashMap<>());
        List<EduOrgTreeVO> treeList = toTree(resultList, eduOrgQueryDTO);
        log.info("过滤后的最终结果，{}", JSONUtil.toJsonStr(treeList));

        return treeList;
    }

    /**
     * 获取组织机构集合
     */
    private Set<Long> getOrgIdSet(List<Long> classIdList, Set<Long> orgIdSet, Map<Long, Long> idParentIdMap) {
        classIdList.forEach(classId -> {
            Long parentId = classId;
            orgIdSet.add(classId);
            while (Objects.nonNull(idParentIdMap.get(parentId))) {
                orgIdSet.add(idParentIdMap.get(parentId));
                parentId = idParentIdMap.get(parentId);
            }
        });
        return orgIdSet;
    }

    /**
     * 通过年级idlist查班级list
     */
    private List<Long> listClassIdByGradeIdList(List<Long> gradeIdList, List<Long> classIdList) {
        for (Long gradeId : gradeIdList) {
            EduClassQueryDTO eduClassQueryDTO = new EduClassQueryDTO();
            eduClassQueryDTO.setSchoolId(WebUtil.getSchoolIdLong());
            eduClassQueryDTO.setGradeId(gradeId);
            List<String> classTypes = new ArrayList<>();
            classTypes.add(SaasClassTypeEnum.XINGZHENG.getCode());
            eduClassQueryDTO.setClassTypes(classTypes);
            eduClassQueryDTO.setGraduationStatus(Constant.STR_ZERO);
            List<EduClassInfoVO> eduClassInfoVOList = basicInfoRemote.queryClassInfoList(eduClassQueryDTO);
            classIdList.addAll(eduClassInfoVOList.stream().map(EduClassInfoVO::getId).collect(Collectors.toList()));
        }
        return classIdList;
    }

    /**
     * 查询班级列表 查当前校区下班级列表，支持名称模糊搜索
     */
    @Override
    public List<QueryClassBaseInfoListVO> queryCampusClassList(CampusClassListDTO param) {
        ClassListDTO query = new ClassListDTO().setSchoolId(WebUtil.getSchoolIdLong())
                .setCampusId(WebUtil.getCampusIdLong()).setClassName(param.getClassName());

        List<ClassGradeListVO> classList = this.queryClassList(query);
        if (CollUtil.isEmpty(classList)) {
            return Collections.emptyList();
        }

        return basicConvert.toQueryClassBaseInfoListVOList(classList);
    }

    /**
     * 查询班级列表 查当前校区下班级列表，支持名称模糊搜索
     */
    @Override
    public List<ClassGradeListVO> queryClassList(ClassListDTO param) {
        EduClassQueryDTO query = new EduClassQueryDTO();
        query.setSchoolId(param.getSchoolId());
        query.setCampusId(param.getCampusId());
        query.setCampusSectionId(param.getCampusSectionId());
        query.setGraduationStatus(Constant.NO.toString());

        List<String> classTypes = new ArrayList<>();
        classTypes.add(SaasClassTypeEnum.XINGZHENG.getCode());
        query.setClassTypes(classTypes);
        List<EduClassInfoVO> eduClassInfoVOList = basicInfoRemote.queryClassInfoList(query);

        if (CollUtil.isEmpty(eduClassInfoVOList)) {
            return Collections.emptyList();
        }
        // 班级名称过滤
        List<EduClassInfoVO> filterClassList = this.filter(eduClassInfoVOList, param.getClassName());
        if (CollUtil.isEmpty(filterClassList)) {
            return Collections.emptyList();
        }
        // 排序 --gradeCode升序，classNum升序
        List<EduClassInfoVO> sortClassList = filterClassList.stream()
                .sorted(Comparator.comparing(EduClassInfoVO::getGradeCode).thenComparing(EduClassInfoVO::getClassNum))
                .collect(Collectors.toList());

        return sortClassList.stream().map(item -> new ClassGradeListVO().setClassId(Convert.toStr(item.getId()))
                .setClassName(item.getClassName()).setClassNum(item.getClassNum())
                .setClassLevelId(item.getClassLevelId()).setGradeId(Convert.toStr(item.getGradeId()))
                .setGradeName(item.getGradeName()).setGradeCode(item.getGradeCode())).collect(Collectors.toList());
    }

    /**
     * 查校学年列表
     *
     * @param param
     * @return
     */
    @Override
    public List<TermVo> listTerms(TermQuery param) {
        List<TermVo> termVos = basicInfoRemote.queryTermList(param);
        if (CollectionUtil.isEmpty(termVos)) {
            return null;
        }
        List<TermVo> currentTerms = termVos.stream().filter(TermVo::isCurrentTerm).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(currentTerms)) {
            return termVos.stream().filter(single -> DateUtil.parse(single.getStartTime())
                    .before(DateUtil.parse(currentTerms.get(0).getEndTime()))).collect(Collectors.toList());
        }
        return termVos.stream().filter(single -> DateUtil.parse(single.getStartTime()).before(DateUtil.date()))
                .collect(Collectors.toList());
    }

    /**
     * 查当前学年信息
     *
     * @param schoolId
     * @param campusId
     * @return
     */
    @Override
    public TermVo queryCurrentTerm(Long schoolId, Long campusId) {
        TermQuery query = new TermQuery();
        query.setSchoolId(schoolId);
        query.setCampusId(campusId);
        List<TermVo> list = this.listTerms(query);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 班级名称过滤
     *
     * @param eduClassInfoVOList
     * @param className
     * @return
     */
    private List<EduClassInfoVO> filter(List<EduClassInfoVO> eduClassInfoVOList, String className) {
        if (StrUtil.isBlank(className)) {
            return eduClassInfoVOList;
        }
        String transformClassName = ParamUtil.transform(className);

        return eduClassInfoVOList.stream().filter(item -> item.getClassName().contains(transformClassName))
                .collect(Collectors.toList());
    }

    /**
     * 过滤数据权限
     */
    private List<EduOrgTreeVO> filterList(Set<Long> filterSet, List<EduOrgTreeVO> eduOrgTreeVOList,
                                          Map<Long, Long> parentMap) {
        if (CollUtil.isEmpty(filterSet) || CollUtil.isEmpty(eduOrgTreeVOList)) {
            return new ArrayList<>();
        }
        List<EduOrgTreeVO> resultList = new ArrayList<>();
        for (EduOrgTreeVO eduOrgTreeVO : eduOrgTreeVOList) {
            if (filterSet.contains(eduOrgTreeVO.getId())) {
                resultList.add(eduOrgTreeVO);
                parentMap.put(eduOrgTreeVO.getParentId(), eduOrgTreeVO.getId());
            } else {
                resultList.addAll(this.filterList(filterSet, eduOrgTreeVO.getChildren(), parentMap));
                if (parentMap.containsKey(eduOrgTreeVO.getId())) {
                    // 上一级
                    resultList.add(eduOrgTreeVO);
                    parentMap.put(eduOrgTreeVO.getParentId(), eduOrgTreeVO.getId());
                }
            }
        }
        return resultList;
    }

    /**
     * 平铺转换为树形结构
     */
    private List<EduOrgTreeVO> toTree(Collection<EduOrgTreeVO> resultList, EduOrgQueryDTO eduOrgQueryDTO) {
        HashMap<Long, List<EduOrgTreeVO>> parentIdVOMap = new HashMap<>();
        for (EduOrgTreeVO orgTreeVO : resultList) {
            if (Objects.nonNull(parentIdVOMap.get(orgTreeVO.getParentId()))) {
                List<EduOrgTreeVO> eduOrgTreeVOList = parentIdVOMap.get(orgTreeVO.getParentId());
                eduOrgTreeVOList.add(orgTreeVO);
                parentIdVOMap.put(orgTreeVO.getParentId(), eduOrgTreeVOList);
            } else {
                List<EduOrgTreeVO> eduOrgTreeVOList = new ArrayList<>();
                eduOrgTreeVOList.add(orgTreeVO);
                parentIdVOMap.put(orgTreeVO.getParentId(), eduOrgTreeVOList);
            }
        }
        setChildren(resultList, parentIdVOMap);
        return resultList.stream().filter(result -> result.getType().equals(eduOrgQueryDTO.getCurrentIdType()))
                .collect(Collectors.toList());
    }

    /**
     * 递归设置子树
     */
    private void setChildren(Collection<EduOrgTreeVO> resultList, HashMap<Long, List<EduOrgTreeVO>> parentIdVOMap) {
        for (EduOrgTreeVO eduOrgTreeVO : resultList) {
            if (Objects.nonNull(parentIdVOMap.get(eduOrgTreeVO.getId()))) {
                eduOrgTreeVO.setChildren(parentIdVOMap.get(eduOrgTreeVO.getId()));
                setChildren(parentIdVOMap.get(eduOrgTreeVO.getId()), parentIdVOMap);
            }
        }
    }

    /**
     * 获取教职工的任职情况
     */
    @Override
    public List<EduStaffClassVO> queryStaffTeachInfo(EduStaffClassQueryDTO eduStaffClassQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====获取教职工的任职情况");
        return basicInfoRemote.queryStaffTeachInfo(eduStaffClassQueryDTO);
    }

    /**
     * 获取学生家长集合
     *
     * @param eduParentQueryDTO
     * @return
     */
    public List<EduParentInfoVO> queryParentInfoList(EduParentQueryDTO eduParentQueryDTO) {
        return basicInfoRemote.queryParentInfoList(eduParentQueryDTO);
    }

    /**
     * 根据学生id查学生家长，按母亲-父亲-监护人-其他取值
     *
     * @param studentId
     * @return
     */
    @Override
    public EduParentInfoPojo getParentInfo(Long studentId) {
        AssertUtil.checkNotNull(studentId, "学生id不能为空");

        List<EduParentInfoVO> parentList = this.queryParentInfoList(
                new EduParentQueryDTO().setStudentIds(Collections.singletonList(studentId)));
        if (CollectionUtil.isEmpty(parentList)) {
            return null;
        }
        List<EduParentInfoPojo> parentInfos = parentList.get(0).getParentInfos();
        if (CollectionUtil.isEmpty(parentInfos)) {
            return null;
        }
        // 按顺序取值
        List<Integer> parentCodeList = SaaSStudentRelationEnum.querySortCodeList();
        for (Integer parentCode : parentCodeList) {
            Optional<EduParentInfoPojo> parent = parentInfos.stream()
                    .filter(item -> Integer.toString(parentCode).equals(item.getRelationCode())).findFirst();
            if (parent.isPresent()) {
                return parent.get();
            }
        }

        return parentInfos.get(0);
    }

    /**
     * 根据学生id查学生家长列表
     *
     * @param studentId
     * @return
     */
    @Override
    public List<EduParentInfoPojo> getParentInfoList(Long studentId) {
        AssertUtil.checkNotNull(studentId, "学生id不能为空");

        List<EduParentInfoVO> parentList = this.queryParentInfoList(
                new EduParentQueryDTO().setStudentIds(Collections.singletonList(studentId)));
        if (CollectionUtil.isEmpty(parentList)) {
            return null;
        }
        List<EduParentInfoPojo> parentInfos = parentList.get(0).getParentInfos();
        if (CollectionUtil.isEmpty(parentInfos)) {
            return null;
        }
        return parentInfos;
    }

    /**
     * 接口13：获取学生的班级列表
     */
    public List<EduStudentClassVO> queryClassListByStudentIds(EduStudentClassQueryDTO eduStudentClassQueryDTO) {
        if (Objects.isNull(eduStudentClassQueryDTO.getStudentIds()) || eduStudentClassQueryDTO.getStudentIds()
                .isEmpty()) {
            log.info("学生id为空，返回空班级数组");
            return new ArrayList<>();
        }
        return basicInfoRemote.queryClassListByStudentIds(eduStudentClassQueryDTO);
    }

    /**
     * 根据学生id查学生所属行政班信息（未毕业） 包含班级的班主任等信息
     */
    public EduStudentClassVO queryStudentClassInfo(Long studentId) {
        AssertUtil.checkNotNull(studentId, "学生id不能为空");

        EduStudentClassQueryDTO query = new EduStudentClassQueryDTO().setStudentIds(
                        Collections.singletonList(studentId))
                .setClassTypes(Collections.singletonList(SaasClassTypeEnum.XINGZHENG.getCode()))
                .setGraduationStatus(Constant.NO.toString());

        List<EduStudentClassVO> resultList = this.queryClassListByStudentIds(query);
        if (CollUtil.isEmpty(resultList)) {
            return null;
        }

        return resultList.get(0);
    }

    /**
     * 根据学生id查学生所属行政班信息（未毕业） 包含学校id、校区id
     */
    @Override
    public StudentClassInfoVO queryStudentSchoolClassInfo(String studentId) {
        AssertUtil.checkNotBlank(studentId, "学生id不能为空");
        EduStudentClassVO saasResult = this.queryStudentClassInfo(Convert.toLong(studentId));
        if (ObjectUtil.isNull(saasResult)) {
            return null;
        }
        List<EduClassBaseInfoPojo> classInfoList = saasResult.getClassBaseInfos();
        AssertUtil.checkNotEmpty(classInfoList, "当前学生在saas没有所属行政班，请检查");
        EduClassBaseInfoPojo classInfo = classInfoList.get(0);

        return new StudentClassInfoVO().setStudentId(saasResult.getStudentId()).setStudentNo(saasResult.getStudentNo())
                .setStudentName(saasResult.getStudentName()).setClassId(classInfo.getId())
                .setClassName(classInfo.getClassName()).setSchoolId(classInfo.getSchoolId())
                .setCampusId(classInfo.getCampusId()).setTenantId(classInfo.getTenantId());
    }

    /**
     * 获取学生家长班级、班主任等信息
     *
     * @param studentId
     * @return
     */
    @Override
    public EduStudentClassTeacherParentVO getStudentInfo(String studentId) {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.start("【获取学生信息】");

        log.info("【BasicInfoService层saas服务调用】====获取学生家长班级、班主任等信息");
        Assert.notNull(studentId, () -> new BizException("学生Id不存在"));

        // 查班级
        StudentClassQueryDTO classQueryDTODTO = new StudentClassQueryDTO();
        List<Long> studentIdList = Lists.newArrayList(Convert.toLong(studentId));

        classQueryDTODTO.setStudentIds(studentIdList);
        List<StudentClassVO> studentClassVOS = saasStudentCacheManager.queryClassListByStudentIds(classQueryDTODTO);

        Assert.notEmpty(studentClassVOS, () -> new BizException("该学生班级不存在，请关闭后重新选择"));

        // 学生saas班级信息
        StudentClassVO studentClassVO = studentClassVOS.get(0);
        Assert.notNull(studentClassVO, () -> new BizException("saas系统该学生无所属班级"));
        ClassBaseInfoPojo classInfo = studentClassVO.getClassBaseInfos().get(0);

        // 获取学生姓名和性别
        StudentPageQueryDTO studentPageQueryDTO = new StudentPageQueryDTO().setSchoolId(WebUtil.getSchoolIdLong());
        studentPageQueryDTO.setCampusId(WebUtil.getCampusIdLong());
        studentPageQueryDTO.setStudentIds(Collections.singletonList(Convert.toLong(studentId)))
                .setPageSize(Constant.ONE);
        List<StudentInfo1VO> studentInfoList = saasStudentManager.queryStudentPage(studentPageQueryDTO);
        Assert.notEmpty(studentInfoList, () -> new BizException("该学生不存在，请关闭后重新选择"));

        StudentInfo1VO studentInfoVO = studentInfoList.get(0);
        Assert.notNull(studentInfoVO, () -> new BizException("该学生不存在，请关闭后重新选择"));
        EduStudentClassTeacherParentVO studentInfo = new EduStudentClassTeacherParentVO();
        studentInfo.setStudentId(Convert.toStr(studentId)).setStudentName(studentInfoVO.getStudentName())
                .setSex(studentInfoVO.getSex()).setClassName(classInfo.getClassName())
                .setHeadMasterName(classInfo.getHeadMasterName());
        // 学生家长信息
        ParentQueryDTO parentQueryDTO = new ParentQueryDTO();
        parentQueryDTO.setStudentIds(studentIdList);
        List<ParentInfoVO> parentInfoList = saasStudentCacheManager.queryParentInfoList(parentQueryDTO);
        if (CollUtil.isEmpty(parentInfoList)) {
            return studentInfo;
        }

        ParentInfoVO parentInfo = parentInfoList.get(0);
        if (CollUtil.isEmpty(parentInfo.getParentInfos())) {
            return studentInfo;
        }
        studentInfo.setGuardianName(getParentName(parentInfo.getParentInfos()));

        log.info("【获取学生家长班级、班主任等信息】<<<<<<<<<<<<<<<<<<<,消耗时长:【{}】",
                timeInterval.interval("【获取学生信息】"));

        return studentInfo;
    }

    /**
     * 选择规则 母亲--父亲--监护人--随机
     *
     * @param parentList
     * @return
     */
    private String getParentName(List<ParentInfoPojo> parentList) {
        Map<Integer, String> nameMap = new HashMap<>();
        parentList.stream().forEach(parent -> {
            nameMap.put(Convert.toInt(parent.getRelationCode()), parent.getName());
        });

        String mom = nameMap.get(SaaSStudentRelationEnum.MOTHER.getCode());
        if (StrUtil.isNotEmpty(mom)) {
            return mom;
        }
        String dad = nameMap.get(SaaSStudentRelationEnum.FATHER.getCode());
        if (StrUtil.isNotEmpty(dad)) {
            return dad;
        }
        String guardian = nameMap.get(SaaSStudentRelationEnum.GUARDIAN.getCode());
        if (StrUtil.isNotEmpty(guardian)) {
            return guardian;
        }

        return parentList.get(0).getName();
    }

    /**
     * 获取所有可查看的班级(行政班)
     */
    private List<Long> listAllAuthClass() {
        //登录人能查看的年级列表
        List<Long> gradeIdList = new ArrayList<>();
        //员工授课班级集合
        List<Long> classIdList = new ArrayList<>();
        //员工担任班主任的班级集合
        List<Long> masterClassIdList = new ArrayList<>();
        List<EduStaffClassVO> eduStaffClassVOList = listEduStaffClass();
        if (Objects.nonNull(eduStaffClassVOList) && !eduStaffClassVOList.isEmpty()) {
            EduStaffClassVO eduStaffClassVO = eduStaffClassVOList.get(0);
            gradeIdList = eduStaffClassVO.getLeaderGradeInfos().stream().map(EduStaffGradeVO::getGradeId)
                    .collect(Collectors.toList());
            classIdList = eduStaffClassVO.getTeachClassInfos().stream().map(EduStaffTeachClassVO::getClassId)
                    .collect(Collectors.toList());
            masterClassIdList = eduStaffClassVO.getLeaderClassInfos().stream().map(EduStaffMasterClassVO::getClassId)
                    .collect(Collectors.toList());
        }
        log.info("登录人能查看的年级列表，{}", JSONUtil.toJsonStr(gradeIdList));
        log.info("员工授课班级集合，{}", JSONUtil.toJsonStr(classIdList));
        log.info("员工担任班主任的班级集合，{}", JSONUtil.toJsonStr(masterClassIdList));
        classIdList.addAll(masterClassIdList);
        classIdList.addAll(listClassIdByGradeIdList(gradeIdList, classIdList));
        classIdList = classIdList.stream().distinct().collect(Collectors.toList());
        return classIdList;
    }

    /**
     * 获取所有可查看的班级（包含走班）
     */
    private List<Long> listAllAuthAllClass() {
        //登录人能查看的年级列表
        List<Long> gradeIdList = new ArrayList<>();
        //员工授课班级集合
        List<Long> classIdList = new ArrayList<>();
        //员工担任班主任的班级集合
        List<Long> masterClassIdList = new ArrayList<>();
        List<EduStaffClassVO> eduStaffClassVOList = listEduStaffAllClass();
        if (Objects.nonNull(eduStaffClassVOList) && !eduStaffClassVOList.isEmpty()) {
            EduStaffClassVO eduStaffClassVO = eduStaffClassVOList.get(0);
            gradeIdList = eduStaffClassVO.getLeaderGradeInfos().stream().map(EduStaffGradeVO::getGradeId)
                    .collect(Collectors.toList());
            classIdList = eduStaffClassVO.getTeachClassInfos().stream().map(EduStaffTeachClassVO::getClassId)
                    .collect(Collectors.toList());
            masterClassIdList = eduStaffClassVO.getLeaderClassInfos().stream().map(EduStaffMasterClassVO::getClassId)
                    .collect(Collectors.toList());
        }
        log.info("登录人能查看的年级列表，{}", JSONUtil.toJsonStr(gradeIdList));
        log.info("员工授课班级集合，{}", JSONUtil.toJsonStr(classIdList));
        log.info("员工担任班主任的班级集合，{}", JSONUtil.toJsonStr(masterClassIdList));
        classIdList.addAll(masterClassIdList);
        classIdList.addAll(listClassIdByGradeIdList(gradeIdList, classIdList));
        classIdList = classIdList.stream().distinct().collect(Collectors.toList());
        return classIdList;
    }

    /**
     * 接口11：获取学生列表
     * <p>
     * 此方法加了数据权限功能 若不需要加数据权限，请直接调用remote下的queryStudentPage 查询学生列表（不包含已经毕业的学生）
     */
    @Override
    public List<EduStudentInfoVO> queryStudentPage(EduStudentPageQueryDTO eduStudentPageQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====接口11：获取学生列表");
        List<EduStudentInfoVO> eduStudentInfoVOList = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
        if (eduStudentInfoVOList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> studentIdList = eduStudentInfoVOList.stream().map(EduStudentInfoVO::getId)
                .collect(Collectors.toList());
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        List<String> classTypes = new ArrayList<>();
        classTypes.add(SaasClassTypeEnum.XINGZHENG.getCode());
        eduStudentClassQueryDTO.setClassTypes(classTypes);
        eduStudentClassQueryDTO.setStudentIds(studentIdList);
        eduStudentClassQueryDTO.setGraduationStatus("0");
        List<EduStudentClassVO> eduStudentClassVOList = basicInfoRemote.queryClassListByStudentIds(
                eduStudentClassQueryDTO);
        Map<Long, List<EduClassBaseInfoPojo>> idClassMap = eduStudentClassVOList.stream()
                .filter(s->Objects.nonNull(s.getStudentId()))
                .filter(s->CollUtil.isNotEmpty(s.getClassBaseInfos()))
                .collect(Collectors.toMap(EduStudentClassVO::getStudentId, EduStudentClassVO::getClassBaseInfos));
        for (EduStudentInfoVO eduStudentInfoVO : eduStudentInfoVOList) {
            List<EduClassBaseInfoPojo> eduClassBaseInfoPojoList = idClassMap.get(eduStudentInfoVO.getId());
            if (Objects.nonNull(eduClassBaseInfoPojoList) && !eduClassBaseInfoPojoList.isEmpty()) {
                eduStudentInfoVO.setClassId(eduClassBaseInfoPojoList.get(0).getId());
                eduStudentInfoVO.setClassName(eduClassBaseInfoPojoList.get(0).getClassName());
                // 补充年级信息
            }
            eduStudentInfoVO.setClassBaseInfos(idClassMap.get(eduStudentInfoVO.getId()));
        }
        log.info("查询学生，过滤数据权限之前的数据条数：{}", eduStudentInfoVOList.size());
        //根据角色过滤数据
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = listOrgIdAndRoleId();
        List<String> roleCodeList;
        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())
                && !listOrgIdAndRoleIdVO.getOrgIdList().isEmpty()) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            log.info("当前登录人没有配置角色，返回空值");
            return Collections.emptyList();
        }
        log.info("当前登录人具有的角色，数据量：{}", eduStudentInfoVOList.size());
        //saas默认角色code未配置，暂时先采用名称匹配，若saas后续配置，更换为code
        if (roleLogic.isHaveMaterRole(roleCodeList)) {
            //校长、德育处主任、教务主任 返回全部数据
            log.info("查询学生接口，无需过滤数据权限，直接返回saas结果，返回结果条数：{}", eduStudentInfoVOList.size());
            return eduStudentInfoVOList;
        }

        if (roleLogic.isHaveOrdinaryRole(roleCodeList)) {
            //年级主任、班主任、学科老师需要过滤数据权限
            List<Long> listAllAuthClass = listAllAuthClass();
            eduStudentInfoVOList = eduStudentInfoVOList.stream()
                    .filter(eduStudentInfoVO -> listAllAuthClass.contains(eduStudentInfoVO.getClassId()))
                    .collect(Collectors.toList());
            log.info("查询学生，过滤数据权限之后的数据条数：{}", eduStudentInfoVOList.size());
            return eduStudentInfoVOList;
        }
        log.info("当前登录人不在指定角色中，返回空");
        return Collections.emptyList();
    }

    @Override
    public List<EduStudentInfoVO> queryStudentPageV2(EduStudentPageQueryDTO eduStudentPageQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====接口11：获取学生列表");

        List<EduStudentInfoVO> responseList = new ArrayList<>();

        eduStudentPageQueryDTO.setClassTypes(Collections.singletonList(SaasClassTypeEnum.XINGZHENG.getCode()));
        eduStudentPageQueryDTO.setWithClass(true);
        eduStudentPageQueryDTO.setGraduationStatus("0");
        List<SaaSEduStudentInfoVO> eduStudentInfoVOList = basicInfoRemote.queryStudentPageV2(eduStudentPageQueryDTO);
        if (eduStudentInfoVOList.isEmpty()) {
            return new ArrayList<>();
        }

        for (SaaSEduStudentInfoVO saaSEduStudentInfoVO : eduStudentInfoVOList) {

            EduStudentInfoVO infoVO = new EduStudentInfoVO();
            infoVO.setId(saaSEduStudentInfoVO.getId());
            infoVO.setStudentNo(saaSEduStudentInfoVO.getStudentNo());
            infoVO.setStudentName(saaSEduStudentInfoVO.getStudentName());
            infoVO.setSex(saaSEduStudentInfoVO.getSex());
            infoVO.setClassId(saaSEduStudentInfoVO.getClassList().get(0).getClassId());
            infoVO.setClassName(saaSEduStudentInfoVO.getClassList().get(0).getClassName());
            infoVO.setGradeId(saaSEduStudentInfoVO.getClassList().get(0).getGradeId());
            infoVO.setGradeName(saaSEduStudentInfoVO.getClassList().get(0).getGradeName());
            infoVO.setSchoolId(eduStudentPageQueryDTO.getSchoolId());
            responseList.add(infoVO);
        }

        log.info("查询学生，过滤数据权限之前的数据条数：{}", eduStudentInfoVOList.size());
        //根据角色过滤数据
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = listOrgIdAndRoleId();
        List<String> roleCodeList;
        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())
                && !listOrgIdAndRoleIdVO.getOrgIdList().isEmpty()) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            log.info("当前登录人没有配置角色，返回空值");
            return Collections.emptyList();
        }
        log.info("当前登录人权限信息：{}", JSONUtil.toJsonStr(listOrgIdAndRoleIdVO));
        if (roleLogic.isHaveMaterRole(roleCodeList)) {
            //校长、德育处主任、教务主任 返回全部数据
            log.info("查询学生接口，无需过滤数据权限，直接返回saas结果，返回结果条数：{}", eduStudentInfoVOList.size());
            return responseList;
        }

        if (roleLogic.isHaveOrdinaryRole(roleCodeList)) {
            //年级主任、班主任、学科老师需要过滤数据权限
            List<Long> listAllAuthClass = listAllAuthClass();
            responseList = responseList.stream()
                    .filter(eduStudentInfoVO -> listAllAuthClass.contains(eduStudentInfoVO.getClassId()))
                    .collect(Collectors.toList());
            log.info("查询学生，过滤数据权限之后的数据条数：{}", eduStudentInfoVOList.size());
            return responseList;
        }
        log.info("当前登录人不在指定角色中，返回空");
        return Collections.emptyList();
    }

    /**
     * 获取学生列表 包含走班
     * <p>
     * 此方法加了数据权限功能 若不需要加数据权限，请直接调用remote下的queryStudentPage 查询学生列表（不包含已经毕业的学生）
     */

    @Override
    public List<EduStudentInfoVO> queryStudentPageAll(EduStudentPageQueryDTO eduStudentPageQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====接口11：获取学生列表");
        List<EduStudentInfoVO> eduStudentInfoVOList = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
        if (eduStudentInfoVOList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> studentIdList = eduStudentInfoVOList.stream().map(EduStudentInfoVO::getId)
                .collect(Collectors.toList());
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        eduStudentClassQueryDTO.setStudentIds(studentIdList);
        eduStudentClassQueryDTO.setGraduationStatus("0");
        List<EduStudentClassVO> eduStudentClassVOList = basicInfoRemote.queryClassListByStudentIds(
                eduStudentClassQueryDTO);
        Map<Long, List<EduClassBaseInfoPojo>> idClassMap = eduStudentClassVOList.stream()
                .collect(Collectors.toMap(EduStudentClassVO::getStudentId, EduStudentClassVO::getClassBaseInfos));
        for (EduStudentInfoVO eduStudentInfoVO : eduStudentInfoVOList) {
            List<EduClassBaseInfoPojo> eduClassBaseInfoPojoList = idClassMap.get(eduStudentInfoVO.getId());
            if (Objects.nonNull(eduClassBaseInfoPojoList) && !eduClassBaseInfoPojoList.isEmpty()) {
                eduStudentInfoVO.setClassId(eduClassBaseInfoPojoList.get(0).getId());
                eduStudentInfoVO.setClassName(eduClassBaseInfoPojoList.get(0).getClassName());
                // 补充年级信息
            }
            eduStudentInfoVO.setClassBaseInfos(idClassMap.get(eduStudentInfoVO.getId()));
        }
        List<EduStudentInfoVO> eduStudentInfoVOs = new ArrayList();
        Iterator<EduStudentInfoVO> iterator = eduStudentInfoVOList.iterator();
        while (iterator.hasNext()) {
            EduStudentInfoVO eduStudentInfoVO = iterator.next();
            List<EduClassBaseInfoPojo> eduClassBaseInfoPojoList = idClassMap.get(eduStudentInfoVO.getId());
            if (Objects.nonNull(eduClassBaseInfoPojoList) && !eduClassBaseInfoPojoList.isEmpty() && Constant.ONE.equals(
                    eduClassBaseInfoPojoList.size())) {
                eduStudentInfoVO.setClassId(eduClassBaseInfoPojoList.get(0).getId());
                eduStudentInfoVO.setClassName(eduClassBaseInfoPojoList.get(0).getClassName());
            } else if (Objects.nonNull(eduClassBaseInfoPojoList) && !eduClassBaseInfoPojoList.isEmpty()
                    && Constant.ONE < (eduClassBaseInfoPojoList.size())) {
                for (int i = 0; i < eduClassBaseInfoPojoList.size(); i++) {
                    if (0 != i) {
                        EduStudentInfoVO eduStudentInfoVO1 = BeanUtil.copyProperties(eduStudentInfoVO,
                                EduStudentInfoVO.class);
                        eduStudentInfoVO1.setClassId(eduClassBaseInfoPojoList.get(i).getId());
                        eduStudentInfoVO1.setClassName(eduClassBaseInfoPojoList.get(i).getClassName());
                        eduStudentInfoVO1.setClassBaseInfos(idClassMap.get(eduStudentInfoVO.getId()));
                        eduStudentInfoVOs.add(eduStudentInfoVO1);
                        continue;
                    }
                    eduStudentInfoVO.setClassId(eduClassBaseInfoPojoList.get(i).getId());
                    eduStudentInfoVO.setClassName(eduClassBaseInfoPojoList.get(i).getClassName());
                }
            }
            eduStudentInfoVO.setClassBaseInfos(idClassMap.get(eduStudentInfoVO.getId()));
        }
        eduStudentInfoVOList.addAll(eduStudentInfoVOs);

        log.info("查询学生，过滤数据权限之前的数据{}", JSONUtil.toJsonStr(eduStudentInfoVOList));
        //根据角色过滤数据
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = listOrgIdAndRoleId();
        List<String> roleCodeList;
        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())
                && !listOrgIdAndRoleIdVO.getOrgIdList().isEmpty()) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            log.info("当前登录人没有配置角色，返回空值");
            return Collections.emptyList();
        }
        log.info("当前登录人具有的角色{}", JSONUtil.toJsonStr(roleCodeList));
        //saas默认角色code未配置，暂时先采用名称匹配，若saas后续配置，更换为code
        if (roleLogic.isHaveMaterRole(roleCodeList)) {
            //校长、德育处主任、教务主任 返回全部数据
            log.info("查询学生接口，无需过滤数据权限，直接返回saas结果，返回结果{}",
                    JSONUtil.toJsonStr(eduStudentInfoVOList));
            return eduStudentInfoVOList;
        }

        if (roleLogic.isHaveOrdinaryRole(roleCodeList)) {
            //年级主任、班主任、学科老师需要过滤数据权限
            List<Long> listAllAuthClass = listAllAuthAllClass();
            eduStudentInfoVOList = eduStudentInfoVOList.stream()
                    .filter(eduStudentInfoVO -> listAllAuthClass.contains(eduStudentInfoVO.getClassId()))
                    .collect(Collectors.toList());
            log.info("查询学生，过滤数据权限之后的数据{}", JSONUtil.toJsonStr(eduStudentInfoVOList));
            return eduStudentInfoVOList;
        }
        log.info("当前登录人不在指定角色中，返回空");
        return Collections.emptyList();
    }

    /**
     * 查询学生列表（不包含已经毕业的学生，返回行政班和走班信息）
     */
    @Override
    public List<EduStudentInfoVO> queryStudentPageWithoutCondition(EduStudentPageQueryDTO eduStudentPageQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====接口11：获取学生列表");
        List<EduStudentInfoVO> eduStudentInfoVOList = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
        if (eduStudentInfoVOList.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> studentIdList = eduStudentInfoVOList.stream().map(EduStudentInfoVO::getId)
                .collect(Collectors.toList());
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        eduStudentClassQueryDTO.setStudentIds(studentIdList);
        eduStudentClassQueryDTO.setGraduationStatus("0");
        List<EduStudentClassVO> eduStudentClassVOList = basicInfoRemote.queryClassListByStudentIds(
                eduStudentClassQueryDTO);
        Map<Long, List<EduClassBaseInfoPojo>> idClassMap = eduStudentClassVOList.stream()
                .collect(Collectors.toMap(EduStudentClassVO::getStudentId, EduStudentClassVO::getClassBaseInfos));
        for (EduStudentInfoVO eduStudentInfoVO : eduStudentInfoVOList) {
            List<EduClassBaseInfoPojo> eduClassBaseInfoPojoList = idClassMap.get(eduStudentInfoVO.getId());
            if (Objects.nonNull(eduClassBaseInfoPojoList) && !eduClassBaseInfoPojoList.isEmpty()) {
                eduStudentInfoVO.setClassId(eduClassBaseInfoPojoList.get(0).getId());
                eduStudentInfoVO.setClassName(eduClassBaseInfoPojoList.get(0).getClassName());
                // 补充年级信息
            }
            eduStudentInfoVO.setClassBaseInfos(idClassMap.get(eduStudentInfoVO.getId()));
        }
        log.info("查询学生，过滤数据权限之前的数据{}", JSONUtil.toJsonStr(eduStudentInfoVOList));
        //根据角色过滤数据
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = listOrgIdAndRoleId();
        List<String> roleCodeList;
        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())
                && !listOrgIdAndRoleIdVO.getOrgIdList().isEmpty()) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            log.info("当前登录人没有配置角色，返回空值");
            return Collections.emptyList();
        }
        log.info("当前登录人具有的角色{}", JSONUtil.toJsonStr(roleCodeList));
        //saas默认角色code未配置，暂时先采用名称匹配，若saas后续配置，更换为code
        if (roleLogic.isHaveMaterRole(roleCodeList)) {
            //校长、德育处主任、教务主任 返回全部数据
            log.info("查询学生接口，无需过滤数据权限，直接返回saas结果，返回结果{}",
                    JSONUtil.toJsonStr(eduStudentInfoVOList));
            return eduStudentInfoVOList;
        }

        if (roleLogic.isHaveOrdinaryRole(roleCodeList)) {
            //年级主任、班主任、学科老师需要过滤数据权限
            List<Long> listAllAuthClass = listAllAuthAllClass();
            eduStudentInfoVOList = eduStudentInfoVOList.stream()
                    .filter(eduStudentInfoVO -> listAllAuthClass.contains(eduStudentInfoVO.getClassId()))
                    .collect(Collectors.toList());
            log.info("查询学生，过滤数据权限之后的数据{}", JSONUtil.toJsonStr(eduStudentInfoVOList));
            return eduStudentInfoVOList;
        }
        log.info("当前登录人不在指定角色中，返回空");
        return Collections.emptyList();
    }

    /**
     * 接口5：获取角色下的教职工列表
     */
    @Override
    public List<StaffVO> queryStaffListByRole(StaffRoleQueryDTO staffRoleQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====接口5：获取角色下的教职工列表");
        List<StaffVO> staffVOList = basicInfoRemote.queryStaffListByRole(staffRoleQueryDTO);
        return solveStaffVO(staffVOList);
    }

    /**
     * 分页查询学校下的教职工列表
     */
    @Override
    public List<StaffVO> queryStaffListBySchool(SchoolStaffQueryDTO schoolStaffQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====分页查询学校下的教职工列表");
        List<StaffVO> staffVOList = basicInfoRemote.queryStaffListBySchool(schoolStaffQueryDTO);
        return solveStaffVO(staffVOList);
    }


    /**
     * 通过StaffIds查询教职工详情
     */
    @Override
    public List<StaffBatchVO> queryBasicInfoByStaffIds(StaffInfoQueryDTO staffInfoQueryDTO) {
        if (Objects.isNull(staffInfoQueryDTO) || CollUtil.isEmpty(staffInfoQueryDTO.getStaffIds())) {
            return new ArrayList<>();
        }
        log.info("【BasicInfoService层saas服务调用】====通过StaffIds查询教职工详情");
        //saas接口未提供 mock数据
        StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
        staffBatchQueryDTO.setStaffIdList(staffInfoQueryDTO.getStaffIds());
        staffBatchQueryDTO.setState(SaasStaffStateQueryEnum.NORMAL.getCode());
        return basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
    }


    /**
     * 获取学校下的校区列表
     */
    @Override
    public List<TchCampusInfoVO> queryCampusListBySchoolId(SchoolQueryDTO schoolQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====获取学校下的校区列表");
        List<TchCampusInfoVO> tchCampusInfoVOList = basicInfoRemote.queryCampusListBySchoolId(schoolQueryDTO);

        return tchCampusInfoVOList;
    }

    /**
     * 获取学校下的校区基本信息列表
     */
    @Override
    public List<TchCampusBaseInfoVO> queryCampusBaseInfoList(String schoolId) {
        AssertUtil.checkNotNull(schoolId, "学校id不能为空");
        SchoolQueryDTO schoolQueryDTO = new SchoolQueryDTO();
        schoolQueryDTO.setSchoolId(Convert.toLong(schoolId));
        log.info("【BasicInfoService层saas服务调用】====获取学校下的校区列表");
        List<TchCampusInfoVO> tchCampusInfoVOList = basicInfoRemote.queryCampusListBySchoolId(schoolQueryDTO);

        return basicConvert.toTchCampusBaseInfoVOList(tchCampusInfoVOList);
    }

    /**
     * 查当前学校下key：校区id value：校区名称
     *
     * @param schoolId
     * @return
     */
    @Override
    public Map<String, String> queryCampusIdNameMap(String schoolId) {
        List<TchCampusBaseInfoVO> list = this.queryCampusBaseInfoList(schoolId);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(
                Collectors.toMap(item -> Convert.toStr(item.getCampusId()), TchCampusBaseInfoVO::getCampusName));
    }

    /**
     * 根据组织机构id取组织机构名称
     */
    @Override
    public String getOrgName(Long id, SaasCurrentIdTypeEnum saasCurrentIdTypeEnum) {
        log.info("【BasicInfoService层saas服务调用】====根据组织机构id取组织机构名称：id={},类型={}", id,
                saasCurrentIdTypeEnum.getDesc());

        Assert.notNull(id, () -> new BizException("组织机构id不能为空"));

        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setIsTree(Constant.NO);
        eduOrgQueryDTO.setCurrentId(id);
        eduOrgQueryDTO.setEndType(saasCurrentIdTypeEnum.getCode());
        eduOrgQueryDTO.setCurrentIdType(saasCurrentIdTypeEnum.getCode());
//        List<EduOrgTreeVO> eduOrgTreeVOList = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        List<EduOrgTreeVO> eduOrgTreeVOList = cacheSaasManager.queryEducationalOrgTree(eduOrgQueryDTO);
        EduOrgTreeVO eduOrgTreeVO = new EduOrgTreeVO();
        if (CollUtil.isNotEmpty(eduOrgTreeVOList)) {
            eduOrgTreeVO = CollUtil.getFirst(eduOrgTreeVOList);
        }
        return eduOrgTreeVO.getName();
    }


    /**
     * 通过教务组织机构集合获取班级列表
     */
    @Override
    public List<Long> listClassIdByEducationOrgId(List<EduOrgQueryDTO> eduOrgQueryDTOS) {
        log.info("【BasicInfoService层saas服务调用】====通过教务组织机构集合获取班级列表");
        List<Long> classIdList = new ArrayList<>();
        for (EduOrgQueryDTO eduOrgQueryDTO : eduOrgQueryDTOS) {
            eduOrgQueryDTO.setIsTree(0);
            List<Long> eduClassIdList = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO).stream()
                    .filter(eduOrgTreeVO -> SaasCurrentIdTypeEnum.CLASS.getCode().equals(eduOrgTreeVO.getType()))
                    .map(EduOrgTreeVO::getId).distinct().collect(Collectors.toList());
            classIdList.addAll(eduClassIdList);
        }
        return classIdList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 根据学生id获取层级关系(有关系的链路id集合)
     *
     * @param studentId
     * @return
     */
    @Override
    public List<Long> listRelationInfo(String studentId) {
        if (Objects.isNull(studentId)) {
            log.warn("【BasicInfoService层saas服务调用】====根据学生id获取层级关系(有关系的链路id集合)异常,学生id为空");
            return new ArrayList<>();
        }
        log.info("【BasicInfoService层saas服务调用】====根据学生id获取层级关系(有关系的链路id集合),学生id=【{}】",
                studentId);
        List<Long> linkInfos = new ArrayList<>();
        List<Long> classIds = new ArrayList<>();
        // 先根据学生获取班级列表
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        eduStudentClassQueryDTO.setStudentIds(Collections.singletonList(Convert.toLong(studentId)));

        List<EduStudentClassVO> eduStudentClassVOS = basicInfoRemote.queryClassListByStudentIds(
                eduStudentClassQueryDTO);
        if (CollUtil.isEmpty(eduStudentClassVOS)) {
            return linkInfos;
        }
        for (EduStudentClassVO d : eduStudentClassVOS) {
            List<EduClassBaseInfoPojo> classBaseInfos = d.getClassBaseInfos();
            if (CollUtil.isNotEmpty(classBaseInfos)) {
                classBaseInfos.forEach(s -> classIds.add(s.getId()));
            }
        }
        if (CollUtil.isEmpty(classIds)) {
            return linkInfos;
        }
        // 在根据班级获取组织层级信息
        log.info("【获取学生链路信息】，班级id：{}", JSONUtil.toJsonStr(classIds));
        List<EduClassInfoLinkVO> eduClassInfoLinkVOS = basicInfoRemote.listUnderByClassIds(CollUtil.distinct(classIds));
        for (EduClassInfoLinkVO d : eduClassInfoLinkVOS) {
            // 租户
            linkInfos.add(d.getTenantId());
            // 学校
            linkInfos.add(d.getSchoolId());
            // 校区
            linkInfos.add(d.getCampusId());
            // 学段关联
            linkInfos.add(d.getCampusSectionId());
            // 年级
            linkInfos.add(d.getGradeId());
            // 班级
            linkInfos.add(d.getId());
        }
        log.info("【获取学生链路信息】，过滤后结果：{}", JSONUtil.toJsonStr(linkInfos));
        return linkInfos;
    }

    /**
     * 分页查询学校下的教职工列表
     *
     * @param schoolStaffStudentDTO
     * @return
     */
    @Override
    public Page<StaffStudentVO> pageStaffAndStudentBySchool(SchoolStaffStudentDTO schoolStaffStudentDTO) {
        log.info("【BasicInfoService层saas服务调用】====分页查询学校下的教职工列表");
        List<StaffStudentVO> staffStudentVOS = new ArrayList<>();
        // 查询教职工的
        SchoolStaffQueryDTO schoolStaffQueryDTO = new SchoolStaffQueryDTO();
        schoolStaffQueryDTO.setPageNum(1);
        schoolStaffQueryDTO.setPageSize(1000);
        schoolStaffQueryDTO.setSchoolId(Convert.toLong(schoolStaffStudentDTO.getSchoolId()));
        schoolStaffQueryDTO.setStaffName(schoolStaffStudentDTO.getName());
        schoolStaffQueryDTO.setType(2);

        List<StaffVO> staffVOList = this.queryStaffListBySchool(schoolStaffQueryDTO);
        if (CollUtil.isNotEmpty(staffVOList)
                && (Constant.ONE.equals(schoolStaffStudentDTO.getRoleType()) || ObjectUtil.isNull(
                schoolStaffStudentDTO.getRoleType()))) {
            staffStudentVOS.addAll(staffVOList.stream().map(d -> {
                StaffStudentVO staffStudentVO = new StaffStudentVO();
                staffStudentVO.setId(Convert.toStr(d.getStaffId()));
                staffStudentVO.setName(d.getStaffName());
                staffStudentVO.setType(3);
                staffStudentVO.setSectionSubjectInfo(d.getSectionSubjectInfo());
                staffStudentVO.setAvatar(d.getAvatar());

                return staffStudentVO;
            }).collect(Collectors.toList()));
        }

        // 查询学生的
        EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
        eduStudentPageQueryDTO.setSchoolId(Convert.toLong(schoolStaffStudentDTO.getSchoolId()));
        eduStudentPageQueryDTO.setCampusId(WebUtil.getCampusIdLong());
        eduStudentPageQueryDTO.setNameLike(schoolStaffStudentDTO.getName());
        eduStudentPageQueryDTO.setPageNum(1);
        eduStudentPageQueryDTO.setPageSize(5000);

        List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
        if (CollUtil.isNotEmpty(eduStudentInfoVOS)
                && (Constant.TWO.equals(schoolStaffStudentDTO.getRoleType()) || ObjectUtil.isNull(
                schoolStaffStudentDTO.getRoleType()))) {
            staffStudentVOS.addAll(eduStudentInfoVOS.stream().map(d -> {
                StaffStudentVO staffStudentVO = new StaffStudentVO();
                staffStudentVO.setId(Convert.toStr(d.getId()));
                staffStudentVO.setName(d.getStudentName());
                staffStudentVO.setSectionSubjectInfo(Collections.EMPTY_LIST);
                staffStudentVO.setType(4);
                staffStudentVO.setAvatar(StrUtil.EMPTY);

                return staffStudentVO;
            }).collect(Collectors.toList()));
        }
        log.info("【聚合搜索老师和学生】，老师条数：【{}】， 学生条数：【{}】", staffVOList.size(), eduStudentInfoVOS.size());
        if (CollUtil.isEmpty(staffStudentVOS)) {
            return new Page<StaffStudentVO>();
        }
        int totalCount = staffStudentVOS.size();

        List<StaffStudentVO> collect = staffStudentVOS.stream()
                .skip((long) (schoolStaffStudentDTO.getPageNum() - 1) * schoolStaffStudentDTO.getPageSize())
                .limit(schoolStaffStudentDTO.getPageSize()).collect(Collectors.toList());

        Page<StaffStudentVO> page = new Page<>(schoolStaffStudentDTO.getPageNum(), schoolStaffStudentDTO.getPageSize());
        page.setRecords(collect);
        page.setTotal(totalCount);

        return page;
    }

    /**
     * 获取用户的所有学校以及学校下的校区 场景：切换校区 H5端：只传userId web端：传schoolId，staffId
     *
     * @param schoolStaffIdQueryDTO
     * @return
     */
    public List<UserSchoolCampusListVO> userSchoolCampusListOld(SchoolStaffIdQueryDTO schoolStaffIdQueryDTO) {
        log.info("【切换校区】-请求参数：【{}】", JSONUtil.toJsonStr(schoolStaffIdQueryDTO));
        List<UserSchoolCampusListVO> userSchoolCampusListVOS = new ArrayList<>();
        String staffId = schoolStaffIdQueryDTO.getStaffId();
//        staffId = StrUtil.isBlank(staffId) ? WebUtil.getStaffId() : schoolStaffIdQueryDTO.getStaffId();

        List<CampusInfoVO> campusInfoVOS = basicInfoRemote.listCampusByStaffId(schoolStaffIdQueryDTO.getUserId(),
                staffId);
        if (CollUtil.isNotEmpty(campusInfoVOS)) {
            log.info("【切换校区】-获取到当前用户关联的信息，userId:【{}】，staffId:【{}】, 关联的校区：【{}】",
                    schoolStaffIdQueryDTO.getUserId(), staffId, JSONUtil.toJsonStr(campusInfoVOS));
            Map<Long, List<CampusInfoVO>> longListMap = CollStreamUtil.groupByKey(campusInfoVOS,
                    CampusInfoVO::getSchoolId);
            longListMap.forEach((k, v) -> {
                UserSchoolCampusListVO userSchoolCampusListVO = new UserSchoolCampusListVO();
                userSchoolCampusListVO.setSchoolId(v.get(0).getSchoolId());
                userSchoolCampusListVO.setSchoolName(v.get(0).getSchoolName());
                userSchoolCampusListVO.setCampusInfoList(v.stream().map(d -> {
                    TchCampusInfoVO tchCampusInfoVO = new TchCampusInfoVO();
                    tchCampusInfoVO.setCampusId(d.getCampusId());
                    tchCampusInfoVO.setCampusName(d.getCampusName());
                    return tchCampusInfoVO;
                }).collect(Collectors.toList()));
                userSchoolCampusListVO.setStaffId(v.get(0).getStaffId());
                userSchoolCampusListVO.setTenantId(v.get(0).getTenantId());

                userSchoolCampusListVOS.add(userSchoolCampusListVO);
            });
        } else {
            String userId = schoolStaffIdQueryDTO.getUserId();
            if (StrUtil.isBlank(userId)) {
                // 针对web端 不传userId的情况
                if (StrUtil.isBlank(staffId)) {
                    log.info("【切换校区】-入参staffId为空，直接返回");
                    return Collections.emptyList();
                }
                List<StaffBatchVO> staffList = this.queryBasicInfoByStaffIds(
                        new StaffInfoQueryDTO().setStaffIds(Collections.singletonList(Convert.toLong(staffId))));
                if (CollUtil.isEmpty(staffList)) {
                    log.info("【切换校区】-staffList为空，直接返回");
                    return Collections.emptyList();
                }
                userId = Convert.toStr(staffList.get(0).getUserId());
            }
            UserSchoolQueryDTO userSchoolQueryDTO = new UserSchoolQueryDTO();
            userSchoolQueryDTO.setUserId(Convert.toLong(userId));
            List<UserSchoolVO> userSchoolVOS = basicInfoRemote.userSchoolList(userSchoolQueryDTO);
            if (CollUtil.isEmpty(userSchoolVOS)) {
                return Collections.emptyList();
            }
            userSchoolVOS.forEach(d -> {
                UserSchoolCampusListVO userSchoolCampusListVO = new UserSchoolCampusListVO();
                userSchoolCampusListVO.setSchoolId(d.getSchoolId());
                userSchoolCampusListVO.setSchoolName(d.getSchoolName());
                SchoolQueryDTO schoolQueryDTO = new SchoolQueryDTO();
                schoolQueryDTO.setSchoolId(d.getSchoolId());
                userSchoolCampusListVO.setCampusInfoList(this.queryCampusListBySchoolId(schoolQueryDTO));
                userSchoolCampusListVO.setStaffId(d.getStaffId());
                userSchoolCampusListVO.setTenantId(d.getTenantId());

                userSchoolCampusListVOS.add(userSchoolCampusListVO);
            });
        }
        String schoolId = schoolStaffIdQueryDTO.getSchoolId();
        if (StrUtil.isNotBlank(schoolId)) {
            List<UserSchoolCampusListVO> userSchoolCampusList = userSchoolCampusListVOS.stream()
                    .filter(d -> schoolId.equals(Convert.toStr(d.getSchoolId()))).collect(Collectors.toList());
            return userSchoolCampusList;
        }
        return userSchoolCampusListVOS;
    }

    /**
     * 获取用户的所有学校以及学校下的校区 场景：切换校区 web端：传schoolId，staffId
     *
     * @param schoolStaffIdQueryDTO
     * @return
     */
    @Override
    public List<UserSchoolCampusListVO> userSchoolCampusList(SchoolStaffIdQueryDTO schoolStaffIdQueryDTO) {
        log.info("【切换校区】-请求参数：【{}】", JSONUtil.toJsonStr(schoolStaffIdQueryDTO));
        List<UserSchoolCampusListVO> userSchoolCampusListVOS = new ArrayList<>();
        String staffId = schoolStaffIdQueryDTO.getStaffId();
        Long schoolId = Convert.toLong(schoolStaffIdQueryDTO.getSchoolId());

        String userId = schoolStaffIdQueryDTO.getUserId();
        if (StrUtil.isBlank(userId)) {
            // 针对web端 不传userId的情况
            if (StrUtil.isBlank(staffId)) {
                log.info("【切换校区】-入参staffId为空，直接返回");
                return Collections.emptyList();
            }
            List<StaffBatchVO> staffList = this.queryBasicInfoByStaffIds(
                    new StaffInfoQueryDTO().setStaffIds(Collections.singletonList(Convert.toLong(staffId))));
            if (CollUtil.isEmpty(staffList)) {
                log.info("【切换校区】-staffList为空，直接返回");
                return Collections.emptyList();
            }
            userId = Convert.toStr(staffList.get(0).getUserId());
        }

        SchoolQueryDTO schoolQueryDTO = new SchoolQueryDTO();
        schoolQueryDTO.setSchoolId(schoolId);
        // 角色
        boolean isMaterRole = checkMaster(Convert.toLong(staffId), schoolId);
        // 所有校区（根据学校查学校下校区）
        List<TchCampusInfoVO> tchCampusInfoVOS = this.queryCampusListBySchoolId(schoolQueryDTO);
        log.info("【切换校区】-当前学校下的所有校区tchCampusInfoVOS信息：{}", JSONUtil.toJsonStr(tchCampusInfoVOS));
        if (CollUtil.isEmpty(tchCampusInfoVOS)) {
            log.info("【切换校区】-tchCampusInfoVOS为空，直接返回");
            return Collections.emptyList();
        }
        // 关联校区(staff_id所在的租户)
        List<CampusInfoVO> campusInfoVOS = basicInfoRemote.listCampusByStaffId(userId,
                schoolStaffIdQueryDTO.getStaffId());
        log.info("【切换校区】-campusInfoVOS信息：{}", JSONUtil.toJsonStr(campusInfoVOS));
        // 数据可能是多个学校的 需要过滤下 只取当前学校下的校区
        List<CampusInfoVO> campusInfoVOSNew = campusInfoVOS.stream()
                .filter(d -> ObjectUtil.equal(d.getSchoolId(), schoolId)).collect(Collectors.toList());
        log.info("【切换校区】-campusInfoVOSNew信息：{}", JSONUtil.toJsonStr(campusInfoVOSNew));
        if (isMaterRole || CollUtil.isEmpty(campusInfoVOSNew)) {
            log.info("【切换校区】-当前学校不存在关联校区或为主角色（校长、教育主任）， 返回所有数据");
            UserSchoolCampusListVO userSchoolCampusListVO = new UserSchoolCampusListVO();
            userSchoolCampusListVO.setSchoolId(schoolId);
            userSchoolCampusListVO.setSchoolName(tchCampusInfoVOS.get(0).getSchoolName());
            userSchoolCampusListVO.setCampusInfoList(tchCampusInfoVOS);
            userSchoolCampusListVO.setStaffId(Convert.toLong(staffId));
            userSchoolCampusListVO.setTenantId(WebUtil.getTenantIdLong());

            userSchoolCampusListVOS.add(userSchoolCampusListVO);
        } else if (CollUtil.isNotEmpty(campusInfoVOSNew)) {
            log.info("【切换校区】-当前学校存在关联校区， 返回关联数据");
            UserSchoolCampusListVO userSchoolCampusListVO = new UserSchoolCampusListVO();
            userSchoolCampusListVO.setSchoolId(schoolId);
            userSchoolCampusListVO.setSchoolName(tchCampusInfoVOS.get(0).getSchoolName());
            userSchoolCampusListVO.setCampusInfoList(campusInfoVOSNew.stream().map(c -> {
                TchCampusInfoVO tchCampusInfoVO = new TchCampusInfoVO();
                tchCampusInfoVO.setCampusId(c.getCampusId());
                tchCampusInfoVO.setCampusName(c.getCampusName());
                return tchCampusInfoVO;
            }).collect(Collectors.toList()));
            userSchoolCampusListVO.setStaffId(Convert.toLong(staffId));
            userSchoolCampusListVO.setTenantId(campusInfoVOSNew.get(0).getTenantId());

            userSchoolCampusListVOS.add(userSchoolCampusListVO);
        }
        return userSchoolCampusListVOS;
    }

    @Override
    public List<UserSchoolCampusListVO> listCampusBySchool(SchoolStaffIdQueryDTO schoolStaffIdQueryDTO) {
        log.info("【H5】-【首页】-【获取用户学校下的关联校区】-请求参数：【{}】", JSONUtil.toJsonStr(schoolStaffIdQueryDTO));

        List<UserSchoolCampusListVO> userSchoolCampusListVOS = new ArrayList<>();
        String userId = schoolStaffIdQueryDTO.getUserId();
        if (CharSequenceUtil.isBlank(userId) || ObjectUtil.isNull(Convert.toLong(userId))) {
            log.warn("【H5】-【首页】-【获取用户学校下的关联校区】-userId不能为空");
            return Collections.emptyList();
        }

        List<UserSchoolVO> userSchoolVOS = listUserSchoolVO(userId);

        if (CollUtil.isEmpty(userSchoolVOS)) {
            log.warn("【H5】-【首页】-【获取用户学校下的关联校区】-userSchoolVOS为空，直接返回");
            return Collections.emptyList();
        }

        Map<Long, List<TchCampusInfoVO>> schoolIdTchCampusInfoVOMap = buildSchoolAndCampusMap(userSchoolVOS);

        TimeInterval timeInterval = DateUtil.timer();
        // 关联的校区（多个学校的关联校区）
        List<CampusInfoVO> campusInfoVOSAll = basicInfoRemote.listCampusByStaffId(schoolStaffIdQueryDTO.getUserId(),
                schoolStaffIdQueryDTO.getStaffId());

        log.warn("【H5】-【首页】-【获取用户学校下的关联校区】-【调用Saas接口，获取员工的校区列表数量：{}，耗时：{}】",
                campusInfoVOSAll.size(), timeInterval.intervalMs());
        timeInterval.restart();

        Map<Long/*SchoolId*/, List<CampusInfoVO>> longListMap = CollStreamUtil.groupByKey(campusInfoVOSAll,
                CampusInfoVO::getSchoolId);

        log.warn("【H5】-【首页】-【获取用户学校下的关联校区】-【聚合组装MAP，耗时：{}】", timeInterval.intervalMs());
        timeInterval.restart();

        userSchoolVOS.parallelStream().forEach(
                d -> buildUserSchoolCampusListVOs(d, longListMap, userSchoolCampusListVOS, schoolIdTchCampusInfoVOMap));

        log.warn("【H5】-【首页】-【获取用户学校下的关联校区】-【组装返回参数，耗时：{}】", timeInterval.intervalMs());

        return userSchoolCampusListVOS;
    }

    private Map<Long, List<TchCampusInfoVO>> buildSchoolAndCampusMap(List<UserSchoolVO> userSchoolVOS) {

        List<Long> SchoolIds = userSchoolVOS.stream().map(UserSchoolVO::getSchoolId).collect(Collectors.toList());

        TimeInterval timeInterval = DateUtil.timer();

        Map<Long, List<TchCampusInfoVO>> schoolIdTchCampusInfoVOMap = new HashMap<>();

        SchoolIdsRequest schoolIdsRequest = new SchoolIdsRequest();
        schoolIdsRequest.setSchoolIds(SchoolIds);
        List<SchoolBaseVO> schoolBaseVOS = saasSchoolManager.querySchoolCampusListBySchoolId(schoolIdsRequest);
        log.warn(
                "【H5】-【首页】-【获取用户学校下的关联校区】-【调用Saas接口，获取员工对应的【学校】下的【校区】列表数量：{}，耗时：{}】",
                schoolBaseVOS.size(), timeInterval.intervalMs());
        timeInterval.restart();

        if (CollUtil.isEmpty(schoolBaseVOS)) {
            log.warn("【H5】-【首页】-【获取用户学校下的关联校区】-schoolBaseVOS为空，直接返回");
            return Collections.emptyMap();
        }

        for (SchoolBaseVO schoolBaseVO : schoolBaseVOS) {

            Long schoolId = schoolBaseVO.getSchoolId();

            List<TchCampusInfoVO> campusInfoVOS = new ArrayList<>();

            List<SchoolCampusPojo> campusPojoList = schoolBaseVO.getCampusPojoList();
            for (SchoolCampusPojo schoolCampusPojo : campusPojoList) {
                TchCampusInfoVO tchCampusInfoVO = new TchCampusInfoVO();
                tchCampusInfoVO.setCampusId(schoolCampusPojo.getId());
                tchCampusInfoVO.setCampusName(schoolCampusPojo.getCampusName());
                tchCampusInfoVO.setSchoolId(schoolId);
                tchCampusInfoVO.setSchoolName(schoolBaseVO.getSchoolName());

                campusInfoVOS.add(tchCampusInfoVO);
            }

            schoolIdTchCampusInfoVOMap.put(schoolId, campusInfoVOS);
        }

        return schoolIdTchCampusInfoVOMap;
    }

    /**
     * 组装返回参数，每一个学校的校区信息
     *
     * @param userSchoolVO
     * @param campusInfoMapBySchoolId
     * @param userSchoolCampusListVOS
     */
    private void buildUserSchoolCampusListVOs(UserSchoolVO userSchoolVO,
                                              Map<Long, List<CampusInfoVO>> campusInfoMapBySchoolId,
                                              List<UserSchoolCampusListVO> userSchoolCampusListVOS,
                                              Map<Long, List<TchCampusInfoVO>> schoolIdTchCampusInfoVOMap) {

        Long schoolId = userSchoolVO.getSchoolId();

        boolean isMaterRole = checkMaster(userSchoolVO.getStaffId(), schoolId);

        List<CampusInfoVO> campusInfoVOS = campusInfoMapBySchoolId.getOrDefault(schoolId, Collections.emptyList());

        log.info("【切换校区】-当前学校：{}, 是否是主角色：{}, 关联的校区：{}", userSchoolVO.getSchoolName(), isMaterRole,
                JSONUtil.toJsonStr(campusInfoVOS));

        if (isMaterRole || CollUtil.isEmpty(campusInfoVOS)) {
            log.info("【切换校区】-当前学校不存在关联校区或为主角色（校长、教育主任）， 返回所有数据");
            UserSchoolCampusListVO userSchoolCampusListVO = new UserSchoolCampusListVO();
            userSchoolCampusListVO.setSchoolId(userSchoolVO.getSchoolId());
            userSchoolCampusListVO.setSchoolName(userSchoolVO.getSchoolName());
            userSchoolCampusListVO.setStaffId(userSchoolVO.getStaffId());
            userSchoolCampusListVO.setTenantId(userSchoolVO.getTenantId());

            userSchoolCampusListVO.setCampusInfoList(schoolIdTchCampusInfoVOMap.get(userSchoolVO.getSchoolId()));

            userSchoolCampusListVOS.add(userSchoolCampusListVO);
        } else if (CollUtil.isNotEmpty(campusInfoVOS)) {
            log.info("【切换校区】-当前学校存在关联校区， 返回关联数据");
            UserSchoolCampusListVO userSchoolCampusListVO = new UserSchoolCampusListVO();
            userSchoolCampusListVO.setSchoolId(campusInfoVOS.get(0).getSchoolId());
            userSchoolCampusListVO.setSchoolName(campusInfoVOS.get(0).getSchoolName());
            userSchoolCampusListVO.setStaffId(campusInfoVOS.get(0).getStaffId());
            userSchoolCampusListVO.setTenantId(campusInfoVOS.get(0).getTenantId());
            userSchoolCampusListVO.setCampusInfoList(campusInfoVOS.stream().map(c -> {
                TchCampusInfoVO tchCampusInfoVO = new TchCampusInfoVO();
                tchCampusInfoVO.setCampusId(c.getCampusId());
                tchCampusInfoVO.setCampusName(c.getCampusName());
                return tchCampusInfoVO;
            }).collect(Collectors.toList()));

            userSchoolCampusListVOS.add(userSchoolCampusListVO);
        }
    }


    /**
     * 根据用户ID 查询Saas对应的学校信息
     *
     * @param userId
     * @return
     */
    private List<UserSchoolVO> listUserSchoolVO(String userId) {
        Long userIdLong = Convert.toLong(userId);
        if (ObjectUtil.isNull(userIdLong)) {
            log.warn("【根据用户ID 查询Saas对应的学校信息】-【userId不能为空，入参信息:{}】", userId);
            return Collections.emptyList();
        }

        TimeInterval timeInterval = DateUtil.timer();
        // 先查出所有学校
        UserSchoolQueryDTO userSchoolQueryDTO = new UserSchoolQueryDTO();
        userSchoolQueryDTO.setUserId(userIdLong);
        List<UserSchoolVO> saasUserSchoolVOS = basicInfoRemote.userSchoolList(userSchoolQueryDTO);

        log.warn(
                "【H5】-【首页】-【获取用户学校下的关联校区】-【调用Saas接口，根据用户id查询当前用户所关联的学校数量：{}，耗时：{}】",
                saasUserSchoolVOS.size(), timeInterval.intervalMs());

        if (CollUtil.isEmpty(saasUserSchoolVOS)) {
            return Collections.emptyList();
        }

        String sourcePlatform = RequestUtil.getHeader("sourcePlatform");

        // 钉钉只显示当前租户下的学校
        if (StrUtil.isNotBlank(sourcePlatform) && "1".equals(sourcePlatform)) {

            return saasUserSchoolVOS.stream().filter(d -> ObjectUtil.equal(d.getTenantId(), WebUtil.getTenantIdLong()))
                    .collect(Collectors.toList());

        } else {
            return saasUserSchoolVOS;
        }
    }

    private boolean checkMaster(Long staffId, Long schoolId) {
        List<String> roleCodeList;
        List<com.hailiang.saas.model.vo.role.ResStaffRoleVO> resStaffRoleVOS = saasStaffManager.queryStaffRoleList(
                schoolId, staffId);
        if (CollUtil.isEmpty(resStaffRoleVOS)) {
            log.info("当前登录人没有配置角色，返回false");
            return false;
        }
        List<com.hailiang.saas.model.pojo.role.ResRoleInfoPojo> roles = resStaffRoleVOS.get(0).getRoles();
        log.info("当前登录人具有的角色{}", JSONUtil.toJsonStr(roles));
        roleCodeList = CollStreamUtil.toList(roles, com.hailiang.saas.model.pojo.role.ResRoleInfoPojo::getRoleCode);
        //校长、德育处主任、教务主任 返回全部数据
        return roleLogic.isHaveMaterRole(roleCodeList);
    }

    /**
     * 获取学生列表(无权限) 查询学生列表以及学生的班级信息（不包含已经毕业的学生）
     */
    @Override
    public List<EduStudentInfoVO> queryStudentPageNoAuth(EduStudentPageQueryDTO eduStudentPageQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====获取学生列表以及学生的班级信息(无权限)");
        List<EduStudentInfoVO> eduStudentInfoByStudentIds = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);

        List<Long> studentIdList = eduStudentInfoByStudentIds.stream().map(EduStudentInfoVO::getId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIdList)) {
            return new ArrayList<>();
        }
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        List<String> classTypes = new ArrayList<>();
        classTypes.add(SaasClassTypeEnum.XINGZHENG.getCode());
        eduStudentClassQueryDTO.setClassTypes(classTypes);
        eduStudentClassQueryDTO.setStudentIds(studentIdList);
        List<EduStudentClassVO> eduStudentClassVOList = basicInfoRemote.queryClassListByStudentIds(
                eduStudentClassQueryDTO);
        Map<Long, List<EduClassBaseInfoPojo>> idClassMap = eduStudentClassVOList.stream()
                .collect(Collectors.toMap(EduStudentClassVO::getStudentId, EduStudentClassVO::getClassBaseInfos));
        for (EduStudentInfoVO eduStudentInfoVO : eduStudentInfoByStudentIds) {
            List<EduClassBaseInfoPojo> eduClassBaseInfoPojoList = idClassMap.get(eduStudentInfoVO.getId());
            if (Objects.nonNull(eduClassBaseInfoPojoList) && !eduClassBaseInfoPojoList.isEmpty()) {
                eduStudentInfoVO.setClassId(eduClassBaseInfoPojoList.get(0).getId());
                eduStudentInfoVO.setClassName(eduClassBaseInfoPojoList.get(0).getClassName());
            }
            eduStudentInfoVO.setClassBaseInfos(idClassMap.get(eduStudentInfoVO.getId()));
        }

        return eduStudentInfoByStudentIds;

    }

    @Override
    public List<EduStudentInfoVO> queryStudentDetailPageNoAuth(EduStudentPageQueryDTO eduStudentPageQueryDTO) {
        log.info("【BasicInfoService层saas服务调用】====获取学生列表以及学生的班级信息(无权限)");
        List<EduStudentInfoVO> studentInfoList = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
        if (CollUtil.isEmpty(studentInfoList)) {
            log.warn("【SAAS】-【查询的学生信息不存在】-【request:{}】", eduStudentPageQueryDTO);
            return Collections.emptyList();
        }

        List<Long> studentIdList = studentInfoList.stream().map(EduStudentInfoVO::getId).collect(Collectors.toList());

        //fill班级信息
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        eduStudentClassQueryDTO.setClassTypes(Lists.newArrayList(SaasClassTypeEnum.XINGZHENG.getCode()));
        eduStudentClassQueryDTO.setStudentIds(studentIdList);
        List<EduStudentClassVO> studentClassList = basicInfoRemote.queryClassListByStudentIds(eduStudentClassQueryDTO);
        Map<Long, List<EduClassBaseInfoPojo>> classMapByStudentId = studentClassList.stream()
                .collect(Collectors.toMap(EduStudentClassVO::getStudentId, EduStudentClassVO::getClassBaseInfos));

        for (EduStudentInfoVO eduStudentInfoVO : studentInfoList) {
            List<EduClassBaseInfoPojo> eduClassBaseInfoPojoList = classMapByStudentId.get(eduStudentInfoVO.getId());
            if (Objects.nonNull(eduClassBaseInfoPojoList) && !eduClassBaseInfoPojoList.isEmpty()) {
                eduStudentInfoVO.setClassId(eduClassBaseInfoPojoList.get(0).getId());
                eduStudentInfoVO.setClassName(eduClassBaseInfoPojoList.get(0).getClassName());
                eduStudentInfoVO.setSchoolId(eduClassBaseInfoPojoList.get(0).getSchoolId());
            }
            eduStudentInfoVO.setClassBaseInfos(classMapByStudentId.get(eduStudentInfoVO.getId()));
        }

        //fill年级信息
        List<EduClassInfoVO> classInfoVOS = basicInfoRemote.queryClassInfoList(
                this.getEduClassQueryDTO(eduStudentPageQueryDTO));
        Map<Long, EduClassInfoVO> classMap = classInfoVOS.stream()
                .collect(Collectors.toMap(EduClassInfoVO::getId, Function.identity()));

        studentInfoList.forEach(info -> {
            EduClassInfoVO classInfoVO = classMap.getOrDefault(info.getClassId(), new EduClassInfoVO());
            info.setGradeId(classInfoVO.getGradeId());
            info.setGradeName(classInfoVO.getGradeName());
        });

        return studentInfoList;
    }


    /**
     * 获取单个学生信息(行政班)(无权限)
     *
     * @param schoolId
     * @param studentId
     * @return
     */
    @Override
    public StudentVO getStudentInfoByStudentId(Long schoolId, Long studentId) {
        log.info("【BasicInfoService层saas服务调用】====获取单个学生信息(行政班)(无权限)");
        try {
            if (ObjectUtil.hasNull(schoolId, studentId)) {
                log.info("根据学号获取学生信息参数,学校或学号为空,查询失败");
                return new StudentVO();
            }
            StudentVO studentVO = new StudentVO();
            // 通过学生id集合查询学生信息
            EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
            eduStudentPageQueryDTO.setSchoolId(schoolId);
            eduStudentPageQueryDTO.setStudentIds(CollUtil.newArrayList(studentId));
            List<EduStudentInfoVO> eduStudentInfoByStudentIds = this.queryStudentPageNoAuth(eduStudentPageQueryDTO);
            if (CollUtil.isNotEmpty(eduStudentInfoByStudentIds)) {
                EduStudentInfoVO eduStudentInfoVO = CollUtil.getFirst(eduStudentInfoByStudentIds);

                //封装学生信息
                studentVO.setStudentId(eduStudentInfoVO.getId());
                studentVO.setStudentNo(eduStudentInfoVO.getStudentNo());
                studentVO.setStudentName(eduStudentInfoVO.getStudentName());
                studentVO.setClassId(eduStudentInfoVO.getClassId());
                studentVO.setClassName(eduStudentInfoVO.getClassName());

                //班主任id
                List<Long> headMasterIds = eduStudentInfoVO.getClassBaseInfos().stream()
                        .filter(t -> t.getClassType().equals("0")).map(EduClassBaseInfoPojo::getHeadMasterId).distinct()
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(headMasterIds)) {
                    StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
                    staffBatchQueryDTO.setStaffIdList(CollUtil.newArrayList(CollUtil.getFirst(headMasterIds)));
                    staffBatchQueryDTO.setState(0);
                    List<StaffBatchVO> staffBatchVOS = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
                    if (CollUtil.isNotEmpty(staffBatchVOS)) {
                        StaffBatchVO staffBatchVO = staffBatchVOS.get(0);
                        studentVO.setHeaderMasterStaffCode(staffBatchVO.getStaffCode());
                        studentVO.setHeaderMasterName(staffBatchVO.getName());
                        studentVO.setHeaderMasterMobile(staffBatchVO.getMobile());
                        studentVO.setHeaderMasterId(staffBatchVO.getId());
                    }
                }
            }
            log.info("根据学号获取学生信息参数,studentInfo=【{}】", studentVO);
            return studentVO;
        } catch (Exception e) {
            log.warn("根据学号获取学生信息失败,schoolId=【{}】,studentIds=【{}】", schoolId, studentId, e);
        }
        return new StudentVO();
    }

    /**
     * 获取单个教师信息(正常非离职状态)
     *
     * @param staffId
     * @return
     */
    @Override
    public StaffBatchVO getBasicInfoByStaffId(Long staffId) {
        log.info("【BasicInfoService层saas服务调用】====获取单个教师信息(正常非离职状态)");
        StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
        staffBatchQueryDTO.setStaffIdList(CollUtil.newArrayList(staffId));
        staffBatchQueryDTO.setState(SaasStaffStateQueryEnum.NORMAL.getCode());
        List<StaffBatchVO> staffBatchVOS = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
        if (CollUtil.isEmpty(staffBatchVOS)) {
            return new StaffBatchVO();
        }
        return CollUtil.getFirst(staffBatchVOS);
    }


    /**
     * 获取单个教师信息(正常非离职状态)
     *
     * @param staffIdList
     * @return
     */
    @Override
    public List<StaffBatchVO> getBasicInfoByStaffIdList(List<Long> staffIdList) {
        log.info("【BasicInfoService层saas服务调用】====获取单个教师信息(正常非离职状态)");
        StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
        staffBatchQueryDTO.setStaffIdList(staffIdList);
        staffBatchQueryDTO.setState(SaasStaffStateQueryEnum.NORMAL.getCode());
        List<StaffBatchVO> staffBatchVOS = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
        if (CollUtil.isEmpty(staffBatchVOS)) {
            return Collections.emptyList();
        }
        return staffBatchVOS;
    }

    @Override
    public StudentInfoVO getStudentInfoByStudentNoAndName(String studentCode, String name) {
        List<StaffNOVO> students = basicInfoRemote.listUnderByNameNo(studentCode, URLUtil.decode(name));
        if (CollUtil.isEmpty(students)) {
            log.warn("查询学生信息students为空，{}{}", studentCode, name);
            return new StudentInfoVO();
        }
        if (students.size() > 1) {
            log.warn("根据学号和姓名查出多条数据,只取第一条，{}，{}，{}", studentCode, name, JSONUtil.toJsonStr(students));
        }
        StudentInfoVO studentInfoVO = new StudentInfoVO();

        StaffNOVO studentInfo = CollUtil.getFirst(students);
        BeanUtil.copyProperties(studentInfo, studentInfoVO);

        //查询学生其他信息(包括班级id)
        StudentVO studentVO = this.getStudentInfoByStudentId(Convert.toLong(studentInfo.getSchoolId()),
                studentInfo.getId());
        if (ObjectUtil.isEmpty(studentVO)) {
            log.warn("查询学生信息studentVO为空，{}{}", studentCode, name);
            return new StudentInfoVO();
        }
        studentInfoVO.setClassId(studentVO.getClassId());
        studentInfoVO.setClassName(studentVO.getClassName());

        return studentInfoVO;
    }

    /**
     * 查询教职工菜单权限列表
     *
     * @return
     */
    @Override
    public List<MenuAuthVO> listMenuAuth() {
        return basicInfoRemote.listMenuAuth();
    }

    /**
     * 查saas值日生、值日老师和值日干部角色列表
     *
     * @return
     */
    @Override
    public List<SaaSOnDutyRoleVO> querySaaSOnDutyRoleList() {
        return SaaSOnDutyRoleEnum.list().stream()
                .map(item -> new SaaSOnDutyRoleVO().setRoleCode(item.getCode()).setRoleName(item.getMessage()))
                .collect(Collectors.toList());
    }

    /**
     * 查学生列表
     *
     * @param query
     * @return
     */
    public List<SaaSEduStudentInfoVO> queryStudentList(EduStudentPageQueryDTO query) {
        CommonResult<PageResult<SaaSEduStudentInfoVO>> result = saaSAPI.queryStudentPage(query);
        log.info("Saas远程请求【/educational/teachManage/v1/student/list】-请求参数：【{}】-返回信息：【{}】",
                JSONUtil.toJsonStr(query), JSONUtil.toJsonStr(result));

        if (ObjectUtil.isNull(result) || ObjectUtil.isNull(result.getData()) || CollUtil.isEmpty(
                result.getData().getList())) {
            return Collections.emptyList();
        }

        return result.getData().getList();
    }

    /**
     * 查学生的值日生、值日老师、值日干部角色列表
     *
     * @param schoolId
     * @param userId
     * @return
     */
    @Override
    public List<Integer> queryStudentDutyRoleList(Long schoolId, Long userId) {
        return this.queryDutyRoleList(schoolId, userId, DutyUserTypeEnum.STUDENT.getCode());
    }

    /**
     * 查教职工的值日生、值日老师、值日干部角色列表
     *
     * @param schoolId
     * @param userId
     * @return
     */
    @Override
    public List<Integer> queryStaffDutyRoleList(Long schoolId, Long userId) {
        return this.queryDutyRoleList(schoolId, userId, DutyUserTypeEnum.STAFF.getCode());
    }

    /**
     * 查用户的值日生、值日老师、值日干部角色列表
     *
     * @param schoolId
     * @param userId
     * @return
     */
    @Override
    public List<Integer> queryDutyRoleList(Long schoolId, Long userId, Integer dutyRoleType) {
        EduDutyRoleQueryDTO param = new EduDutyRoleQueryDTO();
        param.setDutyUserType(dutyRoleType).setUserIds(Collections.singletonList(userId)).setSchoolId(schoolId);
        List<EduDutyRoleVO> saasList = this.queryDutyRoleList(param);
        if (CollUtil.isEmpty(saasList)) {
            return Collections.emptyList();
        }

        return saasList.get(0).getDutyRoleList();
    }

    @ApiOperation("获取用户值日组角色信息列表批量接口(值日学生、值日教师、值日干部)")
    public List<EduDutyRoleVO> queryDutyRoleList(EduDutyRoleQueryDTO param) {
        this.check(param);
        CommonResult<List<EduDutyRoleVO>> saasResult = saaSAPI.queryDutyRoleList(param);
        log.info("Saas远程请求【/educational/duty_group/v1/role/list】-请求参数：【{}】-返回信息：【{}】",
                JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(saasResult));
        if (ObjectUtil.isNull(saasResult) || CollUtil.isEmpty(saasResult.getData())) {
            return Collections.emptyList();
        }
        return saasResult.getData();
    }

    private void check(EduDutyRoleQueryDTO param) {
        AssertUtil.checkNotNull(param, "参数不能为空");
        AssertUtil.checkNotNull(param.getDutyUserType(), "值日组用户类型不能为空");
        AssertUtil.checkIsTrue(DutyUserTypeEnum.isExists(param.getDutyUserType()), "用户类型输入错误，请检查");
        AssertUtil.checkNotNull(param.getSchoolId(), "学校id不能为空");
        AssertUtil.checkNotEmpty(param.getUserIds(), "用户id列表不能为空");
        AssertUtil.checkIsTrue(CollUtil.size(param.getUserIds()) <= Constant.TWO_HUNDRED, "用户id列表最多两百个值");
    }

    /**
     * 查学生信息（附带班级信息）saas原始信息
     *
     * @param param
     * @return
     */
    private List<SaaSEduStudentInfoVO> querySaaSStudentListWithClassInfo(QueryStudentListWithClassDTO param) {
        AssertUtil.checkIsTrue(SaasCurrentIdTypeEnum.isClassType(param.getCurrentIdType()),
                "当前id类型输入错误，请检查");

        List<String> classTypeList = Collections.singletonList(SaasClassTypeEnum.XINGZHENG.getCode());
        QueryStudentListDTO dto = new QueryStudentListDTO();
        dto.setCurrentId(param.getCurrentId()).setCurrentIdType(param.getCurrentIdType())
                .setStudentName(param.getStudentName()).setWithClass(Boolean.TRUE).setWithRoom(Boolean.TRUE)
                .setClassTypes(classTypeList).setPageNum(Constant.ONE).setPageSize(Integer.MAX_VALUE);

        EduStudentPageQueryDTO query = this.convert(dto);

        return this.queryStudentList(query);
    }

    /**
     * 查学生信息（附带班级信息）
     *
     * @param param
     * @return
     */
    @Override
    public List<StudentWithClassInfoListVO> queryStudentListWithClassInfo(QueryStudentListWithClassDTO param) {
        List<SaaSEduStudentInfoVO> list = this.querySaaSStudentListWithClassInfo(param);
        List<StudentWithClassInfoListVO> studentWithClassInfoListVOS = this.convertToStudentWithClassInfo(list);
        this.buildStudentListWithFullName(studentWithClassInfoListVOS);

        return studentWithClassInfoListVOS;
    }

    /**
     * 学生列表添加房间号全称
     */
    private void buildStudentListWithFullName(List<StudentWithClassInfoListVO> studentWithClassInfoListVOS) {

        Map<Long, Integer> leaveStatusFlagMap = this.getLeaveStatusFlagMap(
                studentWithClassInfoListVOS.stream()
                        .map(StudentWithClassInfoListVO::getStudentId)
                        .distinct()
                        .collect(Collectors.toList()));
        studentWithClassInfoListVOS.forEach(s->{
            // 设置请假状态
            s.setLeaveStatusFlag(leaveStatusFlagMap.getOrDefault(s.getStudentId(), Constant.NO));
        });

        for (StudentWithClassInfoListVO studentWithClassInfoListVO : studentWithClassInfoListVOS) {
            if (ObjectUtil.isNull(studentWithClassInfoListVO.getRoomId())) {
                continue;
            }
            SchoolYardRoomQueryDTO dto = new SchoolYardRoomQueryDTO();
            dto.setRoomId(studentWithClassInfoListVO.getRoomId());
            dto.setSchoolId(WebUtil.getSchoolIdLong());
            dto.setWithRoomStudentCount(false);

            //saas 目前没有批量查询房间信息接口，只能单个通过房间号 id 查询
            Object object = redisUtil.get(RedisKeyConstants.DUTY_BUILDING_FLOOR_ROOM_ID + WebUtil.getSchoolId()
                    + studentWithClassInfoListVO.getRoomId());
            if (ObjectUtil.isNull(object)) {
                CommonResult<List<SchoolYardRoomVO>> listCommonResult = saaSYardAPI.queryYardRoom(dto);
                if (ObjectUtil.isNull(listCommonResult) || CollUtil.isEmpty(listCommonResult.getData())) {
                    log.warn("根据宿舍id查询宿舍信息为空，roomId: {},schoolId:{}",
                            studentWithClassInfoListVO.getRoomId(), WebUtil.getSchoolIdLong());
                    continue;
                }
                Optional<SchoolYardRoomVO> first = listCommonResult.getData().stream().findFirst();
                if (first.isPresent()) {
                    SchoolYardRoomVO schoolYardRoomVO = first.get();
                    redisUtil.set(RedisKeyConstants.DUTY_BUILDING_FLOOR_ROOM_ID + WebUtil.getSchoolId()
                            + studentWithClassInfoListVO.getRoomId(), schoolYardRoomVO, 600);
                    studentWithClassInfoListVO.setBuildingName(schoolYardRoomVO.getBuildingName());
                    studentWithClassInfoListVO.setFloorName(schoolYardRoomVO.getFloorName());
                    studentWithClassInfoListVO.setFullName(
                            CharSequenceUtil.format("{}{}楼{}", schoolYardRoomVO.getBuildingName(),
                                    schoolYardRoomVO.getFloorName(), schoolYardRoomVO.getName()));
                }
                continue;
            }
            SchoolYardRoomVO schoolYardRoomVO = BeanUtil.toBean(object, SchoolYardRoomVO.class);
            studentWithClassInfoListVO.setBuildingName(schoolYardRoomVO.getBuildingName());
            studentWithClassInfoListVO.setFloorName(schoolYardRoomVO.getFloorName());
            studentWithClassInfoListVO.setFullName(
                    CharSequenceUtil.format("{}{}楼{}", schoolYardRoomVO.getBuildingName(),
                            schoolYardRoomVO.getFloorName(), schoolYardRoomVO.getName()));
        }
    }

    /**
     * 值日检查--查学生信息（树形）
     *
     * @param param
     * @return
     */
    @Override
    public List<StudentTreeVO> queryGradeStudentTree(QueryGradeStudentTreeDTO param) {
        QueryStudentListWithClassDTO query = basicConvert.toQueryStudentListWithClassDTO(param);

        AssertUtil.checkNotBlank(query.getStudentName(), "学生姓名不能为空");
        List<SaaSEduStudentInfoVO> list = this.querySaaSStudentListWithClassInfo(query);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<StudentWithClassGradeInfoSortListVO> plateList = this.convert(list);

        return this.convertToStudentTree(plateList);
    }

    public List<StudentTreeVO> convertToStudentTree(List<StudentWithClassGradeInfoSortListVO> plateList) {
        if (CollUtil.isEmpty(plateList)) {
            return Collections.emptyList();
        }
        List<StudentTreeVO> gradeList = plateList.stream()
                .map(item -> new StudentTreeVO().setId(item.getGradeId()).setName(item.getGradeName())
                        .setType(Constant.FOUR)).distinct().collect(Collectors.toList());
        for (StudentTreeVO grade : gradeList) {
            List<StudentTreeClassVO> classList = plateList.stream()
                    .filter(item -> grade.getId().equals(item.getGradeId()))
                    .map(classInfo -> new StudentTreeClassVO().setId(classInfo.getClassId())
                            .setName(classInfo.getClassName()).setType(Constant.FIVE)).distinct()
                    .collect(Collectors.toList());
            grade.setChildren(classList);
        }

        Map<Long, List<StudentWithClassGradeInfoSortListVO>> classIdStudentListMap = plateList.stream()
                .collect(Collectors.groupingBy(StudentWithClassGradeInfoSortListVO::getClassId));

        for (StudentTreeVO grade : gradeList) {
            List<StudentTreeClassVO> children = grade.getChildren();
            if (CollUtil.isEmpty(children)) {
                continue;
            }
            for (StudentTreeClassVO classInfo : children) {
                List<StudentWithClassGradeInfoSortListVO> studentList = classIdStudentListMap.get(classInfo.getId());
                List<StudentTreeStudentVO> studentInfoList = studentList.stream().map(item -> this.convert(item))
                        .distinct().collect(Collectors.toList());
                classInfo.setChildren(studentInfoList);
            }
        }

        return gradeList;
    }

    private StudentTreeStudentVO convert(StudentWithClassGradeInfoSortListVO param) {
        return new StudentTreeStudentVO().setId(param.getStudentId()).setName(param.getStudentName())
                .setType(Constant.SIX).setClassId(param.getClassId()).setClassName(param.getClassName())
                .setRoomId(param.getRoomId()).setRoomName(param.getRoomName()).setAvatar(param.getAvatar());
    }

    private List<StudentWithClassInfoListVO> convertToStudentWithClassInfo(List<SaaSEduStudentInfoVO> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<StudentWithClassGradeInfoSortListVO> studentList = this.convert(list);
        return basicConvert.toStudentWithClassGradeInfoSortListVO(studentList);
    }

    /**
     * 查学生信息（附带宿舍的床位信息）
     *
     * @param param
     * @return
     */
    @Override
    public List<StudentWithRoomInfoListVO> queryStudentListWithRoomInfo(QueryStudentListWithRoomDTO param) {
        AssertUtil.checkIsTrue(SaasCurrentIdTypeEnum.isRoomType(param.getCurrentIdType()), "当前id类型输入错误，请检查");
        QueryStudentListDTO dto = new QueryStudentListDTO();
        dto.setCurrentId(param.getCurrentId()).setCurrentIdType(param.getCurrentIdType())
                .setStudentName(param.getStudentName()).setWithRoom(Boolean.TRUE).setWithClass(Boolean.TRUE)
                .setClassTypes(Collections.singletonList(SaasClassTypeEnum.XINGZHENG.getCode()));

        EduStudentPageQueryDTO query = this.convert(dto);

        List<StudentWithRoomInfoListVO> studentWithRoomInfoListVOS = this.queryStudentInfoList(query);
        this.buildStudentWithFullName(studentWithRoomInfoListVOS);
        return studentWithRoomInfoListVOS;
    }

    /**
     * 构建学生寝室全民
     */
    private void buildStudentWithFullName(List<StudentWithRoomInfoListVO> studentWithRoomInfoListVOS) {
        for (StudentWithRoomInfoListVO studentWithRoomInfoListVO : studentWithRoomInfoListVOS) {
            if (ObjectUtil.isNull(studentWithRoomInfoListVO.getRoomId())) {
                continue;
            }
            SchoolYardRoomQueryDTO queryDto = new SchoolYardRoomQueryDTO();
            queryDto.setRoomId(studentWithRoomInfoListVO.getRoomId());
            queryDto.setSchoolId(WebUtil.getSchoolIdLong());
            queryDto.setWithRoomStudentCount(false);

            //saas 目前没有批量查询房间信息接口，只能单个通过房间号 id 查询
            Object object = redisUtil.get(RedisKeyConstants.DUTY_BUILDING_FLOOR_ROOM_ID + WebUtil.getSchoolId()
                    + studentWithRoomInfoListVO.getRoomId());
            if (ObjectUtil.isNull(object)) {
                CommonResult<List<SchoolYardRoomVO>> listCommonResult = saaSYardAPI.queryYardRoom(queryDto);
                if (ObjectUtil.isNull(listCommonResult) || CollUtil.isEmpty(listCommonResult.getData())) {
                    log.warn("根据宿舍id查询宿舍信息为空，roomId: {},schoolId:{}", studentWithRoomInfoListVO.getRoomId(),
                            WebUtil.getSchoolIdLong());
                    continue;
                }
                Optional<SchoolYardRoomVO> first = listCommonResult.getData().stream().findFirst();
                if (first.isPresent()) {
                    SchoolYardRoomVO schoolYardRoomVO = first.get();
                    redisUtil.set(RedisKeyConstants.DUTY_BUILDING_FLOOR_ROOM_ID + WebUtil.getSchoolId()
                            + studentWithRoomInfoListVO.getRoomId(), schoolYardRoomVO, 600);
                    studentWithRoomInfoListVO.setBuildingName(schoolYardRoomVO.getBuildingName());
                    studentWithRoomInfoListVO.setFloorName(schoolYardRoomVO.getFloorName());
                    studentWithRoomInfoListVO.setFullName(
                            CharSequenceUtil.format("{}{}楼{}", schoolYardRoomVO.getBuildingName(),
                                    schoolYardRoomVO.getFloorName(), schoolYardRoomVO.getName()));
                }
                continue;
            }
            SchoolYardRoomVO schoolYardRoomVO = BeanUtil.toBean(object, SchoolYardRoomVO.class);
            studentWithRoomInfoListVO.setBuildingName(schoolYardRoomVO.getBuildingName());
            studentWithRoomInfoListVO.setFloorName(schoolYardRoomVO.getFloorName());
            studentWithRoomInfoListVO.setFullName(
                    CharSequenceUtil.format("{}{}楼{}", schoolYardRoomVO.getBuildingName(),
                            schoolYardRoomVO.getFloorName(), schoolYardRoomVO.getName()));

        }
    }

    /**
     * 查学生信息（附带宿舍的床位信息）
     *
     * @return
     */
    @Override
    public List<StudentWithRoomInfoListVO> queryStudentListWithRoomInfo(Long schoolId, List<Long> roomIdList) {
        AssertUtil.checkNotNull(schoolId, "学校id不能为空");
        AssertUtil.checkNotEmpty(roomIdList, "宿舍id列表不能为空");

        EduStudentPageQueryDTO query = new EduStudentPageQueryDTO().setSchoolId(schoolId).setRoomIds(roomIdList)
                .setWithRoom(Boolean.TRUE).setWithClass(Boolean.TRUE)
                .setClassTypes(Collections.singletonList(SaasClassTypeEnum.XINGZHENG.getCode()))
                .setGraduationStatus(Constant.NO.toString());

        return this.queryStudentInfoList(query);
    }

    /**
     * 查学生信息
     *
     * @param param
     * @return
     */
    private List<StudentWithRoomInfoListVO> queryStudentInfoList(EduStudentPageQueryDTO param) {
        List<SaaSEduStudentInfoVO> studentList = this.queryStudentList(param);
        if (CollUtil.isEmpty(studentList)) {
            return Collections.emptyList();
        }
        if (ObjectUtil.isNotNull(param.getWithRoom()) && param.getWithRoom()) {
            studentList = studentList.stream().filter(item -> CollUtil.isNotEmpty(item.getRoomBedList()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(studentList)) {
                return Collections.emptyList();
            }
        }

        return this.convertToStudentWithRoomList(studentList);
    }

    /**
     * 根据宿舍id列表查宿舍里的学生所对应的班级
     *
     * @return
     */
    @Override
    public List<RoomClassInfoListVO> queryRoomClassList(Long schoolId, List<Long> roomIdList) {
        List<StudentWithRoomInfoListVO> studentList = this.queryStudentListWithRoomInfo(schoolId, roomIdList);
        if (CollUtil.isEmpty(studentList)) {
            return Collections.emptyList();
        }

        return this.convertToTree(studentList);
    }

    private List<RoomClassInfoListVO> convertToTree(List<StudentWithRoomInfoListVO> studentList) {
        if (CollUtil.isEmpty(studentList)) {
            return Collections.emptyList();
        }

        Map<Long, List<StudentWithRoomInfoListVO>> roomIdStudentListMap = studentList.stream()
                .collect(Collectors.groupingBy(StudentWithRoomInfoListVO::getRoomId));
        List<RoomClassInfoListVO> resultList = Lists.newArrayList();
        for (Long roomId : roomIdStudentListMap.keySet()) {
            RoomClassInfoListVO item = new RoomClassInfoListVO().setRoomId(roomId);
            List<StudentWithRoomInfoListVO> studentInfoList = roomIdStudentListMap.get(roomId);
            StudentWithRoomInfoListVO student = studentInfoList.get(0);
            item.setRoomName(student.getRoomName());
            List<RoomClassInfoVO> goalClassList = studentInfoList.stream()
                    .sorted(Comparator.comparing(StudentWithRoomInfoListVO::getGradeCode)
                            .thenComparing(StudentWithRoomInfoListVO::getClassNum))
                    .map(record -> new RoomClassInfoVO().setClassId(record.getClassId())
                            .setClassName(record.getClassName())).distinct().collect(Collectors.toList());
            item.setClassList(goalClassList);
            resultList.add(item);

            // 设置楼栋楼名称-楼层名称-房间号
            this.buildWithBuildingFloorName(roomId, item);
        }
        return resultList;
    }

    /**
     * 设置楼栋楼名称-楼层名称-房间号
     */
    private void buildWithBuildingFloorName(Long roomId, RoomClassInfoListVO item) {
        SchoolYardRoomQueryDTO query = new SchoolYardRoomQueryDTO();
        query.setRoomId(roomId);
        query.setSchoolId(WebUtil.getSchoolIdLong());
        query.setWithRoomStudentCount(false);
        CommonResult<List<SchoolYardRoomVO>> listCommonResult = saaSYardAPI.queryYardRoom(query);
        if (ObjectUtil.isNull(listCommonResult) || CollUtil.isEmpty(listCommonResult.getData())) {
            log.warn("根据宿舍id查询宿舍信息为空，roomId: {},schoolId:{}", roomId, WebUtil.getSchoolIdLong());
            return;
        }
        Optional<SchoolYardRoomVO> first = listCommonResult.getData().stream().findFirst();
        if (first.isPresent()) {
            SchoolYardRoomVO schoolYardRoomVO = first.get();
            item.setBuildingName(schoolYardRoomVO.getBuildingName());
            item.setFloorName(schoolYardRoomVO.getFloorName());
            item.setFullName(CharSequenceUtil.format("{}{}楼{}", schoolYardRoomVO.getBuildingName(),
                    schoolYardRoomVO.getFloorName(), schoolYardRoomVO.getName()));
        }
    }

    /**
     * 转换为学生宿舍信息
     *
     * @param studentList
     * @return
     */
    private List<StudentWithRoomInfoListVO> convertToStudentWithRoomList(List<SaaSEduStudentInfoVO> studentList) {
        List<StudentWithRoomInfoSortListVO> list = Lists.newArrayList();
        for (SaaSEduStudentInfoVO item : studentList) {
            StudentWithRoomInfoSortListVO student = new StudentWithRoomInfoSortListVO().setStudentId(item.getId())
                    .setStudentName(item.getStudentName()).setAvatar(item.getFaceImage());
            list.add(student);
            // 不住校的学生没有床铺
            if (CollUtil.isNotEmpty(item.getRoomBedList())) {
                // 理论上一个人只有一个床铺，saas反馈取第一个
                RoomBedVO roomBed = item.getRoomBedList().get(0);
                String bedName = SaaSBedTypeEnum.getBedName(roomBed.getBedType(), roomBed.getRow(), roomBed.getCol());
                student.setBedName(bedName);
                student.setCol(roomBed.getCol());
                student.setRoomId(roomBed.getRoomId());
                student.setRoomName(roomBed.getRoomName());
            }
            if (CollUtil.isNotEmpty(item.getClassList())) {
                // 一个人只属于一个行政班，取第一个班级
                ClassSimpleVO classInfo = item.getClassList().get(0);
                student.setClassId(classInfo.getClassId());
                student.setClassName(classInfo.getClassName());
                student.setClassNum(classInfo.getClassNum());
                student.setGradeId(classInfo.getGradeId());
                student.setGradeName(classInfo.getGradeName());
                student.setGradeCode(classInfo.getGradeCode());
            }

        }
        // 排序
        List<StudentWithRoomInfoSortListVO> sortList = this.sortList(list);
        return basicConvert.toStudentRoomClassInfoListVOList(sortList);
    }

    private List<StudentWithRoomInfoSortListVO> sortList(List<StudentWithRoomInfoSortListVO> list) {
        // 住校生
        List<StudentWithRoomInfoSortListVO> boarderList = Lists.newArrayList();
        // 非住校生
        List<StudentWithRoomInfoSortListVO> unBoarderList = Lists.newArrayList();

        for (StudentWithRoomInfoSortListVO item : list) {
            if (StrUtil.isNotBlank(item.getBedName())) {
                boarderList.add(item);
                continue;
            }
            unBoarderList.add(item);
        }

        List<StudentWithRoomInfoSortListVO> resultList = boarderList.stream()
                .sorted(Comparator.comparing(StudentWithRoomInfoSortListVO::getCol)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(unBoarderList)) {
            resultList.addAll(unBoarderList);
        }

        return resultList;
    }

    private List<StudentWithClassGradeInfoSortListVO> convert(List<SaaSEduStudentInfoVO> studentList) {
        return studentList.stream().map(item -> {
            AssertUtil.checkNotNull(item.getClassList(),
                    String.format("学生：%s（学生id：%s）没有所属行政班，请检查", item.getId(), item.getStudentName()));
            ClassSimpleVO classInfo = item.getClassList().get(0);
            List<RoomBedVO> roomBedList = item.getRoomBedList();
            StudentWithClassGradeInfoSortListVO studentInfo = new StudentWithClassGradeInfoSortListVO().setStudentId(
                            item.getId()).setStudentName(item.getStudentName()).setClassId(classInfo.getClassId())
                    .setClassName(classInfo.getClassName()).setClassNum(classInfo.getClassNum())
                    .setGradeId(classInfo.getGradeId()).setGradeName(classInfo.getGradeName())
                    .setGradeCode(classInfo.getGradeCode()).setAvatar(item.getFaceImage());
            // 补充寝室信息
            if (CollUtil.isNotEmpty(roomBedList)) {
                RoomBedVO roomBed = roomBedList.get(0);
                studentInfo.setRoomId(roomBed.getRoomId());
                studentInfo.setRoomName(roomBed.getRoomName());
            }

            return studentInfo;
        }).sorted(Comparator.comparing(StudentWithClassGradeInfoSortListVO::getGradeCode)
                .thenComparing(StudentWithClassGradeInfoSortListVO::getClassNum)).collect(Collectors.toList());
    }

    private EduStudentPageQueryDTO convert(QueryStudentListDTO param) {
        EduStudentPageQueryDTO result = new EduStudentPageQueryDTO();
        // saas要求此接口学校id必传
        if (!SaasCurrentIdTypeEnum.SCHOOL.getCode().equals(param.getCurrentIdType())) {
            result.setSchoolId(WebUtil.getSchoolIdLong());
        }
        Long currentId = Convert.toLong(param.getCurrentId());
        switch (param.getCurrentIdType()) {
            case 1:
                result.setSchoolId(currentId);
                break;
            case 2:
                result.setCampusId(currentId);
                break;
            case 3:
                result.setCampusSectionId(currentId);
                break;
            case 4:
                result.setGradeId(currentId);
                break;
            case 5:
                result.setClassId(currentId);
                break;
            case 6:
                result.setBuildingId(currentId);
                break;
            case 7:
                result.setFloorId(currentId);
                break;
            case 8:
                result.setRoomIds(Collections.singletonList(currentId));
                break;
        }

        result.setNameLike(ParamUtil.transform(param.getStudentName()));
        result.setGraduationStatus(Constant.NO.toString());
        result.setClassTypes(param.getClassTypes());
        result.setWithClass(param.getWithClass());
        result.setWithRoom(param.getWithRoom());

        return result;
    }

    /**
     * 查宿舍列表
     *
     * @param param
     * @return
     */
    @Override
    public List<YardRoomVO> queryYardRoomList(YardRoomListDTO param) {
        this.check(param);

        SchoolYardRoomQueryDTO query = this.convert(param);
        query.setWithRoomStudentCount(Boolean.TRUE);
        CommonResult<List<SchoolYardRoomVO>> saasResult = saaSYardAPI.queryYardRoom(query);
        log.info("Saas远程请求【/yard/v1/query_room】-请求参数：【{}】-返回信息：【{}】", JSONUtil.toJsonStr(query),
                JSONUtil.toJsonStr(saasResult));

        if (ObjectUtil.isNull(saasResult) || CollUtil.isEmpty(saasResult.getData())) {
            return Collections.emptyList();
        }

        return saasResult.getData().stream().map(item -> {
            return new YardRoomVO().setRoomId(item.getId()).setRoomName(item.getName())
                    .setBuildingId(item.getBuildingId()).setBuildingName(item.getBuildingName())
                    .setRoomStudentCount(item.getRoomStudentCount()).setFloorId(item.getFloorId())
                    .setFloorName(item.getFloorName()).setCampusId(item.getCampusId());
        }).collect(Collectors.toList());
    }

    /**
     * 查宿舍树形--从楼幢开始展示,通过宿舍名称模糊查询
     *
     * @param param
     * @return
     */
    @Override
    public List<TreeYardVO> querySchoolYardTree(YardTreeDTO param) {
        YardTreeQueryDTO query = this.getParam(WebUtil.getCampusIdLong(), SaasRoomOrgTypeEnum.CAMPUS.getCode(),
                SaasRoomOrgTypeEnum.ROOM.getCode());
        query.setWithRoomStudentCount(Boolean.TRUE);
        CommonResult<List<YardTreeVO>> saasResult = saaSYardAPI.querySchoolYardTree(query);
        log.info("Saas远程请求【/yard/v1/query_tree】-请求参数：【{}】-返回信息：【{}】", JSONUtil.toJsonStr(query),
                JSONUtil.toJsonStr(saasResult));

        if (ObjectUtil.isNull(saasResult) || CollUtil.isEmpty(saasResult.getData())) {
            return Collections.emptyList();
        }
        List<YardTreeVO> saasTree = saasResult.getData();
        if (CollUtil.isEmpty(saasTree)) {
            return Collections.emptyList();
        }
        // 宿舍名称模糊搜索
        List<YardTreeVO> filterTree = this.filterTree(saasTree, query.getEndViewType(), param.getRoomName());
        if (CollUtil.isEmpty(filterTree)) {
            return Collections.emptyList();
        }

        return this.toTreeYardVOList(filterTree);
    }

    /**
     * 查宿舍的楼层（不含宿舍）树形--从楼幢开始展示
     *
     * @return
     */
    @Override
    public List<TreeYardVO> querySchoolYardFloorTree() {
        YardTreeQueryDTO query = this.getParam(WebUtil.getCampusIdLong(), SaasRoomOrgTypeEnum.CAMPUS.getCode(),
                SaasRoomOrgTypeEnum.FLOOR.getCode());
        return this.querySchoolYardTree(query);
    }

    /**
     * 根据宿舍楼层id查宿舍列表
     *
     * @return
     */
    @Override
    public List<TreeYardVO> querySchoolRoomTree(String floorId) {
        AssertUtil.checkNotNull(floorId, "楼层id不能为空");
        List<Integer> skipViewTypeList = Collections.singletonList(SaasRoomOrgTypeEnum.FLOOR.getCode());
        YardTreeQueryDTO query = new YardTreeQueryDTO().setCurrentId(Convert.toLong(floorId))
                .setStartViewType(SaasRoomOrgTypeEnum.FLOOR.getCode())
                .setEndViewType(SaasRoomOrgTypeEnum.ROOM.getCode())
                .setBuildingTypes(SaaSBuildingTypeEnum.queryDormitoryTypeList()).setSkipViewType(skipViewTypeList)
                .setWithRoomStudentCount(Boolean.TRUE);
        List<TreeYardVO> treeYardVOList = this.querySchoolYardTree(query);
        // 补充楼幢楼层名称
        this.buildBuildingFloorName(treeYardVOList);
        return treeYardVOList;
    }

    /**
     * 补充楼幢楼层名称
     */
    private void buildBuildingFloorName(List<TreeYardVO> treeYardVOList) {
        for (TreeYardVO treeYardVO : treeYardVOList) {
            if (ObjectUtil.isNull(treeYardVO.getId())) {
                continue;
            }
            SchoolYardRoomQueryDTO dto = new SchoolYardRoomQueryDTO();
            dto.setRoomId(treeYardVO.getId());
            dto.setSchoolId(WebUtil.getSchoolIdLong());
            dto.setWithRoomStudentCount(false);

            //saas 目前没有批量查询房间信息接口，只能单个通过房间号 id 查询
            Object object = redisUtil.get(
                    RedisKeyConstants.DUTY_BUILDING_FLOOR_ROOM_ID + WebUtil.getSchoolId() + treeYardVO.getId());
            if (ObjectUtil.isNull(object)) {
                CommonResult<List<SchoolYardRoomVO>> listCommonResult = saaSYardAPI.queryYardRoom(dto);
                if (ObjectUtil.isNull(listCommonResult) || CollUtil.isEmpty(listCommonResult.getData())) {
                    log.warn("根据宿舍id查询宿舍信息为空，roomId: {},schoolId:{}", treeYardVO.getId(),
                            WebUtil.getSchoolIdLong());
                    continue;
                }
                Optional<SchoolYardRoomVO> first = listCommonResult.getData().stream().findFirst();
                if (first.isPresent()) {
                    SchoolYardRoomVO schoolYardRoomVO = first.get();
                    redisUtil.set(
                            RedisKeyConstants.DUTY_BUILDING_FLOOR_ROOM_ID + WebUtil.getSchoolId() + treeYardVO.getId(),
                            schoolYardRoomVO, 600);
                    treeYardVO.setBuildingName(schoolYardRoomVO.getBuildingName());
                    treeYardVO.setFloorName(schoolYardRoomVO.getFloorName());
                    treeYardVO.setFullName(CharSequenceUtil.format("{}{}楼{}", schoolYardRoomVO.getBuildingName(),
                            schoolYardRoomVO.getFloorName(), schoolYardRoomVO.getName()));
                }
                continue;
            }
            SchoolYardRoomVO schoolYardRoomVO = BeanUtil.toBean(object, SchoolYardRoomVO.class);
            treeYardVO.setBuildingName(schoolYardRoomVO.getBuildingName());
            treeYardVO.setFloorName(schoolYardRoomVO.getFloorName());
            treeYardVO.setFullName(CharSequenceUtil.format("{}{}楼{}", schoolYardRoomVO.getBuildingName(),
                    schoolYardRoomVO.getFloorName(), schoolYardRoomVO.getName()));

        }
    }

    /**
     * 查宿舍的楼层（不含宿舍）树形--从楼幢开始展示（不包含学校和校区层级）
     *
     * @return
     */
    @Override
    public List<TreeYardVO> querySchoolBuildingFloorTree() {
        YardTreeQueryDTO query = this.getParam(WebUtil.getCampusIdLong(), SaasRoomOrgTypeEnum.CAMPUS.getCode(),
                SaasRoomOrgTypeEnum.FLOOR.getCode());
        return this.querySchoolYardTree(query);
    }

    /**
     * 查宿舍的楼层（不含宿舍）树形--从楼幢开始展示(包含学校和校区层级)
     *
     * @return
     */
    @Override
    public TreeYardVO querySchoolFloorTree() {
        List<Integer> skipViewTypeList = Collections.singletonList(SaasRoomOrgTypeEnum.REGION.getCode());
        YardTreeQueryDTO query = new YardTreeQueryDTO().setCurrentId(WebUtil.getSchoolIdLong())
                .setStartViewType(SaasRoomOrgTypeEnum.SCHOOL.getCode())
                .setEndViewType(SaasRoomOrgTypeEnum.FLOOR.getCode())
                .setBuildingTypes(SaaSBuildingTypeEnum.queryDormitoryTypeList()).setSkipViewType(skipViewTypeList);

        List<TreeYardVO> schoolFloorTree = this.querySchoolYardTree(query);
        if (CollUtil.isEmpty(schoolFloorTree)) {
            return null;
        }

        return this.filter(schoolFloorTree.get(0), WebUtil.getCampusIdLong());
    }


    @Override
    public List<TreeYardVO> queryRegionRoomTreeAll() {
        //场地
        return this.listTreeYardVOS(SaaSBuildingTypeEnum.getAllTypes());
    }

    /**
     * 获取场地树或者宿舍树
     *
     * @param buildingTypes 楼宇类型
     */
    private List<TreeYardVO> listTreeYardVOS(List<Integer> buildingTypes) {
        //参数
        YardTreeQueryDTO query = this.getQueryDTO(buildingTypes);
        //查询场地树或者宿舍树
        List<TreeYardVO> treeYardVOS = this.querySchoolYardTree(query);
        //过滤场地或者宿舍
        return Optional.ofNullable(treeYardVOS)
                .map(trees -> trees.stream().filter(s -> CollUtil.isNotEmpty(s.getChildren()))
                        .collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    // 获取场地/宿舍参数
    private YardTreeQueryDTO getQueryDTO(List<Integer> buildingTypes) {
        List<Integer> skipViewTypeList = Arrays.asList(SaasRoomOrgTypeEnum.SCHOOL.getCode(),
                SaasRoomOrgTypeEnum.CAMPUS.getCode());

        return new YardTreeQueryDTO().setCurrentId(WebUtil.getCampusIdLong())
                .setStartViewType(SaasRoomOrgTypeEnum.CAMPUS.getCode())
                .setEndViewType(SaasRoomOrgTypeEnum.ROOM.getCode()).setBuildingTypes(buildingTypes)
                .setSkipViewType(skipViewTypeList);
    }

    private TreeYardVO filter(TreeYardVO tree, Long campusId) {
        if (ObjectUtil.isNull(tree) || CollUtil.isEmpty(tree.getChildren())) {
            return tree;
        }
        // 过滤出当前校区的信息
        List<TreeYardVO> campusTree = tree.getChildren().stream().filter(item -> item.getId().equals(campusId))
                .collect(Collectors.toList());
        tree.setChildren(campusTree);

        return tree;
    }


    private List<TreeYardVO> querySchoolYardTree(YardTreeQueryDTO query) {
        CommonResult<List<YardTreeVO>> saasResult = saaSYardAPI.querySchoolYardTree(query);
        log.info("Saas远程请求【/yard/v1/query_tree】-请求参数：【{}】-返回信息：【{}】", JSONUtil.toJsonStr(query),
                JSONUtil.toJsonStr(saasResult));
        if (ObjectUtil.isNull(saasResult) || CollUtil.isEmpty(saasResult.getData())) {
            return Collections.emptyList();
        }
        List<YardTreeVO> saasTree = saasResult.getData();

        return this.toTreeYardVOList(saasTree);
    }

    private List<TreeYardVO> toTreeYardVOList(List<YardTreeVO> nodeList) {
        if (CollUtil.isEmpty(nodeList)) {
            return Collections.emptyList();
        }
        return nodeList.stream().map(item -> this.toTreeYardVO(item)).collect(Collectors.toList());
    }

    private TreeYardVO toTreeYardVO(YardTreeVO node) {
        if (ObjectUtil.isNull(node)) {
            return null;
        }
        return new TreeYardVO().setId(node.getId()).setParentId(node.getParentId()).setName(node.getName())
                .setType(node.getViewType()).setChildren(this.toTreeYardVOList(node.getChildren()))
                .setRoomStudentCount(node.getRoomStudentCount());
    }

    /**
     * 按名称模糊搜索叶子节点组织机构
     *
     * @param orgTreeList
     * @param endType
     * @param leafName
     * @return
     */
    private List<YardTreeVO> filterTree(List<YardTreeVO> orgTreeList, Integer endType, String leafName) {
        if (CollUtil.isEmpty(orgTreeList) || StrUtil.isBlank(leafName)) {
            return orgTreeList;
        }
        if (endType.equals(orgTreeList.get(0).getViewType())) {
            return orgTreeList.stream()
                    .filter(item -> StrUtil.isNotBlank(item.getName()) && item.getName().contains(leafName))
                    .collect(Collectors.toList());
        }
        for (YardTreeVO item : orgTreeList) {
            List<YardTreeVO> resultList = this.filterTree(item.getChildren(), endType, leafName);
            item.setChildren(resultList);
        }

        return orgTreeList.stream().filter(item -> CollUtil.isNotEmpty(item.getChildren()))
                .collect(Collectors.toList());
    }


    private YardTreeQueryDTO getParam(Long currentId, Integer startViewType, Integer endViewType) {
        List<Integer> skipViewTypeList = Arrays.asList(SaasRoomOrgTypeEnum.SCHOOL.getCode(),
                SaasRoomOrgTypeEnum.CAMPUS.getCode(), SaasRoomOrgTypeEnum.REGION.getCode());
        return new YardTreeQueryDTO().setCurrentId(currentId).setStartViewType(startViewType)
                .setEndViewType(endViewType).setBuildingTypes(SaaSBuildingTypeEnum.queryDormitoryTypeList())
                .setSkipViewType(skipViewTypeList);


    }

    private void check(YardRoomListDTO param) {
        AssertUtil.checkNotNull(param, "参数不能为空");
        AssertUtil.checkNotBlank(param.getCurrentId(), "currentId不能为空");
        AssertUtil.checkNotNull(param.getCurrentIdType(), "currentIdType不能为空");
        AssertUtil.checkBetween(param.getCurrentIdType(), 1, 6,
                "currentIdType(1：学校id 2：校区id 3：区域id 4：楼幢id 5：楼层id 6：房间id)");
        AssertUtil.checkNotBlank(param.getRoomName(), "roomName不能为空");
    }

    private SchoolYardRoomQueryDTO convert(YardRoomListDTO param) {
        SchoolYardRoomQueryDTO result = new SchoolYardRoomQueryDTO();
        // saas要求此接口学校id必传
        if (!SaasCurrentIdTypeEnum.SCHOOL.getCode().equals(param.getCurrentIdType())) {
            result.setSchoolId(WebUtil.getSchoolIdLong());
        }

        Long currentId = Convert.toLong(param.getCurrentId());
        switch (param.getCurrentIdType()) {
            case 1:
                result.setSchoolId(currentId);
                break;
            case 2:
                result.setCampusId(currentId);
                break;
            case 3:
                result.setRegionId(currentId);
                break;
            case 4:
                result.setBuildingId(currentId);
                break;
            case 5:
                result.setFloorId(currentId);
                break;
            case 6:
                result.setRoomId(currentId);
                break;
        }

        result.setRoomName(ParamUtil.transform(param.getRoomName()));

        return result;
    }

    /**
     * 根据学生id查询学生信息
     *
     * @param studentId
     * @return
     */
    @Override
    public UcStudentClassBffVO listByStudentIds(String studentId) {
        if (CharSequenceUtil.isBlank(studentId)) {
            log.warn("根据学生id查询学生信息，学生 id为空，直接返回");
            return new UcStudentClassBffVO();
        }
        List<Long> studentIdList = Collections.singletonList(Convert.toLong(studentId));
        StudentByIdQuery query = new StudentByIdQuery();
        query.setStudentIds(studentIdList);
        List<UcStudentClassBffVO> list = basicInfoRemote.listByStudentIds(query);
        AssertUtil.checkNotEmpty(list, "saas不存在该学生，请检查");

        return list.get(0);
    }

    public List<StudentClassResponse> listStudentCLassInfo(StudentIdRequest dto) {
        Assert.notNull(dto.getStudentId(), "学生id不能为空");
        StudentByIdQuery query = new StudentByIdQuery();
        query.setStudentIds(CollUtil.newArrayList(Convert.toLong(dto.getStudentId())));
        List<UcStudentClassBffVO> classBffVOS = basicInfoRemote.listByStudentIds(query);
        if (CollUtil.isEmpty(classBffVOS)) {
            return Collections.emptyList();
        }
        return classBffVOS.stream().map(s -> {
            StudentClassResponse studentClassResponse = new StudentClassResponse();
            BeanUtils.copyProperties(s, studentClassResponse);
            return studentClassResponse;
        }).collect(Collectors.toList());
    }

    /**
     * 根据学校查询班级层次列表
     *
     * @param schoolId
     * @return
     */
    @Override
    public List<ClassLevelVO> queryClassLevel(Long schoolId) {
        AssertUtil.checkNotNull(schoolId, "学校id不能为空");
        ClassLevelQuery query = new ClassLevelQuery();
        query.setSchoolId(schoolId);
        CommonResult<List<ClassLevelVO>> saasResult = saaSClassInfoAPI.queryClassLevel(query);
        log.info("Saas远程请求【/classInfo/v1/query_class_level】-请求参数：【{}】-返回信息：【{}】", JSONUtil.toJsonStr(query),
                JSONUtil.toJsonStr(saasResult));

        if (ObjectUtil.isNull(saasResult) || CollUtil.isEmpty(saasResult.getData())) {
            return Collections.emptyList();
        }

        return saasResult.getData();
    }

    /**
     * 查询班级列表(包含历史学年)
     *
     * @return
     */
    @Override
    public List<ClassSimpleVO> queryClassInfoListWithAuth(String sectionCode, String schoolYear) {
        Long schoolId = WebUtil.getSchoolIdLong();
        Long staffId = WebUtil.getStaffIdLong();
        List<String> roleCodeList = this.queryStaffRoleCodeList(schoolId, staffId);
        log.info("queryClassInfoListWithAuth：当前角色：{}", JSONUtil.toJsonStr(roleCodeList));
        Boolean isAdmin = roleLogic.isHaveMaterRole(roleCodeList);
        // 此查询已包含指定学年
        List<ClassSimpleVO> classList = this.queryClassInfoList(sectionCode, schoolYear);
        log.info("queryClassInfoListWithAuth：classList：{}", JSONUtil.toJsonStr(classList));

        if (isAdmin) {
            return classList;
        }

        Boolean ordinaryStaffFlag = roleLogic.isHaveOrdinaryRole(roleCodeList);
        AssertUtil.checkIsTrue(ordinaryStaffFlag, "暂无权限查看学年学段班级列表");

        // 普通角色只能查看权限内班级
        List<String> mineClassIdList = this.queryOrdinaryStaffClassIdList();
        log.info("queryClassInfoListWithAuth：mineClassIdList：{}", JSONUtil.toJsonStr(mineClassIdList));
        if (CollUtil.isEmpty(mineClassIdList)) {
            return Collections.emptyList();
        }
        List<Long> mineClassIdListLong = mineClassIdList.stream().map(item -> Convert.toLong(item))
                .collect(Collectors.toList());
        return classList.stream().filter(item -> mineClassIdListLong.contains(item.getClassId()))
                .collect(Collectors.toList());
    }

    /**
     * 查询班级列表(包含历史学年)(无权限)
     *
     * @return
     */
    @Override
    public List<ClassSimpleVO> querySchoolYearClassNoAuth(String sectionCode, String schoolYear) {
        return this.queryClassInfoList(sectionCode, schoolYear);
    }

    /**
     * 查询班级列表(包含历史学年)
     *
     * @return
     */
    @Override
    public List<ClassSimpleVO> queryClassInfoList(String sectionCode, String schoolYear) {
        AssertUtil.checkNotBlank(sectionCode, "学段不能为空");
        AssertUtil.checkNotBlank(schoolYear, "学年不能为空");
        boolean isCurrentSchoolYear = false;
        // 历史学年的要返回毕业班级 当前学年不返回毕业班级
        List<TermVo> termVos = cacheSaasManager.querySaaSTermList();
        List<TermVo> termVoList = termVos.stream()
                .filter(d -> sectionCode.equals(d.getSectionCode()) && schoolYear.equals(d.getSchoolYear()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(termVoList)) {
            TermVo termVo = termVoList.get(0);
            isCurrentSchoolYear = termVo.getIsCurrentYear();
        }

        ClassHistoryYearQuery query = new ClassHistoryYearQuery().setSchoolId(WebUtil.getSchoolIdLong())
                .setCampusId(WebUtil.getCampusIdLong()).setSectionCode(sectionCode).setSchoolYear(schoolYear)
                .setClassTypes(Collections.singletonList(SaasClassTypeEnum.XINGZHENG.getCode()));
        if (isCurrentSchoolYear) {
            query.setGraduationStatus(Constant.NO.toString());
        }

        List<ClassSimpleVO> classList = this.queryClassInfoList(query);
        if (CollUtil.isEmpty(classList)) {
            return classList;
        }

        return classList.stream()
                .sorted(Comparator.comparing(ClassSimpleVO::getGradeCode).thenComparing(ClassSimpleVO::getClassNum))
                .collect(Collectors.toList());

    }

    /**
     * 根据学段和学年该学段在该学年所有的班级id：班级名称map
     *
     * @return
     */
    @Override
    public Map<String, String> querySchoolYearClassIdNameMap(String sectionCode, String schoolYear) {
        List<ClassSimpleVO> schoolYearClassList = this.queryClassInfoList(sectionCode, schoolYear);
        if (CollUtil.isEmpty(schoolYearClassList)) {
            return Collections.emptyMap();
        }

        return schoolYearClassList.stream().collect(
                Collectors.toMap(item -> Convert.toStr(item.getClassId()), ClassSimpleVO::getClassName, (a, b) -> a));
    }

    /**
     * 根据学段和学年该学段在该学年所有的班级id：班级名称map
     *
     * @return
     */
    @Override
    public Map<String, ClassSimpleVO> querySchoolYearClassIdClassInfoMap(String sectionCode, String schoolYear) {
        List<ClassSimpleVO> schoolYearClassList = this.queryClassInfoList(sectionCode, schoolYear);
        if (CollUtil.isEmpty(schoolYearClassList)) {
            return Collections.emptyMap();
        }

        return schoolYearClassList.stream()
                .collect(Collectors.toMap(item -> Convert.toStr(item.getClassId()), item -> item, (a, b) -> a));
    }

    @Override
    public Map<String, ClassSimpleVO> querySchoolYearClassIdClassInfoMapH5(String sectionCode, String schoolYear,
                                                                           Boolean isCurrentYear) {
        ClassHistoryYearQuery query = new ClassHistoryYearQuery().setSchoolId(WebUtil.getSchoolIdLong())
                .setCampusId(WebUtil.getCampusIdLong()).setSectionCode(sectionCode).setSchoolYear(schoolYear)
                .setClassTypes(Collections.singletonList(SaasClassTypeEnum.XINGZHENG.getCode()));
        if (Objects.nonNull(isCurrentYear) && Boolean.TRUE.equals(isCurrentYear)) {
            query.setGraduationStatus(Constant.NO.toString());
        }
        List<ClassSimpleVO> classList = this.queryClassInfoList(query);
        if (CollUtil.isEmpty(classList)) {
            return new HashMap<>();
        }
        classList = classList.stream()
                .sorted(Comparator.comparing(ClassSimpleVO::getGradeCode).thenComparing(ClassSimpleVO::getClassNum))
                .collect(Collectors.toList());
        return classList.stream()
                .collect(Collectors.toMap(item -> Convert.toStr(item.getClassId()), item -> item, (a, b) -> a));
    }

    /**
     * 查询班级列表(包含历史学年)
     *
     * @param query
     * @return
     */
    private List<ClassSimpleVO> queryClassInfoList(ClassHistoryYearQuery query) {

        CommonResult<List<ClassSimpleVO>> saasResult = saaSClassInfoAPI.queryClassInfoList(query);
        log.info("Saas远程请求【/classInfo/v1/class_history/list】-请求参数：【{}】-返回信息：【{}】",
                JSONUtil.toJsonStr(query), JSONUtil.toJsonStr(saasResult));

        if (ObjectUtil.isNull(saasResult) || CollUtil.isEmpty(saasResult.getData())) {
            return Collections.emptyList();
        }

        return saasResult.getData();
    }

    /**
     * 按班级层次查班级列表
     *
     * @return
     */
    @Override
    public QueryClassLevelListVO queryClassGroupByLevelWithAuth(ClassLevelListWithAuthDTO param) {
        this.check(param);
        Long schoolId = param.getSchoolId();
        Long campusId = param.getCampusId();
        Long campusSectionId = param.getCampusSectionId();
        Long staffId = param.getStaffId();

        QueryClassLevelListVO result = new QueryClassLevelListVO();

        ClassListDTO query = new ClassListDTO().setSchoolId(schoolId).setCampusId(campusId)
                .setCampusSectionId(campusSectionId);
        // 查校区下班级列表
        List<ClassGradeListVO> classList = this.queryClassList(query);
        if (CollUtil.isEmpty(classList)) {
            return result.setShowClassLevelFlag(Boolean.FALSE);
        }

        List<String> roleCodeList = this.queryStaffRoleCodeList(schoolId, staffId);
        // 管理员--展示所有
        if (CollUtil.isNotEmpty(CollUtil.intersection(roleCodeList,
                ModuleCodeEnum.listAdminRoleByCode(ModuleCodeEnum.CLASS_EVALUATE.getCode())))) {
            return this.queryClassGroupByLevel(schoolId, classList);
        }

        // 普通角色--展示权限范围内数据
        Boolean ordinaryStaffFlag = roleLogic.isHaveOrdinaryRole(roleCodeList);
        if (!ordinaryStaffFlag) {
            return result.setShowClassLevelFlag(Boolean.FALSE);
        }
        // 普通角色只能查看权限内班级
        List<String> mineClassIdList = this.queryOrdinaryStaffClassIdList();
        // 权限内无匹配班级则不展示
        if (CollUtil.isEmpty(mineClassIdList)) {
            return result.setShowClassLevelFlag(Boolean.FALSE);
        }
        List<ClassGradeListVO> filterList = classList.stream()
                .filter(item -> mineClassIdList.contains(item.getClassId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(filterList)) {
            return result.setShowClassLevelFlag(Boolean.FALSE);
        }

        return this.queryClassGroupByLevel(schoolId, filterList);
    }

    public void check(ClassLevelListWithAuthDTO param) {
        AssertUtil.checkNotNull(param, "参数不能为空");
        AssertUtil.checkNotNull(param.getSchoolId(), "学校id不能为空");
        AssertUtil.checkNotNull(param.getCampusId(), "校区id不能为空");
        AssertUtil.checkNotNull(param.getCampusSectionId(), "学段id不能为空");
        AssertUtil.checkNotNull(param.getStaffId(), "教职工id不能为空");
    }

    /**
     * 按班级层次查班级列表
     *
     * @param schoolId
     * @return
     */
    @Override
    public QueryClassLevelListVO queryClassGroupByLevel(Long schoolId, List<ClassGradeListVO> classList) {
        AssertUtil.checkNotNull(schoolId, "学校id不能为空");
        QueryClassLevelListVO result = new QueryClassLevelListVO();
        if (CollUtil.isEmpty(classList)) {
            return result.setShowClassLevelFlag(Boolean.FALSE);
        }
        // 查学校下的班级层级
        List<ClassLevelVO> levelList = this.queryClassLevel(schoolId);
        if (CollUtil.isEmpty(levelList)) {
            return result.setShowClassLevelFlag(Boolean.FALSE);
        }
        return this.convert(classList, levelList);
    }

    private QueryClassLevelListVO convert(List<ClassGradeListVO> classList, List<ClassLevelVO> levelList) {
        // 有班级层次的班级
        List<ClassGradeListVO> levelClassList = Lists.newArrayList();
        // 没有班级层次的班级
        List<ClassGradeListVO> noLevelClassList = Lists.newArrayList();
        // -1表示没有班级层次
        Long noLevelId = Convert.toLong(Constant.NEGATIVE_ONE);
        for (ClassGradeListVO classInfo : classList) {
            if (!noLevelId.equals(classInfo.getClassLevelId())) {
                levelClassList.add(classInfo);
                continue;
            }
            noLevelClassList.add(classInfo);
        }
        QueryClassLevelListVO result = new QueryClassLevelListVO();
        // 班级都没有班级层次，不展示
        if (CollUtil.isEmpty(levelClassList)) {
            return result.setShowClassLevelFlag(Boolean.FALSE);
        }
        // 设置没有班级层次的班级
        result.setNoLevelClassList(basicConvert.toClassLevelClassListVOList(noLevelClassList))
                .setShowClassLevelFlag(Boolean.TRUE);
        // key：班级层次id value：班级层次对应的班级
        Map<Long, List<ClassGradeListVO>> levelIdClassListMap = levelClassList.stream()
                .collect(Collectors.groupingBy(ClassGradeListVO::getClassLevelId));
        Map<Long, String> levelIdLevelNameMap = levelList.stream()
                .collect(Collectors.toMap(ClassLevelVO::getId, ClassLevelVO::getClassLevelName, (a, b) -> a));
        // 班级层次按层次id升序
        List<Long> sortLevelIdList = levelIdClassListMap.keySet().stream().sorted().collect(Collectors.toList());

        List<ClassLevelListVO> resultLevelClassList = Lists.newArrayList();
        for (Long levelId : sortLevelIdList) {
            ClassLevelListVO item = new ClassLevelListVO();
            resultLevelClassList.add(item);

            item.setClassLevelId(levelId);
            item.setClassLevelName(levelIdLevelNameMap.get(levelId));
            // 先按年级升序，同年级按班级num升序
            List<ClassGradeListVO> sortLevelClassInfoList = levelIdClassListMap.get(levelId).stream()
                    .sorted(Comparator.comparing(ClassGradeListVO::getGradeCode)
                            .thenComparing(ClassGradeListVO::getClassNum)).collect(Collectors.toList());
            item.setClassList(basicConvert.toClassLevelClassListVOList(sortLevelClassInfoList));
        }
        result.setLevelClassList(resultLevelClassList);

        return result;
    }

    /**
     * 查学段学年列表
     *
     * @return
     */
    @Override
    public List<SectionSchoolYearVo> querySectionSchoolYearList() {
        SectionSchoolYearQuery query = new SectionSchoolYearQuery()
                .setSchoolId(WebUtil.getSchoolIdLong())
                .setCampusId(WebUtil.getCampusIdLong())
                .setWithSchoolYear(Boolean.TRUE)
                .setOnlyWithCurrentSchoolYear(Boolean.FALSE);

        return this.querySectionSchoolYearList(query);
    }

    /**
     * 查学段学年列表
     *
     * @return
     */
    private List<SectionSchoolYearVo> querySectionSchoolYearList(SectionSchoolYearQuery param) {
        TermQuery query = new TermQuery();
        query.setSchoolId(param.getSchoolId());
        query.setCampusId(param.getCampusId());
        List<TermVo> saasResultList = basicInfoRemote.queryTermList(query);
        if (CollUtil.isEmpty(saasResultList)) {
            return Collections.emptyList();
        }

        // 学段列表
        List<SectionSchoolYearVo> resultList = saasResultList.stream().filter(s -> {
                    boolean flag = true;
                    if (StrUtil.isBlank(s.getSectionCode())) {
                        flag = false;
                        log.error("通过saas查学段学年列表异常-返回的sectionCode为空,请求参数:{}-返回参数:{}", param,
                                saasResultList);
                    }
                    return flag;
                }).map(item -> new SectionSchoolYearVo()
                        .setCampusSectionId(item.getCampusSectionId())
                        .setSectionCode(item.getSectionCode())
                        .setSectionName(item.getSectionName()))
                .distinct()
                .sorted(Comparator.comparing(SectionSchoolYearVo::getSectionCode))
                .collect(Collectors.toList());

        if (param.getWithSchoolYear()) {
            this.appendSchoolYearInfo(resultList, saasResultList, param.getOnlyWithCurrentSchoolYear());
        }

        // 根据校区id查询组织树
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentIdType(CAMPUS.getCode());
        eduOrgQueryDTO.setCurrentId(param.getCampusId());
        eduOrgQueryDTO.setEndType(GRADE.getCode());
        eduOrgQueryDTO.setShowViewTypeList(Arrays.asList(3, 4));
        List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        if (CollUtil.isEmpty(eduOrgTreeVOS) || CollUtil.isEmpty(eduOrgTreeVOS.get(0).getChildren())) {
            return resultList;
        }
        //学段
        Map<Long, EduOrgTreeVO> map = eduOrgTreeVOS.get(0).getChildren().stream()
                .collect(Collectors.toMap(EduOrgTreeVO::getId, Function.identity(), (k1, k2) -> k1));
        resultList.forEach(result -> {
            List<SectionGradeVO> list = new ArrayList<>();
            EduOrgTreeVO eduOrgTreeVO = map.get(result.getCampusSectionId());
            List<EduOrgTreeVO> gradeList = eduOrgTreeVO.getChildren();
            gradeList.forEach(grade -> {
                SectionGradeVO sectionGradeVO = new SectionGradeVO();
                sectionGradeVO.setGradeCode(grade.getCode());
                sectionGradeVO.setGradeName(grade.getName());
                sectionGradeVO.setGradeId(grade.getId());
                list.add(sectionGradeVO);
            });
            result.setSectionGradeVOS(list);
        });

        return resultList;
    }

    public List<SectionSchoolYearVo> querySectionSchoolYear(SectionSchoolYearQuery param) {
        TermQuery query = new TermQuery();
        query.setSchoolId(param.getSchoolId());
        query.setCampusId(param.getCampusId());
        query.setCampusSectionId(Convert.toStr(param.getCampusSectionId()));
        List<TermVo> saasResultList = basicInfoRemote.queryTermList(query);
        if (CollUtil.isEmpty(saasResultList)) {
            return Collections.emptyList();
        }

        // 学段列表
        List<SectionSchoolYearVo> resultList = saasResultList.stream()
                .map(item -> new SectionSchoolYearVo().setCampusSectionId(item.getCampusSectionId())
                        .setSectionCode(item.getSectionCode()).setSectionName(item.getSectionName())).distinct()
                .sorted(Comparator.comparing(SectionSchoolYearVo::getSectionCode)).collect(Collectors.toList());

        if (param.getWithSchoolYear()) {
            this.appendSchoolYearInfo(resultList, saasResultList, param.getOnlyWithCurrentSchoolYear());
        }

        return resultList;
    }


    /**
     * 添加学段对应的学年信息
     *
     * @param sectionList
     * @param saasResultList
     * @param onlyWithCurrentSchoolYear
     */
    private void appendSchoolYearInfo(List<SectionSchoolYearVo> sectionList, List<TermVo> saasResultList,
                                      Boolean onlyWithCurrentSchoolYear) {
        // key:学段id value：学段对于学期列表
        Map<Long, List<TermVo>> sectionIdTermListMap = saasResultList.stream()
                .collect(Collectors.groupingBy(TermVo::getCampusSectionId));
        for (SectionSchoolYearVo section : sectionList) {
            List<SchoolYearVo> schoolYearList = Lists.newArrayList();
            List<TermVo> termList = sectionIdTermListMap.get(section.getCampusSectionId());
            // key：学年 value：学年对于学段集合
            Map<String, List<TermVo>> schoolYearTermListMap = termList.stream()
                    .collect(Collectors.groupingBy(TermVo::getSchoolYear));
            for (String schoolYear : schoolYearTermListMap.keySet()) {
                SchoolYearVo item = new SchoolYearVo();
                schoolYearList.add(item);
                // 当前学段，当前学年对应的学期
                List<TermVo> termInfoList = schoolYearTermListMap.get(schoolYear);
                // 取上学期开始时间、下学期结束时间作为该学年的时间
                String minStartTime = termInfoList.stream().min(Comparator.comparing(TermVo::getStartTime))
                        .map(TermVo::getStartTime).get();
                String maxEndTime = termInfoList.stream().max(Comparator.comparing(TermVo::getEndTime))
                        .map(TermVo::getEndTime).get();
                // 上下学期有一个是当前学期则该学年为当前学年
                List<Boolean> currentTermFlagList = termInfoList.stream().map(TermVo::getIsCurrentTerm)
                        .collect(Collectors.toList());
                Boolean currentTermFlag = currentTermFlagList.contains(Boolean.TRUE);

                item.setSchoolYear(schoolYear);
                item.setStartTime(minStartTime);
                item.setEndTime(maxEndTime);
                item.setCurrentSchoolYear(currentTermFlag);
            }
            // 学年倒序排序
            List<SchoolYearVo> sortList = schoolYearList.stream()
                    .sorted(Comparator.comparing(SchoolYearVo::getSchoolYear, Comparator.reverseOrder()))
                    .collect(Collectors.toList());

            // 只展示当前学年
            if (onlyWithCurrentSchoolYear) {
                sortList = sortList.stream().filter(item -> item.isCurrentSchoolYear()).collect(Collectors.toList());
            }
            section.setSchoolYearList(sortList);
        }

    }


    @Override
    public Map<String, SchoolYearVo> querySectionSchoolYearStartTimeEndTimeMap() {
        List<SectionSchoolYearVo> sectionSchoolYearList = this.querySectionSchoolYearList();
        if (CollUtil.isEmpty(sectionSchoolYearList)) {
            return Collections.emptyMap();
        }
        Map<String, SchoolYearVo> resultMap = new HashMap<>();
        for (SectionSchoolYearVo section : sectionSchoolYearList) {
            List<SchoolYearVo> schoolYearList = section.getSchoolYearList();
            for (SchoolYearVo schoolYear : schoolYearList) {
                String key = schoolYear.getSchoolYear() + StrPool.AT + section.getSectionCode();
                resultMap.put(key, schoolYear);
            }
        }

        return resultMap;
    }

    /**
     * 根据员工id查员工角色信息
     *
     * @param schoolId
     * @param staffId
     * @return
     */
    @Override
    public List<ResRoleInfoPojo> queryStaffRoleList(Long schoolId, Long staffId) {
        AssertUtil.checkNotNull(schoolId, "学校id不能为空");
        AssertUtil.checkNotNull(staffId, "教职工id不能为空");
        ResRoleQueryDTO query = new ResRoleQueryDTO().setType(SaasOrgQueryEnum.SCHOOL_ID.getCode()).setId(schoolId)
                .setStaffIds(Collections.singletonList(staffId));
        List<ResStaffRoleVO> resStaffRoleVOList = basicInfoRemote.queryStaffRoleList(query);
        if (CollUtil.isEmpty(resStaffRoleVOList)) {
            return Collections.emptyList();
        }
        return resStaffRoleVOList.get(Constant.ZERO).getRoles();
    }

    /**
     * 根据员工id查员工角色code列表
     *
     * @param schoolId
     * @param staffId
     * @return
     */
    @Override
    public List<String> queryStaffRoleCodeList(Long schoolId, Long staffId) {
        List<ResRoleInfoPojo> roleList = this.queryStaffRoleList(schoolId, staffId);
        if (CollUtil.isEmpty(roleList)) {
            return Collections.emptyList();
        }
        return roleList.stream().map(ResRoleInfoPojo::getRoleCode).collect(Collectors.toList());
    }

    @Override
    public Boolean isHistory(Long campusSectionId, String schoolYear, Date checkTime) {
        TermVo termVo = termLogic.getTermVoByCampusIdCache(WebUtil.getSchoolIdLong(), WebUtil.getCampusIdLong(),
                campusSectionId, checkTime);
        return termVo != null && !Objects.equals(Boolean.TRUE, termVo.getIsCurrentYear());
    }

    /**
     * 获取学期时间
     *
     * @return
     */
    @Override
    public TermVo getCurrentTermByCampusSectionId(String campusSectionId) {
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
        termQuery.setCampusId(Convert.toLong(WebUtil.getCampusId()));
        termQuery.setCampusSectionId(campusSectionId);
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        if (CollectionUtil.isEmpty(termVos)) {
            log.info("【从saas获取学期时间】 返回结果空：【{}】", JSONUtil.toJsonStr(termVos));
            return null;
        }
        //有当前学期返回当前学期
        List<TermVo> currentTerm = termVos.stream().filter(s -> s.isCurrentTerm()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(currentTerm)) {
            return currentTerm.get(Constant.ZERO);
        }
        //无当前学期，返回上一个最近的学期 + 当前日期
        List<TermVo> termVoList = termVos.stream().filter(s -> DateUtil.parse(s.getEndTime()).before(DateUtil.date()))
                .collect(Collectors.toList());
        TermVo termVo = termVoList.stream().max(Comparator.comparing(s -> DateUtil.parse(s.getEndTime()))).get();
        termVo.setEndTime(DateUtil.today());

        return termVo;
    }

    /**
     * 查当前登录人以普通角色所负责的班级Id列表（年级主任、班主任、任课老师）
     */
    @Override
    public List<String> queryOrdinaryStaffClassIdList() {
        //登录人能查看的年级列表
        List<Long> gradeIdList = new ArrayList<>();
        //员工授课班级集合
        List<Long> classIdList = new ArrayList<>();
        //员工担任班主任的班级集合
        List<Long> masterClassIdList = new ArrayList<>();
        List<EduStaffClassVO> eduStaffClassVOList = listEduStaffClass();
        if (Objects.nonNull(eduStaffClassVOList) && !eduStaffClassVOList.isEmpty()) {
            EduStaffClassVO eduStaffClassVO = eduStaffClassVOList.get(0);
            gradeIdList = eduStaffClassVO.getLeaderGradeInfos().stream().map(EduStaffGradeVO::getGradeId)
                    .collect(Collectors.toList());
            classIdList = eduStaffClassVO.getTeachClassInfos().stream().map(EduStaffTeachClassVO::getClassId)
                    .collect(Collectors.toList());
            masterClassIdList = eduStaffClassVO.getLeaderClassInfos().stream().map(EduStaffMasterClassVO::getClassId)
                    .collect(Collectors.toList());
        }
        log.info("登录人能查看的年级列表，{}", JSONUtil.toJsonStr(gradeIdList));
        log.info("员工授课班级集合，{}", JSONUtil.toJsonStr(classIdList));
        log.info("员工担任班主任的班级集合，{}", JSONUtil.toJsonStr(masterClassIdList));
        classIdList.addAll(masterClassIdList);
        classIdList.addAll(listClassIdByGradeIdList(gradeIdList, classIdList));

        return classIdList.stream().distinct().map(Convert::toStr).collect(Collectors.toList());
    }

    @Override
    public List<TermVo> queryTermList(String campusSectionId) {
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
        termQuery.setCampusId(Convert.toLong(WebUtil.getCampusId()));
        termQuery.setCampusSectionId(campusSectionId);
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        Assert.notEmpty(termVos, () -> new BizException("当前学段未配置本学期信息，请联系管理员！"));
        return termVos;
    }

    @Override
    public TermVo getTime(String schoolId, String campusId, String campusSectionId) {
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(Convert.toLong(schoolId));
        termQuery.setCampusId(Convert.toLong(campusId));
        termQuery.setCampusSectionId(campusSectionId);
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        if (CollectionUtil.isEmpty(termVos)) {
            log.info("【从saas获取学期时间】 返回结果空：【{}】", JSONUtil.toJsonStr(termVos));
            return null;
        }
        //有当前学期返回当前学期
        List<TermVo> currentTerm = termVos.stream().filter(TermVo::isCurrentTerm).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(currentTerm)) {
            return currentTerm.get(Constant.ZERO);
        }
        //无当前学期，返回上一个最近的学期 + 当前日期
        List<TermVo> termVoList = termVos.stream().filter(s -> DateUtil.parse(s.getEndTime()).before(DateUtil.date()))
                .collect(Collectors.toList());
        TermVo termVo = termVoList.stream().max(Comparator.comparing(s -> DateUtil.parse(s.getEndTime()))).get();
        termVo.setEndTime(DateUtil.today());
        return termVo;
    }

    @Override
    public Map<Long/*学段ID*/, SchoolYearVo/*学年*/> getLastSchoolYear() {
        TermQuery query = new TermQuery();
        query.setSchoolId(WebUtil.getSchoolIdLong());
        query.setCampusId(WebUtil.getCampusIdLong());
        List<TermVo> saasResultList = basicInfoRemote.queryTermList(query);
        if (CollUtil.isEmpty(saasResultList)) {
            return Collections.emptyMap();
        }
        Map<Long/*学段ID*/, List<TermVo>/*学期列表*/> sectionIdTermListMap = saasResultList.stream()
                .collect(Collectors.groupingBy(TermVo::getCampusSectionId));

        Map<Long/*学段ID*/, SchoolYearVo/*学年*/> schoolYearMap = new HashMap<>();
        for (Map.Entry<Long, List<TermVo>> entry : sectionIdTermListMap.entrySet()) {
            List<SchoolYearVo> schoolYearList = Lists.newArrayList();
            List<TermVo> termList = entry.getValue();
            if (CollUtil.isEmpty(termList)) {
                continue;
            }
            Map<String/*学年*/, List<TermVo>/*学段*/> schoolYearTermListMap = termList.stream()
                    .collect(Collectors.groupingBy(TermVo::getSchoolYear));
            for (Map.Entry<String, List<TermVo>> entryTerm : schoolYearTermListMap.entrySet()) {
                SchoolYearVo item = new SchoolYearVo();
                schoolYearList.add(item);
                // 当前学段，当前学年对应的学期
                List<TermVo> termInfoList = entryTerm.getValue();
                if (CollUtil.isEmpty(termInfoList)) {
                    continue;
                }
                // 取上学期开始时间、下学期结束时间作为该学年的时间
                Optional<String> minStartTime = termInfoList.stream().min(Comparator.comparing(TermVo::getStartTime))
                        .map(TermVo::getStartTime);
                Optional<String> maxEndTime = termInfoList.stream().max(Comparator.comparing(TermVo::getEndTime))
                        .map(TermVo::getEndTime);
                boolean issCurrentYear = termInfoList.stream().anyMatch(TermVo::getIsCurrentYear);
                item.setSchoolYear(entryTerm.getKey());
                item.setStartTime(minStartTime.orElse(""));
                item.setEndTime(maxEndTime.orElse(""));
                item.setCurrentSchoolYear(issCurrentYear);
            }
            // 学年倒序排序
            List<SchoolYearVo> sortList = schoolYearList.stream()
                    .sorted(Comparator.comparing(SchoolYearVo::getSchoolYear, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            Iterator<SchoolYearVo> iterator = sortList.iterator();
            boolean flag = false;
            while (iterator.hasNext()) {
                SchoolYearVo next = iterator.next();
                if (flag) {
                    schoolYearMap.put(entry.getKey(), next);
                    break;
                }
                flag = next.isCurrentSchoolYear();
            }
        }
        return schoolYearMap;
    }

    /**
     * 带优化的查询学生列表（不包含已经毕业的学生）
     */
    @Override
    public List<StudentEduInfoVO> pageClassStudent(PageEduStudentQueryDTO dto) {
        log.info("【查询学生列表】====入参：【{}】", JSONUtil.toJsonStr(dto));
        //入参校验
        Assert.isFalse(
                ObjectUtil.isNull(dto.getTargetId()) || ObjectUtil.isNull(dto.getTargetType()) || ObjectUtil.isNull(
                        dto.getSortFlag()) || ObjectUtil.isNull(dto.getTimeCode()),
                () -> new BizException(BizExceptionEnum.PARAMETER_ERROR.getMessage()));
        log.info("【BasicInfoService层saas服务调用】====接口11：获取学生列表");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<EduStudentInfoVO> eduStudentInfoVOList = basicInfoRemote.queryStudentPage(dto);
        stopWatch.stop();
        log.warn("【学生列表】-【step.1.1】-【获取学生列表】，消耗时长：【{}】", stopWatch.getLastTaskTimeMillis());
        if (eduStudentInfoVOList.isEmpty()) {
            log.warn("【查询学生列表】-【获取学生列表】，查询为空，直接返回");
            return Collections.emptyList();
        }
        List<Long> studentIdList = eduStudentInfoVOList.stream().map(EduStudentInfoVO::getId)
                .collect(Collectors.toList());
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        eduStudentClassQueryDTO.setStudentIds(studentIdList);
        eduStudentClassQueryDTO.setGraduationStatus(Constant.NO.toString());
        List<EduStudentClassVO> eduStudentClassVOList = basicInfoRemote.queryClassListByStudentIds(
                eduStudentClassQueryDTO);
        if (eduStudentInfoVOList.isEmpty()) {
            log.warn("【查询学生列表】-【获取学生班级列表列表】，查询为空，直接返回");
            return Collections.emptyList();
        }
        Map<Long, List<EduClassBaseInfoPojo>> idClassMap = eduStudentClassVOList.stream()
                .collect(Collectors.toMap(EduStudentClassVO::getStudentId, EduStudentClassVO::getClassBaseInfos));

        List<EduStudentInfoVO> studentInfoVOS = new ArrayList<>();
        for (EduStudentInfoVO eduStudentInfoVO : eduStudentInfoVOList) {
            List<EduClassBaseInfoPojo> eduClassBaseInfoPojoList = idClassMap.get(eduStudentInfoVO.getId());
            if (Objects.nonNull(eduClassBaseInfoPojoList) && !eduClassBaseInfoPojoList.isEmpty()) {
                if (Constant.ONE.equals(eduClassBaseInfoPojoList.size())) {
                    eduStudentInfoVO.setClassId(eduClassBaseInfoPojoList.get(0).getId());
                    eduStudentInfoVO.setClassName(eduClassBaseInfoPojoList.get(0).getClassName());
                } else {
                    if (ObjectUtil.isNotNull(dto.getClassId())) {
                        List<EduClassBaseInfoPojo> infoPojos = eduClassBaseInfoPojoList.stream()
                                .filter(s -> dto.getClassId().equals(s.getId())).collect(Collectors.toList());
                        eduStudentInfoVO.setClassId(infoPojos.get(0).getId());
                        eduStudentInfoVO.setClassName(infoPojos.get(0).getClassName());
                    } else {
                        for (int i = 0; i < eduClassBaseInfoPojoList.size(); i++) {
                            if (0 == i) {
                                eduStudentInfoVO.setClassId(eduClassBaseInfoPojoList.get(0).getId());
                                eduStudentInfoVO.setClassName(eduClassBaseInfoPojoList.get(0).getClassName());
                            } else {
                                EduStudentInfoVO eduStudentInfo = new EduStudentInfoVO();
                                BeanUtil.copyProperties(eduStudentInfoVO, eduStudentInfo);
                                eduStudentInfo.setClassId(eduClassBaseInfoPojoList.get(i).getId());
                                eduStudentInfo.setClassName(eduClassBaseInfoPojoList.get(i).getClassName());
                                eduStudentInfo.setClassBaseInfos(idClassMap.get(eduStudentInfoVO.getId()));
                                studentInfoVOS.add(eduStudentInfo);
                            }
                        }
                    }
                }

            }
            eduStudentInfoVO.setClassBaseInfos(idClassMap.get(eduStudentInfoVO.getId()));
        }
        eduStudentInfoVOList.addAll(studentInfoVOS);
        log.info("查询学生，过滤数据权限之前的数据{}", JSONUtil.toJsonStr(eduStudentInfoVOList));
        //根据角色过滤数据
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = listOrgIdAndRoleId();
        List<String> roleCodeList;
        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())
                && !listOrgIdAndRoleIdVO.getOrgIdList().isEmpty()) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            log.info("当前登录人没有配置角色，返回空值");
            return Collections.emptyList();
        }
        log.info("当前登录人具有的角色{}", JSONUtil.toJsonStr(roleCodeList));
        //saas默认角色code未配置，暂时先采用名称匹配，若saas后续配置，更换为code
        if (roleLogic.isHaveMaterRole(roleCodeList)) {
            //校长、德育处主任、教务主任 返回全部数据
            log.info("查询学生接口，无需过滤数据权限，直接返回saas结果，返回结果{}",
                    JSONUtil.toJsonStr(eduStudentInfoVOList));
            return listStudentEduInfoVOS(dto, eduStudentInfoVOList);
        }

        if (roleLogic.isHaveOrdinaryRole(roleCodeList)) {
            //年级主任、班主任、学科老师需要过滤数据权限
            List<Long> listAllAuthClass = listAllAuthAllClass();
            eduStudentInfoVOList = eduStudentInfoVOList.stream()
                    .filter(eduStudentInfoVO -> listAllAuthClass.contains(eduStudentInfoVO.getClassId()))
                    .collect(Collectors.toList());
            log.info("查询学生，过滤数据权限之后的数据{}", JSONUtil.toJsonStr(eduStudentInfoVOList));
            return listStudentEduInfoVOS(dto, eduStudentInfoVOList);
        }
        log.info("当前登录人不在指定角色中，返回空");
        return Collections.emptyList();
    }

    @Override
    public List<EduStudentInfoVO> pageClassStudentV2(PageEduStudentQueryDTO dto) {
        List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoRemote.queryStudentPage(dto);
        if (CollUtil.isNotEmpty(eduStudentInfoVOS)){
            eduStudentInfoVOS.forEach(s->{
                s.setClassId(dto.getClassId());
                s.setSchoolId(dto.getSchoolId());
            });
        }
        return eduStudentInfoVOS;
    }

    private List<StudentEduInfoVO> listStudentEduInfoVOS(PageEduStudentQueryDTO dto,
                                                         List<EduStudentInfoVO> eduStudentInfoVOList) {
        //入参校验
        Assert.isFalse(
                ObjectUtil.isNull(dto.getTargetId()) || ObjectUtil.isNull(dto.getTargetType()) || ObjectUtil.isNull(
                        dto.getSortFlag()) || ObjectUtil.isNull(dto.getTimeCode()),
                () -> new BizException(BizExceptionEnum.PARAMETER_ERROR.getMessage()));
        List<StudentEduInfoVO> studentEduInfoVOS = basicConvert.toStudentEduInfoVOS(eduStudentInfoVOList);
        //获取不同的时间
        TermVo timePro = getTimePro(dto);
        if (BeanUtil.isEmpty(timePro)) {
            log.info("【从saas获取学期时间】 返回结果空：【{}】", JSONUtil.toJsonStr(timePro));
            return studentEduInfoVOS;
        }
        //行政班 id
        List<EduClassBaseInfoPojo> infoPojos = eduStudentInfoVOList.stream().map(EduStudentInfoVO::getClassBaseInfos)
                .collect(Collectors.toList()).stream().flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> classIds = infoPojos.stream()
                .filter(s -> SaasClassTypeEnum.XINGZHENG.getCode().equals(s.getClassType()))
                .map(EduClassBaseInfoPojo::getId).distinct().collect(Collectors.toList());
        BehaviourStudentFileQueryDTO behaviourStudentFileQueryDTO = new BehaviourStudentFileQueryDTO();
        behaviourStudentFileQueryDTO.setClassIds(CollUtil.isEmpty(classIds) ? new ArrayList<>() : classIds);
        behaviourStudentFileQueryDTO.setStartTime(timePro.getStartTime());
        behaviourStudentFileQueryDTO.setEndTime(timePro.getEndTime());
        if (Constant.TWO.equals(dto.getTargetType())) {
            behaviourStudentFileQueryDTO.setTargetId(dto.getTargetId());
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //获取总积分或者改指标分数
        List<BehaviourStudentFileVO> behaviourStudentFileVOS = behaviourRecordService.listStudentScores(
                behaviourStudentFileQueryDTO, eduStudentInfoVOList);
        stopWatch.stop();
        log.info("【带优化的查询学生列表】：获取总积分或者改指标分数,耗时：【{}】", stopWatch.getLastTaskTimeMillis());

        // 获取请假状态
        List<Long> studentIds = studentEduInfoVOS.stream().map(EduStudentInfoVO::getId).distinct()
                .collect(Collectors.toList());
        Map<Long, Integer> leaveStatusFlagMap = this.getLeaveStatusFlagMap(studentIds);

        studentEduInfoVOS.forEach(s -> {
            Optional<BehaviourStudentFileVO> first = behaviourStudentFileVOS.stream()
                    .filter(m -> Convert.toStr(s.getId()).equals(m.getStudentId())).findFirst();
            s.setSumScore(first.map(BehaviourStudentFileVO::getSumScore).orElse(null));
            s.setAddScore(first.map(BehaviourStudentFileVO::getAddScore).orElse(null));
            s.setSubtractScore(first.map(BehaviourStudentFileVO::getSubtractScore).orElse(null));
            // 设置请假状态
            s.setLeaveStatusFlag(leaveStatusFlagMap.getOrDefault(s.getId(), Constant.NO));
        });
        if (Constant.ONE.equals(dto.getSortFlag())) {
            return studentEduInfoVOS.stream().sorted(Comparator.comparing(StudentEduInfoVO::getSumScore,
                    Comparator.nullsFirst(BigDecimal::compareTo))).collect(Collectors.toList());
        } else if (Constant.TWO.equals(dto.getSortFlag())) {
            return studentEduInfoVOS.stream().sorted(Comparator.comparing(StudentEduInfoVO::getSumScore,
                    Comparator.nullsFirst(BigDecimal::compareTo)).reversed()).collect(Collectors.toList());
        } else {
            return studentEduInfoVOS.stream().sorted(Comparator.comparing(StudentEduInfoVO::getStudentNo))
                    .collect(Collectors.toList());
        }
    }


    /**
     * 根据学生id集合获取请假状态
     * <p>
     * 是否请假 1:是 0:否
     *
     * @param studentIds
     * @return
     */
    public Map<Long, Integer> getLeaveStatusFlagMap(List<Long> studentIds) {
        if (CollUtil.isEmpty(studentIds)) {
            return new HashMap<>();
        }
        HaiStudentIdRequest request = new HaiStudentIdRequest();
        request.setSaasStudentIds(studentIds);

        List<StudentLeaveStatusResponse> studentLeaveStatusResponses = haiInfoRemote.listStudentLeaveStatus(request);
        List<Long> leaveStudentIds = studentLeaveStatusResponses.stream()
                .filter(s -> ObjectUtil.equal(s.getLeaveStatus(), 5))
                .map(StudentLeaveStatusResponse::getStudentId).distinct()
                .collect(Collectors.toList());
        Map<Long, Integer> resultMap = new HashMap<>();
        for (Long saasStudentId : request.getSaasStudentIds()) {
            if (leaveStudentIds.contains(saasStudentId)) {
                resultMap.put(saasStudentId, 1);
            } else {
                resultMap.put(saasStudentId, 0);
            }
        }
        return resultMap;
    }

    @Override
    public StudentScoreDetailResponse getStudentScoreDetailInfo(StudentScoreQuery query) {
        AssertUtil.checkNotNull(query.getStudentId(), "学生id不能为空");
        AssertUtil.checkNotNull(query.getTimeCode(), "时间类型不能为空");

        // 查询海思谷学生信息
        EduStudentInfoVO eduStudentInfoVO = this.getSaasStudentInfo(query);

        // 获取查询时间范围
        TermVo timePro = this.getQueryTime(query);
        if (BeanUtil.isEmpty(timePro)) {
            log.info("【获取学期时间】 查询参数：【{}】", JSONUtil.toJsonStr(query));
            return new StudentScoreDetailResponse();
        }

        // 查询行为记录
        List<BehaviourStudentFileVO> behaviours = this.listBehaviourRecord(timePro, eduStudentInfoVO);
        if (CollUtil.isEmpty(behaviours)) {
            log.info("【获取学生行为记录】 查询参数：【{}】", JSONUtil.toJsonStr(query));
            return new StudentScoreDetailResponse();
        }

        // 组装返回结果
        return this.assemblyStudentScoreDetailResponse(behaviours, eduStudentInfoVO);
    }

    private EduStudentInfoVO getSaasStudentInfo(StudentScoreQuery query) {
        EduStudentPageQueryDTO dto = new EduStudentPageQueryDTO();
        dto.setSchoolId(WebUtil.getSchoolIdLong());
        dto.setCampusId(WebUtil.getCampusIdLong());
        dto.setCampusSectionId(query.getCampusSectionId());
        dto.setStudentIds(CollUtil.newArrayList(query.getStudentId()));
        List<EduStudentInfoVO> eduStudentInfoVOList = basicInfoRemote.queryStudentPageAll(dto);
        AssertUtil.checkNotEmpty(eduStudentInfoVOList, "学生信息为空");
        EduStudentInfoVO eduStudentInfoVO = CollUtil.getFirst(eduStudentInfoVOList);
        // 获取学生班级信息
        List<UcStudentClassBffVO> saasStudents = basicInfoRemote.listByStudentIds(
                new StudentByIdQuery().setStudentIds(CollUtil.newArrayList(query.getStudentId())));
        AssertUtil.checkNotEmpty(saasStudents, "学生信息为空");
        UcStudentClassBffVO ucStudentClassBffVO = CollUtil.getFirst(saasStudents);

        // 填充班级信息
        eduStudentInfoVO.setClassId(ucStudentClassBffVO.getClassId());
        eduStudentInfoVO.setClassName(ucStudentClassBffVO.getClassName());
        return eduStudentInfoVO;
    }

    private TermVo getQueryTime(StudentScoreQuery query) {
        // 获取不同的时间
        PageEduStudentQueryDTO queryDTO = new PageEduStudentQueryDTO();
        queryDTO.setTimeCode(query.getTimeCode());
        queryDTO.setCampusSectionId(query.getCampusSectionId());
        return getTimePro(queryDTO);
    }

    private List<BehaviourStudentFileVO> listBehaviourRecord(TermVo timePro, EduStudentInfoVO eduStudentInfoVO) {
        BehaviourStudentFileQueryDTO behaviourStudentFileQueryDTO = new BehaviourStudentFileQueryDTO();
        behaviourStudentFileQueryDTO.setClassIds(CollUtil.newArrayList(eduStudentInfoVO.getClassId()));
        behaviourStudentFileQueryDTO.setStartTime(timePro.getStartTime());
        behaviourStudentFileQueryDTO.setEndTime(timePro.getEndTime());
        // 获取总积分或者改指标分数
        return behaviourRecordService.listStudentScores(
                behaviourStudentFileQueryDTO, CollUtil.newArrayList(eduStudentInfoVO));
    }

    private StudentScoreDetailResponse assemblyStudentScoreDetailResponse(List<BehaviourStudentFileVO> behaviours,
                                                                          EduStudentInfoVO eduStudentInfoVO) {
        BehaviourStudentFileVO studentFileVO = CollUtil.getFirst(behaviours);
        StudentScoreDetailResponse response = new StudentScoreDetailResponse();
        response.setStudentId(studentFileVO.getStudentId());
        response.setStudentName(studentFileVO.getStudentName());
        response.setStudentNo(eduStudentInfoVO.getStudentNo());
        response.setClassId(Convert.toStr(eduStudentInfoVO.getClassId()));
        response.setClassName(eduStudentInfoVO.getClassName());
        response.setSumScore(studentFileVO.getSumScore());
        response.setAddScore(studentFileVO.getAddScore());
        response.setSubtractScore(studentFileVO.getSubtractScore());
        return response;
    }

    @Override
    public TermVo getTimePro(PageEduStudentQueryDTO dto) {
        TermVo termVo = new TermVo();
        if (ExamTimeTypeEnum.NEARLY_TWO_WEEKS.getCode().equals(dto.getTimeCode())) {
            termVo.setEndTime(DateUtil.format(DateUtil.date(), Constant.PATTERN_FORMAT));
            termVo.setStartTime(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -14), Constant.PATTERN_FORMAT));
        } else if (ExamTimeTypeEnum.NEARLY_ONE_MONTH.getCode().equals(dto.getTimeCode())) {
            termVo.setEndTime(DateUtil.format(DateUtil.date(), Constant.PATTERN_FORMAT));
            termVo.setStartTime(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -30), Constant.PATTERN_FORMAT));
        } else if (ExamTimeTypeEnum.NEARLY_TWO_MONTH.getCode().equals(dto.getTimeCode())) {
            termVo.setEndTime(DateUtil.format(DateUtil.date(), Constant.PATTERN_FORMAT));
            termVo.setStartTime(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -60), Constant.PATTERN_FORMAT));
        } else if (ExamTimeTypeEnum.NEARLY_FOUR_MONTH.getCode().equals(dto.getTimeCode())) {
            termVo.setEndTime(DateUtil.format(DateUtil.date(), Constant.PATTERN_FORMAT));
            termVo.setStartTime(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -120), Constant.PATTERN_FORMAT));
        } else if (ExamTimeTypeEnum.THIS_TERM.getCode().equals(dto.getTimeCode())) {
            TermVo time = getTime(WebUtil.getSchoolId(), WebUtil.getCampusId(),
                    Convert.toStr(dto.getCampusSectionId()));
            if (BeanUtil.isEmpty(time)) {
                return termVo;
            }
            termVo.setStartTime(time.getStartTime());
            termVo.setEndTime(time.getEndTime());
        }
        return termVo;
    }

    private EduClassQueryDTO getEduClassQueryDTO(EduStudentPageQueryDTO eduStudentPageQueryDTO) {
        EduClassQueryDTO query = new EduClassQueryDTO();
        query.setSchoolId(eduStudentPageQueryDTO.getSchoolId());
        query.setCampusId(eduStudentPageQueryDTO.getCampusId());
        query.setCampusSectionId(eduStudentPageQueryDTO.getCampusSectionId());
        query.setGraduationStatus(Constant.NO.toString());
        query.setClassTypes(Lists.newArrayList(SaasClassTypeEnum.XINGZHENG.getCode()));
        return query;
    }


    @Override
    public List<SaasStudentVO> listStudentsByParent(StudentByParentQueryDTO queryDTO) {
        log.info("【家长端首页切换学生】-请求参数：【{}】", JSONUtil.toJsonStr(queryDTO));
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.start("【家长端首页切换学生-总】");

        List<SaasStudentVO> studentVOS = basicInfoRemote.userStudentList(queryDTO);
        if (CollUtil.isEmpty(studentVOS)) {
            log.info("【家长端首页切换学生】-studentVOS为空，直接返回");
            return Collections.emptyList();
        }

        // 根据返回值的classList设置下classId（取行政班id）
        for (SaasStudentVO d : studentVOS) {
            List<ClassFullInfoVO> classList = d.getClassList();
            if (CollUtil.isEmpty(classList)) {
                continue;
            }
            classList.stream().forEach(s -> {
                if (SaasClassTypeEnum.XINGZHENG.getCode().equals(Convert.toStr(s.getClassType()))) {
                    d.setClassId(s.getClassId());
                }
            });
        }
        if (StringUtils.isBlank(WebUtil.getTenantId())) {
            return studentVOS;
        }
        log.info("【家长端首页切换学生】<<<<<<<<<<<<<<<<<<<,消耗时长:【{}】",
                timeInterval.interval("【家长端首页切换学生-总】"));
        String tenantId = WebUtil.getTenantId();
        return studentVOS.stream().filter(s -> tenantId.equals(s.getTenantId().toString()))
                .collect(Collectors.toList());
    }

    /**
     * 根据宿舍查询学生
     */
    @Override
    public List<StudentInfo1VO> listStudentByRoomId(IdRequest request) {
        if (BeanUtil.isEmpty(request) || ObjectUtil.isNull(request.getId())) {
            log.warn("根据宿舍查询学生-请求参数为空");
            return Collections.emptyList();
        }

        StudentPageQueryDTO dto = new StudentPageQueryDTO();
        dto.setSchoolId(WebUtil.getSchoolIdLong());
        dto.setRoomIds(Lists.newArrayList(request.getId()));
        dto.setStudentStatus("0");
        dto.setGraduationStatus("0");
        return saasStudentManager.queryStudentPage(dto);
    }

    @Override
    public Boolean checkMenuAuth(MenuAuthCheckDTO menuAuthCheckDTO) {
        if (Objects.isNull(menuAuthCheckDTO.getUserId())) {
            throw new BizException("请选择要校验的用户");
        }
        if (Objects.isNull(menuAuthCheckDTO.getTenantId())) {
            throw new BizException("tenantId不能为空");
        }
        if (Objects.isNull(menuAuthCheckDTO.getMenuId()) && StringUtils.isBlank(menuAuthCheckDTO.getAuthCode())) {
            throw new BizException("请传入要校验的菜单或按钮");
        }
        return basicInfoRemote.menuAuthCheck(menuAuthCheckDTO).getHasAuth();
    }

    @Override
    public UserInfoVO getUserInfoFromToken(String token) {
        if (StringUtils.isBlank(token)) {
            throw new BizException("token不能为空");
        }
        UserInfoByTokenDTO userInfoByTokenDTO = new UserInfoByTokenDTO();
        userInfoByTokenDTO.setToken(token);
        return basicInfoRemote.getUserInfoFromToken(userInfoByTokenDTO);
    }

    @Override
    public List<Long> listTeachingClass() {

        TchClassStaffRequest tchClassStaffRequest = new TchClassStaffRequest();
        tchClassStaffRequest.setStaffIdList(CollUtil.newArrayList(WebUtil.getStaffIdLong()));
        Map<Long, List<TchClassVO>> classInfoMap = basicInfoRemote.queryAllClassByStaffIdList(tchClassStaffRequest);
        List<TchClassVO> tchClassInfos = classInfoMap.get(WebUtil.getStaffIdLong());

        if (CollUtil.isEmpty(tchClassInfos)) {
            return Collections.emptyList();
        }

        return tchClassInfos.stream()
                .filter(s -> Boolean.FALSE.equals(s.getIsDeleted()))
                .map(TchClassVO::getId).collect(Collectors.toList());
    }

}
