package com.hailiang.service;

import com.hailiang.model.request.speed.ClassQueryRequest;
import com.hailiang.model.request.speed.GroupSortRequest;
import com.hailiang.model.request.speed.SpeedConfigQueryRequest;
import com.hailiang.model.request.speed.SpeedConfigRequest;
import com.hailiang.model.response.speed.SpeedConfigResponse;
import com.hailiang.model.response.speed.StudentResponse;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;

import java.util.List;

/**
 * 极速点评配置业务层
 *
 * @Description: 极速点评配置业务层
 * @Author: gaoxin
 * @Date: Created in 2024/1/25
 * @Version: 1.6.0
 */
public interface SpeedConfigService {

    /**
     * 修改点评配置
     */
    void saveOrUpdate(SpeedConfigRequest request);

    /**
     * 根据配置ID修改指标分组顺序
     */
    void updateGroupOrder(GroupSortRequest request);

    /**
     * 获取班级列表
     */
    List<EduOrgTreeVO> listClass(ClassQueryRequest request);

    /**
     * 根据班级ID查询学生列表
     */
    List<StudentResponse> listStudent(SpeedConfigQueryRequest request);

    /**
     * 根据教职工ID和校区ID查询点评配置和指标分组
     */
    SpeedConfigResponse getOneByCampusIdAndStaffId(String campusId, String staffId);
}
