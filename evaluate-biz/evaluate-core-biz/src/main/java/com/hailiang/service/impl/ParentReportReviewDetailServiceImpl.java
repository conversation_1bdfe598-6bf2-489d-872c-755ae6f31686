package com.hailiang.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hailiang.constant.Constant;
import com.hailiang.convert.ReportReviewDetailConvert;
import com.hailiang.enums.*;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.logic.ReportReviewDetailLogic;
import com.hailiang.logic.TermLogic;
import com.hailiang.mapper.ReportReviewDetailMapper;
import com.hailiang.model.dto.query.*;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.MessageLog;
import com.hailiang.model.entity.ReportReviewDetail;
import com.hailiang.model.entity.StudentAbilityModel;
import com.hailiang.model.report.entity.ReportStudentPushRecord;
import com.hailiang.model.response.studentmodel.StudentAbilityModelResponse;
import com.hailiang.model.vo.*;
import com.hailiang.remote.hai.SendMsgManager;
import com.hailiang.remote.hai.domain.dto.request.MsgContent;
import com.hailiang.remote.hai.domain.dto.request.SendMsgRequestDTO;
import com.hailiang.remote.hai.domain.dto.response.SendMsgResponseDTO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduClassQueryDTO;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.vo.educational.EduClassInfoLinkVO;
import com.hailiang.remote.saas.vo.educational.EduClassInfoVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasHistoryStudentCacheManager;
import com.hailiang.saas.SaasStaffCacheManager;
import com.hailiang.saas.model.dto.StudentDTO;
import com.hailiang.service.*;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ParentReportReviewDetailServiceImpl extends ServiceImpl<ReportReviewDetailMapper, ReportReviewDetail> implements ParentReportReviewDetailService {

    @Resource
    private ReportReviewDetailLogic reportReviewDetailLogic;
    @Resource
    private BehaviourRecordService behaviourRecordService;
    @Resource
    private ReportReviewDetailConvert reportReviewDetailConvert;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private ReportReviewDetailLogic reviewDetailLogic;
    @Resource
    private SendMsgManager sendMsgManager;
    @Resource
    private MessageLogService messageLogService;
    @Resource
    private BasicInfoService basicInfoService;
    @Resource
    private StudentAbilityModelService studentAbilityModelService;
    @Resource
    private TermLogic termLogic;
    @Resource
    private SaasStaffCacheManager saasStaffCacheManager;

    private List<PageReportForParentVO> partition(List<PageReportForParentVO> list, int pageSize, int pageNum) {
        // 将 List 按照 PageSzie 拆分成多个List
        List<? extends List<PageReportForParentVO>> partition = Lists.partition(list, pageSize);
        // 总页数
        int pages = partition.size();
        pageNum = pageNum <= 0 ? 0 : (pageNum <= (pages - 1) ? pageNum : (pages - 1));
        return partition.get(pageNum);
    }

    @Override
    public GetReportStudentInfoForTeacherVO getReportStudentInfoForTeacher(GetReportStudentInfoForTeacherDTO dto) {
        Assert.isTrue(dto.getReviewDetailId() != null, "报告明细id不能为空");
        ReportReviewDetail reviewDetail = reportReviewDetailLogic.getById(dto.getReviewDetailId());
        Assert.isTrue(reviewDetail != null, "报告明细被删除");
        List<Integer> modulesInt = new ArrayList<>();
        if (StrUtil.isNotBlank(reviewDetail.getPushModule())) {
            String[] modules = reviewDetail.getPushModule().split(StrPool.COMMA);
            for (String module : modules) {
                modulesInt.add(Integer.parseInt(module));
            }
        }
        GetReportStudentInfoForTeacherVO vo = new GetReportStudentInfoForTeacherVO()
                .setTenantId(reviewDetail.getTenantId())
                .setSchoolId(reviewDetail.getSchoolId())
                .setCampusId(reviewDetail.getCampusId())
                .setClassId(reviewDetail.getClassId())
                .setStudentId(reviewDetail.getStudentId())
                .setStartTime(reviewDetail.getReportStartTime())
                .setEndTime(reviewDetail.getReportEndTime())
                .setModuleList(modulesInt)
                .setReviewTaskId(reviewDetail.getReviewTaskId())
                .setStatus(reviewDetail.getStatus());
        //查询学生信息
        StudentDTO studentByIdQuery = new StudentDTO();
        studentByIdQuery.setStudentIds(Collections.singletonList(Convert.toLong(reviewDetail.getStudentId())));
        List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOList = saasStaffCacheManager.studentDetailV2(studentByIdQuery);
        if (CollUtil.isEmpty(studentInfoVOList)) {
            return vo;
        }
        Long campusSectionId = studentInfoVOList.get(Constant.ZERO).getCampusSectionId();
        TermVo termVo = termLogic.getTermVoByCampusIdCache(WebUtil.getSchoolIdLong(), WebUtil.getCampusIdLong(), campusSectionId, reviewDetail.getReportEndTime());
        if (Objects.isNull(termVo)) {
            log.error("【教师画像校区学期查询】该校区没有学年学期信息，{}, {}, {}", reviewDetail.getStudentId(),
                    reviewDetail.getSchoolId(), reviewDetail.getCampusId());
            return vo;
        }
        vo.setSchoolYear(termVo.getSchoolYear());
        vo.setTermName(termVo.getTermName());
        vo.setIsCurrentYear(termVo.getIsCurrentYear());
        vo.setIsCurrentTerm(termVo.getIsCurrentTerm());

        // 如果校区没有配置能力模型 不返回前端model模块
        StudentAbilityModelResponse studentAbilityModelResponse = studentAbilityModelService.getModelDetail(
                vo.getCampusId(), termVo.getSchoolYear(), vo.getIsCurrentYear());
        if (ObjectUtil.isEmpty(studentAbilityModelResponse)) {
            vo.setModuleList(PushModuleEnum.getCodesNotModel());
        }
        // 查询学生能力模型数据
        StudentAbilityModel studentAbilityModel = studentAbilityModelService.getStudentAbilityModelByDate(vo.getCampusId(), DateUtil.now());
        vo.setAbilityModelName(studentAbilityModel.getAbilityModelName());
        vo.setContrastRange(studentAbilityModel.getContrastRange());

        return vo;
    }

    @Override
    public GetReportStudentInfoForTeacherVO getReportStudentInfoForStudent(GetReportStudentInfoForStudentDTO dto) {
        GetReportStudentInfoForTeacherVO vo = new GetReportStudentInfoForTeacherVO();
        List<EduClassInfoLinkVO> eduClassInfoLinkVOS = basicInfoRemote.listUnderByClassIds(Collections.singletonList(Convert.toLong(dto.getClassId())));
        if (CollUtil.isNotEmpty(eduClassInfoLinkVOS)) {
            EduClassInfoLinkVO eduClassInfoLinkVO = eduClassInfoLinkVOS.get(0);
            vo.setTenantId(Convert.toStr(eduClassInfoLinkVO.getTenantId()));
            vo.setSchoolId(Convert.toStr(eduClassInfoLinkVO.getSchoolId()));
            vo.setCampusId(Convert.toStr(eduClassInfoLinkVO.getCampusId()));
            vo.setClassId(dto.getClassId());
            vo.setStudentId(dto.getStudentId());
            vo.setStartTime(dto.getStartTime());
            vo.setEndTime(dto.getEndTime());
            vo.setModuleList(PushModuleEnum.getAllCodes());

            TermVo termVo = termLogic.getTermVoByCampusId(eduClassInfoLinkVO.getSchoolId(),
                    eduClassInfoLinkVO.getCampusId(), eduClassInfoLinkVO.getCampusSectionId(),
                    dto.getEndTime());
            if (Objects.isNull(termVo)) {
                log.error("【学生查询校区学期】该校区没有学年学期信息，{}, {}, {}", dto.getStudentId(), dto.getClassId(), dto.getStartTime());
                return vo;
            }

            vo.setSchoolYear(termVo.getSchoolYear());
            vo.setTermName(termVo.getTermName());
            vo.setIsCurrentYear(termVo.getIsCurrentYear());
        }


        // 如果校区没有配置能力模型 不返回前端model模块
        StudentAbilityModelResponse studentAbilityModelResponse = studentAbilityModelService.getModelDetail(
                vo.getCampusId(), vo.getSchoolYear(), vo.getIsCurrentYear());
        if (ObjectUtil.isEmpty(studentAbilityModelResponse)) {
            vo.setModuleList(PushModuleEnum.getCodesNotModel());
        }
        // 查询学生能力模型数据
        StudentAbilityModel studentAbilityModel = studentAbilityModelService.getStudentAbilityModelByDate(vo.getCampusId(), DateUtil.now());
        vo.setAbilityModelName(studentAbilityModel.getAbilityModelName());
        vo.setContrastRange(studentAbilityModel.getContrastRange());

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GetReportStudentInfoForParentVO getReportStudentInfoForParent(GetReportStudentInfoForParentDTO dto) {
        Assert.isTrue(dto.getReviewDetailId() != null, () -> new BizException(BizExceptionEnum.REVIEW_DETAIL_ID_NOT_NULL.getMessage()));
        ReportReviewDetail reviewDetail = reportReviewDetailLogic.getById(dto.getReviewDetailId());
        Assert.isTrue(reviewDetail != null, () -> new BizException(BizExceptionEnum.REVIEW_DETAIL_NOT_NULL.getMessage()));
        // 消除小红点
        ReportReviewDetail update = new ReportReviewDetail();
        update.setId(reviewDetail.getId());
        // 家长第一次查看报告时记录时间
        if (Boolean.FALSE.equals(reviewDetail.getIsRead())) {
            update.setFirstViewTime(new Date());
        }
        update.setIsRead(true);
        reportReviewDetailLogic.updateById(update);
        List<Integer> modulesInt = new ArrayList<>();
        if (StrUtil.isNotBlank(reviewDetail.getPushModule())) {
            String[] modules = reviewDetail.getPushModule().split(StrPool.COMMA);
            for (String module : modules) {
                modulesInt.add(Integer.parseInt(module));
            }
        }
        GetReportStudentInfoForParentVO vo = new GetReportStudentInfoForParentVO()
                .setTenantId(reviewDetail.getTenantId())
                .setSchoolId(reviewDetail.getSchoolId())
                .setCampusId(reviewDetail.getCampusId())
                .setClassId(reviewDetail.getClassId())
                .setStudentId(reviewDetail.getStudentId())
                .setStartTime(reviewDetail.getReportStartTime())
                .setEndTime(reviewDetail.getReportEndTime())
                .setModuleList(modulesInt);
        //查询学生信息
        StudentDTO studentByIdQuery = new StudentDTO();
        studentByIdQuery.setStudentIds(Collections.singletonList(Convert.toLong(reviewDetail.getStudentId())));
        List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOList = saasStaffCacheManager.studentDetailV2(studentByIdQuery);
        if (CollUtil.isEmpty(studentInfoVOList)) {
            return vo;
        }
        Long campusSectionId = studentInfoVOList.get(Constant.ZERO).getCampusSectionId();

        TermVo termVo = termLogic.getTermVoByCampusId(Long.valueOf(reviewDetail.getSchoolId()),
                Long.valueOf(reviewDetail.getCampusId()), campusSectionId, reviewDetail.getReportEndTime());
        if (Objects.isNull(termVo)) {
            log.error("【校区学期查询】该校区没有学年学期信息，{}, {}, {}", reviewDetail.getStudentId(),
                    reviewDetail.getSchoolId(), reviewDetail.getCampusId());
            return vo;
        }

        vo.setSchoolYear(termVo.getSchoolYear());
        vo.setTermName(termVo.getTermName());
        vo.setIsCurrentYear(termVo.getIsCurrentYear());
        vo.setIsCurrentTerm(termVo.getIsCurrentTerm());


        // 如果校区没有配置能力模型 不返回前端model模块
        StudentAbilityModelResponse studentAbilityModelResponse = studentAbilityModelService.getModelDetail(
                vo.getCampusId(), termVo.getSchoolYear(), vo.getIsCurrentYear());
        if (ObjectUtil.isEmpty(studentAbilityModelResponse)) {
            vo.setModuleList(PushModuleEnum.getCodesNotModel());
        }
        // 查询学生能力模型数据
        StudentAbilityModel studentAbilityModel = studentAbilityModelService.getStudentAbilityModelByDate(vo.getCampusId(), DateUtil.now());
        vo.setAbilityModelName(studentAbilityModel.getAbilityModelName());
        vo.setContrastRange(studentAbilityModel.getContrastRange());

        return vo;
    }

    @Override
    public List<SendMsgResponseDTO> sendReportToParent(List<ReportReviewDetail> details) {
        log.info("[推送报告]-发送报告给家长, SendReportToParentDTO：{}", JSONUtil.toJsonStr(details));
        List<Long> detailIds = details.stream().map(ReportReviewDetail::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(detailIds)) {
            return new ArrayList<>();
        }
        List<ReportDetailInfoDTO> reportReviewDetails = reviewDetailLogic.listReportDetailByNoApproval(detailIds);
        if (CollUtil.isEmpty(reportReviewDetails)) {
            return new ArrayList<>();
        }
        // 发送微信报告
        return sendWxReportMsg(reportReviewDetails);
    }

    /**
     * 发送微信报告
     *
     * @param reportReviewDetails 审核报告集合
     * @return
     */
    private List<SendMsgResponseDTO> sendWxReportMsg(List<ReportDetailInfoDTO> reportReviewDetails) {
        List<Long> classIds = reportReviewDetails.stream().map(s -> Convert.toLong(s.getClassId())).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(classIds)) {
            return new ArrayList<>();
        }
        EduClassQueryDTO eduClassQueryDTO = new EduClassQueryDTO();
        eduClassQueryDTO.setSchoolId(Convert.toLong(CollUtil.getFirst(reportReviewDetails).getSchoolId()));
        eduClassQueryDTO.setClassIds(classIds);
        List<EduClassInfoVO> eduClassInfos = basicInfoRemote.queryClassInfoList(eduClassQueryDTO);
        Map<String, String> headMasterNameMap = new HashMap<>();
        Map<String, String> headMasterIdMap = new HashMap<>();
        if (CollUtil.isNotEmpty(eduClassInfos)) {
            headMasterNameMap = eduClassInfos.stream().filter(d -> StrUtil.isNotBlank(d.getHeadMasterName())).collect(Collectors.toMap(d -> String.valueOf(d.getId()), EduClassInfoVO::getHeadMasterName, (a, b) -> b));
            headMasterIdMap = eduClassInfos.stream().filter(d -> d.getHeadMasterId() != null).collect(Collectors.toMap(d -> String.valueOf(d.getId()), d -> String.valueOf(d.getHeadMasterId()), (a, b) -> b));
        }

        List<MessageLog> messageLogs = new ArrayList<>();
        Map<Integer, String> pushFrequencyMap = new HashMap<>();
        pushFrequencyMap.put(2, "两");
        pushFrequencyMap.put(3, "三");
        pushFrequencyMap.put(4, "四");

        List<SendMsgResponseDTO> sendWxMsgResults = new ArrayList<>();
        for (ReportDetailInfoDTO detail : reportReviewDetails) {
            // 推家校消息
            MsgContent content = new MsgContent();
            content.setFirst("老师给你发了孩子近" + pushFrequencyMap.get(detail.getPushFrequency()) + "周在校综合表现情况，快来看看吧！");
            content.setKeyword1(detail.getClassName());
            content.setKeyword2(headMasterNameMap.get(detail.getClassId()));
            content.setKeyword3(DateUtil.formatDateTime(DateUtil.date()));
            content.setKeyword4("学生综合表现画像");
            content.setRemark("请及时点击查看");

            SendMsgRequestDTO request = new SendMsgRequestDTO();
            request.setStudentCode(detail.getStudentCode());
            request.setContent(content);
            request.setReviewDetailId(detail.getId());
            request.setStudentName(detail.getStudentName());
            SendMsgResponseDTO sendMsgResponseDTO = sendMsgManager.sendMsg(request);
            sendMsgResponseDTO.setReviewDetailId(detail.getId());
            sendWxMsgResults.add(sendMsgResponseDTO);

            MessageLog messageLog = new MessageLog();
            messageLog.setContent(content.toString());
            messageLog.setBusinessId(detail.getId());
            messageLog.setBusinessType(MessageLogBusinessTypeEnum.HAI_REPORT_MSG.getCode());
            messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.HAI_REPORT_MSG.getMessage());
            messageLog.setMessageType(MessageLogMessageTypeEnum.HAI_REPORT_MSG.getCode());
            messageLog.setTermintalType(MessageLogTerminalTypeEnum.WECHAT.getCode());
            messageLog.setSchoolId(detail.getSchoolId());
            messageLog.setCampusId(detail.getCampusId());
            messageLog.setUserId(Long.parseLong(detail.getStudentId()));
            messageLog.setUserType(TaskRoleTypeEnum.PARENT.getCode());
            messageLog.setTenantId(detail.getTenantId());
            messageLog.setSendResult(sendMsgResponseDTO.getSendResult());
            messageLog.setRequestParam(sendMsgResponseDTO.getRequestParam());
            String headMasterName = headMasterIdMap.get(detail.getClassId());
            messageLog.setCreateBy(StrUtil.isNotBlank(headMasterName) ? headMasterName : "admin");
            messageLogs.add(messageLog);


        }
        log.info("[推送报告]-准备记日志，修改审核详情状态, messageLogs：{}", JSONUtil.toJsonStr(messageLogs));
        messageLogService.saveMessageBatch(messageLogs);

        return sendWxMsgResults;
    }

    @Override
    public void hangUpUnNeedBehaviourRecordIds(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
        // 查出这个时间段内的records
        // 查出detail关联的本期报告推送明细中的本期推送
        // 获取差集
    }

    @Override
    public List<Long> listNoSendRecordByDetail(String detailId) {
        // 根据detailId获取开始时间 结束时间 学生id
        ReportReviewDetail reportReviewDetail = this.getById(detailId);
        Date reportStartTime = reportReviewDetail.getReportStartTime();
        Date reportEndTime = reportReviewDetail.getReportEndTime();
        String studentId = reportReviewDetail.getStudentId();
        if (ObjectUtil.hasEmpty(reportStartTime, reportEndTime, studentId)) {
            log.warn("[获取当前推送报告未推送的数据-参数有误,{}{}{}]", reportStartTime, reportEndTime, studentId);
            return Collections.emptyList();
        }
        // 去record表里面查有哪些record
        List<BehaviourRecord> behaviourRecords = behaviourRecordService.list(new LambdaQueryWrapper<BehaviourRecord>()
                .eq(BehaviourRecord::getStudentId, studentId)
                .ge(BehaviourRecord::getSubmitTime, reportStartTime)
                .le(BehaviourRecord::getSubmitTime, reportEndTime));
        if (CollUtil.isEmpty(behaviourRecords)) {
            log.warn("[获取当前推送报告未推送的数据-behaviourRecords为空，直接返回");
            return Collections.emptyList();
        }
        List<Long> allList = CollStreamUtil.toList(behaviourRecords, BehaviourRecord::getId);
        // 根据detailId获取本次已推送的recordId

        return null;
    }

}

