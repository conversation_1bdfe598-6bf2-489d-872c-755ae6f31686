package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.hailiang.annotation.MethodExecuteLog;
import com.hailiang.constant.Constant;
import com.hailiang.convert.HelpBehaviourRecordConvert;
import com.hailiang.enums.*;
import com.hailiang.enums.error.ReportErrorEnum;
import com.hailiang.enums.report.ReportBehaviourTypeEnum;
import com.hailiang.enums.report.ReportDailyPushWayEnum;
import com.hailiang.exception.BizException;
import com.hailiang.helper.ChannelMsgHelper;
import com.hailiang.logic.ReportReviewDetailLogic;
import com.hailiang.logic.TermLogic;
import com.hailiang.manager.EvaluateHelpBehaviourRecordManager;
import com.hailiang.manager.InitialScoreManager;
import com.hailiang.mapper.ReportReviewTaskMapper;
import com.hailiang.mapper.TargetMapper;
import com.hailiang.model.dto.approval.HandleStatusDTO;
import com.hailiang.model.dto.behaviour.help.HelpBehaviourRecordQueryDTO;
import com.hailiang.model.dto.query.BehaviourRecordQueryDTO;
import com.hailiang.model.dto.query.ReportBehaviorDetailDTO;
import com.hailiang.model.dto.query.ReportReviewQueryDTO;
import com.hailiang.model.dto.query.ReportSendDTO;
import com.hailiang.model.entity.*;
import com.hailiang.model.vo.*;
import com.hailiang.remote.ding.utils.DingDingMsgUtil;
import com.hailiang.remote.feishu.domain.FeiMsgReqDTO;
import com.hailiang.remote.feishu.domain.FeiShuResultDTO;
import com.hailiang.remote.feishu.utils.FeiShuMsgUtil;
import com.hailiang.remote.hai.SendMsgManager;
import com.hailiang.remote.hai.domain.dto.HaiMessageCodeEnum;
import com.hailiang.remote.hai.domain.dto.request.MsgContent;
import com.hailiang.remote.hai.domain.dto.request.SendMsgRequestDTO;
import com.hailiang.remote.hai.domain.dto.response.SendMsgResponseDTO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.dto.educational.EduParentQueryDTO;
import com.hailiang.remote.saas.pojo.educational.EduParentInfoPojo;
import com.hailiang.remote.saas.vo.educational.EduClassInfoLinkVO;
import com.hailiang.remote.saas.vo.educational.EduParentInfoVO;
import com.hailiang.remote.saas.vo.staff.StaffNOVO;
import com.hailiang.remote.saas.vo.student.StudentVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.dto.ParentQueryDTO;
import com.hailiang.saas.model.pojo.ParentInfoPojo;
import com.hailiang.saas.model.vo.ParentInfoVO;
import com.hailiang.service.*;
import com.hailiang.util.NumUtil;
import com.hailiang.util.R;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/13 15:15
 */
@Slf4j
@Service
@RefreshScope
public class ReportServiceImpl implements ReportService {

    @Resource
    private ReportReviewTaskMapper reportReviewTaskMapper;
    @Resource
    private BehaviourRecordService behaviourRecordService;
    @Resource
    private ReportReviewTaskService reportReviewTaskService;
    @Resource
    private ReportReviewDetailService reportReviewDetailService;
    @Resource
    private ReportPushRuleService reportPushRuleService;
    @Resource
    private ReportBehaviourRecordService reportBehaviourRecordService;
    @Resource
    private ParentReportReviewDetailService parentReportReviewDetailService;
    @Resource
    private MessageLogService messageLogService;
    @Resource
    private DingDingMsgUtil dingDingMsgUtil;
    @Resource
    private TargetService targetService;
    @Resource
    private InitialScoreManager initialScoreManager;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private ReportReviewDetailLogic reviewDetailLogic;
    @Resource
    private SendMsgManager sendMsgManager;
    @Resource
    private BasicInfoService basicInfoService;
    @Resource
    private InfoService infoService;
    @Resource
    private TargetMapper targetMapper;
    @Resource
    private FeiShuMsgUtil feiShuMsgUtil;
    @Autowired
    ThreadPoolTaskExecutor evaluateExecutor;
    @Autowired
    ThreadPoolTaskExecutor singleExecutor;
    @Resource
    private SaasStudentManager saasStudentManager;
    @Resource
    private TermLogic termLogic;
    @Resource
    private HelpBehaviourRecordService helpBehaviourRecordService;
    @Resource
    private CacheSaasManager cacheSaasManager;
    @Resource
    private EvaluateHelpBehaviourRecordManager helpBehaviourRecordManager;
    @Resource
    private ChannelMsgHelper channelMsgHelper;
    @Value("${third.hai.parentReceiveReportUrl}")
    private String parentReceiveReportUrl;
    @Value("${third.ding.titleColor}")
    private String titleColor;
    private static final String KEY = "1234567887654321";
    /**
     * 分页获取审核列表
     *
     * @param query
     * @return
     */
    @Override
    public Page<ReportReviewInfoVO> pageReportReview(ReportReviewQueryDTO query) {
        Page<ReportReviewTask> taskPage = reportReviewTaskMapper.pageReportReview(new Page<>(query.getPageNum(), query.getPageSize()), WebUtil.getStaffId(), WebUtil.getCampusId());
        Page<ReportReviewInfoVO> page = new Page<>(query.getPageNum(), query.getPageSize());
        if (CollUtil.isEmpty(taskPage.getRecords())) {
            return page;
        }

        List<Long> taskIds = taskPage.getRecords().stream().map(ReportReviewTask::getId).distinct().collect(Collectors.toList());
        //获取待审核数据(未发送和发送失败得数据)
        Map<Long, Long> unReviewNumMap = new HashMap<>();
        List<Integer> noSendStatus = CollUtil.newArrayList(ReportReviewStatusEnum.UN_REVIEW.getCode(), ReportReviewStatusEnum.SEND_FAIL.getCode());
        //获取已发送数据(发送成功的数据)
        Map<Long, Long> isSendNumMap = new HashMap<>();
        List<Integer> sendStatus = CollUtil.newArrayList(ReportReviewStatusEnum.IS_SEND.getCode());
        //总报告数
        Map<Long, Long> allNumMap = new HashMap<>();
        if (CollUtil.isNotEmpty(taskIds)) {
            List<ReportReviewDetail> reportReviewDetails = reportReviewDetailService.list(new LambdaQueryWrapper<ReportReviewDetail>().in(ReportReviewDetail::getReviewTaskId, taskIds));
            unReviewNumMap = reportReviewDetails.stream().filter(s -> noSendStatus.contains(s.getStatus())).collect(Collectors.groupingBy(ReportReviewDetail::getReviewTaskId, Collectors.counting()));
            isSendNumMap = reportReviewDetails.stream().filter(s -> sendStatus.contains(s.getStatus())).collect(Collectors.groupingBy(ReportReviewDetail::getReviewTaskId, Collectors.counting()));
            allNumMap = reportReviewDetails.stream().collect(Collectors.groupingBy(ReportReviewDetail::getReviewTaskId, Collectors.counting()));

        }
        List<ReportReviewInfoVO> result = new ArrayList<>();
        for (ReportReviewTask record : taskPage.getRecords()) {
            ReportReviewInfoVO reportReviewInfoVO = new ReportReviewInfoVO();
            reportReviewInfoVO.setReviewTaskId(Convert.toStr(record.getId()));
            reportReviewInfoVO.setTitle(DateUtil.format(record.getReportStartTime(), "MM.dd") + "~" + DateUtil.format(record.getReportEndTime(), "MM.dd") + "报告");
            reportReviewInfoVO.setStatus(isSendNumMap.containsKey(record.getId()) && allNumMap.containsKey(record.getId()) && isSendNumMap.get(record.getId()).equals(allNumMap.get(record.getId())) ? 1 : 0);
            reportReviewInfoVO.setClassName(record.getClassName());
            reportReviewInfoVO.setUnReviewNum(Convert.toInt(unReviewNumMap.getOrDefault(record.getId(), 0L)));
            reportReviewInfoVO.setSendNum(Convert.toInt(isSendNumMap.getOrDefault(record.getId(), 0L)));
            result.add(reportReviewInfoVO);
        }

        page.setRecords(result);
        page.setTotal(taskPage.getTotal());
        return page;
    }

    /**
     * 获取班级未审核数据列表
     *
     * @param reviewTaskId   审核任务id
     * @param reviewDetailId 审核详情id(获取审核前后节点时传入调用,正常查询不需要)
     * @return {@link List}<{@link ReportNoReviewDetailVO}>
     */
    @Override
    public List<ReportNoReviewDetailVO> listReportReviewDetail(Long reviewTaskId, Long reviewDetailId) {
        Assert.notNull(reviewTaskId, () -> new BizException(ReportErrorEnum.EMPTY_TASK_ID));

        ReportReviewTask reviewTask = reportReviewTaskService.getById(reviewTaskId);
        Assert.notNull(reviewTask, () -> new BizException(ReportErrorEnum.EMPTY_TASK));
        List<EduClassInfoLinkVO> eduClassInfoLinkVOS = cacheSaasManager.listUnderByClassIds(Collections.singletonList(Convert.toLong(reviewTask.getClassId())));

        List<ReportReviewDetail> reportReviewDetails = reportReviewDetailService.list(new LambdaQueryWrapper<ReportReviewDetail>()
                .eq(ReportReviewDetail::getReviewTaskId, reviewTaskId)
                .in(ReportReviewDetail::getStatus, CollUtil.newArrayList(ReportReviewStatusEnum.UN_REVIEW.getCode(), ReportReviewStatusEnum.SEND_FAIL.getCode())));

        //如果是获取审核前后节点时传入调用,返回的审核数据加上当前被发送的数据
        if (Objects.nonNull(reviewDetailId)) {
            ReportReviewDetail reviewDetail = reportReviewDetailService.getById(reviewDetailId);
            List<Long> detailIds = reportReviewDetails.stream().map(ReportReviewDetail::getId).distinct().collect(Collectors.toList());
            if (!detailIds.contains(reviewDetailId)) {
                reportReviewDetails.add(reviewDetail);
            }
        }

        List<String> studentIds = reportReviewDetails.stream().map(ReportReviewDetail::getStudentId).distinct().collect(Collectors.toList());
        //学生行为记录map
        Map<String, List<BehaviourRecord>> behaviourRecordMap = new HashMap<>();
        List<InitialScorePO> initialScorePOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(studentIds)) {
            List<BehaviourRecord> behaviourRecords = behaviourRecordService.list(new LambdaQueryWrapper<BehaviourRecord>()
                    .in(BehaviourRecord::getStudentId, studentIds)
                    .between(BehaviourRecord::getSubmitTime, reviewTask.getReportStartTime(), reviewTask.getReportEndTime()));
            // 过滤无需统计数据
            behaviourRecords = behaviourRecords
                    .stream()
                    .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                    .collect(Collectors.toList());

            //加入帮扶数据
            HelpBehaviourRecordQueryDTO helpBehaviourRecordQueryDTO = new HelpBehaviourRecordQueryDTO();
            helpBehaviourRecordQueryDTO.setStudentIds(studentIds);
            helpBehaviourRecordQueryDTO.setStartTime(reviewTask.getReportStartTime());
            helpBehaviourRecordQueryDTO.setEndTime(reviewTask.getReportEndTime());
            List<EvaluateHelpBehaviourRecordPO> evaluateHelpBehaviourRecordPOS = helpBehaviourRecordManager.queryByCondition(helpBehaviourRecordQueryDTO);
            // 过滤无需统计帮扶数据
            evaluateHelpBehaviourRecordPOS = evaluateHelpBehaviourRecordPOS
                    .stream()
                    .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(evaluateHelpBehaviourRecordPOS)) {
                List<BehaviourRecord> behaviourRecordList = HelpBehaviourRecordConvert.toBehaviourRecordList(evaluateHelpBehaviourRecordPOS);
                behaviourRecords.addAll(behaviourRecordList);
            }
            behaviourRecordMap = behaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecord::getStudentId));

            // 获取初始分信息
            String campusId = WebUtil.getCampusId();
            initialScorePOS = initialScoreManager.listByCampusId(campusId);
        }

        List<ReportNoReviewDetailVO> result = new ArrayList<>();
        for (ReportReviewDetail reportReviewDetail : reportReviewDetails) {
            ReportNoReviewDetailVO reportNoReviewDetailVO = new ReportNoReviewDetailVO();
            reportNoReviewDetailVO.setReviewDetailId(Convert.toStr(reportReviewDetail.getId()));
            reportNoReviewDetailVO.setStudentName(reportReviewDetail.getStudentName());
            reportNoReviewDetailVO.setStudentId(reportReviewDetail.getStudentId());
            reportNoReviewDetailVO.setReportBeginTime(reviewTask.getReportStartTime());
            reportNoReviewDetailVO.setReportEndTime(reviewTask.getReportEndTime());
            reportNoReviewDetailVO.setPushStatus(reportReviewDetail.getPushStatus());
            //推送状态,如果状态不存在说明未发送
            reportNoReviewDetailVO.setPushMessage(Objects.isNull(reportReviewDetail.getPushStatus())
                    ? ""
                    : HaiMessageCodeEnum.getEvaluateMessageByCode(reportReviewDetail.getPushStatus()));

            if (behaviourRecordMap.containsKey(reportReviewDetail.getStudentId())) {
                List<BehaviourRecord> behaviourRecords = behaviourRecordMap.get(reportReviewDetail.getStudentId());
                //所有加分值
                BigDecimal extraScore = behaviourRecords.stream().filter(s -> BehaviourTypeEnum.EXTRA_POINT.getCode().equals(s.getScoreType())).map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
                //所有减分值(所有负数相加)
                BigDecimal subtractScore = behaviourRecords.stream().filter(s -> BehaviourTypeEnum.SUBTRACT_POINT.getCode().equals(s.getScoreType())).map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
                //总分
                BigDecimal totalScore = extraScore.add(subtractScore);
                String compositeScore = totalScore.toString();
                if ("0.00".equals(totalScore.toString())) {
                    compositeScore = "0";
                }

                reportNoReviewDetailVO.setPraiseScore(extraScore);
                reportNoReviewDetailVO.setNeedImprovedScore(subtractScore);

                //判断是否无分值,所有明细都无分值或者没有数据显示 --
                Boolean isNoValue = Boolean.FALSE;
                //无数据
                if (CollUtil.isEmpty(behaviourRecords)) {
                    isNoValue = Boolean.TRUE;
                }
                //所有明细都无分值
                long noValueNum = behaviourRecords.stream().filter(s -> Objects.isNull(s.getScoreType())).count();
                if (Convert.toInt(noValueNum).equals(behaviourRecords.size())) {
                    isNoValue = Boolean.TRUE;
                }
                reportNoReviewDetailVO.setCompositeScore(isNoValue ? "--" : compositeScore);

                //待改进内容封装
                List<NeedImprovedVO> needImproves = behaviourRecords.stream().filter(s -> BehaviourTypeEnum.SUBTRACT_POINT.getCode().equals(s.getScoreType())).map(s -> {
                    NeedImprovedVO needImprovedVO = new NeedImprovedVO();
                    needImprovedVO.setContent(s.getInfoName());
                    needImprovedVO.setScore(s.getScore());
                    return needImprovedVO;
                }).limit(8L).collect(Collectors.toList());
                reportNoReviewDetailVO.setNeedImprovedList(needImproves);
            }

            if (CollUtil.isNotEmpty(eduClassInfoLinkVOS)) {
                EduClassInfoLinkVO eduClassInfoLinkVO = eduClassInfoLinkVOS.get(Constant.ZERO);
                TermVo termVo = termLogic.getTermVoByCampusIdCache(eduClassInfoLinkVO.getSchoolId(), eduClassInfoLinkVO.getCampusId(), eduClassInfoLinkVO.getCampusSectionId(), reportReviewDetail.getReportStartTime());
                if (termVo != null) {
                    reportNoReviewDetailVO.setIsCurrentTerm(termVo.getIsCurrentTerm());
                    reportNoReviewDetailVO.setIsCurrentYear(termVo.getIsCurrentYear());
                    reportNoReviewDetailVO.setSchoolYear(termVo.getSchoolYear());
                    reportNoReviewDetailVO.setTermName(termVo.getTermName());
                }
            }

            // 组装初始分信息
            InitialScorePO initialScorePO = initialScorePOS
                    .stream()
                    .filter(item -> Objects.equals(item.getSchoolYear(), reportNoReviewDetailVO.getSchoolYear())
                            && Objects.equals(item.getTermName(), reportNoReviewDetailVO.getTermName())
                            && Objects.equals(item.getInitialScoreType(), 0))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(initialScorePO)) {
                BigDecimal totalScore = initialScorePO.getInitialScore();
                if ("--".equals(reportNoReviewDetailVO.getCompositeScore())) {
                    reportNoReviewDetailVO.setCompositeScore("0");
                }
                reportNoReviewDetailVO.setInitialScore(totalScore);
                reportNoReviewDetailVO.setCompositeScore(NumberUtil.round(NumberUtil.add(Convert.toBigDecimal(reportNoReviewDetailVO.getCompositeScore()), totalScore), 2).toPlainString());

            }
            if (!behaviourRecordMap.containsKey(reportReviewDetail.getStudentId()) && Objects.isNull(initialScorePO)) {
                //无明细
                reportNoReviewDetailVO.setCompositeScore("--");
            }
            result.add(reportNoReviewDetailVO);
        }
        return result.stream().sorted(Comparator.comparing(ReportNoReviewDetailVO::getCompositeScore).thenComparing(ReportNoReviewDetailVO::getReviewDetailId).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取班级已发送数据列表
     *
     * @param reviewTaskId 审核任务id
     * @return {@link List}<{@link ReportSendDetailInfoVO}>
     */
    @Override
    public List<ReportSendDetailInfoVO> listSendReviewDetail(Long reviewTaskId) {
        Assert.notNull(reviewTaskId, () -> new BizException(ReportErrorEnum.EMPTY_TASK_ID));

        ReportReviewTask reviewTask = reportReviewTaskService.getById(reviewTaskId);
        Assert.notNull(reviewTask, () -> new BizException(ReportErrorEnum.EMPTY_TASK));

        List<EduClassInfoLinkVO> eduClassInfoLinkVOS = cacheSaasManager.listUnderByClassIds(Collections.singletonList(Convert.toLong(reviewTask.getClassId())));

        List<ReportReviewDetail> reportReviewDetails = reportReviewDetailService.list(new LambdaQueryWrapper<ReportReviewDetail>()
                .eq(ReportReviewDetail::getReviewTaskId, reviewTaskId)
                .eq(ReportReviewDetail::getStatus, ReportReviewStatusEnum.IS_SEND.getCode())
                .orderByDesc(ReportReviewDetail::getReceiveTime));

        List<String> studentIds = reportReviewDetails.stream().map(ReportReviewDetail::getStudentId).distinct().collect(Collectors.toList());
        //学生行为记录map
        Map<String, List<BehaviourRecord>> behaviourRecordMap = new HashMap<>();
        List<InitialScorePO> initialScorePOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(studentIds)) {
            List<Long> reportReviewDetailIdList = reportReviewDetails.stream().map(ReportReviewDetail::getId).collect(Collectors.toList());
            List<ReportBehaviourRecord> reportBehaviourRecords = reportBehaviourRecordService.listNoSendRecordByDetailIdList(reportReviewDetailIdList);
            //无需统计的行为id
            List<Long> unNeedBehaviourRecordIds = new ArrayList<>();
            //无需统计的帮扶行为id
            List<Long> unNeedHelpBehaviourRecordIds = new ArrayList<>();
            for (ReportBehaviourRecord reportBehaviourRecord : reportBehaviourRecords) {
                if (Objects.equals(reportBehaviourRecord.getRecordType(), ReportBehaviourTypeEnum.BEHAVIOUR_RECORD.getCode())) {
                    unNeedBehaviourRecordIds.add(reportBehaviourRecord.getBehaviourRecordId());
                } else if (Objects.equals(reportBehaviourRecord.getRecordType(), ReportBehaviourTypeEnum.HELP_BEHAVIOUR_RECORD.getCode())) {
                    unNeedHelpBehaviourRecordIds.add(reportBehaviourRecord.getBehaviourRecordId());
                }
            }
            List<BehaviourRecord> behaviourRecords = behaviourRecordService.list(new LambdaQueryWrapper<BehaviourRecord>()
                    .in(BehaviourRecord::getStudentId, studentIds)
                    .notIn(CollectionUtils.isNotEmpty(unNeedBehaviourRecordIds), BehaviourRecord::getId, unNeedBehaviourRecordIds)
                    .between(BehaviourRecord::getSubmitTime, reviewTask.getReportStartTime(), reviewTask.getReportEndTime()));
            // 过滤无需统计行为记录
            behaviourRecords = behaviourRecords
                    .stream()
                    .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                    .collect(Collectors.toList());

            //加入帮扶积分
            HelpBehaviourRecordQueryDTO helpBehaviourRecordQueryDTO = new HelpBehaviourRecordQueryDTO();
            helpBehaviourRecordQueryDTO.setStudentIds(studentIds);
            helpBehaviourRecordQueryDTO.setStartTime(reviewTask.getReportStartTime());
            helpBehaviourRecordQueryDTO.setEndTime(reviewTask.getReportEndTime());
            helpBehaviourRecordQueryDTO.setUnNeedHelpBehaviourRecordIds(unNeedHelpBehaviourRecordIds);
            List<EvaluateHelpBehaviourRecordPO> evaluateHelpBehaviourRecordPOS = helpBehaviourRecordManager.queryByCondition(helpBehaviourRecordQueryDTO);
            // 过滤无需统计数据
            evaluateHelpBehaviourRecordPOS = evaluateHelpBehaviourRecordPOS
                    .stream()
                    .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                    .collect(Collectors.toList());

            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(evaluateHelpBehaviourRecordPOS)) {
                List<BehaviourRecord> behaviourRecordList = HelpBehaviourRecordConvert.toBehaviourRecordList(evaluateHelpBehaviourRecordPOS);
                behaviourRecords.addAll(behaviourRecordList);
            }
            behaviourRecordMap = behaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecord::getStudentId));

            // 获取初始分信息
            String campusId = WebUtil.getCampusId();
            initialScorePOS = initialScoreManager.listByCampusId(campusId);
        }

        List<ReportSendDetailInfoVO> result = new ArrayList<>();
        for (ReportReviewDetail reportReviewDetail : reportReviewDetails) {
            ReportSendDetailInfoVO reportSendDetailInfoVO = new ReportSendDetailInfoVO();
            reportSendDetailInfoVO.setReviewDetailId(Convert.toStr(reportReviewDetail.getId()));
            reportSendDetailInfoVO.setStudentName(reportReviewDetail.getStudentName());
            reportSendDetailInfoVO.setStudentId(reportReviewDetail.getStudentId());
            reportSendDetailInfoVO.setReportBeginTime(reviewTask.getReportStartTime());
            reportSendDetailInfoVO.setReportEndTime(reviewTask.getReportEndTime());
            reportSendDetailInfoVO.setStatus(reportReviewDetail.getStatus());
            reportSendDetailInfoVO.setSendTime(reportReviewDetail.getReceiveTime());
            if (behaviourRecordMap.containsKey(reportReviewDetail.getStudentId())) {
                List<BehaviourRecord> behaviourRecords = behaviourRecordMap.get(reportReviewDetail.getStudentId());
                //所有加分值
                BigDecimal extraScore = behaviourRecords.stream().filter(s -> BehaviourTypeEnum.EXTRA_POINT.getCode().equals(s.getScoreType())).map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
                //所有减分值(所有负数相加)
                BigDecimal subtractScore = behaviourRecords.stream().filter(s -> BehaviourTypeEnum.SUBTRACT_POINT.getCode().equals(s.getScoreType())).map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
                //总分
                BigDecimal totalScore = extraScore.add(subtractScore);
                String compositeScore = totalScore.toString();
                if ("0.00".equals(totalScore.toString())) {
                    compositeScore = "0";
                }

                //判断是否无分值,所有明细都无分值或者没有数据显示 --
                Boolean isNoValue = Boolean.FALSE;
                //无数据
                if (CollUtil.isEmpty(behaviourRecords)) {
                    isNoValue = Boolean.TRUE;
                }
                //所有明细都无分值
                long noValueNum = behaviourRecords.stream().filter(s -> Objects.isNull(s.getScoreType())).count();
                if (Convert.toInt(noValueNum).equals(behaviourRecords.size())) {
                    isNoValue = Boolean.TRUE;
                }
                reportSendDetailInfoVO.setCompositeScore(isNoValue ? "--" : compositeScore);
            }

            if (CollUtil.isNotEmpty(eduClassInfoLinkVOS)) {
                EduClassInfoLinkVO eduClassInfoLinkVO = eduClassInfoLinkVOS.get(Constant.ZERO);
                TermVo termVo = termLogic.getTermVoByCampusIdCache(eduClassInfoLinkVO.getSchoolId(), eduClassInfoLinkVO.getCampusId(), eduClassInfoLinkVO.getCampusSectionId(), reportReviewDetail.getReportStartTime());
                if (termVo != null) {
                    // 组装初始分信息
                    InitialScorePO initialScorePO = initialScorePOS
                            .stream()
                            .filter(item -> Objects.equals(item.getSchoolYear(), termVo.getSchoolYear())
                                    && Objects.equals(item.getTermName(), termVo.getTermName())
                                    && Objects.equals(item.getInitialScoreType(), 0))
                            .findFirst()
                            .orElse(null);
                    if (Objects.nonNull(initialScorePO)) {
                        BigDecimal totalScore = initialScorePO.getInitialScore();
                        if ("--".equals(reportSendDetailInfoVO.getCompositeScore())) {
                            reportSendDetailInfoVO.setCompositeScore("0");
                        }
                        reportSendDetailInfoVO.setInitialScore(totalScore);
                        reportSendDetailInfoVO.setCompositeScore(NumberUtil.round(NumberUtil.add(Convert.toBigDecimal(reportSendDetailInfoVO.getCompositeScore()), totalScore), 2).toPlainString());

                    }
                    if (!behaviourRecordMap.containsKey(reportReviewDetail.getStudentId()) && Objects.isNull(initialScorePO)) {
                        //无明细
                        reportSendDetailInfoVO.setCompositeScore("--");
                    }
                }
            }
            result.add(reportSendDetailInfoVO);
        }
        return result.stream().sorted(Comparator.comparing(ReportSendDetailInfoVO::getSendTime, Comparator.nullsLast(Date::compareTo)).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取默认推送规则
     *
     * @param reviewTaskId 审核任务id
     * @return {@link ReportReviewRuleVO}
     */
    @Override
    public ReportReviewRuleVO getReportReviewRule(Long reviewTaskId) {
        Assert.notNull(reviewTaskId, () -> new BizException(ReportErrorEnum.EMPTY_TASK_ID));

        ReportReviewTask reviewTask = reportReviewTaskService.getById(reviewTaskId);
        Assert.notNull(reviewTask, () -> new BizException(ReportErrorEnum.EMPTY_TASK));

//        ReportPushRule reportPushRule = reportPushRuleService.getById(reviewTask.getRuleId());
//        Assert.notNull(reportPushRule, () -> new BizException(ReportErrorEnum.EMPTY_PUSH_RULE));

        ReportReviewRuleVO reportReviewRuleVO = new ReportReviewRuleVO();
        reportReviewRuleVO.setPushModuleIds(StrUtil.split(reviewTask.getPushModule(), StrPool.COMMA));
        return reportReviewRuleVO;
    }

    /**
     * 获取行为明细
     *
     * @param reviewDetailId 审核详情id
     * @return {@link List}<{@link ReportBehaviorDetailVO}>
     */
    @Override
    public List<ReportBehaviorDetailVO> listReportBehaviorDetail(Long reviewDetailId, List<Long> unNeedBehaviourRecordIds) {
        Assert.notNull(reviewDetailId, () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL_ID));

        ReportReviewDetail reviewDetail = reportReviewDetailService.getById(reviewDetailId);
        Assert.notNull(reviewDetail, () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL));

        ReportReviewTask reportReviewTask = reportReviewTaskService.getById(reviewDetail.getReviewTaskId());
        Assert.notNull(reportReviewTask, () -> new BizException(ReportErrorEnum.EMPTY_TASK));

        Assert.isTrue(reviewDetail.getPushModule().contains(Convert.toStr(ReportPushRuleEnum.DETAIL.getCode())), () -> new BizException(ReportErrorEnum.GET_DETAIL_ERROR));

        //学生行为明细(查全部)
        List<BehaviourRecord> behaviourRecords = behaviourRecordService.list(new LambdaQueryWrapper<BehaviourRecord>()
                .in(BehaviourRecord::getStudentId, CollUtil.newArrayList(reviewDetail.getStudentId()))
                .notIn(CollUtil.isNotEmpty(unNeedBehaviourRecordIds), BehaviourRecord::getId, unNeedBehaviourRecordIds)
                .between(BehaviourRecord::getSubmitTime, reviewDetail.getReportStartTime(), reviewDetail.getReportEndTime())
                .orderByDesc(BehaviourRecord::getSubmitTime));
        // 过滤无需统计数据
        behaviourRecords = behaviourRecords
                .stream()
                .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                .collect(Collectors.toList());

        //学生帮扶行为明细(查全部)
        HelpBehaviourRecordQueryDTO helpBehaviourRecordQueryDTO = new HelpBehaviourRecordQueryDTO();
        helpBehaviourRecordQueryDTO.setStudentId(reviewDetail.getStudentId());
        helpBehaviourRecordQueryDTO.setStartTime(reviewDetail.getReportStartTime());
        helpBehaviourRecordQueryDTO.setEndTime(reviewDetail.getReportEndTime());
        List<EvaluateHelpBehaviourRecordPO> evaluateHelpBehaviourRecordPOS = helpBehaviourRecordService.listByStudent(helpBehaviourRecordQueryDTO);
        // 过滤无需统计数据
        evaluateHelpBehaviourRecordPOS = evaluateHelpBehaviourRecordPOS
                .stream()
                .filter(item -> Objects.equals(item.getNotPartCount(), 0))
                .collect(Collectors.toList());

        // 获取名称为选项的指标名称Map
        Map<Long, String> targetNameMap = new HashMap<>();
        List<Long> targetIds = behaviourRecords.stream().filter(s -> InfoTypeEnum.OPTION.getCode().equals(s.getInfoType())).map(BehaviourRecord::getTargetId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(targetIds)) {
            List<Target> targets = targetMapper.listTargetByIds(targetIds);
            targetNameMap = targets.stream().collect(Collectors.toMap(Target::getId, Target::getTargetName));
        }

        Map<Long, String> finalTargetNameMap = targetNameMap;
        //构建行为记录表记录
        List<ReportBehaviorDetailVO> reportBehaviorDetailVOList = behaviourRecords.stream().map(s -> {
            ReportBehaviorDetailVO reportBehaviorDetailVO = new ReportBehaviorDetailVO();
            reportBehaviorDetailVO.setBehaviorDetailId(Convert.toStr(s.getId()));
            reportBehaviorDetailVO.setContent(this.getInfoName(s));
            reportBehaviorDetailVO.setTime(s.getSubmitTime());
            reportBehaviorDetailVO.setScore(Objects.nonNull(s.getScore()) ? s.getScore().setScale(2, RoundingMode.HALF_UP) : null);
            reportBehaviorDetailVO.setDataSource(s.getDataSource());
            reportBehaviorDetailVO.setPushType(BehaviourRecordPushTypeEnum.OTHER.getCode());
            reportBehaviorDetailVO.setInfoType(s.getInfoType());
            reportBehaviorDetailVO.setInfoId(s.getInfoId());
            reportBehaviorDetailVO.setTargetId(s.getTargetId());
            // 如果是选项名称的填充指标名称
            if (InfoTypeEnum.OPTION.getCode().equals(s.getInfoType())) {
                reportBehaviorDetailVO.setTargetName(finalTargetNameMap.get(s.getTargetId()));
            }
            return reportBehaviorDetailVO;
        }).collect(Collectors.toList());
        reportBehaviorDetailVOList = CollectionUtils.isEmpty(reportBehaviorDetailVOList) ? new ArrayList<>() : reportBehaviorDetailVOList;
        //构建帮扶记录表记录
        for (EvaluateHelpBehaviourRecordPO evaluateHelpBehaviourRecordPO : evaluateHelpBehaviourRecordPOS) {
            ReportBehaviorDetailVO reportBehaviorDetailVO = new ReportBehaviorDetailVO();
            reportBehaviorDetailVO.setBehaviorDetailId(Convert.toStr(evaluateHelpBehaviourRecordPO.getId()));
            reportBehaviorDetailVO.setContent(evaluateHelpBehaviourRecordPO.getHelpDesc());
            reportBehaviorDetailVO.setTime(evaluateHelpBehaviourRecordPO.getSubmitTime());
            reportBehaviorDetailVO.setScore(NumUtil.formatFloatNumber(evaluateHelpBehaviourRecordPO.getScore(), 2));
            reportBehaviorDetailVO.setDataSource(DataSourceEnum.EVALUATE_POINT_COMPUTE.getCode());
            reportBehaviorDetailVO.setPushType(BehaviourRecordPushTypeEnum.OTHER.getCode());
            reportBehaviorDetailVO.setTargetId(evaluateHelpBehaviourRecordPO.getTargetId());
            reportBehaviorDetailVO.setTargetName(evaluateHelpBehaviourRecordPO.getTargetName());
            reportBehaviorDetailVOList.add(reportBehaviorDetailVO);
        }
        Collections.sort(reportBehaviorDetailVOList, new Comparator<ReportBehaviorDetailVO>() {
            @Override
            public int compare(ReportBehaviorDetailVO o1, ReportBehaviorDetailVO o2) {
                return o2.getTime().compareTo(o1.getTime());
            }
        });
        return reportBehaviorDetailVOList;
    }

    private String getInfoName(BehaviourRecord s) {
        Integer dataSource = s.getDataSource();
        if (DataSourceEnum.MORAL_CAMPUS.getCode().equals(dataSource) || DataSourceEnum.MORAL_OFF_CAMPUS.getCode().equals(dataSource)) {
            // 重写infoName 因为没有按照五育分组 所以名称中不用加五育
            return s.getTargetName() + ModuleEnum.getModuleName(s.getModuleCode()) + s.getInfoName();
        }
        return s.getInfoName();
    }

    /**
     * 部分推送
     *
     * @param dto dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 200)
    @MethodExecuteLog
    public ReportSendResultVO reportPartSend(ReportSendDTO dto) {
        Assert.notEmpty(dto.getPushModuleIds(), () -> new BizException(ReportErrorEnum.PUSH_MODULE_ERROR));

        ReportSendResultVO reportSendResultVO = new ReportSendResultVO();
        //选择单个学生部分推送,不展示明细
        if (dto.getReviewDetailIds().size() == 1) {
            log.info("[推送报告-部分推送]-ReportSendDTO:{} ", JSONUtil.toJsonStr(dto));
            reportSendResultVO = singlePartSend(dto);
        }
        //选择多个学生部分推送,不展示明细
        if (dto.getReviewDetailIds().size() > 1) {
            log.info("[推送报告-部分推送]-ReportSendDTO:{} ", JSONUtil.toJsonStr(dto));
            //老师审核报告推送参数校验封装
            List<ReportDetailInfoDTO> reviewDetails = teacherApprovalReportParamCheck(dto);
            reportSendResultVO = approvalSendReport(reviewDetails, ReportPushTypeEnum.PART_SEND.getCode(), dto.getPushModuleIds());
        }
        return reportSendResultVO;

    }

    /**
     * 单个学生部分推送
     *
     * @param dto
     * @return
     */
    private ReportSendResultVO singlePartSend(ReportSendDTO dto) {
        Assert.notEmpty(dto.getReviewDetailIds(), () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL_ID));
        List<ReportDetailInfoDTO> reportReviewDetails = reviewDetailLogic.listReportDetailByNoApproval(dto.getReviewDetailIds());
        Assert.notEmpty(reportReviewDetails, () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL));
        ReportDetailInfoDTO reviewDetail = CollUtil.getFirst(reportReviewDetails);

        // 审核权限判断
        if (!infoService.checkAuth(reviewDetail.getStudentId(), reviewDetail.getCreateTime())) {
            log.info("学生画像报告审核,审核需求id=[{}]", dto.getReviewDetailIds());
            throw new BizException("你已不是该学生班主任，无需审核。");
        }

        //(只要没有推送成功过德都可以再次审核推送)
        Assert.isFalse(ReportReviewStatusEnum.IS_SEND.getCode().equals(reviewDetail.getStatus()), () -> new BizException(ReportErrorEnum.ERR_REVIEW_STATUS));

        ReportSendResultVO reportSendResultVO = new ReportSendResultVO();
        ReportSendResultDetailVO reportSendResultDetailVO = new ReportSendResultDetailVO();
        int successCounts = 0;
        int failCounts = 0;

        List<String> campusIds = Collections.singletonList(reviewDetail.getCampusId());
        List<ReportPushRule> reportPushRules = reportPushRuleService.listByCampusIds(campusIds);
        Map<String, List<ReportPushRule>> campusPushWayMap = CollStreamUtil.groupByKey(reportPushRules, ReportPushRule::getCampusId);

        Map<Long, List<ParentInfoVO>> parentInfoMap = this.getParentInfoMap(Collections.singletonList(reviewDetail));


        try {
            List<ReportPushRule> pushRules = campusPushWayMap.get(reviewDetail.getCampusId());
            if (CollUtil.isEmpty(pushRules)) {
                log.info("[根据校区 id 获取推送方式]，数据为空，campusId：{}", reviewDetail.getCampusId());
                return reportSendResultVO;
            }
            Integer pushWay = pushRules.get(0).getPushWay();
            SendMsgResponseDTO sendMsgResponse = new SendMsgResponseDTO();
            if (ReportDailyPushWayEnum.HAI_JX.getCode().equals(pushWay)) {
                //微信推送
                //发送报告给家长
                sendMsgResponse = this.sendWxReport(reviewDetail);
            } else if (ReportDailyPushWayEnum.FEI_SHU.getCode().equals(pushWay)) {
                List<ParentInfoPojo> parentInfos = this.getParentInfoPojos(parentInfoMap, reviewDetail.getStudentId());
                if (CollUtil.isEmpty(parentInfos)) {
                    return reportSendResultVO;
                }
                for (ParentInfoPojo parentInfo : parentInfos) {
                    if (StrUtil.isBlank(parentInfo.getMobile())) {
                        continue;
                    }
                    //飞书推送
//                    sendMsgResponse = this.sendFeiShuReport(reviewDetail, parentInfo.getMobile());
                }

            } else if (ReportDailyPushWayEnum.DING_TALK.getCode().equals(pushWay)) {
                List<ParentInfoPojo> parentInfos = this.getParentInfoPojos(parentInfoMap, reviewDetail.getStudentId());
                if (CollUtil.isEmpty(parentInfos)) {
                    return reportSendResultVO;
                }
                for (ParentInfoPojo parentInfo : parentInfos) {
                    if (StrUtil.isBlank(parentInfo.getMobile())) {
                        continue;
                    }
                    //钉钉推送
                    sendMsgResponse = this.sendDingTalkReport(reviewDetail, parentInfo.getMobile());
                }
            }

            // 未审核的数据需要审核,已审核的数据只是重新发送微信模板消息
            if (ReportReviewStatusEnum.UN_REVIEW.getCode().equals(reviewDetail.getStatus())) {
                //审核数据,保存明细(个人推送审核)
                singleApprovalReport(reviewDetail, dto, sendMsgResponse);
            }
            //消息发送成功失败判断
            if (sendMsgResponse != null && Constant.YES.equals(sendMsgResponse.getSendStatus())) {
                dealMsgSendSuccess(reviewDetail,sendMsgResponse,reportSendResultDetailVO);
                successCounts = successCounts + 1;
                reportSendResultVO.setSuccessDetails(CollUtil.newArrayList(reportSendResultDetailVO));
            } else {
                //消息发送失败
                reportSendResultDetailVO.setStudentId(reviewDetail.getStudentId());
                reportSendResultDetailVO.setStudentName(reviewDetail.getStudentName());
                reportSendResultVO.setSuccessDetails(CollUtil.newArrayList(reportSendResultDetailVO));
                failCounts = failCounts + 1;
                log.warn("学生画像报告审核成功,消息发送异常,审核数据详情:{}", reviewDetail);
            }
        } catch (BizException e) {
            //审核失败数据返回
            reportSendResultDetailVO.setStudentId(reviewDetail.getStudentId());
            reportSendResultDetailVO.setStudentName(reviewDetail.getStudentName());
            reportSendResultVO.setSuccessDetails(CollUtil.newArrayList(reportSendResultDetailVO));
            failCounts = failCounts + 1;
            log.info("学生画像报告审核数据异常:{}", reviewDetail, e);
        }
        reportSendResultVO.setSuccessCounts(successCounts);
        reportSendResultVO.setFailCounts(failCounts);
        log.info("学生画像报告审核成功,最终审核结果:{}", reportSendResultVO);
        return reportSendResultVO;
    }

    /**
     * 个人推送审核
     *
     * @param reviewDetail
     * @param dto
     * @param sendMsgResponse
     */
    private void singleApprovalReport(ReportDetailInfoDTO reviewDetail, ReportSendDTO dto, SendMsgResponseDTO sendMsgResponse) {
        //修改学生详情数据
        //修改状态为以发送
        reviewDetail.setStatus(ReportReviewStatusEnum.IS_REVIEW.getCode());
        //保存学生个人推送规则
        reviewDetail.setPushModule(StrUtil.join(StrPool.COMMA, dto.getPushModuleIds()));
        //设置推送类型
        reviewDetail.setPushType(ReportPushTypeEnum.PART_SEND.getCode());
        //设置接收时间
        reviewDetail.setReceiveTime(new Date());
        //设置审核时间
        reviewDetail.setReviewTime(new Date());
        //微信消息返回状态
        reviewDetail.setPushStatus(Objects.nonNull(sendMsgResponse) ? sendMsgResponse.getResponseCode() : "500");
        //消息推送状态(个人推送审核未作中间状态 "待审核" 处理)
        reviewDetail.setStatus(Objects.nonNull(sendMsgResponse) && Constant.YES.equals(sendMsgResponse.getSendStatus())
                ? ReportReviewStatusEnum.IS_SEND.getCode()
                : ReportReviewStatusEnum.SEND_FAIL.getCode());
        ReportReviewDetail reportReviewDetail = new ReportReviewDetail();
        BeanUtil.copyProperties(reviewDetail, reportReviewDetail);
        boolean flag = reportReviewDetailService.updateById(reportReviewDetail);
        if (flag) {
            //保存明细
            saveSingleBehaviourRecord(reviewDetail, dto);
            //更新钉钉消息状态为已审核
            dingDingMsgUtil.updateMessageStatusToApproval(reviewDetail.getReviewTaskId());
            //更新飞书消息状态为已审核
            feiShuMsgUtil.updateFeiShuMessageStatusToApproval(reviewDetail.getReviewTaskId());
            //更新【教师移动端】消息状态为已审核
            channelMsgHelper.updateTeacherMobileMessageToDone(reviewDetail.getReviewTaskId());

        }
    }

    /**
     * 个人推送审核-保存明细
     *
     * @param reviewDetail
     * @param dto
     */
    private void saveSingleBehaviourRecord(ReportDetailInfoDTO reviewDetail, ReportSendDTO dto) {
        if (dto.getPushModuleIds().contains(Convert.toStr(ReportPushRuleEnum.DETAIL.getCode())) && CollUtil.isNotEmpty(dto.getBehaviorDetailInfos())) {
            //获取本次推送的所有明细类型Map
            Map<Long, Integer> detailTypeMap = dto.getBehaviorDetailInfos().stream().collect(Collectors.toMap(ReportBehaviorDetailDTO::getBehaviorDetailId, ReportBehaviorDetailDTO::getType));

            //获取所有明细id集合
            List<Long> allDetailIds = dto.getBehaviorDetailInfos().stream().map(ReportBehaviorDetailDTO::getBehaviorDetailId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(allDetailIds)) {
                return;
            }

            //获取本次推送的所有明细(排除未勾选的明细)
            List<Long> detailIds = dto.getBehaviorDetailInfos().stream().filter(s -> !s.getType().equals(3)).map(ReportBehaviorDetailDTO::getBehaviorDetailId).distinct().collect(Collectors.toList());

            //查询学生所有明细(本期推送/本期之前推送/本期不推送)
            List<BehaviourRecord> behaviourRecords = behaviourRecordService.list(new LambdaQueryWrapper<BehaviourRecord>()
                    .in(BehaviourRecord::getStudentId, CollUtil.newArrayList(reviewDetail.getStudentId()))
                    .in(BehaviourRecord::getId, allDetailIds));
            Set<Long> behaviourIdSet = behaviourRecords.stream().map(BehaviourRecord::getId).collect(Collectors.toSet());
            List<Long> helpBehaviourRecordIdList = new ArrayList<>();
            for (Long detailId : allDetailIds) {
                //当前明细是否为行为记录表明细
                if (behaviourIdSet.contains(detailId)) {
                    continue;
                }
                //当前记录是要推送的记录
                if (detailIds.contains(detailId)) {
                    //帮扶表中的数据
                    helpBehaviourRecordIdList.add(detailId);
                }
            }
            List<EvaluateHelpBehaviourRecordPO> evaluateHelpBehaviourRecordPOS = null;
            //查师徒帮扶明细
            if (CollectionUtils.isNotEmpty(helpBehaviourRecordIdList)) {
                HelpBehaviourRecordQueryDTO helpBehaviourRecordQueryDTO = new HelpBehaviourRecordQueryDTO();
                helpBehaviourRecordQueryDTO.setStudentId(reviewDetail.getStudentId());
                helpBehaviourRecordQueryDTO.setIdList(helpBehaviourRecordIdList);
                evaluateHelpBehaviourRecordPOS = helpBehaviourRecordService.listByStudent(helpBehaviourRecordQueryDTO);
            }

            //添加报告明细
            List<ReportBehaviourRecord> reportBehaviourRecords = new ArrayList<>();
            //设置明细状态  1：已推送    2：未推送    3：不推送(老师未勾选的明细)
            for (BehaviourRecord behaviourRecord : behaviourRecords) {
                behaviourRecord.setSendType(CollUtil.isNotEmpty(detailIds) && detailIds.contains(behaviourRecord.getId())
                        ? BehaviourSendTypeEnum.IS_SEND.getCode()
                        : BehaviourSendTypeEnum.DONT_SEND.getCode());
                ReportBehaviourRecord reportBehaviourRecord = new ReportBehaviourRecord();
                reportBehaviourRecord.setBehaviourRecordId(behaviourRecord.getId());
                reportBehaviourRecord.setReviewDetailId(reviewDetail.getId());
                reportBehaviourRecord.setSchoolId(WebUtil.getSchoolId());
                reportBehaviourRecord.setTenantId(WebUtil.getTenantId());
                reportBehaviourRecord.setCampusId(WebUtil.getCampusId());
                reportBehaviourRecord.setRecordType(ReportBehaviourTypeEnum.BEHAVIOUR_RECORD.getCode());
                reportBehaviourRecord.setType(detailTypeMap.get(behaviourRecord.getId()));
                reportBehaviourRecords.add(reportBehaviourRecord);
            }
            if (CollectionUtils.isNotEmpty(evaluateHelpBehaviourRecordPOS)) {
                for (EvaluateHelpBehaviourRecordPO evaluateHelpBehaviourRecordPO : evaluateHelpBehaviourRecordPOS) {
                    ReportBehaviourRecord reportBehaviourRecord = new ReportBehaviourRecord();
                    reportBehaviourRecord.setBehaviourRecordId(evaluateHelpBehaviourRecordPO.getId());
                    reportBehaviourRecord.setReviewDetailId(reviewDetail.getId());
                    reportBehaviourRecord.setSchoolId(WebUtil.getSchoolId());
                    reportBehaviourRecord.setTenantId(WebUtil.getTenantId());
                    reportBehaviourRecord.setCampusId(WebUtil.getCampusId());
                    reportBehaviourRecord.setRecordType(ReportBehaviourTypeEnum.HELP_BEHAVIOUR_RECORD.getCode());
                    reportBehaviourRecord.setType(detailTypeMap.get(evaluateHelpBehaviourRecordPO.getId()));
                    reportBehaviourRecords.add(reportBehaviourRecord);
                }
            }
            //修改明细状态
            behaviourRecordService.updateBatchById(behaviourRecords);
            //新增报告明细
            reportBehaviourRecordService.saveBatch(reportBehaviourRecords);
        }
    }

    /**
     * 老师审核报告推送参数校验封装
     *
     * @param dto
     * @return
     */
    private List<ReportDetailInfoDTO> teacherApprovalReportParamCheck(ReportSendDTO dto) {
        log.info("[推送报告-全部推送]-ReportSendDTO:{} ", JSONUtil.toJsonStr(dto));
        Assert.notEmpty(dto.getReviewDetailIds(), () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL_ID));

        List<ReportDetailInfoDTO> reportReviewDetails = reviewDetailLogic.listReportDetailByNoApproval(dto.getReviewDetailIds());
        Assert.notEmpty(reportReviewDetails, () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL));

        for (ReportDetailInfoDTO reportReviewDetail : reportReviewDetails) {
            // 审核权限判断
            if (!infoService.checkAuth(reportReviewDetail.getStudentId(), reportReviewDetail.getCreateTime())) {
                log.info("学生画像报告审核,审核需求id=[{}]", reportReviewDetail.getId());
                throw new BizException("你已不是该学生班主任，无需审核。");
            }
        }

        log.info("[推送报告]-reportReviewDetails:{}", JSONUtil.toJsonStr(CollStreamUtil.toList(reportReviewDetails, ReportDetailInfoDTO::getId)));
        //防重检验(只要没有推送成功过德都可以再次审核推送)
        for (ReportDetailInfoDTO reportReviewDetail : reportReviewDetails) {
            Assert.isFalse(ReportReviewStatusEnum.IS_SEND.getCode().equals(reportReviewDetail.getStatus()), () -> new BizException(ReportErrorEnum.DUPLICATE_REVIEW_STATUS));
        }

        return reportReviewDetails;
    }

    /**
     * 自动审核
     *
     * @param dto
     * @return
     */
    @Override
    public ReportSendResultVO autoApprovalReportParamCheck(ReportSendDTO dto) {
        log.info("[推送报告-自动推送]-ReportSendDTO:{} ", JSONUtil.toJsonStr(dto));
        List<ReportDetailInfoDTO> reportReviewDetails = reviewDetailLogic.listReportDetailByNoApproval(dto.getReviewDetailIds());
        return approvalSendReport(reportReviewDetails, ReportPushTypeEnum.ALL_SEND.getCode(), dto.getPushModuleIds());
    }


    /**
     * 获取学生家长信息Map
     *
     * @param studentIds
     * @return
     */
    private Map<Long, List<EduParentInfoPojo>> getStudentParentInfo(List<String> studentIds) {
        List<Long> collect = studentIds.stream().map(Convert::toLong).collect(Collectors.toList());
        EduParentQueryDTO eduParentQueryDTO = new EduParentQueryDTO();
        eduParentQueryDTO.setStudentIds(collect);
        List<EduParentInfoVO> eduParentInfos = basicInfoRemote.queryParentInfoList(eduParentQueryDTO);
        List<EduParentInfoVO> voList = eduParentInfos.stream().filter(s -> CollUtil.isNotEmpty(s.getParentInfos())).collect(Collectors.toList());
        return voList.stream().collect(Collectors.toMap(EduParentInfoVO::getStudentId, EduParentInfoVO::getParentInfos));
    }

    /**
     * 全部推送
     *
     * @param dto dto
     */
    @Override
    @MethodExecuteLog
    public ReportSendResultVO teacherApprovalReport(ReportSendDTO dto) {
        log.info("[推送报告-老师全部推送]-ReportSendDTO:{} ", JSONUtil.toJsonStr(dto));
        //老师审核报告推送参数校验封装
        List<ReportDetailInfoDTO> reviewDetails = teacherApprovalReportParamCheck(dto);
        return approvalSendReport(reviewDetails, ReportPushTypeEnum.ALL_SEND.getCode(), dto.getPushModuleIds());
    }

    /**
     * 获取学生详情的前后节点
     *
     * @param reviewDetailId 审核详情id
     * @param sendType       是否已发送 1：是  0：否
     * @return {@link R}<{@link ReportPreNextNodeVO}>
     */
    @Override
    public ReportPreNextNodeVO getReportPreNextNode(Long reviewDetailId, Integer sendType) {
        Assert.notNull(reviewDetailId, () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL_ID));
        ReportReviewDetail reviewDetail = reportReviewDetailService.getById(reviewDetailId);
        Assert.notNull(reviewDetail, () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL));

        List<PreNextHandlerVO> handlerList = new ArrayList<>();
        Integer sortNum = 1;
        //已发送
        if (Constant.YES.equals(sendType)) {
            List<ReportSendDetailInfoVO> details = listSendReviewDetail(reviewDetail.getReviewTaskId());
            //新增排序值
            for (ReportSendDetailInfoVO detail : details) {
                PreNextHandlerVO preNextHandlerVO = new PreNextHandlerVO();
                preNextHandlerVO.setDetailId(Convert.toLong(detail.getReviewDetailId()));
                preNextHandlerVO.setSortNum(sortNum);
                handlerList.add(preNextHandlerVO);
                sortNum++;
            }

        }
        //未审核
        if (Constant.NO.equals(sendType)) {
            List<ReportNoReviewDetailVO> details = listReportReviewDetail(reviewDetail.getReviewTaskId(), reviewDetailId);
            //新增排序值
            for (ReportNoReviewDetailVO detail : details) {
                PreNextHandlerVO preNextHandlerVO = new PreNextHandlerVO();
                preNextHandlerVO.setDetailId(Convert.toLong(detail.getReviewDetailId()));
                preNextHandlerVO.setSortNum(sortNum);
                handlerList.add(preNextHandlerVO);
                sortNum++;
            }
        }

        //获取当前数据的排序值
        List<Integer> currentData = handlerList.stream().filter(s -> reviewDetailId.equals(s.getDetailId())).map(PreNextHandlerVO::getSortNum).collect(Collectors.toList());
        if (CollUtil.isEmpty(currentData)) {
            return new ReportPreNextNodeVO();
        }
        Integer currentNodeSortNum = currentData.get(0);

        //前一节点(该节点排序值前的最大节点)
        PreNextHandlerVO preHandlerVO = handlerList.stream().filter(s -> s.getSortNum() < currentNodeSortNum).max(Comparator.comparing(PreNextHandlerVO::getSortNum)).orElse(null);
        //后一节点(该节点排序值后的最小节点)
        PreNextHandlerVO nextHandlerVO = handlerList.stream().filter(s -> s.getSortNum() > currentNodeSortNum).min(Comparator.comparing(PreNextHandlerVO::getSortNum)).orElse(null);

        ReportPreNextNodeVO reportPreNextNodeVO = new ReportPreNextNodeVO();
        reportPreNextNodeVO.setCurrentId(Convert.toStr(reviewDetailId));
        reportPreNextNodeVO.setPreId(Objects.nonNull(preHandlerVO) ? Convert.toStr(preHandlerVO.getDetailId()) : null);
        reportPreNextNodeVO.setNextId(Objects.nonNull(nextHandlerVO) ? Convert.toStr(nextHandlerVO.getDetailId()) : null);
        return reportPreNextNodeVO;
    }

    @Override
    public List<ReportBehaviorDetailVO> listNoSendBehaviorRecord(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
        Assert.notNull(behaviourRecordQueryDTO.getReviewDetailId(), () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL_ID));

        ReportReviewDetail reviewDetail = reportReviewDetailService.getById(behaviourRecordQueryDTO.getReviewDetailId());
        Assert.notNull(reviewDetail, () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL));

        ReportReviewTask reportReviewTask = reportReviewTaskService.getById(reviewDetail.getReviewTaskId());
        Assert.notNull(reportReviewTask, () -> new BizException(ReportErrorEnum.EMPTY_TASK));

        Assert.isTrue(reviewDetail.getPushModule().contains(Convert.toStr(ReportPushRuleEnum.DETAIL.getCode())), () -> new BizException(ReportErrorEnum.GET_DETAIL_ERROR));

        //学生行为明细(查全部)
        List<BehaviourRecord> behaviourRecords = behaviourRecordService.list(new LambdaQueryWrapper<BehaviourRecord>()
                .eq(BehaviourRecord::getStudentId, reviewDetail.getStudentId())
                // 理论上只查3的 为了兼容可能因为程序造成的错误导致 出现2的情况 可以考虑把2也加上（2:未推送  3：不推送）
                .in(BehaviourRecord::getSendType, Arrays.asList(BehaviourSendTypeEnum.DONT_SEND.getCode(), BehaviourSendTypeEnum.NO_SEND.getCode()))
                .eq(BehaviourRecord::getSendType, BehaviourSendTypeEnum.DONT_SEND.getCode())
                .lt(BehaviourRecord::getSubmitTime, reviewDetail.getReportStartTime())
                .orderByDesc(BehaviourRecord::getSubmitTime));

        // 获取名称为选项的指标名称Map
        Map<Long, String> targetNameMap = new HashMap<>();
        List<Long> targetIds = behaviourRecords.stream().filter(s -> InfoTypeEnum.OPTION.getCode().equals(s.getInfoType())).map(BehaviourRecord::getTargetId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(targetIds)) {
            List<Target> targets = targetMapper.listTargetByIds(targetIds);
            targetNameMap = targets.stream().collect(Collectors.toMap(Target::getId, Target::getTargetName));
        }

        List<BehaviourRecord> noSendBehaviourRecords = behaviourRecords.stream().filter(d -> ObjectUtil.equal(BehaviourSendTypeEnum.NO_SEND.getCode(), d.getSendType())).collect(Collectors.toList());
        List<BehaviourRecord> DontSendBehaviourRecords = behaviourRecords.stream().filter(d -> ObjectUtil.equal(BehaviourSendTypeEnum.DONT_SEND.getCode(), d.getSendType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(noSendBehaviourRecords)) {
            log.error("[获取之前未推送的明细]，数据有误，学生id：{}, 报告id：{}, 报告开始时间：{}, 未推送数据：{}", reviewDetail.getStudentId(), reviewDetail.getId(), reviewDetail.getReportStartTime(), JSONUtil.toJsonStr(noSendBehaviourRecords));
        }

        Map<Long, String> finalTargetNameMap = targetNameMap;
        return DontSendBehaviourRecords.stream().map(s -> {
            ReportBehaviorDetailVO reportBehaviorDetailVO = new ReportBehaviorDetailVO();
            reportBehaviorDetailVO.setBehaviorDetailId(Convert.toStr(s.getId()));
            reportBehaviorDetailVO.setContent(s.getInfoName());
            reportBehaviorDetailVO.setTime(s.getSubmitTime());
            reportBehaviorDetailVO.setScore(s.getScore());
            reportBehaviorDetailVO.setDataSource(s.getDataSource());
            reportBehaviorDetailVO.setPushType(BehaviourRecordPushTypeEnum.OTHER.getCode());
            // 如果是选项名称的填充指标名称
            if (InfoTypeEnum.OPTION.getCode().equals(s.getInfoType())) {
                reportBehaviorDetailVO.setTargetName(finalTargetNameMap.get(s.getTargetId()));
            }

            return reportBehaviorDetailVO;
        }).collect(Collectors.toList());
    }

    /**
     * @param reviewDetails 审核报告集合
     * @param pushType      推送类型  1:部分推送选择多个学生(对比全部推送只是推送模块不同,不展示明细但也需要全部推送)   2:全部推送
     * @param pushModuleIds 默认推送规则id集合(部分推送需要)  1:综合评价 2:综合素质 3:成长趋势 4:行为表现 5:智能评价 6:评分占比 7:明细 8：学生能力模型
     */
    @Transactional(rollbackFor = Exception.class, timeout = 720)
    public ReportSendResultVO approvalSendReport(List<ReportDetailInfoDTO> reviewDetails, Integer pushType, List<String> pushModuleIds) {
        ReportSendResultVO reportSendResultVO = new ReportSendResultVO();
        AtomicInteger successCounts = new AtomicInteger();
        AtomicInteger failCounts = new AtomicInteger();
        List<ReportSendResultDetailVO> successDetails = new ArrayList<>();
        List<ReportSendResultDetailVO> failDetails = new ArrayList<>();

        // 创建一个CountDownLatch，计数器为1
        CountDownLatch latch = new CountDownLatch(reviewDetails.size());

        List<String> campusIds = reviewDetails.stream().map(ReportDetailInfoDTO::getCampusId).collect(Collectors.toList());
        List<ReportPushRule> reportPushRules = reportPushRuleService.listByCampusIds(campusIds);
        Map<String, List<ReportPushRule>> campusPushWayMap = CollStreamUtil.groupByKey(reportPushRules, ReportPushRule::getCampusId);

        Map<Long, List<ParentInfoVO>> parentInfoMap = this.getParentInfoMap(reviewDetails);

        for (ReportDetailInfoDTO reviewDetail : reviewDetails) {
            evaluateExecutor.execute(() -> {
                ReportSendResultDetailVO reportSendResultDetailVO = new ReportSendResultDetailVO();
                try {

                    List<ReportPushRule> pushRules = campusPushWayMap.get(reviewDetail.getCampusId());
                    if (CollUtil.isEmpty(pushRules)) {
                        log.info("[根据校区 id 获取推送方式]，数据为空，campusId：{}", reviewDetail.getCampusId());
                        latch.countDown();
                        return;
                    }
                    Integer pushWay = pushRules.get(0).getPushWay();
                    SendMsgResponseDTO sendMsgResponse = new SendMsgResponseDTO();
                    if (ReportDailyPushWayEnum.HAI_JX.getCode().equals(pushWay)) {
                        //微信推送
                        //发送报告给家长
                        sendMsgResponse = this.sendWxReport(reviewDetail);
                    } else if (ReportDailyPushWayEnum.FEI_SHU.getCode().equals(pushWay)) {
                        List<ParentInfoPojo> parentInfos = this.getParentInfoPojos(parentInfoMap, reviewDetail.getStudentId());
                        if (CollUtil.isEmpty(parentInfos)) {
                            latch.countDown();
                            return;
                        }
                        for (ParentInfoPojo parentInfo : parentInfos) {
                            if (StrUtil.isBlank(parentInfo.getMobile())) {
                                latch.countDown();
                                continue;
                            }
                            //飞书推送
                            sendMsgResponse = this.sendFeiShuReport(reviewDetail, parentInfo.getMobile());
                        }

                    } else if (ReportDailyPushWayEnum.DING_TALK.getCode().equals(pushWay)) {
                        List<ParentInfoPojo> parentInfos = this.getParentInfoPojos(parentInfoMap, reviewDetail.getStudentId());
                        if (CollUtil.isEmpty(parentInfos)) {
                            latch.countDown();
                            return;
                        }
                        for (ParentInfoPojo parentInfo : parentInfos) {
                            if (StrUtil.isBlank(parentInfo.getMobile())) {
                                latch.countDown();
                                continue;
                            }
                            //钉钉推送
                            sendMsgResponse = this.sendDingTalkReport(reviewDetail, parentInfo.getMobile());
                        }
                    }


                    // 未审核的数据需要审核,已审核的数据只是重新发送微信模板消息
                    if (ReportReviewStatusEnum.UN_REVIEW.getCode().equals(reviewDetail.getStatus())) {
                        //修改审核数据状态-已审核
                        HandleStatusDTO toApprovalStatus = updateReportDetailStatusToApproval(reviewDetail, pushType, pushModuleIds);

                        //修改审核数据状态-已发送
                        HandleStatusDTO toSendStatus = updateReportDetailStatusToSend(reviewDetail, sendMsgResponse, toApprovalStatus);

                        //保存推送明细
                        HandleStatusDTO saveBehaviorStatus = updateBehaviourRecordSendType(reviewDetail, reviewDetail.getReportStartTime(), reviewDetail.getReportEndTime(), toSendStatus);

                        //所有步骤操作成功后更改钉钉消息状态
                        if (saveBehaviorStatus.getFlag()) {
                            //更新钉钉消息状态为已审核
                            singleExecutor.execute(() -> dingDingMsgUtil.updateMessageStatusToApproval(reviewDetail.getReviewTaskId()));
                            //更新飞书消息状态为已审核
                            singleExecutor.execute(() -> feiShuMsgUtil.updateFeiShuMessageStatusToApproval(reviewDetail.getReviewTaskId()));
                            // 更新【教师移动端】消息状态为已审核
                            singleExecutor.execute(() -> channelMsgHelper.updateTeacherMobileMessageToDone(reviewDetail.getReviewTaskId()));
                        }
                    }

                    //消息发送成功失败判断
                    if (sendMsgResponse != null && Constant.YES.equals(sendMsgResponse.getSendStatus())) {
                        //处理消息发送成功
                        dealMsgSendSuccess(reviewDetail,sendMsgResponse,reportSendResultDetailVO);
                        successDetails.add(reportSendResultDetailVO);
                        successCounts.set(successCounts.get() + 1);
                    } else {
                        //消息发送失败
                        reportSendResultDetailVO.setStudentId(reviewDetail.getStudentId());
                        reportSendResultDetailVO.setStudentName(reviewDetail.getStudentName());
                        failDetails.add(reportSendResultDetailVO);
                        failCounts.set(failCounts.get() + 1);
                        log.warn("学生画像报告审核成功,消息发送异常,审核数据详情:{}", reviewDetail);
                    }

                } catch (Exception e) {
                    //审核失败数据返回
                    reportSendResultDetailVO.setStudentId(reviewDetail.getStudentId());
                    reportSendResultDetailVO.setStudentName(reviewDetail.getStudentName());
                    failDetails.add(reportSendResultDetailVO);
                    failCounts.set(failCounts.get() + 1);
                    log.error("学生画像报告审核数据异常:{}", reviewDetail, e);
                }
                // 减少计数器
                latch.countDown();
            });
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("学生画像报告-推送，报错", e);
        }
        reportSendResultVO.setSuccessCounts(successCounts.get());
        reportSendResultVO.setSuccessDetails(successDetails);
        reportSendResultVO.setFailCounts(failCounts.get());
        reportSendResultVO.setFailDetails(failDetails);
        log.info("学生画像报告最终审核结果:{}", reportSendResultVO);
        return reportSendResultVO;
    }

    /**
     * 处理报告单消息发送成功
     * @param reviewDetail
     * @param sendMsgResponse
     * @param reportSendResultDetailVO
     */
    private void dealMsgSendSuccess(ReportDetailInfoDTO reviewDetail, SendMsgResponseDTO sendMsgResponse, ReportSendResultDetailVO reportSendResultDetailVO) {
        ReportReviewDetail reportReviewDetail = new ReportReviewDetail();
        //查询综合得分
//        if (Objects.isNull(reviewDetail.getSumScore())) {
//            BehaviourRecordQueryDTO commonQuery = new BehaviourRecordQueryDTO();
//            commonQuery.setSchoolId(reviewDetail.getSchoolId());
//            commonQuery.setCampusId(reviewDetail.getCampusId());
//            commonQuery.setClassId(reviewDetail.getClassId());
//            commonQuery.setStudentId(reviewDetail.getStudentId());
//            commonQuery.setReviewDetailId(reviewDetail.getReviewDetailId().toString());
//            commonQuery.setStartTime(DateUtil.format(reviewDetail.getReportStartTime(), DatePattern.NORM_DATETIME_FORMAT));
//            commonQuery.setEndTime(DateUtil.format(reviewDetail.getReportEndTime(), DatePattern.NORM_DATETIME_FORMAT));
//            BehaviourRecordSynthesisScoreVO synthesisScore = behaviourRecordService.getSynthesisScoreWithoutRank(commonQuery);
//            reportReviewDetail.setSumScore(synthesisScore.getSumScore());
//        }
        //修改状态为已发送
        reportReviewDetail.setId(reviewDetail.getId());
        reportReviewDetail.setStatus(TaskApprovalEnum.APPROVAL_PASS.getCode());
        reportReviewDetail.setPushStatus(sendMsgResponse.getResponseCode());
        reportReviewDetailService.updateById(reportReviewDetail);

        //消息发送成功
        reportSendResultDetailVO.setStudentId(reviewDetail.getStudentId());
        reportSendResultDetailVO.setStudentName(reviewDetail.getStudentName());
        log.info("学生画像报告审核成功,消息发送成功,审核数据详情:{}", reviewDetail);
    }

    private SendMsgResponseDTO sendDingTalkReport(ReportDetailInfoDTO reportReviewDetail, String mobile) {
        SendMsgResponseDTO sendMsgResponseDTO = new SendMsgResponseDTO();
        MessageLog messageLog = new MessageLog();
        Map<Integer, String> pushFrequencyMap = new HashMap<>();
        pushFrequencyMap.put(2, "两");
        pushFrequencyMap.put(3, "三");
        pushFrequencyMap.put(4, "四");
        // 推钉钉消息
        OapiMessageCorpconversationAsyncsendV2Request.OA oa = new OapiMessageCorpconversationAsyncsendV2Request.OA();
        OapiMessageCorpconversationAsyncsendV2Request.Body body = new OapiMessageCorpconversationAsyncsendV2Request.Body();
        OapiMessageCorpconversationAsyncsendV2Request.Head head = new OapiMessageCorpconversationAsyncsendV2Request.Head();
        oa.setHead(head);
        head.setText("星未来");
        head.setBgcolor(titleColor);
        oa.setBody(body);
        body.setTitle("学生综合表现画像");
        body.setContent("老师给你发了孩子近" + pushFrequencyMap.get(reportReviewDetail.getPushFrequency()) + "周在校综合表现情况，快来看看吧！");
        oa.setMessageUrl(parentReceiveReportUrl + "/home/<USER>"
                + "?reviewDetailId=" + reportReviewDetail.getId()
                + "&type=" + TaskRoleTypeEnum.PARENT.getCode()
                + "&studentName=" + reportReviewDetail.getStudentName()
                + "&studentCode=" + reportReviewDetail.getStudentCode()
                + "&mobile=" + SecureUtil.aes(KEY.getBytes()).encryptHex(mobile)
                + "&campusId=" + reportReviewDetail.getCampusId()
                + "&schoolId=" + reportReviewDetail.getSchoolId()
                + "&tenantId=" + reportReviewDetail.getTenantId()
                + "&studentId=" + getStudentId(reportReviewDetail.getStudentCode(), reportReviewDetail.getStudentName())
                + "&fromMsg=1&entry=parents");
        Long ddTaskId = 0L;

        try {
//            ddTaskId = dingDingMsgUtil.sendDingOaMessageNew(oa, mobile, reportReviewDetail.getTenantId());
            ddTaskId = dingDingMsgUtil.sendDingOaMessageNewParent(oa, mobile, reportReviewDetail.getTenantId());
        } catch (Exception e) {
            log.warn("[{}-[学生画像推送]]，sendDingTalkReport发生异常", ModuleNameEnum.DINGDING.getMessage(), e);
        }
        StudentVO saasStudentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(reportReviewDetail.getSchoolId()), Convert.toLong(reportReviewDetail.getStudentId()));

        sendMsgResponseDTO.setReviewDetailId(reportReviewDetail.getId());
        sendMsgResponseDTO.setSendStatus(Constant.YES);
        sendMsgResponseDTO.setRequestParam(JSONUtil.toJsonStr(oa));

        messageLog.setContent(oa.getBody().getContent());
        messageLog.setBusinessId(reportReviewDetail.getId());
        messageLog.setBusinessType(MessageLogBusinessTypeEnum.DING_REPORT_MSG.getCode());
        messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.DING_REPORT_MSG.getMessage());
        messageLog.setMessageType(MessageLogMessageTypeEnum.DING_REPORT_MSG.getCode());
        messageLog.setTermintalType(MessageLogTerminalTypeEnum.DINGDING.getCode());
        messageLog.setSchoolId(reportReviewDetail.getSchoolId());
        messageLog.setCampusId(reportReviewDetail.getCampusId());
        messageLog.setUserId(Long.parseLong(reportReviewDetail.getStudentId()));
        messageLog.setUserType(TaskRoleTypeEnum.PARENT.getCode());
        messageLog.setTenantId(reportReviewDetail.getTenantId());
        messageLog.setSendResult(sendMsgResponseDTO.getSendResult());
        messageLog.setRequestParam(sendMsgResponseDTO.getRequestParam());
        messageLog.setCreateBy(StrUtil.isNotBlank(saasStudentVO.getHeaderMasterName()) ? saasStudentVO.getHeaderMasterName() : "admin");
        log.info("[推送报告]-保存消息推送日志");
        messageLogService.save(messageLog);

        return sendMsgResponseDTO;
    }

    private SendMsgResponseDTO sendFeiShuReport(ReportDetailInfoDTO reportReviewDetail, String mobile) {
        SendMsgResponseDTO sendMsgResponseDTO = new SendMsgResponseDTO();
        String callBackId = SnowFlakeIdUtil.nextIdStr();
        Map<Integer, String> pushFrequencyMap = new HashMap<>();
        pushFrequencyMap.put(2, "两");
        pushFrequencyMap.put(3, "三");
        pushFrequencyMap.put(4, "四");
        // 发送飞书消息
        FeiMsgReqDTO feiMsgReqDTO = new FeiMsgReqDTO();
        feiMsgReqDTO.setTitle("学生综合表现画像");
        feiMsgReqDTO.setMsgUrl(parentReceiveReportUrl + "/home/<USER>"
                + "?reviewDetailId=" + reportReviewDetail.getId()
                + "&type=" + TaskRoleTypeEnum.PARENT.getCode()
                + "&studentName=" + reportReviewDetail.getStudentName()
                + "&studentCode=" + reportReviewDetail.getStudentCode()
                + "&mobile=" + SecureUtil.aes(KEY.getBytes()).encryptHex(mobile)
                + "&campusId=" + reportReviewDetail.getCampusId()
                + "&schoolId=" + reportReviewDetail.getSchoolId()
                + "&tenantId=" + reportReviewDetail.getTenantId()
                + "&studentId=" + getStudentId(reportReviewDetail.getStudentCode(), reportReviewDetail.getStudentName())
                + "&fromMsg=1&entry=parents");
        feiMsgReqDTO.setContext("老师给你发了孩子近" + pushFrequencyMap.get(reportReviewDetail.getPushFrequency()) + "周在校综合表现情况，快来看看吧！");
        feiMsgReqDTO.setMobile(mobile);
        feiMsgReqDTO.setCallBackId(callBackId);
        FeiShuResultDTO feiShuResultDTO = new FeiShuResultDTO();
        try {
            feiShuResultDTO = feiShuMsgUtil.sendTextMarkdownMessage(feiMsgReqDTO);
        } catch (Exception e) {
            log.warn("{}-[学生画像推送]，飞书消息发生异常", ModuleNameEnum.FEISHU.getMessage(), e);
        }

        StudentVO saasStudentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(reportReviewDetail.getSchoolId()), Convert.toLong(reportReviewDetail.getStudentId()));
        sendMsgResponseDTO.setReviewDetailId(reportReviewDetail.getId());
        sendMsgResponseDTO.setSendStatus(Constant.YES);
        sendMsgResponseDTO.setRequestParam(JSONUtil.toJsonStr(feiMsgReqDTO));

        MessageLog messageLog = new MessageLog()
                .setMobile(mobile)
                .setContent(feiShuResultDTO.getContext())
                .setBusinessId(null)
                .setBusinessType(MessageLogBusinessTypeEnum.FEI_SHU_REPORT_MSG.getCode())
                .setBusinessRemark(MessageLogBusinessTypeEnum.FEI_SHU_REPORT_MSG.getMessage())
                .setMessageId(Convert.toLong(callBackId))
                .setMessageType(MessageLogMessageTypeEnum.DAILY_COMMENT.getCode())
                .setTermintalType(MessageLogTerminalTypeEnum.FEISHU.getCode())
                .setSchoolId(reportReviewDetail.getSchoolId())
                .setCampusId(reportReviewDetail.getCampusId())
                .setUserId(Long.parseLong(reportReviewDetail.getStudentId()))
                .setUserType(TaskRoleTypeEnum.PARENT.getCode())
                .setSendResult(sendMsgResponseDTO.getSendResult())
                .setRequestParam(sendMsgResponseDTO.getRequestParam())
                .setTenantId(reportReviewDetail.getTenantId());
        messageLog.setCreateBy(StrUtil.isNotBlank(saasStudentVO.getHeaderMasterName()) ? saasStudentVO.getHeaderMasterName() : "admin");
        messageLogService.save(messageLog);
        return sendMsgResponseDTO;
    }

    private Map<Long, List<ParentInfoVO>> getParentInfoMap(List<ReportDetailInfoDTO> reviewDetails) {
        List<String> studentIdStrs = reviewDetails.stream() // 内层流，将内层List转换为Stream
                .map(ReportDetailInfoDTO::getStudentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<Long> studentIds = Convert.toList(Long.class, studentIdStrs).stream().distinct().collect(Collectors.toList());
        //获取家长信息
        ParentQueryDTO parentQueryDTO = new ParentQueryDTO();
        parentQueryDTO.setStudentIds(studentIds);
        List<ParentInfoVO> eduParentInfoVOS = saasStudentManager.queryParentInfoList(parentQueryDTO);
        return CollStreamUtil.groupByKey(eduParentInfoVOS, ParentInfoVO::getStudentId);
    }


    /**
     * 获取家长信息
     *
     * @param studentIdMap 学生 id-家长 map
     * @param studentId    学生 id
     */
    private List<ParentInfoPojo> getParentInfoPojos(Map<Long, List<ParentInfoVO>> studentIdMap, String studentId) {
        List<ParentInfoVO> parentInfoVOS = studentIdMap.get(Convert.toLong(studentId));
        if (CollUtil.isEmpty(parentInfoVOS)) {
            log.info("【每日点评推送】学生{}没有家长信息 不需要推送", studentId);
            return null;
        }
        ParentInfoVO parentInfoVO = parentInfoVOS.get(0);
        List<ParentInfoPojo> parentInfos = parentInfoVO.getParentInfos();
        if (CollUtil.isEmpty(parentInfos)) {
            log.info("【每日点评推送】学生{}没有家长信息 不需要推送", studentId);
            return null;
        }
        return parentInfos;
    }

    /**
     * 获取StudentId
     *
     * @param studentCode
     * @param studentName
     * @return
     */
    private String getStudentId(String studentCode, String studentName) {
        if (StrUtil.isBlank(studentCode) || StrUtil.isBlank(studentName)) {
            log.info("[根据学号和姓名获取studentId]-studentCode或studentName为空，无法获取到学生id， {}{}", studentCode, studentName);
            return StrUtil.EMPTY;
        }
        List<StaffNOVO> staffNOVOS = basicInfoRemote.listUnderByNameNo(studentCode, studentName);
        if (CollUtil.isEmpty(staffNOVOS)) {
            log.info("[根据学号和姓名获取studentId]-staffNOVOS为空，无法获取到学生id");
            return StrUtil.EMPTY;
        }
        StaffNOVO staffNOVO = staffNOVOS.get(0);
        if (ObjectUtil.isEmpty(staffNOVO)) {
            log.info("[根据学号和姓名获取studentId]-staffNOVO为空，无法获取到学生id");
            return StrUtil.EMPTY;
        }
        return Convert.toStr(staffNOVO.getId());
    }

    private SendMsgResponseDTO sendWxReport(ReportDetailInfoDTO reportReviewDetail) {
        SendMsgResponseDTO sendMsgResponseDTO = null;
        MessageLog messageLog = new MessageLog();
        try {
            StudentVO saasStudentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(reportReviewDetail.getSchoolId()), Convert.toLong(reportReviewDetail.getStudentId()));
            Map<Integer, String> pushFrequencyMap = new HashMap<>();
            pushFrequencyMap.put(2, "两");
            pushFrequencyMap.put(3, "三");
            pushFrequencyMap.put(4, "四");

            // 推家校消息
            MsgContent content = new MsgContent();
            content.setFirst("老师给你发了孩子近" + pushFrequencyMap.get(reportReviewDetail.getPushFrequency()) + "周在校综合表现情况，快来看看吧！");
            content.setKeyword1(reportReviewDetail.getClassName());
            content.setKeyword2(saasStudentVO.getHeaderMasterName());
            content.setKeyword3(DateUtil.formatDateTime(DateUtil.date()));
            content.setKeyword4("学生综合表现画像");
            content.setRemark("请及时点击查看");

            SendMsgRequestDTO request = new SendMsgRequestDTO();
            request.setStudentCode(reportReviewDetail.getStudentCode());
            request.setContent(content);
            request.setReviewDetailId(reportReviewDetail.getId());
            request.setStudentName(reportReviewDetail.getStudentName());
            sendMsgResponseDTO = sendMsgManager.sendMsg(request);
            sendMsgResponseDTO.setReviewDetailId(reportReviewDetail.getId());

            messageLog.setContent(content.toString());
            messageLog.setBusinessId(reportReviewDetail.getId());
            messageLog.setBusinessType(MessageLogBusinessTypeEnum.HAI_REPORT_MSG.getCode());
            messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.HAI_REPORT_MSG.getMessage());
            messageLog.setMessageType(MessageLogMessageTypeEnum.HAI_REPORT_MSG.getCode());
            messageLog.setTermintalType(MessageLogTerminalTypeEnum.WECHAT.getCode());
            messageLog.setSchoolId(reportReviewDetail.getSchoolId());
            messageLog.setCampusId(reportReviewDetail.getCampusId());
            messageLog.setUserId(Long.parseLong(reportReviewDetail.getStudentId()));
            messageLog.setUserType(TaskRoleTypeEnum.PARENT.getCode());
            messageLog.setTenantId(reportReviewDetail.getTenantId());
            messageLog.setSendResult(sendMsgResponseDTO.getSendResult());
            messageLog.setRequestParam(sendMsgResponseDTO.getRequestParam());
            messageLog.setCreateBy(StrUtil.isNotBlank(saasStudentVO.getHeaderMasterName()) ? saasStudentVO.getHeaderMasterName() : "admin");
            log.info("[推送报告]-保存消息推送日志");
            messageLogService.save(messageLog);
        } catch (Exception e) {
            log.error("[推送报告]-保存消息推送日志异常,日志详情={}", JSONUtil.toJsonStr(messageLog), e);
        }

        return sendMsgResponseDTO;
    }


    /**
     * 保存推送明细
     *
     * @param reviewDetail    审核详情
     * @param reportStartTime 审核报告开始时间
     * @param reportEndTime   审核报告结束时间
     * @param preHandleStatus 上游操作状态
     */
    private HandleStatusDTO updateBehaviourRecordSendType(ReportDetailInfoDTO reviewDetail, Date reportStartTime, Date reportEndTime, HandleStatusDTO preHandleStatus) {
        HandleStatusDTO currentHandleStatus = new HandleStatusDTO();
        currentHandleStatus.setBusinessId(reviewDetail.getId());

        //只有上游操作成功,才进行后续操作
        if (Boolean.FALSE.equals(preHandleStatus.getFlag())) {
            currentHandleStatus.setFlag(Boolean.FALSE);
            return currentHandleStatus;
        }

        try {
            //所有学生明细
            List<BehaviourRecord> allBehaviourRecords = new ArrayList<>();

            //学生行为记录map
            Map<String, List<BehaviourRecord>> behaviourRecordMap = new HashMap<>();
            List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS = new ArrayList<>();
            if (Objects.nonNull(reviewDetail.getStudentId())) {
                allBehaviourRecords = behaviourRecordService.list(new LambdaQueryWrapper<BehaviourRecord>()
                        .in(BehaviourRecord::getStudentId, reviewDetail.getStudentId())
                        .between(BehaviourRecord::getSubmitTime, reportStartTime, reportEndTime));
                behaviourRecordMap = allBehaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecord::getStudentId));
                //查询师徒帮扶明细
                HelpBehaviourRecordQueryDTO helpBehaviourRecordQueryDTO = new HelpBehaviourRecordQueryDTO();
                helpBehaviourRecordQueryDTO.setStudentId(reviewDetail.getStudentId());
                helpBehaviourRecordQueryDTO.setStartTime(reportStartTime);
                helpBehaviourRecordQueryDTO.setEndTime(reportEndTime);
                helpBehaviourRecordPOS = helpBehaviourRecordService.listByStudent(helpBehaviourRecordQueryDTO);
            }
            //保存需要推送的明细记录
            List<ReportBehaviourRecord> allReportBehaviourRecords = new ArrayList<>();
            if (behaviourRecordMap.containsKey(reviewDetail.getStudentId())) {
                List<BehaviourRecord> behaviourRecords = behaviourRecordMap.get(reviewDetail.getStudentId());
                List<ReportBehaviourRecord> reportBehaviourRecords = behaviourRecords.stream().map(s -> {
                    ReportBehaviourRecord reportBehaviourRecord = new ReportBehaviourRecord();
                    reportBehaviourRecord.setBehaviourRecordId(s.getId());
                    reportBehaviourRecord.setReviewDetailId(reviewDetail.getId());
                    reportBehaviourRecord.setSchoolId(reviewDetail.getSchoolId());
                    reportBehaviourRecord.setTenantId(reviewDetail.getTenantId());
                    reportBehaviourRecord.setCampusId(reviewDetail.getCampusId());
                    reportBehaviourRecord.setRecordType(ReportBehaviourTypeEnum.BEHAVIOUR_RECORD.getCode());
                    reportBehaviourRecord.setType(BehaviourSendTypeEnum.IS_SEND.getCode());
                    //设置创建人,定时任务存的为admin
                    reportBehaviourRecord.setCreateBy(StrUtil.isNotBlank(WebUtil.getStaffId()) ? WebUtil.getStaffId() : "admin");
                    return reportBehaviourRecord;
                }).collect(Collectors.toList());
                allReportBehaviourRecords.addAll(reportBehaviourRecords);
            }
            if (CollectionUtils.isNotEmpty(helpBehaviourRecordPOS)) {
                for (EvaluateHelpBehaviourRecordPO helpBehaviourRecordPO : helpBehaviourRecordPOS) {
                    ReportBehaviourRecord reportBehaviourRecord = new ReportBehaviourRecord();
                    reportBehaviourRecord.setBehaviourRecordId(helpBehaviourRecordPO.getId());
                    reportBehaviourRecord.setReviewDetailId(reviewDetail.getId());
                    reportBehaviourRecord.setSchoolId(reviewDetail.getSchoolId());
                    reportBehaviourRecord.setTenantId(reviewDetail.getTenantId());
                    reportBehaviourRecord.setCampusId(reviewDetail.getCampusId());
                    reportBehaviourRecord.setRecordType(ReportBehaviourTypeEnum.HELP_BEHAVIOUR_RECORD.getCode());
                    reportBehaviourRecord.setType(BehaviourSendTypeEnum.IS_SEND.getCode());
                    //设置创建人,定时任务存的为admin
                    reportBehaviourRecord.setCreateBy(StrUtil.isNotBlank(WebUtil.getStaffId()) ? WebUtil.getStaffId() : "admin");
                    allReportBehaviourRecords.add(reportBehaviourRecord);
                }
            }
            if (CollUtil.isNotEmpty(allReportBehaviourRecords)) {
                reportBehaviourRecordService.saveBatch(allReportBehaviourRecords);
            }
            //修改所有明细状态
            allBehaviourRecords.forEach(s -> s.setSendType(BehaviourSendTypeEnum.IS_SEND.getCode()));
            boolean flag = behaviourRecordService.updateBatchById(allBehaviourRecords);
            currentHandleStatus.setFlag(flag);
        } catch (Exception e) {
            currentHandleStatus.setFlag(Boolean.FALSE);
            log.warn("学生画像报告审核-保存画像明细异常,reviewDetailId=[{}]", reviewDetail.getId(), e);
        }
        return currentHandleStatus;
    }


    /**
     * 修改审核数据状态-已审核
     *
     * @param reviewDetail  审核详情
     * @param pushType      推送类型  1:部分推送选择多个学生(对比全部推送只是推送模块不同,不展示明细但也需要全部推送)   2:全部推送
     * @param pushModuleIds 默认推送规则id集合(部分推送需要)  1:综合评价 2:综合素质 3:成长趋势 4:行为表现 5:智能评价 6:评分占比 7:明细 8：学生能力模型
     * @return
     */
    private HandleStatusDTO updateReportDetailStatusToApproval(ReportDetailInfoDTO reviewDetail, Integer pushType, List<String> pushModuleIds) {
        HandleStatusDTO currentHandleStatus = new HandleStatusDTO();
        currentHandleStatus.setBusinessId(reviewDetail.getId());
        ReportReviewDetail reportDetailDetail = new ReportReviewDetail();
        try {

            //修改状态为以发送
            reviewDetail.setStatus(ReportReviewStatusEnum.IS_REVIEW.getCode());
            //保存学生个人推送规则
            if (ReportPushTypeEnum.PART_SEND.getCode().equals(pushType)) {
                //部分推送
                reviewDetail.setPushModule(StrUtil.join(StrPool.COMMA, pushModuleIds));
            }
            //设置推送类型
            reviewDetail.setPushType(pushType);
            //设置审核时间
            reviewDetail.setReviewTime(new Date());
            BeanUtil.copyProperties(reviewDetail, reportDetailDetail);
            boolean flag = reportReviewDetailService.updateById(reportDetailDetail);
            currentHandleStatus.setFlag(flag);
        } catch (Exception e) {
            currentHandleStatus.setFlag(Boolean.FALSE);
            log.warn("学生画像报告审核-修改审核报告状态为已审核异常,reviewDetailId=[{}]", reviewDetail.getId(), e);
        }
        return currentHandleStatus;
    }

    /**
     * 修改审核数据状态-已发送
     *
     * @param reviewDetail    审核报告
     * @param sendWxMsgResult 微信消息响应状态
     * @param preHandleStatus 上游操作状态
     * @return
     */
    private HandleStatusDTO updateReportDetailStatusToSend(ReportDetailInfoDTO reviewDetail, SendMsgResponseDTO sendWxMsgResult, HandleStatusDTO preHandleStatus) {
        HandleStatusDTO currentHandleStatus = new HandleStatusDTO();
        currentHandleStatus.setBusinessId(reviewDetail.getId());

        //只有上游操作成功,才进行后续操作
        if (Boolean.FALSE.equals(preHandleStatus.getFlag())) {
            currentHandleStatus.setFlag(Boolean.FALSE);
            return currentHandleStatus;
        }

        try {
            // 审核成功的数据状态改为已发送
            if (Boolean.TRUE.equals(preHandleStatus.getFlag())) {
                ReportReviewDetail reportReviewDetail = new ReportReviewDetail();
                reportReviewDetail.setPushStatus(Objects.nonNull(sendWxMsgResult) ? sendWxMsgResult.getResponseCode() : "500");
                // 判断消息是否发送是否成功
                reportReviewDetail.setStatus(Objects.nonNull(sendWxMsgResult) && Constant.YES.equals(sendWxMsgResult.getSendStatus())
                        ? ReportReviewStatusEnum.IS_SEND.getCode()
                        : ReportReviewStatusEnum.SEND_FAIL.getCode());
                reportReviewDetail.setReceiveTime(new Date());
                reportReviewDetail.setId(reviewDetail.getId());

                boolean flag = reportReviewDetailService.updateById(reportReviewDetail);
                currentHandleStatus.setFlag(flag);
            }
        } catch (Exception e) {
            currentHandleStatus.setFlag(Boolean.FALSE);
            log.warn("学生画像报告审核-修改审核报告状态为已发送异常,reviewDetailId=[{}]", reviewDetail.getId(), e);
        }
        return currentHandleStatus;
    }


    /**
     * 获取无学生家长的学生数据
     *
     * @param reportReviewDetails
     * @return
     */
    private List<StudentParentVO> getStudentNoParents(List<ReportDetailInfoDTO> reportReviewDetails) {
        Map<String, String> studentNameMap = reportReviewDetails.stream().collect(Collectors.toMap(ReportDetailInfoDTO::getStudentId, ReportDetailInfoDTO::getStudentName));
        List<String> studentIds = reportReviewDetails.stream().map(ReportDetailInfoDTO::getStudentId).distinct().collect(Collectors.toList());
        //获取学生家长信息Map
        Map<Long, List<EduParentInfoPojo>> studentParentInfoMap = getStudentParentInfo(studentIds);
        //没有家长的学生数
        List<StudentParentVO> studentParents = new ArrayList<>();
        for (String studentId : studentIds) {
            //如果不存在家长
            if (!studentParentInfoMap.containsKey(Convert.toLong(studentId))) {
                StudentParentVO studentParentVO = new StudentParentVO();
                studentParentVO.setStudentId(studentId);
                studentParentVO.setStudentName(studentNameMap.getOrDefault(studentId, null));
                studentParents.add(studentParentVO);
            }
        }
        return studentParents;
    }


//    /**
//     * 更新钉钉消息状态为已审核
//     *
//     * @param taskId 任务id
//     */
//    public void updateTaskMessageStatus(Long taskId,Integer businessType ,Integer messageType) {
//        log.info("[推送报告]-更新钉钉消息状态为已审核, taskId：{}", JSONUtil.toJsonStr(taskId));
//        List<MessageLog> messageLogs = messageLogService.list(new LambdaQueryWrapper<MessageLog>()
//                .eq(MessageLog::getBusinessId, taskId)
//                .eq(MessageLog::getBusinessType, businessType)
//                .eq(MessageLog::getMessageType, messageType));
//        if (CollUtil.isNotEmpty(messageLogs)) {
//            for (MessageLog messageLog : messageLogs) {
//                // 更新钉钉消息
//                if (messageLog.getMessageId() != null) {
//                    ChangeOaStatusReq changeOaStatusReq = new ChangeOaStatusReq()
//                            .setStatusValue("已审核")
//                            .setTaskId(messageLog.getMessageId())
//                            .setStatusBg("0xFFF65E5E");
//                    try {
//                        dingDingMsgUtil.updateStatus(changeOaStatusReq);
//                    } catch (Exception e) {
//                        log.warn("钉钉消息更新失败 e", e);
//                    }
//                }
//            }
//        }
//    }
}
