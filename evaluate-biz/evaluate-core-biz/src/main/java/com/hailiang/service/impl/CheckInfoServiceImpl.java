package com.hailiang.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Range;
import com.hailiang.common.lock.annotation.RLock;
import com.hailiang.constant.Constant;
import com.hailiang.constant.RoleConstant;
import com.hailiang.convert.CheckClassInfoConvert;
import com.hailiang.convert.CheckInfoConvert;
import com.hailiang.enums.*;
import com.hailiang.exception.BizException;
import com.hailiang.helper.check.info.CheckMessageHandle;
import com.hailiang.helper.check.info.ItemInfoHandle;
import com.hailiang.logic.RoleLogic;
import com.hailiang.logic.TermLogic;
import com.hailiang.manager.*;
import com.hailiang.mapper.CheckAppealInfoMapper;
import com.hailiang.mapper.CheckClassInfoMapper;
import com.hailiang.mapper.mongo.CheckClassInfoDao;
import com.hailiang.mapper.mongo.TargetTemplateDao;
import com.hailiang.model.datastatistics.vo.CheckPermissionVo;
import com.hailiang.model.dto.check.*;
import com.hailiang.model.dto.check.appeal.*;
import com.hailiang.model.dto.check.form.CheckSubmitFormDTO;
import com.hailiang.model.dto.query.CheckerClassInfoDTO;
import com.hailiang.model.dto.query.QueryStudentListWithRoomDTO;
import com.hailiang.model.entity.CheckAppealInfo;
import com.hailiang.model.entity.CheckAppealInfoDetail;
import com.hailiang.model.entity.CheckClassInfo;
import com.hailiang.model.entity.CheckClassRelation;
import com.hailiang.model.entity.mongo.CheckClassInfoMongodb;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.query.check.CheckInfoDetailQuery;
import com.hailiang.model.query.check.CheckInfoQuery;
import com.hailiang.model.response.check.CheckAuthConfigSaveResponse;
import com.hailiang.model.vo.StudentWithRoomInfoListVO;
import com.hailiang.model.vo.check.CheckAppealRecordVO;
import com.hailiang.model.vo.check.CheckInfoDetailVO;
import com.hailiang.model.vo.check.CheckInfoVO;
import com.hailiang.model.vo.check.DutyRecord;
import com.hailiang.model.vo.check.appel.CheckApprovalInfoVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.SaaSYardAPI;
import com.hailiang.remote.saas.dto.auth.StaffDataAuthDTO;
import com.hailiang.remote.saas.dto.auth.VerifyStaffDataAuthDTO;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.dto.role.ResRoleQueryDTO;
import com.hailiang.remote.saas.dto.staff.StaffInfoQueryDTO;
import com.hailiang.remote.saas.dto.staff.TeacherInfoQuery;
import com.hailiang.remote.saas.dto.yard.SchoolYardRoomQueryDTO;
import com.hailiang.remote.saas.enums.DutyUserTypeEnum;
import com.hailiang.remote.saas.pojo.role.ResRoleInfoPojo;
import com.hailiang.remote.saas.vo.auth.StaffDataAuthVO;
import com.hailiang.remote.saas.vo.auth.StaffDataCampusVO;
import com.hailiang.remote.saas.vo.auth.StaffDataClassVO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.educational.GlobalEduOrgTreeVO;
import com.hailiang.remote.saas.vo.role.ResStaffRoleVO;
import com.hailiang.remote.saas.vo.staff.ClassStaffVO;
import com.hailiang.remote.saas.vo.staff.StaffBatchVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.remote.saas.vo.yard.SchoolYardRoomVO;
import com.hailiang.saas.common.base.CommonResult;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.CheckAuthConfigService;
import com.hailiang.service.CheckInfoService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/17 15:50
 */
@Slf4j
@Service
public class CheckInfoServiceImpl implements CheckInfoService {

    @Resource
    private CheckClassInfoManager checkClassInfoManager;

    @Resource
    private CheckClassInfoMapper checkClassInfoMapper;

    @Resource
    private TargetTemplateDao targetTemplateDao;

    @Resource
    private CheckClassInfoDao checkClassInfoDao;

    @Resource
    private List<ItemInfoHandle> itemInfoHandles;

    @Resource
    private BasicInfoService basicInfoService;

    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private CheckClassRelationManager checkClassRelationManager;

    @Resource
    private CheckAppealInfoManager checkAppealInfoManager;

    @Resource
    private CheckAppealInfoDetailManager checkAppealInfoDetailManager;

    @Resource
    private CheckInfoConvert checkInfoConvert;

    @Resource
    private CheckAppealInfoMapper checkAppealInfoMapper;

    @Resource
    private CheckClassInfoConvert checkClassInfoConvert;
    
    @Resource
    private CheckMessageHandle checkMessageHandle;
    @Resource
    private RoleLogic roleLogic;
    @Resource
    private CheckAuthConfigService checkAuthConfigService;

    @Value("${oss.urlPrefix}")
    private String urlPrefix;
    @Resource
    private CacheSaasManager cacheSaasManager;
    @Resource
    private SaaSYardAPI saaSYardAPI;

    @Resource
    private TermLogic termLogic;

    /**
     * 获取检查项表单信息
     *
     * @param query 查询条件
     * @return
     */
    @Override
    public CheckInfoVO getCheckInfo(CheckInfoQuery query) {
        Assert.notNull(query.getId(), () -> new RuntimeException("id不能为空"));
        CheckItemBasicInfoDTO checkItemBasicInfoDTO = checkClassInfoMapper.getCheckItemBasicInfo(query.getId(), WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId());
        Assert.notNull(checkItemBasicInfoDTO, () -> new BizException("检查项被禁用或不存在"));

        CheckInfoVO checkInfoVO = new CheckInfoVO();
        checkInfoVO.setCheckDimId(checkItemBasicInfoDTO.getDimId());
        checkInfoVO.setCheckDimName(checkItemBasicInfoDTO.getDimName());
        checkInfoVO.setCheckItemId(checkItemBasicInfoDTO.getItemId());
        checkInfoVO.setCheckItemName(checkItemBasicInfoDTO.getItemName());
        TargetTemplate targetTemplate = targetTemplateDao.findById(checkItemBasicInfoDTO.getTemplateId());
        checkInfoVO.setTemplateInfoList(targetTemplate.getTemplateInfoList());

        return checkInfoVO;
    }

    /**
     * 获取检查项填报详情
     *
     * @param query 查询条件
     * @return
     */
    @Override
    public CheckInfoDetailVO getCheckInfoDetail(CheckInfoDetailQuery query) {
        Assert.notNull(query.getId(), () -> new BizException("id不能为空"));
        CheckClassInfo checkClassInfo = checkClassInfoManager.getOne(new LambdaQueryWrapper<CheckClassInfo>()
                .eq(CheckClassInfo::getId, query.getId())
                .eq(CheckClassInfo::getSchoolId, WebUtil.getSchoolId())
                .eq(CheckClassInfo::getTenantId, WebUtil.getTenantId())
                .eq(CheckClassInfo::getCampusId, WebUtil.getCampusId()));
        Assert.notNull(checkClassInfo, () -> new BizException("班级检查项信息不存在"));

        CheckClassInfoMongodb checkClassInfoMongodb = checkClassInfoDao.findById(checkClassInfo.getCheckInfoId());
        CheckInfoDetailVO checkInfoDetailVO = new CheckInfoDetailVO();
        checkInfoDetailVO.setId(query.getId());
        checkInfoDetailVO.setClassName(checkClassInfo.getClassName());
        checkInfoDetailVO.setCreateBy(checkClassInfo.getCreateBy());
        checkInfoDetailVO.setCheckItemId(checkClassInfo.getCheckItemId());
        checkInfoDetailVO.setCheckItemName(checkClassInfo.getCheckItemName());
        checkInfoDetailVO.setCheckDimId(checkClassInfo.getCheckDimId());
        checkInfoDetailVO.setCheckDimName(checkClassInfo.getCheckDimName());
        checkInfoDetailVO.setScore(checkClassInfo.getTotalScore());
        checkInfoDetailVO.setCheckDate(checkClassInfo.getCheckDate());
        checkInfoDetailVO.setCheckUserName(checkClassInfo.getCheckUserName());
        checkInfoDetailVO.setCheckRoleCode(checkClassInfo.getCheckRoleCode());
        checkInfoDetailVO.setCheckRoleName(SaaSOnDutyRoleEnum.getMessageByCode(Convert.toInt(checkClassInfo.getCheckRoleCode())));
        checkInfoDetailVO.setCheckStatus(checkClassInfo.getCheckStatus());
        checkInfoDetailVO.setAppealStatus(checkClassInfo.getAppealStatus());
        checkInfoDetailVO.setYardId(checkClassInfo.getYardId());
        checkInfoDetailVO.setYardName(checkClassInfo.getYardName());
        // 查询检查项基本信息
        List<CheckItemBasicInfoDTO> checkItemBasics = checkClassInfoMapper.listCheckItemBasicInfo(CollUtil.toList(checkClassInfo.getCheckItemId()), WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId());
        if (CollUtil.isNotEmpty(checkItemBasics)) {
            CheckItemBasicInfoDTO basicInfoDTO = CollUtil.getFirst(checkItemBasics);
            checkInfoDetailVO.setOptStudent(basicInfoDTO.getOptStudent());
            checkInfoDetailVO.setIconUrl(urlPrefix + basicInfoDTO.getIconUrl());
        }

        // 过滤出班级信息
        filterClassInfo(checkClassInfoMongodb.getSubmitInfoList(), checkClassInfo.getClassId(), checkClassInfo.getId());
        // 处理表单视频/图片url
        handleMediaUrl(checkClassInfoMongodb.getSubmitInfoList());
        checkInfoDetailVO.setSubmitInfoList(checkClassInfoMongodb.getSubmitInfoList());
        // 查询申诉记录
        List<CheckAppealInfoDetail> appealInfoDetails = checkAppealInfoDetailManager.list(new LambdaQueryWrapper<CheckAppealInfoDetail>()
                .eq(CheckAppealInfoDetail::getCheckClassInfoId, checkClassInfo.getId())
                .orderByDesc(CheckAppealInfoDetail::getId));
        if (CollUtil.isNotEmpty(appealInfoDetails)) {
            List<CheckAppealRecordVO> appealRecordVOList = appealInfoDetails.stream().map(s -> {
                CheckAppealRecordVO checkAppealRecordVO = new CheckAppealRecordVO();
                checkAppealRecordVO.setOperationUserId(s.getOperationUserId());
                checkAppealRecordVO.setOperationUserName(s.getOperationUserName());
                checkAppealRecordVO.setOperationType(s.getOperationType());
                checkAppealRecordVO.setContent(s.getContent());
                checkAppealRecordVO.setOperationTime(s.getCreateTime());
                return checkAppealRecordVO;
            }).collect(Collectors.toList());
            checkInfoDetailVO.setAppealRecord(appealRecordVOList);
        }

        //学生处主任且状态是录入的，可以手动调分
        if (StrUtil.isNotBlank(WebUtil.getStaffId())) {
            List<String> roleCodeList = basicInfoService.queryStaffRoleCodeList(WebUtil.getSchoolIdLong(), WebUtil.getStaffIdLong());
            if (roleLogic.isHaveStudentMasterRole(roleCodeList) && CheckClassInfoStatusEnum.ENTER.getCode().equals(checkClassInfo.getCheckStatus()) && !CheckAppealStatusEnum.NEED_APPROVAL.getCode().equals(checkClassInfo.getAppealStatus())) {
                checkInfoDetailVO.setEditableFlag(true);
            }
            //
            if (roleLogic.isHaveStudentMasterRole(roleCodeList)) {
                checkInfoDetailVO.setStudentMasterFlag(true);
            }
        }
        this.handleAuthConfig(checkInfoDetailVO);
        return checkInfoDetailVO;
    }

    /**
     * 处理值日记录授权配置信息。
     * 该方法用于根据提供的检查信息详情，配置相应的权限控制，例如学生和老师的角色召回权限和时间限制。
     *
     * @param checkInfoDetailVO 包含检查信息详情的VO对象，包括检查角色代码等。
     */
    private void handleAuthConfig(CheckInfoDetailVO checkInfoDetailVO) {
        // 获取当前校区的授权配置信息
        CheckAuthConfigSaveResponse checkAuthConfigSaveResponse = checkAuthConfigService.detail();
        log.info("当前校区值日记录权限配置信息：{}", JSONUtil.toJsonStr(checkAuthConfigSaveResponse));
        DateTime nowDate = DateUtil.date();
        Date checkDate = checkInfoDetailVO.getCheckDate();

        // 值日生，设置撤回标志、分钟限制和过期时间
        if (ObjectUtil.equal(checkInfoDetailVO.getCheckRoleCode(), Convert.toStr(SaaSOnDutyRoleEnum.STUDENT.getCode()))) {
            checkInfoDetailVO.setRecallFlag(checkAuthConfigSaveResponse.getDutyStudentRecallFlag());
            checkInfoDetailVO.setRecallValidDateFlag(DateUtil.compare(nowDate, DateUtil.offsetMinute(checkInfoDetailVO.getCheckDate(), checkAuthConfigSaveResponse.getDutyStudentRecallMinuteLimit())) < 0);
            checkInfoDetailVO.setRecallExpirationTime(DateUtil.offsetMinute(checkDate, checkAuthConfigSaveResponse.getDutyStudentRecallMinuteLimit()));
        }
        // 值日老师，设置撤回标志、分钟限制和过期时间
        if (ObjectUtil.equal(checkInfoDetailVO.getCheckRoleCode(), Convert.toStr(SaaSOnDutyRoleEnum.TEACHER.getCode()))) {
            checkInfoDetailVO.setRecallFlag(checkAuthConfigSaveResponse.getDutyTeacherRecallFlag());
            checkInfoDetailVO.setRecallValidDateFlag(DateUtil.compare(nowDate, DateUtil.offsetMinute(checkInfoDetailVO.getCheckDate(), checkAuthConfigSaveResponse.getDutyTeacherRecallMinuteLimit())) < 0);
            checkInfoDetailVO.setRecallExpirationTime(DateUtil.offsetMinute(checkDate, checkAuthConfigSaveResponse.getDutyTeacherRecallMinuteLimit()));
        }
        // 值日干部，设置撤回标志、分钟限制和过期时间 (值日干部一直可以撤回，最高权限)
        if (ObjectUtil.equal(checkInfoDetailVO.getCheckRoleCode(), Convert.toStr(SaaSOnDutyRoleEnum.LEADER.getCode()))) {
            checkInfoDetailVO.setRecallFlag(1);
            checkInfoDetailVO.setRecallValidDateFlag(true);
            checkInfoDetailVO.setRecallExpirationTime(null);
        }
        if (checkInfoDetailVO.getStudentMasterFlag()) {
            checkInfoDetailVO.setRecallExpirationTime(null);
        }
        // 班主任，设置审批标志、分钟限制和过期时间
        checkInfoDetailVO.setAppealFlag(checkAuthConfigSaveResponse.getDutyLeaderAppealFlag());
        checkInfoDetailVO.setAppealValidDateFlag(DateUtil.compare(nowDate, DateUtil.offsetMinute(checkInfoDetailVO.getCheckDate(), checkAuthConfigSaveResponse.getDutyLeaderAppealMinuteLimit())) < 0);

        checkInfoDetailVO.setAppealExpirationTime(DateUtil.offsetMinute(checkDate, checkAuthConfigSaveResponse.getDutyLeaderAppealMinuteLimit()));
    }

    /**
     * 处理表单视频/图片url
     *
     * @param submitInfoList 表单信息
     */
    private void handleMediaUrl(List<CheckSubmitFormDTO> submitInfoList) {
        for (CheckSubmitFormDTO checkSubmitFormDTO : submitInfoList) {
            if (SubmitInfoTypeEnum.MEDIA.getText().equals(checkSubmitFormDTO.getType())) {
                List<String> submitValueOld = JSONUtil.parseArray(checkSubmitFormDTO.getSubmitValue()).toList(String.class);
                List<String> submitValueNew = new ArrayList<>();
                for (String imgStr : submitValueOld) {
                    // 拼接视频/图片url
                    imgStr = urlPrefix + imgStr;
                    submitValueNew.add(imgStr);
                }
                checkSubmitFormDTO.setSubmitValue(submitValueNew);
            }
        }
    }

    /**
     * 过滤出班级信息
     *
     * @param submitInfoList 表单信息
     * @param classId        当前班级信息
     * @param checkInfoId    检查记录id
     */
    private void filterClassInfo(List<CheckSubmitFormDTO> submitInfoList, String classId, Long checkInfoId) {
        // 筛选出当前班级
        for (CheckSubmitFormDTO checkSubmitFormDTO : submitInfoList) {
            // 如果是班级控件(包括班级/学生信息)
            if (SubmitInfoTypeEnum.CLASS.getText().equals(checkSubmitFormDTO.getType())) {
                List<CheckEntryDetailDTO> submitInfos = new ArrayList<>();
                List<CheckEntryDetailDTO> checkEntryDetails = JSONUtil.parseArray(checkSubmitFormDTO.getSubmitValue()).toList(CheckEntryDetailDTO.class);
                // 班级数据
                List<CheckEntryDetailDTO> classInfoList = checkEntryDetails.stream().filter(s -> Objects.nonNull(s.getId())).filter(s -> SubmitInfoTypeEnum.CLASS.getText().equals(s.getType()) && s.getId().equals(classId)).collect(Collectors.toList());
                submitInfos.addAll(classInfoList);
                // 学生数据
                List<CheckEntryDetailDTO> studentInfoList = checkEntryDetails.stream().filter(s -> Objects.nonNull(s.getClassId())).filter(s -> SubmitInfoTypeEnum.STUDENT.getText().equals(s.getType()) && s.getClassId().equals(classId)).collect(Collectors.toList());
                submitInfos.addAll(studentInfoList);
                checkSubmitFormDTO.setSubmitValue(submitInfos);
            }
            // 如果是寝室控件
            if (SubmitInfoTypeEnum.DORMITORY.getText().equals(checkSubmitFormDTO.getType())) {
                List<CheckEntryDetailDTO> submitInfos = new ArrayList<>();
                // 寝室数据
                List<CheckEntryDetailDTO> checkEntryDetails = JSONUtil.parseArray(checkSubmitFormDTO.getSubmitValue()).toList(CheckEntryDetailDTO.class);
                List<CheckEntryDetailDTO> dormitoryInfoList = checkEntryDetails.stream().filter(s -> SubmitInfoTypeEnum.DORMITORY.getText().equals(s.getType())).collect(Collectors.toList());

                // 获取检查项对于的寝室关系id集合
                List<CheckClassRelation> checkClassRelations = checkClassRelationManager.list(new LambdaQueryWrapper<CheckClassRelation>() // NOSONAR
                        .eq(CheckClassRelation::getCheckClassInfoId, checkInfoId)
                        .eq(CheckClassRelation::getBusinessType, SubmitInfoTypeEnum.DORMITORY.getText()));
                List<String> dormitoryIds = checkClassRelations.stream().map(s -> Convert.toStr(s.getBusinessId())).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                // 查询寝室是否属于该班级
                for (CheckEntryDetailDTO checkEntryDetailDTO : dormitoryInfoList) {
                    // 如果属于该检查记录的寝室,则显示该寝室
                    if (dormitoryIds.contains(checkEntryDetailDTO.getId())) {
                        // 设置楼宇-楼层-房间号（产品确认历史数据不处理，就可以用存好的房间名称，如果历史数据也要展示就用这个方法）
//                        this.buildBuildAndFloorName(checkEntryDetailDTO);
                        submitInfos.add(checkEntryDetailDTO);
                    }
                }
                // 学生数据
                List<CheckEntryDetailDTO> studentInfos = checkEntryDetails.stream().filter(s -> Objects.nonNull(s.getClassId())).filter(s -> SubmitInfoTypeEnum.STUDENT.getText().equals(s.getType()) && s.getClassId().equals(classId)).collect(Collectors.toList());
                submitInfos.addAll(studentInfos);

                checkSubmitFormDTO.setSubmitValue(submitInfos);
            }
        }
    }

    /**
     * 设置楼宇-楼层-房间号
     */
    private boolean buildBuildAndFloorName(CheckEntryDetailDTO checkEntryDetailDTO) {
        SchoolYardRoomQueryDTO query = new SchoolYardRoomQueryDTO();
        query.setRoomId(Convert.toLong(checkEntryDetailDTO.getId()));
        query.setSchoolId(WebUtil.getSchoolIdLong());
        query.setWithRoomStudentCount(false);
        CommonResult<List<SchoolYardRoomVO>> listCommonResult = saaSYardAPI.queryYardRoom(query);
        if (ObjectUtil.isNull(listCommonResult) || CollUtil.isEmpty(listCommonResult.getData())) {
            log.warn("检查记录详情-根据宿舍id查询宿舍信息为空，roomId: {},schoolId:{}",
                    checkEntryDetailDTO.getId(), WebUtil.getSchoolIdLong());
            return true;
        }
        Optional<SchoolYardRoomVO> first = listCommonResult.getData().stream().findFirst();
        if (first.isPresent()) {
            SchoolYardRoomVO schoolYardRoomVO = first.get();
            checkEntryDetailDTO.setName(CharSequenceUtil.format("{}{}楼{}",
                    schoolYardRoomVO.getBuildingName(),
                    schoolYardRoomVO.getFloorName(),
                    schoolYardRoomVO.getName()));
        }
        return false;
    }

    /**
     * 提交检查项
     *
     * @param dto 提交内容
     * @return
     */
    @Override
    public Boolean saveCheckInfo(CheckInfoSubmitDTO dto) {

        Assert.notNull(dto.getCheckUserId(), () -> new BizException("检查人id不能为空"));
        Assert.notNull(dto.getCheckUserType(), () -> new BizException("检查人类型不能为空"));
        Assert.checkBetween(dto.getCheckUserType(), DutyUserTypeEnum.STUDENT.getCode(), DutyUserTypeEnum.STAFF.getCode(), () -> new BizException("检查人类型不正确"));
        Assert.notEmpty(dto.getCheckItemInfoList(), () -> new BizException("检查项信息不能为空"));

        List<Long> itemIds = dto
                .getCheckItemInfoList()
                .stream()
                .map(CheckItemInfoDTO::getItemId)
                .distinct()
                .collect(Collectors.toList());


        Assert.isTrue(ObjectUtil.equals(itemIds.size(), dto.getCheckItemInfoList().size()), () -> new BizException("存在相同的检查项"));


        for (CheckItemInfoDTO checkItemInfoDTO : dto.getCheckItemInfoList()) {
            Assert.notNull(checkItemInfoDTO.getItemId(), () -> new BizException("检查项id不能为空"));
            checkForm(checkItemInfoDTO.getSubmitInfoList());
        }


        // 校验获取当前提交人信息
        CheckUserRoleDTO checkUserRoleDTO = checkCurrentRoleInfo(dto);

        // 处理检查项内容
        for (CheckItemInfoDTO checkItemInfoDTO : dto.getCheckItemInfoList()) {
            // 查询检查项基本信息
            CheckItemBasicInfoDTO checkItemBasicInfo = checkClassInfoMapper.getCheckItemBasicInfo(
                    checkItemInfoDTO.getItemId(),
                    WebUtil.getTenantId(),
                    WebUtil.getSchoolId(),
                    WebUtil.getCampusId());

            Assert.notNull(checkItemBasicInfo, () -> new BizException(checkItemInfoDTO.getItemName() + "检查项不存在"));

            checkItemInfoDTO.setYardId(dto.getYardId());
            checkItemInfoDTO.setYardName(dto.getYardName());

            // 处理单个检查项内容
            handleCheckItemInfo(checkItemInfoDTO, checkUserRoleDTO);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean fixSaveCheckInfo(CheckInfoSubmitDTO dto, Date date) {
        Assert.notNull(dto.getCheckUserId(), () -> new BizException("检查人id不能为空"));
        Assert.notNull(dto.getCheckUserType(), () -> new BizException("检查人类型不能为空"));
        Assert.checkBetween(dto.getCheckUserType(), DutyUserTypeEnum.STUDENT.getCode(), DutyUserTypeEnum.STAFF.getCode(), () -> new BizException("检查人类型不正确"));
        Assert.notEmpty(dto.getCheckItemInfoList(), () -> new BizException("检查项信息不能为空"));

        List<Long> itemIds = dto
                .getCheckItemInfoList()
                .stream()
                .map(CheckItemInfoDTO::getItemId)
                .distinct()
                .collect(Collectors.toList());


        Assert.isTrue(ObjectUtil.equals(itemIds.size(), dto.getCheckItemInfoList().size()), () -> new BizException("存在相同的检查项"));


        for (CheckItemInfoDTO checkItemInfoDTO : dto.getCheckItemInfoList()) {
            Assert.notNull(checkItemInfoDTO.getItemId(), () -> new BizException("检查项id不能为空"));
            checkForm(checkItemInfoDTO.getSubmitInfoList());
        }


        // 校验获取当前提交人信息
        CheckUserRoleDTO checkUserRoleDTO = checkCurrentRoleInfo(dto);

        // 处理检查项内容
        for (CheckItemInfoDTO checkItemInfoDTO : dto.getCheckItemInfoList()) {
            // 查询检查项基本信息
            CheckItemBasicInfoDTO checkItemBasicInfo = checkClassInfoMapper.getCheckItemBasicInfo(
                    checkItemInfoDTO.getItemId(),
                    WebUtil.getTenantId(),
                    WebUtil.getSchoolId(),
                    WebUtil.getCampusId());

            Assert.notNull(checkItemBasicInfo, () -> new BizException(checkItemInfoDTO.getItemName() + "检查项不存在"));

            checkItemInfoDTO.setYardId(dto.getYardId());
            checkItemInfoDTO.setYardName(dto.getYardName());

            // 处理单个检查项内容
            handleCheckItemInfoV2(checkItemInfoDTO, checkUserRoleDTO, date);
        }
        return Boolean.TRUE;
    }

    /**
     * 表单校验
     *
     * @param submitInfoList 表单内容
     */
    private void checkForm(List<CheckSubmitFormDTO> submitInfoList) {
        for (CheckSubmitFormDTO checkSubmitFormDTO : submitInfoList) {

            // 如果是班级控件
            if (SubmitInfoTypeEnum.CLASS.getText().equals(checkSubmitFormDTO.getType())) {
                Assert.notNull(checkSubmitFormDTO.getIsMultiple(), () -> new BizException("isMultiple不能为空"));
                Assert.notNull(checkSubmitFormDTO.getIsStudent(), () -> new BizException("isStudent不能为空"));
                List<CheckEntryDetailDTO> submitInfos = JSONUtil.parseArray(checkSubmitFormDTO.getSubmitValue()).toList(CheckEntryDetailDTO.class);
                // 如果不是多选
                if (!checkSubmitFormDTO.getIsMultiple()) {
                    // 班级提交数据
                    List<String> classIds = submitInfos.stream().filter(s -> SubmitInfoTypeEnum.CLASS.getText().equals(s.getType())).map(CheckEntryDetailDTO::getId).collect(Collectors.toList());
                    Assert.isTrue(ObjectUtil.equals(classIds.size(), Constant.ONE), () -> new BizException("只能选择一个班级"));
                }
                // 如果不能选到学生
                if (!checkSubmitFormDTO.getIsStudent()) {
                    // 学生提交数据
                    List<String> studentIds = submitInfos.stream().filter(s -> SubmitInfoTypeEnum.STUDENT.getText().equals(s.getType())).map(CheckEntryDetailDTO::getId).collect(Collectors.toList());
                    Assert.isTrue(CollUtil.isEmpty(studentIds), () -> new BizException("未开启选取学生功能,无法添加学生"));
                }

            }
            // 如果是寝室控件
            if (SubmitInfoTypeEnum.DORMITORY.getText().equals(checkSubmitFormDTO.getType())) {
                Assert.notNull(checkSubmitFormDTO.getIsMultiple(), () -> new BizException("isMultiple不能为空"));
                Assert.notNull(checkSubmitFormDTO.getIsStudent(), () -> new BizException("isStudent不能为空"));
                List<CheckEntryDetailDTO> submitInfos = JSONUtil.parseArray(checkSubmitFormDTO.getSubmitValue()).toList(CheckEntryDetailDTO.class);

                //判断学生信息
                List<CheckEntryDetailDTO> studentInfos = submitInfos.stream().filter(s -> SubmitInfoTypeEnum.STUDENT.getText().equals(s.getType())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(studentInfos)) {
                    for (CheckEntryDetailDTO studentInfo : studentInfos) {
                        Assert.notNull(studentInfo.getDormitoryId(), () -> new BizException("学生的寝室id不能为空"));
                    }
                }

                // 寝室提交数据
                List<String> dormitoryIds = submitInfos.stream().filter(s -> SubmitInfoTypeEnum.DORMITORY.getText().equals(s.getType())).map(CheckEntryDetailDTO::getId).collect(Collectors.toList());
                // 判断寝室下是否存在学生
                for (String dormitoryId : dormitoryIds) {
                    // 查询寝室下的学生
                    QueryStudentListWithRoomDTO param = new QueryStudentListWithRoomDTO();
                    param.setCurrentId(dormitoryId);
                    param.setCurrentIdType(8);
                    List<StudentWithRoomInfoListVO> students = basicInfoService.queryStudentListWithRoomInfo(param);
                    Assert.notEmpty(students, () -> new BizException("检查项中存在无学生的寝室,请重新选取"));
                }
                // 如果不是多选
                if (!checkSubmitFormDTO.getIsMultiple()) {
                    Assert.isTrue(ObjectUtil.equals(dormitoryIds.size(), Constant.ONE), () -> new BizException("只能选择一个寝室"));
                }
                // 如果不能选到学生
                if (!checkSubmitFormDTO.getIsStudent()) {
                    // 学生提交数据
                    List<String> studentIds = submitInfos.stream().filter(s -> SubmitInfoTypeEnum.STUDENT.getText().equals(s.getType())).map(CheckEntryDetailDTO::getId).collect(Collectors.toList());
                    Assert.isTrue(CollUtil.isEmpty(studentIds), () -> new BizException("未开启选取学生功能,无法添加学生"));
                }
            }
        }


    }

    /**
     * 校验获取当前提交人信息
     *
     * @param dto 提交人信息
     */
    private CheckUserRoleDTO checkCurrentRoleInfo(CheckInfoSubmitDTO dto) {
        // 用户值日组角色列表,1-值日生,2-值日老师,3-值日干部
        CheckUserRoleDTO checkUserRoleDTO = new CheckUserRoleDTO();
        checkUserRoleDTO.setCheckUserId(Convert.toStr(dto.getCheckUserId()));

        if (DutyUserTypeEnum.STUDENT.getCode().equals(dto.getCheckUserType())) {
            log.info("[检查项提交]-[当前提交人类型为学生]-[提交人id:{}]", dto.getCheckUserId());
            // 查询学生信息信息
            EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
            eduStudentPageQueryDTO.setSchoolId(WebUtil.getSchoolIdLong());
            eduStudentPageQueryDTO.setStudentIds(CollUtil.newArrayList(dto.getCheckUserId()));
            List<EduStudentInfoVO> studentInfos = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
            Assert.notEmpty(studentInfos, () -> new BizException("无法查询到学生信息,提交检查项失败"));
            EduStudentInfoVO studentInfoVO = CollUtil.getFirst(studentInfos);

            // 查询学生值日权限
            List<Integer> roleCodes = basicInfoService.queryStudentDutyRoleList(WebUtil.getSchoolIdLong(), dto.getCheckUserId());
            Assert.notEmpty(roleCodes, () -> new BizException("该学生没有值日权限,提交检查项失败"));

            checkUserRoleDTO.setCheckUserName(studentInfoVO.getStudentName());
            checkUserRoleDTO.setCheckRoleCode("1");

        }
        if (DutyUserTypeEnum.STAFF.getCode().equals(dto.getCheckUserType())) {
            log.info("[检查项提交]-[当前提交人类型为教职工]-[提交人id:{}]", dto.getCheckUserId());
            // 查询教职工信息
            StaffInfoQueryDTO staffInfoQueryDTO = new StaffInfoQueryDTO();
            staffInfoQueryDTO.setStaffIds(CollUtil.newArrayList(dto.getCheckUserId()));
            List<StaffBatchVO> staffInfos = basicInfoService.queryBasicInfoByStaffIds(staffInfoQueryDTO);
            Assert.notEmpty(staffInfos, () -> new BizException("无法查询到教职工信息,提交检查项失败"));
            StaffBatchVO staffInfo = CollUtil.getFirst(staffInfos);

            // 查询教职工值日权限
            List<Integer> roleCodes = basicInfoService.queryStaffDutyRoleList(WebUtil.getSchoolIdLong(), dto.getCheckUserId());
            Assert.notEmpty(roleCodes, () -> new BizException("该教职工没有值日权限,提交检查项失败"));

            // 如果教职工同时拥有值日老师,值日干部角色,取值日干部角色
            Integer roleCode = roleCodes.stream().max(Comparator.comparing(Integer::intValue)).orElse(null);
            Assert.notNull(roleCode, () -> new BizException("该教职工没有值日权限,提交检查项失败"));
            checkUserRoleDTO.setCheckUserName(staffInfo.getName());
            checkUserRoleDTO.setCheckRoleCode(Convert.toStr(roleCode));
        }
        return checkUserRoleDTO;
    }

    /**
     * 获取教职工信息
     *
     * @param staffId 教职工id
     * @return
     */
    private StaffBatchVO getStaffInfo(Long staffId) {
        // 查询教职工信息
        StaffInfoQueryDTO staffInfoQueryDTO = new StaffInfoQueryDTO();
        staffInfoQueryDTO.setStaffIds(CollUtil.newArrayList(staffId));
        List<StaffBatchVO> staffInfos = basicInfoService.queryBasicInfoByStaffIds(staffInfoQueryDTO);
        Assert.notEmpty(staffInfos, () -> new BizException("无法查询到老师信息"));
        return CollUtil.getFirst(staffInfos);
    }

    /**
     * 处理单个检查项内容
     *
     * @param checkItemInfoDTO 检查项表单内容信息
     * @param checkUserRoleDTO 提交人信息
     */
    private void handleCheckItemInfo(CheckItemInfoDTO checkItemInfoDTO, CheckUserRoleDTO checkUserRoleDTO) {
        try {
            // 前置信息处理
            CheckFormInfo checkFormInfo = preHandle(checkItemInfoDTO, checkUserRoleDTO);

            // 策略选取
            ItemInfoHandle itemInfoHandle = itemInfoHandles
                    .stream()
                    .filter(s -> s.equalsStrategy(checkFormInfo.getCheckItemBasicInfo().getCheckObjType()))
                    .findFirst()
                    .orElse(null);

            if (Objects.nonNull(itemInfoHandle)) {
                itemInfoHandle.handle(checkFormInfo);
            }
        } catch (RuntimeException e) {
            log.error("[检查项提交]-[检查项表单处理异常]-[检查人信息:{}]-[表单信息:{}]", checkUserRoleDTO, checkItemInfoDTO, e);
            throw new BizException("提交失败，请稍后重试");
        }
    }

    private void handleCheckItemInfoV2(CheckItemInfoDTO checkItemInfoDTO, CheckUserRoleDTO checkUserRoleDTO, Date date) {
        try {
            // 前置信息处理
            CheckFormInfo checkFormInfo = preHandle(checkItemInfoDTO, checkUserRoleDTO);

            // 策略选取
            ItemInfoHandle itemInfoHandle = itemInfoHandles
                    .stream()
                    .filter(s -> s.equalsStrategy(checkFormInfo.getCheckItemBasicInfo().getCheckObjType()))
                    .findFirst()
                    .orElse(null);

            if (Objects.nonNull(itemInfoHandle)) {
                itemInfoHandle.handleV2(checkFormInfo, date);
            }
        } catch (RuntimeException e) {
            log.error("[检查项提交]-[检查项表单处理异常]-[检查人信息:{}]-[表单信息:{}]", checkUserRoleDTO, checkItemInfoDTO, e);
            throw new BizException("提交失败，请稍后重试");
        }
    }

    /**
     * 检查项提交前置处理
     *
     * @param checkItemInfoDTO 检查项表单内容信息
     * @param checkUserRoleDTO 提交人信息
     */
    private CheckFormInfo preHandle(CheckItemInfoDTO checkItemInfoDTO, CheckUserRoleDTO checkUserRoleDTO) {
        // 查询检查项基本信息
        CheckItemBasicInfoDTO checkItemBasicInfo = checkClassInfoMapper.getCheckItemBasicInfo(checkItemInfoDTO.getItemId(), WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId());
        Assert.notNull(checkItemBasicInfo, () -> new BizException("检查项不存在"));
        checkItemBasicInfo.setYardId(checkItemInfoDTO.getYardId());
        checkItemBasicInfo.setYardName(checkItemInfoDTO.getYardName());


        // 表单填写对象信息
        CheckItemEntryDTO checkItemEntryDTO = new CheckItemEntryDTO();
        // 检查项内的填写项内容信息
        List<CheckOptionDetailDTO> checkOptionDetails = new ArrayList<>();
        // 分值控件
        CheckScoreControl checkScoreControl = new CheckScoreControl();
        // 解析表单,提取数据
        parsingForm(checkItemInfoDTO.getSubmitInfoList(), checkItemEntryDTO, checkOptionDetails, checkScoreControl);
        // 保存mongodb信息
        CheckClassInfoMongodb checkClassInfoMongodb = new CheckClassInfoMongodb();
        checkClassInfoMongodb.setTenantId(WebUtil.getTenantId());
        checkClassInfoMongodb.setSchoolId(WebUtil.getSchoolId());
        checkClassInfoMongodb.setCampusId(WebUtil.getCampusId());
        checkClassInfoMongodb.setSubmitInfoList(checkItemInfoDTO.getSubmitInfoList());
        CheckClassInfoMongodb infoMongodb = checkClassInfoDao.save(checkClassInfoMongodb);

        // 前置信息封装
        CheckFormInfo checkFormInfo = new CheckFormInfo();
        checkFormInfo.setCheckUserRoleDTO(checkUserRoleDTO);
        checkFormInfo.setCheckClassInfoMongodb(infoMongodb);
        checkFormInfo.setCheckItemBasicInfo(checkItemBasicInfo);
        checkFormInfo.setCheckItemEntryDTO(checkItemEntryDTO);
        checkFormInfo.setCheckOptionDetails(checkOptionDetails);
        checkFormInfo.setCheckScoreControl(checkScoreControl);


        return checkFormInfo;
    }

    /**
     * 解析表单,提取数据
     *
     * @param submitInfoList     表单内容信息
     * @param checkItemEntryDTO  需要提取的表单填写对象信息
     * @param checkOptionDetails 需要提取的检查项内的填写项内容信息
     * @param checkScoreControl  分值控件
     */
    private Boolean parsingForm(List<CheckSubmitFormDTO> submitInfoList, CheckItemEntryDTO checkItemEntryDTO, List<CheckOptionDetailDTO> checkOptionDetails, CheckScoreControl checkScoreControl) {
        // 检查项中的班级信息
        List<CheckEntryDetailDTO> classInfos = new ArrayList<>();
        // 检查项中的寝室信息
        List<CheckEntryDetailDTO> dormitoryInfos = new ArrayList<>();
        // 检查项中的学生信息
        List<CheckEntryDetailDTO> studentInfos = new ArrayList<>();


        for (CheckSubmitFormDTO submitInfoSaveDTO : submitInfoList) {
            // 如果是班级控件
            if (SubmitInfoTypeEnum.CLASS.getText().equals(submitInfoSaveDTO.getType())) {
                List<CheckEntryDetailDTO> classInfoList = JSONUtil.parseArray(submitInfoSaveDTO.getSubmitValue()).toList(CheckEntryDetailDTO.class);
                // 班级提交数据
                List<CheckEntryDetailDTO> classValues = classInfoList.stream().filter(s -> SubmitInfoTypeEnum.CLASS.getText().equals(s.getType())).collect(Collectors.toList());
                classInfos.addAll(classValues);
                // 学生提交数据
                List<CheckEntryDetailDTO> studentValues = classInfoList.stream().filter(s -> SubmitInfoTypeEnum.STUDENT.getText().equals(s.getType())).collect(Collectors.toList());
                studentInfos.addAll(studentValues);
            }
            // 如果是寝室控件
            if (SubmitInfoTypeEnum.DORMITORY.getText().equals(submitInfoSaveDTO.getType())) {
                // 寝室数据
                List<CheckEntryDetailDTO> dormitoryInfoList = JSONUtil.parseArray(submitInfoSaveDTO.getSubmitValue()).toList(CheckEntryDetailDTO.class);
                // 班级提交数据
                List<CheckEntryDetailDTO> dormitoryValues = dormitoryInfoList.stream().filter(s -> SubmitInfoTypeEnum.DORMITORY.getText().equals(s.getType())).collect(Collectors.toList());
                dormitoryInfos.addAll(dormitoryValues);
                // 学生提交数据
                List<CheckEntryDetailDTO> studentValues = dormitoryInfoList.stream().filter(s -> SubmitInfoTypeEnum.STUDENT.getText().equals(s.getType())).collect(Collectors.toList());
                studentInfos.addAll(studentValues);
            }

            // 如果存在分值控件
            if (SubmitInfoTypeEnum.SCORE.getText().equals(submitInfoSaveDTO.getType())) {
                checkScoreControl.setIsScore(Boolean.TRUE);
                // 加分类型，“1”为减分，“2”为加分
                checkScoreControl.setScoreControlValue(Constant.ONE.equals(submitInfoSaveDTO.getScoreType()) ? submitInfoSaveDTO.getScore() : submitInfoSaveDTO.getScore().negate());
            }


            // 如果是单选或者多选,提取填写项内容
            if (SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(submitInfoSaveDTO.getType()) || SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(submitInfoSaveDTO.getType())) {
                // 是否开启加分 1:是  0:否
                Integer isScore = Constant.NO;
                // 判断是否开启加分
                if (Boolean.TRUE.equals(submitInfoSaveDTO.getIsScore())) {
                    isScore = Constant.YES;
                }

                // 填写项内容
                List<CheckOptionDetailDTO> optionDetail = new ArrayList<>();
                // 单选
                if (SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(submitInfoSaveDTO.getType())) {
                    if (Objects.nonNull(submitInfoSaveDTO.getSubmitValue())) {
                        CheckOptionDetailDTO checkOptionDetailDTO = JSONUtil.parseObj(submitInfoSaveDTO.getSubmitValue()).toBean(CheckOptionDetailDTO.class);
                        optionDetail.add(checkOptionDetailDTO);
                    }
                }
                // 多选
                if (SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(submitInfoSaveDTO.getType())) {
                    if (Objects.nonNull(submitInfoSaveDTO.getSubmitValue())) {
                        List<CheckOptionDetailDTO> checkOptionDetailDTOList = JSONUtil.parseArray(submitInfoSaveDTO.getSubmitValue()).toList(CheckOptionDetailDTO.class);
                        optionDetail.addAll(checkOptionDetailDTOList);
                    }
                }

                for (CheckOptionDetailDTO s : optionDetail) {
                    s.setIsScore(isScore);
                    // 判断加减分
                    s.setScoreType(submitInfoSaveDTO.getIsScore() ? s.getValue().doubleValue() >= 0 ? ScoreTypeEnum.PLUS.getCode() : ScoreTypeEnum.REDUCE.getCode() : null);
                }
                checkOptionDetails.addAll(optionDetail);
            }

            // 图片视频存相对路径
            if (SubmitInfoTypeEnum.MEDIA.getText().equals(submitInfoSaveDTO.getType())) {
                List<String> submitValueOld = JSONUtil.parseArray(submitInfoSaveDTO.getSubmitValue()).toList(String.class);
                List<String> submitValueNew = new ArrayList<>();
                for (String imgStr : submitValueOld) {
                    String[] url = imgStr.split("\\/");
                    submitValueNew.add(url[url.length - 1]);
                }
                submitInfoSaveDTO.setSubmitValue(submitValueNew);
            }
        }

        checkItemEntryDTO.setClassInfos(classInfos);
        checkItemEntryDTO.setDormitoryInfos(dormitoryInfos);
        checkItemEntryDTO.setStudentInfos(studentInfos);

        return Boolean.TRUE;
    }

    /**
     * 老师发起申诉
     *
     * @param dto 请求体
     * @return
     */
    @Override
    @Transactional()
    @RLock(name = "teacherAppeal", keys = {"#checkClassInfoId"})
    public Boolean teacherAppeal(CheckAppealInfoDTO dto) {
        if (StrUtil.isNotBlank(dto.getContent())) {
            Assert.isTrue(dto.getContent().length() <= 100, () -> new BizException("申诉理由，最多100字"));
        }
        Assert.notNull(dto.getCheckClassInfoId(), () -> new BizException("检查记录id不能为空"));

        CheckClassInfo checkClassInfo = checkClassInfoManager.getById(dto.getCheckClassInfoId());

        Assert.notNull(checkClassInfo, () -> new BizException("检查记录不存在"));
        Assert.isTrue(CheckClassInfoStatusEnum.ENTER.getCode().equals(checkClassInfo.getCheckStatus()), () -> new BizException("记录已撤回或已考评，无法申诉"));
        Assert.isTrue(ObjectUtil.equals(checkClassInfo.getAwardNum(), Constant.ZERO), () -> new BizException("存在颁发记录，无法申诉"));

        CheckAuthConfigSaveResponse detail = checkAuthConfigService.detail();
        if (ObjectUtil.isNotEmpty(detail)) {
            Assert.isTrue(detail.getDutyLeaderAppealFlag() == 1, () -> new BizException("由于权限控制，不允许申诉"));

            DateTime dateTime = DateUtil.offsetMinute(checkClassInfo.getCheckDate(), detail.getDutyLeaderAppealMinuteLimit());
            Assert.isTrue(dateTime.isAfter(new Date()), () -> new BizException("已超过申诉截止时间"));
        }

        List<CheckAppealInfo> checkAppealInfos = checkAppealInfoManager.list(new LambdaQueryWrapper<CheckAppealInfo>()
                .eq(CheckAppealInfo::getCheckClassInfoId, dto.getCheckClassInfoId()));
        Assert.isTrue(CollUtil.isEmpty(checkAppealInfos), () -> new BizException("存在申诉记录，无法提交申诉"));

        StaffBatchVO staffInfo = getStaffInfo(WebUtil.getStaffIdLong());
        CheckAppealInfo checkAppealInfo = new CheckAppealInfo();
        checkAppealInfo.setCheckClassInfoId(dto.getCheckClassInfoId());
        checkAppealInfo.setSubmitUserId(WebUtil.getStaffId());
        checkAppealInfo.setSubmitUserName(staffInfo.getName());
        checkAppealInfo.setAppealStatus(CheckAppealStatusEnum.NEED_APPROVAL.getCode());
        checkAppealInfo.setTenantId(WebUtil.getTenantId());
        checkAppealInfo.setSchoolId(WebUtil.getSchoolId());
        checkAppealInfo.setCampusId(WebUtil.getCampusId());
        checkAppealInfoManager.save(checkAppealInfo);

        checkClassInfo.setAppealStatus(CheckAppealStatusEnum.NEED_APPROVAL.getCode());
        checkClassInfoManager.updateById(checkClassInfo);

        CheckAppealInfoDetail checkAppealInfoDetail = new CheckAppealInfoDetail();
        checkAppealInfoDetail.setCheckClassInfoId(dto.getCheckClassInfoId());
        checkAppealInfoDetail.setCheckAppealInfoId(checkAppealInfo.getId());
        checkAppealInfoDetail.setOperationUserId(WebUtil.getStaffId());
        checkAppealInfoDetail.setOperationUserName(staffInfo.getName());
        checkAppealInfoDetail.setOperationType(CheckAppealOperationTypeEnum.START_APPEAL.getCode());
        checkAppealInfoDetail.setContent(dto.getContent());
        checkAppealInfoDetail.setTenantId(WebUtil.getTenantId());
        checkAppealInfoDetail.setSchoolId(WebUtil.getSchoolId());
        checkAppealInfoDetail.setCampusId(WebUtil.getCampusId());
        checkAppealInfoDetailManager.save(checkAppealInfoDetail);

        // 发送值日记录待审核通知
        checkMessageHandle.sendApprovalMessage(checkClassInfo, staffInfo.getName());

        return Boolean.TRUE;
    }

    /**
     * 管理员审核
     *
     * @param dto 请求体
     * @return
     */
    @Override
    @RLock(name = "approvalHandle", keys = {"#checkClassInfoId"})
    public Boolean approvalHandle(CheckApprovalInfoDTO dto) {
        log.info("【值日记录】-管理员审核，请求参数：{}", JSONUtil.toJsonStr(dto));
        if (StrUtil.isNotBlank(dto.getContent())) {
            Assert.isTrue(dto.getContent().length() <= 100, () -> new BizException("审核建议，最多100字"));
        }
        Assert.notNull(dto.getCheckClassInfoId(), () -> new BizException("检查记录id不能为空"));
        Assert.notNull(dto.getHandleType(), () -> new BizException("审核类型不正确"));
        Assert.checkBetween(dto.getHandleType(), 2, 3, () -> new BizException("审核类型范围不正确"));

        CheckClassInfo checkClassInfo = checkClassInfoManager.getById(dto.getCheckClassInfoId());

        Assert.notNull(checkClassInfo, () -> new BizException("检查记录不存在"));

        List<CheckAppealInfo> checkAppealInfos = checkAppealInfoManager.list(new LambdaQueryWrapper<CheckAppealInfo>()
                .eq(CheckAppealInfo::getCheckClassInfoId, dto.getCheckClassInfoId()));

        Assert.notEmpty(checkAppealInfos, () -> new BizException("申诉记录不存在，无法审核"));

        CheckAppealInfo checkAppealInfo = CollUtil.getFirst(checkAppealInfos);

        Assert.isNull(checkAppealInfo.getApprovalUserId(), () -> new BizException("此申诉记录已被管理员审核"));

        StaffBatchVO staffInfo = getStaffInfo(WebUtil.getStaffIdLong());

        checkAppealInfo.setApprovalUserId(WebUtil.getStaffId());
        checkAppealInfo.setApprovalUserName(staffInfo.getName());
        checkAppealInfo.setAppealStatus(dto.getHandleType());
        checkAppealInfoManager.updateById(checkAppealInfo);

        checkClassInfo.setAppealStatus(dto.getHandleType());
        checkClassInfoManager.updateById(checkClassInfo);

        CheckAppealInfoDetail checkAppealInfoDetail = new CheckAppealInfoDetail();
        checkAppealInfoDetail.setCheckClassInfoId(dto.getCheckClassInfoId());
        checkAppealInfoDetail.setCheckAppealInfoId(checkAppealInfo.getId());
        checkAppealInfoDetail.setOperationUserId(WebUtil.getStaffId());
        checkAppealInfoDetail.setOperationUserName(staffInfo.getName());
        checkAppealInfoDetail.setOperationType(dto.getHandleType());
        checkAppealInfoDetail.setContent(dto.getContent());
        checkAppealInfoDetail.setTenantId(WebUtil.getTenantId());
        checkAppealInfoDetail.setSchoolId(WebUtil.getSchoolId());
        checkAppealInfoDetail.setCampusId(WebUtil.getCampusId());
        checkAppealInfoDetailManager.save(checkAppealInfoDetail);

        // 审核通过,检查记录状态变为已撤回
        if (Constant.TWO.equals(dto.getHandleType())) {
            checkClassInfo.setCheckStatus(CheckClassInfoStatusEnum.WITHDRAW.getCode());
            checkClassInfoManager.updateById(checkClassInfo);
        }
        log.info("管理员审核，准备更新消息：{}", JSONUtil.toJsonStr(checkClassInfo));
        //更改审核通知的处里状态为已处里
        checkMessageHandle.updateApprovalStatus(checkClassInfo.getId());
        // 发送值日记录审核结果通知
        checkMessageHandle.sendApprovalResultMessage(checkClassInfo, dto.getHandleType(), staffInfo.getName());

        return Boolean.TRUE;
    }

    /**
     * 检查人主动撤回检查记录
     *
     * @param dto 请求体
     * @return
     */
    @Override
    @RLock(name = "initiativeWithdraw", keys = {"#checkClassInfoId"})
    public Boolean initiativeWithdraw(CheckWithdrawInfoDTO dto, boolean studentMasterFlag) {
        Assert.notNull(dto.getCheckClassInfoId(), () -> new BizException("检查记录id不能为空"));

        CheckClassInfo checkClassInfo = checkClassInfoManager.getById(dto.getCheckClassInfoId());

        Assert.notNull(checkClassInfo, () -> new BizException("检查记录不存在"));
        Assert.isTrue(CheckClassInfoStatusEnum.ENTER.getCode().equals(checkClassInfo.getCheckStatus()), () -> new BizException("检查记录已撤回或已考评，无法撤回"));
        Assert.isTrue(CheckClassInfoStatusEnum.ENTER.getCode().equals(checkClassInfo.getCheckStatus())
                && (Objects.isNull(checkClassInfo.getAppealStatus()) || CheckAppealStatusEnum.REFUSE.getCode().equals(checkClassInfo.getAppealStatus())), () -> new BizException("存在申诉记录，无法撤回"));
        Assert.isTrue(this.isCurrentTerm(checkClassInfo), () -> new BizException("不能撤回历史学期记录"));

        // 权限校验 不是值日管理员 需要校验下撤回权限
        if (!studentMasterFlag) {
            this.checkAuthConfig(checkClassInfo.getCheckDate());
        }


        checkClassInfo.setCheckStatus(CheckClassInfoStatusEnum.WITHDRAW.getCode());
        checkClassInfo.setCheckDate(new Date());
        // 如果是学生的撤回
        if (StrUtil.isNotBlank(WebUtil.getStudentIdStr())) {
            checkClassInfo.setUpdateBy(WebUtil.getStudentIdStr());
        }
        checkClassInfoManager.updateById(checkClassInfo);

        // 发送值日记录撤回通知 改为定时任务触发
//        checkMessageHandle.sendWithdrawCheckInfoMessage(checkClassInfo);
        //给值日管理员发送值日记录撤回通知
        checkMessageHandle.sendWithdrawCheckInfoMessageV1(checkClassInfo);
        return Boolean.TRUE;
    }

    private boolean isCurrentTerm(CheckClassInfo checkClassInfo) {
        TermVo currentTermVo = termLogic.getCurrentTermVo(Long.valueOf(checkClassInfo.getSchoolId()), Long.valueOf(checkClassInfo.getCampusId()), Long.valueOf(checkClassInfo.getCampusSectionId()), new Date());
        TermVo checkTermVo = termLogic.getCurrentTermVo(Long.valueOf(checkClassInfo.getSchoolId()), Long.valueOf(checkClassInfo.getCampusId()), Long.valueOf(checkClassInfo.getCampusSectionId()), checkClassInfo.getCheckDate());
        if (currentTermVo.getSchoolYear().equals(checkTermVo.getSchoolYear()) && currentTermVo.getTermName().equals(checkTermVo.getTermName())) {
            return true;
        }
        return false;
    }

    private void checkAuthConfig(Date checkDate) {
        CheckAuthConfigSaveResponse detail = checkAuthConfigService.detail();
        if (ObjectUtil.isEmpty(detail)) {
            return;
        }
        // 值日生
        if (StrUtil.isNotBlank(WebUtil.getStudentIdStr())) {
            Assert.isTrue(detail.getDutyStudentRecallFlag() == 1, () -> new BizException("权限有调整，不允许撤回"));

            DateTime dateTime = DateUtil.offsetMinute(checkDate, detail.getDutyStudentRecallMinuteLimit());
            Assert.isTrue(dateTime.isAfter(new Date()), () -> new BizException("超过撤回时限，不能再撤回啦"));
        } else {
            Assert.isTrue(detail.getDutyTeacherRecallFlag() == 1, () -> new BizException("权限有调整，不允许撤回"));

            DateTime dateTime = DateUtil.offsetMinute(checkDate, detail.getDutyTeacherRecallMinuteLimit());
            Assert.isTrue(dateTime.isAfter(new Date()), () -> new BizException("超过撤回时限，不能再撤回啦"));
        }
    }

    /**
     * 获取检查人的值日记录列表
     *
     * @param param 请求体
     * @return
     */
    @Override
    public Page<DutyRecord> listCheckerClassInfos(CheckerClassInfoDTO param) {
        Assert.notBlank(param.getSchoolYear(), () -> new BizException("学年不能为空"));
//        Assert.notBlank(param.getSectionCode(), () -> new BizException("学段不能为空"));

        Page<DutyRecord> pages = new Page<>(param.getPageNum(), param.getPageSize());

        //获取年级信息 从saas获取年级班级数据
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(param.getCampusSectionId()));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
        eduOrgQueryDTO.setSchoolYear(param.getSchoolYear());
        eduOrgQueryDTO.setModuleCode(ModuleCodeEnum.CLASS_EVALUATE.getCode());
        List<GlobalEduOrgTreeVO> eduOrgTreeVOS = basicInfoService.queryGlobalEducationalOrgTreeV2(eduOrgQueryDTO);
        if (CollUtil.isNotEmpty(eduOrgTreeVOS)) {
            param.setSectionCode(eduOrgTreeVOS.get(0).getCode());
        }
        if ("-1".equals(param.getGradeId())) {
            param.setClassId(null);
            param.setGradeId(null);
        }
        if ("-1".equals(Convert.toStr(param.getClassId()))) {
            param.setClassId(null);
        }

        // 获取值日记录
        Page<CheckClassInfo> results = checkClassInfoManager.page(new Page<>(param.getPageNum(), param.getPageSize()), new LambdaQueryWrapper<CheckClassInfo>()
                .eq(CheckClassInfo::getSchoolYear, param.getSchoolYear())
                .eq(StringUtils.isNotEmpty(param.getSectionCode()), CheckClassInfo::getCampusSectionCode, param.getSectionCode())
                .eq(CheckClassInfo::getTenantId, WebUtil.getTenantId())
                .eq(CheckClassInfo::getSchoolId, WebUtil.getSchoolId())
                .eq(CheckClassInfo::getCampusId, WebUtil.getCampusId())
                .eq(CheckClassInfo::getCampusSectionId, Convert.toStr(param.getCampusSectionId()))
                .eq(StringUtils.isNotEmpty(param.getGradeId()), CheckClassInfo::getGradeId, param.getGradeId())
                .eq(Objects.nonNull(param.getClassId()), CheckClassInfo::getClassId, Convert.toStr(param.getClassId()))
                .eq(CheckClassInfo::getCreateBy, CharSequenceUtil.isBlank(WebUtil.getStaffId()) ? WebUtil.getStudentIdStr() : WebUtil.getStaffId())
                .ge(Objects.nonNull(param.getStartTime()), CheckClassInfo::getCreateTime, param.getStartTime())
                .le(Objects.nonNull(param.getEndTime()), CheckClassInfo::getCreateTime, DateUtil.endOfDay(param.getEndTime()))
                .orderByDesc(CheckClassInfo::getCreateTime));


        if (CollUtil.isEmpty(results.getRecords())) {
            return pages;
        }

        //值日权限校验
        //教师端值日权限
        if (ObjectUtil.isNotNull(WebUtil.getStaffIdLong())) {
            List<Integer> teacherAuthList = basicInfoService.queryDutyRoleList(WebUtil.getSchoolIdLong(), WebUtil.getStaffIdLong(), DutyUserTypeEnum.STAFF.getCode());
            if (CollUtil.isEmpty(teacherAuthList)) {
                log.info("当前老师没有值日权限,staffId:[{}]", WebUtil.getStaffId());
                return pages;
            }
        }
        //学生端值日权限
        if (ObjectUtil.isNotNull(WebUtil.getStudentIdLong())) {
            List<Integer> studentAuthList = basicInfoService.queryDutyRoleList(WebUtil.getSchoolIdLong(), WebUtil.getStudentIdLong(), DutyUserTypeEnum.STUDENT.getCode());
            if (CollUtil.isEmpty(studentAuthList)) {
                log.info("当前学生没有值日权限,studentId:[{}]", WebUtil.getStudentIdStr());
                return pages;
            }
        }


        Map<Long, String> itemMap = new HashMap<>();
        List<Long> itemIds = results.getRecords().stream().map(CheckClassInfo::getCheckItemId).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(itemIds)) {
            // 查询检查项基本信息
            List<CheckItemBasicInfoDTO> checkItemBasics = checkClassInfoMapper.listCheckItemBasicInfo(itemIds, WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId());
            if (CollUtil.isNotEmpty(checkItemBasics)) {
                itemMap = checkItemBasics.stream().collect(Collectors.toMap(CheckItemBasicInfoDTO::getItemId, CheckItemBasicInfoDTO::getIconUrl));
            }
        }

        // 设置图标
        List<DutyRecord> dutyRecords = checkClassInfoConvert.toDutyRecordList(results.getRecords());
        for (DutyRecord dutyRecord : dutyRecords) {
            if (StrUtil.isNotBlank(itemMap.get(dutyRecord.getCheckItemId()))) {
                dutyRecord.setIconUrl(urlPrefix + itemMap.get(dutyRecord.getCheckItemId()));
            }
        }
        // 挂上寝室信息
        this.handleDormitory(dutyRecords);
        pages.setRecords(dutyRecords);
        pages.setCurrent(results.getCurrent());
        pages.setTotal(results.getTotal());
        return pages;
    }

    private void handleDormitory(List<DutyRecord> records) {
        List<Long> checkClassInfoIds = CollStreamUtil.toList(records, DutyRecord::getId);
        List<CheckClassRelation> checkClassRelations = checkClassRelationManager.list(new LambdaQueryWrapper<CheckClassRelation>().in(CheckClassRelation::getCheckClassInfoId, checkClassInfoIds)
                .eq(CheckClassRelation::getBusinessType, "dormitory"));
        if (CollUtil.isEmpty(checkClassRelations)) {
            return;
        }
        Map<Long, String> dormitoryMap = CollStreamUtil.toMap(checkClassRelations, CheckClassRelation::getCheckClassInfoId, CheckClassRelation::getBusinessName);
        for (DutyRecord d : records) {
            String dormitory = dormitoryMap.get(d.getId());
            if (StrUtil.isNotBlank(dormitory)) {
                d.setDormitory(dormitory);
            }
        }
    }

    /**
     * 管理员查询带审核/已审核记录列表
     *
     * @param dto 请求体
     * @return
     */
    @Override
    public Page<CheckApprovalInfoVO> listApprovalRecord(CheckApprovalQuery dto) {
        Assert.notNull(dto.getAppealStatus(), () -> new BizException("审核类型不正确"));
        Assert.checkBetween(dto.getAppealStatus(), CheckAppealStatusEnum.NEED_APPROVAL.getCode(), CheckAppealStatusEnum.PASS.getCode(), () -> new BizException("审核类型范围不正确"));
        // 查询当前用户角色信息
        List<String> roleCodes = checkRoleAuth();
        // 拥有管理员权限
        boolean studentMasterFlag = CollUtil.isNotEmpty(CollUtil.intersection(roleCodes, CollUtil.newArrayList(RoleConstant.DUTY_ADMIN)));
        Assert.isTrue(studentMasterFlag, () -> new BizException("您无管理员权限"));

        String approvalUserId = null;
        List<Integer> appealStatusList = new ArrayList<>();
        if (!CheckAppealStatusEnum.NEED_APPROVAL.getCode().equals(dto.getAppealStatus())) {
            approvalUserId = WebUtil.getStaffId();
            appealStatusList.addAll(CollUtil.newArrayList(CheckAppealStatusEnum.PASS.getCode(), CheckAppealStatusEnum.REFUSE.getCode()));
        } else {
            appealStatusList.add(CheckAppealStatusEnum.NEED_APPROVAL.getCode());
        }
        Page<CheckApprovalBasicDTO> result = checkAppealInfoMapper.listApprovalRecord(new Page(dto.getPageNum(), dto.getPageSize()), appealStatusList, approvalUserId, WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId());
        List<CheckApprovalInfoVO> checkApprovalInfoVOList = checkInfoConvert.toCheckApprovalInfoVOList(result.getRecords());
        Page<CheckApprovalInfoVO> pages = new Page<>(dto.getPageNum(), dto.getPageSize());
        pages.setRecords(checkApprovalInfoVOList);
        pages.setCurrent(result.getCurrent());
        pages.setTotal(result.getTotal());

        return pages;
    }

    /**
     * 消息通知url跳转权限校验
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean messageCheckAuthVerify(CheckMessageTypeDTO dto) {
        Assert.notNull(dto.getType(), "消息类型不能为空");
        Assert.checkBetween(dto.getType(), 1, 5, () -> new BizException("消息类型值不正确"));
        return checkApprovalAuth(dto);
    }

    /**
     * 判断周期内是否含有审核中的记录
     *
     * @param dto 请求体
     * @return
     */
    @Override
    public Boolean judgeApprovalRecord(JudgeApprovalRecordDTO dto) {
        Assert.notNull(dto.getCampusSectionId(), "学段id不能为空不能为空");
        Assert.notNull(dto.getSchoolYear(), "学年不能为空");
        Assert.notNull(dto.getStartTime(), "开始时间不能为空");
        Assert.notNull(dto.getEndTime(), "结束时间不能为空");

        // 校验是否通过
        Boolean flag = Boolean.TRUE;
        List<CheckClassInfo> classInfos = checkClassInfoManager.list(new LambdaQueryWrapper<CheckClassInfo>()
                .eq(CheckClassInfo::getTenantId, WebUtil.getTenantId())
                .eq(CheckClassInfo::getSchoolId, WebUtil.getSchoolId())
                .eq(CheckClassInfo::getCampusId, WebUtil.getCampusId())
                .eq(CheckClassInfo::getAppealStatus, CheckAppealStatusEnum.NEED_APPROVAL.getCode())
                .eq(CheckClassInfo::getCampusSectionId, dto.getCampusSectionId())
                .eq(CheckClassInfo::getSchoolYear, dto.getSchoolYear())
                .between(CheckClassInfo::getCreateTime, dto.getStartTime(), DateUtil.endOfDay(dto.getEndTime())));
        if (CollUtil.isNotEmpty(classInfos)) {
            flag = Boolean.FALSE;
        }
        return flag;
    }

    /**
     * 校验是否存在权限
     *
     * @param dto 请求体
     * @return
     */
    private Boolean checkApprovalAuth(CheckMessageTypeDTO dto) {
        StaffDataAuthDTO staffDataAuthDTO = new StaffDataAuthDTO();
        staffDataAuthDTO.setStaffId(WebUtil.getStaffIdLong());
        staffDataAuthDTO.setSchoolId(WebUtil.getSchoolIdLong());
        staffDataAuthDTO.setCampusId(WebUtil.getCampusIdLong());

        // 查询当前用户角色信息
        List<String> roleCodes = checkRoleAuth();
        if (CheckMessageTypeEnum.APPROVAL.getCode().equals(dto.getType())) {
            Assert.notNull(dto.getCheckClassInfoId(), "检查记录id不能为空");
            CheckClassInfo checkClassInfo = checkClassInfoManager.getById(dto.getCheckClassInfoId());
            Assert.notNull(checkClassInfo, () -> new BizException("检查记录不存在"));
            // 如果是待审核的数据,需要管理员角色
            if (CheckAppealStatusEnum.NEED_APPROVAL.getCode().equals(checkClassInfo.getAppealStatus())) {
                // 拥有学生处主任角色或值日管理员
                Assert.isTrue(roleCodes.contains(SaaSRoleEnum.STUDENT_DIRECTOR.getCode()) || roleCodes.contains(SaaSRoleEnum.CHECK_MANAGER.getCode()), () -> new BizException("您无审核权限，如有疑问请联系学校管理员"));
                Assert.isTrue(verifyUserDataAuth(checkClassInfo.getClassId(), staffDataAuthDTO), () -> new BizException("您无审核权限，如有疑问请联系学校管理员"));
            } else {
                // 如果是已审批(通过/拒绝的数据)或者其他无申诉状态的数据
                // 拥有学生处主任或班主任角色或值日管库员
                Assert.isTrue(roleCodes.contains(SaaSRoleEnum.STUDENT_DIRECTOR.getCode()) || roleCodes.contains(SaaSRoleEnum.CLASS_TEACHER.getCode()) || roleCodes.contains(SaaSRoleEnum.CHECK_MANAGER.getCode()), () -> new BizException("您无权查看，如有疑问请联系学校管理员"));
                Assert.isTrue(verifyUserDataAuth(checkClassInfo.getClassId(), staffDataAuthDTO), () -> new BizException("您无权查看，如有疑问请联系学校管理员"));
            }
            return Boolean.TRUE;
        } else if (CheckMessageTypeEnum.RED_FLAG_ISSUE.getCode().equals(dto.getType())) {
            // 流动红旗颁发校验
            Assert.notNull(dto.getClassId(), "班级id不能为空");
            checkMasterClassAuth(roleCodes, dto.getClassId());
            return Boolean.TRUE;
        } else if (CheckMessageTypeEnum.CHECK_INFO_WITHDRAW.getCode().equals(dto.getType())) {
            // 值日撤回班主任和值日管理员都能查看
            Assert.notNull(dto.getCheckClassInfoId(), "检查记录id不能为空");
            CheckClassInfo checkClassInfo = checkClassInfoManager.getById(dto.getCheckClassInfoId());
            Assert.notNull(checkClassInfo, () -> new BizException("检查记录不存在"));
            if (roleCodes.contains(SaaSRoleEnum.STUDENT_DIRECTOR.getCode())) {
                return Boolean.TRUE;
            }
            // 判断是否班级班主任
            boolean isMaster = checkMasterWithdrawAuth(roleCodes, Convert.toLong(checkClassInfo.getClassId()));
            // 判断是否值日管理员
            boolean isCheckManager = roleCodes.contains(SaaSRoleEnum.CHECK_MANAGER.getCode());
            Assert.isTrue(isMaster || isCheckManager, () -> new BizException("您非此班级班主任或值日管理员，无权查看，如有疑问请联系管理员"));
            return Boolean.TRUE;
        } else {
            Assert.notNull(dto.getCheckClassInfoId(), "检查记录id不能为空");
            CheckClassInfo checkClassInfo = checkClassInfoManager.getById(dto.getCheckClassInfoId());
            Assert.notNull(checkClassInfo, () -> new BizException("检查记录不存在"));
            checkMasterClassAuth(roleCodes, Convert.toLong(checkClassInfo.getClassId()));
            return Boolean.TRUE;
        }
    }

    private List<String> checkRoleAuth() {
        ResRoleQueryDTO resRoleQueryDTO = new ResRoleQueryDTO();
        resRoleQueryDTO.setStaffIds(CollUtil.newArrayList(WebUtil.getStaffIdLong()));
        resRoleQueryDTO.setId(WebUtil.getSchoolIdLong());
        resRoleQueryDTO.setType(SaasOrgQueryEnum.SCHOOL_ID.getCode());
        List<ResStaffRoleVO> resStaffRoleVOList = basicInfoRemote.queryStaffRoleList(resRoleQueryDTO);
        Assert.notEmpty(resStaffRoleVOList, () -> new BizException("你无角色权限，查看失败"));
        ResStaffRoleVO staffRoleVO = CollUtil.getFirst(resStaffRoleVOList);
        Assert.notEmpty(staffRoleVO.getRoles(), () -> new BizException("你无角色权限，查看失败"));
        List<String> roleCodes = staffRoleVO.getRoles().stream().map(ResRoleInfoPojo::getRoleCode).distinct().collect(Collectors.toList());
        Assert.notEmpty(roleCodes, () -> new BizException("你无角色权限，查看失败"));
        return roleCodes;
    }

    /**
     * 校验班主任数据权限
     *
     * @param roleCodes 用户拥有的角色
     * @param classId   班级id
     */
    private void checkMasterClassAuth(List<String> roleCodes, Long classId) {
        // 拥有班主任角色
        Assert.isTrue(roleCodes.contains(SaaSRoleEnum.CLASS_TEACHER.getCode()), () -> new BizException("您非此班级班主任，无权查看，如有疑问请联系管理员"));
        // 校验数据权限
        TeacherInfoQuery teacherInfoQuery = new TeacherInfoQuery();
        teacherInfoQuery.setClassId(classId);
        teacherInfoQuery.setType("0");
        List<ClassStaffVO> classMasterInfos = basicInfoRemote.queryTeachersByClassId(teacherInfoQuery);
        Assert.notEmpty(classMasterInfos, () -> new BizException("您非此班级班主任，无权查看，如有疑问请联系管理员"));
        ClassStaffVO classMaster = CollUtil.getFirst(classMasterInfos);
        // 比较当前当路人是否为该班级的班主任
        Assert.isTrue(ObjectUtil.equals(classMaster.getStaffId(), WebUtil.getStaffIdLong()), () -> new BizException("您非此班级班主任，无权查看，如有疑问请联系管理员"));
    }

    /**
     * 校验值日检查撤回班主任数据权限
     *
     * @param roleCodes 用户拥有的角色
     * @param classId   班级id
     */
    private Boolean checkMasterWithdrawAuth(List<String> roleCodes, Long classId) {
        // 拥有班主任角色
        Assert.isTrue(roleCodes.contains(SaaSRoleEnum.CLASS_TEACHER.getCode()), () -> new BizException("您非此班级班主任，无权查看，如有疑问请联系管理员"));
        // 校验数据权限
        TeacherInfoQuery teacherInfoQuery = new TeacherInfoQuery();
        teacherInfoQuery.setClassId(classId);
        teacherInfoQuery.setType("0");
        List<ClassStaffVO> classMasterInfos = basicInfoRemote.queryTeachersByClassId(teacherInfoQuery);
        if (CollUtil.isEmpty(classMasterInfos)) {
            log.info("【值日检查】-【撤回】-【根据班级无法查询到班级下的教师信息】-【班级id】:{}", classId);
            return false;
        }
        ClassStaffVO classMaster = CollUtil.getFirst(classMasterInfos);
        // 比较当前当路人是否为该班级的班主任
        return ObjectUtil.equals(classMaster.getStaffId(), WebUtil.getStaffIdLong());
    }

    /**
     * 验证用户数据权限
     *
     * @param classId          班级id
     * @param staffDataAuthDTO 教职工信息
     * @return
     */
    @Override
    public Boolean verifyUserDataAuth(String classId, StaffDataAuthDTO staffDataAuthDTO) {
        StaffDataAuthVO staffDataAuth = basicInfoRemote.queryStaffDataAuth(staffDataAuthDTO);
        // 如果拥有全部权限
        if (Constant.ONE.equals(staffDataAuth.getDataAuthority())) {
            return Boolean.TRUE;
        }
        // 如果没有校区数据
        if (CollUtil.isEmpty(staffDataAuth.getCampusList())) {
            return Boolean.FALSE;
        }
        // 如果没有当前校区数据
        StaffDataCampusVO staffDataCampusVO = staffDataAuth.getCampusList().stream().filter(s -> staffDataAuthDTO.getCampusId().equals(s.getCampusId())).findFirst().orElse(null);
        if (Objects.isNull(staffDataCampusVO)) {
            return Boolean.FALSE;
        }
        // 如果当前校区下没有班级数据
        if (CollUtil.isEmpty(staffDataCampusVO.getClassList())) {
            return Boolean.FALSE;
        }
        List<Long> classIds = staffDataCampusVO.getClassList().stream().filter(s -> Objects.nonNull(s.getId())).map(StaffDataClassVO::getId).distinct().collect(Collectors.toList());
        // 拥有当前班级权限
        if (classIds.contains(Convert.toLong(classId))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public CheckPermissionVo verifyUserDataAuth(VerifyStaffDataAuthDTO verifyStaffDataAuthDTO, StaffDataAuthDTO staffDataAuthDTO) {
        if (staffDataAuthDTO.getSchoolId() == null) {
            staffDataAuthDTO.setSchoolId(WebUtil.getSchoolIdLong());
        }
        if (staffDataAuthDTO.getCampusId() == null) {
            staffDataAuthDTO.setCampusId(WebUtil.getCampusIdLong());
        }
        if (staffDataAuthDTO.getStaffId() == null) {
            staffDataAuthDTO.setStaffId(WebUtil.getStaffIdLong());
        }
        StaffDataAuthVO staffDataAuth = cacheSaasManager.queryStaffDataAuth(staffDataAuthDTO);
        // 如果拥有全部权限
        if (Constant.ONE.equals(staffDataAuth.getDataAuthority())) {
            return CheckInfoConvert.INSTANCE.toCheckPermissionVo(verifyStaffDataAuthDTO);
        }
        // 如果没有校区数据
        CheckPermissionVo checkPermissionVo = new CheckPermissionVo();
        if (CollUtil.isEmpty(staffDataAuth.getCampusList())) {
            return checkPermissionVo;
        }
        // 如果没有当前校区数据
        StaffDataCampusVO staffDataCampusVO = staffDataAuth.getCampusList().stream().filter(s -> staffDataAuthDTO.getCampusId().equals(s.getCampusId())).findFirst().orElse(null);
        // 没有当前校区数据 或 当前校区下没有班级数据
        if (Objects.isNull(staffDataCampusVO) || CollUtil.isEmpty(staffDataCampusVO.getClassList())) {
            return checkPermissionVo;
        }
        List<String> gradeIdList = new ArrayList<>();
        List<String> classIdList = staffDataCampusVO.getClassList().stream().map(item -> {
            gradeIdList.add(String.valueOf(item.getGradeId()));
            return String.valueOf(item.getId());
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 拥有当前年级权限
        if (CollUtil.isEmpty(verifyStaffDataAuthDTO.getGradeIdList()) || StringUtils.isEmpty(verifyStaffDataAuthDTO.getGradeIdList().get(Constant.ZERO))) {
            checkPermissionVo.setGradeIdList(gradeIdList);
        } else {
            Collection<String> intersectionGradeIdList = CollUtil.intersection(gradeIdList, verifyStaffDataAuthDTO.getGradeIdList());
            if (CollUtil.isNotEmpty(intersectionGradeIdList)) {
                checkPermissionVo.setGradeIdList(new ArrayList<>(intersectionGradeIdList));
            }
        }
        // 拥有当前班级权限
        if (CollUtil.isEmpty(verifyStaffDataAuthDTO.getClassIdList()) || StringUtils.isEmpty(verifyStaffDataAuthDTO.getClassIdList().get(Constant.ZERO))) {
            checkPermissionVo.setClassIdList(new ArrayList<>(classIdList));
        } else {
            Collection<String> intersectionClassIdList = CollUtil.intersection(classIdList, verifyStaffDataAuthDTO.getClassIdList());
            if (CollUtil.isNotEmpty(intersectionClassIdList)) {
                checkPermissionVo.setClassIdList(new ArrayList<>(intersectionClassIdList));
            }
        }
        return checkPermissionVo;
    }

    /**
     * 获取待审核数量
     *
     * @return
     */
    @Override
    public Long getApprovalNum() {
        // 查询当前用户角色信息
        List<String> roleCodes = checkRoleAuth();
        // 拥有学生处主任角色
        boolean studentMasterFlag = CollUtil.isNotEmpty(CollUtil.intersection(roleCodes, CollUtil.newArrayList(RoleConstant.STUDENT_DIRECTOR, RoleConstant.DUTY_ADMIN)));
        Assert.isTrue(studentMasterFlag, () -> new BizException("您无管理员权限"));

        return checkAppealInfoManager.count(new LambdaQueryWrapper<CheckAppealInfo>()
                .eq(CheckAppealInfo::getSchoolId, WebUtil.getSchoolId())
                .eq(CheckAppealInfo::getCampusId, WebUtil.getCampusId())
                .eq(CheckAppealInfo::getTenantId, WebUtil.getTenantId())
                .eq(CheckAppealInfo::getAppealStatus, CheckAppealStatusEnum.NEED_APPROVAL.getCode()));
    }

    /**
     * 学生处主任手动调整分值
     *
     * @param dto 请求体
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean adjustScore(AdjustScoreDTO dto) {
        Assert.notNull(dto.getCheckClassInfoId(), () -> new BizException("检查记录id不能为空"));
        CheckClassInfo checkClassInfo = checkClassInfoManager.getById(dto.getCheckClassInfoId());
        Assert.notNull(checkClassInfo, () -> new BizException("检查记录不存在"));
        Assert.isTrue(CheckClassInfoStatusEnum.ENTER.getCode().equals(checkClassInfo.getCheckStatus()), () -> new BizException("检查记录已撤回或已考评，无法手动修改分数"));

        BigDecimal totalScore = checkClassInfo.getTotalScore();
        Range<BigDecimal> range = Range.closed(totalScore.subtract(BigDecimal.valueOf(10)), totalScore.add(BigDecimal.valueOf(10)));
        Assert.isTrue(range.contains(dto.getScore()), () -> new BizException("调整分数范围为10分之内"));

        checkClassInfo.setTotalScore(dto.getScore());
        checkClassInfo.setUpdateTime(new Date());
        checkClassInfo.setUpdateBy(WebUtil.getStaffId());
        checkClassInfoManager.updateById(checkClassInfo);

        return Boolean.TRUE;
    }
}
