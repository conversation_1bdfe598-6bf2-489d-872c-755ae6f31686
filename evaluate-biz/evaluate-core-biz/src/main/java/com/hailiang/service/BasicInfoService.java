package com.hailiang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.model.datastatistics.vo.CheckPermissionVo;
import com.hailiang.model.dto.StudentIdRequest;
import com.hailiang.model.dto.query.*;
import com.hailiang.model.query.StudentScoreQuery;
import com.hailiang.model.request.IdRequest;
import com.hailiang.model.request.StaffIdRequest;
import com.hailiang.model.response.StudentClassResponse;
import com.hailiang.model.response.StudentScoreDetailResponse;
import com.hailiang.model.vo.*;
import com.hailiang.remote.saas.dto.administration.OrgQueryDTO;
import com.hailiang.remote.saas.dto.auth.MenuAuthCheckDTO;
import com.hailiang.remote.saas.dto.educational.*;
import com.hailiang.remote.saas.dto.school.SchoolQueryDTO;
import com.hailiang.remote.saas.dto.school.SchoolStaffIdQueryDTO;
import com.hailiang.remote.saas.dto.staff.*;
import com.hailiang.remote.saas.dto.student.StudentByParentQueryDTO;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.pojo.educational.EduParentInfoPojo;
import com.hailiang.remote.saas.pojo.role.ResRoleInfoPojo;
import com.hailiang.remote.saas.vo.administration.AdminOrgTreeVO;
import com.hailiang.remote.saas.vo.administration.SchoolOrgVO;
import com.hailiang.remote.saas.vo.campus.TchCampusBaseInfoVO;
import com.hailiang.remote.saas.vo.campus.TchCampusInfoVO;
import com.hailiang.remote.saas.vo.educational.*;
import com.hailiang.remote.saas.vo.menu.MenuAuthVO;
import com.hailiang.remote.saas.vo.school.UserSchoolCampusListVO;
import com.hailiang.remote.saas.vo.staff.StaffBatchVO;
import com.hailiang.remote.saas.vo.staff.StaffStudentVO;
import com.hailiang.remote.saas.vo.staff.StaffVO;
import com.hailiang.remote.saas.vo.student.SaasStudentVO;
import com.hailiang.remote.saas.vo.student.StudentClassInfoVO;
import com.hailiang.remote.saas.vo.student.StudentVO;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.remote.saas.vo.teachingorg.ClassLevelVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.remote.saas.vo.user.UserInfoVO;
import com.hailiang.saas.model.vo.StudentInfo1VO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 基础数据服务层
 */
public interface BasicInfoService {

    /**
     * 根据学段ID获取当前和历史学期信息包含寒暑假
     *
     * @param campusSectionId
     * @return
     */
    List<TermVo> listNowAndHistoryTermsByCampusSectionId(String campusSectionId);

    ListOrgIdAndRoleIdVO listOrgIdAndRoleId();

    /**
     * 获取教职工所有组织机构id和角色id
     *
     * @param staffId  教职工 id
     * @param schoolId 学校 id
     * @return
     */
    ListOrgIdAndRoleIdVO listOrgIdAndRoleId(Long staffId, String schoolId);

    /**
     * 通过组织机构、角色获取所有教职工(老师)
     */
    List<BasicInfoListAllUserVO> listAllUser(List<BasicInfoListAllUserDTO> basicInfoListAllUserDTO);


    /**
     * 获取组织机构树形
     */
    List<AdminOrgTreeVO> listOrgTree(OrgQueryDTO orgQueryDTO);

    /**
     * 通过学校id获取组织机构树形
     */
    List<AdminOrgTreeVO> listOrgTreeBySchoolId(SchoolQueryDTO schoolQueryDTO);

    /**
     * 通过学校id获取学校的组织机构id
     */
    SchoolOrgVO getOrgIdBySchoolId(SchoolQueryDTO schoolQueryDTO);

    /**
     * 查询组织机构下的教职工列表
     */
    List<StaffVO> queryStaffListByOrg(OrgStaffQueryDTO orgStaffQueryDTO);

    /**
     * 获取学校下的校区列表
     */
    List<EduOrgTreeVO> queryEducationalOrgTree(EduOrgQueryDTO eduOrgQueryDTO);

    /**
     * 获取学校下的校区列表
     */
    List<EduOrgTreeVO> queryEducationalOrgTreeV2(EduOrgQueryDTO eduOrgQueryDTO);

    /**
     * 获取学校下的校区级联树
     *
     * @param eduOrgQueryDTO
     * @return
     */
    List<GlobalEduOrgTreeVO> queryGlobalEducationalOrgTree(EduOrgQueryDTO eduOrgQueryDTO);


    /**
     * 获取学校的组织信息（树形结构）
     *
     * @param eduOrgQueryDTO
     * @return
     */
    List<GlobalEduOrgTreeVO> queryGlobalEducationalOrgTreeV2(EduOrgQueryDTO eduOrgQueryDTO);


    /**
     * 获取学校下的教务组织架构树，包含走班
     */
    List<GlobalEduOrgTreeVO> queryGlobalEducationalOrgTreeV3(EduOrgQueryDTO eduOrgQueryDTO);

    /**
     * 判断当前用户是否拥有管理员权限
     * (校长、学生处主任、产品运营、教务主任)
     *
     * @return
     */
    Boolean judgeCurrentUserAdminAuth();

    /**
     * 德育页面获取学校下的校区级联树
     *
     * @param isGuidanceTeacher 是否德育指导老师角色
     * @param eduOrgQueryDTO
     * @return
     */
    List<EduOrgTreeVO> queryGlobalEducationalOrgTree(Boolean isGuidanceTeacher, EduOrgQueryDTO eduOrgQueryDTO);

    /**
     * 校验当前登录教职工是否有年级、班级查看权限（支持历史数据）
     *
     * @param campusSectionId 学段ID
     * @param isCurrentYear   是否当前学年
     * @param gradeId         年级ID
     * @param classId         班级ID
     * @param checkTime       校验时间
     * @return 有权限的年级集合、班级集合
     */
    CheckPermissionVo checkPermission(Long campusSectionId, String schoolYear, Boolean isCurrentYear, String gradeId, String classId, Date checkTime);

    List<EduOrgTreeVO> queryEducationalOrgTreeForGrade(EduOrgQueryDTO eduOrgQueryDTO);

    List<QueryClassBaseInfoListVO> queryCampusClassList(CampusClassListDTO param);

    List<ClassGradeListVO> queryClassList(ClassListDTO param);

    List<TermVo> listTerms(TermQuery termQuery);

    TermVo queryCurrentTerm(Long schoolId, Long campusId);

    /**
     * 接口6：获取教职工的任职情况
     */
    List<EduStaffClassVO> queryStaffTeachInfo(EduStaffClassQueryDTO eduStaffClassQueryDTO);


    EduParentInfoPojo getParentInfo(Long studentId);

    List<EduParentInfoPojo> getParentInfoList(Long studentId);

    StudentClassInfoVO queryStudentSchoolClassInfo(String studentId);

    EduStudentClassTeacherParentVO getStudentInfo(String studentId);

    /**
     * 接口11：获取学生列表
     * 查询学生列表（不包含已经毕业的学生）
     */
    List<EduStudentInfoVO> queryStudentPage(EduStudentPageQueryDTO eduStudentPageQueryDTO);

    List<EduStudentInfoVO> queryStudentPageV2(EduStudentPageQueryDTO eduStudentPageQueryDTO);

    /**
     * ：获取学生列表 包含走班
     * 查询学生列表（不包含已经毕业的学生）
     */
    List<EduStudentInfoVO> queryStudentPageAll(EduStudentPageQueryDTO eduStudentPageQueryDTO);

    /**
     * 查询学生列表（不包含已经毕业的学生，返回行政班和走班信息）
     */
    List<EduStudentInfoVO> queryStudentPageWithoutCondition(EduStudentPageQueryDTO eduStudentPageQueryDTO);

    /**
     * 接口5：获取角色下的教职工列表
     */
    List<StaffVO> queryStaffListByRole(StaffRoleQueryDTO staffRoleQueryDTO);

    /**
     * 分页查询学校下的教职工列表
     */
    List<StaffVO> queryStaffListBySchool(SchoolStaffQueryDTO schoolStaffQueryDTO);

    /**
     * 通过StaffIds查询教职工详情
     */
    List<StaffBatchVO> queryBasicInfoByStaffIds(StaffInfoQueryDTO staffInfoQueryDTO);


    /**
     * 获取学校下的校区列表
     */
    List<TchCampusInfoVO> queryCampusListBySchoolId(SchoolQueryDTO schoolQueryDTO);


    List<TchCampusBaseInfoVO> queryCampusBaseInfoList(String schoolId);

    Map<String, String> queryCampusIdNameMap(String schoolId);

    /**
     * 根据组织机构id取组织机构名称
     */
    String getOrgName(Long id, SaasCurrentIdTypeEnum saasCurrentIdTypeEnum);

    /**
     * 通过组织机构列表
     * 获取组织机构下的所有班级列表
     */
    List<Long> listClassIdByEducationOrgId(List<EduOrgQueryDTO> eduOrgQueryDTOS);

    /**
     * 根据学生id获取层级关系(有关系的链路id集合)
     *
     * @param studentId
     * @return
     */
    List<Long> listRelationInfo(String studentId);

    /**
     * 分页查询学校下的教职工列表
     *
     * @param schoolStaffStudentDTO
     * @return
     */
    Page<StaffStudentVO> pageStaffAndStudentBySchool(SchoolStaffStudentDTO schoolStaffStudentDTO);

    /**
     * 获取用户的所有学校以及学校下的校区
     * 场景：切换校区
     *
     * @param schoolStaffIdQueryDTO
     * @return
     */
    List<UserSchoolCampusListVO> userSchoolCampusList(SchoolStaffIdQueryDTO schoolStaffIdQueryDTO);

    /**
     * 获取用户的所有学校以及学校下的校区
     * 场景：切换校区
     *
     * @param schoolStaffIdQueryDTO
     * @return
     */
    List<UserSchoolCampusListVO> listCampusBySchool(SchoolStaffIdQueryDTO schoolStaffIdQueryDTO);

    /**
     * 获取学生列表(无权限)
     * 查询学生列表（不包含已经毕业的学生）
     */
    List<EduStudentInfoVO> queryStudentPageNoAuth(EduStudentPageQueryDTO eduStudentPageQueryDTO);

    /**
     * 获取学生列表(无权限)
     * 结果包含 班级、年级信息
     * 查询学生列表（不包含已经毕业的学生）
     */
    List<EduStudentInfoVO> queryStudentDetailPageNoAuth(EduStudentPageQueryDTO eduStudentPageQueryDTO);

    /**
     * 获取单个学生信息(行政班)(无权限)
     *
     * @param schoolId
     * @param studentId
     * @return
     */
    StudentVO getStudentInfoByStudentId(Long schoolId, Long studentId);

    /**
     * 获取单个教师信息(正常非离职状态)
     *
     * @param staffId
     * @return
     */
    StaffBatchVO getBasicInfoByStaffId(Long staffId);

    /**
     * 获取教师信息列表(正常非离职状态)
     *
     * @param staffIdList
     * @return
     */
    List<StaffBatchVO> getBasicInfoByStaffIdList(List<Long> staffIdList);

    /**
     * 根据学号和姓名获取学生信息
     *
     * @param studentCode
     * @param name
     * @return
     */
    StudentInfoVO getStudentInfoByStudentNoAndName(String studentCode, String name);

    /**
     * 查询教职工菜单权限列表
     *
     * @return
     */
    List<MenuAuthVO> listMenuAuth();


    List<EduOrgTreeVO> querySchoolOrgTree(EduGradeClassQueryDTO param);

    List<EduOrgTreeVO> queryEducationalOrgTree(EduOrganizationalQueryDTO param);

    /**
     * 判断校长,学生处主任,教务主任,运营人员权限
     *
     * @param roleCodeList
     * @return
     */
    List<EduOrgTreeVO> checkSupPermission(List<String> roleCodeList, EduOrgQueryDTO eduOrgQueryDTO);

    /**
     * 判断普通角色权限
     *
     * @param roleCodeList
     * @return
     */
    List<EduOrgTreeVO> checkOrdinaryPermission(List<String> roleCodeList, EduOrgQueryDTO eduOrgQueryDTO);

    TermVo getCurrentTermByCampusSectionId(String campusSectionId);

    List<SaaSOnDutyRoleVO> querySaaSOnDutyRoleList();

    List<Integer> queryStudentDutyRoleList(Long schoolId, Long userId);

    List<Integer> queryStaffDutyRoleList(Long schoolId, Long userId);

    List<Integer> queryDutyRoleList(Long schoolId, Long userId, Integer dutyRoleType);

    List<StudentWithClassInfoListVO> queryStudentListWithClassInfo(QueryStudentListWithClassDTO param);

    List<StudentTreeVO> queryGradeStudentTree(QueryGradeStudentTreeDTO param);

    List<StudentWithRoomInfoListVO> queryStudentListWithRoomInfo(QueryStudentListWithRoomDTO param);

    List<StudentWithRoomInfoListVO> queryStudentListWithRoomInfo(Long schoolId, List<Long> roomIdList);

    List<RoomClassInfoListVO> queryRoomClassList(Long schoolId, List<Long> roomIdList);

    List<YardRoomVO> queryYardRoomList(YardRoomListDTO param);

    List<TreeYardVO> querySchoolYardTree(YardTreeDTO param);

    List<TreeYardVO> querySchoolYardFloorTree();

    List<TreeYardVO> querySchoolRoomTree(String floorId);

    List<TreeYardVO> querySchoolBuildingFloorTree();

    TreeYardVO querySchoolFloorTree();


    /**
     * 查校区下场地（包含宿舍）
     */
    List<TreeYardVO> queryRegionRoomTreeAll();

    UcStudentClassBffVO listByStudentIds(String studentId);

    List<StudentClassResponse> listStudentCLassInfo(StudentIdRequest dto);

    List<ClassLevelVO> queryClassLevel(Long schoolId);

    List<ClassSimpleVO> queryClassInfoListWithAuth(String sectionCode, String schoolYear);

    List<ClassSimpleVO> querySchoolYearClassNoAuth(String sectionCode, String schoolYear);

    List<ClassSimpleVO> queryClassInfoList(String sectionCode, String schoolYear);

    Map<String, String> querySchoolYearClassIdNameMap(String sectionCode, String schoolYear);

    Map<String, ClassSimpleVO> querySchoolYearClassIdClassInfoMap(String sectionCode, String schoolYear);

    Map<String, ClassSimpleVO> querySchoolYearClassIdClassInfoMapH5(String sectionCode, String schoolYear, Boolean isCurrentYear);

    QueryClassLevelListVO queryClassGroupByLevelWithAuth(ClassLevelListWithAuthDTO param);

    QueryClassLevelListVO queryClassGroupByLevel(Long schoolId, List<ClassGradeListVO> classList);

    List<SectionSchoolYearVo> querySectionSchoolYearList();


    Map<String, SchoolYearVo> querySectionSchoolYearStartTimeEndTimeMap();

    List<ResRoleInfoPojo> queryStaffRoleList(Long schoolId, Long staffId);

    List<String> queryStaffRoleCodeList(Long schoolId, Long staffId);

    /**
     * 查看数据是否超过上一学年
     *
     * @param campusSectionId 所选学段ID
     * @param schoolYear      所选学年
     * @param checkTime       校验时间
     * @return
     */
    Boolean isHistory(Long campusSectionId, String schoolYear, Date checkTime);

    List<String> queryOrdinaryStaffClassIdList();

    List<TermVo> queryTermList(String campusSectionId);


    /**
     * 获取当前学年信息
     * 当前学年不存在 获取上一学年
     */
    TermVo getTime(String schoolId, String campusId, String campusSectionId);

    /**
     * 获取上一学年信息
     */
    Map<Long/*学段ID*/, SchoolYearVo/*学年*/> getLastSchoolYear();

    /**
     * 带优化的查询学生列表（不包含已经毕业的学生）
     */
    List<StudentEduInfoVO> pageClassStudent(PageEduStudentQueryDTO dto);

    List<EduStudentInfoVO> pageClassStudentV2(PageEduStudentQueryDTO dto);

    StudentScoreDetailResponse getStudentScoreDetailInfo(StudentScoreQuery query);

    TermVo getTimePro(PageEduStudentQueryDTO dto);

    /**
     * 获取家长下关联学生列表
     *
     * @param queryDTO
     * @return
     */
    List<SaasStudentVO> listStudentsByParent(StudentByParentQueryDTO queryDTO);

    /**
     * 根据宿舍查询学生
     */
    List<StudentInfo1VO> listStudentByRoomId(IdRequest request);

    EduAuthVO getCurrentStaffAuth(String staffId, String schoolYear, String termName, String moduleCode);

    EduAuthVO getCurrentStaffAuth(String staffId, String schoolYear, String termName);

    EduAuthVO getCurrentStaffAuth(String staffId, String schoolYear);

    /**
     * 权限，包含走班
     */
    EduAuthVO getCurrentStaffAuthAll(String staffId, String schoolYear, String termName, String moduleCode, List<Integer> classTypes);

    /**
     * 权限，包含走班
     */
    EduAuthVO getCurrentStaffAuthAll(String staffId, String schoolYear, String termName, List<Integer> classTypes);

    /**
     * 检查用户是否有某个按钮接口code的权限
     *
     * @return
     */
    Boolean checkMenuAuth(MenuAuthCheckDTO menuAuthCheckDTO);

    /**
     * 根据token查询用户信息
     *
     * @return
     */
    UserInfoVO getUserInfoFromToken(String token);

    /**
     * 查询当前教职工所有任教班级信息
     *
     * @return
     */
    List<Long> listTeachingClass();

    /**
     * 根据学生id集合获取请假状态
     * <p>
     * 是否请假 1:是 0:否
     *
     * @param studentIds
     * @return
     */
    Map<Long, Integer> getLeaveStatusFlagMap(List<Long> studentIds);

}
