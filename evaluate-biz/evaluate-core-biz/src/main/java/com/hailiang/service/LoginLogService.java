package com.hailiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.query.LoginLogQueryDTO;
import com.hailiang.model.dto.save.LoginLogSaveDTO;
import com.hailiang.model.entity.LoginLog;
import com.hailiang.model.vo.LoginLogVO;
import com.hailiang.remote.saas.vo.login.LoginAuthProtocolVO;
import com.hailiang.saas.auth.vo.AuthenticationResource;

import javax.servlet.http.HttpServletRequest;


/**
 * 登录日志
 */
public interface LoginLogService extends IService<LoginLog> {

    IPage<LoginLogVO> pageLoginLog(Page page, LoginLogQueryDTO loginLogDTO);

    boolean saveLoginLog(HttpServletRequest request);

    /**
     * 保存登录日志  1：钉钉， 2：飞书 ，3：微信，4：浙里办，5，积分柜  9：其他
     */
    Object saveLoginLogAll(HttpServletRequest request);
}