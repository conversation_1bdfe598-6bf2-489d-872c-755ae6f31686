package com.hailiang.service;

import com.hailiang.model.medal.dto.MedalLogoSaveDTO;
import com.hailiang.model.medal.vo.MedalLogoVO;

import java.util.List;

/**
 * @Author: JJL
 * @Date: 2023/5/31 14:30
 */
public interface MedalLogoService {

    /**
     * 新增奖章logo
     *
     * @param dto
     * @return
     */
    void saveMedalLogo(MedalLogoSaveDTO dto);

    /**
     * 删除奖章logo
     *
     * @param id
     * @return
     */
    void deleteMedalLogo(Long id);

    /**
     * 查询奖章logo
     *
     * @return
     */
    List<MedalLogoVO> listMedalLogo();
}
