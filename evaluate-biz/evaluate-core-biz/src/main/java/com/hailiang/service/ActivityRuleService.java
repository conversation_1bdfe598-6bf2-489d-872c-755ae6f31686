package com.hailiang.service;

import com.hailiang.model.dto.JobIssueActivityMedalDTO;
import com.hailiang.model.dto.activity.rule.query.ActivityTaskQuery;
import com.hailiang.model.dto.activity.rule.query.RuleModuleInfoVO;
import com.hailiang.model.dto.activity.rule.save.SaveTaskRuleDTO;
import com.hailiang.model.dto.activity.rule.save.TargetLevelDTO;
import com.hailiang.model.vo.activity.rule.ActivityTaskRuleVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/5 13:36
 */
public interface ActivityRuleService {


    /**
     * 保存任务
     *
     * @param dto
     * @return
     */
    Boolean saveTaskRule(SaveTaskRuleDTO dto);

    /**
     * 查询活动任务
     *
     * @param dto
     * @return
     */
    List<ActivityTaskRuleVO> listTaskRule(ActivityTaskQuery dto);

    /**
     * 获取五育明细下的所有指标
     *
     * @return
     */
    List<RuleModuleInfoVO> listRuleModuleInfo();

    /**
     * 早上八点定时任务颁发奖章
     *
     * @param jobIssueActivityMedalDTO dto
     * @return
     */
    Boolean issueActivityMedal(JobIssueActivityMedalDTO jobIssueActivityMedalDTO);

    TargetLevelDTO encapsulationLevel(String submitId, Integer submitType, Long targetId);

//    /**
//     * 修复颁章明细相关数据(新增二级任务详情表数据,新增颁章明细操作记录表数据,颁章明细completion_id字段为空数据)
//     *
//     * @param medalFixDTO 数据库主键id集合
//     * @return
//     */
//    void fixMedalUserAcquireData(MedalFixDTO medalFixDTO);
//
//    /**
//     * 修复颁章明细班级id,名称错误数据
//     *
//     * @param medalFixDTO 数据库主键id集合
//     * @return
//     */
//    void fixMedalUserAcquireClassIdData(MedalFixDTO medalFixDTO);
}
