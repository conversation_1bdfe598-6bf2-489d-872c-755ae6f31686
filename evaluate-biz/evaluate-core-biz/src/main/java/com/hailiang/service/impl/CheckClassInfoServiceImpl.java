package com.hailiang.service.impl;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.constant.Constant;
import com.hailiang.convert.CheckConvert;
import com.hailiang.enums.*;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.logic.RoleLogic;
import com.hailiang.manager.CheckClassInfoManager;
import com.hailiang.manager.CheckClassRelationManager;
import com.hailiang.mapper.mongo.CheckClassInfoDao;
import com.hailiang.model.datastatistics.vo.ExcelClassCompareVO;
import com.hailiang.model.datastatistics.vo.ExcelDutyRecordVO;
import com.hailiang.model.datastatistics.vo.excel.ExcelClassCompareNewVO;
import com.hailiang.model.dto.check.CheckOptionDetailDTO;
import com.hailiang.model.dto.check.form.CheckSubmitFormDTO;
import com.hailiang.model.dto.query.*;
import com.hailiang.model.entity.CheckClassInfo;
import com.hailiang.model.entity.CheckClassRelation;
import com.hailiang.model.entity.mongo.CheckClassInfoMongodb;
import com.hailiang.model.vo.*;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStaffClassQueryDTO;
import com.hailiang.remote.saas.vo.educational.ClassSimpleVO;
import com.hailiang.remote.saas.vo.educational.EduStaffClassVO;
import com.hailiang.remote.saas.vo.educational.EduStaffMasterClassVO;
import com.hailiang.remote.saas.vo.educational.GlobalEduOrgTreeVO;
import com.hailiang.service.*;
import com.hailiang.util.*;
import java.util.function.Function;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【check_class_info(班级检查记录)】的数据库操作Service实现
 * @createDate 2023-07-19 11:38:00
 */
@Service
@Slf4j
public class CheckClassInfoServiceImpl implements CheckClassInfoService {


    @Autowired
    private CheckConvert checkConvert;
    @Autowired
    private CheckClassInfoManager checkClassInfoManager;
    @Autowired
    private CheckClassRelationManager checkClassRelationManager;
    @Autowired
    private CheckDimService checkDimService;

    @Autowired
    private CheckItemService checkItemService;
    @Autowired
    private CampusParamService campusParamService;

    @Autowired
    private BasicInfoService basicInfoService;
    @Autowired
    private CacheSaasManager cacheSaasManager;
    @Autowired
    private RoleLogic roleLogic;
    @Resource
    private CheckClassInfoDao checkClassInfoDao;

    /**
     * 分页查询班级检查记录
     *
     * @param param
     * @return
     */
    @Override
    public IPage<DutyRecordPageVO> queryCheckClassInfoPageWithAuth(DutyRecordPageDTO param) {
        Long schoolId = WebUtil.getSchoolIdLong();
        Long staffId = WebUtil.getStaffIdLong();
        //日期赋值
        param.setCheckStartTime(Objects.isNull(param.getCheckStartTime()) ? param.getStartTime() : param.getCheckStartTime());
        param.setCheckEndTime(Objects.isNull(param.getCheckEndTime()) ? param.getEndTime() : param.getCheckEndTime());

        Page<DutyRecordPageVO> page = new Page<>();
        page.setCurrent(param.getPageNum());
        page.setSize(param.getPageSize());
        page.setTotal(Constant.ZERO);

        //获取年级信息 从saas获取年级班级数据
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(param.getCampusSectionId()));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
        eduOrgQueryDTO.setSchoolYear(param.getSchoolYear());
        eduOrgQueryDTO.setModuleCode(ModuleCodeEnum.CLASS_EVALUATE.getCode());
        List<GlobalEduOrgTreeVO> eduOrgTreeVOS = basicInfoService.queryGlobalEducationalOrgTreeV2(eduOrgQueryDTO);
        if (CollUtil.isEmpty(eduOrgTreeVOS)) {
            log.warn("[值日记录]-[获取组织信息失败]-[请求参数:{}]", JSON.toJSONString(eduOrgQueryDTO));
            return page;
        }

        param.setSectionCode(eduOrgTreeVOS.get(0).getCode());  //以前的逻辑先不动

        DutyRecordPageMapperDTO query = checkConvert.toDutyRecordPageMapperDTO(param);
        query.setCampusId(WebUtil.getCampusId());
        query.setCampusSectionId(param.getCampusSectionId());
        query.setGradeId(param.getGradeId());

        List<String> roleCodeList = basicInfoService.queryStaffRoleCodeList(schoolId, staffId);

        //是否为管理员权限，管理员可以查看全部数据
        if (CollUtil.isNotEmpty(CollUtil.intersection(roleCodeList, ModuleCodeEnum.listAdminRoleByCode(ModuleCodeEnum.CLASS_EVALUATE.getCode())))) {
            return this.queryCheckClassInfoPage(query);
        }


        // 普通角色可查看负责的班级，其他角色无权限
        boolean ordinaryStaffFlag = roleLogic.isHaveOrdinaryRole(roleCodeList);
        if (!ordinaryStaffFlag) {
            log.warn("[值日记录]-[当前登陆人无权限查看]-[已有角色信息:{}]", JSON.toJSONString(roleCodeList));
            return page;
        }

        //解析获取班级ID列表
        List<String> classIdList = this.parseOrgTree(eduOrgTreeVOS);
        if (CollUtil.isEmpty(classIdList)) {
            log.warn("[值日记录]-[无任何班级权限]-[请求参数:{}]", JSON.toJSONString(eduOrgQueryDTO));
            return page;
        }

        query.setMineClassIdList(classIdList);

        return this.queryCheckClassInfoPage(query);
    }

    /**
     * 分页查询班级检查记录
     *
     * @param param
     * @return
     */
    @Override
    public IPage<DutyRecordPageVO> queryCheckClassInfoPage(DutyRecordPageMapperDTO param) {
        this.check(param);
        param.setCheckItemName(ParamUtil.transform(param.getCheckItemName()));
        param.setCheckUserName(ParamUtil.transform(param.getCheckUserName()));
        if (ObjectUtil.isNotNull(param.getCheckEndTime())) {
            param.setCheckEndTime(DateUtil.endOfDay(param.getCheckEndTime()));
        }

        Page<DutyRecordPageVO> page = new Page<>();
        page.setCurrent(param.getPageNum());
        page.setSize(param.getPageSize());

        if ("-1".equals(param.getGradeId())){
            param.setClassId(null);
            param.setGradeId(null);
        }
        if ("-1".equals(param.getClassId())){
            param.setClassId(null);
        }

        IPage<CheckClassInfo> checkClassInfoIPage = checkClassInfoManager.queryCheckClassInfoPage(param);
        if (ObjectUtil.isNull(checkClassInfoIPage) || CollUtil.isEmpty(checkClassInfoIPage.getRecords())) {
            return page;
        }

        List<DutyRecordPageVO> dutyRecordList = checkConvert.toDutyRecordPageVOList(checkClassInfoIPage.getRecords());
        // 为值日记录补充维度名称、检查项名称
        this.appendColumValue(dutyRecordList);
        // 挂上寝室信息
        this.handleDormitory(dutyRecordList);
        // 补充班级名称
        if (StrUtil.isNotBlank(param.getSectionCode()) && StrUtil.isNotBlank(param.getSchoolYear())) {
            this.appendSchoolYearClassName(param.getSectionCode(), param.getSchoolYear(), dutyRecordList);
        }

        // 填充扣分原因
        this.processDutyRecords(dutyRecordList);

        page.setRecords(dutyRecordList);
        page.setTotal(checkClassInfoIPage.getTotal());
        return page;
    }

    /**
     * 填充扣分原因
     */
    private void processDutyRecords(List<DutyRecordPageVO> dutyRecordList) {
        if (CollUtil.isEmpty(dutyRecordList)) {
            log.warn("【值日记录】-【扣分原因入参为空】-【请求参数:{}】", JSON.toJSONString(dutyRecordList));
            return;
        }

        List<String> checkInfoIds = dutyRecordList
                .stream()
                .map(DutyRecordPageVO::getCheckInfoId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(checkInfoIds)) {
            log.warn("【值日记录】-【扣分原因checkInfoId为空】-【请求参数:{}】", JSON.toJSONString(dutyRecordList));
            return;
        }

        // 查询检查项
        Query query = new Query();
        query.addCriteria(Criteria.where("id").in(checkInfoIds));
        List<CheckClassInfoMongodb> checkClassInfoS = checkClassInfoDao.find(query);
        if (CollUtil.isEmpty(checkClassInfoS)) {
            log.warn("【值日记录】-【扣分原因为空】-【参数:{}】", JSON.toJSONString(query));
            return;
        }

        Map<String, CheckClassInfoMongodb> checkInfoIdMap = CollStreamUtil.toIdentityMap(checkClassInfoS,
                CheckClassInfoMongodb::getId);

        dutyRecordList.forEach(dutyRecordPageVO -> {
            String checkInfoId = dutyRecordPageVO.getCheckInfoId();
            CheckClassInfoMongodb checkClassInfo = checkInfoIdMap.get(checkInfoId);
            if (BeanUtil.isEmpty(checkClassInfo)) {
                return;
            }

            List<CheckOptionDetailDTO> detailDTOS = this.extractDetails(checkClassInfo);
            String reason = this.buildReason(detailDTOS);
            dutyRecordPageVO.setReason(reason);
        });

    }

    /**
     * 提取检查项的选项信息
     */
    private List<CheckOptionDetailDTO> extractDetails(CheckClassInfoMongodb checkClassInfo) {
        if (BeanUtil.isEmpty(checkClassInfo)) {
            log.warn("【值日记录】-【提取检查项的选项信息】-【入参为空】");
            return Collections.emptyList();
        }
        List<CheckSubmitFormDTO> submitInfoList = Optional.ofNullable(checkClassInfo.getSubmitInfoList())
                .orElse(Collections.emptyList());

        List<CheckOptionDetailDTO> detailDTOS = new ArrayList<>();

        // 多选
        List<CheckOptionDetailDTO> multiCheckOptionDetailDTOS = submitInfoList.stream()
                .filter(s -> SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(s.getType()))
                .map(CheckSubmitFormDTO::getSubmitValue)
                .filter(Objects::nonNull)
                .flatMap(this::parseMultiSubmitValue)
                .collect(Collectors.toList());

        // 单选
        List<CheckOptionDetailDTO> singleCheckOptionDetailDTOS = submitInfoList.stream()
                .filter(s -> SubmitInfoTypeEnum.SINGLE_CHECK.getText().equals(s.getType()))
                .map(CheckSubmitFormDTO::getSubmitValue)
                .filter(Objects::nonNull)
                .map(submitValue -> JSONUtil.parseObj(submitValue).toBean(CheckOptionDetailDTO.class))
                .collect(Collectors.toList());

        detailDTOS.addAll(multiCheckOptionDetailDTOS);
        detailDTOS.addAll(singleCheckOptionDetailDTOS);
        return detailDTOS;
    }

    /**
     * 解析多选选项信息
     */
    private Stream<CheckOptionDetailDTO> parseMultiSubmitValue(Object submitValue) {
        return Optional.ofNullable(submitValue)
                .map(JSONUtil::parseArray)
                .map(array -> array.toList(CheckOptionDetailDTO.class))
                .orElse(Collections.emptyList()).stream();
    }

    /**
     * 将选项信息拼接成字符串
     */
    private String buildReason(List<CheckOptionDetailDTO> detailDTOS) {
        if (CollUtil.isEmpty(detailDTOS)) {
            return "";
        }
        return detailDTOS.stream()
                .filter(detailDTO -> detailDTO != null && CharSequenceUtil.isNotBlank(detailDTO.getLabel()))
                .map(CheckOptionDetailDTO::getLabel)
                .collect(Collectors.joining("、"));
    }

    /**
     * 设置学年学段内该班级的名称
     *
     * @param sectionCode
     * @param schoolYear
     * @param dutyRecordList
     */
    private void appendSchoolYearClassName(String sectionCode, String schoolYear, List<DutyRecordPageVO> dutyRecordList) {
        // 查学段--学年内所有班级id：班级名称map
        Map<String, String> classIdNameMap = basicInfoService.querySchoolYearClassIdNameMap(sectionCode, schoolYear);
        if (CollUtil.isEmpty(classIdNameMap) || CollUtil.isEmpty(dutyRecordList)) {
            return;
        }
        for (DutyRecordPageVO item : dutyRecordList) {
            String className = classIdNameMap.get(item.getClassId());
            if (StrUtil.isBlank(className)) {
                continue;
            }
            item.setClassName(className);
        }
    }

    /**
     * 校验入参
     *
     * @param param
     */
    private void check(DutyRecordPageMapperDTO param) {
        AssertUtil.checkNotNull(param, "参数不能为空");
        AssertUtil.checkNotNull(param.getPageNum(), "当前页码不能为空");
        AssertUtil.checkNotNull(param.getPageSize(), "每页条数不能为空");
        AssertUtil.checkNotBlank(param.getCampusSectionId(), "学段不能为空");
        AssertUtil.checkNotBlank(param.getSchoolYear(), "学年不能为空");

        boolean startTimeNullFlag = ObjectUtil.isNull(param.getCheckStartTime());
        boolean endTimeNullFlag = ObjectUtil.isNull(param.getCheckEndTime());
        boolean oneTimeNullFlag = (startTimeNullFlag && !endTimeNullFlag) || (!startTimeNullFlag && endTimeNullFlag);
        AssertUtil.checkIsFalse(oneTimeNullFlag, "检查开始、结束时间必须同时输入或同时不输入");
        boolean bothNotNullFlag = !startTimeNullFlag && !endTimeNullFlag;
        if (bothNotNullFlag) {
            Map<String, SchoolYearVo> sectionSchoolYearTimeMap = cacheSaasManager.querySectionSchoolYearStartTimeEndTimeMap();
            AssertUtil.checkNotEmpty(sectionSchoolYearTimeMap, "当前学校没有学段学期，请检查");
//            String key = param.getSchoolYear() + StrPool.AT + param.getCampusSectionId();
//            SchoolYearVo schoolYear = sectionSchoolYearTimeMap.get(key);
//            AssertUtil.checkNotNull(schoolYear, "当前学校不存在当前学段、学年");

//            AssertUtil.checkBetween(param.getCheckStartTime(), Convert.toDate(schoolYear.getStartTime()), Convert.toDate(schoolYear.getEndTime()), "查询时间必须在当前学年时间段内");
//            AssertUtil.checkBetween(param.getCheckEndTime(), Convert.toDate(schoolYear.getStartTime()), Convert.toDate(schoolYear.getEndTime()), "查询时间必须在当前学年时间段内");
        }

        if (ObjectUtil.isNotNull(param.getCheckRoleCode())) {
            AssertUtil.checkIsTrue(SaaSOnDutyRoleEnum.isExists(param.getCheckRoleCode()), "值日角色输入错误，请检查");
        }

        if (ObjectUtil.isNotNull(param.getCheckStatus())) {
            AssertUtil.checkIsTrue(CheckClassInfoStatusEnum.isExists(param.getCheckStatus()), "状态输入错误，请检查");
        }
    }

    /**
     * 为值日记录补充维度名称、检查项名称
     *
     * @param dutyRecordList
     */
    private void appendColumValue(List<DutyRecordPageVO> dutyRecordList) {
        // 补充检查维度与检查项名称
        String campusId = WebUtil.getCampusId();
        String sourceType = SourceTypeEnum.LDHQ.getCode();

        Map<Long, String> checkDimIdNameMap = checkDimService.queryAllCheckDimIdNameMap(campusId, sourceType);

        Map<Long, String> checkItemIdNameMap = checkItemService.queryCheckItemIdNameMap(campusId, sourceType);

        for (DutyRecordPageVO item : dutyRecordList) {
            String checkDimName = checkDimIdNameMap.get(item.getCheckDimId());
            String checkItemName = checkItemIdNameMap.get(item.getCheckItemId());

            item.setCheckDimName(checkDimName);
            item.setCheckItemName(checkItemName);
        }
    }

    /**
     * 移动端--班级排行
     *
     * @param param
     * @return
     */
    @Override
    public ClassMobileCompareVO queryClassCompareScoreListWithAuth(ClassCompareListDTO param) {
        Long schoolId = WebUtil.getSchoolIdLong();
        Long staffId = WebUtil.getStaffIdLong();

        //获取年级信息 从saas获取年级班级数据
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(param.getCampusSectionId()));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
        eduOrgQueryDTO.setSchoolYear(param.getSchoolYear());
        eduOrgQueryDTO.setModuleCode(ModuleCodeEnum.CLASS_EVALUATE.getCode());
        if (StringUtils.isNotEmpty(param.getCampusSectionId())) {
            List<GlobalEduOrgTreeVO> eduOrgTreeVOS = basicInfoService.queryGlobalEducationalOrgTreeV2(eduOrgQueryDTO);
            if (CollUtil.isNotEmpty(eduOrgTreeVOS)) {
                param.setSectionCode(eduOrgTreeVOS.get(0).getCode());
            }
        }
        if (StringUtils.isNotEmpty(param.getGradeId()) && "-1".equals(param.getGradeId())){
            param.setGradeId(null);
        }

        List<String> roleCodeList = basicInfoService.queryStaffRoleCodeList(schoolId, staffId);
        //是否为管理员权限，管理员可以查看全部数据
        boolean isAdmin = CollUtil.isNotEmpty(CollUtil.intersection(roleCodeList, ModuleCodeEnum.listAdminRoleByCode(ModuleCodeEnum.CLASS_EVALUATE.getCode())));
        Assert.isTrue(isAdmin, () -> new BizException(BizExceptionEnum.NOT_AUTH.getMessage()));
        return this.queryClassCompareScoreList(param);


//        // 全量班级数据
//        ClassMobileCompareVO compareVO = this.queryClassCompareScoreList(param);
//        if (ObjectUtil.isNull(compareVO) || CollUtil.isEmpty(compareVO.getClassList())){
//            return compareVO;
//        }
//        // 普通角色可查看负责的班级，其他角色无权限
//        Boolean ordinaryStaffFlag = roleLogic.isHaveOrdinaryRole(roleCodeList);
//        if (!ordinaryStaffFlag){
//            return compareVO.setClassList(Collections.emptyList()).setMineClassList(Collections.emptyList());
//        }
//        // 普通角色负责的班级id列表
//        List<String> mineClassIdList = basicInfoService.queryOrdinaryStaffClassIdList();
//        if (CollUtil.isEmpty(mineClassIdList)){
//            return compareVO.setClassList(Collections.emptyList()).setMineClassList(Collections.emptyList());
//        }
//
//        List<ClassCompareVO> filterList = compareVO.getClassList().stream().filter(item -> mineClassIdList.contains(item.getClassId())).collect(Collectors.toList());
//        compareVO.setClassList(filterList);
//
//        return compareVO;
    }

    /**
     * 移动端--班级排行
     *
     * @param param
     * @return
     */
    @Override
    public ClassMobileCompareVO queryClassCompareScoreList(ClassCompareListDTO param) {
        this.check(param);

        Long schoolId = WebUtil.getSchoolIdLong();
        Long staffId = WebUtil.getStaffIdLong();
        String campusId = WebUtil.getCampusId();
        CheckClassInfoScoreListDTO query = new CheckClassInfoScoreListDTO()
                .setCampusId(campusId)
                .setSectionCode(param.getSectionCode())
                .setCampusSectionId(param.getCampusSectionId())
                .setSchoolYear(param.getSchoolYear())
                .setStartTime(param.getStartTime())
                .setEndTime(DateUtil.endOfDay(param.getEndTime()))
                .setGradeId(param.getGradeId());

        List<CheckClassScoreListVO> classScoreList = checkClassInfoManager.queryClassScoreList(query);
        // 所有班级都没有值日记录则所有班级分数都是初始分
        if (CollUtil.isEmpty(classScoreList)) {
            classScoreList = Lists.newArrayList();
        }

        List<ClassCompareVO> classTotalScoreList = checkConvert.toClassCompareVOList(classScoreList);
        // 初始分
        Integer initScore = campusParamService.getCampusInitScore(campusId);
        // 总分添加初始分
        this.addInitScore(initScore, classTotalScoreList);
        // 查学段--学年内所有班级id：班级名称map
        Map<String, ClassSimpleVO> classIdClassInfoMap = basicInfoService.querySchoolYearClassIdClassInfoMapH5(param.getSectionCode(), param.getSchoolYear(), param.getIsCurrentYear());
        if (CollUtil.isNotEmpty(classIdClassInfoMap)) {
            // 补充班级名称和排序字段
            this.appendHistoryClassInfo(classIdClassInfoMap, classTotalScoreList);
            // 补充没有值日检查的班级信息
            this.appendNoDutyClassInfo(classIdClassInfoMap, classTotalScoreList, initScore, Convert.toLong(param.getGradeId()));
        }
        // 所有班级都没有值日记录且saas在当前学年不存在班级返回空
        if (CollUtil.isEmpty(classTotalScoreList)) {
            return null;
        }
        // 补充是否为当前登录人负责的班级
        this.appendMineClassFlag(classTotalScoreList, schoolId, staffId);

        // 排序 总分倒序，classNum正序
        List<ClassCompareVO> sortList = classTotalScoreList.stream()
                .sorted(Comparator.comparing(ClassCompareVO::getTotalScore, Comparator.reverseOrder())
                        .thenComparing(ClassCompareVO::getGradeCode)
                        .thenComparing(ClassCompareVO::getClassNum))
                .collect(Collectors.toList());

        // 过滤出班主任班级
        List<ClassCompareMineVO> mineClassList = sortList.stream()
                .filter(item -> Boolean.TRUE.equals(item.getMineClassFlag())).map(item -> {
                    return new ClassCompareMineVO()
                            .setClassId(item.getClassId())
                            .setClassName(item.getClassName())
                            .setTotalScore(item.getTotalScore());
                }).collect(Collectors.toList());

        return new ClassMobileCompareVO().setClassList(sortList).setMineClassList(mineClassList);
    }

    private void appendNoDutyClassInfo(Map<String, ClassSimpleVO> classIdClassInfoMap, List<ClassCompareVO> classTotalScoreList, Integer initScore, Long groupId) {
        List<String> appendClassIdList = classIdClassInfoMap.keySet().stream().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(classTotalScoreList)) {
            Set<String> existClassIdList = classTotalScoreList.stream().map(ClassCompareVO::getClassId).collect(Collectors.toSet());
            appendClassIdList.removeAll(existClassIdList);
        }
        BigDecimal score = BigDecimal.valueOf(initScore);
        for (String classId : appendClassIdList) {
            ClassSimpleVO classInfo = classIdClassInfoMap.get(classId);
            if(Objects.nonNull(groupId) && !Objects.equals(groupId, classInfo.getGradeId())){
                continue;
            }
            ClassCompareVO item = new ClassCompareVO()
                    .setClassId(classId)
                    .setClassName(classInfo.getClassName())
                    .setClassNum(classInfo.getClassNum())
                    .setGradeCode(classInfo.getGradeCode())
                    .setTotalScore(score);
            classTotalScoreList.add(item);
        }

    }

    /**
     * 设置学年学段内该班级的名称
     *
     * @param classTotalScoreList
     */
    private void appendHistoryClassInfo(Map<String, ClassSimpleVO> classIdClassInfoMap, List<ClassCompareVO> classTotalScoreList) {

        if (CollUtil.isEmpty(classIdClassInfoMap) || CollUtil.isEmpty(classTotalScoreList)) {
            return;
        }
        for (ClassCompareVO item : classTotalScoreList) {
            ClassSimpleVO classInfo = classIdClassInfoMap.get(item.getClassId());
            if (ObjectUtil.isNull(classInfo)) {
                continue;
            }
            item.setClassName(classInfo.getClassName())
                    .setClassNum(classInfo.getClassNum())
                    .setGradeCode(classInfo.getGradeCode());
        }
    }

    /**
     * 添加初始分
     *
     * @param classTotalScoreList
     * @param initScore
     */
    private void addInitScore(Integer initScore, List<ClassCompareVO> classTotalScoreList) {
        // 获取初始分
        BigDecimal score = BigDecimal.valueOf(initScore);
        for (ClassCompareVO item : classTotalScoreList) {
            item.setTotalScore(item.getTotalScore().add(score));
        }
    }

    /**
     * 补充班主任班级信息
     *
     * @param classTotalScoreList
     * @param schoolId
     * @param staffId
     */
    private void appendMineClassFlag(List<ClassCompareVO> classTotalScoreList, Long schoolId, Long staffId) {
        EduStaffClassQueryDTO query = new EduStaffClassQueryDTO()
                .setSchoolId(schoolId)
                .setStaffIds(Arrays.asList(staffId))
                .setClassTypes(Arrays.asList(SaasClassTypeEnum.XINGZHENG.getCode()));
        List<EduStaffClassVO> staffClassList = basicInfoService.queryStaffTeachInfo(query);
        if (CollUtil.isEmpty(staffClassList)) {
            return;
        }
        // 当前员工任班主任的班级
        List<EduStaffMasterClassVO> leaderClassList = staffClassList.get(0).getLeaderClassInfos();
        if (CollUtil.isEmpty(leaderClassList)) {
            return;
        }
        List<Long> leaderClassIdList = leaderClassList.stream().map(EduStaffMasterClassVO::getClassId).collect(Collectors.toList());
        for (ClassCompareVO item : classTotalScoreList) {
            if (leaderClassIdList.contains(Convert.toLong(item.getClassId()))) {
                item.setMineClassFlag(Boolean.TRUE);
                continue;
            }
            item.setMineClassFlag(Boolean.FALSE);
        }

    }

    /**
     * 班级排行
     *
     * @param param
     * @return
     */
    @Override
    public ClassCompareListVO queryClassScoreListWithAuth(ClassCompareListDTO param) {
        Long schoolId = WebUtil.getSchoolIdLong();
        Long staffId = WebUtil.getStaffIdLong();

        //获取年级信息 从saas获取年级班级数据
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(param.getCampusSectionId()));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
        eduOrgQueryDTO.setSchoolYear(param.getSchoolYear());
        eduOrgQueryDTO.setModuleCode(ModuleCodeEnum.CLASS_EVALUATE.getCode());
        List<GlobalEduOrgTreeVO> eduOrgTreeVOS = basicInfoService.queryGlobalEducationalOrgTreeV2(eduOrgQueryDTO);
        if (CollUtil.isEmpty(eduOrgTreeVOS)) {
            log.warn("[班级排行]-[获取组织信息失败]-[请求参数:{}]", JSON.toJSONString(eduOrgQueryDTO));
            return new  ClassCompareListVO();
        }
        if (StringUtils.isNotEmpty(param.getCampusSectionId())) {
            param.setSectionCode(eduOrgTreeVOS.get(0).getCode());
        }
        // 管理员可以查看所有班级排行
        List<String> roleCodeList = basicInfoService.queryStaffRoleCodeList(schoolId, staffId);
        //是否为管理员权限，管理员可以查看全部数据
        if (CollUtil.isNotEmpty(CollUtil.intersection(roleCodeList, ModuleCodeEnum.listAdminRoleByCode(ModuleCodeEnum.CLASS_EVALUATE.getCode())))) {
            return this.queryClassScoreList(param);
        }
        // 查全量班级排行数据
        ClassCompareListVO compareVO = this.queryClassScoreList(param);
        if (ObjectUtil.isNull(compareVO) || CollUtil.isEmpty(compareVO.getClassScoreList())) {
            return compareVO;
        }
        // 普通角色可查看负责的班级，其他角色无权限,返回空列表
        boolean ordinaryStaffFlag = roleLogic.isHaveOrdinaryRole(roleCodeList);
        if (!ordinaryStaffFlag) {
            log.warn("[班级排行]-[当前登陆人无权限查看]-[已有角色信息:{}]", JSON.toJSONString(roleCodeList));
            return compareVO.setClassScoreList(Collections.emptyList());
        }

        //解析获取班级ID列表
        List<String> classIdList = this.parseOrgTree(eduOrgTreeVOS);
        if (CollUtil.isEmpty(classIdList)) {
            log.warn("[班级排行]-[无任何班级权限]-[请求参数:{}]", JSON.toJSONString(eduOrgQueryDTO));
            return compareVO.setClassScoreList(Collections.emptyList());
        }

        List<ClassCompareInfoListVO> filterList = compareVO.getClassScoreList().stream().filter(item -> classIdList.contains(item.getClassId())).collect(Collectors.toList());
        compareVO.setClassScoreList(filterList);

        return compareVO;
    }

    private List<String> parseOrgTree(List<GlobalEduOrgTreeVO> eduOrgTreeVOS) {
        List<String> classIdList = Lists.newArrayList();
        if (CollUtil.isEmpty(eduOrgTreeVOS)) {
            return classIdList;
        }
        for (GlobalEduOrgTreeVO orgTreeVO : eduOrgTreeVOS) {
            List<GlobalEduOrgTreeVO> gradeList = orgTreeVO.getChildren();
            if (CollUtil.isEmpty(gradeList)) {
                continue;
            }
            List<String> classIds = gradeList.stream()
                    .filter(gradeVO -> gradeVO.getChildren() != null).flatMap(gradeVO -> gradeVO.getChildren().stream())
                    .filter(Objects::nonNull).map(GlobalEduOrgTreeVO::getId).map(String::valueOf).collect(Collectors.toList());

            classIdList.addAll(classIds);
        }
        return classIdList;
    }

    /**
     * 班级排行
     *
     * @param param
     * @return
     */
    private ClassCompareListVO queryClassScoreList(ClassCompareListDTO param) {
        this.check(param);
        String campusId = WebUtil.getCampusId();
        CheckClassInfoScoreListDTO query = new CheckClassInfoScoreListDTO()
                .setCampusId(campusId)
                .setCampusSectionId(param.getCampusSectionId())
                .setSectionCode(param.getSectionCode())
                .setIsCurrentYear(param.getIsCurrentYear())
                .setSchoolYear(param.getSchoolYear())
                .setStartTime(param.getStartTime())
                .setEndTime(DateUtil.endOfDay(param.getEndTime()));
        //不是全部年级，把年级赋值
        if (!"-1".equals(param.getGradeId())) {
            query.setGradeId(param.getGradeId());
        }
        // 当前校区下的默认维度列表
        List<CheckDimBaseListInfoVO> campusDimList = this.queryCampusDimList(campusId, Constant.YES);
        AssertUtil.checkNotEmpty(campusDimList, "当前校区没有检查维度，请检查");
        ClassCompareListVO result = new ClassCompareListVO();
        result.setDimList(campusDimList);

        List<CheckClassDimScoreListVO> classScoreList = checkClassInfoManager.queryClassDimScoreList(query);
        log.info("[班级排行]：classScoreList：{}", JSONUtil.toJsonStr(classScoreList));
        // 所有班级都没有值日记录则所有班级都是初始分
        if (CollUtil.isEmpty(classScoreList)) {
            classScoreList = Lists.newArrayList();
        }

        return this.convert(result, classScoreList, query, Boolean.TRUE);
    }

    /**
     * 班级排行--按年纪分组--有数据权限
     *
     * @param param
     * @return
     */
    @Override
    public List<GradeClassCompareListVO> queryGradeClassScoreListWithAuth(ClassCompareListDTO param) {
        Long schoolId = WebUtil.getSchoolIdLong();
        Long staffId = WebUtil.getStaffIdLong();

        ClassCompareListVO classScoreInfo = this.queryClassScoreList(param);
        if (ObjectUtil.isNull(classScoreInfo) || CollUtil.isEmpty(classScoreInfo.getClassScoreList())) {
            return Collections.emptyList();
        }
        // 全量数据
        List<ClassCompareInfoListVO> classScoreList = classScoreInfo.getClassScoreList();
        // 管理员可以查看所有班级排行
        List<String> roleCodeList = basicInfoService.queryStaffRoleCodeList(schoolId, staffId);

        if (CollUtil.isNotEmpty(CollUtil.intersection(roleCodeList, ModuleCodeEnum.listAdminRoleByCode(ModuleCodeEnum.CLASS_EVALUATE.getCode())))) {
            return this.convert(classScoreList);
        }

        // 普通角色只能查自己的数据，其他角色无数据
        Boolean ordinaryStaffFlag = roleLogic.isHaveOrdinaryRole(roleCodeList);
        if (!ordinaryStaffFlag) {
            return Collections.emptyList();
        }
        List<String> mineClassIdList = basicInfoService.queryOrdinaryStaffClassIdList();
        if (CollUtil.isEmpty(mineClassIdList)) {
            return Collections.emptyList();
        }
        List<ClassCompareInfoListVO> filterList = classScoreList.stream().filter(item -> mineClassIdList.contains(item.getClassId())).collect(Collectors.toList());
        return this.convert(filterList);
    }

    private List<GradeClassCompareListVO> convert(List<ClassCompareInfoListVO> classScoreList) {
        if (CollUtil.isEmpty(classScoreList)) {
            return Collections.emptyList();
        }
        Map<Long, List<ClassCompareInfoListVO>> gradeIdClassListMap = classScoreList.stream().collect(Collectors.groupingBy(ClassCompareInfoListVO::getGradeId));
        Set<Long> gradeIdSet = gradeIdClassListMap.keySet();
        List<GradeClassCompareListVO> gradeClassInfoList = new ArrayList<>(CollUtil.size(gradeIdSet));
        for (Long gradeId : gradeIdSet) {
            List<ClassCompareInfoListVO> classInfoList = gradeIdClassListMap.get(gradeId);
            ClassCompareInfoListVO classInfo = classInfoList.get(Constant.ZERO);

            GradeClassCompareListVO item = new GradeClassCompareListVO()
                    .setGradeId(gradeId)
                    .setGradeName(classInfo.getGradeName())
                    .setGradeCode(classInfo.getGradeCode());

            // 班级按总分倒序，班级num升序
            List<GradeClassCompareInfoListVO> sortClassInfoList = classInfoList.stream().map(classItem -> new GradeClassCompareInfoListVO()
                            .setClassId(classItem.getClassId())
                            .setClassName(classItem.getClassName())
                            .setClassNum(classItem.getClassNum())
                            .setScore(classItem.getScore())
                            .setGradeId(gradeId)
                            .setGradeName(classInfo.getGradeName())
                            .setGradeCode(classInfo.getGradeCode()))
                    .sorted(Comparator.comparing(GradeClassCompareInfoListVO::getScore, Comparator.reverseOrder())
                            .thenComparing(GradeClassCompareInfoListVO::getClassNum))
                    .collect(Collectors.toList());

            item.setClassScoreList(sortClassInfoList);

            gradeClassInfoList.add(item);
        }

        // 年纪按gradeCode升序
        return gradeClassInfoList.stream().sorted(Comparator.comparing(GradeClassCompareListVO::getGradeCode)).collect(Collectors.toList());
    }

    private void check(ClassCompareListDTO param) {
        AssertUtil.checkNotNull(param, "参数不能为空");
//        AssertUtil.checkNotBlank(param.getCampusSectionId(), "学段不能为空");
        AssertUtil.checkNotBlank(param.getSchoolYear(), "学年不能为空");
        AssertUtil.checkNotNull(param.getStartTime(), "开始时间不能为空");
        AssertUtil.checkNotNull(param.getEndTime(), "结束时间不能为空");
        AssertUtil.checkIsFalse(param.getStartTime().after(param.getEndTime()), "开始时间不能晚于结束时间");

//        Map<String, SchoolYearVo> sectionSchoolYearTimeMap = basicInfoService.querySectionSchoolYearStartTimeEndTimeMap();
//        AssertUtil.checkNotEmpty(sectionSchoolYearTimeMap, "当前学校没有学段学期，请检查");
//        String key = param.getSchoolYear() + StrPool.AT + param.getSectionCode();
//        SchoolYearVo schoolYear = sectionSchoolYearTimeMap.get(key);
//        AssertUtil.checkNotNull(schoolYear, "当前学校不存在当前学段、学年");
//
//        AssertUtil.checkBetween(param.getStartTime(), Convert.toDate(schoolYear.getStartTime()), Convert.toDate(schoolYear.getEndTime()), "查询时间必须在当前学年时间段内");
//        AssertUtil.checkBetween(param.getEndTime(), Convert.toDate(schoolYear.getStartTime()), Convert.toDate(schoolYear.getEndTime()), "查询时间必须在当前学年时间段内");
    }

    /**
     * 根据班级分数对象获取班级排行
     *
     * @param classScoreList
     * @param query
     * @return onlyDefaultDim true：只统计默认维度 false：统计所有维度
     */
    private ClassCompareListVO convert(ClassCompareListVO result, List<CheckClassDimScoreListVO> classScoreList, CheckClassInfoScoreListDTO query, Boolean onlyDefaultDim) {
        log.info("convert入参：{},{},{},{}", JSONUtil.toJsonStr(result), JSONUtil.toJsonStr(classScoreList),JSONUtil.toJsonStr(query),onlyDefaultDim);
        // 获取初始分
        Integer initScore = campusParamService.getCampusInitScore(query.getCampusId());
        log.info("校区初始分：{}", initScore);
        // 过滤出默认维度
        if (onlyDefaultDim && CollUtil.isNotEmpty(classScoreList)) {
            // 默认维度id集合
            List<Long> defaultDimIdList = result.getDimList().stream().map(CheckDimBaseListInfoVO::getCheckDimId).collect(Collectors.toList());
            classScoreList = classScoreList.stream().filter(item -> defaultDimIdList.contains(item.getCheckDimId())).collect(Collectors.toList());
        }
        // 当前校区下所有班级分数列表
        List<ClassCompareInfoListVO> classInfoList = Lists.newArrayList();
        result.setClassScoreList(classInfoList);
        // key:班级id value：班级所有的维度分数集合
        Map<String, List<CheckClassDimScoreListVO>> classIdClassScoreListMap = classScoreList.stream().collect(Collectors.groupingBy(CheckClassDimScoreListVO::getClassId));
        for (String classId : classIdClassScoreListMap.keySet()) {
            List<CheckClassDimScoreListVO> checkClassScoreList = classIdClassScoreListMap.get(classId);

            ClassCompareInfoListVO compare = new ClassCompareInfoListVO();
            classInfoList.add(compare);
            List<ClassCompareListCheckItemVO> dimList = Lists.newArrayList();
            compare.setDimScoreList(dimList);
            // 设置维度分数
            for (CheckClassDimScoreListVO item : checkClassScoreList) {
                ClassCompareListCheckItemVO dimScore = new ClassCompareListCheckItemVO();
                dimScore.setDimId(item.getCheckDimId());
                dimScore.setScore(item.getTotalScore());
                dimList.add(dimScore);
            }
            CheckClassDimScoreListVO classInfo = checkClassScoreList.get(Constant.ZERO);
            compare.setClassId(classId)
                    .setClassName(classInfo.getClassName());
            // 记录初始分
            BigDecimal totalScore = dimList.stream().map(ClassCompareListCheckItemVO::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
            compare.setScore(BigDecimal.valueOf(initScore).add(totalScore));
        }
        log.info("classInfoList-1：{}", JSONUtil.toJsonStr(classInfoList));
        // 查学段--学年内所有班级id：班级信息map
        Map<String, ClassSimpleVO> classIdClassInfoMap = basicInfoService.querySchoolYearClassIdClassInfoMap(query.getSectionCode(), query.getSchoolYear());
        log.info("classIdClassInfoMap：{}", JSONUtil.toJsonStr(classIdClassInfoMap));
        if (CollUtil.isNotEmpty(classIdClassInfoMap)) {
            // 补充学年班级信息
            this.appendSchoolYearClassInfo(classIdClassInfoMap, classInfoList);
            log.info("classInfoList-2：{}", JSONUtil.toJsonStr(classInfoList));
            // 补充没有值日记录的班级
            this.appendNoDutyRecordClassInfo(classIdClassInfoMap, classInfoList, initScore);
            log.info("classInfoList-3：{}", JSONUtil.toJsonStr(classInfoList));
        }
        // 过滤掉classInfoList中gradeId为空的数据
        classInfoList = classInfoList.stream().filter(item -> Objects.nonNull(item.getGradeId())).collect(Collectors.toList());

        // 排序
        List<ClassCompareInfoListVO> sortClassInfoList = classInfoList.stream()
                .filter(classCompareInfoListVO -> {
                    if(Objects.isNull(query.getGradeId())){
                        return true;
                    }
                    return Objects.equals(classCompareInfoListVO.getGradeId(), Convert.toLong(query.getGradeId()));
                })
                .sorted(Comparator.comparing(ClassCompareInfoListVO::getScore, Comparator.reverseOrder())
                        .thenComparing(ClassCompareInfoListVO::getGradeCode)
                        .thenComparing(ClassCompareInfoListVO::getClassNum))
                .collect(Collectors.toList());

        result.setClassScoreList(sortClassInfoList);
        return result;
    }

    /**
     * @param campusId
     * @param isDefault 是否默认的  0:否 1：是  null: 查所有
     * @return
     */
    private List<CheckDimBaseListInfoVO> queryCampusDimList(String campusId, Integer isDefault) {
        // 查当前学校所有检查维度
        ListCheckDimBaseInfoDTO queryCheckDim = new ListCheckDimBaseInfoDTO()
                .setCampusId(campusId)
                .setSourceType(SourceTypeEnum.LDHQ.getCode())
                .setContainsItemFlag(Boolean.FALSE)
                .setIsDefault(isDefault);
        List<CheckDimBaseInfoVO> dimBaseInfoList = checkDimService.queryCheckDimBaseInfoList(queryCheckDim);
        AssertUtil.checkNotEmpty(dimBaseInfoList, "当前校区没有检查维度，请检查");
        ClassCompareListVO result = new ClassCompareListVO();
        // 当前校区下所有默认的检查维度
        return checkConvert.toCheckDimBaseListInfoVOList(dimBaseInfoList);
    }

    /**
     * 设置学年学段内该班级的名称
     *
     * @param classScoreList
     */
    private void appendSchoolYearClassInfo(Map<String, ClassSimpleVO> classIdClassInfoMap, List<ClassCompareInfoListVO> classScoreList) {
        if (CollUtil.isEmpty(classIdClassInfoMap) || CollUtil.isEmpty(classScoreList)) {
            return;
        }
        for (ClassCompareInfoListVO item : classScoreList) {
            ClassSimpleVO classInfo = classIdClassInfoMap.get(item.getClassId());
            if (ObjectUtil.isNull(classInfo)) {
                continue;
            }
            item.setClassName(classInfo.getClassName())
                    .setClassNum(classInfo.getClassNum())
                    .setGradeCode(classInfo.getGradeCode())
                    .setGradeId(classInfo.getGradeId())
                    .setGradeName(classInfo.getGradeName());
        }
    }

    /**
     * 班级排行--颁发流动红旗--是否展示按层次展示班级
     *
     * @param param
     * @return
     */
    @Override
    public Boolean queryShowClassLevelFlag(ClassCompareLevelListDTO param) {
        try {
            QueryClassLevelListWithScoreVO classLevelInfo = this.queryClassWithLevelScoreInfo(param);
            if (ObjectUtil.isNull(classLevelInfo) || ObjectUtil.isNull(classLevelInfo.getShowClassLevelFlag())) {
                return Boolean.FALSE;
            }

            return classLevelInfo.getShowClassLevelFlag();
        } catch (Exception exp) {
            log.warn("获取班级层次失败", exp);
            return Boolean.FALSE;
        }
    }

    /**
     * 按班级层次查班级列表
     *
     * @return
     */
    @Override
    public QueryClassLevelListWithScoreVO queryClassWithLevelScoreInfo(ClassCompareLevelListDTO param) {

        ClassCompareListDTO query = checkConvert.convert(param);
        this.check(query);
        query.setEndTime(DateUtil.endOfDay(query.getEndTime()));

        Long schoolId = WebUtil.getSchoolIdLong();
        Long campusId = WebUtil.getCampusIdLong();
        Long staffId = WebUtil.getStaffIdLong();

        ClassLevelListWithAuthDTO queryClass = new ClassLevelListWithAuthDTO()
                .setSchoolId(schoolId)
                .setCampusId(campusId)
                .setCampusSectionId(param.getCampusSectionId())
                .setStaffId(staffId);
        QueryClassLevelListWithScoreVO levelClassInfo = queryClassGroupByLevelWithAuth(queryClass);

        if (ObjectUtil.isNull(levelClassInfo) || !levelClassInfo.getShowClassLevelFlag()) {
            return new QueryClassLevelListWithScoreVO().setShowClassLevelFlag(Boolean.FALSE);
        }
        // 补充分数信息
        this.appendScore(levelClassInfo, query, campusId);
        //排序
        this.sort(levelClassInfo);

        return levelClassInfo;
    }

    private void sort(QueryClassLevelListWithScoreVO levelClassInfo) {
        List<ClassLevelClassListWithScoreVO> noLevelClassList = levelClassInfo.getNoLevelClassList();
        if (CollUtil.isNotEmpty(noLevelClassList)) {
            levelClassInfo.setNoLevelClassList(this.sort(noLevelClassList));
        }
        List<ClassLevelListWithScoreVO> levelClassList = levelClassInfo.getLevelClassList();
        if (CollUtil.isNotEmpty(levelClassList)) {
            for (ClassLevelListWithScoreVO level : levelClassList) {
                level.setClassList(this.sort(level.getClassList()));
            }
        }

        List<ClassLevelListWithScoreVO> levelList = levelClassList.stream().sorted(Comparator.comparing(ClassLevelListWithScoreVO::getClassLevelId)).collect(Collectors.toList());
        levelClassInfo.setLevelClassList(levelList);
    }

    private List<ClassLevelClassListWithScoreVO> sort(List<ClassLevelClassListWithScoreVO> param) {
        return param.stream().sorted(Comparator.comparing(ClassLevelClassListWithScoreVO::getTotalScore, Comparator.reverseOrder()).
                        thenComparing(ClassLevelClassListWithScoreVO::getGradeCode)
                        .thenComparing(ClassLevelClassListWithScoreVO::getClassNum))
                .collect(Collectors.toList());
    }

    /**
     * 按班级层次查班级列表
     *
     * @return
     */
    @Override
    public QueryClassLevelListWithScoreVO queryClassGroupByLevelWithAuth(ClassLevelListWithAuthDTO param) {
        QueryClassLevelListVO levelClassInfo = basicInfoService.queryClassGroupByLevelWithAuth(param);
        // 转换
        return this.convert(levelClassInfo);
    }

    private void appendScore(QueryClassLevelListWithScoreVO classLevelInfo, ClassCompareListDTO param, Long campusId) {
        if (ObjectUtil.isNull(classLevelInfo) || !classLevelInfo.getShowClassLevelFlag()) {
            return;
        }

        CheckClassInfoScoreListDTO query = new CheckClassInfoScoreListDTO()
                .setCampusId(Convert.toStr(campusId))
                .setSectionCode(param.getSectionCode())
                .setSchoolYear(param.getSchoolYear())
                .setStartTime(param.getStartTime())
                .setEndTime(param.getEndTime());
        // 获取班级id--分数map（不包含初始分）
        Map<String, BigDecimal> classIdScoreMap = queryClassIdTotalScoreMap(query);

        // 获取初始分
        BigDecimal initScore = BigDecimal.valueOf(campusParamService.getCampusInitScore(Convert.toStr(campusId)));

        // 有班级层次的班级补充分数
        this.appendScore(classLevelInfo.getLevelClassList(), classIdScoreMap, initScore);
        // 没有班级层次的班级补充分数
        this.appendClassScore(classLevelInfo.getNoLevelClassList(), classIdScoreMap, initScore);
    }

    private void appendClassScore(List<ClassLevelClassListWithScoreVO> classList, Map<String, BigDecimal> classIdScoreMap, BigDecimal initScore) {
        if (CollUtil.isEmpty(classList)) {
            return;
        }
        for (ClassLevelClassListWithScoreVO classInfo : classList) {
            BigDecimal score = classIdScoreMap.get(classInfo.getClassId());
            if (ObjectUtil.isNull(score)) {
                classInfo.setTotalScore(initScore);
                continue;
            }
            classInfo.setTotalScore(initScore.add(score));
        }
    }

    private void appendScore(List<ClassLevelListWithScoreVO> levelClassList, Map<String, BigDecimal> classIdScoreMap, BigDecimal initScore) {
        if (CollUtil.isEmpty(levelClassList)) {
            return;
        }
        for (ClassLevelListWithScoreVO level : levelClassList) {
            List<ClassLevelClassListWithScoreVO> classList = level.getClassList();
            this.appendClassScore(classList, classIdScoreMap, initScore);
        }
    }

    /**
     * 补充没有检查记录的班级的分数信息
     *
     * @param classScoreList
     */
    private void appendNoDutyRecordClassInfo(Map<String, ClassSimpleVO> classIdClassInfoMap, List<ClassCompareInfoListVO> classScoreList, Integer initScore) {
        if (CollUtil.isEmpty(classIdClassInfoMap)) {
            return;
        }
        List<String> appendClassIdList = classIdClassInfoMap.keySet().stream().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(classScoreList)) {
            List<String> existClassIdList = classScoreList.stream().map(ClassCompareInfoListVO::getClassId).collect(Collectors.toList());
            appendClassIdList.removeAll(existClassIdList);
        }

        for (String classId : appendClassIdList) {
            ClassSimpleVO classInfo = classIdClassInfoMap.get(classId);
            log.info("classInfoList-3-classInfo：{}", JSONUtil.toJsonStr(classInfo));
            ClassCompareInfoListVO item = new ClassCompareInfoListVO()
                    .setClassId(classId)
                    .setClassName(classInfo.getClassName())
                    .setClassNum(classInfo.getClassNum())
                    .setScore(BigDecimal.valueOf(initScore))
                    .setGradeCode(classInfo.getGradeCode())
                    .setGradeId(classInfo.getGradeId())
                    .setGradeName(classInfo.getGradeName());

            classScoreList.add(item);
        }
    }

    private QueryClassLevelListWithScoreVO convert(QueryClassLevelListVO param) {
        QueryClassLevelListWithScoreVO result = new QueryClassLevelListWithScoreVO();
        if (ObjectUtil.isNull(param) || !param.getShowClassLevelFlag()) {
            return result.setShowClassLevelFlag(Boolean.FALSE);
        }

        result.setShowClassLevelFlag(param.getShowClassLevelFlag());
        // 没有班级层次的班级
        List<ClassLevelClassListWithScoreVO> noLevelList = checkConvert.toClassLevelClassListWithScoreVOList(param.getNoLevelClassList());
        result.setNoLevelClassList(noLevelList);

        if (CollUtil.isEmpty(param.getLevelClassList())) {
            return result;
        }
        // 有班级层级的班级
        List<ClassLevelListWithScoreVO> levelClassList = param.getLevelClassList().stream().map(item -> {
            List<ClassLevelClassListWithScoreVO> classInfoList = checkConvert.toClassLevelClassListWithScoreVOList(item.getClassList());
            return new ClassLevelListWithScoreVO()
                    .setClassLevelId(item.getClassLevelId())
                    .setClassLevelName(item.getClassLevelName())
                    .setClassList(classInfoList);
        }).collect(Collectors.toList());

        result.setLevelClassList(levelClassList);

        return result;
    }

    /**
     * 查班级id：班级总分map(不包含初始分)
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, BigDecimal> queryClassIdTotalScoreMap(CheckClassInfoScoreListDTO param) {
        List<CheckClassScoreListVO> classScoreList = checkClassInfoManager.queryClassScoreList(param);
        if (CollUtil.isEmpty(classScoreList)) {
            return Collections.emptyMap();
        }

        return classScoreList.stream().collect(Collectors.toMap(CheckClassScoreListVO::getClassId, CheckClassScoreListVO::getTotalScore));
    }

    /**
     * 导出值日记录
     *
     * @param response
     * @param dto
     */
    @Override
    public void exportDutyRecord(HttpServletResponse response, DutyRecordPageDTO dto) throws IOException {
        //日期赋值
        dto.setCheckStartTime(Objects.isNull(dto.getCheckStartTime()) ? dto.getStartTime() : dto.getCheckStartTime());
        dto.setCheckEndTime(Objects.isNull(dto.getCheckEndTime()) ? dto.getEndTime() : dto.getCheckEndTime());
        //日期校验
        long days = DateUtil.betweenDay(dto.getCheckStartTime(), dto.getCheckEndTime(), true);
        Assert.isFalse(Convert.toLong(Constant.DAY_NUMBER_OF_YEAR) < days, () -> new BizException(BizExceptionEnum.EXPORT_DUTY_RECORD_DATE_ERROR.getMessage()));

        IPage<DutyRecordPageVO> page = this.queryCheckClassInfoPageWithAuth(dto);
        //数据校验
        Assert.isFalse(ObjectUtil.isNull(page) || CollUtil.isEmpty(page.getRecords()), () -> new BizException(BizExceptionEnum.EXPORT_DUTY_RECORD_ERROR.getMessage()));

        //获取数据
        dto.setPageNum(Constant.ONE);
        dto.setPageSize(Convert.toInt(page.getTotal()));
        IPage<DutyRecordPageVO> recordPageVOIPage = this.queryCheckClassInfoPageWithAuth(dto);
        Assert.isFalse(ObjectUtil.isNull(recordPageVOIPage) || CollUtil.isEmpty(recordPageVOIPage.getRecords()), () -> new BizException(BizExceptionEnum.EXPORT_DUTY_RECORD_ERROR.getMessage()));

        List<DutyRecordPageVO> records = recordPageVOIPage.getRecords();
        // 挂上寝室信息
        this.handleDormitory(records);
        // 填充扣分原因
        this.processDutyRecords(records);

        List<ExcelDutyRecordVO> targetRankVOS = checkConvert.toExcelDutyRecordList(records);

        List<Map<String, Object>> schoolCourseList = new ArrayList<>();
        Map<String, Object> sheet1Map = new HashMap<String, Object>();
        ExportParams sheet1Params = new ExportParams();
        sheet1Params.setSheetName("值日记录");
        sheet1Map.put("title", sheet1Params);
        sheet1Map.put("entity", ExcelDutyRecordVO.class);
        sheet1Map.put("data", targetRankVOS);
        schoolCourseList.add(sheet1Map);
        Workbook workbook = ExcelExportUtil.exportExcel(schoolCourseList, ExcelType.XSSF);
        EasyPoiUtil.adjustStyle(workbook.getSheetAt(0));
        EasyPoiUtil.downLoadExcelWithFormat("值日记录", response, workbook);
//        EasyPoiUtil.saveExcelFile(workbook, "c://home//excel//1.xlsx");
    }

    private void handleDormitory(List<DutyRecordPageVO> records) {
        List<Long> checkClassInfoIds = CollStreamUtil.toList(records, DutyRecordPageVO::getCheckClassInfoId);
        List<CheckClassRelation> checkClassRelations = checkClassRelationManager.list(new LambdaQueryWrapper<CheckClassRelation>().in(CheckClassRelation::getCheckClassInfoId, checkClassInfoIds)
                .eq(CheckClassRelation::getBusinessType, "dormitory"));
        if (CollUtil.isEmpty(checkClassRelations)){
            return;
        }
        Map<Long, String> dormitoryMap = CollStreamUtil.toMap(checkClassRelations, CheckClassRelation::getCheckClassInfoId, CheckClassRelation::getBusinessName);
        for (DutyRecordPageVO d : records){
            String dormitory = dormitoryMap.get(d.getCheckClassInfoId());
            if (StrUtil.isNotBlank(dormitory)){
                d.setDormitory(dormitory);
            }
        }
    }

    /**
     * pc端--班级排行导出
     */
    @Override
    public void exportOld(ClassCompareListDTO param, HttpServletResponse response) throws IOException {
        //入参校验
        Assert.isFalse(BeanUtil.hasNullField(param), () -> new BizException(BizExceptionEnum.PARAMETER_ERROR.getMessage()));
        //查询班级排行
        ClassCompareListVO classCompareListVO = this.queryClassScoreListWithAuth(param);
        //格式转换
        ArrayList<ExcelClassCompareVO> excelClassCompareVOS = new ArrayList<>();
        listExcelClassCompareVos(classCompareListVO, excelClassCompareVOS);

        //下载
        List<Map<String, Object>> classCompareInfoListVOS = new ArrayList<>();
        Map<String, Object> sheet1Map = new HashMap<String, Object>();
        ExportParams sheet1Params = new ExportParams();
        sheet1Params.setSheetName("班级评比统计");
        sheet1Map.put("title", sheet1Params);
        sheet1Map.put("entity", ExcelClassCompareVO.class);
        sheet1Map.put("data", excelClassCompareVOS);
        classCompareInfoListVOS.add(sheet1Map);
        Workbook workbook = ExcelExportUtil.exportExcel(classCompareInfoListVOS, ExcelType.XSSF);
        EasyPoiUtil.adjustStyle(workbook.getSheetAt(0));
        EasyPoiUtil.downLoadExcelWithFormat(DateUtil.format(param.getStartTime(), Constant.PATTERN_FORMAT) + "~" + DateUtil.format(param.getEndTime(), Constant.PATTERN_FORMAT) + "班级评比统计", response, workbook);
    }
    /**
     * pc端--班级排行导出
     */
    @Override
    public void export(ClassCompareListDTO param, HttpServletResponse response) throws IOException {
        if(StringUtils.isBlank(param.getSectionCode())){
            param.setSectionCode(Constant.MINUS_ONE);
        }
        if(Objects.isNull(param.getGradeId())){
            Assert.isTrue(Objects.nonNull(param.getSchoolYear()) && Objects.nonNull(param.getCampusSectionId())
                            && Objects.nonNull(param.getStartTime()) && Objects.nonNull(param.getEndTime()),
                    BizExceptionEnum.PARAMETER_ERROR.getMessage());
        }else{
            //入参校验
            Assert.isFalse(BeanUtil.hasNullField(param), () -> new BizException(BizExceptionEnum.PARAMETER_ERROR.getMessage()));
        }
        //查询班级排行
        ClassCompareListVO classCompareListVO = this.queryClassScoreListWithAuth(param);
        List<CheckDimBaseListInfoVO> dimList = classCompareListVO.getDimList();
        if (CollUtil.isEmpty(dimList)){
            log.error("当前校区未配置dim信息");
            return;
        }
        Map<Long, String> dimMap = CollStreamUtil.toMap(dimList, CheckDimBaseListInfoVO::getCheckDimId, CheckDimBaseListInfoVO::getCheckDimName);

        List<ClassCompareInfoListVO> classScoreList = classCompareListVO.getClassScoreList();
        if (CollUtil.isEmpty(classScoreList)){
            log.warn("当前校区下没有班级排行信息");
            return;
        }
        // 数据转换
        List<ExcelClassCompareNewVO> excelClassCompareNewVOS = convertExcelClassCompareNewVos(classScoreList, dimMap);

        //下载
        List<Map<String, Object>> classCompareInfoListVOS = new ArrayList<>();
        Map<String, Object> sheet1Map = new HashMap<String, Object>();
        ExportParams sheet1Params = new ExportParams();
        sheet1Params.setSheetName("班级评比统计");
        sheet1Map.put("title", sheet1Params);
        sheet1Map.put("entity", ExcelClassCompareNewVO.class);
        sheet1Map.put("data", excelClassCompareNewVOS);
        classCompareInfoListVOS.add(sheet1Map);
        Workbook workbook = ExcelExportUtil.exportExcel(classCompareInfoListVOS, ExcelType.XSSF);
        EasyPoiUtil.adjustStyle(workbook.getSheetAt(0));
        EasyPoiUtil.downLoadExcelWithFormat(DateUtil.format(param.getStartTime(), Constant.PATTERN_FORMAT) + "~" + DateUtil.format(param.getEndTime(), Constant.PATTERN_FORMAT) + "班级评比统计", response, workbook);
//        EasyPoiUtil.saveExcelFile(workbook, "c://home//excel//1.xlsx");
    }

    private List<ExcelClassCompareNewVO> convertExcelClassCompareNewVos(List<ClassCompareInfoListVO> classScoreList, Map<Long, String> dimMap) {
        Map<String, Long> dimNameMap = MapUtil.inverse(dimMap);
        List<ExcelClassCompareNewVO> excelClassCompareNewVOS = new ArrayList<>();
        for (ClassCompareInfoListVO d : classScoreList){
            List<ClassCompareListCheckItemVO> dimScoreList = d.getDimScoreList();
            Map<Long, BigDecimal> dimScoreMap = CollStreamUtil.toMap(dimScoreList, ClassCompareListCheckItemVO::getDimId, ClassCompareListCheckItemVO::getScore);

            ExcelClassCompareNewVO excelClassCompareNewVO = new ExcelClassCompareNewVO();
            excelClassCompareNewVO.setClassName(d.getClassName());
            excelClassCompareNewVO.setScore(NumberUtil.toStr(d.getScore()));
            excelClassCompareNewVO.setEtiquetteScore(dimScoreMap.containsKey(dimNameMap.get(CheckDimEnum.ETIQUETTE.getMessage())) ? NumberUtil.toStr(dimScoreMap.get(dimNameMap.get(CheckDimEnum.ETIQUETTE.getMessage()))) : "-");
            excelClassCompareNewVO.setDisciplineScore(dimScoreMap.containsKey(dimNameMap.get(CheckDimEnum.DISCIPLINE.getMessage())) ? NumberUtil.toStr(dimScoreMap.get(dimNameMap.get(CheckDimEnum.DISCIPLINE.getMessage()))) : "-");
            excelClassCompareNewVO.setSanitationScore(dimScoreMap.containsKey(dimNameMap.get(CheckDimEnum.SANITATION.getMessage())) ? NumberUtil.toStr(dimScoreMap.get(dimNameMap.get(CheckDimEnum.SANITATION.getMessage()))) : "-");

            excelClassCompareNewVOS.add(excelClassCompareNewVO);
        }
        // 处理排名
        for (int i = 1;i < excelClassCompareNewVOS.size() + 1; i++){
            ExcelClassCompareNewVO excelClassCompareNewVO = excelClassCompareNewVOS.get(i - 1);
            excelClassCompareNewVO.setRank(Convert.toStr(i));
        }
        for (int i = 1; i < excelClassCompareNewVOS.size(); i++){
            ExcelClassCompareNewVO oldExcelClassCompareNewVO = excelClassCompareNewVOS.get(i-1);
            ExcelClassCompareNewVO excelClassCompareNewVO = excelClassCompareNewVOS.get(i);
            if (ObjectUtil.equal(excelClassCompareNewVO.getScore(), oldExcelClassCompareNewVO.getScore())){
                excelClassCompareNewVO.setRank(oldExcelClassCompareNewVO.getRank());
            }
        }
        return excelClassCompareNewVOS;
    }

    private static void listExcelClassCompareVos(ClassCompareListVO classCompareListVO,  ArrayList<ExcelClassCompareVO> excelClassCompareVOS) {
        List<ClassCompareInfoListVO> classScoreList = classCompareListVO.getClassScoreList();
        List<CheckDimBaseListInfoVO> dimList = classCompareListVO.getDimList();
        Assert.isFalse(ObjectUtil.isNull(classCompareListVO) || CollUtil.isEmpty(classScoreList), () -> new BizException("当前查询条件下无记录"));
        classScoreList.forEach(s -> {
            List<ClassCompareListCheckItemVO> dimScoreList = s.getDimScoreList();
            //总分
            ExcelClassCompareVO excelClassCompareVO = new ExcelClassCompareVO();
            excelClassCompareVO.setClassName(s.getClassName());
            excelClassCompareVO.setCheckDimName(CheckDimEnum.TOTAL_SCORE.getMessage());
            excelClassCompareVO.setScore(new BigDecimal(s.getScore().stripTrailingZeros().toPlainString()));
            excelClassCompareVOS.add(excelClassCompareVO);

            //礼仪
            ExcelClassCompareVO excelClassCompareVO1 = new ExcelClassCompareVO();
            getExcelClassCompareVO(excelClassCompareVOS, s, excelClassCompareVO1, dimList, dimScoreList,CheckDimEnum.ETIQUETTE.getMessage());

            //纪律
            ExcelClassCompareVO excelClassCompareVO2 = new ExcelClassCompareVO();
            getExcelClassCompareVO(excelClassCompareVOS, s, excelClassCompareVO2, dimList, dimScoreList,CheckDimEnum.DISCIPLINE.getMessage());

            //卫生
            ExcelClassCompareVO excelClassCompareVO3 = new ExcelClassCompareVO();
            getExcelClassCompareVO(excelClassCompareVOS, s, excelClassCompareVO3, dimList, dimScoreList,CheckDimEnum.SANITATION.getMessage());
        });
    }

    private static void getExcelClassCompareVO(ArrayList<ExcelClassCompareVO> excelClassCompareVOS, ClassCompareInfoListVO s, ExcelClassCompareVO excelClassCompareVO1, List<CheckDimBaseListInfoVO> dimList, List<ClassCompareListCheckItemVO> dimScoreList,String checkDimName) {
        excelClassCompareVO1.setClassName(s.getClassName());
        excelClassCompareVO1.setCheckDimName(checkDimName);
        List<CheckDimBaseListInfoVO> infoVOS = dimList.stream().filter(d -> checkDimName.equals(d.getCheckDimName())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(infoVOS) && CollUtil.isNotEmpty(dimScoreList)) {
            CheckDimBaseListInfoVO checkDimBaseListInfoVO = infoVOS.get(0);
            List<ClassCompareListCheckItemVO> itemVOS = dimScoreList.stream().filter(m -> checkDimBaseListInfoVO.getCheckDimId().equals(m.getDimId())).collect(Collectors.toList());
            excelClassCompareVO1.setScore(CollUtil.isEmpty(itemVOS) ? BigDecimal.ZERO : new BigDecimal(itemVOS.get(0).getScore().stripTrailingZeros().toPlainString()));
        } else {
            excelClassCompareVO1.setScore(BigDecimal.ZERO);
        }
        excelClassCompareVOS.add(excelClassCompareVO1);
    }
}




