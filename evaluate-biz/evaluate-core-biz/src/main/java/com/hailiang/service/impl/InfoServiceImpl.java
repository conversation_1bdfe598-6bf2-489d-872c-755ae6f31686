package com.hailiang.service.impl;

import static com.hailiang.enums.InfoTypeEnum.SPEED_OPTION_SCHOOL_SCHOOL;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.hailiang.annotation.CurrentStaffConvert;
import com.hailiang.annotation.MethodExecuteTime;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.common.lock.annotation.RLock;
import com.hailiang.constant.Constant;
import com.hailiang.convert.BehaviourRecordConvert;
import com.hailiang.convert.InfoConvert;
import com.hailiang.entity.EvaluateBehaviourImportsRecordDetailPO;
import com.hailiang.entity.EvaluateBehaviourRecordExtPO;
import com.hailiang.entity.EvaluateBehaviourRecordOptExtPO;
import com.hailiang.enums.ApprovalOperateEnum;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.EvaluateTypeEnum;
import com.hailiang.enums.MessageLogBusinessTypeEnum;
import com.hailiang.enums.MessageLogMessageTypeEnum;
import com.hailiang.enums.MessageLogTerminalTypeEnum;
import com.hailiang.enums.ModuleNameEnum;
import com.hailiang.enums.OperateTypeEnum;
import com.hailiang.enums.PointMqBusinessTypeEnum;
import com.hailiang.enums.PointMqDataOperateTypeEnum;
import com.hailiang.enums.RecordDataSourceEnum;
import com.hailiang.enums.RevaluateBusinessSourceEnum;
import com.hailiang.enums.RevaluateBusinessTypeEnum;
import com.hailiang.enums.SaasClassTypeEnum;
import com.hailiang.enums.SaasOrgQueryEnum;
import com.hailiang.enums.StudentInfoTypeEnum;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.enums.SubmitRateEnum;
import com.hailiang.enums.TargetReviewTypeEnum;
import com.hailiang.enums.TaskApprovalEnum;
import com.hailiang.enums.TaskRoleTypeEnum;
import com.hailiang.enums.TaskStatusEnum;
import com.hailiang.enums.TaskUserTypeEnum;
import com.hailiang.enums.motivate.CoinExchangeOperateTypeEnum;
import com.hailiang.enums.motivate.CoinExchangeStrategyEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.helper.ChannelMsgHelper;
import com.hailiang.helper.EvaluateReminderHandle;
import com.hailiang.helper.InfoHelper;
import com.hailiang.helper.SaasClassHelper;
import com.hailiang.internal.InternalDriveRemote;
import com.hailiang.internal.model.request.ComputeRecordCommentRequest;
import com.hailiang.internal.model.request.ComputeRecordRequest;
import com.hailiang.internal.model.request.ComputeRecordStudentRequest;
import com.hailiang.internal.model.request.PlanListsByEvaluateRequest;
import com.hailiang.internal.model.request.PlanListsRequest;
import com.hailiang.internal.model.response.InternalDrivePointRecordResponse;
import com.hailiang.internal.model.response.PlanListsResponse;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.logic.InfoLogic;
import com.hailiang.logic.PointsSendLogic;
import com.hailiang.logic.RevaluateHistoryDataLogic;
import com.hailiang.logic.TargetLogic;
import com.hailiang.logic.TaskLogic;
import com.hailiang.logic.TaskOperateLogManager;
import com.hailiang.logic.TermLogic;
import com.hailiang.manager.DorisBehaviourRecordManager;
import com.hailiang.manager.EvaluateBehaviourImportsRecordDetailManager;
import com.hailiang.manager.EvaluateBehaviourRecordExtManager;
import com.hailiang.manager.EvaluateBehaviourRecordOptExtManager;
import com.hailiang.manager.EvaluateHelpBehaviourRecordManager;
import com.hailiang.manager.EvaluateTargetReviewTeacherManager;
import com.hailiang.mapper.SpeedTargetGroupMapper;
import com.hailiang.mapper.mongo.InfoDao;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.datastatistics.query.RevaluateHistoryDataQuery;
import com.hailiang.model.dto.QueryClassSubjectRelDTO;
import com.hailiang.model.dto.StudentIdRequest;
import com.hailiang.model.dto.internaldrive.merge.ThirdToXdlRecordMessageDTO;
import com.hailiang.model.dto.internaldrive.modify.InfoModifyDTO;
import com.hailiang.model.dto.internaldrive.modify.SubmitInfoModifyDTO;
import com.hailiang.model.dto.internaldrive.modify.SubmitOptionInfoModifyDTO;
import com.hailiang.model.dto.internaldrive.modify.SubmitOptionInfoSubModifyDTO;
import com.hailiang.model.dto.motivate.BehaviourPointExchangeDTO;
import com.hailiang.model.dto.motivate.BehaviourPointExchangeDetailDTO;
import com.hailiang.model.dto.query.GetEvaluateInfoDetailQueryDTO;
import com.hailiang.model.dto.remove.InfoRemoveDTO;
import com.hailiang.model.dto.save.ApprovalHandleDTO;
import com.hailiang.model.dto.save.BehaviourRecordHandleDTO;
import com.hailiang.model.dto.save.BehaviourRecordSaveDTO;
import com.hailiang.model.dto.save.InfoSaveDTO;
import com.hailiang.model.dto.save.SubjectRelDTO;
import com.hailiang.model.dto.save.SubmitInfoSaveDTO;
import com.hailiang.model.dto.save.SubmitOptionInfoSaveDTO;
import com.hailiang.model.dto.save.SubmitOptionInfoSubSaveDTO;
import com.hailiang.model.dto.save.SubmitSubjectValueDTO;
import com.hailiang.model.dto.save.TaskOperateLogSaveDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.EvaluateHelpBehaviourRecordPO;
import com.hailiang.model.entity.EvaluateTargetReviewTeacherPO;
import com.hailiang.model.entity.MessageLog;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TaskOperateLog;
import com.hailiang.model.entity.TaskPO;
import com.hailiang.model.entity.mongo.Info;
import com.hailiang.model.entity.mongo.StudentInfo;
import com.hailiang.model.entity.mongo.SubStudentInfo;
import com.hailiang.model.entity.mongo.SubmitInfo;
import com.hailiang.model.response.StudentClassResponse;
import com.hailiang.model.response.speed.SpeedRecordResponse;
import com.hailiang.model.response.speed.SpeedRecordStudentDetailResponse;
import com.hailiang.model.response.speed.SpeedRecordStudentResponse;
import com.hailiang.model.vo.GetEvaluateInfoDetailVO;
import com.hailiang.model.vo.ModifyHistoryVO;
import com.hailiang.model.vo.SchoolYearVo;
import com.hailiang.model.vo.StudentInfoVO;
import com.hailiang.model.vo.SubmitInfoSubVO;
import com.hailiang.model.vo.SubmitInfoVO;
import com.hailiang.model.vo.SubmitOptionInfoSaveVO;
import com.hailiang.remote.ding.utils.DingDingMsgUtil;
import com.hailiang.remote.feishu.domain.FeiMsgReqDTO;
import com.hailiang.remote.feishu.domain.FeiShuResultDTO;
import com.hailiang.remote.feishu.utils.FeiShuMsgUtil;
import com.hailiang.remote.hai.SendMsgManager;
import com.hailiang.remote.hai.domain.dto.request.MsgContent;
import com.hailiang.remote.hai.domain.dto.request.SendMsgRequestDTO;
import com.hailiang.remote.hai.domain.dto.response.SendMsgResponseDTO;
import com.hailiang.remote.internaldrive.EventSendHelper;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.dto.educational.EduStaffClassQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentClassQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.dto.staff.StaffOrgQueryDTO;
import com.hailiang.remote.saas.dto.student.StudentByIdQuery;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.pojo.educational.EduClassBaseInfoPojo;
import com.hailiang.remote.saas.pojo.educational.EduParentInfoPojo;
import com.hailiang.remote.saas.vo.educational.EduClassInfoLinkVO;
import com.hailiang.remote.saas.vo.educational.EduStaffClassVO;
import com.hailiang.remote.saas.vo.educational.EduStaffSubjectVO;
import com.hailiang.remote.saas.vo.educational.EduStaffTeachClassVO;
import com.hailiang.remote.saas.vo.educational.EduStudentClassVO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.staff.StaffBatchVO;
import com.hailiang.remote.saas.vo.staff.StaffNOVO;
import com.hailiang.remote.saas.vo.staff.StaffOrgsVO;
import com.hailiang.remote.saas.vo.student.StudentVO;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasClassManager;
import com.hailiang.saas.SaasStaffCacheManager;
import com.hailiang.saas.SaasStaffManager;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.dto.StudentDTO;
import com.hailiang.saas.model.dto.StudentPageQueryDTO;
import com.hailiang.saas.model.vo.StudentInfo1VO;
import com.hailiang.saas.model.vo.educational.TchClassOpenV2VO;
import com.hailiang.saas.model.vo.staff.ClassSubjectTeacherVO;
import com.hailiang.saas.model.vo.staff.ClassTeacherInfoVO;
import com.hailiang.service.ActivityRuleMatchService;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.BehaviourRecordService;
import com.hailiang.service.ConvertInitialService;
import com.hailiang.service.InfoService;
import com.hailiang.service.MessageLogService;
import com.hailiang.service.ReportPushRuleService;
import com.hailiang.service.TargetService;
import com.hailiang.service.TaskOperateLogService;
import com.hailiang.service.TaskService;
import com.hailiang.util.AssertUtil;
import com.hailiang.util.RequestUtil;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import com.hailiang.xxb.manage.XxbManager;
import com.hailiang.xxb.model.ClassNoticeRequest;
import com.hailiang.xxb.model.ClassStudentRequest;
import com.hailiang.xxb.model.NoticeStudentRequest;
import com.yomahub.tlog.core.annotation.TLogAspect;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class InfoServiceImpl implements InfoService {

    @Autowired(required = false)
    protected MongoTemplate mongoTemplate;
    @Autowired
    ThreadPoolTaskExecutor evaluateExecutor;

    @Resource
    private ConvertInitialService convertInitialService;
    @Resource
    private InfoDao infoDao;
    @Resource
    private InfoConvert infoConvert;
    @Resource
    private TaskOperateLogService taskOperateLogService;
    @Resource
    private BehaviourRecordService behaviourRecordService;
    @Resource
    private TaskOperateLogManager taskOperateLogManager;
    @Resource
    private InfoLogic infoLogic;
    @Resource
    private InfoHelper infoHelper;
    @Resource
    private BehaviourRecordManager behaviourRecordManager;
    @Resource
    private DorisBehaviourRecordManager dorisBehaviourRecordManager;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private CacheSaasManager cacheSaasManager;
    @Resource
    private TaskLogic taskLogic;
    @Resource
    private TaskService taskService;
    @Resource
    private BasicInfoService basicInfoService;
    @Resource
    private SendMsgManager sendMsgManager;
    @Resource
    private MessageLogService messageLogService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private DingDingMsgUtil dingDingMsgUtil;
    @Resource
    private FeiShuMsgUtil feiShuMsgUtil;
    @Resource
    private ActivityRuleMatchService activityRuleMatchService;
    @Resource
    private BehaviourRecordConvert convert;
    @Resource
    private TargetLogic targetLogic;
    @Resource
    private SaasStaffCacheManager saasStaffCacheManager;
    @Resource
    private SaasStudentManager saasStudentManager;
    @Resource
    private EvaluateBehaviourRecordOptExtManager evaluateBehaviourRecordOptExtManager;
    @Value("${third.ding.messageUrl}")
    private String messageUrl;
    @Value("${third.ding.titleColor}")
    private String titleColor;

    @Resource
    InternalDriveRemote internalDriveRemote;
    @Resource
    private SpeedTargetGroupMapper speedTargetGroupMapper;
    @Resource
    private EvaluateBehaviourRecordExtManager evaluateBehaviourRecordExtManager;

    @Resource
    private EvaluateHelpBehaviourRecordManager evaluateHelpBehaviourRecordManager;
    @Resource
    private RevaluateHistoryDataLogic revaluateHistoryDataLogic;
    @Resource
    private TermLogic termLogic;
    @Resource
    private EventSendHelper eventSendHelper;
    @Resource
    private ChannelMsgHelper channelMsgHelper;
    @Resource
    private SaasClassHelper saasClassHelper;
    @Resource
    private EvaluateReminderHandle evaluateReminderHandle;
    @Resource
    private TargetService targetService;
    @Value("${third.sendEvent.xwlAddRecord}")
    private String xwlAddRecord;
    @Value("${third.sendEvent.xwlDelRecord}")
    private String xwlDelRecord;
    @Value("${rocketmq.topic.sportTarget}")
    private String sportTargetTopic;
    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private EvaluateBehaviourImportsRecordDetailManager evaluateBehaviourImportsRecordDetailManager;
    @Resource
    private EvaluateTargetReviewTeacherManager reviewTeacherManager;
    @Resource
    private SaasClassManager saasClassManager;
    @Resource
    private SaasStaffManager saasStaffManager;
    @Resource
    private ReportPushRuleService reportPushRuleService;
    @Resource
    private PointsSendLogic pointsSendLogic;

    @Resource
    private XxbManager xxbManager;

    /**
     * 行为记录新增/修改 影响活动任务
     *
     * @param behaviourRecordHandleDTO
     * @return
     */
    @Override
    public Boolean handleActivity(BehaviourRecordHandleDTO behaviourRecordHandleDTO) {
        if (CollUtil.isNotEmpty(behaviourRecordHandleDTO.getAddBehaviourRecordIds())) {
            evaluateExecutor.execute(
                    () -> activityRuleMatchService.matchProcess(behaviourRecordHandleDTO.getAddBehaviourRecordIds()));
        }
        if (CollUtil.isNotEmpty(behaviourRecordHandleDTO.getDeletedBehaviourRecordIds())) {
            evaluateExecutor.execute(() -> activityRuleMatchService.matchProcess(
                    behaviourRecordHandleDTO.getDeletedBehaviourRecordIds()));
        }
        return Boolean.TRUE;
    }

    @Override
    public Info saveTeacherValuateInfo(InfoSaveDTO dto, Date operateTime, Boolean isSpeedEvaluate,
                                       Boolean isSportTarget) {
        BehaviourRecordHandleDTO behaviourRecordHandleDTO = new BehaviourRecordHandleDTO();
        Info info = saveEvaluateInfo(dto, behaviourRecordHandleDTO, operateTime, isSpeedEvaluate, isSportTarget);
        this.handleActivity(behaviourRecordHandleDTO);
        //图文点评，体测指标，则同步数据至学生体测数据
        if (Boolean.TRUE.equals(isSportTarget)) {
            asyncUpdateStudentSportData(behaviourRecordHandleDTO.getAddBehaviourRecords());
        }
        return info;
    }

    /**
     * 保存评估信息
     *
     * @param dto
     * @param behaviourRecordHandleDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class, timeout = 600)
    public Info saveEvaluateInfo(InfoSaveDTO dto, BehaviourRecordHandleDTO behaviourRecordHandleDTO, Date operateTime,
                                 Boolean isSpeedEvaluate, Boolean isSportTarget) {
        PlanListsRequest planTagListsRequest = new PlanListsRequest();
        planTagListsRequest.setStaffId(WebUtil.getStaffIdLong());
        planTagListsRequest.setSaasClassIds(Collections.singletonList(Convert.toLong(dto.getClassId())));
        planTagListsRequest.setSaasSchoolId(WebUtil.getSchoolIdLong());
        List<PlanListsResponse> planListsResponses = internalDriveRemote.queryPlanLists(planTagListsRequest);
        if (CharSequenceUtil.isNotBlank(dto.getPlanId())) {
            List<PlanListsResponse> filterPlanList = planListsResponses.stream()
                    .filter(item -> Objects.equals(Convert.toStr(item.getPlanId()), dto.getPlanId()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(filterPlanList)) {
                throw new BizException("当前积分板不存在，请重新选择积分板");
            }
        } else {
            Optional<PlanListsResponse> first = planListsResponses.stream().findFirst();
            first.ifPresent(planListsResponse -> {
                dto.setPlanId(Convert.toStr(planListsResponse.getPlanId()));
                dto.setPlanType(planListsResponse.getPlanType());
            });
        }

        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();

        // 防止重复提交，如果taskId不为空 缓存 并校验
        if (ObjectUtil.isNotEmpty(dto.getTaskId())) {
            checkTaskId(dto.getTaskId());
        }
        // 参数判断
        Assert.isTrue(dto.getTargetId() != null,
                () -> new BizException(BizExceptionEnum.TARGET_ID_NOT_NULL.getMessage()));
        Assert.isTrue(StrUtil.isNotBlank(dto.getTaskName()),
                () -> new BizException(BizExceptionEnum.TASK_NAME_NOT_NULL.getMessage()));
        Assert.isTrue(dto.getSubmitStaffId() != null,
                () -> new BizException(BizExceptionEnum.SUBMITOR_NOT_NULL.getMessage()));
        Assert.isTrue(CollUtil.isNotEmpty(dto.getSubmitInfoList()),
                () -> new BizException(BizExceptionEnum.SUBMIT_NOT_NULL.getMessage()));
        Assert.isTrue(WebUtil.getCampusId().equals(targetLogic.getByIdWithOutDeleted(dto.getTargetId()).getCampusId()),
                () -> new BizException(BizExceptionEnum.CAMPUS_ID_ERROR.getMessage()));

        // 前端提交参数
        List<SubmitInfoSaveDTO> submitInfoList = dto.getSubmitInfoList();
        // 判断模版格式
        infoHelper.judgeTemplate(infoConvert.toTemplateInfoSaves(submitInfoList));
        log.info("[评估任务-教师端保存]-[step.1.1]-[参数校验]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        // 学生列表
        List<StudentInfo> totalStudentInfoList = new ArrayList<>();
        // 分数和学生的映射
        List<Map<String, List>> infoList = new ArrayList<>();
        // 填充分数
        infoHelper.fullFillDatas(totalStudentInfoList, infoList, dto.getTaskName(), submitInfoList);
        log.info("[评估任务-教师端保存]-[step.1.2]-[填充分数]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        Date now = DateUtil.date();
        if (isSpeedEvaluate) {
            now = operateTime;
        }

        // 完成任务(老师)
        Long taskId = infoHelper.teacherFinishTask(dto, now);
        log.info("[评估任务-教师端保存]-[step.1.3]-[保存任务]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 图片视频存相对路径
        infoHelper.transferImgForSave(dto);
        log.info("[评估任务-教师端保存]-[step.1.4]-[处理图片或视频路径]--结束，消耗时长：[{}]",
                TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 保存表单
        Info evaluateInfoSave = infoConvert.toEntity(dto);
        evaluateInfoSave
                .setTenantId(WebUtil.getTenantId())
                .setSchoolId(WebUtil.getSchoolId())
                .setCampusId(WebUtil.getCampusId())
                .setTargetId(dto.getTargetId())
                .setTaskId(taskId)
                .setSubmitTime(now)
                .setStudentInfos(totalStudentInfoList);

        String evaluateInfoId = infoDao.save(evaluateInfoSave).getId();
        log.info("[评估任务-教师端保存]-[step.1.5]-[保存表单]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 评价人信息(老师)
        StaffBatchVO staffInfo = basicInfoService.getBasicInfoByStaffId(WebUtil.getStaffIdLong());
        log.info("[评估任务-教师端保存]-[step.1.6]-[获取评价人信息]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 保存统计数据
        // 填充统计数据
        List<BehaviourRecordSaveDTO> behaviourRecordSaves = infoHelper.generateRecordsV2(infoList, now, taskId,
                evaluateInfoId, dto.getTargetId());
        log.info("[评估任务-教师端保存]-[step.1.7]-[生成行为数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 填充评价人信息(老师)
        behaviourRecordSaves.forEach(s -> {
            if (Objects.nonNull(staffInfo)) {
                s.setAppraisalType(TaskRoleTypeEnum.TEACHER.getCode());
                s.setAppraisalId(WebUtil.getStaffId());
                s.setAppraisalName(staffInfo.getName());
            }
        });

        List<BehaviourRecord> records = convert.toBehaviourRecords(behaviourRecordSaves);
        if (CollUtil.isNotEmpty(records) && records.stream()
                .anyMatch(item -> Objects.isNull(item.getCampusSectionId()))) {
            log.error("组转图文点评数据获取学段id异常");
            throw new BizException("系统异常, 请稍后重试");
        }
        if (CollUtil.isNotEmpty(records)) {
            //老师点评关联学科
            this.fillSubjectCode(isSportTarget, records, dto.getSubjectRelList());

            records.forEach(item -> {
                item.setId(SnowFlakeIdUtil.nextId());
            });
            // 处理点评记录
            this.filterRecords(records);

            //校验学生提交频次
            List<String> restrictedStudentList = checkSubmissionFrequency(dto.getTargetId(), records,
                    Collections.emptyList());
            if (CollUtil.isNotEmpty(restrictedStudentList)) {
                StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
                studentByIdQuery.setStudentIds(restrictedStudentList
                        .stream()
                        .distinct().map(Convert::toLong).collect(Collectors.toList()));
                List<UcStudentClassBffVO> studentInfos = basicInfoRemote.listByStudentIds(studentByIdQuery);
                Assert.notEmpty(studentInfos, "学生不存在！");
                //抛出异常
                throwSubmissionException(studentInfos
                        .stream()
                        .map(UcStudentClassBffVO::getStudentName).distinct().collect(Collectors.toList()));
            }

            // 行为记录id对帮扶明细内容的映射
            Map<Long, String> idToHelpContentMap = new HashMap<>();
            // 帮扶数据
            List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS = new ArrayList<>();
            // 帮扶积分板类型
            if (Objects.equals(dto.getPlanType(), 3) && CharSequenceUtil.isNotBlank(dto.getPlanId()) && Objects.nonNull(
                    dto.getClassId())) {
                // 点评项和分数的映射
                Map<String, BigDecimal> optionToScoreMap = new HashMap<>();
                // 点评项和学生的映射
                Map<String, List<SubStudentInfo>> optionToStudentMap = new HashMap<>();
                // 明细点评项和学生的映射
                Map<String, List<SubStudentInfo>> optionDetailToStudentMap = new HashMap<>();
                try {
                    infoHelper.buildInfoMap(submitInfoList, optionToScoreMap, optionToStudentMap,
                            optionDetailToStudentMap);
                    log.info("处理点评帮扶数据，optionToScoreMap:{},optionToStudentMap:{},optionDetailToStudentMap:{}",
                            JSONUtil.toJsonStr(optionToScoreMap), JSONUtil.toJsonStr(optionToStudentMap),
                            JSONUtil.toJsonStr(optionDetailToStudentMap));
                    this.fillInternalDriver(dto, records, optionToScoreMap, optionToStudentMap,
                            optionDetailToStudentMap, idToHelpContentMap, helpBehaviourRecordPOS);
                } catch (Exception e) {
                    log.error("点评时处理点评关联帮扶数据出错，异常:", e);
                    throw new BizException("点评获取帮扶分数出错");
                }
            }
            if (CharSequenceUtil.isNotBlank(dto.getPlanId())) {
                List<BehaviourRecord> behaviourRecords1 = records.stream()
                        .filter(item -> Objects.nonNull(item.getScore())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(behaviourRecords1)) {
                    List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS = this.convertThirdToXdlRecordMessageDTO(
                            behaviourRecords1, Convert.toLong(dto.getPlanId()), null, idToHelpContentMap);
                    // 师徒帮扶
                    this.convertHelpThirdToXdlRecordMessageDTO(Convert.toLong(dto.getPlanId()),
                            thirdToXdlRecordMessageDTOS,
                            helpBehaviourRecordPOS
                    );
                    log.info("图文点评，同步星动力,thirdToXdlRecordMessageDTOS:{}",
                            JSONUtil.toJsonStr(thirdToXdlRecordMessageDTOS));
                    eventSendHelper.sendEventMQ(thirdToXdlRecordMessageDTOS, xwlAddRecord);
                }
            }
            log.info("保存图文点评，records:{}", JSONUtil.toJsonStr(records));

            // 帮扶数据走班行政班处理
            List<String> classIds = records.stream().map(BehaviourRecord::getClassId).distinct()
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(classIds)
                    && classIds.size() == 1 && !Objects.equals(classIds.get(0), dto.getClassId())
                    && CollUtil.isNotEmpty(helpBehaviourRecordPOS)
                    && Objects.equals(dto.getPlanType(), 3)
                    && CharSequenceUtil.isNotBlank(dto.getPlanId())) {
                helpBehaviourRecordPOS.forEach(item -> item.setClassId(classIds.get(0)));
            }
            // 兴趣班处理
            saasClassHelper.checkAndHandleClassForRecord(dto.getClassId(), records);
            behaviourRecordService.saveBatch(records);

            // 发送积分系统
            pointsSendLogic.convertAndSendPoints(records);

            if (CollUtil.isNotEmpty(helpBehaviourRecordPOS)) {
                // 兴趣班处理
                saasClassHelper.checkAndHandleClassForHelpRecord(dto.getClassId(), helpBehaviourRecordPOS);
                evaluateHelpBehaviourRecordManager.saveBatch(helpBehaviourRecordPOS);
            }
        }
        log.info("[评估任务-教师端保存]-[step.1.8]-[保存行为数据]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 保存提交操作日志
        TaskOperateLogSaveDTO saveDTO = new TaskOperateLogSaveDTO()
                .setTenantId(WebUtil.getTenantId())
                .setSchoolId(WebUtil.getSchoolId())
                .setCampusId(WebUtil.getCampusId())
                .setInfoId(evaluateInfoId)
                .setRoleType(TaskRoleTypeEnum.TEACHER.getCode())
                .setOperateType(OperateTypeEnum.SUBMIT.getCode())
                .setOperateTime(operateTime)
                .setIsBatchFlag(dto.getIsBatchFlag())
                .setOperatorId(WebUtil.getStaffId());

        taskOperateLogService.saveEvaluateTaskOperateLog(saveDTO);
        log.info("[评估任务-教师端保存]-[step.1.9]-[保存操作日志]--结束，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 新增的行为记录
        List<Long> behaviourIds = records.stream().filter(s -> Objects.nonNull(s.getId())).map(BehaviourRecord::getId)
                .distinct().collect(Collectors.toList());
        behaviourRecordHandleDTO.setAddBehaviourRecordIds(behaviourIds);

        // 发送消息到mq
        convertInitialService.sendMQ(1, 1, records, null);
        // 发送积分金币转换mq
        convertInitialService.sendBehaviourExchangeMq(
                this.assemblyPointExchangeMq(records, Convert.toLong(dto.getPlanId()),
                        CoinExchangeOperateTypeEnum.CREATE.getCode(),
                        CoinExchangeStrategyEnum.GRAPHIC_COMMENT_TEACHER.getCode()));

        //点评项增删改打标
        Set<String> sectionSet = new HashSet<>();
        //新增的年级
        if (CollUtil.isNotEmpty(records)) {
            addGradeRedis(records, sectionSet);
        }
        //新增和删除的学段
        if (CollUtil.isNotEmpty(sectionSet)) {
            addSectionRedis(sectionSet);
        }
        // 异步发送消息 班主任提醒消息
        Target target = targetLogic.getByIdWithOutDeleted(dto.getTargetId());
        String classId = dto.getClassId();
        evaluateReminderHandle.teacherNoticeSend(target, records, classId, staffInfo.getName(), "/evaluate-detail");

        behaviourRecordHandleDTO.setAddBehaviourRecords(records);
        return evaluateInfoSave;
    }

    /**
     * 组装积分金币转换mq
     *
     * @param records
     * @param planId
     * @param operateType
     * @return
     */
    public List<BehaviourPointExchangeDTO> assemblyPointExchangeMq(List<BehaviourRecord> records,
                                                                   Long planId,
                                                                   Integer operateType,
                                                                   Integer businessType) {
        return this.assemblyPointExchangeAllMq(records, planId, null, operateType, businessType);
    }

    /**
     * 组装积分金币转换mq(家长审核,点评人为审核老师)
     *
     * @param records
     * @param planId
     * @param operateType
     * @return
     */
    public List<BehaviourPointExchangeDTO> assemblyParentApprovePointExchangeMq(List<BehaviourRecord> records,
                                                                                Long planId,
                                                                                Integer operateType,
                                                                                Integer businessType,
                                                                                Long staffId) {
        List<BehaviourPointExchangeDTO> behaviourPointExchangeDTOS = this.assemblyPointExchangeAllMq(records, planId,
                null, operateType, businessType);
        behaviourPointExchangeDTOS.forEach(s -> s.setStaffId(staffId));
        return behaviourPointExchangeDTOS;
    }

    /**
     * 组装积分金币转换mq(需要subjectCode)
     *
     * @param records
     * @param planId
     * @param subjectCode
     * @param operateType
     * @return
     */
    public List<BehaviourPointExchangeDTO> assemblyPointExchangeAllMq(List<BehaviourRecord> records,
                                                                      Long planId,
                                                                      String subjectCode,
                                                                      Integer operateType,
                                                                      Integer businessType) {
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyList();
        }
        // 过滤出未开启加分的点评&&分值是否不加入画像综合得分和点评记录中的点评
        records = records.stream()
                // 需要开启积分的数据
                .filter(item -> Boolean.TRUE.equals(item.getIsScore()))
                // 需要加入学生画像的数据
                .filter(item -> !Objects.equals(item.getNotPartCount(), 1)).collect(Collectors.toList());
        if (CollUtil.isEmpty(records)) {
            log.info(
                    "【金币转积分】-【过滤出未开启加分的点评&&分值是否不加入画像综合得分和点评记录中的点评】-无转换积分直接返回,入参:{}",
                    JSONUtil.toJsonStr(records));
            return Collections.emptyList();
        }

        // 行为记录可能涉及多个班级,需要根据班级分组
        Map<String, List<BehaviourRecord>> classGroup = records.stream()
                .collect(Collectors.groupingBy(BehaviourRecord::getClassId));

        List<BehaviourPointExchangeDTO> result = new ArrayList<>();
        for (String classId : classGroup.keySet()) {
            if (CollUtil.isEmpty(classGroup.get(classId))) {
                continue;
            }
            List<BehaviourRecord> behaviourRecords = classGroup.get(classId);
            BehaviourRecord behaviourRecord = CollUtil.getFirst(behaviourRecords);
            BehaviourPointExchangeDTO behaviourPointExchangeDTO = new BehaviourPointExchangeDTO();
            behaviourPointExchangeDTO.setSchoolId(Convert.toLong(behaviourRecord.getSchoolId()));
            behaviourPointExchangeDTO.setClassId(Convert.toLong(behaviourRecord.getClassId()));
            behaviourPointExchangeDTO.setPlanId(Objects.nonNull(planId) ? planId : behaviourRecord.getPlanId());
            behaviourPointExchangeDTO.setSubjectCode(subjectCode);
            behaviourPointExchangeDTO.setStaffId(Objects.nonNull(WebUtil.getStaffIdLong()) ? WebUtil.getStaffIdLong()
                    : Convert.toLong(behaviourRecord.getAppraisalId()));
            behaviourPointExchangeDTO.setBusinessSource(businessType);
            behaviourPointExchangeDTO.setOperateType(operateType);
            List<BehaviourPointExchangeDetailDTO> exchangeDetails = behaviourRecords.stream().map(s -> {
                BehaviourPointExchangeDetailDTO behaviourPointExchangeDetailDTO = new BehaviourPointExchangeDetailDTO();
                behaviourPointExchangeDetailDTO.setBehaviourId(s.getId());
                behaviourPointExchangeDetailDTO.setStudentId(Convert.toLong(s.getStudentId()));
                behaviourPointExchangeDetailDTO.setChangePoint(s.getScore());
                behaviourPointExchangeDetailDTO.setTargetId(s.getTargetId());
                behaviourPointExchangeDetailDTO.setTargetName(s.getTargetName());
                behaviourPointExchangeDetailDTO.setInfoName(s.getInfoName());
                behaviourPointExchangeDetailDTO.setDataSource(s.getDataSource());
                behaviourPointExchangeDetailDTO.setInfoType(s.getInfoType());
                if (Objects.nonNull(s.getOldBehaviourId())) {
                    behaviourPointExchangeDetailDTO.setOldBehaviourId(s.getOldBehaviourId());
                }
                return behaviourPointExchangeDetailDTO;
            }).collect(Collectors.toList());
            behaviourPointExchangeDTO.setDetailList(exchangeDetails);
            result.add(behaviourPointExchangeDTO);
        }
        log.info("【金币转积分MQ组装返回】-【behaviourPointExchangeDTOS:{}】", JSONUtil.toJsonStr(result));
        return result;
    }

    /**
     * 异步更新学生体育数据
     */
    private void asyncUpdateStudentSportData(List<BehaviourRecord> behaviourRecords) {
        if (CollUtil.isEmpty(behaviourRecords)) {
            return;
        }
        log.info("【图文点评】-【体测指标】-【behaviourRecord:{}】", JSONUtil.toJsonStr(behaviourRecords));

        for (BehaviourRecord behaviourRecord : behaviourRecords) {
            //发送mq
            log.info("【图文点评】-【体测指标】-【要发送的数据:{}】", JSONUtil.toJsonStr(behaviourRecord));
            rocketMQTemplate.asyncSendOrderly(sportTargetTopic, Convert.toStr(behaviourRecord.getId()),
                    behaviourRecord.getStudentId(), new SendCallback() {
                        @Override
                        public void onSuccess(SendResult sendResult) {
                            log.info("【图文点评】-【体测指标】-【mq消息发送成功:{}】", JSONUtil.toJsonStr(behaviourRecord));
                        }

                        @Override
                        public void onException(Throwable throwable) {
                            log.error("【图文点评】-【体测指标】-【mq消息发送异常:{},异常信息:{}】",
                                    JSONUtil.toJsonStr(behaviourRecord),
                                    throwable.getMessage(), throwable);
                        }
                    });
        }
    }

    private List<String> checkSubmissionFrequency(Long targetId,
                                                  List<BehaviourRecord> behaviourRecordSaves,
                                                  List<Long> needDeleteBehaviourRecordIds) {
        List<String> restrictedStudentList = new ArrayList<>();
        if (CollUtil.isEmpty(behaviourRecordSaves)) {
            return restrictedStudentList;
        }
        //提交频次校验
        Target target = targetLogic.getByIdWithOutDeleted(targetId);
        if (!SubmitRateEnum.TERM.getSubmitType().equals(target.getSubmitType())) {
            return restrictedStudentList;
        }
        //获取学段和当前学期的映射
        Map<String, TermVo> campusSectionTermMap = this.listCurrentTermMap(behaviourRecordSaves);
        //图文点评可以跨学段选学生
        Map<String, List<BehaviourRecord>> campusSectionSaveMap = behaviourRecordSaves
                .stream()
                .collect(Collectors.groupingBy(BehaviourRecord::getCampusSectionId));

        for (Entry<String, List<BehaviourRecord>> entry : campusSectionSaveMap.entrySet()) {
            String campusSectionId = entry.getKey();
            List<BehaviourRecord> neeedSaveStudentRecordList = entry.getValue();
            //获取学段下的当前学期信息
            BehaviourRecord behaviourRecordSaveDTO = CollUtil.getFirst(neeedSaveStudentRecordList);
            TermVo currentTermVo = campusSectionTermMap.get(behaviourRecordSaveDTO.getCampusSectionId());
            //获取学期时间内的点评记录
            List<BehaviourRecordDTO> existStudentRecords = dorisBehaviourRecordManager.getRecord(
                    WebUtil.getSchoolId(),
                    WebUtil.getCampusId(),
                    null,
                    null,
                    null,
                    DateUtil.beginOfDay(DateUtil.parseDate(currentTermVo.getStartTime())),
                    DateUtil.endOfDay(DateUtil.parseDate(currentTermVo.getEndTime())),
                    null,
                    targetId,
                    neeedSaveStudentRecordList
                            .stream()
                            .map(BehaviourRecord::getStudentId).distinct().collect(Collectors.toList()));

            if (CollUtil.isEmpty(existStudentRecords)) {
                continue;
            }
            //如果是编辑指标的场景，需要过滤掉已经存在的点评记录
            existStudentRecords = existStudentRecords
                    .stream()
                    .filter(item -> !needDeleteBehaviourRecordIds.contains(item.getId()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(existStudentRecords)) {
                continue;
            }

            //学生维度分组
            Map<String, List<BehaviourRecordDTO>> studentRecordMap = existStudentRecords
                    .stream()
                    .collect(Collectors.groupingBy(BehaviourRecordDTO::getStudentId));

            for (BehaviourRecord needSaveStudentRecord : neeedSaveStudentRecordList) {
                List<BehaviourRecordDTO> subExistStudentRecords = studentRecordMap.get(
                        needSaveStudentRecord.getStudentId());
                if (CollUtil.isEmpty(subExistStudentRecords)) {
                    continue;
                }
                //学生班级下只能存在一条数据
                subExistStudentRecords = subExistStudentRecords
                        .stream()
                        .filter(item -> item.getClassId().equals(needSaveStudentRecord.getClassId()))
                        .collect(Collectors.toList());
                //已存在记录
                if (CollUtil.isNotEmpty(subExistStudentRecords)) {
                    restrictedStudentList.add(needSaveStudentRecord.getStudentId());
                }
            }
        }
        return restrictedStudentList;
    }

    private void throwSubmissionException(List<String> studentNameList) {
        String message;
        if (studentNameList.size() > 3) {
            message = String.format("本学期已经提交过%s等人记录，无需再提交",
                    String.join("、", studentNameList.subList(0, 3)));
        } else {
            message = String.format("本学期已经提交过%s的记录，无需再提交", String.join("、", studentNameList));
        }
        throw new BizException(message);
    }

    private Map<String, TermVo> listCurrentTermMap(List<BehaviourRecord> behaviourRecordSaves) {

        List<String> campusSectionIdList = behaviourRecordSaves
                .stream()
                .map(BehaviourRecord::getCampusSectionId).distinct().collect(Collectors.toList());

        Map<String, TermVo> campusSectionTermMap = Maps.newHashMap();

        for (String campusSectionId : campusSectionIdList) {
            TermQuery termQuery = new TermQuery();
            termQuery.setCampusSectionId(campusSectionId);
            termQuery.setCampusId(WebUtil.getCampusIdLong());
            termQuery.setSchoolId(WebUtil.getSchoolIdLong());
            List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
            Assert.notEmpty(termVos, "海思谷学期信息为空！");
            TermVo termVo = termVos
                    .stream()
                    .filter(TermVo::isCurrentTerm)
                    .findFirst().orElse(null);
            Assert.notNull(termVo, "当前学期为空！");

            campusSectionTermMap.put(campusSectionId, termVo);
        }
        return campusSectionTermMap;
    }

    /**
     * 处理点评记录
     *
     * @param records
     */
    private void filterRecords(List<BehaviourRecord> records) {
        // 根据学生获取行政班级
        List<String> studentIds = records.stream().map(BehaviourRecord::getStudentId).distinct()
                .collect(Collectors.toList());
        List<EduStudentClassVO> eduStudentClassVOList = listEduStudentClassVOS(studentIds);
        if (CollUtil.isNotEmpty(eduStudentClassVOList)) {
            //班级
            List<EduClassBaseInfoPojo> eduClassList = eduStudentClassVOList.stream()
                    .map(EduStudentClassVO::getClassBaseInfos).collect(Collectors.toList()).stream()
                    .flatMap(Collection::stream).collect(Collectors.toList());
            //行政班 id
            List<Long> classIds = eduClassList.stream()
                    .filter(item -> SaasClassTypeEnum.XINGZHENG.getCode().equals(item.getClassType()))
                    .map(EduClassBaseInfoPojo::getId).collect(Collectors.toList());
            //过滤既在行政班又在走班的走班数据，并将只在走班的班级 id 转为行政班班级 id
            fillRecords(records, classIds, eduStudentClassVOList);
        }
    }

    /**
     * 填充星动力师徒帮扶明细数据
     *
     * @param records
     */
    private void fillInternalDriver(InfoSaveDTO dto,
                                    List<BehaviourRecord> records,
                                    Map<String, BigDecimal> optionToScoreMap,
                                    Map<String, List<SubStudentInfo>> optionToStudentsMap,
                                    Map<String, List<SubStudentInfo>> optionDetailToStudentsMap,
                                    Map<Long, String> idToHelpContentMap,
                                    List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS) {
        if (CollUtil.isEmpty(records) || CollUtil.isEmpty(optionToScoreMap) || (CollUtil.isEmpty(optionToStudentsMap)
                && CollUtil.isEmpty(optionDetailToStudentsMap))) {
            return;
        }
        // 获取帮扶分数
        List<InternalDrivePointRecordResponse> internalDrivePointRecordResponses = this.requestInternalDriver(
                dto,
                records,
                optionToScoreMap,
                optionToStudentsMap,
                optionDetailToStudentsMap
        );

        // 写入帮扶学生点评数据
        Map<Long, List<InternalDrivePointRecordResponse>> studentMap = internalDrivePointRecordResponses.stream()
                .collect(Collectors.groupingBy(InternalDrivePointRecordResponse::getStudentId));
        studentMap.forEach((k, v) -> {
            for (InternalDrivePointRecordResponse internalDrivePointRecordResponse : v) {
                if (Objects.equals(internalDrivePointRecordResponse.getScene(), "personal")) {
                    continue;
                }
                List<BehaviourRecord> behaviourRecords = records.stream().filter(item -> {
                    if (CharSequenceUtil.isBlank(item.getOptionId())) {
                        return Objects.equals(internalDrivePointRecordResponse.getPlanTagId(), item.getTargetId());
                    }
                    return Objects.equals(internalDrivePointRecordResponse.getPlanCommentId(), item.getOptionId());
                }).collect(Collectors.toList());
                if (CollUtil.isEmpty(behaviourRecords)) {
                    continue;
                }
                BehaviourRecord internalDriveRecord = behaviourRecords.get(0);
                EvaluateHelpBehaviourRecordPO evaluateHelpBehaviourRecordPO = this.buildEvaluateHelpBehaviourRecordPO(
                        internalDriveRecord, internalDrivePointRecordResponse);
                helpBehaviourRecordPOS.add(evaluateHelpBehaviourRecordPO);
            }
        });
    }

    /**
     * 获取学生帮扶数据
     *
     * @param dto
     * @param records
     * @param optionToScoreMap
     * @param optionToStudentsMap
     * @param optionDetailToStudentsMap
     * @return
     */
    private List<InternalDrivePointRecordResponse> requestInternalDriver(InfoSaveDTO dto,
                                                                         List<BehaviourRecord> records,
                                                                         Map<String, BigDecimal> optionToScoreMap,
                                                                         Map<String, List<SubStudentInfo>> optionToStudentsMap,
                                                                         Map<String, List<SubStudentInfo>> optionDetailToStudentsMap) {
        String subjectCode = records.get(0).getSubjectCode();
        Integer moduleCode = records.get(0).getModuleCode();
        ComputeRecordRequest computeRecordRequest = new ComputeRecordRequest();
        computeRecordRequest.setSubjectCode(StringUtils.isBlank(subjectCode) ? "0" : subjectCode);
        computeRecordRequest.setPlanId(Convert.toLong(dto.getPlanId()));
        computeRecordRequest.setSaasClassId(dto.getClassId());
        computeRecordRequest.setSaasCampusId(WebUtil.getCampusId());
        computeRecordRequest.setSaasSchoolId(WebUtil.getSchoolId());
        List<ComputeRecordCommentRequest> commentRequestList = new ArrayList<>();
        if (CollUtil.isNotEmpty(optionToStudentsMap)) {
            optionToStudentsMap.forEach((k, v) -> {
                ComputeRecordCommentRequest computeRecordCommentRequest = new ComputeRecordCommentRequest();
                String[] split = k.split("-");
                if (split.length >= 2) {
                    computeRecordCommentRequest.setCommentId(split[0]);
                    computeRecordCommentRequest.setCommentName(split[1]);
                    computeRecordCommentRequest.setTagId(dto.getTargetId());
                    computeRecordCommentRequest.setModuleCode(moduleCode);
                    BigDecimal score = optionToScoreMap.get(k);
                    if (Objects.nonNull(score)) {
                        computeRecordCommentRequest.setScore(score);

                        List<ComputeRecordStudentRequest> studentRequests = new ArrayList<>();
                        v.forEach(item -> {
                            ComputeRecordStudentRequest computeRecordStudentRequest = new ComputeRecordStudentRequest();
                            computeRecordStudentRequest.setStudentId(Convert.toLong(item.getId()));
                            studentRequests.add(computeRecordStudentRequest);
                        });
                        computeRecordCommentRequest.setStudentList(studentRequests);
                        if (score.compareTo(new BigDecimal(0L)) != 0 && CollUtil.isNotEmpty(
                                computeRecordCommentRequest.getStudentList())) {
                            commentRequestList.add(computeRecordCommentRequest);
                        }
                    }
                }
            });
        }
        if (CollUtil.isNotEmpty(optionDetailToStudentsMap)) {
            optionDetailToStudentsMap.forEach((k, v) -> {
                ComputeRecordCommentRequest computeRecordCommentRequest = new ComputeRecordCommentRequest();
                String[] split = k.split("-");
                if (split.length >= 2) {
                    computeRecordCommentRequest.setCommentId(split[0]);
                    computeRecordCommentRequest.setTagId(dto.getTargetId());
                    computeRecordCommentRequest.setCommentName(split[1]);
                    computeRecordCommentRequest.setModuleCode(moduleCode);
                    BigDecimal score = optionToScoreMap.get(k);
                    if (Objects.nonNull(score)) {
                        computeRecordCommentRequest.setScore(score);

                        List<ComputeRecordStudentRequest> studentRequests = new ArrayList<>();
                        v.forEach(item -> {
                            ComputeRecordStudentRequest computeRecordStudentRequest = new ComputeRecordStudentRequest();
                            computeRecordStudentRequest.setStudentId(Convert.toLong(item.getId()));
                            studentRequests.add(computeRecordStudentRequest);
                        });
                        computeRecordCommentRequest.setStudentList(studentRequests);
                        if (score.compareTo(new BigDecimal(0L)) != 0 && CollUtil.isNotEmpty(
                                computeRecordCommentRequest.getStudentList())) {
                            commentRequestList.add(computeRecordCommentRequest);
                        }
                    }
                }
            });
        }
        if (CollUtil.isNotEmpty(commentRequestList)) {
            computeRecordRequest.setCommentList(commentRequestList);
            return internalDriveRemote.queryComputeRecord(computeRecordRequest);
        }

        return Collections.emptyList();
    }

    private EvaluateHelpBehaviourRecordPO buildEvaluateHelpBehaviourRecordPO(BehaviourRecord internalDriveRecord,
                                                                             InternalDrivePointRecordResponse internalDrivePointRecordResponse) {
        EvaluateHelpBehaviourRecordPO evaluateHelpBehaviourRecordPO = new EvaluateHelpBehaviourRecordPO();
        evaluateHelpBehaviourRecordPO.setId(SnowFlakeIdUtil.nextId());
        BigDecimal score = BigDecimal.valueOf(internalDrivePointRecordResponse.getScore());
        evaluateHelpBehaviourRecordPO.setScore(score);
        evaluateHelpBehaviourRecordPO.setScoreValue(score.abs());
        evaluateHelpBehaviourRecordPO.setScoreType(internalDriveRecord.getScoreType());
        evaluateHelpBehaviourRecordPO.setTenantId(internalDriveRecord.getTenantId());
        evaluateHelpBehaviourRecordPO.setSchoolId(internalDriveRecord.getSchoolId());
        evaluateHelpBehaviourRecordPO.setCampusId(internalDriveRecord.getCampusId());
        evaluateHelpBehaviourRecordPO.setCampusSectionId(internalDriveRecord.getCampusSectionId());
        evaluateHelpBehaviourRecordPO.setCampusSectionCode(internalDriveRecord.getCampusSectionCode());
        evaluateHelpBehaviourRecordPO.setGradeId(internalDriveRecord.getGradeId());
        evaluateHelpBehaviourRecordPO.setGradeCode(internalDriveRecord.getGradeCode());
        evaluateHelpBehaviourRecordPO.setClassId(internalDriveRecord.getClassId());
        evaluateHelpBehaviourRecordPO.setStudentId(Convert.toStr(internalDrivePointRecordResponse.getStudentId()));
        evaluateHelpBehaviourRecordPO.setStudentName(internalDrivePointRecordResponse.getStudentName());
        evaluateHelpBehaviourRecordPO.setModuleCode(internalDriveRecord.getModuleCode());
        evaluateHelpBehaviourRecordPO.setTargetId(internalDriveRecord.getTargetId());
        evaluateHelpBehaviourRecordPO.setTargetName(internalDriveRecord.getTargetName());
        evaluateHelpBehaviourRecordPO.setNotPartCount(internalDriveRecord.getNotPartCount());
        evaluateHelpBehaviourRecordPO.setClassifyId(-1L);
        evaluateHelpBehaviourRecordPO.setClassifyName("其他");
        evaluateHelpBehaviourRecordPO.setOptionId(internalDriveRecord.getOptionId());
        evaluateHelpBehaviourRecordPO.setOptionName(internalDriveRecord.getInfoName());
        evaluateHelpBehaviourRecordPO.setHelpDesc(internalDrivePointRecordResponse.getContent());
        evaluateHelpBehaviourRecordPO.setDataType(internalDriveRecord.getInfoType());
        evaluateHelpBehaviourRecordPO.setDataChannel(1);
        evaluateHelpBehaviourRecordPO.setSubmitTime(internalDriveRecord.getSubmitTime());
        evaluateHelpBehaviourRecordPO.setAppraisalId(internalDriveRecord.getAppraisalId());
        evaluateHelpBehaviourRecordPO.setAppraisalName(internalDriveRecord.getAppraisalName());
        return evaluateHelpBehaviourRecordPO;
    }

    private void convertHelpThirdToXdlRecordMessageDTO(Long planId,
                                                       List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS,
                                                       List<EvaluateHelpBehaviourRecordPO> evaluateHelpBehaviourRecordPOS) {

        if (CollUtil.isEmpty(evaluateHelpBehaviourRecordPOS)) {
            return;
        }
        for (EvaluateHelpBehaviourRecordPO evaluateHelpBehaviourRecordPO : evaluateHelpBehaviourRecordPOS) {
            ThirdToXdlRecordMessageDTO thirdToXdlRecordMessageDTO = new ThirdToXdlRecordMessageDTO();
            thirdToXdlRecordMessageDTO.setPlanId(planId);
            thirdToXdlRecordMessageDTO.setScore(evaluateHelpBehaviourRecordPO.getScore());
            thirdToXdlRecordMessageDTO.setScoreType(evaluateHelpBehaviourRecordPO.getScoreType());
            thirdToXdlRecordMessageDTO.setGradeId(evaluateHelpBehaviourRecordPO.getGradeId());
            thirdToXdlRecordMessageDTO.setGradeCode(evaluateHelpBehaviourRecordPO.getGradeCode());
            thirdToXdlRecordMessageDTO.setTenantId(evaluateHelpBehaviourRecordPO.getTenantId());
            thirdToXdlRecordMessageDTO.setSchoolId(evaluateHelpBehaviourRecordPO.getSchoolId());
            thirdToXdlRecordMessageDTO.setCampusId(evaluateHelpBehaviourRecordPO.getCampusId());
            thirdToXdlRecordMessageDTO.setCampusSectionId(evaluateHelpBehaviourRecordPO.getCampusSectionId());
            thirdToXdlRecordMessageDTO.setCampusSectionCode(evaluateHelpBehaviourRecordPO.getCampusSectionCode());
            thirdToXdlRecordMessageDTO.setClassId(evaluateHelpBehaviourRecordPO.getClassId());
            thirdToXdlRecordMessageDTO.setSubjectCode("0");
            thirdToXdlRecordMessageDTO.setThirdRecordId(evaluateHelpBehaviourRecordPO.getId().toString());
            thirdToXdlRecordMessageDTO.setStudentId(evaluateHelpBehaviourRecordPO.getStudentId());
            thirdToXdlRecordMessageDTO.setModuleCode(evaluateHelpBehaviourRecordPO.getModuleCode());
            thirdToXdlRecordMessageDTO.setThirdTargetType(1);
            Integer thirdOperationType =
                    Objects.equals(SPEED_OPTION_SCHOOL_SCHOOL.getCode(), evaluateHelpBehaviourRecordPO.getDataType()) ?
                            1 : 2;
            thirdToXdlRecordMessageDTO.setThirdTargetName(evaluateHelpBehaviourRecordPO.getClassifyName());
            thirdToXdlRecordMessageDTO.setThirdTargetId(Convert.toStr(evaluateHelpBehaviourRecordPO.getClassifyId()));
            thirdToXdlRecordMessageDTO.setThirdOperationType(thirdOperationType);
            thirdToXdlRecordMessageDTO.setThirdOptionId(evaluateHelpBehaviourRecordPO.getOptionId());
            thirdToXdlRecordMessageDTO.setThirdOptionName(evaluateHelpBehaviourRecordPO.getOptionName());
            thirdToXdlRecordMessageDTO.setThirdDataSource(2);
            thirdToXdlRecordMessageDTO.setCreateBy(WebUtil.getStaffId());
            thirdToXdlRecordMessageDTO.setCreateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            thirdToXdlRecordMessageDTO.setCreateByName(evaluateHelpBehaviourRecordPO.getAppraisalName());
            thirdToXdlRecordMessageDTO.setScene("help");
            thirdToXdlRecordMessageDTO.setContent(evaluateHelpBehaviourRecordPO.getHelpDesc());
            thirdToXdlRecordMessageDTOS.add(thirdToXdlRecordMessageDTO);
        }
    }

    /**
     * 构建发送星动力事件消息体
     *
     * @param behaviourRecords
     * @param planId
     * @return
     */
    private List<ThirdToXdlRecordMessageDTO> convertThirdToXdlRecordMessageDTO(List<BehaviourRecord> behaviourRecords,
                                                                               Long planId,
                                                                               Long oldBehaviourRecordId,
                                                                               Map<Long, String> idToHelpContentMap) {
        List<ThirdToXdlRecordMessageDTO> records = new ArrayList<>();
        for (BehaviourRecord behaviourRecord : behaviourRecords) {
            ThirdToXdlRecordMessageDTO thirdToXdlRecordMessageDTO = new ThirdToXdlRecordMessageDTO();
            thirdToXdlRecordMessageDTO.setPlanId(planId);
            thirdToXdlRecordMessageDTO.setScore(behaviourRecord.getScore());
            thirdToXdlRecordMessageDTO.setScoreType(behaviourRecord.getScoreType());
            thirdToXdlRecordMessageDTO.setGradeId(behaviourRecord.getGradeId());
            thirdToXdlRecordMessageDTO.setGradeCode(behaviourRecord.getGradeCode());
            thirdToXdlRecordMessageDTO.setTenantId(behaviourRecord.getTenantId());
            thirdToXdlRecordMessageDTO.setSchoolId(behaviourRecord.getSchoolId());
            thirdToXdlRecordMessageDTO.setCampusId(behaviourRecord.getCampusId());
            thirdToXdlRecordMessageDTO.setCampusSectionId(behaviourRecord.getCampusSectionId());
            thirdToXdlRecordMessageDTO.setCampusSectionCode(behaviourRecord.getCampusSectionCode());
            thirdToXdlRecordMessageDTO.setClassId(behaviourRecord.getClassId());
            if (CharSequenceUtil.isNotBlank(behaviourRecord.getSubjectCode())) {
                thirdToXdlRecordMessageDTO.setSubjectCode(behaviourRecord.getSubjectCode());
            } else {
                thirdToXdlRecordMessageDTO.setSubjectCode("0");
            }
            thirdToXdlRecordMessageDTO.setThirdRecordId(behaviourRecord.getId().toString());
            thirdToXdlRecordMessageDTO.setStudentId(behaviourRecord.getStudentId());
            thirdToXdlRecordMessageDTO.setModuleCode(behaviourRecord.getModuleCode());
            thirdToXdlRecordMessageDTO.setThirdTargetType(1);
            thirdToXdlRecordMessageDTO.setThirdTargetName("其他");
            thirdToXdlRecordMessageDTO.setThirdTargetId("-1");
            thirdToXdlRecordMessageDTO.setThirdOperationType(1);
            thirdToXdlRecordMessageDTO.setThirdOptionId(
                    CharSequenceUtil.isBlank(behaviourRecord.getOptionId()) ? Convert.toStr(
                            behaviourRecord.getTargetId()) : behaviourRecord.getOptionId());
            thirdToXdlRecordMessageDTO.setThirdOptionName(behaviourRecord.getInfoName());
            thirdToXdlRecordMessageDTO.setThirdDataSource(2);
            thirdToXdlRecordMessageDTO.setCreateBy(
                    CharSequenceUtil.isNotBlank(WebUtil.getStaffId())
                            ? WebUtil.getStaffId()
                            : WebUtil.getStudentIdStr());
            thirdToXdlRecordMessageDTO.setCreateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            thirdToXdlRecordMessageDTO.setCreateByName(behaviourRecord.getAppraisalName());
            thirdToXdlRecordMessageDTO.setOldBehaviourRecordId(oldBehaviourRecordId);
            thirdToXdlRecordMessageDTO.setScene("personal");
            thirdToXdlRecordMessageDTO.setContent(behaviourRecord.getInfoName());
            records.add(thirdToXdlRecordMessageDTO);
        }
        return records;
    }

    /**
     * 过滤既在行政班又在走班的走班数据，并将只在走班的班级 id 转为行政班班级 id
     *
     * @param records
     * @param classIds
     * @param eduStudentClassVOList
     */
    private static void fillRecords(List<BehaviourRecord> records, List<Long> classIds,
                                    List<EduStudentClassVO> eduStudentClassVOList) {
        //按学生 id 分组
        List<List<BehaviourRecord>> repeatBehaviourRecordListByStudentId = records.stream()
                .collect(Collectors.groupingBy(BehaviourRecord::getStudentId))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        //按学生 id 和班级 id 分组,筛选出有行政班和走班的行为记录
        List<List<BehaviourRecord>> repeatBehaviourRecordList = new ArrayList<>();
        for (List<BehaviourRecord> behaviourRecords : repeatBehaviourRecordListByStudentId) {
            int size = behaviourRecords.stream()
                    .collect(Collectors.groupingBy(p -> Pair.of(p.getStudentId(), p.getClassId())))
                    .size();
            if (Constant.ONE < size) {
                repeatBehaviourRecordList.addAll(behaviourRecords.stream()
                        .collect(Collectors.groupingBy(p -> Pair.of(p.getStudentId(), p.getClassId())))
                        .entrySet().stream()
                        .map(Map.Entry::getValue)
                        .collect(Collectors.toList()));
            }
        }

        ArrayList<SubStudentInfo> studentInfos = new ArrayList<>();
        repeatBehaviourRecordList.forEach(s -> {
            if (CollUtil.isNotEmpty(s)) {
                SubStudentInfo student = new SubStudentInfo();
                student.setId(s.get(0).getStudentId());
                student.setClassId(s.get(0).getClassId());
                studentInfos.add(student);
            }
        });
        //既在行政班又在走班的学生
        List<SubStudentInfo> repeatStudentInfos = studentInfos.stream()
                .collect(Collectors.groupingBy(SubStudentInfo::getId))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getValue)
                .collect(Collectors.toList()).stream().flatMap(Collection::stream).collect(Collectors.toList());
        //既在行政班又在走班的学生过滤出走班学生
        List<SubStudentInfo> zouBanStudents = repeatStudentInfos.stream()
                .filter(entry -> !classIds.contains(Convert.toLong(entry.getClassId()))).collect(Collectors.toList());

        List<BehaviourRecord> zouBanRecords = new ArrayList<>();
        //既在行政班又在走班的学生的走班行为数据
        zouBanStudents.forEach(s -> {
            zouBanRecords.addAll(records.stream()
                    .filter(item -> item.getStudentId().equals(s.getId()) && item.getClassId().equals(s.getClassId()))
                    .collect(Collectors.toList()));
        });
        //过滤既在行政班又在走班的学生的走班行为数据
        records.removeAll(zouBanRecords);

        //将走班 id 转成行政班 id
        records.forEach(item -> {
            Optional<EduStudentClassVO> first = eduStudentClassVOList.stream()
                    .filter(s -> item.getStudentId().equals(Convert.toStr(s.getStudentId()))).findFirst();
            first.ifPresent(eduStudentClassVO -> item.setClassId(
                    Convert.toStr(eduStudentClassVO.getClassBaseInfos().get(0).getId())));
        });
    }

    private List<EduStudentClassVO> listEduStudentClassVOS(List<String> studentIdList) {
        if (CollUtil.isEmpty(studentIdList)) {
            return Collections.emptyList();
        }
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        List<String> classTypes = new ArrayList<>();
        classTypes.add(SaasClassTypeEnum.XINGZHENG.getCode());
        eduStudentClassQueryDTO.setClassTypes(classTypes);
        eduStudentClassQueryDTO.setStudentIds(Convert.toList(Long.class, studentIdList));
        eduStudentClassQueryDTO.setGraduationStatus("0");
        List<EduStudentClassVO> eduStudentClassVOList = cacheSaasManager.queryClassListByStudentIds(
                eduStudentClassQueryDTO);
        return eduStudentClassVOList;
    }

    private List<EduStudentClassVO> listEduStudentClassVOSIncludeGraduated(List<String> studentIdList) {
        if (CollUtil.isEmpty(studentIdList)) {
            return Collections.emptyList();
        }
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        List<String> classTypes = new ArrayList<>();
        classTypes.add(SaasClassTypeEnum.XINGZHENG.getCode());
        eduStudentClassQueryDTO.setClassTypes(classTypes);
        eduStudentClassQueryDTO.setStudentIds(Convert.toList(Long.class, studentIdList));
        List<EduStudentClassVO> eduStudentClassVOList = basicInfoRemote.queryClassListByStudentIdsIncludeGraduated(
                eduStudentClassQueryDTO);
        return eduStudentClassVOList;
    }

    /**
     * 老师点评关联学科
     */
    private void fillSubjectCode(Boolean isSportTarget, List<BehaviourRecord> records,
                                 List<SubjectRelDTO> subjectRelList) {

        long startTime = System.currentTimeMillis();
        Map<String, SubjectRelDTO> subjectRelMap = Optional.ofNullable(subjectRelList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(SubjectRelDTO::getClassId, Function.identity()));

        List<String> classIds = records
                .stream()
                .filter(record -> TaskRoleTypeEnum.TEACHER.getCode().equals(record.getAppraisalType()))
                .map(BehaviourRecord::getClassId)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(classIds)) {
            return;
        }

        // 如果涉及多个班级学生则不挂学科（混班点评不挂学科）
        if (classIds.size() > 1) {
            log.info("【图文点评】-【混班点评不挂学科】-classIds:{}", JSONUtil.toJsonStr(classIds));
            return;
        }
        //默认：体测项目指标不挂学科
        if (Boolean.TRUE.equals(isSportTarget)) {
            log.info("【图文点评】-【默认体测指标不挂学科】-classIds:{}", JSONUtil.toJsonStr(classIds));
            return;
        }

        //获取关联学科
        QueryClassSubjectRelDTO query = new QueryClassSubjectRelDTO();
        query.setSubmitValue(classIds.stream().map(classId -> {
            SubStudentInfo subStudentInfo = new SubStudentInfo();
            subStudentInfo.setId(classId);
            subStudentInfo.setClassId(classId);
            subStudentInfo.setType(StudentInfoTypeEnum.CLASS.getCode());
            return subStudentInfo;
        }).collect(Collectors.toList()));

        List<EduStaffTeachClassVO> relatedSubject = this.getRelatedSubject(query, WebUtil.getSchoolId(),
                WebUtil.getStaffId());
        if (CollUtil.isEmpty(relatedSubject)) {
            for (BehaviourRecord record : records) {
                if (subjectRelMap.containsKey(record.getClassId())) {
                    String defaultSubjectCode = subjectRelMap.get(record.getClassId()).getDefaultSubjectCode();
                    record.setSubjectCode(CharSequenceUtil.isBlank(defaultSubjectCode) ? "0" : defaultSubjectCode);
                }
            }
            return;
        }

        Map<Long, EduStaffTeachClassVO> saasSubjectRelMap = relatedSubject.stream()
                .collect(Collectors.toMap(EduStaffTeachClassVO::getClassId, Function.identity()));

        for (BehaviourRecord record : records) {
            //只有老师点评才会记录
            if (!TaskRoleTypeEnum.TEACHER.getCode().equals(record.getAppraisalType())) {
                continue;
            }

            Long classId = Long.valueOf(record.getClassId());
            EduStaffTeachClassVO teachClassVO = saasSubjectRelMap.get(classId);
            if (ObjectUtil.isNull(teachClassVO)) {
                continue;
            }

            List<EduStaffSubjectVO> subjects = teachClassVO.getSubjects();
            if (CollUtil.isEmpty(subjects)) {
                continue;
            }
            EduStaffSubjectVO eduSubjectVO = subjects.stream().findFirst().orElse(null);
            if (ObjectUtil.isNull(eduSubjectVO)) {
                continue;
            }

            //对应班级只任教一门学科
            if (subjects.size() == 1) {
                record.setSubjectCode(eduSubjectVO.getSubjectCode()); // NOSONAR
            } else {
                //对应班级任教多门学科需要用户前端选择
                SubjectRelDTO subjectRelDTO = subjectRelMap.get(record.getClassId());
                if (ObjectUtil.isNull(subjectRelDTO) || (ObjectUtil.isNull(subjectRelDTO.getDefaultSubjectCode())
                        && ObjectUtil.isNull(subjectRelDTO.getSubjectCode()))) {
                    List<EduClassInfoLinkVO> classInfoVOS = Optional.ofNullable(
                                    cacheSaasManager.listUnderByClassIds(Lists.newArrayList(classId)))
                            .orElse(Collections.emptyList());
                    Map<Long, String> classMap = classInfoVOS.stream()
                            .collect(Collectors.toMap(EduClassInfoLinkVO::getId, EduClassInfoLinkVO::getClassName));
                    throw new BizException(String.format("%s未选择学科", classMap.getOrDefault(classId, "")));
                }
                List<String> needChooseTargets = subjectRelDTO.getNeedChooseTargets();
                if (CollUtil.isNotEmpty(needChooseTargets) && needChooseTargets.contains(
                        Convert.toStr(record.getTargetId()))) {
                    record.setSubjectCode(subjectRelDTO.getSubjectCode());
                }
                List<String> noNeedChooseTargets = subjectRelDTO.getNoNeedChooseTargets();
                if (CollUtil.isNotEmpty(noNeedChooseTargets) && noNeedChooseTargets.contains(
                        Convert.toStr(record.getTargetId()))) {
                    record.setSubjectCode(subjectRelDTO.getDefaultSubjectCode());
                }
            }
        }
        log.info("[评估任务-教师端保存]-[保存行为数据]-[回填学科信息]-结束，消耗时长：[{}]",
                System.currentTimeMillis() - startTime);
    }

    private void checkTaskId(Long taskId) {
        String key = "saveValuateInfo:" + taskId;
        Object object = redisUtil.get(key);
        if (ObjectUtil.isNotNull(object)) {
            throw new BizException("当前任务正在提交中，请勿重复提交");
        } else {
            redisUtil.set(key, "1", CacheConstants.ONE_MINUTE);
        }
        TaskPO taskPO = taskService.getById(taskId);
        if (ObjectUtil.isEmpty(taskPO)) {
            throw new BizException("当前任务不存在，请核对后重新提交");
        }
        if (ObjectUtil.equal(taskPO.getTaskStatus(), TaskStatusEnum.SUBMITTED.getCode())) {
            throw new BizException("当前任务已提交，请勿重复提交");
        }
    }

    @Override
    public Boolean modifyEvaluateInfo(InfoModifyDTO dto, Boolean isSportTarget) {
        BehaviourRecordHandleDTO behaviourRecordHandleDTO = new BehaviourRecordHandleDTO();
        Boolean flag = updateEvaluateInfo(dto, behaviourRecordHandleDTO, isSportTarget);
        this.handleActivity(behaviourRecordHandleDTO);
        //图文点评，体测指标，则同步数据至学生体测数据
        if (Boolean.TRUE.equals(isSportTarget)) {
            asyncUpdateStudentSportData(behaviourRecordHandleDTO.getAddBehaviourRecords());
        }
        return flag;
    }

    /**
     * 修改点评项
     *
     * @param dto
     * @param behaviourRecordHandleDTO
     */
    @Transactional(rollbackFor = Exception.class, timeout = 600)
    public Boolean updateEvaluateInfo(InfoModifyDTO dto, BehaviourRecordHandleDTO behaviourRecordHandleDTO,
                                      Boolean isSportTarget) {
        if (Objects.equals(dto.getSource(), 2) && CharSequenceUtil.isNotBlank(dto.getPlanId())) {
            PlanListsRequest planTagListsRequest = new PlanListsRequest();
            planTagListsRequest.setStaffId(WebUtil.getStaffIdLong());
            planTagListsRequest.setSaasClassIds(Collections.singletonList(Convert.toLong(dto.getClassId())));
            planTagListsRequest.setSaasSchoolId(WebUtil.getSchoolIdLong());
            List<PlanListsResponse> planListsResponses = internalDriveRemote.queryPlanLists(planTagListsRequest);
            List<PlanListsResponse> filterPlanList = planListsResponses.stream()
                    .filter(item -> Objects.equals(item.getPlanId(), item.getPlanId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(filterPlanList)) {
                throw new BizException("当前积分板不存在，请重新选择积分板");
            }
        }

        // 参数校验
        Assert.isTrue(StrUtil.isNotBlank(dto.getSubmitInfoId()),
                () -> new BizException(BizExceptionEnum.DETAIL_ID_NOT_NULL.getMessage()));
        Assert.isTrue(CollUtil.isNotEmpty(dto.getSubmitInfoList()),
                () -> new BizException(BizExceptionEnum.DETAIL_CONTENT_NOT_NULL.getMessage()));

        Info info = infoLogic.getById(dto.getSubmitInfoId());
        Assert.isTrue(info != null, () -> new BizException(BizExceptionEnum.DETAIL_NOT_NULL.getMessage()));
        Assert.isTrue(WebUtil.getCampusId().equals(info.getCampusId()),
                () -> new BizException(BizExceptionEnum.CAMPUS_ID_ERROR.getMessage()));

        // 修改任务更新时间
        TaskPO taskPOUpdate = new TaskPO();
        taskPOUpdate.setUpdateTime(DateUtil.date());
        taskPOUpdate.setId(info.getTaskId());
        taskLogic.updateById(taskPOUpdate);

        // 判断模版格式
        infoHelper.judgeTemplate(infoConvert.toTemplates(dto.getSubmitInfoList()));

        // 分数和学生的映射
        List<Map<String, List>> infoList = new ArrayList<>();
        // 所有学生列表
        List<StudentInfo> totalStudentInfoList = new ArrayList<>();
        // 填充分数
        infoHelper.fullFillDatas(totalStudentInfoList, infoList, info.getTaskName(),
                infoConvert.toTemplateInfoModifys(dto.getSubmitInfoList()));

        // 更新填写详情
        // 图片视频存相对路径
        infoHelper.transferImgForUpdate(dto.getSubmitInfoList());
        List<SubmitInfo> submitInfos = infoConvert.toEvaluateInfos(dto.getSubmitInfoList());
        Info update = new Info();
        update.setStudentInfos(totalStudentInfoList).setSubmitInfoList(submitInfos).setId(dto.getSubmitInfoId());
        infoDao.update(update);

        // 删除原来的行为记录
        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.listByInfoIdV3(info.getId());
        List<Long> needDeleteBehaviourRecordIds = behaviourRecords.stream().map(BehaviourRecord::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(behaviourRecords)) {
            behaviourRecordManager.removeBatchByIds(needDeleteBehaviourRecordIds);
        }
        if (CharSequenceUtil.isNotBlank(dto.getPlanId()) && Objects.equals(dto.getSource(), 2)) {
            List<BehaviourRecord> behaviourRecords1 = behaviourRecords.stream()
                    .filter(item -> Objects.nonNull(item.getScore())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(behaviourRecords1)) {
                List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS = this.convertThirdToXdlRecordMessageDTO(
                        behaviourRecords1, Convert.toLong(dto.getPlanId()), null, new HashMap<>());
                eventSendHelper.sendEventMQ(thirdToXdlRecordMessageDTOS, xwlDelRecord);
            }
        }

        // 评价人信息(老师)
        StaffBatchVO staffInfo = basicInfoService.getBasicInfoByStaffId(WebUtil.getStaffIdLong());

        // 新增新的行为记录
        TaskPO taskPO = taskLogic.getById(info.getTaskId());
        Assert.isTrue(taskPO != null, () -> new BizException(BizExceptionEnum.TASK_NOT_NULL.getMessage()));
        List<BehaviourRecordSaveDTO> behaviourRecordSaves = infoHelper.generateRecordsV2(infoList,
                taskPO.getSubmitTime(), info.getTaskId(), info.getId(), info.getTargetId());
        // 填充评价人信息(老师)
        behaviourRecordSaves.forEach(s -> {
            if (Objects.nonNull(staffInfo)) {
                s.setAppraisalType(TaskRoleTypeEnum.TEACHER.getCode());
                s.setAppraisalId(WebUtil.getStaffId());
                s.setAppraisalName(staffInfo.getName());
            }
        });

        List<BehaviourRecord> records = convert.toBehaviourRecords(behaviourRecordSaves);
        if (CollUtil.isNotEmpty(records) && records.stream()
                .anyMatch(item -> Objects.isNull(item.getCampusSectionId()))) {
            log.error("组转图文点评数据获取学段id异常");
            throw new BizException("系统异常。请稍后重试");
        }

        if (CollUtil.isNotEmpty(records)) {
            //老师点评关联学科
            this.fillSubjectCode(isSportTarget, records, dto.getSubjectRelList());

            records.forEach(item -> {
                item.setId(SnowFlakeIdUtil.nextId());
            });

            //处理点评记录
            this.filterRecords(records);
            //校验提交频次
            List<String> restrictedStudentList = checkSubmissionFrequency(dto.getTargetId(), records,
                    needDeleteBehaviourRecordIds);
            if (CollUtil.isNotEmpty(restrictedStudentList)) {
                StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
                studentByIdQuery.setStudentIds(restrictedStudentList
                        .stream()
                        .distinct().map(Convert::toLong).collect(Collectors.toList()));
                List<UcStudentClassBffVO> studentInfos = basicInfoRemote.listByStudentIds(studentByIdQuery);
                Assert.notEmpty(studentInfos, "学生不存在！");
                //抛出异常
                throwSubmissionException(studentInfos
                        .stream()
                        .map(UcStudentClassBffVO::getStudentName).distinct().collect(Collectors.toList()));
            }

            // 行为记录id对帮扶明细内容的映射
            Map<Long, String> idToHelpContentMap = new HashMap<>();
            List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS = new ArrayList<>();
            if (Objects.equals(dto.getSource(), 2)
                    && Objects.equals(dto.getPlanType(), 3)
                    && CharSequenceUtil.isNotBlank(dto.getPlanId())
                    && Objects.nonNull(dto.getClassId())) {
                List<SubmitInfoSaveDTO> submitInfoSaveDTOS = this.convertModifyDTOToSaveDTO(dto.getSubmitInfoList());
                InfoSaveDTO infoSaveDTO = this.convertInfoModifyDTOToSaveDTO(dto, submitInfoSaveDTOS);
                // 点评项和分数的映射
                Map<String, BigDecimal> optionToScoreMap = new HashMap<>();
                // 点评项和学生的映射
                Map<String, List<SubStudentInfo>> optionToStudentMap = new HashMap<>();
                // 明细点评项和学生的映射
                Map<String, List<SubStudentInfo>> optionDetailToStudentMap = new HashMap<>();
                try {
                    infoHelper.buildInfoMap(submitInfoSaveDTOS, optionToScoreMap, optionToStudentMap,
                            optionDetailToStudentMap);
                    log.info("处理点评帮扶数据，optionToScoreMap:{},optionToStudentMap:{},optionDetailToStudentMap:{}",
                            JSONUtil.toJsonStr(optionToScoreMap), JSONUtil.toJsonStr(optionToStudentMap),
                            JSONUtil.toJsonStr(optionDetailToStudentMap));
                    this.fillInternalDriver(infoSaveDTO, records, optionToScoreMap, optionToStudentMap,
                            optionDetailToStudentMap, idToHelpContentMap, helpBehaviourRecordPOS);
                } catch (Exception e) {
                    log.error("点评时处理点评关联帮扶数据出错，异常:", e);
                    throw new BizException("点评获取帮扶分数出错");
                }
            }

            if (CharSequenceUtil.isNotBlank(dto.getPlanId()) && Objects.equals(dto.getSource(), 2)) {
                List<BehaviourRecord> behaviourRecords1 = records.stream()
                        .filter(item -> Objects.nonNull(item.getScore())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(behaviourRecords1)) {
                    List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS = this.convertThirdToXdlRecordMessageDTO(
                            behaviourRecords1,
                            Convert.toLong(dto.getPlanId()),
                            behaviourRecords.get(0).getId(),
                            idToHelpContentMap);
                    // 师徒帮扶
                    this.convertHelpThirdToXdlRecordMessageDTO(Convert.toLong(dto.getPlanId()),
                            thirdToXdlRecordMessageDTOS,
                            helpBehaviourRecordPOS
                    );
                    log.info("图文点评，同步星动力,thirdToXdlRecordMessageDTOS:{}",
                            JSONUtil.toJsonStr(thirdToXdlRecordMessageDTOS));
                    eventSendHelper.sendEventMQ(thirdToXdlRecordMessageDTOS, xwlAddRecord);
                }
            }

            log.info("保存图文点评，records:{}", JSONUtil.toJsonStr(records));

            // 帮扶数据走班行政班处理
            List<String> classIds = records.stream().map(BehaviourRecord::getClassId).distinct()
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(classIds)
                    && classIds.size() == 1 && (!Objects.equals(classIds.get(0), dto.getClassId()))
                    && CollUtil.isNotEmpty(helpBehaviourRecordPOS)
                    && Objects.equals(dto.getSource(), 2)
                    && Objects.equals(dto.getPlanType(), 3)
                    && CharSequenceUtil.isNotBlank(dto.getPlanId())) {
                helpBehaviourRecordPOS.forEach(item -> item.setClassId(classIds.get(0)));
            }
            behaviourRecordService.saveBatch(records);
            if (CollUtil.isNotEmpty(helpBehaviourRecordPOS)) {
                evaluateHelpBehaviourRecordManager.saveBatch(helpBehaviourRecordPOS);
            }
        }

        // 新增修改操作日志
        TaskOperateLogSaveDTO saveDTO = new TaskOperateLogSaveDTO()
                .setTenantId(WebUtil.getTenantId())
                .setSchoolId(WebUtil.getSchoolId())
                .setCampusId(WebUtil.getCampusId())
                .setInfoId(dto.getSubmitInfoId())
                .setRoleType(TaskRoleTypeEnum.TEACHER.getCode())
                .setOperateType(OperateTypeEnum.MODIFY.getCode())
                .setOperateTime(DateUtil.date())
                .setIsBatchFlag(dto.getIsBatchFlag())
                .setOperatorId(WebUtil.getStaffId());

        taskOperateLogService.saveEvaluateTaskOperateLog(saveDTO);

        // 删除的行为记录
        List<Long> removeBehaviourIds = behaviourRecords.stream().filter(s -> Objects.nonNull(s.getId()))
                .map(BehaviourRecord::getId).distinct().collect(Collectors.toList());
        behaviourRecordHandleDTO.setDeletedBehaviourRecordIds(removeBehaviourIds);

        // 新增的行为记录
        List<Long> behaviourIds = records.stream().filter(s -> Objects.nonNull(s.getId())).map(BehaviourRecord::getId)
                .distinct().collect(Collectors.toList());
        behaviourRecordHandleDTO.setAddBehaviourRecordIds(behaviourIds);
        // 发送消息到mq 先删除 在新增
        convertInitialService.sendMQ(2, 3, behaviourRecords, null);
        // 发送积分金币转换mq（删除）
        convertInitialService.sendBehaviourExchangeMq(
                this.assemblyPointExchangeMq(behaviourRecords, Convert.toLong(dto.getPlanId()),
                        CoinExchangeOperateTypeEnum.DELETE.getCode(),
                        CoinExchangeStrategyEnum.GRAPHIC_COMMENT_TEACHER.getCode()));

        evaluateExecutor.execute(() -> {
            // 延迟新增 避免先处理新增 在处理删除
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                log.info("发送新增消息失败，错误信息：", e);
            }
            convertInitialService.sendMQ(2, 1, records, null);
            // 发送积分金币转换mq（新增）
            convertInitialService.sendBehaviourExchangeMq(
                    this.assemblyPointExchangeMq(records, Convert.toLong(dto.getPlanId()),
                            CoinExchangeOperateTypeEnum.CREATE.getCode(),
                            CoinExchangeStrategyEnum.GRAPHIC_COMMENT_TEACHER.getCode()));
        });

        //点评项增删改打标
        Set<String> sectionSet = new HashSet<>();
        //删除的年级
        if (CollUtil.isNotEmpty(behaviourRecords)) {
            addGradeRedis(behaviourRecords, sectionSet);
        }
        //新增的年级
        if (CollUtil.isNotEmpty(records)) {
            addGradeRedis(records, sectionSet);
        }
        //新增和删除的学段
        if (CollUtil.isNotEmpty(sectionSet)) {
            addSectionRedis(sectionSet);
        }
        //图文点评，体测指标，则同步数据至学生体测数据
        behaviourRecordHandleDTO.setAddBehaviourRecords(records);

        evaluateExecutor.execute(() -> {
            // 历史学年点评记录变动触发重新计算
            if (CollUtil.isEmpty(behaviourRecords)) {
                return;
            }
            BehaviourRecord behaviourRecord = behaviourRecords.get(Constant.ZERO);
            TermVo termVo = termLogic.getTermVoByCampusIdCache(WebUtil.getSchoolIdLong(), WebUtil.getCampusIdLong(),
                    Convert.toLong(behaviourRecord.getCampusSectionId()), behaviourRecord.getSubmitTime());
            // 如果是历史学期 直接触发综合成绩计算
            if (!termVo.getIsCurrentTerm()) {
                // 综合成绩
                this.revaluate(behaviourRecord.getCampusSectionId(), behaviourRecord.getGradeId(),
                        termVo.getSchoolYear(), termVo.getTermName(), behaviourRecord.getSubmitTime(),
                        RevaluateBusinessTypeEnum.REPORT_EXAM.getCode());
            }
            // 如果修改的不是当天的提交记录 则触发今日之前的T+1数据变更重复重新计算
            if (!DateUtil.isSameDay(DateUtil.date(), behaviourRecord.getSubmitTime())) {
                // 今日之前的T+1数据变更重复重新计算
                this.revaluate(behaviourRecord.getCampusSectionId(), behaviourRecord.getGradeId(),
                        termVo.getSchoolYear(), termVo.getTermName(), behaviourRecord.getSubmitTime(),
                        RevaluateBusinessTypeEnum.BEHAVIOUR_RECORD.getCode());
            }
        });
        return Boolean.TRUE;
    }

    public void revaluate(String campusSectionId, String gradeId, String schoolYear, String termName, Date submitTime,
                          Integer businessType) {

        Map<String/*业务字段名*/, Object/*业务字段、DTO对象、json等*/> businessData = new HashMap<String, Object>() {{
            put(RevaluateHistoryDataQuery.SUBMIT_TIME, submitTime);
        }};
        revaluateHistoryDataLogic.revaluate(RevaluateHistoryDataQuery.builder()
                .schoolId(WebUtil.getSchoolId())
                .campusId(WebUtil.getCampusId())
                .campusSectionId(campusSectionId)
                .gradeId(gradeId)
                .businessSource(RevaluateBusinessSourceEnum.HISTORY_APPRAISE_CHANGE.getCode())
                .businessType(businessType)
                .businessData(businessData)
                .schoolYear(schoolYear)
                .termName(termName)
                .build());
    }

    private void addGradeRedis(List<BehaviourRecord> behaviourRecords, Set<String> sectionSet) {
        Map<String, List<BehaviourRecord>> deleteSectionIdMap = behaviourRecords
                .stream()
                .collect(Collectors.groupingBy(BehaviourRecord::getCampusSectionId));

        deleteSectionIdMap.forEach((key, value) -> {
            Set<String> gradeIdSet = value.stream().map(BehaviourRecord::getGradeId).collect(Collectors.toSet());
            gradeIdSet.forEach(s -> {
                //年级
                redisUtil.sSet(RedisKeyConstants.EVALUATE_INFO_SECTION_ID + key, s);
                log.info("【点评项变动，通知计算，学段 id：【{}】, 年级 id：【{}】", key, s);
            });

        });

        sectionSet.addAll(deleteSectionIdMap.keySet());
    }

    private void addSectionRedis(Set<String> sectionSet) {
        sectionSet.forEach(s -> {
            redisUtil.sSet(RedisKeyConstants.EVALUATE_INFO_MARK, s);
            log.info("【点评项变动，通知计算，学段 id：【{}】", s);
        });

    }

    private InfoSaveDTO convertInfoModifyDTOToSaveDTO(InfoModifyDTO infoModifyDTO,
                                                      List<SubmitInfoSaveDTO> submitInfoList) {
        if (Objects.isNull(infoModifyDTO)) {
            return null;
        }
        InfoSaveDTO infoSaveDTO = new InfoSaveDTO();
        infoSaveDTO.setPlanType(infoModifyDTO.getPlanType());
        infoSaveDTO.setPlanId(infoModifyDTO.getPlanId());
        infoSaveDTO.setTargetId(infoModifyDTO.getTargetId());
        infoSaveDTO.setClassId(infoModifyDTO.getClassId());
        infoSaveDTO.setSubjectRelList(infoModifyDTO.getSubjectRelList());
        infoSaveDTO.setIsBatchFlag(infoModifyDTO.getIsBatchFlag());
        infoSaveDTO.setSubmitInfoList(submitInfoList);
        return infoSaveDTO;
    }

    /**
     * 转换图文点评编辑类
     *
     * @param submitInfoModifyDTOS
     * @return
     */
    private List<SubmitInfoSaveDTO> convertModifyDTOToSaveDTO(List<SubmitInfoModifyDTO> submitInfoModifyDTOS) {
        List<SubmitInfoSaveDTO> submitInfoSaveDTOS = new ArrayList<>();
        if (CollUtil.isEmpty(submitInfoModifyDTOS)) {
            return submitInfoSaveDTOS;
        }

        for (SubmitInfoModifyDTO submitInfoModifyDTO : submitInfoModifyDTOS) {
            SubmitInfoSaveDTO submitInfoSaveDTO = new SubmitInfoSaveDTO();
            submitInfoSaveDTO.setName(submitInfoModifyDTO.getName());
            submitInfoSaveDTO.setKey(submitInfoModifyDTO.getKey());
            submitInfoSaveDTO.setRequired(submitInfoModifyDTO.getRequired());
            submitInfoSaveDTO.setAdjustScore(submitInfoModifyDTO.getAdjustScore());
            submitInfoSaveDTO.setType(submitInfoModifyDTO.getType());
            submitInfoSaveDTO.setIsScore(submitInfoModifyDTO.getIsScore());
            submitInfoSaveDTO.setScore(submitInfoModifyDTO.getScore());
            submitInfoSaveDTO.setSubmitList(submitInfoModifyDTO.getSubmitList());
            submitInfoSaveDTO.setSubmitValue(submitInfoModifyDTO.getSubmitValue());
            submitInfoSaveDTO.setScoreType(submitInfoModifyDTO.getScoreType());
            submitInfoSaveDTO.setAdjustValue(submitInfoModifyDTO.getAdjustValue());
            submitInfoSaveDTO.setUnit(submitInfoModifyDTO.getUnit());
            submitInfoSaveDTO.setList(submitInfoModifyDTO.getList());
            submitInfoSaveDTO.setSubmitType(submitInfoModifyDTO.getScoreType());
            submitInfoSaveDTO.setPlaceholder(submitInfoModifyDTO.getPlaceholder());
            submitInfoSaveDTO.setIsPhoto(submitInfoModifyDTO.getIsPhoto());
            submitInfoSaveDTO.setOptions(convertModifyOptionToSaveOption(submitInfoModifyDTO.getOptions()));
            submitInfoSaveDTO.setIsAdjustScore(submitInfoModifyDTO.getIsAdjustScore());
            submitInfoSaveDTO.setIsMultiple(submitInfoModifyDTO.getIsMultiple());
            submitInfoSaveDTO.setNameMaxlength(submitInfoModifyDTO.getNameMaxlength());
            submitInfoSaveDTO.setReTypeName(submitInfoModifyDTO.getReTypeName());
            submitInfoSaveDTO.setTypeName(submitInfoModifyDTO.getTypeName());
            submitInfoSaveDTOS.add(submitInfoSaveDTO);
        }
        return submitInfoSaveDTOS;
    }

    private SubmitOptionInfoSaveDTO convertModifyOptionToSaveOption(
            SubmitOptionInfoModifyDTO submitOptionInfoModifyDTO) {
        if (Objects.isNull(submitOptionInfoModifyDTO)) {
            return null;
        }

        SubmitOptionInfoSaveDTO submitOptionInfoSaveDTO = new SubmitOptionInfoSaveDTO();
        submitOptionInfoSaveDTO.setIsScore(submitOptionInfoModifyDTO.getIsScore());
        List<SubmitOptionInfoSubSaveDTO> options = new ArrayList<>();
        if (CollUtil.isEmpty(submitOptionInfoModifyDTO.getOptions())) {
            return submitOptionInfoSaveDTO;
        }
        for (SubmitOptionInfoSubModifyDTO option : submitOptionInfoModifyDTO.getOptions()) {
            SubmitOptionInfoSubSaveDTO submitOptionInfoSubSaveDTO = new SubmitOptionInfoSubSaveDTO();
            submitOptionInfoSubSaveDTO.setKey(option.getKey());
            submitOptionInfoSubSaveDTO.setLabel(option.getLabel());
            submitOptionInfoSubSaveDTO.setValue(option.getValue());
            options.add(submitOptionInfoSubSaveDTO);
        }
        submitOptionInfoSaveDTO.setOptions(options);
        return submitOptionInfoSaveDTO;
    }

    @Override
    public Boolean removeEvaluateInfo(InfoRemoveDTO dto) {
        BehaviourRecordHandleDTO behaviourRecordHandleDTO = new BehaviourRecordHandleDTO();
        deleteEvaluateInfo(dto, behaviourRecordHandleDTO);
        this.handleActivity(behaviourRecordHandleDTO);
        return Boolean.TRUE;
    }


    /**
     * 删除行为记录
     *
     * @param dto
     * @param behaviourRecordHandleDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteEvaluateInfo(InfoRemoveDTO dto, BehaviourRecordHandleDTO behaviourRecordHandleDTO) {
        Assert.isTrue(StrUtil.isNotBlank(dto.getSubmitInfoId()),
                () -> new BizException(BizExceptionEnum.DETAIL_ID_NOT_NULL.getMessage()));

        boolean hasRecords = false;
        List<BehaviourRecord> behaviourRecords;

        //图文详情下的 批量导入直接查行为记录表
        if (dto.getEvaluateType() != null && dto.getEvaluateType().equals(3)) {

            behaviourRecords = behaviourRecordManager.listByInfoIdV3(dto.getSubmitInfoId());

        } else {
            Info info = infoLogic.getById(dto.getSubmitInfoId());
            Assert.notNull(info, () -> new BizException(BizExceptionEnum.DETAIL_NOT_NULL.getMessage()));

            // 删除行为记录
            behaviourRecords = behaviourRecordManager.listByInfoIdV3(info.getId());

            hasRecords = CollUtil.isNotEmpty(behaviourRecords);

            if (hasRecords) {
                BehaviourRecord behaviourRecord = behaviourRecords.get(Constant.ZERO);
                TermVo termVo = termLogic.getTermVoByCampusIdCache(WebUtil.getSchoolIdLong(), WebUtil.getCampusIdLong(),
                        Convert.toLong(behaviourRecord.getCampusSectionId()), behaviourRecord.getSubmitTime());
                if (termVo != null && Objects.equals(Boolean.FALSE, termVo.getIsCurrentTerm())) {
                    throw new BizException("历史学期点评记录不允许删除");
                }
            }
            taskLogic.removeById(info.getTaskId());
            infoDao.deleteById(dto.getSubmitInfoId());
        }

        if (hasRecords) {
            List<Long> behaviourIds = behaviourRecords.stream().map(BehaviourRecord::getId)
                    .collect(Collectors.toList());
            behaviourRecordManager.removeBatchByIds(behaviourIds);

            if (CharSequenceUtil.isNotBlank(dto.getPlanId()) && Objects.equals(dto.getSource(), 2)) {
                List<BehaviourRecord> behaviourRecords1 = behaviourRecords.stream()
                        .filter(item -> Objects.nonNull(item.getScore())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(behaviourRecords1)) {
                    List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS = this.convertThirdToXdlRecordMessageDTO(
                            behaviourRecords1, Convert.toLong(dto.getPlanId()), null, new HashMap<>());
                    eventSendHelper.sendEventMQ(thirdToXdlRecordMessageDTOS, xwlDelRecord);
                }
            }

            // 删除行为记录需要匹配活动
            behaviourRecordHandleDTO.setDeletedBehaviourRecordIds(behaviourIds);
        }
        // 发送消息到mq
        convertInitialService.sendMQ(3, 3, behaviourRecords, null);
        // 发送积分金币转换mq
        convertInitialService.sendBehaviourExchangeMq(
                this.assemblyPointExchangeMq(behaviourRecords, Convert.toLong(dto.getPlanId()),
                        CoinExchangeOperateTypeEnum.DELETE.getCode(),
                        CoinExchangeStrategyEnum.GRAPHIC_COMMENT_TEACHER.getCode()));

        //点评项增删改打标
        Set<String> sectionSet = new HashSet<>();
        //删除的年级
        if (hasRecords) {
            addGradeRedis(behaviourRecords, sectionSet);
        }
        //新增和删除的学段
        if (CollUtil.isNotEmpty(sectionSet)) {
            addSectionRedis(sectionSet);
        }

        evaluateExecutor.execute(() -> {
            // 历史学年点评记录变动触发重新计算
            if (CollUtil.isEmpty(behaviourRecords)) {
                return;
            }
            BehaviourRecord behaviourRecord = behaviourRecords.get(Constant.ZERO);
            TermVo termVo = termLogic.getTermVoByCampusIdCache(WebUtil.getSchoolIdLong(), WebUtil.getCampusIdLong(),
                    Convert.toLong(behaviourRecord.getCampusSectionId()), behaviourRecord.getSubmitTime());
            // 如果是历史学期 直接触发综合成绩计算
            if (!termVo.getIsCurrentTerm()) {
                // 综合成绩
                this.revaluate(behaviourRecord.getCampusSectionId(), behaviourRecord.getGradeId(),
                        termVo.getSchoolYear(), termVo.getTermName(), behaviourRecord.getSubmitTime(),
                        RevaluateBusinessTypeEnum.REPORT_EXAM.getCode());
            }
            // 如果修改的不是当天的提交记录 则触发今日之前的T+1数据变更重复重新计算
            if (!DateUtil.isSameDay(DateUtil.date(), behaviourRecord.getSubmitTime())) {
                // 今日之前的T+1数据变更重复重新计算
                this.revaluate(behaviourRecord.getCampusSectionId(), behaviourRecord.getGradeId(),
                        termVo.getSchoolYear(), termVo.getTermName(), behaviourRecord.getSubmitTime(),
                        RevaluateBusinessTypeEnum.BEHAVIOUR_RECORD.getCode());
            }
        });

        return Boolean.TRUE;
    }


    @Override
    public GetEvaluateInfoDetailVO getEvaluateInfoDetail(GetEvaluateInfoDetailQueryDTO dto) {
        // 参数判断
        Assert.isTrue(CharSequenceUtil.isNotBlank(dto.getInfoId()),
                () -> new BizException(BizExceptionEnum.DETAIL_ID_NOT_NULL.getMessage()));

        GetEvaluateInfoDetailVO getEvaluateInfoDetailVO = new GetEvaluateInfoDetailVO();
        //极速点评记录
        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.listByInfoIdV2(dto.getInfoId());
        if (CollUtil.isEmpty(behaviourRecords)) {
            behaviourRecords = dorisBehaviourRecordManager.listByInfoIdV4(dto.getInfoId());
        }
        // 是否显示点评人
        Boolean showAppraisalFlag = reportPushRuleService.getShowAppraisalFlag(WebUtil.getCampusId());

        if (Objects.equals(dto.getDetailType(), 5)) {
            behaviourRecords = behaviourRecordManager.listByIds(Collections.singletonList(Convert.toLong(dto.getId())));
            this.getCardEvaluateInfoDetailVO(behaviourRecords, getEvaluateInfoDetailVO);
            return getEvaluateInfoDetailVO;
        }

        //处理新的点评详情
        if (!dto.getInfoId().matches(Constant.LETTER_REGEX)) {
            if (CollUtil.isEmpty(behaviourRecords)) {
                log.warn("极速点评记录-详情为空，dto：{}", JSONUtil.toJsonStr(dto));
                getEvaluateInfoDetailVO.setSpeedRecordDeleted(Boolean.TRUE);
                return getEvaluateInfoDetailVO;
            }

            //说明是批量导入
            if (DataSourceEnum.BATCH_IMPORTS.getCode().equals(behaviourRecords.get(0).getDataSource())) {
                getBatchImportsGetEvaluateInfoDetailVO(dto, behaviourRecords, getEvaluateInfoDetailVO);
                return getEvaluateInfoDetailVO;
            }

            return this.getGetEvaluateInfoDetailVO(dto, behaviourRecords, getEvaluateInfoDetailVO);
        }

        // 填写详情
        Info evaluateInfo = infoLogic.getById(dto.getInfoId());
        if (evaluateInfo == null) {
            return new GetEvaluateInfoDetailVO();
        }
        TaskPO taskPO = taskService.getById(evaluateInfo.getTaskId());
        if (Objects.isNull(taskPO)) {
            return new GetEvaluateInfoDetailVO();
        }
        // 拼接图片url
        infoHelper.jointImg(evaluateInfo);

        GetEvaluateInfoDetailVO vo = infoConvert.toEvaluateInfoDetailVO(evaluateInfo);

        Long targetId = evaluateInfo.getTargetId();
        //是否为体测指标
        Map<Long, Boolean> targetFromSportMap =
                targetService.checkTargetFromSport(CollUtil.toList(targetId));
        vo.setIsSportTarget(Boolean.TRUE.equals(targetFromSportMap.get(targetId)));

        vo.setApprovalStatus(taskPO.getApprovalStatus());

        if (TaskRoleTypeEnum.PARENT.getCode().equals(taskPO.getRoleType())) {
            StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(taskPO.getSchoolId()),
                    Convert.toLong(taskPO.getSubmitStaffId()));
            vo.setStudentName(Objects.nonNull(studentVO) ? studentVO.getStudentName() : "");
        }

        //填充积分版信息以及来源
        this.fillDataSourceAndPlans(behaviourRecords, vo);

        // 操作日志集合
        List<TaskOperateLog> evaluateTaskOperateLogs = taskOperateLogManager.listTaskOperateLogByInfoId(
                dto.getInfoId());
        List<ModifyHistoryVO> historyVOS = new ArrayList<>();

        //老师名称
        List<Long> staffIds = evaluateTaskOperateLogs.stream()
                .filter(s -> TaskRoleTypeEnum.TEACHER.getCode().equals(s.getRoleType()))
                .map(t -> Long.parseLong(t.getOperatorId())).distinct().collect(Collectors.toList());
        Map<String, String> nameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(staffIds)) {
            StaffOrgQueryDTO staffOrgQueryDTO = new StaffOrgQueryDTO();
            staffOrgQueryDTO.setStaffIds(staffIds);
            staffOrgQueryDTO.setId(Long.parseLong(WebUtil.getSchoolId()));
            //入参类型：学校id
            staffOrgQueryDTO.setType(SaasOrgQueryEnum.SCHOOL_ID.getCode());
            List<StaffOrgsVO> staffOrgVOList = cacheSaasManager.queryStaffBelongToOrgs(staffOrgQueryDTO);
            if (CollUtil.isNotEmpty(staffOrgVOList)) {
                nameMap = staffOrgVOList.stream()
                        .collect(Collectors.toMap(id -> String.valueOf(id.getStaffId()), StaffOrgsVO::getStaffName));
            }
        }

        //学生名称
        List<Long> studentIds = evaluateTaskOperateLogs.stream()
                .filter(s -> TaskRoleTypeEnum.PARENT.getCode().equals(s.getRoleType()))
                .map(t -> Long.parseLong(t.getOperatorId())).distinct().collect(Collectors.toList());
        //加上当前学生的id
        if (CharSequenceUtil.isNotBlank(dto.getStudentId())) {
            studentIds.add(Convert.toLong(dto.getStudentId()));
        }
        Map<Long, String> studentNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(studentIds)) {
            EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
            eduStudentPageQueryDTO.setStudentIds(studentIds);
            eduStudentPageQueryDTO.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
            List<EduStudentInfoVO> eduStudentInfos = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
            studentNameMap = eduStudentInfos.stream()
                    .collect(Collectors.toMap(EduStudentInfoVO::getId, EduStudentInfoVO::getStudentName));
        }

        for (TaskOperateLog log : evaluateTaskOperateLogs) {
            ModifyHistoryVO historyVO = new ModifyHistoryVO().setOperateDate(log.getOperateTime());
            historyVO.setOperateType(log.getOperateType());
            historyVO.setRoleType(log.getRoleType());
            historyVO.setOperatorName(
                    TaskRoleTypeEnum.TEACHER.getCode().equals(log.getRoleType()) ? nameMap.get(log.getOperatorId())
                            : studentNameMap.get(Convert.toLong(log.getOperatorId())));
            historyVO.setApprovalReason(log.getApprovalReason());
            historyVO.setApprovalStatus(log.getApprovalStatus());
            historyVOS.add(historyVO);
        }
        // 处理操作人数据 如果showAppraisalFlag为false，剔除historyVOS中roleType为老师的数据
        if (!showAppraisalFlag) {
//            historyVOS = historyVOS.stream().filter(s -> !TaskRoleTypeEnum.TEACHER.getCode().equals(s.getRoleType())).collect(Collectors.toList());
            historyVOS = Collections.emptyList();
        }

        vo.setModifyHistory(historyVOS);

        //获取最新的操作记录的审核状态(且和当前的任务状态一致)
        if (CollUtil.isNotEmpty(evaluateTaskOperateLogs)) {
            //获取到有操作状态的数据
            List<TaskOperateLog> collect = evaluateTaskOperateLogs.stream()
                    .filter(s -> Objects.nonNull(s.getApprovalStatus()))
                    .filter(s -> s.getApprovalStatus().equals(taskPO.getApprovalStatus())).collect(Collectors.toList());
            if (CollUtil.isEmpty(collect)) {
                return this.fillResponseParam(vo, evaluateInfo.getId(), taskPO.getSubmitTime(), studentIds);
            }
            TaskOperateLog taskOperateLog = collect.get(0);
            vo.setRefuseReason(taskOperateLog.getApprovalReason());
            vo.setDate(taskOperateLog.getCreateTime());
        }

        return this.fillResponseParam(vo, evaluateInfo.getId(), taskPO.getSubmitTime(), studentIds);
    }

    /**
     * 填充积分版信息以及来源
     */
    private void fillDataSourceAndPlans(List<BehaviourRecord> behaviourRecords, GetEvaluateInfoDetailVO vo) {
        if (CollUtil.isEmpty(behaviourRecords)) {
            log.warn("极速点评记录-填充积分版信息以及来源入参为空，behaviourRecords：{}",
                    JSONUtil.toJsonStr(behaviourRecords));
            return;
        }
        Integer dataSource = behaviourRecords.get(0).getDataSource();
        if (DataSourceEnum.EVALUATE_IMAGE_TEXT.getCode().equals(dataSource)) {
            vo.setEvaluateType(EvaluateTypeEnum.PICTURE.getCode());
            vo.setSource(RecordDataSourceEnum.NEW.getCode());
            vo.setClassId(behaviourRecords.get(0).getClassId());
        } else {
            vo.setEvaluateType(EvaluateTypeEnum.PICTURE.getCode());
            vo.setSource(RecordDataSourceEnum.HISTORY.getCode());
        }

        //积分版信息
        PlanListsByEvaluateRequest request = new PlanListsByEvaluateRequest();
        request.setSourceType(2);
        request.setRecordId(Convert.toStr(behaviourRecords.get(0).getId()));
        PlanListsResponse planListsResponse = internalDriveRemote.queryPlansByEvaluateId(request);
        if (Objects.nonNull(planListsResponse)) {
            vo.setPlanId(planListsResponse.getPlanId());
            vo.setPlanType(planListsResponse.getPlanType());
        }
    }

    /**
     * 处理新的点评详情
     */
    private GetEvaluateInfoDetailVO getGetEvaluateInfoDetailVO(GetEvaluateInfoDetailQueryDTO dto,
                                                               List<BehaviourRecord> behaviourRecords,
                                                               GetEvaluateInfoDetailVO getEvaluateInfoDetailVO) {
        //获取学生信息
        List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOS = this.listStudentInfoVOS(behaviourRecords);

        List<SpeedRecordStudentResponse> speedRecordStudentResponseList = new ArrayList<>();
        getEvaluateInfoDetailVO.setSpeedRecordStudentResponseList(speedRecordStudentResponseList);
        getEvaluateInfoDetailVO.setSource(RecordDataSourceEnum.NEW.getCode());
        getEvaluateInfoDetailVO.setEvaluateType(EvaluateTypeEnum.SPEED.getCode());
        getEvaluateInfoDetailVO.setClassId(behaviourRecords.get(0).getClassId());

        // 查询行为记录关联的分类
        List<Long> ids = behaviourRecords.stream().map(BehaviourRecord::getId).collect(Collectors.toList());
        Map<Long, List<String>> optExtPOS = new HashMap<>();
        if (Objects.equals(dto.getDetailType(), 2)) {
            optExtPOS = CollStreamUtil.toMap(evaluateBehaviourRecordOptExtManager.byBehaviourRecordIds(ids),
                    EvaluateBehaviourRecordOptExtPO::getBehaviourRecordId,
                    item -> StrUtil.isEmpty(item.getTextarea()) ? new ArrayList<>()
                            : JSON.parseArray(item.getTextarea(), String.class));
        }

        List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordExtPOS = evaluateBehaviourRecordExtManager.listByEvaluateIds(
                ids);
//        List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordExtPOS = evaluateBehaviourRecordExtManager.listByEvaluateIdsDoris(ids);
        Map<Long, EvaluateBehaviourRecordExtPO> longEvaluateBehaviourRecordClassifyPOMap = evaluateBehaviourRecordExtPOS.stream()
                .collect(Collectors.toMap(EvaluateBehaviourRecordExtPO::getBehaviourRecordId, Function.identity()));

        Map<String, List<BehaviourRecord>> studentIdToRecordsMap = CollStreamUtil
                .groupByKey(behaviourRecords, BehaviourRecord::getStudentId);
        for (Map.Entry<String, List<BehaviourRecord>> studentIdToRecordEntry : studentIdToRecordsMap.entrySet()) {
            List<BehaviourRecord> studentIdToRecordEntryValue = studentIdToRecordEntry.getValue();
            List<SpeedRecordResponse> speedRecordResponses =
                    taskService.listSpeedRecordResponses(studentIdToRecordEntry, speedRecordStudentResponseList,
                            studentInfoVOS);

            //校标
            taskService.fillSchoolRecordDetail(studentIdToRecordEntryValue, speedRecordResponses,
                    longEvaluateBehaviourRecordClassifyPOMap, optExtPOS);

            //师标
            taskService.fillGeneralRecordDetail(studentIdToRecordEntryValue, speedRecordResponses,
                    longEvaluateBehaviourRecordClassifyPOMap);
        }
        //操作日志
        this.listTaskOperateLogs(dto, getEvaluateInfoDetailVO, CollUtil.getFirst(behaviourRecords));
        return getEvaluateInfoDetailVO;
    }

    /**
     * 处理新的点评详情
     */
    private GetEvaluateInfoDetailVO getCardEvaluateInfoDetailVO(List<BehaviourRecord> behaviourRecords,
                                                                GetEvaluateInfoDetailVO getEvaluateInfoDetailVO) {
        if (CollUtil.isEmpty(behaviourRecords)) {
            return null;
        }
        //获取学生信息
        List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOS = this.listStudentInfoVOS(behaviourRecords);
        if (CollUtil.isEmpty(studentInfoVOS)) {
            return null;
        }

        List<SpeedRecordStudentResponse> speedRecordStudentResponseList = new ArrayList<>();
        getEvaluateInfoDetailVO.setSpeedRecordStudentResponseList(speedRecordStudentResponseList);
        getEvaluateInfoDetailVO.setSource(RecordDataSourceEnum.NEW.getCode());
        getEvaluateInfoDetailVO.setEvaluateType(EvaluateTypeEnum.SPEED.getCode());
        getEvaluateInfoDetailVO.setClassId(behaviourRecords.get(0).getClassId());

        // 查询行为记录关联的分类
        List<SpeedRecordStudentResponse> speedRecordStudentResponses = this.buildSpeedRecordStudentResponse(
                behaviourRecords.get(0), studentInfoVOS.get(0));

        //操作日志
        List<ModifyHistoryVO> modifyHistoryVOS = this.buildCardModifyHistory(behaviourRecords.get(0));
        getEvaluateInfoDetailVO.setModifyHistory(modifyHistoryVOS);
        getEvaluateInfoDetailVO.setSpeedRecordStudentResponseList(speedRecordStudentResponses);
        return getEvaluateInfoDetailVO;
    }

    private List<SpeedRecordStudentResponse> buildSpeedRecordStudentResponse(BehaviourRecord behaviourRecord,
                                                                             com.hailiang.saas.model.vo.StudentInfoVO studentInfoVO) {
        SpeedRecordStudentResponse speedRecordStudentResponse = new SpeedRecordStudentResponse();
        speedRecordStudentResponse.setStudentName(studentInfoVO.getStudentName());
        speedRecordStudentResponse.setStudentId(behaviourRecord.getStudentId());
        List<SpeedRecordResponse> speedRecordResponses = new ArrayList<>();
        SpeedRecordResponse speedRecordResponse = new SpeedRecordResponse();
        speedRecordResponse.setGroupName("分值卡");
        speedRecordResponse.setGroupId(behaviourRecord.getTargetId());
        List<SpeedRecordStudentDetailResponse> speedRecordStudentDetailResponses = new ArrayList<>();
        speedRecordResponse.setSpeedRecordStudentDetailResponses(speedRecordStudentDetailResponses);
        SpeedRecordStudentDetailResponse speedRecordStudentDetailResponse = new SpeedRecordStudentDetailResponse();
        speedRecordStudentDetailResponse.setScore(behaviourRecord.getScore());
        speedRecordStudentDetailResponse.setInfoName(behaviourRecord.getInfoName());
        speedRecordStudentDetailResponse.setInfoType(behaviourRecord.getInfoType());
        speedRecordStudentDetailResponse.setOptionId(behaviourRecord.getOptionId());
        speedRecordStudentDetailResponse.setScoreType(behaviourRecord.getScoreType());
        speedRecordStudentDetailResponse.setSubmitTime(behaviourRecord.getSubmitTime());
        speedRecordStudentDetailResponse.setDataSource(behaviourRecord.getDataSource());
        speedRecordStudentDetailResponses.add(speedRecordStudentDetailResponse);
        speedRecordResponses.add(speedRecordResponse);
        speedRecordStudentResponse.setSpeedRecordResponses(speedRecordResponses);
        List<SpeedRecordStudentResponse> result = new ArrayList<>();
        result.add(speedRecordStudentResponse);
        return result;
    }

    /**
     * 构建修改记录
     *
     * @return
     */
    private List<ModifyHistoryVO> buildCardModifyHistory(BehaviourRecord behaviourRecord) {
        List<ModifyHistoryVO> modifyHistoryVOS = new ArrayList<>();
        ModifyHistoryVO modifyHistoryVO = new ModifyHistoryVO();
        modifyHistoryVO.setOperateType(1);
        modifyHistoryVO.setRoleType(1);
        modifyHistoryVO.setOperateDate(behaviourRecord.getSubmitTime());
        modifyHistoryVO.setOperatorName(behaviourRecord.getAppraisalName());
        modifyHistoryVOS.add(modifyHistoryVO);
        return modifyHistoryVOS;
    }


    /**
     * 处理新的点评详情（批量导入）
     */
    private void getBatchImportsGetEvaluateInfoDetailVO(GetEvaluateInfoDetailQueryDTO dto,
                                                        List<BehaviourRecord> behaviourRecords,
                                                        GetEvaluateInfoDetailVO getEvaluateInfoDetailVO) {
        //获取学生信息
        BehaviourRecord behaviourRecord = behaviourRecords.get(0);

        List<SubmitInfoVO> submitInfoList = new ArrayList<>();

        getEvaluateInfoDetailVO.setSource(RecordDataSourceEnum.NEW.getCode());
        getEvaluateInfoDetailVO.setEvaluateType(EvaluateTypeEnum.IMPORTS.getCode());
        getEvaluateInfoDetailVO.setClassId(behaviourRecord.getClassId());
        getEvaluateInfoDetailVO.setTargetId(behaviourRecord.getTargetId());
        getEvaluateInfoDetailVO.setTaskName(behaviourRecord.getTargetName());

        List<EvaluateBehaviourImportsRecordDetailPO> importsRecordDetailPOS = evaluateBehaviourImportsRecordDetailManager.listByInfoId(
                Convert.toLong(dto.getInfoId()));

        if (CollUtil.isEmpty(importsRecordDetailPOS)) {
            return;
        }

        buildStudentInfo(importsRecordDetailPOS, submitInfoList);

        for (EvaluateBehaviourImportsRecordDetailPO importsRecordDetailPO : importsRecordDetailPOS) {
            SubmitInfoVO submitInfoVO = new SubmitInfoVO();
            submitInfoVO.setType(importsRecordDetailPO.getControlType());
            submitInfoVO.setTypeName(
                    SubmitInfoTypeEnum.getByValue(importsRecordDetailPO.getControlType()).getMessage());
            submitInfoVO.setReTypeName(
                    SubmitInfoTypeEnum.getByValue(importsRecordDetailPO.getControlType()).getMessage());
            submitInfoVO.setKey(importsRecordDetailPO.getOptionId());
            submitInfoVO.setName(importsRecordDetailPO.getControlName());
            submitInfoVO.setIsScore(importsRecordDetailPO.getControlIsScore() == 0 ? false : true);

            //点评项为选项则特殊处理
            if (SubmitInfoTypeEnum.isCheckSubmitInfo(importsRecordDetailPO.getControlType())) {

                SubmitInfoSubVO submitInfoSubVO = new SubmitInfoSubVO();

                if (importsRecordDetailPO.getControlIsScore() == 1) {
                    BigDecimal optionScore = importsRecordDetailPO.getOptionScore();
                    if (optionScore != null) {
                        submitInfoVO.setScoreType(optionScore.compareTo(BigDecimal.ZERO));
                        submitInfoVO.setScore(optionScore);
                        submitInfoSubVO.setValue(optionScore);
                    }
                }

                submitInfoSubVO.setLabel(importsRecordDetailPO.getOptionValue());
                submitInfoSubVO.setKey(importsRecordDetailPO.getOptionId());

                if (SubmitInfoTypeEnum.MULTI_CHECK.getText().equals(importsRecordDetailPO.getControlType())) {
                    submitInfoVO.setSubmitValue(CollUtil.newArrayList(submitInfoSubVO));
                } else {
                    submitInfoVO.setSubmitValue(submitInfoSubVO);
                }
            }
            //点评项为分值特殊处理
            else if (SubmitInfoTypeEnum.SCORE.getText().equals(importsRecordDetailPO.getControlType())) {

                String controlValue = importsRecordDetailPO.getControlValue();
                if (controlValue != null) {
                    BigDecimal score = new BigDecimal(controlValue);
                    submitInfoVO.setScoreType(score.compareTo(BigDecimal.ZERO));
                    submitInfoVO.setScore(score.abs());
                    submitInfoVO.setSubmitValue(score.abs());
                }
            } else {
                submitInfoVO.setSubmitValue(importsRecordDetailPO.getControlValue());
            }

            submitInfoVO.setOptions(new SubmitOptionInfoSaveVO());
            submitInfoList.add(submitInfoVO);
        }

        getEvaluateInfoDetailVO.setSubmitInfoList(submitInfoList);

        ModifyHistoryVO historyVO = new ModifyHistoryVO()
                .setOperateDate(behaviourRecord.getSubmitTime())
                .setOperateType(OperateTypeEnum.SUBMIT.getCode())
                .setRoleType(TaskRoleTypeEnum.TEACHER.getCode())
                .setOperatorName(behaviourRecord.getAppraisalName());

        getEvaluateInfoDetailVO.setModifyHistory(CollUtil.newArrayList(historyVO));

        TermVo termVo = termLogic.getTermVoByCampusIdCache(
                WebUtil.getSchoolIdLong(),
                WebUtil.getCampusIdLong(),
                Convert.toLong(behaviourRecord.getCampusSectionId()),
                behaviourRecord.getSubmitTime());

        if (termVo == null) {
            return;
        }
        getEvaluateInfoDetailVO.setIsCurrentTerm(termVo.getIsCurrentTerm());
        getEvaluateInfoDetailVO.setIsCurrentYear(termVo.getIsCurrentYear());
        getEvaluateInfoDetailVO.setSchoolYear(termVo.getSchoolYear());
        getEvaluateInfoDetailVO.setTermName(termVo.getTermName());
    }

    /**
     * 组装学生信息
     *
     * @param importsRecordDetailPOS
     * @param submitInfoList
     */
    private void buildStudentInfo(List<EvaluateBehaviourImportsRecordDetailPO> importsRecordDetailPOS,
                                  List<SubmitInfoVO> submitInfoList) {
        SubmitInfoVO submitStudentInfoVO = new SubmitInfoVO();
        submitStudentInfoVO.setType("student");
        submitStudentInfoVO.setTypeName("批量点评学生");
        submitStudentInfoVO.setName("学生");
        submitStudentInfoVO.setOptions(new SubmitOptionInfoSaveVO());

        List<SubStudentInfo> subStudentInfos = new ArrayList<>();
        SubStudentInfo subStudentInfo = new SubStudentInfo();
        EvaluateBehaviourImportsRecordDetailPO evaluateBehaviourImportsRecordDetailPO = importsRecordDetailPOS.get(0);
        subStudentInfo.setId(Convert.toStr(evaluateBehaviourImportsRecordDetailPO.getStudentId()));
        subStudentInfo.setName(evaluateBehaviourImportsRecordDetailPO.getStudentName());
        subStudentInfo.setType(6);
        subStudentInfo.setSchoolId(Convert.toStr(evaluateBehaviourImportsRecordDetailPO.getSchoolId()));
        subStudentInfo.setClassId(Convert.toStr(evaluateBehaviourImportsRecordDetailPO.getClassId()));
        subStudentInfo.setSectionCode(evaluateBehaviourImportsRecordDetailPO.getCampusSectionCode());
        subStudentInfos.add(subStudentInfo);
        submitStudentInfoVO.setSubmitValue(subStudentInfos);

        submitInfoList.add(submitStudentInfoVO);
    }

    /**
     * 操作日志
     */
    private List<TaskOperateLog> listTaskOperateLogs(GetEvaluateInfoDetailQueryDTO dto, GetEvaluateInfoDetailVO vo,
                                                     BehaviourRecord behaviourRecord) {
        // 操作日志集合
        List<TaskOperateLog> evaluateTaskOperateLogs = taskOperateLogManager.listTaskOperateLogByInfoId(
                dto.getInfoId());
        List<ModifyHistoryVO> historyVOS = new ArrayList<>();

        //老师名称
        List<Long> staffIds = evaluateTaskOperateLogs
                .stream()
                .filter(s -> TaskRoleTypeEnum.TEACHER.getCode()
                        .equals(s.getRoleType()))
                .map(t -> Long.parseLong(t.getOperatorId()))
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> nameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(staffIds)) {
            StaffOrgQueryDTO staffOrgQueryDTO = new StaffOrgQueryDTO();
            staffOrgQueryDTO.setStaffIds(staffIds);
            staffOrgQueryDTO.setId(Long.parseLong(WebUtil.getSchoolId()));
            //入参类型：学校id
            staffOrgQueryDTO.setType(SaasOrgQueryEnum.SCHOOL_ID.getCode());
            List<StaffOrgsVO> staffOrgVOList = cacheSaasManager.queryStaffBelongToOrgs(staffOrgQueryDTO);
            if (CollUtil.isNotEmpty(staffOrgVOList)) {
                nameMap = staffOrgVOList.stream()
                        .collect(Collectors.toMap(id -> String.valueOf(id.getStaffId()), StaffOrgsVO::getStaffName));
            }
        }

        //学生名称
        List<Long> studentIds = evaluateTaskOperateLogs.stream()
                .filter(s -> TaskRoleTypeEnum.PARENT.getCode().equals(s.getRoleType()))
                .map(t -> Long.parseLong(t.getOperatorId())).distinct().collect(Collectors.toList());
        //加上当前学生的id
        if (CharSequenceUtil.isNotBlank(dto.getStudentId())) {
            studentIds.add(Convert.toLong(dto.getStudentId()));
        }
        Map<Long, String> studentNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(studentIds)) {
            EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
            eduStudentPageQueryDTO.setStudentIds(studentIds);
            eduStudentPageQueryDTO.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
            List<EduStudentInfoVO> eduStudentInfos = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
            studentNameMap = eduStudentInfos.stream()
                    .collect(Collectors.toMap(EduStudentInfoVO::getId, EduStudentInfoVO::getStudentName));
        }

        for (TaskOperateLog log : evaluateTaskOperateLogs) {
            ModifyHistoryVO historyVO = new ModifyHistoryVO().setOperateDate(log.getOperateTime());
            historyVO.setOperateType(log.getOperateType());
            historyVO.setRoleType(log.getRoleType());
            historyVO.setOperatorName(
                    TaskRoleTypeEnum.TEACHER.getCode().equals(log.getRoleType()) ? nameMap.get(log.getOperatorId())
                            : studentNameMap.get(Convert.toLong(log.getOperatorId())));
            historyVO.setApprovalReason(log.getApprovalReason());
            historyVO.setApprovalStatus(log.getApprovalStatus());
            historyVOS.add(historyVO);
        }
        if (CollUtil.isNotEmpty(historyVOS)) {
            vo.setModifyHistory(historyVOS);
        } else {
            // 星动力的指标,第一次新增时evaluate_task_operate_log表无记录,操作日志取行为记录表中的提交人信息
            ModifyHistoryVO historyVO = new ModifyHistoryVO().setOperateDate(behaviourRecord.getSubmitTime());
            historyVO.setOperateType(OperateTypeEnum.SUBMIT.getCode());
            historyVO.setRoleType(TaskRoleTypeEnum.TEACHER.getCode());
            historyVO.setOperatorName(behaviourRecord.getAppraisalName());
            vo.setModifyHistory(CollUtil.newArrayList(historyVO));
        }
        return evaluateTaskOperateLogs;
    }


    /**
     * 获取学生信息
     */
    private List<com.hailiang.saas.model.vo.StudentInfoVO> listStudentInfoVOS(List<BehaviourRecord> behaviourRecords) {
        if (CollUtil.isEmpty(behaviourRecords)) {
            log.warn("点评记录-获取学生信息失败，参数为空，behaviourRecords：{}", JSONUtil.toJsonStr(behaviourRecords));
            return Collections.emptyList();
        }
        List<Long> studentIds = behaviourRecords.stream().map(BehaviourRecord::getStudentId).distinct()
                .map(Convert::toLong)
                .collect(Collectors.toList());
        StudentDTO studentDTO = new StudentDTO();

        List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOS = new ArrayList<>();
        if (studentIds.size() <= 1000) {
            studentDTO.setStudentIds(studentIds);
            return saasStudentManager.studentDetailV2(studentDTO);
        } else {
            int size = studentIds.size();
            int loop = size / 1000;
            for (int i = 0; i <= loop; i++) {
                int start = i * 1000;
                int end = Math.min((i + 1) * 1000, size);
                List<Long> splitStudentIds = studentIds.subList(start, end);
                if (CollUtil.isEmpty(splitStudentIds)) {
                    break;
                }
                studentDTO.setStudentIds(splitStudentIds);
                List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOList = saasStudentManager.studentDetailV2(
                        studentDTO);
                if (CollUtil.isEmpty(studentInfoVOList)) {
                    return studentInfoVOList;
                }
                if (studentInfoVOList.size() < 1000 && splitStudentIds.size() < 1000) {
                    studentInfoVOS.addAll(studentInfoVOList);
                    return studentInfoVOS;
                }
                studentInfoVOS.addAll(studentInfoVOList);
            }
        }
        return studentInfoVOS;
    }

    private GetEvaluateInfoDetailVO fillResponseParam(GetEvaluateInfoDetailVO vo, String infoId, Date submitTime,
                                                      List<Long> studentIds) {
        if (StrUtil.isBlank(infoId)) {
            return vo;
        }
        Long campusSectionId = null;
        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.list(
                new LambdaQueryWrapper<BehaviourRecord>().eq(BehaviourRecord::getInfoId, infoId).last("limit 1"));
        if (CollUtil.isEmpty(behaviourRecords)) {
            behaviourRecords = dorisBehaviourRecordManager.listByInfoIdV4(infoId);
        }

        if (CollUtil.isEmpty(behaviourRecords)) {
            // 可能是在家长提交老师审核阶段，还没有落库，尝试使用学生获取学段
            if (CollUtil.isNotEmpty(studentIds)) {
                List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOS = saasStudentManager.studentDetailV2(
                        studentIds);
                if (CollUtil.isEmpty(studentInfoVOS)) {
                    return vo;
                }
                campusSectionId = studentInfoVOS.get(0).getCampusSectionId();
            }
        } else {
            campusSectionId = Convert.toLong(behaviourRecords.get(0).getCampusSectionId());
        }
        log.info("获取到学段信息：campusSectionId{}", campusSectionId);
        if (campusSectionId == null) {
            return vo;
        }

        TermVo termVo = termLogic.getTermVoByCampusIdCache(WebUtil.getSchoolIdLong(), WebUtil.getCampusIdLong(),
                campusSectionId, submitTime);
        if (termVo == null) {
            return vo;
        }
        vo.setIsCurrentTerm(termVo.getIsCurrentTerm());
        vo.setIsCurrentYear(termVo.getIsCurrentYear());
        vo.setSchoolYear(termVo.getSchoolYear());
        vo.setTermName(termVo.getTermName());
        return vo;

    }

    /**
     * 根据学生过滤详情
     */
    @Override
    public GetEvaluateInfoDetailVO getStudentEvaluateDetail(GetEvaluateInfoDetailQueryDTO dto) {
        String isCheckAuth = dto.getIsCheckAuth();
        if (StrUtil.isBlank(isCheckAuth) || "1".equals(isCheckAuth)) {
            if (StrUtil.isNotBlank(WebUtil.getStaffId())) {
                log.info("[查看详情]-校验权限");
                if (!checkAuth(dto.getStudentId(), null)) {
                    log.info("当前学生id：{}", dto.getStudentId());
                    throw new BizException("你已不是该学生班主任，无需审核。");
                }
            }
        }
        if (StrUtil.isBlank(dto.getStudentId())) {
            dto.setStudentId(WebUtil.getStudentIdStr());
        }

        GetEvaluateInfoDetailVO getEvaluateInfoDetailVO = this.getEvaluateInfoDetail(dto);

        List<SubmitInfoVO> submitInfoVOS = getEvaluateInfoDetailVO.getSubmitInfoList();
        if (CollUtil.isEmpty(submitInfoVOS)) {
            return getEvaluateInfoDetailVO;
        }
        boolean hasStudent = false;
        //1.无论是否包含明细 -过滤学生控件中的学生
        for (SubmitInfoVO submitInfoVO : submitInfoVOS) {
            if (SubmitInfoTypeEnum.STUDENT.getText().equals(submitInfoVO.getType())
                    && Objects.nonNull(submitInfoVO.getSubmitValue()) &&
                    // 批量导入的学生控件特殊处理
                    !"批量点评学生".equals(submitInfoVO.getTypeName())) {
                submitInfoVO.setSubmitValue(
                        fillStudent((List<LinkedHashMap>) submitInfoVO.getSubmitValue(), dto.getStudentId()));
                hasStudent = true;
            }

            if (!hasStudent) {
                //2 包含明细
                // 2.1 包含明细-包含当前学生-过滤明细中学生控件中的学生
                if (SubmitInfoTypeEnum.DETAIL.getText().equals(submitInfoVO.getType())) {
                    List<List<LinkedHashMap>> submitList = submitInfoVO.getSubmitList();
                    List<List<LinkedHashMap>> submitListNew = new ArrayList<>();
                    submitList.forEach(d -> {
                        boolean isStudent = false;
                        for (LinkedHashMap detailMap : d) {
                            if (SubmitInfoTypeEnum.STUDENT.getText().equals(detailMap.get("type"))) {
                                List<LinkedHashMap> submitValue = (List<LinkedHashMap>) detailMap.get("submitValue");
                                List<LinkedHashMap> resultMap = fillStudent(submitValue, dto.getStudentId());
                                if (CollUtil.isNotEmpty(resultMap)) {
                                    // 设置为新的值
                                    detailMap.put("submitValue", resultMap);
                                    isStudent = true;
                                }
                            }
                        }
                        if (isStudent) {
                            submitListNew.add(d);
                        }
                    });
                    submitInfoVO.setSubmitList(submitListNew);
                }
            }
        }
        return getEvaluateInfoDetailVO;
    }

    private String getStudentName(String studentId) {
        String studentName = "";
        EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
        eduStudentPageQueryDTO.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
        eduStudentPageQueryDTO.setStudentIds(Collections.singletonList(Convert.toLong(studentId)));
        List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
        if (CollUtil.isNotEmpty(eduStudentInfoVOS)) {
            studentName = eduStudentInfoVOS.get(0).getStudentName();
        }
        return studentName;
    }

    @Override
    public Boolean checkAuth(String studentId, Date submitTime) {
        if (submitTime == null) {
            submitTime = new Date();
        }
        Long studentIdL = Convert.toLong(studentId);
        //查询学生信息
        StudentDTO studentByIdQuery = new StudentDTO();
        studentByIdQuery.setStudentIds(Collections.singletonList(Convert.toLong(studentId)));
        List<com.hailiang.saas.model.vo.StudentInfoVO> studentInfoVOList = saasStaffCacheManager.studentDetailV2(
                studentByIdQuery);
        if (CollUtil.isNotEmpty(studentInfoVOList)) {
            Long campusSectionId = studentInfoVOList.get(Constant.ZERO).getCampusSectionId();
            Map<Long, SchoolYearVo> lastSchoolYearMap = cacheSaasManager.getLastSchoolYear();
            if (lastSchoolYearMap.containsKey(campusSectionId)) {
                SchoolYearVo schoolYearVo = lastSchoolYearMap.get(campusSectionId);
                // 历史学年
                if (schoolYearVo != null
                        && DateUtil.parseDate(schoolYearVo.getEndTime()).getTime() >= submitTime.getTime()) {
                    List<Long> studentIdList = saasStaffCacheManager.queryStudentByStaffId(schoolYearVo.getSchoolYear(),
                            campusSectionId);
                    if (CollUtil.isEmpty(studentIdList) || !studentIdList.contains(studentIdL)) {
                        log.info("[老师权限]，当前老师下无学生信息，无权限");
                        return Boolean.FALSE;
                    }
                    return Boolean.TRUE;
                }
            }
        }
        // 查询当前老师下有多少学生
        List<Long> teacherStudentIds = saasStaffCacheManager.queryStudentByStaffId(WebUtil.getSchoolIdLong(),
                WebUtil.getStaffIdLong());
        // 判断是否在里面
        if (CollUtil.isEmpty(teacherStudentIds)) {
            log.info("[老师权限]，当前老师下无学生信息，无权限");
            return Boolean.FALSE;
        }
        if (teacherStudentIds.contains(studentIdL)) {
            log.info("[老师权限]，当前老师下有该学生，有权限");
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private List<LinkedHashMap> fillStudent(List<LinkedHashMap> submitValueList, String studentId) {
        if (CollUtil.isEmpty(submitValueList)) {
            return Collections.emptyList();
        }
        List<Long> studentOrgIds = cacheSaasManager.listRelationInfo(studentId);
        //去重
        List<String> orgIds = studentOrgIds.stream().map(Convert::toStr).distinct().collect(Collectors.toList());
        //加上当前学生id
        orgIds.add(studentId);

        List<LinkedHashMap> submitValues = submitValueList.stream()
                .filter(linkedHashMap -> orgIds.contains(linkedHashMap.get("id"))).collect(Collectors.toList());

        Set<String> nameSet = new HashSet<>();
        List<LinkedHashMap> newSubmitValues = new ArrayList<>();
        if (CollUtil.isNotEmpty(submitValues)) {
            for (LinkedHashMap d : submitValues) {
                String name = getStudentName(studentId);
                if (!nameSet.contains(name)) {
                    nameSet.add(name);
                    d.put("name", name);
                    newSubmitValues.add(d);
                }
            }
        }

        return newSubmitValues;
    }

    @Override
    @RLock(name = "saveParentEvaluateInfo", keys = {"#dto.targetId"}, waitTime = 10, leaseTime = 5)
    public Info saveParentEvaluateInfo(InfoSaveDTO dto) {
        // 参数判断
        Assert.isTrue(dto.getTargetId() != null,
                () -> new BizException(BizExceptionEnum.TARGET_ID_NOT_NULL.getMessage()));
        Assert.isTrue(StrUtil.isNotBlank(dto.getTaskName()),
                () -> new BizException(BizExceptionEnum.TASK_NAME_NOT_NULL.getMessage()));
        Assert.isTrue(CollUtil.isNotEmpty(dto.getSubmitInfoList()),
                () -> new BizException(BizExceptionEnum.SUBMIT_NOT_NULL.getMessage()));
        // 防止重复提交，如果taskId不为空 缓存 并校验
        if (ObjectUtil.isNotEmpty(dto.getTaskId())) {
            checkTaskId(dto.getTaskId());
        }
        Target target = targetLogic.getById(dto.getTargetId());
        this.checkTargetValid(target);

        dto.setSubmitStaffId(WebUtil.getStudentIdStr());
        // 前端提交参数
        List<SubmitInfoSaveDTO> submitInfoList = dto.getSubmitInfoList();
        // 判断模版格式
        infoHelper.judgeTemplate(infoConvert.toTemplateInfoSaves(submitInfoList));
        // 学生列表
        List<StudentInfo> totalStudentInfoList = new ArrayList<>();
        // 分数和学生的映射
        List<Map<String, List>> infoList = new ArrayList<>();
        // 填充分数
        infoHelper.fullFillDatas(totalStudentInfoList, infoList, dto.getTaskName(), submitInfoList);
        Date now = DateUtil.date();
        // 完成任务(家长)
        Long taskId = infoHelper.parentFinishTask(dto, now, dto.getTargetId(), TaskRoleTypeEnum.PARENT.getCode());
        // 图片视频存相对路径
        infoHelper.transferImgForSave(dto);

        // 保存表单
        Info evaluateInfoSave = infoConvert.toEntity(dto);
        evaluateInfoSave
                .setTenantId(WebUtil.getTenantId())
                .setSchoolId(WebUtil.getSchoolId())
                .setCampusId(WebUtil.getCampusId())
                .setTargetId(dto.getTargetId())
                .setTaskId(taskId)
                .setSubmitTime(now)
                .setStudentInfos(totalStudentInfoList);

        Query query = new Query(Criteria.where("taskId").in(CollUtil.newArrayList(taskId)).and("deleted").is(0));
        List<Info> infos = mongoTemplate.find(query, Info.class);
        String evaluateInfoId = "";
        //重新提交修改mongodb
        if (CollUtil.isNotEmpty(infos)) {
            String infoId = infos.get(0).getId();
            evaluateInfoSave.setId(infoId);
            evaluateInfoId = infoId;
            infoDao.update(evaluateInfoSave);
        } else {
            //第一次提交新增数据
            evaluateInfoId = infoDao.save(evaluateInfoSave).getId();
        }

        //发送
        TaskPO taskPO = taskLogic.getById(taskId);
        // 查询学生信息（发送消息前置数据准备）
        StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(taskPO.getSchoolId()),
                Convert.toLong(taskPO.getSubmitStaffId()));

        // 审核人开关关闭，无需审核
        if (Constant.NO.equals(target.getParentSubmitReviewFlag())) {
            // 家长提交无需审核后续处理
            this.dealAfterParentSubmit(evaluateInfoId, taskPO, studentVO,TaskRoleTypeEnum.PARENT.getCode());
            return evaluateInfoSave;
        }

        // 获取指标审核人
        List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS = reviewTeacherManager.listByTargetId(dto.getTargetId(),1);

        if (ObjectUtil.isNotEmpty(studentVO)) {
            // 默认班主任
            if (CollUtil.isEmpty(reviewTeacherPOS)) {
                Long reviewTeacherId = studentVO.getHeaderMasterId();
                String reviewTeacherIdMobile = studentVO.getHeaderMasterMobile();
                if (ObjectUtil.isNull(reviewTeacherId) || CharSequenceUtil.isBlank(reviewTeacherIdMobile)) {
                    log.warn("【家长提交点评】发送消息给审核人，班主任不存在，studentId：{}", taskPO.getSubmitStaffId());
                    throw new BizException("班主任不存在，请联系老师");
                }

                infoHelper.sendDingDingMsg(taskPO, evaluateInfoId, reviewTeacherId, reviewTeacherIdMobile, studentVO);
                infoHelper.sendFeiShuMsg(taskPO, evaluateInfoId, reviewTeacherId, reviewTeacherIdMobile, studentVO);
                infoHelper.sendWechatMsg(taskPO, evaluateInfoId, studentVO, reviewTeacherId, reviewTeacherIdMobile);
                infoHelper.sendTeacherMobileMsg(taskPO, evaluateInfoId, studentVO, reviewTeacherId,
                        reviewTeacherIdMobile);
            } else {
                // 发送消息给审核人
                this.sendReviewTeacherMsg(reviewTeacherPOS, studentVO, taskPO, evaluateInfoId, false,
                        TaskRoleTypeEnum.PARENT.getCode());
            }
        } else {
            log.warn("【发送家长提交老师审核消息】学生信息为空，无需发送消息");
        }
        // 保存提交操作日志
        TaskOperateLogSaveDTO saveDTO = new TaskOperateLogSaveDTO().setTenantId(taskPO.getTenantId())
                .setSchoolId(taskPO.getSchoolId()).setCampusId(taskPO.getCampusId()).setInfoId(evaluateInfoId)
                .setRoleType(TaskRoleTypeEnum.PARENT.getCode()).setOperateType(OperateTypeEnum.SUBMIT.getCode())
                .setApprovalStatus(TaskApprovalEnum.APPROVAL_ING.getCode()).setOperateTime(now)
                .setCreateBy(WebUtil.getStudentIdStr()).setOperatorId(WebUtil.getStudentIdStr());
        taskOperateLogService.saveEvaluateTaskOperateLog(saveDTO);
        return evaluateInfoSave;
    }

    private void checkTargetValid(Target target) {
        Assert.isTrue(ObjectUtil.isNotEmpty(target), () -> new BizException("指标已被删除，无需提交"));
        Assert.isTrue(target.getTargetStatus() == 1, () -> new BizException("指标已被关闭，无需提交"));
    }


    /**
     * 家长提交无需审核后续处理
     */
    private void dealAfterParentSubmit(String evaluateInfoId, TaskPO taskPO, StudentVO studentVO, Integer roleType) {
        // 处理行为记录
        List<BehaviourRecord> records = new ArrayList<>();
        BehaviourRecordHandleDTO behaviourRecordHandleDTO = new BehaviourRecordHandleDTO();
        records = this.approvalPassed(behaviourRecordHandleDTO, evaluateInfoId, taskPO, roleType, null);

        // 更新审核任务状态
        taskPO.setApprovalStatus(TaskApprovalEnum.APPROVAL_PASS.getCode());
        taskPO.setApprovalUserId(Convert.toStr(studentVO.getStudentId()));
        taskService.updateById(taskPO);

        // 新增修改操作日志
        TaskOperateLogSaveDTO saveDTO = new TaskOperateLogSaveDTO().setTenantId(WebUtil.getTenantId())
                .setSchoolId(WebUtil.getSchoolId()).setCampusId(WebUtil.getCampusId()).setInfoId(evaluateInfoId)
                .setApprovalReason("无需老师审核").setRoleType(TaskRoleTypeEnum.TEACHER.getCode())
                .setOperateType(OperateTypeEnum.APPROVAL.getCode()).setOperatorId(
                        Convert.toStr(studentVO.getStudentId()))
                .setApprovalStatus(TaskApprovalEnum.APPROVAL_PASS.getCode()).setOperateTime(DateUtil.date());
        taskOperateLogService.saveEvaluateTaskOperateLog(saveDTO);

        // 发送消息到mq
        if (CollUtil.isNotEmpty(records)) {
            convertInitialService.sendMQ(PointMqBusinessTypeEnum.ADD.getCode(),
                    PointMqDataOperateTypeEnum.ADD.getCode(), records, null);
            // 发送积分金币转换mq
            convertInitialService.sendBehaviourExchangeMq(
                    this.assemblyPointExchangeMq(records, -1L,
                            CoinExchangeOperateTypeEnum.CREATE.getCode(),
                            CoinExchangeStrategyEnum.GRAPHIC_COMMENT_PARENT.getCode()));
            BehaviourRecord behaviourRecord = records.get(Constant.ZERO);
            TermVo termVo = termLogic.getTermVoByCampusIdCache(WebUtil.getSchoolIdLong(), WebUtil.getCampusIdLong(),
                    Convert.toLong(behaviourRecord.getCampusSectionId()), behaviourRecord.getSubmitTime());
            if (!Objects.equals(Boolean.TRUE,
                    Optional.ofNullable(termVo).map(TermVo::getIsCurrentTerm).orElse(Boolean.FALSE))) {
                // 综合成绩
                this.revaluate(behaviourRecord.getCampusSectionId(), behaviourRecord.getGradeId(),
                        termVo.getSchoolYear(), termVo.getTermName(), behaviourRecord.getSubmitTime(),
                        RevaluateBusinessTypeEnum.REPORT_EXAM.getCode());
            }
            if (!DateUtil.isSameDay(DateUtil.date(), behaviourRecord.getSubmitTime())) {
                // 今日之前的T+1数据变更重复重新计算
                this.revaluate(behaviourRecord.getCampusSectionId(), behaviourRecord.getGradeId(),
                        termVo.getSchoolYear(), termVo.getTermName(), behaviourRecord.getSubmitTime(),
                        RevaluateBusinessTypeEnum.BEHAVIOUR_RECORD.getCode());
            }
        }

        // 行为记录变更触发下游争章活动规则
        this.handleActivity(behaviourRecordHandleDTO);
    }

    /**
     * 给审核人发送消息
     */
    private void sendReviewTeacherMsg(List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS,
                                      StudentVO studentVO, TaskPO taskPO,
                                      String evaluateInfoId,
                                      boolean isResubmit,
                                      Integer roleType) {
        if (CollUtil.isEmpty(reviewTeacherPOS) || ObjectUtil.isNull(studentVO)) {
            log.warn("【发送审核人消息】审核人信息为空，无需发送消息");
            return;
        }

        // 1. 数据准备
        List<Long> staffIds = new ArrayList<>();
        Map<Long, Long> subjectIdToStaffMap = new HashMap<>();

        // 2. 处理班级内的学科老师
        this.processClassTeachers(reviewTeacherPOS, studentVO, staffIds, subjectIdToStaffMap, roleType);

        // 3. 处理指定老师
        this.processAppointedTeachers(reviewTeacherPOS, staffIds);

        // 4. 处理班主任
        this.processHeadMaster(reviewTeacherPOS, staffIds, studentVO, subjectIdToStaffMap);
        //fix空指针
        staffIds = staffIds.stream().filter(Objects::nonNull).collect(Collectors.toList());

        // 5. 批量获取员工信息, 过滤离职老师
        List<com.hailiang.saas.model.vo.staff.StaffBatchVO> staffBatchVOS = CollUtil.isNotEmpty(staffIds) ?
                saasStaffManager.queryStaffByIds(staffIds, 0) :
                Collections.emptyList();
        if (CollUtil.isEmpty(staffBatchVOS)) {
            log.warn("【家长提交发送审核人消息】审核人信息为空，将发送给班主任审核，staffIds:{}", staffIds);
            Long reviewTeacherId = studentVO.getHeaderMasterId();
            String reviewTeacherIdMobile = studentVO.getHeaderMasterMobile();
            if (ObjectUtil.isNull(reviewTeacherId) || CharSequenceUtil.isBlank(reviewTeacherIdMobile)) {
                log.warn("【家长提交发送审核人消息】审核人和班主任不存在，发送消息失败，staffIds:{}", staffIds);
                throw new BizException("审核人和班主任不存在，请联系老师");
            }

            // 审核人离职，将消息推给班主任审核
            infoHelper.sendDingDingMsg(taskPO, evaluateInfoId, reviewTeacherId, reviewTeacherIdMobile, studentVO);
            infoHelper.sendFeiShuMsg(taskPO, evaluateInfoId, reviewTeacherId, reviewTeacherIdMobile, studentVO);
            infoHelper.sendWechatMsg(taskPO, evaluateInfoId, studentVO, reviewTeacherId, reviewTeacherIdMobile);
            infoHelper.sendTeacherMobileMsg(taskPO, evaluateInfoId, studentVO, reviewTeacherId, reviewTeacherIdMobile);
        }

        // 6. 异步发送消息
        CompletableFuture.runAsync(
                        () -> this.sendOrResendMessage(reviewTeacherPOS, studentVO, taskPO, evaluateInfoId, isResubmit,
                                staffBatchVOS, subjectIdToStaffMap),
                        evaluateExecutor)
                .exceptionally(ex -> {
                    log.error(
                            "【家长提交点评】发送消息给审核人-reviewTeacherPOS: 【{}】，studentVO: 【{}】，taskPO：【{}】，evaluateInfoId: 【{}】",
                            JSONUtil.toJsonStr(reviewTeacherPOS), JSONUtil.toJsonStr(studentVO),
                            JSONUtil.toJsonStr(taskPO), evaluateInfoId, ex);
                    return null;
                });
    }

    private void sendOrResendMessage(List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS, StudentVO studentVO,
                                     TaskPO taskPO,
                                     String evaluateInfoId, boolean isResubmit,
                                     List<com.hailiang.saas.model.vo.staff.StaffBatchVO> staffBatchVOS,
                                     Map<Long, Long> subjectIdToStaffMap) {
        // 6. 发送消息
        if (!isResubmit) {
            // 审核人
            List<Long> reviewTeacherIds = Lists.newCopyOnWriteArrayList();
            // 首次提交
            reviewTeacherPOS.forEach(
                    po -> this.sendMessages(po, staffBatchVOS, subjectIdToStaffMap, taskPO, studentVO, evaluateInfoId,
                            reviewTeacherIds));

            String reviewTeacherId = JSONUtil.toJsonStr(reviewTeacherIds);

            // 审核任务添加审核人
            taskPO.setReviewTeacherId(Convert.toStr(reviewTeacherId));
            taskLogic.updateById(taskPO);
            return;
        }
        // 重新提交
        reviewTeacherPOS.forEach(
                po -> this.reSendMessages(po, staffBatchVOS, subjectIdToStaffMap, taskPO, studentVO, evaluateInfoId)
        );
    }

    /**
     * 处理班主任
     */
    private void processHeadMaster(List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS,
                                   List<Long> staffIds, StudentVO studentVO,
                                   Map<Long, Long> subjectIdToStaffMap) {
        if (CollUtil.isEmpty(reviewTeacherPOS) || ObjectUtil.isNull(studentVO)) {
            log.warn("【发送审核人消息】班主任或学生信息为空，无需发送消息，reviewTeacherPOS:{},studentVO:{}",
                    reviewTeacherPOS, studentVO);
            return;
        }

        // 添加班主任staffId
        reviewTeacherPOS.stream()
                .filter(po -> null != po && TargetReviewTypeEnum.HEADER_MASTER.getCode().equals(po.getReviewType()))
                .findFirst()
                .ifPresent(po -> {
                    Long reviewTeacherId = studentVO.getHeaderMasterId();
                    staffIds.add(reviewTeacherId);
                    subjectIdToStaffMap.put(-1L, reviewTeacherId);
                });

    }


    /**
     * 处理班级内的学科老师逻辑
     */
    private void processClassTeachers(List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS,
                                      StudentVO studentVO,
                                      List<Long> staffIds,
                                      Map<Long, Long> subjectIdToStaffMap,
                                      Integer roleType) {
        if (CollUtil.isEmpty(reviewTeacherPOS) || ObjectUtil.isNull(studentVO)) {
            log.warn("【发送审核人消息】班级内的学科老师或学生信息为空，无需发送消息，reviewTeacherPOS:{},studentVO:{}",
                    reviewTeacherPOS, studentVO);
            return;
        }

        // 班级内的学科老师对应的 businessId 是课程 id
        List<Long> subjectIds = reviewTeacherPOS
                .stream()
                .filter(po -> TargetReviewTypeEnum.CLASS_TEACHER.getCode().equals(po.getReviewType()))
                .map(EvaluateTargetReviewTeacherPO::getBusinessId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(subjectIds)) {
            return;
        }
        List<Long> classIds = CollUtil.newArrayList(studentVO.getClassId());
        // 如果是学生提交审核,需要查询走班信息
        if (TaskRoleTypeEnum.STUDENT.getCode().equals(roleType) && CollUtil.isNotEmpty(studentVO.getAllClassIds())) {
            classIds.addAll(studentVO.getAllClassIds());
        }

        // 获取班级内学科老师信息，通过课程id对比获取对应课程老师 staffId
        Optional.ofNullable(saasClassManager.queryClassTeacherInfo(classIds))
                .filter(CollUtil::isNotEmpty)
                .map(list -> list.get(0))
                .map(ClassTeacherInfoVO::getTeacherList)
                .ifPresent(teachers -> teachers.forEach(teacher ->
                        Optional.ofNullable(teacher.getSubjectList()).orElse(Collections.emptyList())
                                .stream()
                                .filter(subject -> subject != null && subjectIds.contains(subject.getSubjectId()))
                                .forEach(subject -> {
                                    subjectIdToStaffMap.put(subject.getSubjectId(), teacher.getStaffId());
                                    staffIds.add(teacher.getStaffId());
                                })
                ));
    }


    /**
     * 处理指定老师逻辑
     */
    private void processAppointedTeachers(List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS,
                                          List<Long> staffIds) {
        Optional.ofNullable(reviewTeacherPOS).orElse(Collections.emptyList())
                .stream()
                .filter(po -> null != po && TargetReviewTypeEnum.APPOINTED_TEACHER.getCode().equals(po.getReviewType()))
                .map(EvaluateTargetReviewTeacherPO::getBusinessId)
                .filter(Objects::nonNull)
                .forEach(staffIds::add);
    }

    /**
     * 发送消息逻辑
     */
    private void sendMessages(EvaluateTargetReviewTeacherPO po,
                              List<com.hailiang.saas.model.vo.staff.StaffBatchVO> staffBatchVOS,
                              Map<Long, Long> subjectIdToStaffMap,
                              TaskPO taskPO,
                              StudentVO studentVO,
                              String evaluateInfoId,
                              List<Long> reviewTeacherIds) {
        Long staffId = this.getStaffId(po, subjectIdToStaffMap);
        String mobile = this.getStaffMobile(staffId, staffBatchVOS);

        if (Objects.isNull(staffId) || Objects.isNull(mobile)) {
            return;
        }
        reviewTeacherIds.add(staffId);

        infoHelper.sendDingDingMsg(taskPO, evaluateInfoId, staffId, mobile, studentVO);
        infoHelper.sendFeiShuMsg(taskPO, evaluateInfoId, staffId, mobile, studentVO);
        infoHelper.sendWechatMsg(taskPO, evaluateInfoId, studentVO, staffId, mobile);
        infoHelper.sendTeacherMobileMsg(taskPO, evaluateInfoId, studentVO, staffId, mobile);
    }

    /**
     * 重新提交发送消息逻辑
     */
    private void reSendMessages(EvaluateTargetReviewTeacherPO po,
                                List<com.hailiang.saas.model.vo.staff.StaffBatchVO> staffBatchVOS,
                                Map<Long, Long> subjectIdToStaffMap,
                                TaskPO taskPO,
                                StudentVO studentVO,
                                String evaluateInfoId) {
        Long staffId = this.getStaffId(po, subjectIdToStaffMap);
        String mobile = this.getStaffMobile(staffId, staffBatchVOS);

        if (Objects.isNull(staffId) || Objects.isNull(mobile)) {
            return;
        }

        // 发送老师审核钉钉通知
        infoHelper.sendResubmitDingDingMsg(taskPO, evaluateInfoId, staffId, mobile, studentVO);
        // 发送老师审核飞书通知
        infoHelper.sendResubmitFeiShuMsg(taskPO, evaluateInfoId, staffId, mobile, studentVO);
        // 发送老师审核-微信通知
        infoHelper.sendResubmitWechatMsg(taskPO, evaluateInfoId, studentVO, staffId, mobile);
        // 发送老师审核-教师移动端通知
        infoHelper.sendResubmitTeacherMobileMsg(taskPO, evaluateInfoId, studentVO, staffId, mobile);
    }

    /**
     * 获取staffId
     */
    private Long getStaffId(EvaluateTargetReviewTeacherPO po, Map<Long, Long> subjectIdToStaffMap) {
        if (TargetReviewTypeEnum.CLASS_TEACHER.getCode().equals(po.getReviewType())) {
            return subjectIdToStaffMap.getOrDefault(po.getBusinessId(), null);
        } else if (TargetReviewTypeEnum.APPOINTED_TEACHER.getCode().equals(po.getReviewType())) {
            return po.getBusinessId();
        } else if (TargetReviewTypeEnum.HEADER_MASTER.getCode().equals(po.getReviewType())) {
            return subjectIdToStaffMap.getOrDefault(-1L, null);
        } else {
            return null;
        }
    }

    /**
     * 获取老师手机号
     */
    private String getStaffMobile(Long staffId, List<com.hailiang.saas.model.vo.staff.StaffBatchVO> staffBatchVOS) {
        return staffBatchVOS.stream()
                .filter(staff -> null != staff && staff.getId().equals(staffId))
                .findFirst()
                .map(com.hailiang.saas.model.vo.staff.StaffBatchVO::getMobile)
                .orElse(null);
    }

    @Override
    public Boolean modifyParentEvaluateInfo(InfoModifyDTO dto) {
        // 参数校验
        Assert.isTrue(StrUtil.isNotBlank(dto.getSubmitInfoId()),
                () -> new BizException(BizExceptionEnum.DETAIL_ID_NOT_NULL.getMessage()));
        Assert.isTrue(CollUtil.isNotEmpty(dto.getSubmitInfoList()),
                () -> new BizException(BizExceptionEnum.DETAIL_CONTENT_NOT_NULL.getMessage()));

        Info info = infoLogic.getById(dto.getSubmitInfoId());
        Assert.isTrue(info != null, () -> new BizException(BizExceptionEnum.DETAIL_NOT_NULL.getMessage()));

        // 修改任务更新时间
        TaskPO taskPOUpdate = new TaskPO();
        taskPOUpdate.setUpdateBy(WebUtil.getStudentIdStr());
        taskPOUpdate.setUpdateTime(new Date());
        taskPOUpdate.setUpdateTime(DateUtil.date());
        taskPOUpdate.setId(info.getTaskId());
        taskPOUpdate.setApprovalStatus(TaskApprovalEnum.APPROVAL_ING.getCode());
        taskLogic.updateById(taskPOUpdate);

        // 判断模版格式
        infoHelper.judgeTemplate(infoConvert.toTemplates(dto.getSubmitInfoList()));

        // 分数和学生的映射
        List<Map<String, List>> infoList = new ArrayList<>();
        // 所有学生列表
        List<StudentInfo> totalStudentInfoList = new ArrayList<>();
        // 填充分数
        infoHelper.fullFillDatas(totalStudentInfoList, infoList, info.getTaskName(),
                infoConvert.toTemplateInfoModifys(dto.getSubmitInfoList()));

        // 更新填写详情
        // 图片视频存相对路径
        infoHelper.transferImgForUpdate(dto.getSubmitInfoList());
        List<SubmitInfo> submitInfos = infoConvert.toEvaluateInfos(dto.getSubmitInfoList());
        Info update = new Info();
        update.setStudentInfos(totalStudentInfoList).setSubmitInfoList(submitInfos).setId(dto.getSubmitInfoId());
        infoDao.update(update);

        //发送老师审核钉钉通知 todo
        TaskPO taskPO = taskLogic.getById(info.getTaskId());
        // 查询学生信息（发送消息前置数据准备）
        StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(taskPO.getSchoolId()),
                Convert.toLong(taskPO.getSubmitStaffId()));

        // 获取指标审核人
        List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS = reviewTeacherManager.listByTargetId(dto.getTargetId(), 1);

        if (ObjectUtil.isNotEmpty(studentVO)) {
            // 默认班主任
            if (CollUtil.isEmpty(reviewTeacherPOS)) {
                Long reviewTeacherId = studentVO.getHeaderMasterId();
                String reviewTeacherIdMobile = studentVO.getHeaderMasterMobile();
                if (ObjectUtil.isNull(reviewTeacherId) || CharSequenceUtil.isBlank(reviewTeacherIdMobile)) {
                    log.warn("【家长重新提交点评】发送消息给审核人，班主任不存在，studentId：{}", taskPO.getSubmitStaffId());
                    throw new BizException("班主任不存在，请联系老师");
                }
                infoHelper.sendResubmitDingDingMsg(taskPO, info.getId(), reviewTeacherId, reviewTeacherIdMobile,
                        studentVO);
                //发送老师审核飞书通知
                infoHelper.sendResubmitFeiShuMsg(taskPO, info.getId(), reviewTeacherId, reviewTeacherIdMobile,
                        studentVO);
                // 发送老师审核-微信通知
                infoHelper.sendResubmitWechatMsg(taskPO, info.getId(), studentVO, reviewTeacherId,
                        reviewTeacherIdMobile);
                // 发送老师审核-教师移动端通知
                infoHelper.sendResubmitTeacherMobileMsg(taskPO, info.getId(), studentVO, reviewTeacherId,
                        reviewTeacherIdMobile);
            } else {
                // 发送消息给审核人
                this.sendReviewTeacherMsg(reviewTeacherPOS, studentVO, taskPO, info.getId(), true,
                        TaskRoleTypeEnum.PARENT.getCode());
            }
        } else {
            log.warn("【发送家长重新提交老师审核消息】学生信息为空，无需发送消息");
        }

        // 新增修改操作日志
        TaskOperateLogSaveDTO saveDTO = new TaskOperateLogSaveDTO()
                .setTenantId(taskPO.getTenantId())
                .setSchoolId(taskPO.getSchoolId())
                .setCampusId(taskPO.getCampusId())
                .setInfoId(dto.getSubmitInfoId())
                .setRoleType(TaskRoleTypeEnum.PARENT.getCode())
                .setOperateType(OperateTypeEnum.MODIFY.getCode())
                .setApprovalStatus(TaskApprovalEnum.APPROVAL_ING.getCode())
                .setCreateBy(WebUtil.getStudentIdStr())
                .setOperateTime(DateUtil.date())
                .setOperatorId(WebUtil.getStudentIdStr());

        return taskOperateLogService.saveEvaluateTaskOperateLog(saveDTO);
    }

    @Override
    @Transactional
    public Boolean removeParentEvaluateInfo(InfoRemoveDTO dto) {
        BehaviourRecordHandleDTO behaviourRecordHandleDTO = new BehaviourRecordHandleDTO();
        deleteParentEvaluateInfo(dto, behaviourRecordHandleDTO);
        this.handleActivity(behaviourRecordHandleDTO);
        return Boolean.TRUE;
    }

    /**
     * 家长删除指标记录
     *
     * @param dto
     * @param behaviourRecordHandleDTO
     */
    public void deleteParentEvaluateInfo(InfoRemoveDTO dto, BehaviourRecordHandleDTO behaviourRecordHandleDTO) {
        Assert.isTrue(StrUtil.isNotBlank(dto.getSubmitInfoId()),
                () -> new BizException(BizExceptionEnum.DETAIL_ID_NOT_NULL.getMessage()));

        Info info = infoLogic.getById(dto.getSubmitInfoId());
        Assert.notNull(info, () -> new BizException(BizExceptionEnum.DETAIL_NOT_NULL.getMessage()));
        taskLogic.removeById(info.getTaskId());

        //删除学生画像数据
        taskLogic.remove(new LambdaQueryWrapper<TaskPO>().eq(TaskPO::getId, info.getTaskId()));
        infoDao.deleteById(dto.getSubmitInfoId());

        // 删除行为记录
        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.listByInfoIdV2(info.getId());
        if (CollUtil.isNotEmpty(behaviourRecords)) {
            List<Long> behaviourIds = behaviourRecords.stream().map(BehaviourRecord::getId)
                    .collect(Collectors.toList());
            behaviourRecordManager.removeBatchByIds(behaviourIds);
            // 发送消息到mq(行为记录变动)
            convertInitialService.sendMQ(3, 3, behaviourRecords, null);
            // 发送积分金币转换mq
            convertInitialService.sendBehaviourExchangeMq(
                    this.assemblyPointExchangeMq(behaviourRecords, Convert.toLong(dto.getPlanId()),
                            CoinExchangeOperateTypeEnum.DELETE.getCode(),
                            CoinExchangeStrategyEnum.GRAPHIC_COMMENT_PARENT.getCode()));

            // 删除行为记录需要匹配活动
            behaviourRecordHandleDTO.setDeletedBehaviourRecordIds(behaviourIds);
        }

        //点评项增删改打标
        Set<String> sectionSet = new HashSet<>();
        //删除的年级
        if (CollUtil.isNotEmpty(behaviourRecords)) {
            addGradeRedis(behaviourRecords, sectionSet);
        }
        //新增和删除的学段
        if (CollUtil.isNotEmpty(sectionSet)) {
            addSectionRedis(sectionSet);
        }
    }

    @MethodExecuteTime
    @TLogAspect(convert = CurrentStaffConvert.class)
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RLock(name = "approvalParentEvaluateInfo", keys = {"#dto.infoId"}, waitTime = 10, leaseTime = 5)
    public void approvalParentEvaluateInfo(ApprovalHandleDTO dto) {
        // 前置校验
        TaskPO task = this.checkApprovalAuth(dto.getInfoId());

        // 审核通过处理行为记录
        List<BehaviourRecord> records = new ArrayList<>();
        BehaviourRecordHandleDTO behaviourRecordHandleDTO = new BehaviourRecordHandleDTO();
        if (ApprovalOperateEnum.APPROVAL_PASS.getCode().equals(dto.getApprovalType())) {
            records = this.approvalPassed(behaviourRecordHandleDTO, dto.getInfoId(), task, TaskRoleTypeEnum.PARENT.getCode(), null);
        }

        // 修改审核任务状态
        this.updateTask(task, dto);

        // 审核后的消息处理
        this.handleMessage(dto, task, records, TaskUserTypeEnum.PARENT.getCode());

        // 行为记录变更触发下游争章活动规则
        this.handleActivity(behaviourRecordHandleDTO);
    }


    /**
     * 审核-前置权限校验
     *
     * @param infoId
     * @return
     */
    private TaskPO checkApprovalAuth(String infoId) {
        AssertUtil.checkNotNull(infoId, "infoId不能为空");
        // 填写详情
        Info evaluateInfo = infoLogic.getById(infoId);
        AssertUtil.checkNotNull(evaluateInfo, "获取点评详情失败");
        TaskPO taskPO = taskService.getById(evaluateInfo.getTaskId());
        AssertUtil.checkNotNull(taskPO, "获取审核任务情失败");

        Assert.isTrue(TaskApprovalEnum.APPROVAL_ING.getCode().equals(taskPO.getApprovalStatus()),
                () -> new BizException("审核失败,数据已审核"));

        // 权限控制
        String staffId = WebUtil.getStaffId();
        if (CharSequenceUtil.isBlank(staffId)) {
            return taskPO;
        }
        log.info("[查看详情]-校验权限，staffId:{}", staffId);
        String reviewTeacherId = taskPO.getReviewTeacherId();
        if (CharSequenceUtil.isBlank(reviewTeacherId)) {
            // 如果审核人为空，说明是历史数据，默认是班主任，则需要判断是否是当前班主任
            if (Boolean.FALSE.equals(checkAuth(taskPO.getSubmitStaffId(), taskPO.getSubmitTime()))) {
                throw new BizException("你已不是该学生班主任，无需审核。");
            }
            return taskPO;
        }
        if (!reviewTeacherId.contains(staffId)) {
            throw new BizException("你不是该指标审核人，无需审核。");
        }

        return taskPO;
    }

    /**
     * 审核通过
     *
     * @param behaviourRecordHandleDTO
     * @param infoId
     * @param taskPO
     * @return
     */
    private List<BehaviourRecord> approvalPassed(BehaviourRecordHandleDTO behaviourRecordHandleDTO, String infoId,
                                                 TaskPO taskPO,
                                                 Integer roleType,
                                                 List<SubmitInfoModifyDTO> submitInfoList) {
        // 指标需要老师审核同意后才生成
        Info info = infoDao.findById(infoId);
        // 学生提交表单老师审核修改表单情况
        info = this.updateSubmitInfo(info,submitInfoList,roleType);

        // 获取学生班级信息
        UcStudentClassBffVO saasStudent = this.getSaasStudentInfo(taskPO.getSubmitStaffId());

        // 分数和学生的映射
        List<Map<String, List>> infoList = new ArrayList<>();
        // 所有学生列表
        List<StudentInfo> totalStudentInfoList = new ArrayList<>();
        // 填充分数
        infoHelper.fullFillDatas(totalStudentInfoList, infoList, info.getTaskName(),
                infoConvert.toTheTemplateInfoModifys(info.getSubmitInfoList()));
        // 保存统计数据
        // 填充统计数据
        List<BehaviourRecordSaveDTO> behaviourRecordSaves = infoHelper.generateRecords(infoList, new Date(),
                info.getTaskId(), info.getId(), info.getTargetId());
        // 提前获取当前填报的班级id
        Long classId = Convert.toLong(CollUtil.getFirst(behaviourRecordSaves).getClassId());

        // 如果是学生提交走班下的学生,获取填报学生的走班信息
        Map<Long,Long> studentXzClassIdMap =  this.getStudentXzClassIdMap(behaviourRecordSaves,roleType);

        // 填充评价人信息(学生)
        behaviourRecordSaves.forEach(s -> {
            if (Objects.nonNull(saasStudent)) {
                s.setAppraisalType(roleType);
                s.setAppraisalId(Convert.toStr(saasStudent.getStudentId()));
                s.setAppraisalName(saasStudent.getStudentName());
            }
            // 学生填报行点评挂政班处理
            if (ObjectUtil.equal(TaskUserTypeEnum.STUDENT.getCode(), roleType)
                    &&(Objects.nonNull(studentXzClassIdMap.get(Convert.toLong(s.getStudentId()))))){
                s.setClassId(Convert.toStr(studentXzClassIdMap.get(Convert.toLong(s.getStudentId()))));
            }
        });

        // 获取提交表单中学科控件中的课程code集合
        List<String> courseCodes = this.getSubmitSubjectValue(info);
        List<BehaviourRecord> records = convert.toBehaviourRecords(behaviourRecordSaves);
        if (CollUtil.isNotEmpty(behaviourRecordSaves)) {
            records.forEach(s->{
                if (CollUtil.isNotEmpty(courseCodes)){
                    s.setSubjectCode(CharSequenceUtil.join(StrPool.COMMA,courseCodes));
                }
            });
            behaviourRecordService.saveBatch(records);

            records.forEach(item -> item.setCreateBy(WebUtil.getStaffId()));
            // 同步积分系统
            pointsSendLogic.convertAndSendPoints(records);
        }
        // 新增行为记录需要匹配活动
        List<Long> behaviourIds = records.stream().filter(s -> Objects.nonNull(s.getId())).map(BehaviourRecord::getId)
                .distinct().collect(Collectors.toList());
        behaviourRecordHandleDTO.setAddBehaviourRecordIds(behaviourIds);

        // 点评项增删改打标
        Set<String> sectionSet = new HashSet<>();
        // 新增的年级
        if (CollUtil.isNotEmpty(records)) {
            addGradeRedis(records, sectionSet);
        }
        //新增和删除的学段
        if (CollUtil.isNotEmpty(sectionSet)) {
            addSectionRedis(sectionSet);
        }
        // 学生提交审核挂星动力积分板
        this.sendXdlPlan(records, info, roleType, classId);

        return records;
    }

    /**
     * 获取学生行政班信息map
     *
     * @param behaviourRecordSaves
     * @param roleType
     * @return
     */
    private Map<Long, Long> getStudentXzClassIdMap(List<BehaviourRecordSaveDTO> behaviourRecordSaves,
                                                   Integer roleType) {
        if (CollUtil.isEmpty(behaviourRecordSaves)){
            return Collections.emptyMap();
        }
        // 如果是学生提交走班下的学生,点评需要挂走班学生的行政班id
        if (!ObjectUtil.equal(TaskUserTypeEnum.STUDENT.getCode(), roleType)) {
            return Collections.emptyMap();
        }
        List<Long> studentIds = behaviourRecordSaves.stream().map(s -> Convert.toLong(s.getStudentId())).distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIds)){
            return Collections.emptyMap();
        }
        // 查询学生行政班信息
        List<UcStudentClassBffVO> saasStudents = basicInfoRemote.listByStudentIds(
                new StudentByIdQuery().setStudentIds(studentIds),SaasClassTypeEnum.XINGZHENG.getCode());
        if (CollUtil.isEmpty(saasStudents)){
            return Collections.emptyMap();
        }
        return saasStudents.stream()
                .filter(s->Objects.nonNull(s.getStudentId()))
                .filter(s->Objects.nonNull(s.getClassId()))
                .collect(Collectors.toMap(UcStudentClassBffVO::getStudentId,
                UcStudentClassBffVO::getClassId));
    }

    /**
     * 学生提交表单老师审核修改表单情况
     *
     * @param info
     * @param submitInfoList
     * @param roleType
     * @return
     */
    private Info updateSubmitInfo(Info info, List<SubmitInfoModifyDTO> submitInfoList, Integer roleType) {
        // 仅学生提交审核才挂能修改表单
        if (!ObjectUtil.equal(TaskUserTypeEnum.STUDENT.getCode(), roleType)) {
            return info;
        }
        if (CollUtil.isEmpty(submitInfoList)){
            return info;
        }
        // 判断模版格式
        infoHelper.judgeTemplate(infoConvert.toTemplates(submitInfoList));

        // 分数和学生的映射
        List<Map<String, List>> infoList = new ArrayList<>();
        // 所有学生列表
        List<StudentInfo> totalStudentInfoList = new ArrayList<>();
        // 填充分数
        infoHelper.fullFillDatas(totalStudentInfoList, infoList, info.getTaskName(),
                infoConvert.toTemplateInfoModifys(submitInfoList));

        // 更新填写详情
        // 图片视频存相对路径
        infoHelper.transferImgForUpdate(submitInfoList);
        List<SubmitInfo> submitInfos = infoConvert.toEvaluateInfos(submitInfoList);
        Info update = new Info();
        update.setStudentInfos(totalStudentInfoList).setSubmitInfoList(submitInfos).setId(info.getId());
        infoDao.update(update);
        return infoDao.findById(info.getId());
    }

    /**
     * 学生提交审核挂星动力积分板
     * @param records
     * @param info
     * @param roleType
     * @param classId
     */
    private void sendXdlPlan(List<BehaviourRecord> records, Info info, Integer roleType, Long classId) {
        // 仅学生提交审核才挂星动力积分板
        if (!ObjectUtil.equal(TaskUserTypeEnum.STUDENT.getCode(), roleType)) {
            return;
        }
        if (CollUtil.isEmpty(records)){
            return;
        }
        // 挂积分板
        List<Long> submitCourseIds = this.getSubmitCourseIds(info);
        if (CollUtil.isEmpty(submitCourseIds) && Objects.nonNull(WebUtil.getStaffIdLong())) {
            // 无课程的明细，挂审批人的第一个积分板（若无审批人则不挂）
            PlanListsRequest planTagListsRequest = new PlanListsRequest();
            planTagListsRequest.setStaffId(WebUtil.getStaffIdLong());
            planTagListsRequest.setSaasClassIds(CollUtil.newArrayList(classId));
            planTagListsRequest.setSaasSchoolId(WebUtil.getSchoolIdLong());
            List<PlanListsResponse> planListsResponses = internalDriveRemote.queryPlanLists(planTagListsRequest);
            planListsResponses = planListsResponses.stream()
                    .filter(s -> ObjectUtil.equal(s.getSaasClassId(), classId)).collect(Collectors.toList());
            List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS = this.convertThirdToXdlRecordMessageDTO(
                    records,
                    CollUtil.isNotEmpty(planListsResponses) ? CollUtil.getFirst(planListsResponses).getPlanId() : -1L,
                    null,
                    null);
            // 挂积分板的班级为填报表单时选择班级的id
            thirdToXdlRecordMessageDTOS.forEach(s -> s.setClassId(Convert.toStr(classId)));
            eventSendHelper.sendEventMQ(thirdToXdlRecordMessageDTOS, xwlAddRecord);
        } else {
            //  有课程的明细，挂科目对应的老师的第一个积分板（若科目无老师，挂其他）
            List<ClassSubjectTeacherVO> classSubjectTeachers = saasClassManager.queryClassSubjectTeacherInfo(
                    CollUtil.newArrayList(classId));

            if (CollUtil.isEmpty(classSubjectTeachers)) {
                return;
            }
            // 获取任教科目老师
            List<Long> subjectTeacherIds = classSubjectTeachers.stream()
                    .filter(s -> submitCourseIds.contains(s.getSubjectId()))
                    .map(ClassSubjectTeacherVO::getStaffId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(subjectTeacherIds)) {
                return;
            }
            for (Long subjectTeacherId : subjectTeacherIds) {
                // 获取课程老师对应的积分板
                PlanListsRequest planTagListsRequest = new PlanListsRequest();
                planTagListsRequest.setStaffId(subjectTeacherId);
                planTagListsRequest.setSaasClassIds(CollUtil.newArrayList(classId));
                planTagListsRequest.setSaasSchoolId(WebUtil.getSchoolIdLong());
                List<PlanListsResponse> planListsResponses = internalDriveRemote.queryPlanLists(planTagListsRequest);
                planListsResponses = planListsResponses.stream()
                        .filter(s -> ObjectUtil.equal(s.getSaasClassId(), classId)).collect(Collectors.toList());
                List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS = this.convertThirdToXdlRecordMessageDTO(
                        records,
                        CollUtil.isNotEmpty(planListsResponses) ? CollUtil.getFirst(planListsResponses).getPlanId()
                                : -1L,
                        null,
                        null);
                // 挂积分板的班级为填报表单时选择班级的id
                thirdToXdlRecordMessageDTOS.forEach(s -> s.setClassId(Convert.toStr(classId)));
                eventSendHelper.sendEventMQ(thirdToXdlRecordMessageDTOS, xwlAddRecord);
                log.info("挂积分板：{}", JSON.toJSONString(thirdToXdlRecordMessageDTOS));
            }
        }
    }

    /**
     * 获取学生信息
     *
     * @param studentId 学生id
     * @return
     */
    private UcStudentClassBffVO getSaasStudentInfo(String studentId) {
        List<UcStudentClassBffVO> saasStudents = basicInfoRemote.listByStudentIds(
                new StudentByIdQuery().setStudentIds(CollUtil.newArrayList(Convert.toLong(studentId))));
        if (CollUtil.isEmpty(saasStudents)) {
            return null;
        }
        return CollUtil.getFirst(saasStudents);
    }

    /**
     * 审核-修改任务状态
     *
     * @param taskPO
     * @param dto
     */
    private void updateTask(TaskPO taskPO, ApprovalHandleDTO dto) {
        // 更新审核任务状态
        taskPO.setApprovalStatus(ApprovalOperateEnum.APPROVAL_PASS.getCode().equals(dto.getApprovalType())
                ? TaskApprovalEnum.APPROVAL_PASS.getCode() : TaskApprovalEnum.APPROVAL_REFUSE.getCode());
        taskPO.setApprovalUserId(
                ApprovalOperateEnum.APPROVAL_PASS.getCode().equals(dto.getApprovalType()) ? WebUtil.getStaffId()
                        : null);
        taskService.updateById(taskPO);

        // 新增修改操作日志
        TaskOperateLogSaveDTO saveDTO = new TaskOperateLogSaveDTO().setTenantId(WebUtil.getTenantId())
                .setSchoolId(WebUtil.getSchoolId()).setCampusId(WebUtil.getCampusId()).setInfoId(dto.getInfoId())
                .setApprovalReason(dto.getApprovalReason()).setRoleType(TaskRoleTypeEnum.TEACHER.getCode())
                .setOperateType(OperateTypeEnum.APPROVAL.getCode()).setOperatorId(WebUtil.getStaffId())
                .setApprovalStatus(ApprovalOperateEnum.APPROVAL_PASS.getCode().equals(dto.getApprovalType())
                        ? TaskApprovalEnum.APPROVAL_PASS.getCode()
                        : TaskApprovalEnum.APPROVAL_REFUSE.getCode()).setOperateTime(DateUtil.date());
        taskOperateLogService.saveEvaluateTaskOperateLog(saveDTO);
    }

    /**
     * 审核-消息处理
     *
     * @param dto
     * @param taskPO
     * @param records
     */
    private void handleMessage(ApprovalHandleDTO dto, TaskPO taskPO, List<BehaviourRecord> records, Integer userType) {
        evaluateExecutor.execute(() -> {
            if (TaskUserTypeEnum.PARENT.getCode().equals(userType)){
                //发送飞书-钉钉消息-家长
                this.sendFeiShuAndDingApprovalMsg(taskPO, dto.getApprovalType(), dto.getInfoId());
            }
            //发送微信消息
            this.sendWXApprovalMsg(taskPO, dto.getApprovalType(), dto.getInfoId());
            //更新钉钉消息状态为已审核
            dingDingMsgUtil.updateMessageStatusToApproval(taskPO.getId());
            //更新飞书消息状态为已审核
            feiShuMsgUtil.updateFeiShuMessageStatusToDealed(taskPO.getId());
            // 更新【教师移动端】消息状态为已审核
            channelMsgHelper.updateTeacherMobileMessageToDone(taskPO.getId());
            // 发送消息到mq
            if (CollUtil.isNotEmpty(records)) {
                convertInitialService.sendMQ(PointMqBusinessTypeEnum.ADD.getCode(),
                        PointMqDataOperateTypeEnum.ADD.getCode(), records, null);
                // 发送积分金币转换mq
                convertInitialService.sendBehaviourExchangeMq(
                        this.assemblyParentApprovePointExchangeMq(records,
                                null,
                                CoinExchangeOperateTypeEnum.CREATE.getCode(),
                                CoinExchangeStrategyEnum.GRAPHIC_COMMENT_PARENT.getCode(),
                                // 点评人为当前审核老师
                                WebUtil.getStaffIdLong()));
                BehaviourRecord behaviourRecord = records.get(Constant.ZERO);
                TermVo termVo = termLogic.getTermVoByCampusIdCache(WebUtil.getSchoolIdLong(), WebUtil.getCampusIdLong(),
                        Convert.toLong(behaviourRecord.getCampusSectionId()), behaviourRecord.getSubmitTime());
                if (Objects.isNull(termVo)) {
                    log.error("【班主任审核】-【消息处理】-获取学期信息为空，taskId:{}", taskPO.getId());
                    return;
                }
                if (!Objects.equals(Boolean.TRUE, termVo.getIsCurrentTerm())) {
                    // 综合成绩
                    this.revaluate(behaviourRecord.getCampusSectionId(), behaviourRecord.getGradeId(),
                            termVo.getSchoolYear(), termVo.getTermName(), behaviourRecord.getSubmitTime(),
                            RevaluateBusinessTypeEnum.REPORT_EXAM.getCode());
                }
                if (!DateUtil.isSameDay(DateUtil.date(), behaviourRecord.getSubmitTime())) {
                    // 今日之前的T+1数据变更重复重新计算
                    this.revaluate(behaviourRecord.getCampusSectionId(), behaviourRecord.getGradeId(),
                            termVo.getSchoolYear(), termVo.getTermName(), behaviourRecord.getSubmitTime(),
                            RevaluateBusinessTypeEnum.BEHAVIOUR_RECORD.getCode());
                }
            }
        });
    }

    /**
     * 老师审核发送微信消息
     *
     * @param taskPO
     * @param approvalType
     * @param infoId
     */
    public void sendWXApprovalMsg(TaskPO taskPO, Integer approvalType, String infoId) {
        //获取学生信息
        StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(taskPO.getSchoolId()),
                Convert.toLong(taskPO.getSubmitStaffId()));
        if (Objects.isNull(studentVO)) {
            return;
        }

        String approvalStatus = approvalType.equals(1) ? "已通过，请查看" : "未通过审核，请重新提交";

        // 推家校消息
        MsgContent content = new MsgContent();
        content.setFirst(studentVO.getStudentName() + "【" + taskPO.getTaskName() + "】" + approvalStatus);
        content.setKeyword1(studentVO.getClassName());
        content.setKeyword2(studentVO.getHeaderMasterName());
        content.setKeyword3(DateUtil.formatDateTime(DateUtil.date()));
        content.setKeyword4(taskPO.getTaskName());
        content.setRemark("请及时点击查看");

        SendMsgRequestDTO request = new SendMsgRequestDTO().setStudentCode(studentVO.getStudentNo()).setInfoId(infoId)
                .setContent(content).setStudentName(studentVO.getStudentName());
        SendMsgResponseDTO sendMsgResponseDTO = sendMsgManager.sendTeacherApprovalResultMsg(request);

        MessageLog messageLog = new MessageLog();
        messageLog.setMobile(taskPO.getMobile());
        messageLog.setContent(JSONUtil.toJsonStr(content));
        messageLog.setBusinessId(taskPO.getId());
        messageLog.setBusinessType(MessageLogBusinessTypeEnum.APPROVAL_RESULT.getCode());
        messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.APPROVAL_RESULT.getMessage());
        messageLog.setMessageId(Convert.toLong(sendMsgResponseDTO.getResponseCode()));
        messageLog.setMessageType(MessageLogMessageTypeEnum.WX_REMIND.getCode());
        messageLog.setTermintalType(MessageLogTerminalTypeEnum.WECHAT.getCode());
        messageLog.setSchoolId(taskPO.getSchoolId());
        messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
        messageLog.setCampusId(taskPO.getCampusId());
        messageLog.setUserId(Long.parseLong(taskPO.getSubmitStaffId()));
        messageLog.setUserType(TaskRoleTypeEnum.PARENT.getCode());
        messageLog.setTenantId(taskPO.getTenantId());
        messageLog.setRequestParam(sendMsgResponseDTO.getRequestParam());
        messageLogService.save(messageLog);
    }

    /**
     * 老师审核发送飞书-钉钉消息
     *
     * @param taskPO
     * @param approvalType
     * @param infoId
     */
    public void sendFeiShuAndDingApprovalMsg(TaskPO taskPO, Integer approvalType, String infoId) {
        //获取学生信息
        StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(taskPO.getSchoolId()),
                Convert.toLong(taskPO.getSubmitStaffId()));
        if (Objects.isNull(studentVO)) {
            return;
        }
        String approvalStatus = approvalType.equals(1) ? "已通过，请查看" : "未通过审核，请重新提交";
        log.info("执行【老师审核-审核结果】--------推送家长消息------当前学生:{}，审核状态:{}", studentVO, approvalStatus);
        List<EduParentInfoPojo> parentInfo = basicInfoService.getParentInfoList(studentVO.getStudentId());
        log.info("执行【老师审核-审核结果】--------推送家长消息------当前学生关联家长:{}", parentInfo);
        if (CollUtil.isNotEmpty(parentInfo) && TaskRoleTypeEnum.PARENT.getCode().equals(taskPO.getRoleType())) {
            for (EduParentInfoPojo parent : parentInfo) {
                // 推飞书消息（老师审核结果通知）
//                sendFeiShuParentApprovalMsg(task, approvalStatus, infoId,studentVO,parent);
                // 推钉钉消息（老师审核结果通知）
                sendDingDingParentApprovalMsg(taskPO, approvalStatus, infoId, studentVO, parent);
            }
        }
    }


    /**
     * 老师审核发送飞书消息
     *
     * @param taskPO
     * @param approvalStatus
     * @param infoId
     */
    public void sendFeiShuParentApprovalMsg(TaskPO taskPO, String approvalStatus, String infoId, StudentVO studentVO,
                                            EduParentInfoPojo parent) {
        log.info("执行【老师审核-审核结果】--------推送家长飞书消息------");
        String callBackId = SnowFlakeIdUtil.nextIdStr();
        // 发送飞书消息
        FeiMsgReqDTO feiMsgReqDTO = new FeiMsgReqDTO();
        // 设置飞书消息标题
        feiMsgReqDTO.setTitle(studentVO.getStudentName() + "【" + taskPO.getTaskName() + "】" + approvalStatus);
        //设置飞书消息内容
        feiMsgReqDTO.setContext("请及时点击查看");
        // 构建飞书消息URL
        feiMsgReqDTO.setMsgUrl(messageUrl + "/transfer-parent?" +
                "targetId=" + taskPO.getTargetId() +
                "&taskId=" + taskPO.getId() +
                "&tenantId=" + taskPO.getTenantId() +
                "&schoolId=" + taskPO.getSchoolId() +
                "&campusId=" + taskPO.getCampusId() +
                "&studentName=" + studentVO.getStudentName() +
                "&studentCode=" + studentVO.getStudentNo() +
                "&studentId=" + studentVO.getStudentId() +
                "&fromMsg=1&entry=parents");
        // 设置接收者（家长）的手机号
        feiMsgReqDTO.setMobile(parent.getMobile());
        feiMsgReqDTO.setCallBackId(callBackId);
        FeiShuResultDTO feiShuResultDTO = new FeiShuResultDTO();
        try {
            feiShuResultDTO = feiShuMsgUtil.sendTextMessage(feiMsgReqDTO);
        } catch (Exception e) {
            log.warn("[{}-家长飞书消息-老师审核结果通知]，sendFeiShuParentApprovalMsg发生异常",
                    ModuleNameEnum.DINGDING.getMessage(), e);
        }

        MessageLog messageLog = new MessageLog();
        messageLog.setMobile(parent.getMobile());//家长手机号
        messageLog.setContent(feiShuResultDTO.getContext());
        messageLog.setBusinessId(taskPO.getId());
        messageLog.setBusinessType(MessageLogBusinessTypeEnum.APPROVAL_RESULT.getCode());
        messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.APPROVAL_RESULT.getMessage());
        messageLog.setMessageId(Convert.toLong(callBackId));
        messageLog.setMessageType(MessageLogMessageTypeEnum.WX_REMIND.getCode());
        messageLog.setTermintalType(MessageLogTerminalTypeEnum.FEISHU.getCode());//飞书端
        messageLog.setSchoolId(taskPO.getSchoolId());
        messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
        messageLog.setCampusId(taskPO.getCampusId());
        messageLog.setUserId(parent.getId());//家长id
        messageLog.setUserType(TaskRoleTypeEnum.PARENT.getCode());
        messageLog.setTenantId(taskPO.getTenantId());
        messageLogService.save(messageLog);
        log.info("执行【老师审核-审核结果】--------推送家长消息------推送飞书消息:{}", messageLog);
    }

    /**
     * 老师审核发送钉钉消息
     *
     * @param taskPO
     * @param approvalStatus
     * @param infoId
     */
    public void sendDingDingParentApprovalMsg(TaskPO taskPO, String approvalStatus, String infoId, StudentVO studentVO,
                                              EduParentInfoPojo parent) {
        log.info("执行【老师审核-审核结果】--------推送家长钉钉消息------");
        OapiMessageCorpconversationAsyncsendV2Request.OA oa = new OapiMessageCorpconversationAsyncsendV2Request.OA();
        OapiMessageCorpconversationAsyncsendV2Request.Body body = new OapiMessageCorpconversationAsyncsendV2Request.Body();
        OapiMessageCorpconversationAsyncsendV2Request.Head head = new OapiMessageCorpconversationAsyncsendV2Request.Head();
        //钉钉消息头
        oa.setHead(head);
        head.setText("综合素质评估");
        head.setBgcolor(titleColor);
        //钉钉消息内容
        oa.setBody(body);
        body.setTitle(studentVO.getStudentName() + "【" + taskPO.getTaskName() + "】" + approvalStatus);
        body.setContent("请及时点击查看");
        //钉钉消息链接
        oa.setMessageUrl(messageUrl + "/transfer-parent?" +
                "targetId=" + taskPO.getTargetId() + "&taskId=" + taskPO.getId() + "&tenantId=" + taskPO.getTenantId() +
                "&schoolId=" + taskPO.getSchoolId() + "&campusId=" + taskPO.getCampusId() +
                "&studentName=" + studentVO.getStudentName() + "&studentCode=" + studentVO.getStudentNo() +
                "&studentId=" + studentVO.getStudentId() + "&fromMsg=1&entry=parents");
        Long ddTaskId = 0L;
        try {
            //发送消息---接收消息目标手机号
//            ddTaskId = dingDingMsgUtil.sendDingOaMessageNew(oa, parent.getMobile(), task.getTenantId());
            ddTaskId = dingDingMsgUtil.sendDingOaMessageNewParent(oa, parent.getMobile(), taskPO.getTenantId());
        } catch (Exception e) {
            log.warn("[{}-家长钉钉消息-老师审核结果通知]，sendDingDingParentApprovalMsg发生异常",
                    ModuleNameEnum.DINGDING.getMessage(), e);
        }

        MessageLog messageLog = new MessageLog();
        messageLog.setMobile(parent.getMobile());//家长手机号
        messageLog.setContent(oa.getBody().getContent());
        messageLog.setBusinessId(taskPO.getId());
        messageLog.setBusinessType(MessageLogBusinessTypeEnum.APPROVAL_RESULT.getCode());
        messageLog.setBusinessRemark(MessageLogBusinessTypeEnum.APPROVAL_RESULT.getMessage());
        messageLog.setMessageId(ddTaskId);
        messageLog.setMessageType(MessageLogMessageTypeEnum.WX_REMIND.getCode());
        messageLog.setTermintalType(MessageLogTerminalTypeEnum.DINGDING.getCode());//钉钉端
        messageLog.setSchoolId(taskPO.getSchoolId());
        messageLog.setCreateBy(Convert.toStr(studentVO.getStudentId()));
        messageLog.setCampusId(taskPO.getCampusId());
        messageLog.setUserId(parent.getId());//家长id
        messageLog.setUserType(TaskRoleTypeEnum.PARENT.getCode());
        messageLog.setTenantId(taskPO.getTenantId());
        messageLogService.save(messageLog);
        log.info("执行【老师审核-审核结果】--------推送家长消息------推送钉钉消息:{}", messageLog);
    }


    @Override
    public StudentInfoVO getStudentInfoByStudentNoAndName() {
        String studentNo = RequestUtil.getHeader("studentCode");
        String studentName = RequestUtil.getHeader("studentName");
//        if (StrUtil.isBlank(studentNo)) {
//            return new StudentInfoVO();
//        }
        List<StaffNOVO> students = basicInfoRemote.listUnderByNameNo(studentNo, URLUtil.decode(studentName));
        // 根据学号和姓名无法获取学生信息的情况
        Long studentIdLong = WebUtil.getStudentIdLong();
        if (CollUtil.isEmpty(students)) {
            StudentInfoVO studentInfoVO1 = new StudentInfoVO();
            if (Objects.isNull(studentIdLong)) {
                return studentInfoVO1;
            }
            com.hailiang.saas.model.vo.StudentInfoVO studentInfoVO = saasStudentManager.studentDetail(studentIdLong);
            BeanUtil.copyProperties(studentInfoVO, studentInfoVO1);
            return studentInfoVO1;
        }
        StudentInfoVO studentInfoVO = new StudentInfoVO();

        StaffNOVO studentInfo = null;

        if (students.size() > 1) {
            for (StaffNOVO student : students) {
                if (Objects.equals(studentIdLong, student.getId())) {
                    studentInfo = student;
                }
            }
        } else {
            studentInfo = CollUtil.getFirst(students);
        }

        if (studentInfo == null) {
            log.error(
                    "根据学生学号和姓名查询学生信息(家长端)，未能查询到对应的学生信息，studentNo:{}, studentName:{},studentId:{},students:{}",
                    studentNo, studentName, studentIdLong, JSONUtil.toJsonStr(students));
            throw new BizException("根据学生学号和姓名查询学生信息(家长端)，未能查询对应的海思谷学生");
        }

        BeanUtil.copyProperties(studentInfo, studentInfoVO);

        //查询学生其他信息(包括班级id)
        StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(studentInfo.getSchoolId()),
                studentInfo.getId());
        studentInfoVO.setClassId(studentVO.getClassId());
        studentInfoVO.setClassName(studentVO.getClassName());

        return studentInfoVO;
    }

    @Override
    public Info saveStudentEvaluateInfo(InfoSaveDTO dto) {
        // 参数判断
        Assert.isTrue(dto.getTargetId() != null,
                () -> new BizException(BizExceptionEnum.TARGET_ID_NOT_NULL.getMessage()));
        Assert.isTrue(StrUtil.isNotBlank(dto.getTaskName()),
                () -> new BizException(BizExceptionEnum.TASK_NAME_NOT_NULL.getMessage()));
        Assert.isTrue(CollUtil.isNotEmpty(dto.getSubmitInfoList()),
                () -> new BizException(BizExceptionEnum.SUBMIT_NOT_NULL.getMessage()));
        // 防止重复提交，如果taskId不为空 缓存 并校验
        if (ObjectUtil.isNotEmpty(dto.getTaskId())) {
            checkTaskId(dto.getTaskId());
        }
        Target target = targetLogic.getById(dto.getTargetId());
        this.checkTargetValid(target);

        dto.setSubmitStaffId(WebUtil.getStudentIdStr());
        // 前端提交参数
        List<SubmitInfoSaveDTO> submitInfoList = dto.getSubmitInfoList();
        // 判断模版格式
        infoHelper.judgeTemplate(infoConvert.toTemplateInfoSaves(submitInfoList));
        // 学生列表
        List<StudentInfo> totalStudentInfoList = new ArrayList<>();
        // 分数和学生的映射
        List<Map<String, List>> infoList = new ArrayList<>();
        // 填充分数
        infoHelper.fullFillDatas(totalStudentInfoList, infoList, dto.getTaskName(), submitInfoList);
        Date now = DateUtil.date();
        // 完成任务(学生)
        Long taskId = infoHelper.parentFinishTask(dto, now, dto.getTargetId(), TaskRoleTypeEnum.STUDENT.getCode());
        // 图片视频存相对路径
        infoHelper.transferImgForSave(dto);

        // 保存表单
        Info evaluateInfoSave = infoConvert.toEntity(dto);
        evaluateInfoSave
                .setTenantId(WebUtil.getTenantId())
                .setSchoolId(WebUtil.getSchoolId())
                .setCampusId(WebUtil.getCampusId())
                .setTargetId(dto.getTargetId())
                .setTaskId(taskId)
                .setSubmitTime(now)
                .setStudentInfos(totalStudentInfoList);

        Query query = new Query(Criteria.where("taskId").in(CollUtil.newArrayList(taskId)).and("deleted").is(0));
        List<Info> infos = mongoTemplate.find(query, Info.class);
        String evaluateInfoId = "";
        //重新提交修改mongodb
        if (CollUtil.isNotEmpty(infos)) {
            String infoId = infos.get(0).getId();
            evaluateInfoSave.setId(infoId);
            evaluateInfoId = infoId;
            infoDao.update(evaluateInfoSave);
        } else {
            //第一次提交新增数据
            evaluateInfoId = infoDao.save(evaluateInfoSave).getId();
        }

        //发送
        TaskPO taskPO = taskLogic.getById(taskId);
        // 查询学生信息（发送消息前置数据准备）
        StudentVO studentVO = basicInfoService.getStudentInfoByStudentId(Convert.toLong(taskPO.getSchoolId()),
                Convert.toLong(taskPO.getSubmitStaffId()));

        // 学生提交,查询学生的所有班级信息（包括走班）
        StudentIdRequest studentIdRequest = new StudentIdRequest();
        studentIdRequest.setStudentId(taskPO.getSubmitStaffId());
        List<StudentClassResponse> studentClassResponses = basicInfoService.listStudentCLassInfo(studentIdRequest);
        if (CollUtil.isNotEmpty(studentClassResponses)){
            List<Long> allClassIds = studentClassResponses.stream().map(StudentClassResponse::getClassId).distinct()
                    .collect(Collectors.toList());
            studentVO.setAllClassIds(allClassIds);
        }

        // 审核人开关关闭，无需审核
        if (Constant.NO.equals(target.getStudentSubmitReviewFlag())) {
            // 学生提交无需审核后续处理
            this.dealAfterParentSubmit(evaluateInfoId, taskPO, studentVO, TaskRoleTypeEnum.STUDENT.getCode());
            return evaluateInfoSave;
        }

        // 获取指标审核人
        List<EvaluateTargetReviewTeacherPO> reviewTeacherPOS = reviewTeacherManager.listByTargetId(dto.getTargetId(),2);

        if (ObjectUtil.isNotEmpty(studentVO)) {
            // 默认班主任
            if (CollUtil.isEmpty(reviewTeacherPOS)) {
                Long reviewTeacherId = studentVO.getHeaderMasterId();
                String reviewTeacherIdMobile = studentVO.getHeaderMasterMobile();
                if (ObjectUtil.isNull(reviewTeacherId) || CharSequenceUtil.isBlank(reviewTeacherIdMobile)) {
                    log.warn("【家长提交点评】发送消息给审核人，班主任不存在，studentId：{}", taskPO.getSubmitStaffId());
                    throw new BizException("班主任不存在，请联系老师");
                }

                infoHelper.sendDingDingMsg(taskPO, evaluateInfoId, reviewTeacherId, reviewTeacherIdMobile, studentVO);
                infoHelper.sendFeiShuMsg(taskPO, evaluateInfoId, reviewTeacherId, reviewTeacherIdMobile, studentVO);
                infoHelper.sendWechatMsg(taskPO, evaluateInfoId, studentVO, reviewTeacherId, reviewTeacherIdMobile);
                infoHelper.sendTeacherMobileMsg(taskPO, evaluateInfoId, studentVO, reviewTeacherId,
                        reviewTeacherIdMobile);
            } else {
                // 发送消息给审核人
                this.sendReviewTeacherMsg(reviewTeacherPOS, studentVO, taskPO, evaluateInfoId, false,
                        TaskRoleTypeEnum.STUDENT.getCode());
            }
        } else {
            log.warn("【发送家长提交老师审核消息】学生信息为空，无需发送消息");
        }
        // 保存提交操作日志
        TaskOperateLogSaveDTO saveDTO = new TaskOperateLogSaveDTO().setTenantId(taskPO.getTenantId())
                .setSchoolId(taskPO.getSchoolId()).setCampusId(taskPO.getCampusId()).setInfoId(evaluateInfoId)
                .setRoleType(TaskRoleTypeEnum.STUDENT.getCode()).setOperateType(OperateTypeEnum.SUBMIT.getCode())
                .setApprovalStatus(TaskApprovalEnum.APPROVAL_ING.getCode()).setOperateTime(now)
                .setCreateBy(WebUtil.getStudentIdStr()).setOperatorId(WebUtil.getStudentIdStr());
        taskOperateLogService.saveEvaluateTaskOperateLog(saveDTO);
        return evaluateInfoSave;
    }

    /**
     * 获取学科控件中选择的课程信息
     *
     * @param info
     * @return
     */
    private List<String> getSubmitSubjectValue(Info info) {
        if (Objects.isNull(info)){
            return Collections.emptyList();
        }
        if (CollUtil.isEmpty(info.getSubmitInfoList())){
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>();
        for (SubmitInfo submitInfo : info.getSubmitInfoList()) {
            if (!SubmitInfoTypeEnum.SUBJECT.getText().equals(submitInfo.getType())) {
                continue;
            }
            if (Objects.isNull(submitInfo.getSubmitValue())){
                continue;
            }
            List<SubmitSubjectValueDTO> subjectRelList = JSONUtil.parseArray(submitInfo.getSubmitValue()).toList(
                    SubmitSubjectValueDTO.class);
            if (CollUtil.isEmpty(subjectRelList)){
                continue;
            }
            // 课程code集合
            List<String> courseCodes = subjectRelList.stream()
                    .map(
                            SubmitSubjectValueDTO::getCourseCode).filter(
                            CharSequenceUtil::isNotBlank)
                    .distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(courseCodes)){
                continue;
            }
            result.addAll(courseCodes);
        }
        return result;
    }

    /**
     * 获取学科控件中选择的课程id集合
     *
     * @param info
     * @return
     */
    private List<Long> getSubmitCourseIds(Info info) {
        if (Objects.isNull(info)){
            return Collections.emptyList();
        }
        if (CollUtil.isEmpty(info.getSubmitInfoList())){
            return Collections.emptyList();
        }
        List<Long> result = new ArrayList<>();
        for (SubmitInfo submitInfo : info.getSubmitInfoList()) {
            if (!SubmitInfoTypeEnum.SUBJECT.getText().equals(submitInfo.getType())) {
                continue;
            }
            if (Objects.isNull(submitInfo.getSubmitValue())){
                continue;
            }
            List<SubmitSubjectValueDTO> subjectRelList = JSONUtil.parseArray(submitInfo.getSubmitValue()).toList(
                    SubmitSubjectValueDTO.class);
            if (CollUtil.isEmpty(subjectRelList)){
                continue;
            }
            // 课程id集合
            List<Long> courseIds = subjectRelList.stream()
                    .map(s->Convert.toLong(s.getCourseId()))
                    .filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(courseIds)){
                continue;
            }
            result.addAll(courseIds);
        }
        return result;
    }

    @Override
    public void approvalStudentEvaluateInfo(ApprovalHandleDTO dto) {
        // 前置校验
        TaskPO task = this.checkApprovalAuth(dto.getInfoId());

        // 审核通过处理行为记录
        List<BehaviourRecord> records = new ArrayList<>();
        BehaviourRecordHandleDTO behaviourRecordHandleDTO = new BehaviourRecordHandleDTO();
        if (ApprovalOperateEnum.APPROVAL_PASS.getCode().equals(dto.getApprovalType())) {
            records = this.approvalPassed(
                    behaviourRecordHandleDTO,
                    dto.getInfoId(),
                    task,
                    TaskRoleTypeEnum.STUDENT.getCode(),
                    dto.getSubmitInfoList());
        }

        // 修改审核任务状态
        this.updateTask(task, dto);

        // 审核后的消息处理
        this.handleMessage(dto, task, records, TaskUserTypeEnum.STUDENT.getCode());

        // 发送平板消息
        this.sendPadMessage(task, dto.getApprovalType());

        // 行为记录变更触发下游争章活动规则
        this.handleActivity(behaviourRecordHandleDTO);
    }

    /**
     * 学生提交审核发送评比消息
     *
     * @param task
     * @param approvalType 1:同意 2:拒绝
     */
    private void sendPadMessage(TaskPO task, Integer approvalType) {
        Long studentId = Convert.toLong(task.getSubmitStaffId());
        if (Objects.isNull(studentId)) {
            return;
        }
        String targetName = task.getTaskName();
        if (ApprovalOperateEnum.APPROVAL_PASS.getCode().equals(approvalType)) {
            targetName = targetName + "已审批通过";
        } else {
            targetName = targetName + "审批未通过";
        }

        ClassNoticeRequest classNoticeRequest = new ClassNoticeRequest();
        classNoticeRequest.setTitle(targetName);
        classNoticeRequest.setContent(targetName);
        classNoticeRequest.setStaffId(WebUtil.getStaffIdLong());
        classNoticeRequest.setSchoolId(WebUtil.getSchoolIdLong());
        classNoticeRequest.setPublishTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));

        List<UcStudentClassBffVO> saasStudents = basicInfoRemote.listByStudentIds(
                new StudentByIdQuery().setStudentIds(CollUtil.newArrayList(studentId)),SaasClassTypeEnum.XINGZHENG.getCode());
        if (CollUtil.isEmpty(saasStudents)){
            return;
        }

        Map<Long, String> studentNameMap = saasStudents.stream()
                .collect(Collectors.toMap(UcStudentClassBffVO::getStudentId, UcStudentClassBffVO::getStudentName));

        List<ClassStudentRequest> sendClassStudentList = new ArrayList<>();
        for (UcStudentClassBffVO student :saasStudents) {
            ClassStudentRequest classStudentRequest = new ClassStudentRequest();
            classStudentRequest.setClassId(student.getClassId());
            NoticeStudentRequest noticeStudentRequest = new NoticeStudentRequest();
            noticeStudentRequest.setStudentId(student.getStudentId());
            noticeStudentRequest.setStudentName(studentNameMap.get(student.getStudentId()));
            classStudentRequest.setStudentList(CollUtil.newArrayList(noticeStudentRequest));
            sendClassStudentList.add(classStudentRequest);
        }
        classNoticeRequest.setSendClassStudentList(sendClassStudentList);
        xxbManager.sendClassNotice(classNoticeRequest);
        log.info("[用户:{},学校:{}]-[发送平板消息]-[发送成功],消息内容:[{}]", WebUtil.getStaffIdLong(),
                WebUtil.getSchoolIdLong(), JSONUtil.toJsonStr(classNoticeRequest));
    }


    @Override
    public List<EduStaffTeachClassVO> getRelatedSubject(QueryClassSubjectRelDTO request, String schoolId,
                                                        String staffId) {

        if (ObjectUtil.isNull(request) || CollUtil.isEmpty(request.getSubmitValue())) {
            log.warn("[用户:{},学校:{}]-[获取点评人关联学科]-[请求参数有误:{}]", staffId, schoolId, request);
            return Collections.emptyList();
        }

        List<SubStudentInfo> subStudentInfoListsRequest = request.getSubmitValue();

        //获取登陆人任教情况
        EduStaffClassQueryDTO eduStaffClassQueryDTO = new EduStaffClassQueryDTO();
        eduStaffClassQueryDTO.setStaffIds(Lists.newArrayList(Long.valueOf(staffId)));
        eduStaffClassQueryDTO.setSchoolId(Long.valueOf(schoolId));

        List<EduStaffClassVO> eduStaffClassVOS = basicInfoRemote.queryStaffTeachInfo(eduStaffClassQueryDTO);

        if (CollUtil.isEmpty(eduStaffClassVOS)) {
            log.warn("[用户:{},学校:{}]-[获取点评人关联学科]-[不存在任教信息:{}]", staffId, schoolId,
                    eduStaffClassQueryDTO);
            return Collections.emptyList();
        }

        EduStaffClassVO eduStaffClassVO = eduStaffClassVOS.stream().findFirst().orElse(null);// NOSONAR
        if (ObjectUtil.isNull(eduStaffClassVO)) {
            log.warn("[用户:{},学校:{}]-[获取点评人关联学科]-[不存在任教信息:{}]", staffId, schoolId,
                    eduStaffClassQueryDTO);
            return Collections.emptyList();
        }

        List<EduStaffTeachClassVO> teachClassInfos = eduStaffClassVO.getTeachClassInfos();// NOSONAR
        if (CollUtil.isEmpty(teachClassInfos)) {
            log.warn("[用户:{},学校:{}]-[获取点评人关联学科]-[不存在授课班级]", staffId, schoolId);
            return Collections.emptyList();
        }

        Set<EduStaffTeachClassVO> resultSet = Sets.newHashSetWithExpectedSize(teachClassInfos.size());

        List<SubStudentInfo> subStudentInfos = listStudentInfos(subStudentInfoListsRequest, schoolId, staffId);
        if (CollUtil.isEmpty(subStudentInfos)) {
            log.warn("[用户:{},学校:{}]-[获取点评人关联学科]-[获取选中的学生为空],studentInfos:{}]", staffId, schoolId,
                    JSONUtil.toJsonStr(subStudentInfoListsRequest));
            return Collections.emptyList();
        }

        List<String> studentId = subStudentInfos.stream().map(SubStudentInfo::getId).collect(Collectors.toList());
        //  // 根据学生获取行政班级
        List<EduStudentClassVO> eduStudentClassVOList = this.listEduStudentClassVOSIncludeGraduated(studentId);
        //班级
        List<EduClassBaseInfoPojo> eduClassList = eduStudentClassVOList.stream()
                .map(EduStudentClassVO::getClassBaseInfos).collect(Collectors.toList()).stream()
                .flatMap(Collection::stream).collect(Collectors.toList());
        //行政班 id
        List<Long> classIdList = eduClassList.stream()
                .filter(item -> SaasClassTypeEnum.XINGZHENG.getCode().equals(item.getClassType()))
                .map(EduClassBaseInfoPojo::getId).collect(Collectors.toList());

        //去除记在行政班又在走班的学生
        //按学生 id 分组
        List<List<SubStudentInfo>> repeatInfos = subStudentInfos.stream()
                .collect(Collectors.groupingBy(SubStudentInfo::getId))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        //按学生 id 和班级 id 分组,筛选出有行政班和走班的学生
        List<List<SubStudentInfo>> repeatinfoList = new ArrayList<>();
        for (List<SubStudentInfo> infos : repeatInfos) {
            int size = infos.stream()
                    .collect(Collectors.groupingBy(p -> Pair.of(p.getId(), p.getClassId())))
                    .size();
            if (Constant.ONE < size) {
                repeatinfoList.addAll(infos.stream()
                        .collect(Collectors.groupingBy(p -> Pair.of(p.getId(), p.getClassId())))
                        .entrySet().stream()
                        .map(Map.Entry::getValue)
                        .collect(Collectors.toList()));
            }
        }

        List<SubStudentInfo> studentInfos1 = repeatinfoList.stream().flatMap(List::stream).collect(Collectors.toList());
        List<String> studentIds = studentInfos1.stream().map(SubStudentInfo::getId).collect(Collectors.toList());
        //
        subStudentInfos = subStudentInfos.stream()
                .filter(s -> !(studentIds.contains(s.getId()) && !classIdList.contains(Convert.toLong(s.getClassId()))))
                .collect(Collectors.toList());
        List<String> classIdss = subStudentInfos.stream().map(SubStudentInfo::getClassId).distinct()
                .collect(Collectors.toList());
        List<Long> classIdLongs = Convert.toList(Long.class, classIdss);
        classIdLongs.stream().flatMap(
                        classId -> teachClassInfos.stream().filter(eduStaffClass -> eduStaffClass.getClassId().equals(classId)))
                .forEach(resultSet::add);

        if (CollUtil.isEmpty(resultSet)) {
            log.warn("[用户:{},学校:{}]-[获取点评人关联学科]-[匹配学科失败 request:{}]", staffId, schoolId, request);
            return Collections.emptyList();
        }
        return Lists.newArrayList(resultSet);
    }

    private List<SubStudentInfo> listStudentInfos(List<SubStudentInfo> subStudentInfoListsRequest, String schoolId,
                                                  String staffId) {
        // 学生
        List<SubStudentInfo> uniqueStudentInfos = new ArrayList<>();
        Map<String, Boolean> uniqueMap = new HashMap<>();
        for (SubStudentInfo subStudentInfo : subStudentInfoListsRequest) {

            if (StudentInfoTypeEnum.STUDENT.getCode().equals(subStudentInfo.getType())) {
                if (uniqueMap.containsKey(subStudentInfo.getId() + subStudentInfo.getClassId())) {
                    continue;
                }
                uniqueStudentInfos.add(subStudentInfo);
                uniqueMap.put(subStudentInfo.getId() + subStudentInfo.getClassId(), true);
            } else if (StudentInfoTypeEnum.GRADE.getCode().equals(subStudentInfo.getType())
                    || StudentInfoTypeEnum.SECTION.getCode().equals(subStudentInfo.getType())) {
                EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
                eduStudentPageQueryDTO.setSchoolId(Convert.toLong(schoolId));
                eduStudentPageQueryDTO.setClassId(
                        StudentInfoTypeEnum.CLASS.getCode().equals(subStudentInfo.getType()) ? Long.parseLong(
                                subStudentInfo.getId()) : null);
                eduStudentPageQueryDTO.setGradeId(
                        StudentInfoTypeEnum.GRADE.getCode().equals(subStudentInfo.getType()) ? Long.parseLong(
                                subStudentInfo.getId()) : null);
                eduStudentPageQueryDTO.setCampusSectionId(
                        StudentInfoTypeEnum.SECTION.getCode().equals(subStudentInfo.getType()) ? Long.parseLong(
                                subStudentInfo.getId()) : null);
                eduStudentPageQueryDTO.setPageSize(Integer.MAX_VALUE);
//                List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoService.queryStudentPage(eduStudentPageQueryDTO);
                List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoService.queryStudentPageAll(eduStudentPageQueryDTO);
                if (CollUtil.isEmpty(eduStudentInfoVOS)) {
                    log.warn("统计行为获取学生为空");
                    continue;
                }
                for (EduStudentInfoVO vo : eduStudentInfoVOS) {
                    if (uniqueMap.containsKey(Convert.toStr(vo.getId()) + Convert.toStr(vo.getClassId()))) {
                        continue;
                    }
                    SubStudentInfo student = new SubStudentInfo();
                    student.setId(Convert.toStr(vo.getId()));
                    student.setName(vo.getStudentName());
                    student.setType(StudentInfoTypeEnum.STUDENT.getCode());
                    student.setSchoolId(subStudentInfo.getSchoolId());
                    student.setClassId(Convert.toStr(vo.getClassId()));
                    uniqueStudentInfos.add(student);
                    uniqueMap.put(Convert.toStr(vo.getId()) + Convert.toStr(vo.getClassId()), true);
                }
            } else if (StudentInfoTypeEnum.CLASS.getCode().equals(subStudentInfo.getType())) {
                //根据班级 id 获取学生
                StudentPageQueryDTO eduStudentPageQueryDTO = new StudentPageQueryDTO();
                eduStudentPageQueryDTO.setSchoolId(Convert.toLong(schoolId));
                eduStudentPageQueryDTO.setClassId(Convert.toLong(subStudentInfo.getId()));
                eduStudentPageQueryDTO.setPageSize(Integer.MAX_VALUE);
                List<StudentInfo1VO> eduStudentInfoVOList = saasStudentManager.queryStudentPage(eduStudentPageQueryDTO);
                if (eduStudentInfoVOList.isEmpty()) {
                    log.info("统计行为获取班级为空,班级 id：【{}】", subStudentInfo.getId());
                    continue;
                }
                for (StudentInfo1VO vo : eduStudentInfoVOList) {
                    if (uniqueMap.containsKey(Convert.toStr(vo.getId()) + subStudentInfo.getId())) {
                        continue;
                    }
                    SubStudentInfo student = new SubStudentInfo();
                    student.setId(Convert.toStr(vo.getId()));
                    student.setName(vo.getStudentName());
                    student.setType(StudentInfoTypeEnum.STUDENT.getCode());
                    student.setSchoolId(subStudentInfo.getSchoolId());
                    student.setClassId(subStudentInfo.getId());
                    uniqueStudentInfos.add(student);
                    uniqueMap.put(Convert.toStr(vo.getId()) + subStudentInfo.getId(), true);
                }
            }
        }
        return uniqueStudentInfos;
    }

//    @Override
//    public void fixEvaluateData() {
//        TimeInterval timeInterval = DateUtil.timer();
//        Map<String, Long> staffMap = new HashMap<>();
//        staffMap.put("6907818000288997376", 5818L);
//        staffMap.put("6907818736301268992", 794L);
//        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.list(new LambdaQueryWrapper<BehaviourRecord>()
//                .eq(BehaviourRecord::getDataSource, 5)
//                .eq(BehaviourRecord::getSubjectCode, "0"));
//        log.info("【修复星动力数据】-获取行为记录数据结束,耗时:{}",timeInterval.intervalMs());
//        behaviourRecords = behaviourRecords
//                .stream()
//                .filter(item -> Objects.equals(item.getAppraisalId(), "6907818000288997376")
//                || Objects.equals(item.getAppraisalId(), "6907818736301268992"))
//                .collect(Collectors.toList());
//        Map<String, List<BehaviourRecord>> behaviourRecordMap = behaviourRecords
//                .stream().collect(Collectors.groupingBy(BehaviourRecord::getAppraisalId));
//        behaviourRecordMap.forEach((k,v) -> {
//            Long planId = staffMap.get(k);
//            if(Objects.nonNull(planId)){
//                List<ThirdToXdlRecordMessageDTO> thirdToXdlRecordMessageDTOS = this.convertThirdToXdlRecordMessageDTOV2(v, planId, null, null);
//                eventSendHelper.sendEventMQ(thirdToXdlRecordMessageDTOS, xwlAddRecord);
//            }
//        });
//    }


    private List<ThirdToXdlRecordMessageDTO> convertThirdToXdlRecordMessageDTOV2(List<BehaviourRecord> behaviourRecords,
                                                                                 Long planId,
                                                                                 Long oldBehaviourRecordId,
                                                                                 Map<Long, String> idToHelpContentMap) {
        List<ThirdToXdlRecordMessageDTO> records = new ArrayList<>();
        for (BehaviourRecord behaviourRecord : behaviourRecords) {
            ThirdToXdlRecordMessageDTO thirdToXdlRecordMessageDTO = new ThirdToXdlRecordMessageDTO();
            thirdToXdlRecordMessageDTO.setPlanId(planId);
            thirdToXdlRecordMessageDTO.setScore(behaviourRecord.getScore());
            thirdToXdlRecordMessageDTO.setScoreType(behaviourRecord.getScoreType());
            thirdToXdlRecordMessageDTO.setGradeId(behaviourRecord.getGradeId());
            thirdToXdlRecordMessageDTO.setGradeCode(behaviourRecord.getGradeCode());
            thirdToXdlRecordMessageDTO.setTenantId(behaviourRecord.getTenantId());
            thirdToXdlRecordMessageDTO.setSchoolId(behaviourRecord.getSchoolId());
            thirdToXdlRecordMessageDTO.setCampusId(behaviourRecord.getCampusId());
            thirdToXdlRecordMessageDTO.setCampusSectionId(behaviourRecord.getCampusSectionId());
            thirdToXdlRecordMessageDTO.setCampusSectionCode(behaviourRecord.getCampusSectionCode());
            thirdToXdlRecordMessageDTO.setClassId(behaviourRecord.getClassId());
            thirdToXdlRecordMessageDTO.setSubjectCode(behaviourRecord.getSubjectCode());
            thirdToXdlRecordMessageDTO.setThirdRecordId(behaviourRecord.getId().toString());
            thirdToXdlRecordMessageDTO.setStudentId(behaviourRecord.getStudentId());
            thirdToXdlRecordMessageDTO.setModuleCode(behaviourRecord.getModuleCode());
            thirdToXdlRecordMessageDTO.setThirdTargetType(1);
            thirdToXdlRecordMessageDTO.setThirdTargetName("其他");
            thirdToXdlRecordMessageDTO.setThirdTargetId("-1");
            thirdToXdlRecordMessageDTO.setThirdOperationType(1);
            thirdToXdlRecordMessageDTO.setThirdOptionId(
                    CharSequenceUtil.isBlank(behaviourRecord.getOptionId()) ? Convert.toStr(
                            behaviourRecord.getTargetId()) : behaviourRecord.getOptionId());
            thirdToXdlRecordMessageDTO.setThirdOptionName(behaviourRecord.getInfoName());
            thirdToXdlRecordMessageDTO.setThirdDataSource(2);
            thirdToXdlRecordMessageDTO.setCreateBy(behaviourRecord.getCreateBy());
            thirdToXdlRecordMessageDTO.setCreateTime(
                    DateUtil.format(behaviourRecord.getSubmitTime(), "yyyy-MM-dd HH:mm:ss"));
            thirdToXdlRecordMessageDTO.setCreateByName(behaviourRecord.getAppraisalName());
            thirdToXdlRecordMessageDTO.setOldBehaviourRecordId(oldBehaviourRecordId);
            thirdToXdlRecordMessageDTO.setScene("personal");
            thirdToXdlRecordMessageDTO.setContent(behaviourRecord.getInfoName());
            records.add(thirdToXdlRecordMessageDTO);
        }
        return records;
    }

}
