package com.hailiang.service;

import com.hailiang.model.request.studentmodel.StudentAbilityDataV2Request;
import com.hailiang.model.response.studentmodel.StudentAbilityModelDataFinalV2Response;
import com.hailiang.model.response.studentmodel.StudentAbilityModelDataV2Response;
import com.hailiang.model.response.studentmodel.StudentAbilityModelNewResponse;

import java.util.List;


/**
 * 接口
 *
 * <AUTHOR> 2024-04-19 15:29:38
 */
public interface StudentAbilityModelDataService{

    StudentAbilityModelDataFinalV2Response getDataV2(StudentAbilityDataV2Request studentAbilityDataV2Request);

    List<StudentAbilityModelDataV2Response> fillStudentData(StudentAbilityModelNewResponse studentAbilityModel, String studentId, String classId, String gradeId, String dimId);

}