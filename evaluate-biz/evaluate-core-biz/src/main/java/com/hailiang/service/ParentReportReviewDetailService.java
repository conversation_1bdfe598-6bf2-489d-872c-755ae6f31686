package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.query.*;
import com.hailiang.model.entity.ReportReviewDetail;
import com.hailiang.model.vo.GetReportStudentInfoForParentVO;
import com.hailiang.model.vo.GetReportStudentInfoForTeacherVO;
import com.hailiang.remote.hai.domain.dto.response.SendMsgResponseDTO;

import java.util.List;

public interface ParentReportReviewDetailService extends IService<ReportReviewDetail> {


    // 老师查看报告获取学生信息和模块信息
    GetReportStudentInfoForTeacherVO getReportStudentInfoForTeacher(GetReportStudentInfoForTeacherDTO dto);

    GetReportStudentInfoForTeacherVO getReportStudentInfoForStudent(GetReportStudentInfoForStudentDTO dto);

    // 家长查看报告获取学生信息和模块信息
    GetReportStudentInfoForParentVO getReportStudentInfoForParent(GetReportStudentInfoForParentDTO dto);

    List<SendMsgResponseDTO> sendReportToParent(List<ReportReviewDetail> details);

    void hangUpUnNeedBehaviourRecordIds(BehaviourRecordQueryDTO behaviourRecordQueryDTO);

    List<Long> listNoSendRecordByDetail(String detailId);
}

