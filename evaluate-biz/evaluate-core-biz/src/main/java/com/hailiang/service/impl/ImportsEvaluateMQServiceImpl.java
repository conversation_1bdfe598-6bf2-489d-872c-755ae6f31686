package com.hailiang.service.impl;

import com.hailiang.model.dto.ImportsBehaviourRecordMsgDTO;
import com.hailiang.service.ImportsEvaluateMQService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 批量点评录入发送消息服务具体实现
 *
 * @Description: 批量点评录入发送消息服务具体实现
 * @Author: Jovi
 * @Date: Created in 2024/12/6
 * @Version: 2.0.0
 */
@Service
@Slf4j
public class ImportsEvaluateMQServiceImpl implements ImportsEvaluateMQService {

    @Resource
    RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.topic.imports.behaviourRecord}")
    private String behaviourRecordImportsTopic;

    @Override
    public void sendMQ(ImportsBehaviourRecordMsgDTO importsBehaviourRecordMsgDTO) {

        log.info("【批量点评导入】-要发送的数据:{}", importsBehaviourRecordMsgDTO);

        rocketMQTemplate.asyncSendOrderly(behaviourRecordImportsTopic,
                importsBehaviourRecordMsgDTO,
                "evaluateImports",
                new SendCallback() {
                    @Override
                    public void onSuccess(SendResult sendResult) {
                        log.info("【批量点评导入】-【mq消息发送成功】，MsgId:{}", sendResult.getMsgId());
                    }

                    @Override
                    public void onException(Throwable throwable) {
                        log.error("【批量点评导入】-【mq消息发送成功】发送失败:【{}】,异常信息：【{}】", importsBehaviourRecordMsgDTO,
                                throwable.getMessage());
                    }

                });
    }

}
