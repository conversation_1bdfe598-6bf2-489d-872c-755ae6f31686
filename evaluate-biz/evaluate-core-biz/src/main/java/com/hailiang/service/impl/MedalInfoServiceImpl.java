package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.constant.Constant;
import com.hailiang.convert.MedalConvert;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.manager.*;
import com.hailiang.model.entity.*;
import com.hailiang.model.medal.dto.MedalInfoDeleteDTO;
import com.hailiang.model.medal.dto.MedalInfoEditDTO;
import com.hailiang.model.medal.dto.MedalInfoQueryDTO;
import com.hailiang.model.medal.dto.MedalInfoSaveDTO;
import com.hailiang.model.medal.vo.MedalCatalogueInfoVO;
import com.hailiang.model.medal.vo.MedalCatalogueVO;
import com.hailiang.model.medal.vo.MedalInfoVO;
import com.hailiang.service.MedalCatalogueService;
import com.hailiang.service.MedalInfoService;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import com.lark.oapi.core.utils.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: JJL
 * @Date: 2023/5/31 14:31
 */
@Service
@Slf4j
public class MedalInfoServiceImpl implements MedalInfoService {
    @Autowired
    private MedalInfoManager medalInfoManager;
    @Resource
    private MedalConvert convert;
    @Autowired
    private MedalTaskManager medalTaskManager;
    @Autowired
    private MedalLogoManager medalLogoManager;
    @Autowired
    private MedalCatalogueManager medalCatalogueManager;
    @Value("${oss.urlPrefix}")
    private String urlPrefix;
    @Autowired
    private MedalCatalogueService medalCatalogueService;
    @Autowired
    private MedalUserAcquireRecordManager medalUserAcquireRecordManager;
    /**
     * 模块名称
     */
    private final static String MODULE_NAME = "[学生争章-";

    /**
     * 新增奖章
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMedalInfo(MedalInfoSaveDTO dto) {
        if (BeanUtil.isEmpty(dto)) {
            return;
        }
        log.info(MODULE_NAME + "新增奖章], 入参:{} ", JSONUtil.toJsonStr(dto));
        //校验该校区下奖章名称是否重名
        MedalInfo medalInfo = medalInfoManager.getOne(new LambdaQueryWrapper<MedalInfo>()
                .eq(MedalInfo::getName, dto.getName())
                .eq(MedalInfo::getTenantId, WebUtil.getTenantId())
                .eq(MedalInfo::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalInfo::getCampusId, WebUtil.getCampusId()));
        Assert.isFalse(BeanUtil.isNotEmpty(medalInfo), () -> new BizException(BizExceptionEnum.MEDAL_DETAIL_EXIST.getMessage()));
        dto.setTenantId(WebUtil.getTenantId());
        dto.setSchoolId(WebUtil.getSchoolId());
        dto.setCampusId(WebUtil.getCampusId());
        dto.setId(SnowFlakeIdUtil.nextId());
        //新增奖章
        medalInfoManager.save(convert.toMedalInfo(dto));
    }

    /**
     * 删除奖章
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMedalInfo(MedalInfoDeleteDTO dto) {
        if (ObjectUtil.isNull(dto.getId()) || BeanUtil.isEmpty(dto)) {
            return;
        }
        log.info(MODULE_NAME + "删除奖章], 入参:{} ", JSONUtil.toJsonStr(dto));
        //彻底删除 删除奖章明细表里的获章记录
        if (Constant.ONE.equals(dto.getCompletelyDelete())) {
            medalUserAcquireRecordManager.remove(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                    .eq(MedalUserAcquireRecord::getTenantId, WebUtil.getTenantId())
                    .eq(MedalUserAcquireRecord::getSchoolId, WebUtil.getSchoolId())
                    .eq(MedalUserAcquireRecord::getCampusId, WebUtil.getCampusId())
                    .eq(MedalUserAcquireRecord::getMedalInfoId, dto.getId())
            );
        }
        //逻辑删除
        medalInfoManager.removeById(dto.getId());
        //该奖章对应的任务失效
        List<MedalTask> medalTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>()
                .eq(MedalTask::getTenantId, WebUtil.getTenantId())
                .eq(MedalTask::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalTask::getCampusId, WebUtil.getCampusId())
                .eq(MedalTask::getMedalInfoId, dto.getId()));
        if (CollectionUtil.isEmpty(medalTasks)) {
            return;
        }
        medalTasks.stream().forEach(s -> {
            s.setStatus(Constant.ONE);
        });
        medalTaskManager.updateBatchById(medalTasks);
    }

    /**
     * 修改奖章
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMedalInfo(MedalInfoEditDTO dto) {
        if (BeanUtil.isEmpty(dto)) {
            return;
        }
        MedalLogo medalLogo = medalLogoManager.getById(dto.getLogoId());
        //校验奖章logo是否被删除
        Assert.isFalse(BeanUtil.isEmpty(medalLogo), () -> new BizException(BizExceptionEnum.MEDAL_LOGO_NOT_EXIST_ERROR.getMessage()));
        log.info(MODULE_NAME + "修改奖章], 入参:{} ", JSONUtil.toJsonStr(dto));
        //校验该校区下奖章名称是否重名
        MedalInfo medalInfo = medalInfoManager.getOne(new LambdaQueryWrapper<MedalInfo>()
                .eq(MedalInfo::getName, dto.getName())
                .ne(MedalInfo::getId, dto.getId())
                .eq(MedalInfo::getTenantId, WebUtil.getTenantId())
                .eq(MedalInfo::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalInfo::getCampusId, WebUtil.getCampusId()));
        Assert.isFalse(BeanUtil.isNotEmpty(medalInfo), () -> new BizException(BizExceptionEnum.MEDAL_DETAIL_EXIST.getMessage()));
        MedalInfo managerById = medalInfoManager.getById(dto.getId());
        managerById.setDescription(dto.getDescription());
        managerById.setLogoId(dto.getLogoId());
        managerById.setName(dto.getName());
        //修改奖章
        medalInfoManager.updateById(managerById);
    }

    /**
     * 查询奖章(分页)
     *
     * @param dto
     * @return
     */
    @Override
    public Page<MedalInfoVO> pageMedalInfo(MedalInfoQueryDTO dto) {
        if (BeanUtil.isEmpty(dto)) {
            return new Page<>();
        }
        Page<MedalInfo> medalInfoPage = medalInfoManager.page(new Page<>(dto.getPageNum(), dto.getPageSize()),
                new LambdaQueryWrapper<MedalInfo>()
                        .eq(MedalInfo::getTenantId, WebUtil.getTenantId())
                        .eq(MedalInfo::getSchoolId, WebUtil.getSchoolId())
                        .eq(MedalInfo::getCampusId, WebUtil.getCampusId())
                        .eq(MedalInfo::getMedalCatalogueId, dto.getMedalCatalogueId())
                        .orderByDesc(MedalInfo::getCreateTime));
        if (CollectionUtil.isEmpty(medalInfoPage.getRecords())) {
            return new Page<>();
        }
        Page<MedalInfoVO> medalInfoVOPage = convert.toPageMedalInfoVO(medalInfoPage);
        List<Long> logoIds = medalInfoPage.getRecords().stream().map(MedalInfo::getLogoId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(logoIds)) {
            return medalInfoVOPage;
        }
        List<MedalLogo> logos = medalLogoManager.listMedalLogos(logoIds);
        medalInfoVOPage.getRecords().stream().forEach(s -> {
            s.setLogoUrl(urlPrefix + logos.stream().filter(m -> m.getId().equals(s.getLogoId())).collect(Collectors.toList()).get(Constant.ZERO).getLogoUrl());
        });
        return medalInfoVOPage;
    }


    /**
     * 奖章详情
     *
     * @param id
     * @return
     */
    @Override
    public MedalInfoVO getMedalInfo(Long id) {
        if (ObjectUtil.isNull(id)) {
            return null;
        }
        MedalInfo medalInfo = medalInfoManager.getById(id);
        if (BeanUtil.isEmpty(medalInfo)) {
            return null;
        }
        List<MedalLogo> medalLogos = medalLogoManager.listMedalLogos(Lists.newArrayList(medalInfo.getLogoId()));
        MedalInfoVO medalInfoVO = convert.toMedalInfoVO(medalInfo);
        medalInfoVO.setLogoUrl(urlPrefix + medalLogos.get(Constant.ZERO).getLogoUrl());
        return medalInfoVO;
    }

    /**
     * 查询奖章(不分页)
     *
     * @return
     */
    @Override
    public List<MedalInfoVO> listMedalDetails() {
        List<MedalInfo> medalInfos = medalInfoManager.list(new LambdaQueryWrapper<MedalInfo>()
                .eq(MedalInfo::getTenantId, WebUtil.getTenantId())
                .eq(MedalInfo::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalInfo::getCampusId, WebUtil.getCampusId())
                .orderByDesc(MedalInfo::getCreateTime));
        if (CollectionUtil.isEmpty(medalInfos)) {
            return Collections.EMPTY_LIST;
        }
        List<MedalInfoVO> medalInfoVOS = convert.toListMedalInfoVO(medalInfos);
        List<Long> logoIds = medalInfos.stream().map(MedalInfo::getLogoId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(logoIds)) {
            return medalInfoVOS;
        }
        List<MedalLogo> logos = medalLogoManager.listMedalLogos(logoIds);
        medalInfoVOS.stream().forEach(s -> {
            s.setLogoUrl(urlPrefix + logos.stream().filter(m -> m.getId().equals(s.getLogoId())).collect(Collectors.toList()).get(Constant.ZERO).getLogoUrl());
        });
        return medalInfoVOS;
    }
    /**
     * 查询奖章(不分页)
     *
     * @return
     */
    @Override
    public List<MedalInfoVO> listMedalDetailsBySchoolId(String schoolId) {
        List<MedalInfo> medalInfos = medalInfoManager.listMedalInfosBySchoolId(schoolId);
        if (CollectionUtil.isEmpty(medalInfos)) {
            return Collections.emptyList();
        }
        List<MedalInfoVO> medalInfoVOS = convert.toListMedalInfoVO(medalInfos);
        // 章目名称
        List<MedalCatalogue> medalCatalogues = medalCatalogueManager.listMedalCataloguesBySchoolId(schoolId);
        Map<Long, String> medalCatalogueMap = CollStreamUtil.toMap(medalCatalogues, MedalCatalogue::getId, MedalCatalogue::getName);
        medalInfoVOS.stream().forEach(s -> {
            s.setMedalCatalogueName(medalCatalogueMap.getOrDefault(s.getMedalCatalogueId(), null));
        });
        // logo
        List<Long> logoIds = medalInfos.stream().map(MedalInfo::getLogoId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(logoIds)) {
            return medalInfoVOS;
        }
        List<MedalLogo> logos = medalLogoManager.listMedalLogos(logoIds);
        medalInfoVOS.stream().forEach(s -> {
            s.setLogoUrl(urlPrefix + logos.stream().filter(m -> m.getId().equals(s.getLogoId())).collect(Collectors.toList()).get(Constant.ZERO).getLogoUrl());
        });
        return medalInfoVOS;
    }
    /**
     * 查询奖章(二级含章目)
     *
     * @return
     */
    @Override
    public List<MedalCatalogueInfoVO> listMedalCatalogueInfos() {
        ArrayList<MedalCatalogueInfoVO> medalCatalogueInfoVOS = new ArrayList<>();
        //该校区章目
        List<MedalCatalogueVO> medalCatalogueVOS = medalCatalogueService.listMedalCatalogue();
        if (CollectionUtil.isEmpty(medalCatalogueVOS)) {
            return medalCatalogueInfoVOS;
        }
        //该校区奖章
        List<MedalInfoVO> medalInfoVOS = listMedalDetails();
        if (CollectionUtil.isEmpty(medalInfoVOS)) {
            return medalCatalogueInfoVOS;
        }
        medalCatalogueVOS.stream().forEach(s -> {
            MedalCatalogueInfoVO medalCatalogueInfoVO = new MedalCatalogueInfoVO();
            List<MedalInfoVO> medalInfoVOList = medalInfoVOS.stream().filter(m -> m.getMedalCatalogueId().equals(s.getId())).collect(Collectors.toList());
            medalCatalogueInfoVO.setMedalCatalogueId(s.getId());
            medalCatalogueInfoVO.setMedalCatalogueName(s.getName());
            medalCatalogueInfoVO.setMedalInfoVOS(medalInfoVOList);
            if (CollectionUtil.isNotEmpty(medalInfoVOList)) {
                medalCatalogueInfoVOS.add(medalCatalogueInfoVO);
            }
        });
        return medalCatalogueInfoVOS;
    }


}
