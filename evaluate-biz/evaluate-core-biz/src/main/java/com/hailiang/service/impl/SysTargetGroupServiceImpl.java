package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.hailiang.constant.Constant;
import com.hailiang.convert.SysTargetGroupConvert;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.SubmitRateEnum;
import com.hailiang.enums.TargetTypeEnum;
import com.hailiang.enums.TaskUserTypeEnum;
import com.hailiang.enums.medal.MedalTargetTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.manager.SysTargetGroupManager;
import com.hailiang.manager.SysTargetManager;
import com.hailiang.mapper.SysTargetGroupMapper;
import com.hailiang.mapper.mongo.TargetTemplateDao;
import com.hailiang.model.dto.query.TargetGroupListByConditionsDTO;
import com.hailiang.model.dto.query.TargetGroupListByConditionsDaoDTO;
import com.hailiang.model.dto.query.TargetGroupListDTO;
import com.hailiang.model.dto.query.TargetIdQuery;
import com.hailiang.model.dto.query.listAllEvaluateTargetDaoDTO;
import com.hailiang.model.dto.response.speed.GroupInfoResponse;
import com.hailiang.model.dto.response.speed.ModuleInfoResponse;
import com.hailiang.model.dto.response.speed.TargetInfoResponse;
import com.hailiang.model.dto.save.TargetGroupSaveDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.dto.target.SystemRecommendTargetDTO;
import com.hailiang.model.entity.SysTarget;
import com.hailiang.model.entity.SysTargetGroup;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.vo.ListAllEvaluateTargetVOInnerTargetGroup;
import com.hailiang.model.vo.ListAllEvaluateTargetVOModule;
import com.hailiang.model.vo.ListOrgIdAndRoleIdVO;
import com.hailiang.model.vo.TargetGroupListByConditionsVO;
import com.hailiang.model.vo.TargetGroupListByConditionsVOInnerTarget;
import com.hailiang.model.vo.TargetGroupListVO;
import com.hailiang.model.vo.TargetGroupTargetListByConditionsVO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.SysTargetGroupService;
import com.hailiang.service.SysTargetService;
import com.hailiang.service.TargetTemplateService;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/8/29
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class SysTargetGroupServiceImpl implements SysTargetGroupService {

    @Autowired
    private SysTargetGroupManager sysTargetGroupManager;

    @Autowired
    private SysTargetGroupConvert convert;

    @Autowired
    private SysTargetManager sysTargetManager;

    @Resource
    private SysTargetGroupMapper sysTargetGroupMapper;

    @Autowired
    private TargetTemplateService templateService;

    @Autowired
    private SysTargetService sysTargetService;

    @Resource
    private TargetTemplateDao templateDao;

    @Autowired
    private BasicInfoService basicInfoService;


    /**
     * 指标分组--添加、修改
     *
     * @param dto
     */
    @Override
    public void save(TargetGroupSaveDTO dto) {
        Assert.isTrue(dto.getGroupName().length() <= 10, "指标分组名称最多输入10个字符");
        // 根据模块编码获取模块名称
        String moduleName = ModuleEnum.getModuleName(dto.getModuleCode());
        // 判断模块名称是否存在
        if (StrUtil.isBlank(moduleName)) {
            throw new BizException("模块编码输入有误");
        }

        SysTargetGroup evaluateTargetGroup = convert.toSysTargetGroup(dto);
        evaluateTargetGroup.setId(StrUtil.isNotBlank(dto.getId()) ? Long.parseLong(dto.getId()) : null);
        evaluateTargetGroup.setModuleName(moduleName);

        // 只有添加时才计算排名
        if (StrUtil.isBlank(dto.getId())) {
            // 查询当前学校分组数，用于计算sortIndex，此处包含已删除分组
            int count = (int) sysTargetGroupManager.count(new LambdaQueryWrapper<SysTargetGroup>()
                    .eq(SysTargetGroup::getDeleted, Constant.NO));
            evaluateTargetGroup.setSortIndex(count + 1);
        }

        sysTargetGroupManager.saveOrUpdate(evaluateTargetGroup);
    }

    /**
     * 指标分组--删除
     *
     * @param groupId
     */
    @Override
    public void delete(String groupId) {
        if (StrUtil.isBlank(groupId)) {
            return;
        }
        Long id = Long.parseLong(groupId);
        long count = sysTargetManager.count(new LambdaQueryWrapper<SysTarget>()
                .eq(SysTarget::getGroupId, id));

        if (count != 0) {
            throw new BizException("请将组内的指标移到其他分组或删除完，才能删除分组");
        }
        sysTargetGroupManager.removeById(id);
    }

    /**
     * 查分组列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<TargetGroupListVO> list(TargetGroupListDTO dto) {
        // 查分组列表
        List<SysTargetGroup> evaluateTargetGroupList = sysTargetGroupManager.list(
                new LambdaQueryWrapper<SysTargetGroup>()
                        .eq(SysTargetGroup::getModuleCode, dto.getModuleCode())
                        .eq(SysTargetGroup::getDataSource, DataSourceEnum.EVALUATE.getCode())
                        .eq(SysTargetGroup::getGroupType, TargetTypeEnum.DEFAULT)
                        .orderByAsc(SysTargetGroup::getSortIndex));
        if (CollUtil.isEmpty(evaluateTargetGroupList)) {
            return null;
        }
        List<TargetGroupListVO> evaluateTargetGroupListVOList = convert.toEvaluateTargetGroupListVOList(evaluateTargetGroupList);
        // 不查分组指标--返回
        if (!dto.getQueryTargetFlag()) {
            return evaluateTargetGroupListVOList;
        }

        // 查分组指标
        for (TargetGroupListVO group : evaluateTargetGroupListVOList) {
            // 查分组列表
            List<SysTarget> list = sysTargetManager.list(new LambdaQueryWrapper<SysTarget>()
                    .eq(SysTarget::getGroupId, group.getGroupId())
                    .eq(SysTarget::getTargetType, TargetTypeEnum.DEFAULT.getCode())
                    .eq(SysTarget::getDeleted, Constant.ZERO)
                    .orderByAsc(SysTarget::getSortIndex));

            if (CollUtil.isEmpty(list)) {
                continue;
            }
            // 设置分组指标
            group.setTargetList(convert.toGroupInnerTargetList(list));
        }

        return evaluateTargetGroupListVOList;
    }

    /**
     * 指标分组--排序
     *
     * @param dtoList
     */
    @Override
    public void sort(List<String> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        List<Long> groupIdList = new ArrayList<>();
        for (String id : dtoList) {
            groupIdList.add(Long.parseLong(id));
        }
        for (int i = 0; i < groupIdList.size(); i++) {
            SysTargetGroup group = new SysTargetGroup();
            group.setId(groupIdList.get(i));
            group.setSortIndex(i + 1);
            // 修改当前学校下分组顺序
            sysTargetGroupManager.update(group, new LambdaQueryWrapper<SysTargetGroup>() // NOSONAR
                    .eq(SysTargetGroup::getId, groupIdList.get(i))
                    .eq(SysTargetGroup::getDeleted, Constant.ZERO));
        }
    }

    /**
     * 通过分组id获取分组
     *
     * @param groupId
     * @return
     */
    @Override
    public SysTargetGroup get(Long groupId) {
        SysTargetGroup group = sysTargetGroupManager.getById(groupId);
        if (ObjectUtil.isNull(group)) {
            return null;
        }
        return group;
    }

    /**
     * 根据学校id和模块编码查分组列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<TargetGroupTargetListByConditionsVO> listByConditions(TargetGroupListByConditionsDTO dto) {
        // 查分组列表
        TargetGroupListByConditionsDaoDTO daoDTO = new TargetGroupListByConditionsDaoDTO();
        daoDTO.setModuleCode(dto.getModuleCode());
        daoDTO.setGroupType(TargetTypeEnum.DEFAULT.getCode());
        daoDTO.setTargetType(TargetTypeEnum.DEFAULT.getCode());
        List<TargetGroupListByConditionsVO> groupList = sysTargetGroupMapper.listByConditions(daoDTO);
        if (CollUtil.isEmpty(groupList)) {
            return Lists.newArrayList();
        }
        // 获取华为云链接前缀
        String iconUrlPrefix = sysTargetService.getIconUrlPrefix();
        // 剔除每个分组中已删除的和数据来源不是综合素质评价的
        for (TargetGroupListByConditionsVO group : groupList) {
            // 获取分组指标
            List<TargetGroupListByConditionsVOInnerTarget> targetList = group.getTargetList();
            if(CollUtil.isEmpty(targetList)){
                continue;
            }
            // 筛选出数据源为综合素质评价且未删除的指标
            List<TargetGroupListByConditionsVOInnerTarget> targetFilterList = targetList.stream().filter(target -> {
                return DataSourceEnum.EVALUATE.getCode().equals(target.getDataSource()) && Constant.FALSE.equals(target.getDeleted());
            }).sorted((t1, t2) -> t1.getSortIndex().compareTo(t2.getSortIndex())).collect(Collectors.toList());
            // 筛选后为空则将当前分组对应的指标列表设置为空
            if (CollUtil.isEmpty(targetFilterList)) {
                group.setTargetList(targetFilterList);
                continue;
            }
            // 筛选未删除填写人
            for (TargetGroupListByConditionsVOInnerTarget target : targetFilterList) {
                // iconUrl添加华为云链接前缀
                target.setIconUrl(iconUrlPrefix + target.getIconUrl());
                // 设置提交类型名称
                target.setSubmitTypeName(SubmitRateEnum.getSubmitRateName(target.getSubmitType()));
//                List<TargetGroupListByConditionsVOInnerTargetUser> userList = target.getSubmitUserNameList().stream().filter(user -> Constant.FALSE.equals(user.getDeleted())).collect(Collectors.toList());
//                target.setSubmitUserNameList(userList);
            }
            // 设置指标列表
            group.setTargetList(targetFilterList);
        }


        return convert.toTargetGroupTargetListByConditionsVOList(groupList);
    }

    /**
     * 获取当前登录人所有指标(老师端)
     *
     * @param userType 用户类型1:老师 2:家长
     * @return
     */
    @Override
    public List<ListAllEvaluateTargetVOModule> listAllEvaluateTarget(Integer userType) {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.start("[获取当前登录人所有指标]");
        List<ListAllEvaluateTargetVOModule> moduleList = new ArrayList<>();
        //老师
        if (TaskUserTypeEnum.TEACHER.getCode().equals(userType)) {
            moduleList = queryStaffInfoModules();
        }
        //家长
        if (TaskUserTypeEnum.PARENT.getCode().equals(userType)) {
            moduleList = queryStudentInfoModules();
        }
        if (CollUtil.isEmpty(moduleList)) {
            return new ArrayList<>();
        }

        // 获取华为云链接前缀
        String iconUrlPrefix = sysTargetService.getIconUrlPrefix();
        // 处理指标iconUrl添加华为云链接前缀
        for (ListAllEvaluateTargetVOModule module : moduleList) {
            List<ListAllEvaluateTargetVOInnerTargetGroup> groupList = module.getTargetGroupList();
            for (ListAllEvaluateTargetVOInnerTargetGroup group : groupList) {
                group.getTargetList().forEach(target -> target.setIconUrl(iconUrlPrefix + target.getIconUrl()));
            }
        }

        log.info("[获取当前登录人所有指标]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.interval("[获取当前登录人所有指标]"));

        return moduleList;
    }

    /**
     * 获取指标点评项
     *
     * @param targetId 指标id
     * @return
     */
    @Override
    public List<TemplateInfoSaveDTO> getTargetOption(Long targetId) {
        TargetTemplate targetTemplate = templateService.getByTargetId(targetId);

        List<TemplateInfoSaveDTO> list = new ArrayList<>();
        for (TemplateInfoSaveDTO templateInfoSaveDTO : targetTemplate.getTemplateInfoList()) {
            // 如果是单选和多选
            if (CollUtil.newArrayList("radio", "checkbox").contains(templateInfoSaveDTO.getType())) {
                list.add(templateInfoSaveDTO);
            }
            // 如果是明细
            if ("card".equals(templateInfoSaveDTO.getType())) {
                if (CollUtil.isEmpty(templateInfoSaveDTO.getList())) {
                    continue;
                }
                // 明细列表
                List<LinkedHashMap> detailList = new ArrayList<>();
                // 判断明细下的控件是否为单选和多选
                for (LinkedHashMap linkedHashMap : templateInfoSaveDTO.getList()) {
                    if (CollUtil.newArrayList("radio", "checkbox").contains(Convert.toStr(linkedHashMap.get("type")))) {
                        detailList.add(linkedHashMap);
                    }
                }
                templateInfoSaveDTO.setList(detailList);
                list.add(templateInfoSaveDTO);
            }
        }
        return list;
    }

    /**
     * 图文点评设置
     */
    @Override
    public void setPictureEvaluate(TargetIdQuery query) {
        if (BeanUtil.isEmpty(query) || CollUtil.isEmpty(query.getTargetIds())) {
            log.info("[图文点评设置]-[指标列表为空]-直接返回");
            return;
        }
        sysTargetService.setPictureEvaluate(query);
    }

    /**
     * 图文点评指标列表
     */
    @Override
    public List<Long> listPictureEvaluates() {
        return sysTargetService.listPictureEvaluates();
    }

    /**
     * 指标树（不带选项）
     */
    @Override
    public List<ModuleInfoResponse> listGroupTargets() {
        Set<Integer> moduleCodes = ModuleEnum.normalModuleMap.keySet();

        // 获取分组信息
        List<GroupInfoResponse> groupInfos = listGroupInfo();

        List<ModuleInfoResponse> list = new ArrayList<>();
        for (Integer moduleCode : moduleCodes) {
            ModuleInfoResponse ruleModuleInfoVO = new ModuleInfoResponse();
            ruleModuleInfoVO.setId(moduleCode);
            ruleModuleInfoVO.setName(ModuleEnum.getModuleName(moduleCode));
            ruleModuleInfoVO.setModuleCode(moduleCode);
            ruleModuleInfoVO.setModuleName(ModuleEnum.getModuleName(moduleCode));
            ruleModuleInfoVO.setSubmitType(MedalTargetTypeEnum.MODULE_CODE.getCode());
            List<GroupInfoResponse> infoVOS = groupInfos.stream().filter(s -> moduleCode.equals(s.getModuleCode())).collect(Collectors.toList());
            ruleModuleInfoVO.setChildren(infoVOS);
            list.add(ruleModuleInfoVO);
        }
        return list;
    }

    @Override
    public Long saveSystemRecommendGroupAndTarget(SystemRecommendTargetDTO dto) {

        if (Constant.ONE.equals(dto.getType())) {
            Integer moduleCode = Convert.toInt(dto.getParentId());
            SysTargetGroup sysTargetGroup = new SysTargetGroup();
            sysTargetGroup.setModuleCode(moduleCode);
            sysTargetGroup.setModuleName(ModuleEnum.getModuleName(moduleCode));
            sysTargetGroup.setGroupName(dto.getName());
            sysTargetGroup.setDataSource(dto.getDataSource());
            sysTargetGroup.setGroupType(TargetTypeEnum.SYSTEM_RECOMMEND.getCode());
            sysTargetGroup.setId(SnowFlakeIdUtil.nextId());
            sysTargetGroup.setCreateBy(WebUtil.getStaffId());
            sysTargetGroup.setCreateTime(new Date());
            sysTargetGroup.setDeleted(Boolean.FALSE);
            int count = (int) sysTargetGroupManager.count(new LambdaQueryWrapper<SysTargetGroup>()
                    .eq(SysTargetGroup::getModuleCode, moduleCode)
                    .eq(SysTargetGroup::getDeleted, Boolean.FALSE));
            sysTargetGroup.setSortIndex(count + 1);
            sysTargetGroupManager.save(sysTargetGroup);
            return sysTargetGroup.getId();
        } else if (Constant.TWO.equals(dto.getType())) {
            Long groupId = Convert.toLong(dto.getParentId());
            List<SysTarget> sysTargets = sysTargetManager.list(new LambdaQueryWrapper<SysTarget>()
                    .eq(SysTarget::getGroupId, groupId));

            List<SysTarget> pictureEvaluates = sysTargets.stream()
                    .filter(item -> Constant.YES.equals(item.getPictureEvaluateFlag()))
                    .collect(Collectors.toList());

            SysTarget sysTarget = new SysTarget();
            sysTarget.setIconUrl("icon/target/target-icon-1.png");
            sysTarget.setTargetName(dto.getName());
            sysTarget.setTargetStatus(Constant.NO);
            sysTarget.setGroupId(groupId);

            sysTarget.setSubmitType(Constant.FIVE);
//            sysTarget.setSubmitDate();
//            sysTarget.setRemindTime();
//            sysTarget.setHolidayNoticeFlag();
//            sysTarget.setMustSubmitFlag();
//            sysTarget.setUrgeTime();

            sysTarget.setSortIndex(sysTargets.size() + 1);
            sysTarget.setDataSource(dto.getDataSource());
//            sysTarget.setScoreControlName();
//            sysTarget.setScoreControlType();
//            sysTarget.setScoreValue();
//            sysTarget.setScore();
            sysTarget.setPictureEvaluateFlag(Constant.YES);
            sysTarget.setSendParentFlag(Constant.ZERO);
            sysTarget.setPictureSortIndex(pictureEvaluates.size() + 1);
            sysTarget.setTargetType(TargetTypeEnum.SYSTEM_RECOMMEND.getCode());
            sysTarget.setId(SnowFlakeIdUtil.nextId());
            sysTarget.setCreateBy(WebUtil.getStaffId());
            sysTarget.setCreateTime(new Date());
            sysTarget.setDeleted(Boolean.FALSE);

            TargetTemplate template = new TargetTemplate();
            template.setTargetId(sysTarget.getId());
            template.setTemplateInfoList(dto.getTemplateInfoList());
            TargetTemplate resultTargetTemplate = templateDao.save(template);
            sysTarget.setTemplateId(resultTargetTemplate.getId());
            sysTargetManager.save(sysTarget);
            return sysTarget.getId();
        }
        return null;
    }

    /**
     * 获取分组信息
     *
     * @return
     */
    private List<GroupInfoResponse> listGroupInfo() {
        List<SysTargetGroup> targetGroups = sysTargetGroupManager.list(new LambdaQueryWrapper<SysTargetGroup>()
                .in(SysTargetGroup::getDataSource, CollUtil.newArrayList(DataSourceEnum.EVALUATE.getCode()))
                .eq(SysTargetGroup::getGroupType,TargetTypeEnum.DEFAULT.getCode()));
        if (CollUtil.isEmpty(targetGroups)) {
            return Collections.emptyList();
        }

        List<GroupInfoResponse> list = new ArrayList<>();
        for (SysTargetGroup targetGroup : targetGroups) {
            GroupInfoResponse ruleGroupInfoVO = new GroupInfoResponse();
            ruleGroupInfoVO.setId(targetGroup.getId());
            ruleGroupInfoVO.setName(targetGroup.getGroupName());
            ruleGroupInfoVO.setSubmitType(MedalTargetTypeEnum.GROUP_ID.getCode());
            ruleGroupInfoVO.setModuleCode(targetGroup.getModuleCode());
            ruleGroupInfoVO.setModuleName(ModuleEnum.getModuleName(targetGroup.getModuleCode()));
            List<TargetInfoResponse> targetInfos = listTargetInfo(ruleGroupInfoVO);
            if (CollUtil.isNotEmpty(targetInfos)) {
                ruleGroupInfoVO.setChildren(targetInfos);
                list.add(ruleGroupInfoVO);
            }
        }

        return list;
    }

    /**
     * 获取指标信息(只获取综评指标)
     *
     * @param ruleGroupInfoVO
     * @return
     */
    private List<TargetInfoResponse> listTargetInfo(GroupInfoResponse ruleGroupInfoVO) {
        List<SysTarget> targets = sysTargetManager.list(new LambdaQueryWrapper<SysTarget>()
                .eq(SysTarget::getGroupId, ruleGroupInfoVO.getId())
                .eq(SysTarget::getTargetStatus, Constant.YES)
                .in(SysTarget::getDataSource, CollUtil.newArrayList(DataSourceEnum.EVALUATE.getCode()))
                .eq(SysTarget::getTargetType, TargetTypeEnum.DEFAULT.getCode())
                .orderByAsc(SysTarget::getSortIndex));
        if (CollUtil.isEmpty(targets)) {
            return Collections.emptyList();
        }
        // 获取华为云链接前缀
        String iconUrlPrefix = sysTargetService.getIconUrlPrefix();

        List<TargetInfoResponse> list = new ArrayList<>();
        for (SysTarget target : targets) {
            TargetInfoResponse ruleTargetInfoVO = new TargetInfoResponse();
            ruleTargetInfoVO.setModuleCode(ruleGroupInfoVO.getModuleCode());
            ruleTargetInfoVO.setModuleName(ruleGroupInfoVO.getModuleName());
            ruleTargetInfoVO.setParentId(ruleGroupInfoVO.getId());
            ruleTargetInfoVO.setParentName(ruleGroupInfoVO.getName());
            ruleTargetInfoVO.setId(target.getId());
            ruleTargetInfoVO.setName(target.getTargetName());
            ruleTargetInfoVO.setIconUrl(iconUrlPrefix + target.getIconUrl());
            ruleTargetInfoVO.setSubmitType(MedalTargetTypeEnum.TARGET_ID.getCode());
            list.add(ruleTargetInfoVO);
        }
        return list;
    }

    /**
     * 查询当前登录人的所有填报指标(老师)
     *
     * @return
     */
    private List<ListAllEvaluateTargetVOModule> queryStaffInfoModules() {
        ListOrgIdAndRoleIdVO listOrgRole = basicInfoService.listOrgIdAndRoleId();
        if (ObjectUtil.isNull(listOrgRole)) {
            return null;
        }
        // 合并组织机构id集合和角色id集合
        List<String> orgIdRoleIdList = new ArrayList<>();
        orgIdRoleIdList.addAll(listOrgRole.getOrgIdList());
        orgIdRoleIdList.addAll(listOrgRole.getRoleIdList());
        // 添加当前教职工的staffId
        orgIdRoleIdList.add(WebUtil.getStaffId());

        listAllEvaluateTargetDaoDTO query = new listAllEvaluateTargetDaoDTO()
                .setSubmitValueList(orgIdRoleIdList);
        // 当前登录人所有模块，所有分组，所有指标
        List<ListAllEvaluateTargetVOModule> moduleList = sysTargetGroupMapper.listAllEvaluateTarget(query);
        return moduleList;
    }

    /**
     * 查询当前登录人的所有填报指标(家长)
     *
     * @return
     */
    private List<ListAllEvaluateTargetVOModule> queryStudentInfoModules() {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.start("[查询当前登录人的所有填报指标(家长)]");

        List<Long> studentOrgIds = basicInfoService.listRelationInfo(WebUtil.getStudentIdStr());

        log.info("[查询当前登录人的所有填报指标(家长)-[根据学生id获取层级关系(有关系的链路id集合)]]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.interval("[查询当前登录人的所有填报指标(家长)]"));
        //去重
        List<String> collect = studentOrgIds.stream().map(Convert::toStr).distinct().collect(Collectors.toList());
        //加上当前学生id
        collect.add(WebUtil.getStudentIdStr());

        listAllEvaluateTargetDaoDTO query = new listAllEvaluateTargetDaoDTO()
                .setSubmitValueList(collect);
        // 当前登录人所有模块，所有分组，所有指标
        // todo 查询参数学校id，校区id目前没加,待优化
        List<ListAllEvaluateTargetVOModule> moduleList = sysTargetGroupMapper.listParentAllEvaluateTarget(query);


        log.info("[查询当前登录人的所有填报指标(家长)]<<<<<<<<<<<<<<<<<<<,消耗时长:[{}]", timeInterval.interval("[查询当前登录人的所有填报指标(家长)]"));

        return moduleList;
    }
}
