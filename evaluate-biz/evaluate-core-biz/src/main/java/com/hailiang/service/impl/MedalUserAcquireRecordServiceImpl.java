package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hailiang.constant.Constant;
import com.hailiang.constant.SaasConstant;
import com.hailiang.convert.MedalConvert;
import com.hailiang.enums.SaasClassOrgTypeEnum;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.enums.TimeEnum;
import com.hailiang.enums.medal.DateToCNEnum;
import com.hailiang.enums.medal.MedalAwardStatusEnum;
import com.hailiang.enums.medal.MedalRuleTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.manager.*;
import com.hailiang.model.dto.activity.detail.MedalActivityInfoVO;
import com.hailiang.model.entity.*;
import com.hailiang.model.medal.dto.MedalStatisticsQueryDTO;
import com.hailiang.model.medal.dto.StudentMedalQueryDTO;
import com.hailiang.model.medal.dto.StudentRankQueryDTO;
import com.hailiang.model.medal.vo.*;
import com.hailiang.model.vo.ListOrgIdAndRoleIdVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentClassQueryDTO;
import com.hailiang.remote.saas.dto.staff.StaffClassDTO;
import com.hailiang.remote.saas.dto.student.StudentByIdQuery;
import com.hailiang.remote.saas.dto.student.UcStudentClassQuery;
import com.hailiang.remote.saas.pojo.educational.EduClassBaseInfoPojo;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.remote.saas.vo.educational.EduStudentClassVO;
import com.hailiang.remote.saas.vo.educational.GlobalEduOrgTreeVO;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.saas.SaasHistoryStudentCacheManager;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.CommonService;
import com.hailiang.service.MedalUserAcquireRecordService;
import com.hailiang.util.RequestUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: JJL
 * @Date: 2023/6/16 9:53
 */

@Slf4j
@Service
public class MedalUserAcquireRecordServiceImpl implements MedalUserAcquireRecordService {
    @Autowired
    private MedalUserAcquireRecordManager medalUserAcquireRecordManager;
    @Autowired
    private BasicInfoRemote basicInfoRemote;
    @Autowired
    private MedalInfoManager medalInfoManager;
    @Autowired
    private BasicInfoService basicInfoService;
    @Resource
    private MedalConvert convert;
    @Autowired
    private MedalActivityGradeManager medalActivityGradeManager;
    @Autowired
    private MedalActivityManager medalActivityManager;
    @Autowired
    private MedalCatalogueManager medalCatalogueManager;
    @Autowired
    private MedalLogoManager medalLogoManager;
    @Autowired
    private MedalRecordUserManager medalRecordUserManager;
    @Autowired
    private MedalTaskCompletionRelationManager medalTaskCompletionRelationManager;
    @Autowired
    CommonService commonService;
    @Resource
    private MedalTaskManager medalTaskManager;
    @Resource
    private SaasHistoryStudentCacheManager saasHistoryStudentCacheManager;

    @Value("${oss.urlPrefix}")
    private String urlPrefix;

    private final static String CONTENT = "恭喜您的孩子{0}在{1}活动中完成{2}获得{3}1枚";
    private final static String RULE_TYPE_COUNT_NAME = "{0}{1}次";
    private final static String RULE_TYPE_SCORE_NAME = "{0}{1}分";

    /**
     * 获取该校区下所有班级（除去幼儿园）
     *
     * @return
     */
    @Override
    public List<EduOrgTreeVO> listClassByCampusId() {
        //获取当前登录教职工所有组织机构id和角色id
        ListOrgIdAndRoleIdVO listOrgIdAndRoleIdVO = basicInfoService.listOrgIdAndRoleId();
        List<String> roleCodeList;
        if (Objects.nonNull(listOrgIdAndRoleIdVO) && Objects.nonNull(listOrgIdAndRoleIdVO.getRoleCodeList())
                && !listOrgIdAndRoleIdVO.getOrgIdList().isEmpty()) {
            roleCodeList = listOrgIdAndRoleIdVO.getRoleCodeList();
        } else {
            log.info("当前登录人没有配置角色，返回空值");
            return new ArrayList<>();
        }
        log.info("当前登录人具有的角色{}", JSONUtil.toJsonStr(roleCodeList));

        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(WebUtil.getCampusId()));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.CAMPUS.getCode());

        List<EduOrgTreeVO> eduOrgTreeVOS;
        //saas默认角色code未配置，暂时先采用名称匹配，若saas后续配置，更换为code
        eduOrgTreeVOS = basicInfoService.checkOrdinaryPermission(roleCodeList, eduOrgQueryDTO);

        eduOrgQueryDTO.setIsTree(Constant.ONE);
        List<EduOrgTreeVO> supEduOrgTreeVOS = basicInfoService.checkSupPermission(roleCodeList, eduOrgQueryDTO);

        if (CollUtil.isNotEmpty(supEduOrgTreeVOS)) {
            eduOrgTreeVOS = supEduOrgTreeVOS;
            log.info("管理员角色，返回管理员的权限");
        } else {
            log.info("普通用户角色，返回普通用户的权限");
        }
        if (CollectionUtil.isEmpty(eduOrgTreeVOS)) {
            log.info("eduOrgTreeVOS为空，直接返回");
            return Collections.EMPTY_LIST;
        }
        List<EduOrgTreeVO> sectionEduOrgTreeVOS = eduOrgTreeVOS.get(Constant.ZERO).getChildren();
        if (CollectionUtil.isEmpty(sectionEduOrgTreeVOS)) {
            log.info("sectionEduOrgTreeVOS为空，直接返回");
            return Collections.EMPTY_LIST;
        }
        //去除幼儿园
        List<EduOrgTreeVO> eduOrgTreeVOList = sectionEduOrgTreeVOS.parallelStream().filter(s -> !Constant.ONE.equals(Convert.toInt(s.getCode()))).collect(Collectors.toList());
        //所有年级
        List<EduOrgTreeVO> gradeEduOrgTreeVOS = eduOrgTreeVOList.stream().filter(Objects::nonNull).map(EduOrgTreeVO::getChildren).filter(Objects::nonNull)
                .collect(Collectors.toList()).stream().collect(ArrayList::new, ArrayList::addAll, ArrayList::addAll);
        //所有班级
        List<EduOrgTreeVO> classEduOrgTreeVOS = gradeEduOrgTreeVOS.stream().filter(Objects::nonNull).map(EduOrgTreeVO::getChildren).filter(Objects::nonNull)
                .collect(Collectors.toList()).stream().collect(ArrayList::new, ArrayList::addAll, ArrayList::addAll);

        log.info("最终返回给前端的结果：{}", JSONUtil.toJsonStr(classEduOrgTreeVOS));
        return classEduOrgTreeVOS;
    }

    /**
     * 获章总数、获章人数、获章列表
     *
     * @param dto
     * @return
     */
    @Override
    public MedalStatisticsVO listMedals(MedalStatisticsQueryDTO dto) {
        dto.setType(8);
        //时间校验
        validateTime(dto.getStartTime(), dto.getEndTime(), dto.getType());
        //入参校验
        // TODO: 2023/7/7   ObjectUtil.hasNull(dto)
        Assert.isFalse(Objects.isNull(dto.getClassId()) || Objects.isNull(dto.getGradeId()) || Objects.isNull(dto.getStartTime())
                        || Objects.isNull(dto.getEndTime()) || Objects.isNull(dto.getType()),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE.getMessage()));

        MedalStatisticsVO medalStatisticsVO = new MedalStatisticsVO();
        ArrayList<MedalInfoVO> medalInfoVOS = new ArrayList<>();

        //获取年级信息
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(dto.getCampusSectionId()));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
        eduOrgQueryDTO.setSchoolYear(dto.getSchoolYear());
        eduOrgQueryDTO.setRoleFilter(Boolean.FALSE);
        List<GlobalEduOrgTreeVO> eduOrgTreeVOS = basicInfoService.queryGlobalEducationalOrgTree(eduOrgQueryDTO);
        if (CollectionUtil.isEmpty(eduOrgTreeVOS)) {
            return null;
        }
        //该年级下所有班级
        List<GlobalEduOrgTreeVO> classEduOrgTreeVOS = getAllClassList(eduOrgTreeVOS,Long.valueOf(dto.getGradeId()));
        if (CollectionUtil.isEmpty(classEduOrgTreeVOS)) {
            return null;
        }
        dealGradeAndClassData(dto);
        //班级所有学生
        //从saas获取该学段(年级，班级)所有正常状态学生（分页获取total）
        List<SassStudentVO> ucStudentClassBffVOS;
        if (Objects.nonNull(dto.getIsCurrentYear()) && Boolean.FALSE.equals(dto.getIsCurrentYear())) {
            //历史学生
            ucStudentClassBffVOS = saasHistoryStudentCacheManager.listHisStudentByGradeIdAndClassId(WebUtil.getSchoolIdLong(), dto.getSchoolYear(),
                    WebUtil.getCampusIdLong(), Convert.toLong(dto.getGradeId()), Convert.toLong(dto.getClassId()), Convert.toLong(dto.getCampusSectionId()));
        } else {
            //从saas获取该学段所有学生
            ucStudentClassBffVOS = listTotalSaasStudent(Convert.toLong(dto.getCampusSectionId()), dto.getSchoolYear(),
                    Convert.toLong(dto.getGradeId()), Convert.toLong(dto.getClassId()));
        }
        if (CollectionUtil.isEmpty(ucStudentClassBffVOS)) {
            return null;
        }

        //年级所有奖章
        List<MedalUserAcquireRecord> gradeRecordList = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .select(MedalUserAcquireRecord::getClassId, MedalUserAcquireRecord::getStudentId, MedalUserAcquireRecord::getMedalInfoId, MedalUserAcquireRecord::getMedalActivityId)
                .eq(MedalUserAcquireRecord::getTenantId, WebUtil.getTenantId())
                .eq(MedalUserAcquireRecord::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalUserAcquireRecord::getCampusId, WebUtil.getCampusId())
                .eq(StringUtils.isNotEmpty(dto.getGradeId()), MedalUserAcquireRecord::getGradeId, dto.getGradeId())//年级id
                .in(CollUtil.isNotEmpty(dto.getGradeIds()), MedalUserAcquireRecord::getGradeId, dto.getGradeIds())//年级ids
                .eq(MedalUserAcquireRecord::getCampusSectionId, dto.getCampusSectionId())//学段id
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_COMPLETION.getCode())
                .between(MedalUserAcquireRecord::getAwardDate, dto.getStartTime(), dto.getEndTime())
        );
        if (CollectionUtil.isEmpty(gradeRecordList)) {
            medalStatisticsVO.setMedalCount(Constant.ZERO);
            medalStatisticsVO.setMedalInfoVOS(Collections.EMPTY_LIST);
            medalStatisticsVO.setClassRank(classEduOrgTreeVOS.size());
            medalStatisticsVO.setClassCount(classEduOrgTreeVOS.size());
            medalStatisticsVO.setMedalStudentCount(Constant.ZERO);
            medalStatisticsVO.setClassStudentCount(ucStudentClassBffVOS.size());
            return medalStatisticsVO;
        }


        for (MedalUserAcquireRecord medalUserAcquireRecord : gradeRecordList) {
            if (ObjectUtil.isNull(medalUserAcquireRecord.getMedalInfoId())) {
                Long medalActivityId = medalUserAcquireRecord.getMedalActivityId();
                Long medalInfoId = medalTaskManager.getByActivityId(medalActivityId);
                medalUserAcquireRecord.setMedalInfoId(medalInfoId);
            }
        }

        //各个班级的奖章数量
        Map<String, Long> classMedalCountMap = gradeRecordList.stream().collect(Collectors.groupingBy(MedalUserAcquireRecord::getClassId, Collectors.counting()));

        // 排除休学人数
        List<Long> studentIds = ucStudentClassBffVOS.stream().map(SassStudentVO::getStudentId).distinct().collect(Collectors.toList());
        gradeRecordList = gradeRecordList.stream().filter(item -> studentIds.contains(Convert.toLong(item.getStudentId()))).collect(Collectors.toList());

        //该班级所有奖章记录
        List<MedalUserAcquireRecord> classRecordList;
        if (StringUtils.isEmpty(dto.getClassId())) {
            classRecordList = gradeRecordList.stream().filter(s -> dto.getClassIds().contains(s.getClassId())).collect(Collectors.toList());
        } else {
            classRecordList = gradeRecordList.stream().filter(s -> dto.getClassId().equals(s.getClassId())).collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(classRecordList)) {
            medalStatisticsVO.setMedalInfoVOS(Collections.EMPTY_LIST);
            medalStatisticsVO.setClassRank(classMedalCountMap.keySet().size() + Constant.ONE);
            medalStatisticsVO.setMedalStudentCount(Constant.ZERO);
        } else {
            //奖章id分组以及其数量
            Map<Long, Long> medalInfoIdAndCount = classRecordList.stream().filter(s -> ObjectUtil.isNotNull(s.getMedalInfoId())).collect(Collectors.groupingBy(MedalUserAcquireRecord::getMedalInfoId, Collectors.counting()));
            Set<Long> medalInfoIds = medalInfoIdAndCount.keySet();
            List<MedalInfoVO> medalInfoList = medalInfoManager.listInfoByIds(Convert.toList(Long.class, medalInfoIds));
            Iterator<Map.Entry<Long, Long>> iterator = medalInfoIdAndCount.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, Long> next = iterator.next();
                MedalInfoVO medalInfoVO = new MedalInfoVO();
                medalInfoVO.setId(next.getKey());
                medalInfoVO.setName(medalInfoList.stream().filter(s -> next.getKey().equals(s.getId())).collect(Collectors.toList()).get(Constant.ZERO).getName());
                medalInfoVO.setLogoUrl(urlPrefix + medalInfoList.stream().filter(s -> next.getKey().equals(s.getId())).collect(Collectors.toList()).get(Constant.ZERO).getLogoUrl());
                medalInfoVO.setMedalCount(Convert.toInt(next.getValue()));
                medalInfoVOS.add(medalInfoVO);
            }
            medalStatisticsVO.setMedalInfoVOS(medalInfoVOS);
            medalStatisticsVO.setMedalStudentCount(classRecordList.stream().map(MedalUserAcquireRecord::getStudentId).distinct().collect(Collectors.toList()).size());
            //班级排名
            int classRank = getClassRank(classMedalCountMap,dto.getClassId());
            medalStatisticsVO.setClassRank(classRank);
        }

        medalStatisticsVO.setMedalCount(classRecordList.size());
        medalStatisticsVO.setClassCount(classEduOrgTreeVOS.size());
        medalStatisticsVO.setClassStudentCount(ucStudentClassBffVOS.size());

        return medalStatisticsVO;
    }

    /**
     * 获取组织树中所有班级信息
     * @param eduOrgTreeVOS 原组织树
     * @param gradeId 年级id
     * @return
     */
    private List<GlobalEduOrgTreeVO> getAllClassList(List<GlobalEduOrgTreeVO> eduOrgTreeVOS, Long gradeId) {
        if(CollectionUtil.isEmpty(eduOrgTreeVOS)){
            return Collections.emptyList();
        }
        for (GlobalEduOrgTreeVO eduOrgTreeVO : eduOrgTreeVOS) {
            if(Objects.equals(eduOrgTreeVO.getType(),SaasClassOrgTypeEnum.GRADE.getCode())
             && Objects.equals(eduOrgTreeVO.getId(),gradeId)){
                return eduOrgTreeVO.getChildren();
            }else if (eduOrgTreeVO.getType() < SaasClassOrgTypeEnum.GRADE.getCode()){
                //大于年级的分类
                List<GlobalEduOrgTreeVO> allClassList = getAllClassList(eduOrgTreeVO.getChildren(), gradeId);
                if(CollectionUtil.isNotEmpty(allClassList)){
                    return allClassList;
                }
            }
        }
        return Collections.emptyList();
    }

    /**
     * 年级信息
     *
     * @param dto
     * @return
     */
    private List<EduOrgTreeVO> getEduOrgTreeVOS(MedalStatisticsQueryDTO dto) {
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(dto.getGradeId()));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.GRADE.getCode());
        return basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
    }

    /**
     * 从saas获取该学校下学段及以下的教务组织架构树形
     *
     * @param type
     * @param campusSectionId
     * @param gradeId
     * @param classId
     * @return
     */
    private List<EduOrgTreeVO> listEduOrgTree(Integer type, String campusSectionId, String gradeId, String classId) {
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        if (SaasCurrentIdTypeEnum.SECTION.getCode().equals(type)) {
            eduOrgQueryDTO.setCurrentId(Convert.toLong(campusSectionId));
        } else if (SaasCurrentIdTypeEnum.GRADE.getCode().equals(type)) {
            eduOrgQueryDTO.setCurrentId(Convert.toLong(gradeId));
        } else if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(type)) {
            eduOrgQueryDTO.setCurrentId(Convert.toLong(classId));
        }
        eduOrgQueryDTO.setCurrentIdType(type);
        //从saas获取数据
        List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        return eduOrgTreeVOS;
    }

    /**
     * saas获取班级信息
     *
     * @param dto
     * @return
     */
    private List<UcStudentClassBffVO> listClassStudents(MedalStatisticsQueryDTO dto) {
        UcStudentClassQuery ucStudentClassQuery = new UcStudentClassQuery();
        ucStudentClassQuery.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
        ucStudentClassQuery.setGradeId(Convert.toLong(dto.getGradeId()));
        ucStudentClassQuery.setClassId(Convert.toLong(dto.getClassId()));
        ucStudentClassQuery.setStudentStatus(Convert.toStr(Constant.ZERO));
        ucStudentClassQuery.setUpgradeStatus(Convert.toStr(Constant.ZERO));
        ucStudentClassQuery.setPageSize(Constant.THOUSAND);
        List<UcStudentClassBffVO> ucStudentClassBffVOS = basicInfoRemote.queryClassStudentPageByCondition(ucStudentClassQuery).getList();
        List<UcStudentClassBffVO> ucStudentClassBffVOList = JSONUtil.toList(JSONUtil.toJsonStr(ucStudentClassBffVOS), UcStudentClassBffVO.class);
        ucStudentClassBffVOList = ucStudentClassBffVOList.stream().filter(s -> Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
        return ucStudentClassBffVOList;
    }

    /**
     * 年级下各个班级的奖章个数排名方法，同分通名次，获取各个班级的奖章个数排名
     *
     * @param classMedalCountMap
     * @param key
     * @return
     */
    private int getClassRank(Map<String, Long> classMedalCountMap, String key) {
        classMedalCountMap.values().removeIf(Objects::isNull);
        List<Map.Entry<String, Long>> list = classMedalCountMap.entrySet().stream().filter(Objects::nonNull).sorted(Map.Entry.<String, Long>comparingByValue().reversed()).filter(Objects::nonNull).collect(Collectors.toList());
        Long preValue = Long.MIN_VALUE;
        int index = 0;
        for (Map.Entry<String, Long> stringLongEntry : list) {
            if(!Objects.equals(stringLongEntry.getValue(),preValue)){
                //与前一位不同
                preValue = stringLongEntry.getValue();
                index ++;
            }
            if(StringUtils.equals(key,stringLongEntry.getKey())){
                return index;
            }
        }
        return 0;
    }

    /**
     * 年级下各个班级的奖章个数排名方法，同分通名次，获取各个班级的奖章个数排名
     *
     * @param classMedalCountMap
     * @param count
     * @return
     */
    private int getClassRank(Map<String, Long> classMedalCountMap, Long count) {
        classMedalCountMap.values().removeIf(Objects::isNull);
        List<Map.Entry<String, Long>> list = classMedalCountMap.entrySet().stream().filter(Objects::nonNull).sorted(Map.Entry.<String, Long>comparingByValue().reversed()).filter(Objects::nonNull).collect(Collectors.toList());
        ArrayList<Long> arrayList = new ArrayList();
        Map map = new HashMap();
        //将需要排序的字段放入集合
        for (int p = 0; p < list.size(); p++) {
            arrayList.add(list.get(p).getValue());
        }
        //用户成绩为key，循坏下标为value生成map
        for (int i = 0; i < arrayList.size(); i++) {
            if (i == 0) {
                map.put(arrayList.get(0), 0 + 1);
            }
            if (!map.containsKey(arrayList.get(i))) {
                map.put(arrayList.get(i), i + 1);
            }
        }
        //从map中取得对应的位置
        return (int) Optional.ofNullable(map.get(count)).orElse(0);
    }

    /**
     * 时间校验
     *
     * @param startTime
     * @param endTime
     */
    private void validateTime(Date startTime, Date endTime, Integer type) {
        //时间校验
        Assert.isFalse(startTime.after(endTime), () -> new BizException(BizExceptionEnum.ACTIVITY_EDIT_TIME_ERROR.getMessage()));
//        Assert.isFalse(Convert.toInt(DateUtil.betweenDay(startTime, endTime, true)) > Constant.THIRTY_ONE,
//                () -> new BizException(BizExceptionEnum.MONTH_TIME_ERROR.getMessage()));
//        Assert.isFalse(!TimeEnum.MONTH.getCode().equals(type) && !TimeEnum.WEEK.getCode().equals(type) && !TimeEnum.TIME_PERIODS.getCode().equals(type), () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE.getMessage()));
    }


    /**
     * 获章趋势
     *
     * @param dto
     * @return
     */
    @Override
    public List<MedalTrendVO> listMedalTrend(MedalStatisticsQueryDTO dto) {
        dto.setType(8);
        //时间校验
        validateTime(dto.getStartTime(), dto.getEndTime(), dto.getType());
        //入参校验
        Assert.isFalse(Objects.isNull(dto.getClassId()) || Objects.isNull(dto.getGradeId()) || Objects.isNull(dto.getStartTime())
                        || Objects.isNull(dto.getEndTime()) || Objects.isNull(dto.getType()),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE.getMessage()));

        //获取年级信息
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(dto.getCampusSectionId()));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
        eduOrgQueryDTO.setSchoolYear(dto.getSchoolYear());
        eduOrgQueryDTO.setRoleFilter(Boolean.FALSE);
        List<GlobalEduOrgTreeVO> eduOrgTreeVOS = basicInfoService.queryGlobalEducationalOrgTree(eduOrgQueryDTO);
        if (CollectionUtil.isEmpty(eduOrgTreeVOS)) {
            return Collections.EMPTY_LIST;
        }
        //该年级下所有班级
        List<GlobalEduOrgTreeVO> classEduOrgTreeVOS = eduOrgTreeVOS.get(Constant.ZERO).getChildren();
        if (CollectionUtil.isEmpty(classEduOrgTreeVOS)) {
            return Collections.EMPTY_LIST;
        }
        dealGradeAndClassData(dto);

        //开始时间和结束时间
        Date startTime = dto.getStartTime();
        Date endTime = dto.getEndTime();

        //年级所有奖章
        List<MedalUserAcquireRecord> gradeRecordList = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .select(MedalUserAcquireRecord::getId, MedalUserAcquireRecord::getClassId, MedalUserAcquireRecord::getAwardDate)
                .eq(MedalUserAcquireRecord::getTenantId, WebUtil.getTenantId())
                .eq(MedalUserAcquireRecord::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalUserAcquireRecord::getCampusId, WebUtil.getCampusId())
                .eq(StringUtils.isNotEmpty(dto.getGradeId()), MedalUserAcquireRecord::getGradeId, dto.getGradeId())//年级id
                .in(CollUtil.isNotEmpty(dto.getGradeIds()), MedalUserAcquireRecord::getGradeId, dto.getGradeIds())//年级ids
                .eq(MedalUserAcquireRecord::getCampusSectionId, dto.getCampusSectionId())//学段id
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_COMPLETION.getCode())
                .between(MedalUserAcquireRecord::getAwardDate, startTime, endTime)
        );

        ArrayList<MedalTrendVO> medalTrendVOS = new ArrayList<>();

        if (TimeEnum.MONTH.getCode().equals(dto.getType())) {
            //本月
            return listMonthMedalTrendVOS(dto, classEduOrgTreeVOS, gradeRecordList, medalTrendVOS);
        } else if (TimeEnum.WEEK.getCode().equals(dto.getType())) {
            //本周
            return listWeekMedalTrendVOS(dto, classEduOrgTreeVOS, gradeRecordList, medalTrendVOS);
        } else if (TimeEnum.TIME_PERIODS.getCode().equals(dto.getType())) {
            //选择时间段
            return listPeriodMedalTrendVOS(dto, classEduOrgTreeVOS, startTime, endTime, gradeRecordList, medalTrendVOS);
        }
        return medalTrendVOS;
    }

    /**
     * 获章趋势时间段
     *
     * @param dto
     * @param classEduOrgTreeVOS
     * @param startTime
     * @param endTime
     * @param gradeRecordList
     * @param medalTrendVOS
     * @return
     */
    private ArrayList<MedalTrendVO> listPeriodMedalTrendVOS(MedalStatisticsQueryDTO dto, List<GlobalEduOrgTreeVO> classEduOrgTreeVOS, Date startTime, Date endTime, List<MedalUserAcquireRecord> gradeRecordList, ArrayList<MedalTrendVO> medalTrendVOS) {
        //相差的天数
        int betweenDay = Convert.toInt(DateUtil.betweenDay(startTime, endTime, true));
        if (Constant.SIX >= betweenDay) {
            //小于等于七天
            for (int i = 0; i <= betweenDay; i++) {
                MedalTrendVO medalTrendVO = new MedalTrendVO();
                DateTime beginOfDay = DateUtil.offsetDay(startTime, i);
                DateTime endOfDay = DateUtil.endOfDay(beginOfDay);
                //日范围内年级的获章记录
                getDayRecords(dto, classEduOrgTreeVOS, gradeRecordList, medalTrendVOS, medalTrendVO, beginOfDay, endOfDay);
            }
            return medalTrendVOS;
        }
        HashMap<String, Date> map = new HashMap<>();
        //大于七天小于30天
        if (Constant.THIRTY_ONE >= betweenDay) {
            return getWeekMedalTrendVOS(dto, classEduOrgTreeVOS, startTime, endTime, gradeRecordList, medalTrendVOS, map);
        } else if (Constant.THIRTY_ONE < betweenDay && Constant.DAY_NUMBER_OF_YEAR >= betweenDay) {//大于30天小于一年
            return getMonthMedalTrendVOS(dto, classEduOrgTreeVOS, startTime, endTime, gradeRecordList, medalTrendVOS, map);
        } else {//大于一年的
            return getYearMedalTrendVOS(dto, classEduOrgTreeVOS, startTime, endTime, gradeRecordList, medalTrendVOS, map);
        }
    }

    private ArrayList<MedalTrendVO> getYearMedalTrendVOS(MedalStatisticsQueryDTO dto, List<GlobalEduOrgTreeVO> classEduOrgTreeVOS, Date startTime, Date endTime, List<MedalUserAcquireRecord> gradeRecordList, ArrayList<MedalTrendVO> medalTrendVOS, HashMap<String, Date> map) {
        map.put("beginOfDay", startTime);
        map.put("endOfDay", DateUtil.endOfDay(DateUtil.offsetDay(startTime, Constant.DAY_NUMBER_OF_YEAR)));
        while (map.get("endOfDay").before(endTime)) {
            getYearPeriodRecords(dto, classEduOrgTreeVOS, map.get("endOfDay"), gradeRecordList, medalTrendVOS, map);
            map.put("beginOfDay", DateUtil.beginOfDay(DateUtil.offsetDay(map.get("endOfDay"), Constant.ONE)));
            map.put("endOfDay", DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.beginOfDay(DateUtil.offsetDay(map.get("endOfDay"), Constant.ONE)), Constant.DAY_NUMBER_OF_YEAR)));
        }
        getYearPeriodRecords(dto, classEduOrgTreeVOS, endTime, gradeRecordList, medalTrendVOS, map);
        return medalTrendVOS;
    }

    private ArrayList<MedalTrendVO> getMonthMedalTrendVOS(MedalStatisticsQueryDTO dto, List<GlobalEduOrgTreeVO> classEduOrgTreeVOS, Date startTime, Date endTime, List<MedalUserAcquireRecord> gradeRecordList, ArrayList<MedalTrendVO> medalTrendVOS, HashMap<String, Date> map) {
        map.put("beginOfDay", startTime);
        map.put("endOfDay", DateUtil.endOfDay(DateUtil.offsetDay(startTime, Constant.THIRTY)));
        while (map.get("endOfDay").before(endTime)) {
            getTimePeriodRecords(dto, classEduOrgTreeVOS, map.get("endOfDay"), gradeRecordList, medalTrendVOS, map);
            map.put("beginOfDay", DateUtil.beginOfDay(DateUtil.offsetDay(map.get("endOfDay"), Constant.ONE)));
            map.put("endOfDay", DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.beginOfDay(DateUtil.offsetDay(map.get("endOfDay"), Constant.ONE)), Constant.THIRTY)));
        }
        getTimePeriodRecords(dto, classEduOrgTreeVOS, endTime, gradeRecordList, medalTrendVOS, map);
        return medalTrendVOS;
    }

    private ArrayList<MedalTrendVO> getWeekMedalTrendVOS(MedalStatisticsQueryDTO dto, List<GlobalEduOrgTreeVO> classEduOrgTreeVOS, Date startTime, Date endTime, List<MedalUserAcquireRecord> gradeRecordList, ArrayList<MedalTrendVO> medalTrendVOS, HashMap<String, Date> map) {
        map.put("beginOfDay", startTime);
        map.put("endOfDay", DateUtil.endOfDay(DateUtil.offsetDay(startTime, Constant.SIX)));
        while (map.get("endOfDay").before(endTime)) {
            getTimePeriodRecords(dto, classEduOrgTreeVOS, map.get("endOfDay"), gradeRecordList, medalTrendVOS, map);
            map.put("beginOfDay", DateUtil.beginOfDay(DateUtil.offsetDay(map.get("endOfDay"), Constant.ONE)));
            map.put("endOfDay", DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.beginOfDay(DateUtil.offsetDay(map.get("endOfDay"), Constant.ONE)), Constant.SIX)));
        }
        getTimePeriodRecords(dto, classEduOrgTreeVOS, endTime, gradeRecordList, medalTrendVOS, map);
        return medalTrendVOS;
    }

    /**
     * 时间段之内班级和年级获章数量
     *
     * @param dto
     * @param classEduOrgTreeVOS
     * @param endTime
     * @param gradeRecordList
     * @param medalTrendVOS
     * @param map
     */
    private void getTimePeriodRecords(MedalStatisticsQueryDTO dto, List<GlobalEduOrgTreeVO> classEduOrgTreeVOS, Date endTime, List<MedalUserAcquireRecord> gradeRecordList, ArrayList<MedalTrendVO> medalTrendVOS, HashMap<String, Date> map) {
        MedalTrendVO medalTrendVO = new MedalTrendVO();
        //日范围内年级的获章记录
        List<MedalUserAcquireRecord> gradeRecords = gradeRecordList.stream().filter(s -> s.getAwardDate().after(map.get("beginOfDay")) && s.getAwardDate().before(map.get("endOfDay"))).collect(Collectors.toList());
        //日范围内班级的获章记录
        List<MedalUserAcquireRecord> classRecords;
        if (StringUtils.isEmpty(dto.getClassId())) {
            classRecords = gradeRecords.stream().filter(s -> dto.getClassIds().contains(s.getClassId())).collect(Collectors.toList());
        } else {
            classRecords = gradeRecords.stream().filter(s -> dto.getClassId().equals(s.getClassId())).collect(Collectors.toList());
        }
        //年级平均数量
        int classCount = getClassCount(classEduOrgTreeVOS,Long.valueOf(dto.getGradeId()));
        BigDecimal gradeCount = classCount == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(Convert.toDouble(BigDecimal.valueOf(gradeRecords.size()).divide(BigDecimal.valueOf(classCount), Constant.ONE, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
        medalTrendVO.setName(DateUtil.format(map.get("beginOfDay"), Constant.FORMAL_MONTH_DAY_FORMAT) + Constant.WAVY_LINE + DateUtil.format(endTime, Constant.FORMAL_MONTH_DAY_FORMAT));
        medalTrendVO.setGradeCount(new BigDecimal(gradeCount.stripTrailingZeros().toPlainString()));
        medalTrendVO.setClassCount(classRecords.size());
        medalTrendVOS.add(medalTrendVO);
    }

    /**
     * 跨年的班级和年级获章数量
     *
     * @param dto
     * @param classEduOrgTreeVOS
     * @param endTime
     * @param gradeRecordList
     * @param medalTrendVOS
     * @param map
     */
    private void getYearPeriodRecords(MedalStatisticsQueryDTO dto, List<GlobalEduOrgTreeVO> classEduOrgTreeVOS, Date endTime, List<MedalUserAcquireRecord> gradeRecordList, ArrayList<MedalTrendVO> medalTrendVOS, HashMap<String, Date> map) {
        MedalTrendVO medalTrendVO = new MedalTrendVO();
        //日范围内年级的获章记录
        List<MedalUserAcquireRecord> gradeRecords = gradeRecordList.stream().filter(s -> s.getAwardDate().after(map.get("beginOfDay")) && s.getAwardDate().before(map.get("endOfDay"))).collect(Collectors.toList());
        //日范围内班级的获章记录
        List<MedalUserAcquireRecord> classRecords;
        if (StringUtils.isEmpty(dto.getClassId())) {
            classRecords = gradeRecords.stream().filter(s -> dto.getClassIds().contains(s.getClassId())).collect(Collectors.toList());
        } else {
            classRecords = gradeRecords.stream().filter(s -> dto.getClassId().equals(s.getClassId())).collect(Collectors.toList());
        }
        //年级平均数量
        int classCount = getClassCount(classEduOrgTreeVOS,Long.valueOf(dto.getGradeId()));
        BigDecimal gradeCount = BigDecimal.valueOf(Convert.toDouble(BigDecimal.valueOf(gradeRecords.size()).divide(BigDecimal.valueOf(classCount), Constant.ONE, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
        medalTrendVO.setName(DateUtil.format(map.get("beginOfDay"), Constant.PATTERN_FORMAT) + Constant.WAVY_LINE + DateUtil.format(endTime, Constant.PATTERN_FORMAT));
        medalTrendVO.setGradeCount(new BigDecimal(gradeCount.stripTrailingZeros().toPlainString()));
        medalTrendVO.setClassCount(classRecords.size());
        medalTrendVOS.add(medalTrendVO);
    }

    /**
     * 获章趋势本周
     *
     * @param dto
     * @param classEduOrgTreeVOS
     * @param gradeRecordList
     * @param medalTrendVOS
     * @return
     */
    private ArrayList<MedalTrendVO> listWeekMedalTrendVOS(MedalStatisticsQueryDTO dto, List<GlobalEduOrgTreeVO> classEduOrgTreeVOS, List<MedalUserAcquireRecord> gradeRecordList, ArrayList<MedalTrendVO> medalTrendVOS) {
        DateTime beginOfWeek = DateUtil.beginOfWeek(DateUtil.date());
//            DateTime beginOfWeek = DateUtil.beginOfWeek(dto.getEndTime());
        int thisDayOfWeek = Convert.toInt(DateUtil.betweenDay(beginOfWeek, DateUtil.date(), true)) + Constant.ONE;
//            int thisDayOfWeek = Convert.toInt(DateUtil.betweenDay(beginOfWeek, dto.getEndTime(), true)) + Constant.ONE;
        for (int i = 0; i < thisDayOfWeek; i++) {
            MedalTrendVO medalTrendVO = new MedalTrendVO();
            DateTime beginOfDay = DateUtil.offsetDay(beginOfWeek, i);
            DateTime endOfDay = DateUtil.endOfDay(beginOfDay);
            //日范围内年级的获章记录
            getDayRecords(dto, classEduOrgTreeVOS, gradeRecordList, medalTrendVOS, medalTrendVO, beginOfDay, endOfDay);
        }
        return medalTrendVOS;
    }

    /**
     * 获章趋势本月
     *
     * @param dto
     * @param classEduOrgTreeVOS
     * @param gradeRecordList
     * @param medalTrendVOS
     * @return
     */
    private ArrayList<MedalTrendVO> listMonthMedalTrendVOS(MedalStatisticsQueryDTO dto, List<GlobalEduOrgTreeVO> classEduOrgTreeVOS, List<MedalUserAcquireRecord> gradeRecordList, ArrayList<MedalTrendVO> medalTrendVOS) {
        //当前日期所在月份的第几周
        int thisWeekOfMonth = DateUtil.thisWeekOfMonth();
//      int thisWeekOfMonth = DateUtil.weekOfMonth(dto.getEndTime());
        //大于一周，按本周，一周前展示
        if (Constant.ONE < thisWeekOfMonth) {
            for (int i = 0; i < thisWeekOfMonth; i++) {
                MedalTrendVO medalTrendVO = new MedalTrendVO();
                if (i == thisWeekOfMonth - Constant.ONE) {
                    DateTime beginOfWeek = DateUtil.beginOfMonth(DateUtil.date());
                    DateTime endOfWeek = DateUtil.endOfWeek(beginOfWeek);
                    //周范围之内班级和年级获章数量
                    getWeedRecords(dto, classEduOrgTreeVOS, gradeRecordList, medalTrendVOS, i, medalTrendVO, beginOfWeek, endOfWeek);
                    break;
                }
                DateTime beginOfWeek = DateUtil.beginOfWeek(DateUtil.offsetWeek(DateUtil.date(), -i));
                DateTime endOfWeek = DateUtil.endOfWeek(DateUtil.offsetWeek(DateUtil.date(), -i));
                //周范围之内班级和年级获章数量
                getWeedRecords(dto, classEduOrgTreeVOS, gradeRecordList, medalTrendVOS, i, medalTrendVO, beginOfWeek, endOfWeek);
            }
            return medalTrendVOS;

        }
        //一周按日期展示
        int thisDayOfMonth = DateUtil.thisDayOfMonth();
//        int thisDayOfMonth = DateUtil.dayOfMonth(dto.getEndTime());
        DateTime beginOfMonth = DateUtil.beginOfMonth(DateUtil.date());
        for (int i = 0; i < thisDayOfMonth; i++) {
            MedalTrendVO medalTrendVO = new MedalTrendVO();
            DateTime beginOfDay = DateUtil.offsetDay(beginOfMonth, i);
            DateTime endOfDay = DateUtil.endOfDay(beginOfDay);
            getDayRecords(dto, classEduOrgTreeVOS, gradeRecordList, medalTrendVOS, medalTrendVO, beginOfDay, endOfDay);
        }
        return medalTrendVOS;
    }

    /**
     * 日范围之内班级和年级获章数量
     *
     * @param dto
     * @param classEduOrgTreeVOS
     * @param gradeRecordList
     * @param medalTrendVOS
     * @param medalTrendVO
     * @param beginOfDay
     * @param endOfDay
     */
    private void getDayRecords(MedalStatisticsQueryDTO dto, List<GlobalEduOrgTreeVO> classEduOrgTreeVOS, List<MedalUserAcquireRecord> gradeRecordList, ArrayList<MedalTrendVO> medalTrendVOS, MedalTrendVO medalTrendVO, DateTime beginOfDay, DateTime endOfDay) {
        //日范围内年级的获章记录
        List<MedalUserAcquireRecord> gradeRecords = gradeRecordList.stream().filter(s -> s.getAwardDate().after(beginOfDay) && s.getAwardDate().before(endOfDay)).collect(Collectors.toList());
        //日范围内班级的获章记录
        List<MedalUserAcquireRecord> classRecords;
        if (StringUtils.isEmpty(dto.getClassId())) {
            classRecords = gradeRecords.stream().filter(s -> dto.getClassIds().contains(s.getClassId())).collect(Collectors.toList());
        } else {
            classRecords = gradeRecords.stream().filter(s -> dto.getClassId().equals(s.getClassId())).collect(Collectors.toList());
        }
        //必须选择到班级
        int classNum = getClassCount(classEduOrgTreeVOS,Long.valueOf(dto.getGradeId()));
        //年级平均数量
        BigDecimal gradeCount = classNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(Convert.toDouble(BigDecimal.valueOf(gradeRecords.size()).divide(BigDecimal.valueOf(classNum), Constant.ONE, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
        medalTrendVO.setName(DateUtil.format(beginOfDay, Constant.FORMAL_MONTH_DAY_FORMAT));
        medalTrendVO.setGradeCount(new BigDecimal(gradeCount.stripTrailingZeros().toPlainString()));
        medalTrendVO.setClassCount(classRecords.size());
        medalTrendVOS.add(medalTrendVO);
    }

    /**
     * 获取班级数量
     * @param classEduOrgTreeVOS
     * @return
     */
    private int getClassCount(List<GlobalEduOrgTreeVO> classEduOrgTreeVOS,Long gradeId) {
        int count = 0;
        if(CollectionUtil.isEmpty(classEduOrgTreeVOS)){
            return count;
        }
        for (GlobalEduOrgTreeVO classEduOrgTreeVO : classEduOrgTreeVOS) {
            if(Objects.equals(SaasClassOrgTypeEnum.GRADE.getCode(),classEduOrgTreeVO.getType())
                && Objects.equals(gradeId,classEduOrgTreeVO.getId())){
                return CollectionUtils.isEmpty(classEduOrgTreeVO.getChildren()) ? Constant.ONE : classEduOrgTreeVO.getChildren().size();
            }else{
                count += getClassCount(classEduOrgTreeVO.getChildren(),gradeId);
            }
        }
        return count;
    }

    /**
     * 周范围之内班级和年级获章数量
     *
     * @param dto
     * @param classEduOrgTreeVOS
     * @param gradeRecordList
     * @param medalTrendVOS
     * @param i
     * @param medalTrendVO
     * @param beginOfWeek
     * @param endOfWeek
     */
    private void getWeedRecords(MedalStatisticsQueryDTO dto, List<GlobalEduOrgTreeVO> classEduOrgTreeVOS, List<MedalUserAcquireRecord> gradeRecordList, ArrayList<MedalTrendVO> medalTrendVOS, int i, MedalTrendVO medalTrendVO, DateTime beginOfWeek, DateTime endOfWeek) {
        //周范围内年级的获章记录
        List<MedalUserAcquireRecord> gradeRecords = gradeRecordList.stream().filter(s -> s.getAwardDate().after(beginOfWeek) && s.getAwardDate().before(endOfWeek)).collect(Collectors.toList());
        //周范围内班级的获章记录
        List<MedalUserAcquireRecord> classRecords;
        if (StringUtils.isEmpty(dto.getClassId())) {
            classRecords = gradeRecords.stream().filter(s -> dto.getClassIds().contains(s.getClassId())).collect(Collectors.toList());
        } else {
            classRecords = gradeRecords.stream().filter(s -> dto.getClassId().equals(s.getClassId())).collect(Collectors.toList());
        }
        //年级平均数量
        BigDecimal gradeCount = BigDecimal.valueOf(Convert.toDouble(BigDecimal.valueOf(gradeRecords.size()).divide(BigDecimal.valueOf(classEduOrgTreeVOS.size()), Constant.ONE, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()));
        medalTrendVO.setName(DateToCNEnum.getMessageByCode(i));
        medalTrendVO.setGradeCount(new BigDecimal(gradeCount.stripTrailingZeros().toPlainString()));
        medalTrendVO.setClassCount(classRecords.size());
        medalTrendVOS.add(Constant.ZERO, medalTrendVO);
    }

    /**
     * 获章排名
     *
     * @param dto
     * @return
     */
    @Override
    public List<MedalTrendVO> listMedalRank(MedalStatisticsQueryDTO dto) {
        //时间校验
        validateTime(dto.getStartTime(), dto.getEndTime(), dto.getType());
        //入参校验
        Assert.isFalse(Objects.isNull(dto.getClassId()) || Objects.isNull(dto.getGradeId()) || Objects.isNull(dto.getStartTime())
                        || Objects.isNull(dto.getEndTime()),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE.getMessage()));

        dealGradeAndClassData(dto);
        //年级所有获章记录
        List<MedalUserAcquireRecord> gradeRecordList = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .select(MedalUserAcquireRecord::getGradeId, MedalUserAcquireRecord::getClassId, MedalUserAcquireRecord::getMedalInfoId, MedalUserAcquireRecord::getMedalActivityId)
                .eq(MedalUserAcquireRecord::getTenantId, WebUtil.getTenantId())
                .eq(MedalUserAcquireRecord::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalUserAcquireRecord::getCampusId, WebUtil.getCampusId())
                .eq(StringUtils.isNotEmpty(dto.getGradeId()), MedalUserAcquireRecord::getGradeId, dto.getGradeId())//年级
                .in(CollUtil.isNotEmpty(dto.getGradeIds()), MedalUserAcquireRecord::getGradeId, dto.getGradeIds())//年级ids
                .eq(MedalUserAcquireRecord::getCampusSectionId, dto.getCampusSectionId())//学段id
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_COMPLETION.getCode())
                .between(MedalUserAcquireRecord::getAwardDate, dto.getStartTime(), dto.getEndTime())
        );
        if (CollectionUtil.isEmpty(gradeRecordList)) {
            return Collections.EMPTY_LIST;
        }
        for (MedalUserAcquireRecord medalUserAcquireRecord : gradeRecordList) {
            if (ObjectUtil.isNull(medalUserAcquireRecord.getMedalInfoId())) {
                Long medalActivityId = medalUserAcquireRecord.getMedalActivityId();
                Long medalInfoId = medalTaskManager.getByActivityId(medalActivityId);
                medalUserAcquireRecord.setMedalInfoId(medalInfoId);
            }
        }

        List<MedalTrendVO> medalTrendVOS = new ArrayList<>();

        //奖章id
        List<Long> medalInfoIds = gradeRecordList.stream().map(MedalUserAcquireRecord::getMedalInfoId).collect(Collectors.toList());
        //奖章信息
        List<MedalInfoVO> medalInfoList = medalInfoManager.listInfoByIds(medalInfoIds);

        //年级按每个奖章分类，并获得各个章的个数
        Map<Long, Long> medalInfoCountMap = gradeRecordList.stream().filter(s -> ObjectUtil.isNotNull(s.getMedalInfoId())).collect(Collectors.groupingBy(MedalUserAcquireRecord::getMedalInfoId, Collectors.counting()));
        //班级的奖章记录
        List<MedalUserAcquireRecord> classRecordList;
        if (StringUtils.isEmpty(dto.getClassId())) {
            classRecordList = gradeRecordList.stream().filter(s -> dto.getClassIds().contains(s.getClassId())).collect(Collectors.toList());
        } else {
            classRecordList = gradeRecordList.stream().filter(s -> dto.getClassId().equals(s.getClassId())).collect(Collectors.toList());
        }
        //班级按每个奖章分类，并获得各个章的个数
        Map<Long, Long> classMedalInfoCountMap = classRecordList.stream().collect(Collectors.groupingBy(MedalUserAcquireRecord::getMedalInfoId, Collectors.counting()));

        Iterator<Map.Entry<Long, Long>> iterator = medalInfoCountMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, Long> next = iterator.next();
            MedalTrendVO medalTrendVO = new MedalTrendVO();
            medalTrendVO.setName(medalInfoList.stream().filter(s -> next.getKey().equals(s.getId())).collect(Collectors.toList()).get(Constant.ZERO).getName());
            medalTrendVO.setMedalCreateTime(medalInfoList.stream().filter(s -> next.getKey().equals(s.getId())).collect(Collectors.toList()).get(Constant.ZERO).getCreateTime());
            medalTrendVO.setClassCount(CollectionUtil.isEmpty(classRecordList) || MapUtil.isEmpty(classMedalInfoCountMap) ? Constant.ZERO : ObjectUtil.isEmpty(classMedalInfoCountMap.get(next.getKey())) ? Constant.ZERO : Convert.toInt(classMedalInfoCountMap.get(next.getKey())));
            medalTrendVO.setGradeCount(BigDecimal.valueOf(next.getValue()));
            medalTrendVOS.add(medalTrendVO);
        }
        //按班级奖章个数倒序，一样以奖章创建时间正序
        medalTrendVOS = medalTrendVOS.stream().sorted(Comparator.comparing(MedalTrendVO::getClassCount).thenComparing(MedalTrendVO::getMedalCreateTime)).collect(Collectors.toList());
        return medalTrendVOS;
    }

    private void dealGradeAndClassData(MedalStatisticsQueryDTO dto) {
        //获取年级信息
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(Convert.toLong(dto.getCampusSectionId()));
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SECTION.getCode());
        eduOrgQueryDTO.setSchoolYear(dto.getSchoolYear());
        List<GlobalEduOrgTreeVO> eduOrgTreeVOS = basicInfoService.queryGlobalEducationalOrgTree(eduOrgQueryDTO);

        List<String> gradeIdList = new ArrayList<>();
        List<String> classIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(eduOrgTreeVOS)) {
            List<GlobalEduOrgTreeVO> gradeList = eduOrgTreeVOS.get(0).getChildren();
            //获取年级信息
            gradeIdList = gradeList.stream().map(GlobalEduOrgTreeVO::getId).map(String::valueOf).collect(Collectors.toList());
            // 获取班级信息
            classIdList = gradeList.stream().filter(gradeVO -> gradeVO.getChildren() != null).flatMap(gradeVO -> gradeVO.getChildren().stream()).filter(Objects::nonNull)
                    .map(GlobalEduOrgTreeVO::getId).map(String::valueOf).collect(Collectors.toList());
        }
        //全部年级、班级，传参为-1，查全部直接置为null
        if (StringUtils.isNotBlank(dto.getGradeId()) && "-1".equals(dto.getGradeId())) {
            dto.setGradeId(null);
            dto.setGradeIds(gradeIdList);
        }
        if (StringUtils.isNotBlank(dto.getClassId()) && "-1".equals(dto.getClassId())) {
            dto.setClassId(null);
            dto.setClassIds(classIdList);
        }
    }

    /**
     * 学生排名
     *
     * @param dto
     * @return
     */
    @Override
    public Page<StudentMedalRankVO> pageStudentMedalRank(StudentRankQueryDTO dto) {
        //时间校验
        validateTime(dto.getStartTime(), dto.getEndTime(), dto.getType());
        //入参校验
        Assert.isFalse(Objects.isNull(dto.getClassId()) || Objects.isNull(dto.getGradeId()) || Objects.isNull(dto.getStartTime())
                        || Objects.isNull(dto.getEndTime()),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE.getMessage()));

        StaffClassDTO staffClassDTO = new StaffClassDTO();
        staffClassDTO.setClassIdList(Arrays.asList(Convert.toLong(dto.getClassId())));

        //班级所有学生
        //从saas获取该学段(年级，班级)所有正常状态学生（分页获取total）
        List<SassStudentVO> ucStudentClassBffVOS;
        if (Objects.nonNull(dto.getIsCurrentYear()) && Boolean.FALSE.equals(dto.getIsCurrentYear())) {
            //历史学生
            ucStudentClassBffVOS = saasHistoryStudentCacheManager.listHisStudentByGradeIdAndClassId(WebUtil.getSchoolIdLong(), dto.getSchoolYear(),
                    WebUtil.getCampusIdLong(), Convert.toLong(dto.getGradeId()), Convert.toLong(dto.getClassId()), Convert.toLong(dto.getCampusSectionId()));
        } else {
            //从saas获取该学段所有学生
            ucStudentClassBffVOS = listTotalSaasStudent(Convert.toLong(dto.getCampusSectionId()), dto.getSchoolYear(),
                    Convert.toLong(dto.getGradeId()), Convert.toLong(dto.getClassId()));
        }
        if (CollectionUtil.isEmpty(ucStudentClassBffVOS)) {
            return new Page<>();
        }
        List<UcStudentClassBffVO> studentBaseOutVOList = new ArrayList<>();
        for (SassStudentVO sassStudentVO : ucStudentClassBffVOS) {
            UcStudentClassBffVO newVo = new UcStudentClassBffVO();
            BeanUtil.copyProperties(sassStudentVO, newVo);
            studentBaseOutVOList.add(newVo);
        }

        List<StudentMedalRankVO> studentMedalRankVOS = convert.toStudentMedalRankVOList(studentBaseOutVOList);
        Page<StudentMedalRankVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());

        dealGradeAndClassData(dto);
        //班级所有获章记录
        List<MedalUserAcquireRecord> classRecordList = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .select(MedalUserAcquireRecord::getStudentId, MedalUserAcquireRecord::getStudentName)
                .eq(MedalUserAcquireRecord::getTenantId, WebUtil.getTenantId())
                .eq(MedalUserAcquireRecord::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalUserAcquireRecord::getCampusId, WebUtil.getCampusId())
                .eq(StringUtils.isNotEmpty(dto.getClassId()), MedalUserAcquireRecord::getClassId, dto.getClassId())//班级
                .in(CollUtil.isNotEmpty(dto.getClassIds()), MedalUserAcquireRecord::getClassId, dto.getClassIds())//班级ids
                .eq(MedalUserAcquireRecord::getCampusSectionId, dto.getCampusSectionId())//学段id
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_COMPLETION.getCode())
                .between(MedalUserAcquireRecord::getAwardDate, dto.getStartTime(), dto.getEndTime())
        );

        if (CollectionUtil.isEmpty(classRecordList)) {
            //没有获章记录，所有同学的排名都是最后一名，奖章数量都是0
            for (StudentMedalRankVO s : studentMedalRankVOS) {
                s.setRank(studentMedalRankVOS.size());
                s.setCount(Constant.ZERO);
            }
            List<StudentMedalRankVO> studentMedalRankVOList = studentMedalRankVOS.stream().skip((dto.getPageNum() - Constant.ONE) * dto.getPageSize()).limit(dto.getPageSize()).collect(Collectors.toList());
            page.setRecords(studentMedalRankVOList);
            page.setTotal(studentMedalRankVOS.size());
            return page;
        }

        //当前班级所有学生id
        List<Long> baseStudentIds = ucStudentClassBffVOS.stream().filter(Objects::nonNull).map(SassStudentVO::getStudentId).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> baseStudentStrIds = Convert.toList(String.class, baseStudentIds);

        //按每个学生分组，并获取其奖章数量
        Map<String, Long> studentMedalCountMap = classRecordList.stream().collect(Collectors.groupingBy(MedalUserAcquireRecord::getStudentId, Collectors.counting()));
        List<String> medalStudentIds = Convert.toList(String.class, studentMedalCountMap.keySet());
        int medalStudentCount = CollectionUtil.intersection(baseStudentStrIds, medalStudentIds).size();

        //各个同学的名称和奖章数量
        for (StudentMedalRankVO s : studentMedalRankVOS) {
            Long count = studentMedalCountMap.get(Convert.toStr(s.getStudentId()));
            s.setRank(ObjectUtil.isNull(count) ? medalStudentCount + Constant.ONE : getClassRank(studentMedalCountMap, count));
            s.setCount(ObjectUtil.isNull(count) ? Constant.ZERO : Convert.toInt(count));
        }
        //排序
        studentMedalRankVOS = studentMedalRankVOS.stream().sorted(Comparator.comparing(StudentMedalRankVO::getRank)).collect(Collectors.toList());
        List<StudentMedalRankVO> studentMedalRankVOList = studentMedalRankVOS.stream().skip((dto.getPageNum() - Constant.ONE) * dto.getPageSize()).limit(dto.getPageSize()).collect(Collectors.toList());
        page.setRecords(studentMedalRankVOList);
        page.setTotal(studentMedalRankVOS.size());
        return page;
    }

    /**
     * 从saas获取(学段、年级、班级)的状态为正常的学生
     *
     * @param campusSectionId
     * @param schoolYear
     * @param gradeId
     * @param classId
     * @return
     */
    private List<SassStudentVO> listTotalSaasStudent(Long campusSectionId, String schoolYear, Long gradeId, Long classId) {
        List<SassStudentVO> sassStudentVOS = saasHistoryStudentCacheManager
                .getCurrentStudentVOS(
                        Convert.toLong(WebUtil.getSchoolId()),
                        WebUtil.getCampusIdLong(),
                        campusSectionId,
                        gradeId,
                        classId,
                        schoolYear, "0", "0");
        if (CollUtil.isNotEmpty(sassStudentVOS)) {
            //筛选行政班
            sassStudentVOS = sassStudentVOS.stream().filter(s -> Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
            //过滤学段
            if (com.hailiang.jxgy.common.utils.StringUtil.isNotEmpty(campusSectionId)) {
                sassStudentVOS = sassStudentVOS.stream().filter(s -> s.getCampusSectionId().equals(campusSectionId)).collect(Collectors.toList());
            }
            //指定班级id
            if (com.hailiang.jxgy.common.utils.StringUtil.isNotEmpty(classId) && -1 != classId) {
                sassStudentVOS = sassStudentVOS.stream().filter(s -> s.getClassId().equals(classId)).collect(Collectors.toList());
            }
        }
        return sassStudentVOS;
    }

    /**
     * 学生奖章列表
     *
     * @param dto
     * @return
     */
    @Override
    public Page<StudentMedalVO> pageStudentMedal(StudentRankQueryDTO dto) {
        //时间校验
        validateTime(dto.getStartTime(), dto.getEndTime(), dto.getType());
        //入参校验
        Assert.isFalse(Objects.isNull(dto.getClassId()) || Objects.isNull(dto.getGradeId()) || Objects.isNull(dto.getStartTime())
                        || Objects.isNull(dto.getEndTime()),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE.getMessage()));

        dealGradeAndClassData(dto);
        //班级所有获章记录
        List<MedalUserAcquireRecord> classRecordList = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .select(MedalUserAcquireRecord::getStudentId, MedalUserAcquireRecord::getClassName, MedalUserAcquireRecord::getStudentName, MedalUserAcquireRecord::getMedalInfoId, MedalUserAcquireRecord::getAwardDate, MedalUserAcquireRecord::getMedalActivityId)
                .eq(MedalUserAcquireRecord::getTenantId, WebUtil.getTenantId())
                .eq(MedalUserAcquireRecord::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalUserAcquireRecord::getCampusId, WebUtil.getCampusId())
                .eq(StringUtils.isNotEmpty(dto.getClassId()), MedalUserAcquireRecord::getClassId, dto.getClassId())//班级
                .in(CollUtil.isNotEmpty(dto.getClassIds()), MedalUserAcquireRecord::getClassId, dto.getClassIds())//班级ids
                .eq(CharSequenceUtil.isNotBlank(dto.getGradeId()), MedalUserAcquireRecord::getGradeId, dto.getGradeId())
                .eq(MedalUserAcquireRecord::getCampusSectionId, dto.getCampusSectionId())//学段id
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_COMPLETION.getCode())
                .between(MedalUserAcquireRecord::getAwardDate, dto.getStartTime(), dto.getEndTime())
        );
        if (CollectionUtil.isEmpty(classRecordList)) {
            return new Page<>();
        }
        for (MedalUserAcquireRecord medalUserAcquireRecord : classRecordList) {
            if (ObjectUtil.isNull(medalUserAcquireRecord.getMedalInfoId())) {
                Long medalActivityId = medalUserAcquireRecord.getMedalActivityId();
                Long medalInfoId = medalTaskManager.getByActivityId(medalActivityId);
                medalUserAcquireRecord.setMedalInfoId(medalInfoId);
            }
        }

        List<StudentMedalVO> studentMedalVOS = convert.toStudentMedalVOList(classRecordList);

        //奖章id
        List<Long> medalInfoIds = classRecordList.stream().map(MedalUserAcquireRecord::getMedalInfoId).collect(Collectors.toList());
        //奖章信息
        List<MedalInfoVO> medalInfoList = medalInfoManager.listInfoByIds(medalInfoIds);
        studentMedalVOS.stream().forEach(s -> {
            Optional<MedalInfoVO> first = medalInfoList.stream().filter(m -> ObjectUtil.isNotNull(s.getMedalInfoId()) && s.getMedalInfoId().equals(m.getId())).collect(Collectors.toList()).stream().findFirst();
            if (first.isPresent()) {
                s.setMedalInfoName(first.get().getName());
                s.setMedalCatalogueName(first.get().getMedalCatalogueName());
            }
        });
        studentMedalVOS = studentMedalVOS.stream().sorted(Comparator.comparing(StudentMedalVO::getTime).reversed().thenComparing(StudentMedalVO::getStudentId)).collect(Collectors.toList());
        List<StudentMedalVO> studentMedalVOList = studentMedalVOS.stream().skip((dto.getPageNum() - Constant.ONE) * dto.getPageSize()).limit(dto.getPageSize()).collect(Collectors.toList());
        Page<StudentMedalVO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        page.setRecords(studentMedalVOList);
        page.setTotal(studentMedalVOS.size());
        return page;
    }

    /**
     * 个人争章展示学生信息以及其获得的章目
     *
     * @param studentId
     * @return
     */
    @Override
    public StudentMedalInfoVO getStudentInfo(String studentId) {
        StudentMedalInfoVO studentMedalInfoVO = new StudentMedalInfoVO();
        ArrayList<MedalCatalogueVO> medalCatalogueVOS = new ArrayList<>();
        if (StrUtil.isBlank(studentId)) {
            return studentMedalInfoVO;
        }
        StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
        studentByIdQuery.setStudentIds(Arrays.asList(Convert.toLong(studentId)));
        List<UcStudentClassBffVO> ucStudentClassBffVOS = basicInfoRemote.listByStudentIds(studentByIdQuery);
        if (CollectionUtil.isEmpty(ucStudentClassBffVOS)) {
            return studentMedalInfoVO;
        }
        //行政班
        List<UcStudentClassBffVO> ucStudentClassBffVOList = ucStudentClassBffVOS.stream().filter(s -> Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ucStudentClassBffVOList)) {
            return studentMedalInfoVO;
        }
        String classId = Convert.toStr(ucStudentClassBffVOList.get(Constant.ZERO).getClassId());
        String gradeId = Convert.toStr(ucStudentClassBffVOList.get(Constant.ZERO).getGradeId());

        MedalStatisticsQueryDTO medalStatisticsQueryDTO = new MedalStatisticsQueryDTO();
        medalStatisticsQueryDTO.setGradeId(gradeId);
        medalStatisticsQueryDTO.setClassId(classId);
        //班级所有学生
        List<UcStudentClassBffVO> studentClassBffVOS = listClassStudents(medalStatisticsQueryDTO);
        if (CollectionUtil.isEmpty(studentClassBffVOS)) {
            return studentMedalInfoVO;
        }

        //班级所有学生id
        List<Long> ids = studentClassBffVOS.stream().map(UcStudentClassBffVO::getId).collect(Collectors.toList());
        List<String> stringIds = Convert.toList(String.class, ids);

        //该学生的奖章记录
        List<MedalUserAcquireRecord> studentAcquireRecords = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .select(MedalUserAcquireRecord::getMedalInfoId, MedalUserAcquireRecord::getStudentId)
                .eq(MedalUserAcquireRecord::getTenantId, WebUtil.getTenantId())
                .eq(MedalUserAcquireRecord::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalUserAcquireRecord::getCampusId, WebUtil.getCampusId())
                .eq(MedalUserAcquireRecord::getStudentId, studentId)
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_COMPLETION.getCode())
        );

        //参与的奖章章目
        ArrayList<Long> medalCatalogueIds = medalInfoManager.listMedalCatalogueIds(WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId(), gradeId);
        if (CollectionUtil.isNotEmpty(medalCatalogueIds)) {
            List<MedalCatalogue> medalCatalogues = medalCatalogueManager.list(new LambdaQueryWrapper<MedalCatalogue>()
                    .select(MedalCatalogue::getId, MedalCatalogue::getName)
                    .in(MedalCatalogue::getId, medalCatalogueIds));
            medalCatalogueVOS.addAll(convert.toMedalCatalogueVO(medalCatalogues));
        }
        //获章记录的章目
        if (CollectionUtil.isNotEmpty(studentAcquireRecords)) {
            List<Long> medalInfoIds = studentAcquireRecords.stream().map(MedalUserAcquireRecord::getMedalInfoId).distinct().collect(Collectors.toList());
            List<MedalCatalogue> medalCatalogues = medalCatalogueManager.listMedalCatalogues(WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId(), medalInfoIds);
            medalCatalogueVOS.addAll(convert.toMedalCatalogueVO(medalCatalogues));
        }

        //该班级所有学生对应的奖章个数
        Map<String, Long> maps = medalUserAcquireRecordManager.selectStudentCountByIds(stringIds);
        if (MapUtil.isEmpty(maps)) {
            //所有学生都没记录，全部都最后一名
            studentMedalInfoVO.setRank(ids.size());
        } else if (Constant.ZERO.equals(studentAcquireRecords.size())) {
            //该学生没记录，排名为有名次的同学后一名
            studentMedalInfoVO.setRank(maps.keySet().size() + Constant.ONE);
        } else {
            //该学生有记录，获取其名次
            studentMedalInfoVO.setRank(getClassRank(maps, Convert.toLong(studentAcquireRecords.size())));
        }

        studentMedalInfoVO.setStudentId(studentId);
        studentMedalInfoVO.setName(ucStudentClassBffVOList.get(Constant.ZERO).getStudentName());
        studentMedalInfoVO.setCount(studentAcquireRecords.size());
        studentMedalInfoVO.setMedalCatalogueVOS(medalCatalogueVOS.stream().distinct().collect(Collectors.toList()));
        return studentMedalInfoVO;
    }

    /**
     * 个人争章展示学生章目下的奖章
     *
     * @param studentId        学生id
     * @param medalCatalogueId 章目id
     * @return
     */
    @Override
    public List<MedalInfoVO> listStudentMedals(String studentId, Long medalCatalogueId) {
        if (StrUtil.isBlank(studentId) || ObjectUtil.isNull(medalCatalogueId)) {
            return Collections.EMPTY_LIST;
        }
        List<MedalInfoVO> medalInfoVOArrayList = new ArrayList<>();
        //参与章目下的奖章
        List<MedalInfo> medalInfos = medalInfoManager.list(new LambdaQueryWrapper<MedalInfo>()
                .select(MedalInfo::getLogoId, MedalInfo::getMedalCatalogueId, MedalInfo::getId, MedalInfo::getName)
                .eq(MedalInfo::getTenantId, WebUtil.getTenantId())
                .eq(MedalInfo::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalInfo::getCampusId, WebUtil.getCampusId())
                .eq(MedalInfo::getMedalCatalogueId, medalCatalogueId));
        if (CollectionUtil.isNotEmpty(medalInfos)) {
            List<MedalInfoVO> medalInfoVOS = convert.toListMedalInfoVO(medalInfos);
            List<Long> logoIds = medalInfos.stream().map(MedalInfo::getLogoId).distinct().collect(Collectors.toList());
            List<MedalLogo> logos = medalLogoManager.listMedalLogos(logoIds);
            medalInfoVOS.stream().forEach(s -> {
                s.setMedalCount(Constant.ZERO);
                s.setLogoUrl(urlPrefix + logos.stream().filter(m -> m.getId().equals(s.getLogoId())).collect(Collectors.toList()).get(Constant.ZERO).getLogoUrl());
            });
            medalInfoVOArrayList.addAll(medalInfoVOS);
        }

        //获得的章目下的奖章
        List<MedalInfoVO> medalInfoVOS = medalInfoManager.selectMedalInfoIdsByStudentId(WebUtil.getTenantId(), WebUtil.getSchoolId(), WebUtil.getCampusId(), studentId);
        medalInfoVOS = medalInfoVOS.stream().filter(s -> medalCatalogueId.equals(s.getMedalCatalogueId())).collect(Collectors.toList());
        //去重
        List<Long> collect = medalInfoVOS.stream().map(MedalInfoVO::getId).collect(Collectors.toList());
        medalInfoVOArrayList = medalInfoVOArrayList.stream().filter(s -> !collect.contains(s.getId())).collect(Collectors.toList());

        medalInfoVOS.stream().forEach(s -> {
            s.setLogoUrl(urlPrefix + s.getLogoUrl());
        });
        medalInfoVOArrayList.addAll(medalInfoVOS);
        return medalInfoVOArrayList;
    }

    /**
     * 学生奖章记录
     *
     * @return
     */
    @Override
    public Page<StudentMedalListVO> pageStudentMedalInfos(Integer pageNum, Integer pageSize, HttpServletRequest request) {
        if (ObjectUtil.isNull(pageNum) || ObjectUtil.isNull(pageSize)) {
            pageNum = Constant.ONE;
            pageSize = Constant.TEN;
        }

        //家长手机号
        String mobile = RequestUtil.getHeader(SaasConstant.MOBILE);
        if (StrUtil.isBlank(mobile)) {
            mobile = commonService.getMobileByToken(request).getMobile();
        }
        //学生id
        String studentIdStr = WebUtil.getStudentIdStr();

        //分页查询该学生获得的奖章
        Page<MedalUserAcquireRecord> recordPage = medalUserAcquireRecordManager.page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .eq(MedalUserAcquireRecord::getStudentId, studentIdStr)
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_COMPLETION.getCode())
                .orderByDesc(MedalUserAcquireRecord::getAwardDate)
        );

        if (BeanUtil.isEmpty(recordPage) || CollectionUtil.isEmpty(recordPage.getRecords())) {
            return new Page<>();
        }
        //获章记录
        List<MedalUserAcquireRecord> pageRecords = recordPage.getRecords();
        //转成返回的vo
        Page<StudentMedalListVO> studentMedalListVOPage = convert.toPageStudentMedalListVO(recordPage);

        //获章明细id
        List<Long> ids = pageRecords.stream().map(MedalUserAcquireRecord::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ids)) {
            return new Page<>();
        }

        //奖章id
        List<Long> medalInfoIds = pageRecords.stream().map(MedalUserAcquireRecord::getMedalInfoId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(medalInfoIds)) {
            return new Page<>();
        }
        //奖章信息
        List<MedalInfoVO> medalInfoVOS = medalInfoManager.listInfoByIds(medalInfoIds);

        //活动id
        List<Long> activityIds = pageRecords.stream().map(MedalUserAcquireRecord::getMedalActivityId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(activityIds)) {
            return new Page<>();
        }
        //活动名称
        List<MedalActivityInfoVO> medalActivityInfoVOS = medalActivityManager.listMedalActivityInfo(activityIds);

        //二级任务完成详情
        List<Long> completionIds = pageRecords.stream().filter(Objects::nonNull).map(MedalUserAcquireRecord::getCompletionId).filter(Objects::nonNull).collect(Collectors.toList());
        List<MedalTaskCompletionRelation> taskCompletionRelationList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(completionIds)) {
            taskCompletionRelationList = medalTaskCompletionRelationManager.list(new LambdaQueryWrapper<MedalTaskCompletionRelation>()
                    .in(CollectionUtil.isNotEmpty(completionIds), MedalTaskCompletionRelation::getTaskCompletionId, completionIds));
        }
        HashMap<String, List<MedalTaskCompletionRelation>> stringListHashMap = new HashMap<>();
        stringListHashMap.put("taskCompletionRelationList", taskCompletionRelationList);
        //查询已读
        List<MedalRecordUser> medalRecordUsers = medalRecordUserManager.list(new LambdaQueryWrapper<MedalRecordUser>()
                .select(MedalRecordUser::getMedalUserAcquireRecordId, MedalRecordUser::getParentMobile)
                .eq(MedalRecordUser::getParentMobile, mobile)
                .eq(MedalRecordUser::getReadFlag, Constant.ONE)
                .in(MedalRecordUser::getMedalUserAcquireRecordId, ids));

        //添加活动名称，奖章名称，logo，已读标记，内容详情
        studentMedalListVOPage.getRecords().stream().forEach(s -> {
            List<MedalRecordUser> recordUsers = medalRecordUsers.stream().filter(m -> s.getId().equals(m.getMedalUserAcquireRecordId())).collect(Collectors.toList());
            List<MedalInfoVO> medalInfoVOList = medalInfoVOS.stream().filter(m -> ObjectUtil.isNotNull(s.getMedalInfoId()) && s.getMedalInfoId().equals(m.getId())).collect(Collectors.toList());
            List<MedalActivityInfoVO> activityNameVOS = medalActivityInfoVOS.stream().filter(m -> ObjectUtil.isNotNull(s.getMedalActivityId()) && s.getMedalActivityId().equals(m.getActivityId())).collect(Collectors.toList());


            ArrayList<String> strings = new ArrayList<>();
            List<MedalTaskCompletionRelation> completionRelationList = stringListHashMap.get("taskCompletionRelationList");
            //二级任务获章规则
            List<MedalTaskCompletionRelation> completionRelations = completionRelationList.stream().filter(m -> m.getTaskCompletionId().equals(s.getCompletionId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(completionRelations)) {
                for (MedalTaskCompletionRelation completionRelation : completionRelations) {
                    if (MedalRuleTypeEnum.COMMENT_NUM.getCode().equals(completionRelation.getMedalRuleType())) {
                        strings.add(MessageFormat.format(RULE_TYPE_COUNT_NAME, completionRelation.getMedalRuleName(), completionRelation.getTargetValue()));
                    } else if (MedalRuleTypeEnum.COMMENT_TOTAL.getCode().equals(completionRelation.getMedalRuleType())) {
                        strings.add(MessageFormat.format(RULE_TYPE_SCORE_NAME, completionRelation.getMedalRuleName(), completionRelation.getTargetValue()));
                    }
                }
            }

            s.setReadFlag(CollectionUtil.isEmpty(recordUsers) ? Constant.ZERO : Constant.ONE);
            s.setMedalInfoName(CollectionUtil.isEmpty(medalInfoVOList) ? null : medalInfoVOList.get(Constant.ZERO).getName());
            s.setLogoUrl(CollectionUtil.isEmpty(medalInfoVOList) ? null : urlPrefix + medalInfoVOList.get(Constant.ZERO).getLogoUrl());
            if (Constant.TWO.equals(s.getAwardType())) {
                s.setContent(s.getAwardContent());
            } else if (Constant.ONE.equals(s.getAwardType())) {
                s.setContent(MessageFormat.format(CONTENT,
                        //恭喜您的孩子{0}在{1}活动中完成{2}获得{3}1枚
                        s.getStudentName(),
                        CollectionUtil.isNotEmpty(activityNameVOS) ? activityNameVOS.get(Constant.ZERO).getActivityName() : null,
                        String.join(StrPool.COMMA + StrPool.C_SPACE, strings),
                        CollectionUtil.isNotEmpty(medalInfoVOList) ? medalInfoVOList.get(Constant.ZERO).getName() : null));
            }
        });
        return studentMedalListVOPage;
    }

    /**
     * 学生奖章记录详情
     *
     * @param id
     * @return
     */
    @Override
    public StudentMedalListVO getStudentMedalInfo(Long id, HttpServletRequest request) {
        if (Objects.isNull(id)) {
            return null;
        }
        //家长手机号
        String mobile = RequestUtil.getHeader(SaasConstant.MOBILE);
        if (StrUtil.isBlank(mobile)) {
            mobile = commonService.getMobileByToken(request).getMobile();
        }
        //学生id
        String studentIdStr = WebUtil.getStudentIdStr();

        //该奖章明细
        MedalUserAcquireRecord recordManagerById = medalUserAcquireRecordManager.getById(id);
        if (BeanUtil.isEmpty(recordManagerById) || ObjectUtil.isNull(recordManagerById.getMedalInfoId()) || ObjectUtil.isNull(recordManagerById.getMedalActivityId())) {
            return null;
        }
        //转成要返回的vo
        StudentMedalListVO studentMedalListVO = convert.toStudentMedalListVO(recordManagerById);

        //奖章信息
        List<MedalInfoVO> medalInfoVOS = medalInfoManager.listInfoByIds(Arrays.asList(recordManagerById.getMedalInfoId()));
        if (CollectionUtil.isEmpty(medalInfoVOS)) {
            return studentMedalListVO;
        }
        MedalInfoVO medalInfoVO = medalInfoVOS.get(Constant.ZERO);

        //二级任务完成详情
        List<MedalTaskCompletionRelation> taskCompletionRelationList = new ArrayList<>();
        if (ObjectUtil.isNotNull(recordManagerById.getCompletionId())) {
            taskCompletionRelationList = medalTaskCompletionRelationManager.list(new LambdaQueryWrapper<MedalTaskCompletionRelation>()
                    .eq(MedalTaskCompletionRelation::getTaskCompletionId, recordManagerById.getCompletionId()));
        }

        ArrayList<String> strings = new ArrayList<>();
        //二级任务获章规则
        if (CollectionUtil.isNotEmpty(taskCompletionRelationList)) {
            for (MedalTaskCompletionRelation completionRelation : taskCompletionRelationList) {
                if (MedalRuleTypeEnum.COMMENT_NUM.getCode().equals(completionRelation.getMedalRuleType())) {
                    strings.add(MessageFormat.format(RULE_TYPE_COUNT_NAME, completionRelation.getMedalRuleName(), completionRelation.getTargetValue()));
                } else if (MedalRuleTypeEnum.COMMENT_TOTAL.getCode().equals(completionRelation.getMedalRuleType())) {
                    strings.add(MessageFormat.format(RULE_TYPE_SCORE_NAME, completionRelation.getMedalRuleName(), completionRelation.getTargetValue()));
                }
            }
        }

        studentMedalListVO.setMedalInfoName(BeanUtil.isEmpty(medalInfoVOS) ? null : medalInfoVO.getName());
        studentMedalListVO.setLogoUrl(BeanUtil.isEmpty(medalInfoVOS) ? null : urlPrefix + medalInfoVO.getLogoUrl());
        if (Constant.TWO.equals(studentMedalListVO.getAwardType())) {
            studentMedalListVO.setAwardContentList(Arrays.asList(recordManagerById.getAwardContent()));
        } else if (Constant.ONE.equals(studentMedalListVO.getAwardType())) {
            studentMedalListVO.setAwardContentList(strings);
        }

        //查询已读
        List<MedalRecordUser> medalRecordUsers = medalRecordUserManager.list(new LambdaQueryWrapper<MedalRecordUser>()
                .select(MedalRecordUser::getMedalUserAcquireRecordId, MedalRecordUser::getParentMobile)
                .eq(MedalRecordUser::getParentMobile, mobile)
                .eq(MedalRecordUser::getReadFlag, Constant.ONE)
                .eq(MedalRecordUser::getMedalUserAcquireRecordId, id));
        if (CollectionUtil.isEmpty(medalRecordUsers) && StrUtil.isNotBlank(mobile)) {
            //更改为已读
            MedalRecordUser medalRecordUser = new MedalRecordUser();
            medalRecordUser.setMedalUserAcquireRecordId(id);
            medalRecordUser.setReadTime(DateUtil.date());
            medalRecordUser.setParentMobile(mobile);
            medalRecordUser.setReadFlag(Constant.ONE);
            medalRecordUser.setCreateBy(studentIdStr);
            medalRecordUserManager.save(medalRecordUser);
        }

        return studentMedalListVO;
    }

    /**
     * 星动力判断是否有学生个人争章展示按钮
     *
     * @param studentId
     * @return
     */
    @Override
    public MedalMenuVO getMedalMenuFlag(String studentId, String campusId) {
        if (StringUtils.isEmpty(campusId) || StringUtils.isEmpty(studentId)) {
            log.info("[/evaluate-web/medal/getMedalMenuFlag]入参错误:校区id:[{}], 学生id:[{}]", campusId, studentId);
            throw new BizException("校区id或者学生id为空");
        }
        log.info("[星动力判断是否有学生个人争章展示按钮]，入参，租户id：[{}], 学校id：[{}], 校区id:[{}], 学生id:[{}]", WebUtil.getTenantId(), WebUtil.getSchoolId(), campusId, studentId);

        MedalMenuVO medalMenuVO = new MedalMenuVO();
        medalMenuVO.setMedalMenuFlag(false);
        if (StrUtil.isBlank(studentId)) {
            return medalMenuVO;
        }
        StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
        studentByIdQuery.setStudentIds(Arrays.asList(Convert.toLong(studentId)));
        List<UcStudentClassBffVO> ucStudentClassBffVOS = basicInfoRemote.listByStudentIds(studentByIdQuery);
        if (CollectionUtil.isEmpty(ucStudentClassBffVOS)) {
            return medalMenuVO;
        }
        //行政班
        List<UcStudentClassBffVO> ucStudentClassBffVOList = ucStudentClassBffVOS.stream().filter(s -> Convert.toLong(Constant.ZERO).equals(s.getFormalClassId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ucStudentClassBffVOList)) {
            return medalMenuVO;
        }
        String gradeId = Convert.toStr(ucStudentClassBffVOList.get(Constant.ZERO).getGradeId());
        String classId = Convert.toStr(ucStudentClassBffVOList.get(Constant.ZERO).getClassId());

        //学生获章的记录条数
        long count = medalUserAcquireRecordManager.count(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .eq(MedalUserAcquireRecord::getTenantId, WebUtil.getTenantId())
                .eq(MedalUserAcquireRecord::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalUserAcquireRecord::getCampusId, campusId)
                .eq(MedalUserAcquireRecord::getStudentId, studentId)
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_COMPLETION.getCode())
        );
        //有获章 展示
        if (Constant.ZERO < Convert.toInt(count)) {
            medalMenuVO.setMedalMenuFlag(true);
            return medalMenuVO;
        }

        //改学生年级对应的活动
        List<MedalActivityGrade> medalActivityGrades = medalActivityGradeManager.list(new LambdaQueryWrapper<MedalActivityGrade>()
                .select(MedalActivityGrade::getMedalActivityId)
                .eq(MedalActivityGrade::getTenantId, WebUtil.getTenantId())
                .eq(MedalActivityGrade::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalActivityGrade::getCampusId, campusId)
                .eq(MedalActivityGrade::getGradeId, gradeId)
        );
        if (CollectionUtil.isEmpty(medalActivityGrades)) {
            return medalMenuVO;
        }
        List<Long> medalActivityIds = medalActivityGrades.stream().map(MedalActivityGrade::getMedalActivityId).distinct().collect(Collectors.toList());
        //活动
        long medalActivityCount = medalActivityManager.count(new LambdaQueryWrapper<MedalActivity>().in(MedalActivity::getId, medalActivityIds));
        //学校创建的活动的范围中有该学生年级  展示
        if (Constant.ZERO < Convert.toInt(medalActivityCount)) {
            medalMenuVO.setMedalMenuFlag(true);
            return medalMenuVO;
        }
        return medalMenuVO;
    }

    /**
     * 查学生奖章信息
     *
     * @param dto
     * @return
     */
    @Override
    public Page<MedalInfoVO> pageStudentMedalInfo(StudentMedalQueryDTO dto) {
        //入参校验
        if (BeanUtil.isEmpty(dto) || StrUtil.isBlank(dto.getStudentId())) {
            return null;
        }
        Integer pageNum = dto.getPageNum();
        Integer pageSize = dto.getPageSize();
        if (ObjectUtil.isNull(dto.getPageNum()) || ObjectUtil.isNull(dto.getPageSize())) {
            pageNum = Constant.ONE;
            pageSize = Constant.TEN;
        }

        //分页查询获得的奖章
        Page<MedalUserAcquireRecord> recordPage = medalUserAcquireRecordManager.page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .select(MedalUserAcquireRecord::getMedalInfoId)
//                .isNotNull(MedalUserAcquireRecord::getMedalInfoId)
//                .eq(MedalUserAcquireRecord::getTenantId, WebUtil.getTenantId())
//                .eq(MedalUserAcquireRecord::getSchoolId, WebUtil.getSchoolId())
//                .eq(MedalUserAcquireRecord::getCampusId, WebUtil.getCampusId())
                .eq(MedalUserAcquireRecord::getStudentId, dto.getStudentId())
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_COMPLETION.getCode()));
        if (BeanUtil.isEmpty(recordPage) || CollectionUtil.isEmpty(recordPage.getRecords())) {
            return new Page<>();
        }
        Page<MedalInfoVO> medalInfoVOPage = convert.toPageStudentMedalInfoVO(recordPage);
        List<MedalInfoVO> infoVOPageRecords = medalInfoVOPage.getRecords();
        List<MedalUserAcquireRecord> records = recordPage.getRecords();
        List<Long> medalInfoIds = records.stream().map(MedalUserAcquireRecord::getMedalInfoId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(medalInfoIds)) {
            return new Page<>();
        }
        //奖章信息
        List<MedalInfoVO> medalInfoVOS = medalInfoManager.listInfoByIds(medalInfoIds);
        infoVOPageRecords.stream().forEach(s -> {
            MedalInfoVO medalInfoVO = medalInfoVOS.stream().filter(m -> s.getId().equals(m.getId())).collect(Collectors.toList()).get(Constant.ZERO);
            s.setName(medalInfoVO.getName());
            s.setLogoUrl(urlPrefix + medalInfoVO.getLogoUrl());
        });

        medalInfoVOPage.setRecords(infoVOPageRecords);
        return medalInfoVOPage;
    }

    /**
     * 获取获章同学消息通知内容
     *
     * @param acquireRecords 奖章明细内容
     * @return
     */
    @Override
    public List<StudentMedalContentVO> listStudentMedalContent(List<MedalUserAcquireRecord> acquireRecords) {

        if (CollectionUtil.isEmpty(acquireRecords)) {
            return Collections.EMPTY_LIST;
        }
        ArrayList<StudentMedalContentVO> studentMedalContentVOS = convert.toListStudentMedalContentVOS(acquireRecords);

        //奖章id
        List<Long> medalInfoIds = studentMedalContentVOS.stream().map(StudentMedalContentVO::getMedalInfoId).collect(Collectors.toList());
        List<MedalInfoVO> medalInfoVOS = medalInfoManager.listInfoByIds(medalInfoIds);

        //学生id
        List<String> studentStrIds = studentMedalContentVOS.stream().map(StudentMedalContentVO::getStudentId).distinct().collect(Collectors.toList());
        List<Long> studentIds = Convert.toList(Long.class, studentStrIds);

        //学生信息
        List<UcStudentClassBffVO> ucStudentClassBffVOS = new ArrayList<>();

        //分片，saas学生查询不能超 5000
        List<List<Long>> partition = ListUtil.partition(studentIds, Constant.FIVE_THOUSAND);
        for (List<Long> studentIdLongs : partition) {
            //学生信息
            StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
            studentByIdQuery.setStudentIds(studentIdLongs);
            List<UcStudentClassBffVO> studentClassBffVOS = basicInfoRemote.listByStudentIds(studentByIdQuery);
            ucStudentClassBffVOS.addAll(studentClassBffVOS);
        }
        log.info("定时任务颁发奖章-获取消息内容-原学生数量：{}", ucStudentClassBffVOS.size());
        //行政班 和 学生状态正常
        List<UcStudentClassBffVO> ucStudentClassBffVOList = ucStudentClassBffVOS.stream().filter(s -> Convert.toLong(Constant.ZERO).equals(s.getFormalClassId()) && Constant.ZERO.equals(Convert.toInt(s.getStudentStatus()))).collect(Collectors.toList());
        log.info("定时任务颁发奖章-获取消息内容-过滤后学生数量：{}", ucStudentClassBffVOList.size());
        if (CollectionUtil.isEmpty(ucStudentClassBffVOList)) {
            log.info("定时任务颁发奖章-ucStudentClassBffVOList为空");
            return Collections.EMPTY_LIST;
        }
        /*  List<Long> studentIds = CollStreamUtil.toList(ucStudentClassBffVOList, UcStudentClassBffVO::getStudentId);*/
        //班主任信息
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        eduStudentClassQueryDTO.setStudentIds(studentIds);
        List<EduStudentClassVO> eduStudentClassVOS = basicInfoRemote.queryClassListByStudentIds(eduStudentClassQueryDTO);

        //家长信息
//        EduParentQueryDTO eduParentQueryDTO = new EduParentQueryDTO();
//        eduParentQueryDTO.setStudentIds(studentIds);
//        List<EduParentInfoVO> eduParentInfoVOS = basicInfoRemote.queryParentInfoList(eduParentQueryDTO);

        //先默认班主任都不存在
        studentMedalContentVOS.stream().forEach(s -> {
            if (Constant.ONE.equals(s.getAwardType())) {
                s.setAwardUserName(StrPool.DASHED + StrPool.DASHED);
            }
            //设置学号
            List<UcStudentClassBffVO> studentClassBffVOS = ucStudentClassBffVOList.stream().filter(m -> s.getStudentId().equals(Convert.toStr(m.getStudentId()))).collect(Collectors.toList());
            s.setStudentCode(CollectionUtil.isNotEmpty(studentClassBffVOS) ? studentClassBffVOS.get(Constant.ZERO).getStudentNo() : null);
        });

        //设置班主任名称，家长手机号
        studentMedalContentVOS.stream().forEach(s -> {
            s.setMedalInfoName(medalInfoVOS.stream().filter(m -> s.getMedalInfoId().equals(m.getId())).collect(Collectors.toList()).get(Constant.ZERO).getName());
            //家长
            /*List<EduParentInfoVO> parentInfoVOS = eduParentInfoVOS.stream().filter(m -> s.getStudentId().equals(Convert.toStr(m.getStudentId()))).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(parentInfoVOS)) {
                List<EduParentInfoPojo> parentInfos = parentInfoVOS.get(0).getParentInfos();
                if (CollectionUtil.isNotEmpty(parentInfos)) {

                }
                *//*List<EduParentInfoPojo> eduParentInfoPojos = parentInfoVOS.stream().map(EduParentInfoVO::getParentInfos).collect(Collectors.toList()).stream().flatMap(Collection::stream).collect(Collectors.toList());
                s.setMobiles(eduParentInfoPojos.stream().map(EduParentInfoPojo::getMobile).collect(Collectors.toList()));
                List<EduParentInfoPojo> parentInfos = parentInfoVOS.get(Constant.ZERO).getParentInfos();
                if (CollectionUtil.isNotEmpty(parentInfos)) {
                    s.setMobile(ObjectUtil.isNotNull(parentInfos.get(Constant.ZERO).getMobile()) ? parentInfos.get(Constant.ZERO).getMobile() : null);
                }*//*
            }*/

            if (Constant.ONE.equals(s.getAwardType())) {
                //班主任
                List<EduStudentClassVO> studentClassVOS = eduStudentClassVOS.stream().filter(m -> s.getStudentId().equals(Convert.toStr(m.getStudentId()))).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(studentClassVOS)) {
                    List<EduClassBaseInfoPojo> classBaseInfos = studentClassVOS.get(Constant.ZERO).getClassBaseInfos();
                    if (CollectionUtil.isNotEmpty(classBaseInfos)) {
                        s.setAwardUserId(Convert.toStr(ObjectUtil.isNotNull(classBaseInfos.get(Constant.ZERO).getHeadMasterId()) ? classBaseInfos.get(Constant.ZERO).getHeadMasterId() : null));
                        s.setAwardUserName(Convert.toStr(ObjectUtil.isNotNull(classBaseInfos.get(Constant.ZERO).getHeadMasterName()) ? classBaseInfos.get(Constant.ZERO).getHeadMasterName() : StrPool.DASHED + StrPool.DASHED));
                    }
                }
            }
        });
        return studentMedalContentVOS;
    }


}
