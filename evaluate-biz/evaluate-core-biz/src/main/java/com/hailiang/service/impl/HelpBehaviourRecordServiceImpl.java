package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.hailiang.constant.Constant;
import com.hailiang.convert.HelpBehaviourRecordConvert;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.exception.BizException;
import com.hailiang.manager.EvaluateHelpBehaviourRecordManager;
import com.hailiang.mapper.EvaluateHelpBehaviourRecordMapper;
import com.hailiang.model.dto.BehaviourRecordBatchQueryDTO;
import com.hailiang.model.dto.behaviour.help.HelpBehaviourRecordQueryDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.EvaluateHelpBehaviourRecordPO;
import com.hailiang.model.vo.BehaviourStudentFileVO;
import com.hailiang.portrait.StuPortraitService;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.service.HelpBehaviourRecordService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 行为记录表-师徒帮扶
 * @author: panjian
 * @create: 2024/9/19 10:39
 * @Version 1.0
 */
@Slf4j
@Service
public class HelpBehaviourRecordServiceImpl implements HelpBehaviourRecordService {

    @Resource
    private EvaluateHelpBehaviourRecordManager helpBehaviourRecordManager;
    @Resource
    private StuPortraitService stuPortraitService;


    @Override
    public List<EvaluateHelpBehaviourRecordPO> listByStudent(HelpBehaviourRecordQueryDTO recordQueryCondition) {
        return helpBehaviourRecordManager.queryByCondition(recordQueryCondition);
    }


    @Override
    public List<StudentDailyStatisticsPO> getAndMergeHelpBehaviourClassStatistics(List<StudentDailyStatisticsPO> behaviourClassStatisticsList, StuPortraitQuery dto) {
        Date startTime = dto.getStartTime();
        Date endTime = dto.getEndTime();
        String classId = dto.getClassId();
        if (CollUtil.isNotEmpty(dto.getHelpBehaviourRecordIds())) {
            dto.setNeedIds(dto.getHelpBehaviourRecordIds());
            dto.setStartTime(null);
            dto.setEndTime(null);
            dto.setClassId(null);
        }
        //查询师徒帮扶统计数据
        List<StudentDailyStatisticsPO> studentDailyStatisticsPOS = helpBehaviourRecordManager.listStudentDailyStatisticsFromDrois(dto);
        if (CollectionUtils.isEmpty(studentDailyStatisticsPOS)) {
            return behaviourClassStatisticsList;
        }
        //对已统计的行为记录表数据按学生id和提交时间进行分类
        Map<String/**studentId-submit-time*/, StudentDailyStatisticsPO> statisticsPOMap = new HashMap<>();
        for (StudentDailyStatisticsPO studentDailyStatisticsPO : behaviourClassStatisticsList) {
            String key = buildKey(studentDailyStatisticsPO);
            statisticsPOMap.put(key, studentDailyStatisticsPO);
        }
        //将师徒帮扶数据加入到行为记录表统计数据中
        for (StudentDailyStatisticsPO studentDailyStatisticsPO : studentDailyStatisticsPOS) {
            String key = buildKey(studentDailyStatisticsPO);
            StudentDailyStatisticsPO existStatistics = statisticsPOMap.get(key);
            if (Objects.isNull(existStatistics)) {
                behaviourClassStatisticsList.add(studentDailyStatisticsPO);
            } else {
                //计算新的统计后数据
                //德育加分、减分
                existStatistics.setPlusMoralScore(addScore(existStatistics.getPlusMoralScore(), studentDailyStatisticsPO.getPlusMoralScore()));
                existStatistics.setMinusMoralScore(addScore(existStatistics.getMinusMoralScore(), studentDailyStatisticsPO.getMinusMoralScore()));
                //智育加分、减分
                existStatistics.setPlusWisdomScore(addScore(existStatistics.getPlusWisdomScore(), studentDailyStatisticsPO.getPlusWisdomScore()));
                existStatistics.setMinusWisdomScore(addScore(existStatistics.getMinusWisdomScore(), studentDailyStatisticsPO.getMinusWisdomScore()));
                //体育加分、减分
                existStatistics.setPlusSportScore(addScore(existStatistics.getPlusSportScore(), studentDailyStatisticsPO.getPlusSportScore()));
                existStatistics.setMinusSportScore(addScore(existStatistics.getMinusSportScore(), studentDailyStatisticsPO.getMinusSportScore()));
                //美育加分、减分
                existStatistics.setPlusPrettyScore(addScore(existStatistics.getPlusPrettyScore(), studentDailyStatisticsPO.getPlusPrettyScore()));
                existStatistics.setMinusPrettyScore(addScore(existStatistics.getMinusPrettyScore(), studentDailyStatisticsPO.getMinusPrettyScore()));
                //劳育加分、减分
                existStatistics.setPlusWorkScore(addScore(existStatistics.getPlusWorkScore(), studentDailyStatisticsPO.getPlusWorkScore()));
                existStatistics.setMinusWorkScore(addScore(existStatistics.getMinusWorkScore(), studentDailyStatisticsPO.getMinusWorkScore()));
                //其他育加分、减分
                existStatistics.setPlusOtherScore(addScore(existStatistics.getPlusOtherScore(), studentDailyStatisticsPO.getPlusOtherScore()));
                existStatistics.setMinusOtherScore(addScore(existStatistics.getMinusOtherScore(), studentDailyStatisticsPO.getMinusOtherScore()));
                //总加分、减分
                existStatistics.setPlusTotalScore(addScore(existStatistics.getPlusTotalScore(), studentDailyStatisticsPO.getPlusTotalScore()));
                existStatistics.setMinusTotalScore(addScore(existStatistics.getMinusTotalScore(), studentDailyStatisticsPO.getMinusTotalScore()));
                //设置总分
                existStatistics.setTotalScore(addScore(existStatistics.getTotalScore(), studentDailyStatisticsPO.getTotalScore()));
                existStatistics.setAppraisalCount(existStatistics.getAppraisalCount() + studentDailyStatisticsPO.getAppraisalCount());
            }
        }
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setClassId(classId);
        return behaviourClassStatisticsList;
    }

    @Override
    public List<BehaviourStudentFileVO> getAndMergeHelpBehaviourRecord(BehaviourRecordBatchQueryDTO dto, Boolean includeHelpBehaviour, List<BehaviourStudentFileVO> behaviourStudentFileVOS) {
        if(Objects.isNull(includeHelpBehaviour) || !includeHelpBehaviour){
            return behaviourStudentFileVOS;
        }
        List<BehaviourStudentFileVO> helpBehaviourStudentFileList = helpBehaviourRecordManager.listStudentHelpPortrait(dto);
        log.warn("【学生画像】-【列表】 - 【step.7-1】 - 【从Doris获取学生帮扶数据】，查询到学生画像记录数:{}", helpBehaviourStudentFileList.size());
        if(CollectionUtils.isEmpty(helpBehaviourStudentFileList)){
            return behaviourStudentFileVOS;
        }
        Map<String/**studentId*/,BehaviourStudentFileVO> existMap = new HashMap<>();
        for (BehaviourStudentFileVO behaviourStudentFileVO : behaviourStudentFileVOS) {
            existMap.put(behaviourStudentFileVO.getStudentId(),behaviourStudentFileVO);
        }
        for (BehaviourStudentFileVO behaviourStudentFileVO : helpBehaviourStudentFileList) {
            BehaviourStudentFileVO exist = existMap.get(behaviourStudentFileVO.getStudentId());
            if (exist != null) {
                exist.setAddScore(addScore(exist.getAddScore(), behaviourStudentFileVO.getAddScore()));
                exist.setSumScore(addScore(exist.getSumScore(), behaviourStudentFileVO.getSumScore()));
                exist.setSubtractScore(addScore(exist.getSubtractScore(), behaviourStudentFileVO.getSubtractScore()));
            }else{
                behaviourStudentFileVOS.add(behaviourStudentFileVO);
            }
        }
        return behaviourStudentFileVOS;
    }


    /**
     * 构建key
     * @param studentDailyStatisticsPO
     * @return
     */
    private String buildKey(StudentDailyStatisticsPO studentDailyStatisticsPO) {
        return studentDailyStatisticsPO.getStudentId() + "-" + DateUtil.format(studentDailyStatisticsPO.getStatisticsTime(), DatePattern.NORM_DATETIME_FORMAT);
    }

    /**
     * bigDecimal相加
     *
     * @param num1
     * @param num2
     * @return
     */
    private BigDecimal addScore(BigDecimal num1, BigDecimal num2) {
        if (Objects.isNull(num1)) {
            num1 = BigDecimal.ZERO;
        }
        if (Objects.isNull(num2)) {
            num2 = BigDecimal.ZERO;
        }
        return num1.add(num2);
    }
}