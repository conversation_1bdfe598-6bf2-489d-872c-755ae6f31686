package com.hailiang.service;

import com.hailiang.model.dto.request.speed.SpeedConfigDetailEditRequest;
import com.hailiang.model.dto.request.speed.SpeedConfigGroupSortRequest;
import com.hailiang.model.entity.Target;

import java.util.List;

/**
 * 极速点评配置后台设置
 *
 * @Description: 极速点评配置后台设置
 * @Author: Jovi
 * @Date: Created in 2024-01-29
 * @Version: 1.6.0
 */
public interface SpeedConfigSetUpEditService {

    Boolean sortIndexGroup(List<SpeedConfigGroupSortRequest> requests);

    /**
     * 根据对应分组ID删除分组
     *
     * @param speedGroupId
     * @return
     */
    Boolean deleteGroup(Long speedGroupId);

    /**
     * 极速分组添加或者名称修改
     * @param name
     * @param id
     * @param isTemplate 是否是运营平台模板，true：是，false：否
     * @return
     */
    Boolean saveOrUpdateSpeedGroup(String name, Long id, Boolean isTemplate);

    /**
     * 保存或更新极速点评后台配置
     *
     * @param detailRequests
     * @return
     */
    Boolean saveOrUpdateSpeedEvaluateConfig(SpeedConfigDetailEditRequest detailRequests);

    void checkAndCopySpeedTarget(String tenantId, String schoolId, String campusId, List<Target> targetList);
}
