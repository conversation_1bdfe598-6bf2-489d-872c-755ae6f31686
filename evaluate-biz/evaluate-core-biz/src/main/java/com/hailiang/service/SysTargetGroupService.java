package com.hailiang.service;

import com.hailiang.model.dto.query.TargetGroupListByConditionsDTO;
import com.hailiang.model.dto.query.TargetGroupListDTO;
import com.hailiang.model.dto.query.TargetIdQuery;
import com.hailiang.model.dto.response.speed.ModuleInfoResponse;
import com.hailiang.model.dto.save.TargetGroupSaveDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.dto.target.SystemRecommendTargetDTO;
import com.hailiang.model.entity.SysTargetGroup;
import com.hailiang.model.vo.ListAllEvaluateTargetVOModule;
import com.hailiang.model.vo.TargetGroupListVO;
import com.hailiang.model.vo.TargetGroupTargetListByConditionsVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/29
 */
public interface SysTargetGroupService {
    void save(TargetGroupSaveDTO dto);

    void delete(String groupId);

    List<TargetGroupListVO> list(TargetGroupListDTO dto);

    void sort(List<String> dtoList);

    SysTargetGroup get(Long groupId);

    List<TargetGroupTargetListByConditionsVO> listByConditions(TargetGroupListByConditionsDTO dto);

    /**
     * 获取当前登录人所有指标
     * @param userType  用户类型1:老师 2:家长
     * @return
     */
    List<ListAllEvaluateTargetVOModule> listAllEvaluateTarget(Integer userType);

    /**
     * 获取指标点评项
     *
     * @param targetId 指标id
     * @return
     */
    List<TemplateInfoSaveDTO> getTargetOption(Long targetId);

    /**
     * 图文点评设置
     */
    void setPictureEvaluate(TargetIdQuery query);
    /**
     * 图文点评指标列表
     */
    List<Long> listPictureEvaluates();
    /**
     * 指标树（不带选项）
     */
    List<ModuleInfoResponse> listGroupTargets();

    /**
     * 创建系统推荐分组、指标
     */
    Long saveSystemRecommendGroupAndTarget(SystemRecommendTargetDTO dto);
}
