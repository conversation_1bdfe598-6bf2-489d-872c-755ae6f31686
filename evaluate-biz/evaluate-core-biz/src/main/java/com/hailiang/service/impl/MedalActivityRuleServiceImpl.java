package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hailiang.constant.Constant;
import com.hailiang.enums.medal.MedalRuleFailureEnum;
import com.hailiang.enums.medal.MedalRuleLevelEnum;
import com.hailiang.enums.medal.MedalTargetTypeEnum;
import com.hailiang.manager.*;
import com.hailiang.model.dto.activity.rule.save.RuleOptionInfoDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.entity.*;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/1 13:57
 */
@Slf4j
@Service
public class MedalActivityRuleServiceImpl implements MedalActivityRuleService {


    @Resource
    private MedalTaskManager medalTaskManager;

    @Resource
    private MedalTaskRuleManager medalTaskRuleManager;

    @Resource
    private MedalTaskRuleTargetManager medalTaskRuleTargetManager;

    @Resource
    private TargetTemplateService templateService;

    @Resource
    private TargetService targetService;


    /**
     * @param businessId 业务id  操作类型1,2,3为指标id,4为分组id
     * @param type       操作类型  1:修改指标出发规则填写项失效  2:删除指标  3:禁用指标   4:分组删除
     * @return
     */
    @Override
    public Boolean updateRuleStatusFailure(Long businessId, Integer type) {
        try {
            // 失效的规则内容集合
            List<MedalTaskRuleTarget> medalTaskRuleTargets = new ArrayList<>();

            // 如果是删除或禁用指标
            if (CollUtil.newArrayList(MedalRuleFailureEnum.DELETE_TARGET.getCode(), MedalRuleFailureEnum.FORBIDDEN_TARGET.getCode()).contains(type)) {
                LambdaQueryWrapper<MedalTaskRuleTarget> wrapper = Wrappers.lambdaQuery();
                wrapper.eq(MedalTaskRuleTarget::getTargetId, businessId);
                medalTaskRuleTargets = medalTaskRuleTargetManager.list(wrapper);
            }
            // 如果是修改指标 1.修改选项内容,2.修改分组
            if (MedalRuleFailureEnum.UPDATE_TARGET.getCode().equals(type)) {
                medalTaskRuleTargets = listRuleTargetByUpdateTarget(businessId,null);
            }
            // 如果是分组删除
            if (MedalRuleFailureEnum.GROUP_DELETED.getCode().equals(type)) {
                LambdaQueryWrapper<MedalTaskRuleTarget> wrapper = Wrappers.lambdaQuery();
                wrapper.eq(MedalTaskRuleTarget::getGroupId, businessId);
                medalTaskRuleTargets = medalTaskRuleTargetManager.list(wrapper);
            }

            if (CollUtil.isEmpty(medalTaskRuleTargets)) {
                return Boolean.TRUE;
            }
            // 根据失效的规则判断任务是否失效
            judgeTaskRuleFailure(medalTaskRuleTargets);

        } catch (Exception e) {
            log.error("指标-[修改]-[删除]-[禁用]-异常,指标id:[{}],操作类型:[{}]", businessId, type, e);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchUpdateRuleStatusFailureByTargets(Collection<Target> targets, Integer type) {
        // 失效的规则内容集合
        List<MedalTaskRuleTarget> medalTaskRuleTargets = new ArrayList<>();
        List<Long> targetIdList = targets.stream().map(Target::getId).collect(Collectors.toList());
        // 如果是删除或禁用指标
        if (CollUtil.newArrayList(MedalRuleFailureEnum.DELETE_TARGET.getCode(), MedalRuleFailureEnum.FORBIDDEN_TARGET.getCode()).contains(type)) {
            LambdaQueryWrapper<MedalTaskRuleTarget> wrapper = Wrappers.lambdaQuery();
            wrapper.in(MedalTaskRuleTarget::getTargetId, targetIdList);
            medalTaskRuleTargets = medalTaskRuleTargetManager.list(wrapper);
        }
        // 如果是修改指标 1.修改选项内容,2.修改分组
        if (MedalRuleFailureEnum.UPDATE_TARGET.getCode().equals(type)) {
            //暂时没有批量修改指标的场景，目前先按循环单个处理
            for(Target target : targets) {
                medalTaskRuleTargets = listRuleTargetByUpdateTarget(target.getId(),target);
            }
        }
        if (CollUtil.isEmpty(medalTaskRuleTargets)) {
            return Boolean.TRUE;
        }
        // 根据失效的规则判断任务是否失效
        batchJudgeTaskRuleFailure(medalTaskRuleTargets);
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchUpdateRuleStatusFailureByTargetGroups(Collection<TargetGroup> targetGroupList, Integer type) {
        // 失效的规则内容集合
        List<MedalTaskRuleTarget> medalTaskRuleTargets = new ArrayList<>();
        List<Long> targetGroupIdList = targetGroupList.stream().map(TargetGroup::getId).collect(Collectors.toList());
        // 如果是分组删除
        if (MedalRuleFailureEnum.GROUP_DELETED.getCode().equals(type)) {
            LambdaQueryWrapper<MedalTaskRuleTarget> wrapper = Wrappers.lambdaQuery();
            wrapper.in(MedalTaskRuleTarget::getGroupId, targetGroupIdList);
            medalTaskRuleTargets = medalTaskRuleTargetManager.list(wrapper);
        }
        if (CollUtil.isEmpty(medalTaskRuleTargets)) {
            return Boolean.TRUE;
        }
        // 根据失效的规则判断任务是否失效
        batchJudgeTaskRuleFailure(medalTaskRuleTargets);
        return Boolean.TRUE;
    }


    /**
     * 获取因为修改指标而失效的规则
     *
     * @param targetId 指标id
     * @return
     */
    private List<MedalTaskRuleTarget> listRuleTargetByUpdateTarget(Long targetId,Target target) {
        if(Objects.isNull(targetId) && Objects.isNull(target)){
            return Collections.emptyList();
        }
        if(Objects.isNull(target)) {
            target = targetService.getById(targetId);
        }
        List<MedalTaskRuleTarget> list = new ArrayList<>();

        // 获取该指标下规则为填写项的数据
        LambdaQueryWrapper<MedalTaskRuleTarget> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MedalTaskRuleTarget::getTargetId, targetId);
        wrapper.eq(MedalTaskRuleTarget::getSubmitType, MedalTargetTypeEnum.OPTION_ID.getCode());
        List<MedalTaskRuleTarget> medalTaskRuleTargets = medalTaskRuleTargetManager.list(wrapper);
        if (CollUtil.isEmpty(medalTaskRuleTargets)) {
            return list;
        }

        // 情况1.修改选项内容
        // 获取该指标下所有最新的填写项数据
        List<RuleOptionInfoDTO> optionInfos = listTargetOptions(targetId);
        // 如果没有填写项,则说明规则全部失效
        if (CollUtil.isEmpty(optionInfos)) {
            return medalTaskRuleTargets;
        }
        List<String> optionKeys = optionInfos.stream().map(RuleOptionInfoDTO::getOptionKey).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        // 2.修改分组
        if (Objects.isNull(target)) {
            return medalTaskRuleTargets;
        }
        if (Objects.isNull(target.getGroupId())) {
            return medalTaskRuleTargets;
        }

        // 筛选失效的规则
        for (MedalTaskRuleTarget medalTaskRuleTarget : medalTaskRuleTargets) {
            // 如果最新的填写项key不包含规则中的填写项key,则规则失效
            if (!optionKeys.contains(medalTaskRuleTarget.getOptionId())) {
                list.add(medalTaskRuleTarget);
            }
            // 分组改变,规则中的分组与最新的分组不匹配
            if (!target.getGroupId().equals(medalTaskRuleTarget.getGroupId())) {
                list.add(medalTaskRuleTarget);
            }
        }
        return list;
    }

    /**
     * 根据失效的规则判断任务是否失效
     *
     * @param medalTaskRuleTargets
     * @return
     */
    private Boolean batchJudgeTaskRuleFailure(List<MedalTaskRuleTarget> medalTaskRuleTargets) {
        if(CollectionUtils.isEmpty(medalTaskRuleTargets)){
            return Boolean.TRUE;
        }
        // 查询出符合条件的规则
        List<Long> ruleIds = medalTaskRuleTargets.stream().map(MedalTaskRuleTarget::getMedalTaskRuleId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ruleIds)){
            return Boolean.TRUE;
        }
        // 批量修改状态为已失效
        medalTaskRuleManager.batchUpdateStatus(ruleIds,Constant.YES);

        // 当任务内的子任务全部失效时,大任务判断为失效
        // 筛选出所有任务id
        List<Long> taskIds = medalTaskRuleTargets.stream().filter(s -> Objects.nonNull(s.getMedalTaskId())).map(MedalTaskRuleTarget::getMedalTaskId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(taskIds)) {
            return Boolean.TRUE;
        }
        List<MedalTaskRule> taskRuleList = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>()
                .in(MedalTaskRule::getMedalTaskId, taskIds)
                .eq(MedalTaskRule::getLevel, MedalRuleLevelEnum.SUB_LEVEL.getCode()));
        Map<Long, List<MedalTaskRule>> taskMap = taskRuleList.stream().collect(Collectors.groupingBy(MedalTaskRule::getMedalTaskId));

        List<Long> loseTaskIds = new ArrayList<>();
        // 筛选出所有子任务规则失效的大任务
        for (Long taskId : taskMap.keySet()) {
            List<MedalTaskRule> taskRules = taskMap.get(taskId);
            // 筛选出失效的数据
            List<MedalTaskRule> loseRules = taskRules.stream().filter(s -> Constant.YES.equals(s.getStatus())).collect(Collectors.toList());
            if (ObjectUtil.equals(taskRules.size(), loseRules.size())) {
                loseTaskIds.add(taskId);
            }
        }

        if (CollUtil.isEmpty(loseTaskIds)) {
            return Boolean.TRUE;
        }

        // 将大任务置为失效
        List<MedalTask> medalTasks = medalTaskManager.listByIds(loseTaskIds);
        medalTasks.forEach(s -> s.setStatus(Constant.YES));
        medalTaskManager.updateBatchById(medalTasks);
        return Boolean.TRUE;
    }


    /**
     * 根据失效的规则判断任务是否失效
     *
     * @param medalTaskRuleTargets
     * @return
     */
    private Boolean judgeTaskRuleFailure(List<MedalTaskRuleTarget> medalTaskRuleTargets) {
        // 查询出符合条件的规则
        List<Long> ruleIds = medalTaskRuleTargets.stream().map(MedalTaskRuleTarget::getMedalTaskRuleId).distinct().collect(Collectors.toList());
        List<MedalTaskRule> medalTaskRules = medalTaskRuleManager.listByIds(ruleIds);
        if (CollUtil.isEmpty(medalTaskRules)) {
            return Boolean.TRUE;
        }
        // 修改状态为已失效
        medalTaskRules.forEach(s -> s.setStatus(Constant.YES));
        medalTaskRuleManager.updateBatchById(medalTaskRules);


        // 当任务内的子任务全部失效时,大任务判断为失效
        // 筛选出所有任务id
        List<Long> taskIds = medalTaskRules.stream().filter(s -> Objects.nonNull(s.getMedalTaskId())).map(MedalTaskRule::getMedalTaskId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(taskIds)) {
            return Boolean.TRUE;
        }
        List<MedalTaskRule> taskRuleList = medalTaskRuleManager.list(new LambdaQueryWrapper<MedalTaskRule>()
                .in(MedalTaskRule::getMedalTaskId, taskIds)
                .eq(MedalTaskRule::getLevel, MedalRuleLevelEnum.SUB_LEVEL.getCode()));
        Map<Long, List<MedalTaskRule>> taskMap = taskRuleList.stream().collect(Collectors.groupingBy(MedalTaskRule::getMedalTaskId));

        List<Long> loseTaskIds = new ArrayList<>();
        // 筛选出所有子任务规则失效的大任务
        for (Long taskId : taskMap.keySet()) {
            List<MedalTaskRule> taskRules = taskMap.get(taskId);
            // 筛选出失效的数据
            List<MedalTaskRule> loseRules = taskRules.stream().filter(s -> Constant.YES.equals(s.getStatus())).collect(Collectors.toList());
            if (ObjectUtil.equals(taskRules.size(), loseRules.size())) {
                loseTaskIds.add(taskId);
            }
        }

        if (CollUtil.isEmpty(loseTaskIds)) {
            return Boolean.TRUE;
        }

        // 将大任务置为失效
        List<MedalTask> medalTasks = medalTaskManager.listByIds(loseTaskIds);
        medalTasks.forEach(s -> s.setStatus(Constant.YES));
        medalTaskManager.updateBatchById(medalTasks);
        return Boolean.TRUE;
    }

    /**
     * 获取指标下的所有选项
     *
     * @param targetId 指标id
     * @return
     */
    @Override
    public List<RuleOptionInfoDTO> listTargetOptions(Long targetId) {
        TargetTemplate targetTemplate = templateService.getByTargetId(targetId);
        if (Objects.isNull(targetTemplate)) {
            return Collections.emptyList();
        }
        if (CollUtil.isEmpty(targetTemplate.getTemplateInfoList())) {
            return Collections.emptyList();
        }
        // 如果存在加分控件,返回指标
        List<String> types = targetTemplate.getTemplateInfoList().stream().filter(s -> StrUtil.isNotBlank(s.getType())).map(TemplateInfoSaveDTO::getType).collect(Collectors.toList());
        if (types.contains("score")) {
            return Collections.emptyList();
        }

        List<RuleOptionInfoDTO> list = new ArrayList<>();
        for (TemplateInfoSaveDTO templateInfoSaveDTO : targetTemplate.getTemplateInfoList()) {
            TemplateInfoSaveDTO.InnerSubmitOptionInfoSave options = templateInfoSaveDTO.getOptions();
            if (CollUtil.isNotEmpty(options.getOptions())) {
                for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave option : options.getOptions()) {
                    RuleOptionInfoDTO ruleOptionInfoDTO = new RuleOptionInfoDTO();
                    ruleOptionInfoDTO.setTargetId(targetId);
                    ruleOptionInfoDTO.setOptionKey(option.getKey());
                    ruleOptionInfoDTO.setOptionName(option.getLabel());
                    list.add(ruleOptionInfoDTO);
                }
            }

            // 如果是明细
            if ("card".equals(templateInfoSaveDTO.getType())) {
                // 明细列表
                for (LinkedHashMap linkedHashMap : templateInfoSaveDTO.getList()) {
                    if (Objects.nonNull(linkedHashMap.get("options"))) {
                        HashMap innerOption = (HashMap) linkedHashMap.get("options");
                        List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> innerOptions = JSONUtil.parseArray(innerOption.get("options")).toList(TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave.class);
                        if (CollUtil.isNotEmpty(innerOptions)) {
                            for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave option : innerOptions) {
                                RuleOptionInfoDTO ruleOptionInfoDTO = new RuleOptionInfoDTO();
                                ruleOptionInfoDTO.setTargetId(targetId);
                                ruleOptionInfoDTO.setOptionKey(option.getKey());
                                ruleOptionInfoDTO.setOptionName(option.getLabel());
                                list.add(ruleOptionInfoDTO);
                            }
                        }
                    }
                }
            }
        }
        return list;
    }
}
