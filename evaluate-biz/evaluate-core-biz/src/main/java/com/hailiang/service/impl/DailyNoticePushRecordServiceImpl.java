package com.hailiang.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.mapper.DailyNoticePushRecordMapper;
import com.hailiang.model.entity.DailyNoticePushRecord;
import com.hailiang.service.DailyNoticePushRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 报告推送规则表(ReportPushRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-13 11:30:31
 */
@Slf4j
@Service
public class DailyNoticePushRecordServiceImpl extends ServiceImpl<DailyNoticePushRecordMapper, DailyNoticePushRecord> implements DailyNoticePushRecordService {



}

