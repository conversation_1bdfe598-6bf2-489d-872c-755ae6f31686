package com.hailiang.service;

import com.hailiang.entity.EvaluateBehaviourImportsRecordPO;
import com.hailiang.model.dto.ImportsBehaviourRecordMsgRowDTO;
import com.hailiang.model.entity.BehaviourRecord;

import java.util.List;

/**
 * 批量导入详情处理服务
 *
 * @Description: 批量导入详情处理服务
 * @Author: Jovi
 * @Date: Created in 2024/12/11
 * @Version: 2.0.0
 */
public interface ImportsEvaluateRecordDetailService {

    /**
     * 批量处理行为记录
     * @param batchSubmitId
     * @param behaviourRecordPOList
     * @return
     */
    Boolean batchDealBehaviourRecordDTOList(Long batchSubmitId,
                                            EvaluateBehaviourImportsRecordPO evaluateBehaviourImportsRecordPO,
                                            List<ImportsBehaviourRecordMsgRowDTO> importsBehaviourRecordMsgRowDTOList,
                                            List<BehaviourRecord> behaviourRecordPOList);
}
