package com.hailiang.service.impl;

import static com.hailiang.common.cache.constant.RedisKeyConstants.RESET_CAMPUS_ID_KEY;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.common.lock.annotation.RLock;
import com.hailiang.common.lock.model.LockTimeoutStrategy;
import com.hailiang.constant.Constant;
import com.hailiang.convert.SysTargetConvert;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.SaaSUserTypeEnum;
import com.hailiang.enums.ScoreTypeEnum;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.enums.SubmitRateEnum;
import com.hailiang.enums.TargetTypeEnum;
import com.hailiang.enums.TargetUserSubmitTypeEnum;
import com.hailiang.enums.TaskRoleTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.logic.TargetCopyLogic;
import com.hailiang.manager.SysTargetGroupManager;
import com.hailiang.manager.SysTargetManager;
import com.hailiang.manager.TargetUserManager;
import com.hailiang.mapper.mongo.TargetTemplateDao;
import com.hailiang.model.dto.TargetCopyDTO;
import com.hailiang.model.dto.query.TargetIdQuery;
import com.hailiang.model.dto.save.TargetSaveDTO;
import com.hailiang.model.dto.save.TargetSaveDTO.InnerEvaluateTargetUser;
import com.hailiang.model.dto.save.TargetSortDTO;
import com.hailiang.model.dto.save.TargetTemplateSaveDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.entity.SysTarget;
import com.hailiang.model.entity.SysTargetGroup;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TargetGroup;
import com.hailiang.model.entity.TargetUserPO;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.vo.SportGroupTargetVO;
import com.hailiang.model.vo.TargetDetailVO;
import com.hailiang.model.vo.TargetListTargetUserVO;
import com.hailiang.model.vo.TargetTargetUserInnerTargetUserVO;
import com.hailiang.model.vo.TargetUserQueryVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.dto.org.SchoolParam;
import com.hailiang.remote.saas.dto.org.TcOrgQueryDTO;
import com.hailiang.remote.saas.dto.staff.SchoolStaffQueryDTO;
import com.hailiang.remote.saas.enums.OrgTypeEnum;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.remote.saas.vo.educational.EduSchoolInfoVO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.role.ResRoleBffVO;
import com.hailiang.remote.saas.vo.school.TchSchoolVO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.SpeedConfigSetUpEditService;
import com.hailiang.service.SysTargetService;
import com.hailiang.service.TargetGroupService;
import com.hailiang.service.TargetService;
import com.hailiang.service.TargetTemplateService;
import com.hailiang.util.HolidayUtil;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2023/8/29
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class SysTargetServiceImpl implements SysTargetService {


    @Autowired
    private SysTargetGroupManager sysTargetGroupManager;
    @Autowired
    private SysTargetConvert convert;

    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Autowired
    private TargetTemplateService templateService;

    @Autowired
    private TargetService targetService;
    @Autowired
    private TargetGroupService targetGroupService;
    @Autowired
    private TargetUserManager targetUserManager;

    private String separator = ",";

    @Autowired
    private BasicInfoService basicInfoService;

    @Autowired
    private HolidayUtil holidayUtil;

    @Autowired
    private TargetCopyLogic targetCopyLogic;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private TargetTemplateDao templateDao;

    @Resource
    private SpeedConfigSetUpEditService speedConfigSetUpEditService;

    @Autowired
    private SysTargetManager sysTargetManager;

    @Value("${evaluate.target.icon.http:https}")
    private String targetIconHttp;
    @Value("${evaluate.target.icon.count}")
    private Integer targetIconCount;
    @Value("${evaluate.target.icon.prefix}")
    private String targetIconPrefix;
    @Value("${evaluate.target.icon.suffix}")
    private String targetIconSuffix;
    @Value("${evaluate.target.icon.path}")
    private String targetIconPath;
    @Value("${oss.config.bucketName}")
    private String bucketName;
    @Value("${oss.config.endPoint}")
    private String endPoint;
    /**
     * saas组织机构、教职工头像华为云链接前缀
     */
    @Value("${open.saasIconPrefix}")
    private String saasIconPrefix;


    /**
     * 添加指标
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(TargetSaveDTO dto) {
        if (ObjectUtil.isNull(dto.getId())) {
            add(dto);
            return;
        }
        update(dto);
    }

    /**
     * 添加指标
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(TargetSaveDTO dto) {

        // 去除iconUrl华为云链接前缀
        String iconUrl = dto.getIconUrl().substring(getIconUrlPrefix().length());
        dto.setIconUrl(iconUrl);
        SysTarget target = convert.toEvaluateTarget(dto);

        // 不限定频次时，可不输入提醒时间、法定节假日、寒暑假是否通知、是否每次必须提交
        checkParams(dto);

        // 插入表单到MongoDB
        TargetTemplateSaveDTO templateSaveDTO = new TargetTemplateSaveDTO()
                .setTemplateInfoList(dto.getTemplateInfoList());
        TargetTemplate template = templateService.save(templateSaveDTO);

        // 查当前学校当前分组下有多少指标
        int count = (int) sysTargetManager.count(new LambdaQueryWrapper<SysTarget>()
                .eq(SysTarget::getGroupId, dto.getGroupId()));
        target.setSortIndex(count + 1);
        target.setTargetStatus(Constant.YES);

        target.setTemplateId(template.getId());
        // 加分控件
        TargetSaveDTO.InnerScoreControl scoreControl = dto.getScoreControl();
        if (ObjectUtil.isNotNull(scoreControl)) {
            // 设置加分控件信息
            target.setScoreControlName(scoreControl.getScoreControlName());
            target.setScoreControlType(scoreControl.getScoreControlType());
            target.setScore(scoreControl.getScore());
            BigDecimal score = scoreControl.getScore();
            BigDecimal scoreValue = ObjectUtil.isNull(score) ? null : score.abs();
            target.setScoreValue(scoreValue);
            if (ScoreTypeEnum.REDUCE.getCode().equals(scoreControl.getScoreControlType()) && ObjectUtil.isNotNull(
                    scoreControl.getScore())) {
                target.setScore(scoreControl.getScore().abs().negate());
            }

        }

        // 保存指标
        sysTargetManager.saveOrUpdate(target);
        // 将指标id保存到MongoDB（和表单绑定）
        TargetTemplateSaveDTO templateSave = new TargetTemplateSaveDTO()
                .setTemplateId(template.getId())
                .setTargetId(target.getId());
        templateService.save(templateSave);
    }

    private void checkParams(TargetSaveDTO dto) {
        Assert.isTrue(dto.getTargetName().length() <= 10, "指标名称不能超过10个字符");
        // 不限定频次时，可不输入提醒时间、法定节假日、寒暑假是否通知、是否每次必须提交
        if (SubmitRateEnum.isRemind(dto.getSubmitType())) {
            Assert.isTrue(StrUtil.isNotBlank(dto.getRemindTime()), "提醒时间不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(dto.getHolidayNoticeFlag()), "法定节假日、寒暑假通知flag不能为空");
            Assert.isTrue(ObjectUtil.isNotNull(dto.getMustSubmitFlag()), "是否每次必须提交flag不能为空");
        }
    }

    /**
     * 修改指标
     *
     * @param dto
     */
    public void update(TargetSaveDTO dto) {

        // 去除iconUrl华为云链接前缀
        String iconUrl = dto.getIconUrl().substring(getIconUrlPrefix().length());
        dto.setIconUrl(iconUrl);

        SysTarget target = convert.toEvaluateTarget(dto);

        // 不限定频次时，可不输入提醒时间、法定节假日、寒暑假是否通知、是否每次必须提交
        checkParams(dto);

        // 修改指标时判断表单是否发送变化
        boolean theSameTemplate = isTheSameTemplate(dto.getId(), dto.getTemplateInfoList());
        // 表单发送修改才生成新表单
        TargetTemplate template = null;
        if (!theSameTemplate) {
            // 插入表单到MongoDB
            TargetTemplateSaveDTO templateSaveDTO = new TargetTemplateSaveDTO()
                    .setTemplateInfoList(dto.getTemplateInfoList());
            template = templateService.save(templateSaveDTO);

            target.setTemplateId(template.getId());
        }

        // 加分控件
        TargetSaveDTO.InnerScoreControl scoreControl = dto.getScoreControl();
        if (ObjectUtil.isNotNull(scoreControl)) {
            // 设置加分控件信息
            target.setScoreControlName(scoreControl.getScoreControlName());
            target.setScoreControlType(scoreControl.getScoreControlType());
            target.setScore(scoreControl.getScore());
            BigDecimal score = scoreControl.getScore();
            BigDecimal scoreValue = ObjectUtil.isNull(score) ? null : score.abs();
            target.setScoreValue(scoreValue);
            if (ScoreTypeEnum.REDUCE.getCode().equals(scoreControl.getScoreControlType()) && ObjectUtil.isNotNull(
                    scoreControl.getScore())) {
                target.setScore(scoreControl.getScore().abs().negate());
            }
        } else {
            target.setScoreControlName(null);
            target.setScoreControlType(null);
            target.setScore(null);
            target.setScoreValue(null);
        }

        sysTargetManager.update(target, new LambdaUpdateWrapper<SysTarget>()
                .eq(SysTarget::getId, target.getId())
                .set(SysTarget::getScore, target.getScore())
                .set(SysTarget::getScoreValue, target.getScoreValue())
                .set(SysTarget::getScoreControlName, target.getScoreControlName())
                .set(SysTarget::getScoreControlType, target.getScoreControlType())
        );

        // 指标发生改变才需要重新绑定
        if (!theSameTemplate) {
            // 将指标id保存到MongoDB（和表单绑定）
            TargetTemplateSaveDTO templateSave = new TargetTemplateSaveDTO()
                    .setTemplateId(template.getId())
                    .setTargetId(target.getId());
            templateService.save(templateSave);
        }

    }

    /**
     * 获取指标填写人
     *
     * @param target
     * @param targetUserList
     * @return
     */
//    private List<TargetUser> getTaskUserList(SysTarget target, List<TargetSaveDTO.InnerEvaluateTargetUser> targetUserList) {
//        if (CollUtil.isEmpty(targetUserList)) {
//            return Lists.newArrayList();
//        }
//        List<TargetUser> evaluateTargetUserList = convert.toEvaluateTargetUserList(targetUserList);
//
//        // 修改时先删除当前指标所有可提交人
//        if (ObjectUtil.isNotNull(target.getId())) {
//            targetUserService.remove(new LambdaQueryWrapper<TargetUser>()
//                    .eq(TargetUser::getSchoolId, WebUtil.getSchoolId())
//                    .eq(TargetUser::getCampusId, WebUtil.getCampusId())
//                    .eq(TargetUser::getTargetId, target.getId()));
//        }
//
//        for (TargetUser user : evaluateTargetUserList) {
//            user.setSchoolId(WebUtil.getSchoolId());
//            user.setCampusId(WebUtil.getCampusId());
//            user.setTargetId(target.getId());
//            user.setTenantId(WebUtil.getTenantId());
//            // 头像去除华为云链接前缀
//            user.setIconUrl(StrUtil.isBlank(user.getIconUrl()) ? user.getIconUrl() : user.getIconUrl().substring(saasIconPrefix.length() + 1));
//
//            // 教职工和家长，userCount处理为1
//            Boolean flag = SaaSUserTypeEnum.STAFF.getCode().equals(user.getSubmitType()) || SaaSUserTypeEnum.PARENT.getCode().equals(user.getSubmitType());
//            user.setUserCount(flag ? Constant.ONE : user.getUserCount());
//        }
//
//        return evaluateTargetUserList;
//    }

    /**
     * 判断指标模板是否发生变化
     *
     * @param targetId
     * @param templateInfoList
     * @return
     */
    private boolean isTheSameTemplate(Long targetId, List<TemplateInfoSaveDTO> templateInfoList) {
        if (ObjectUtil.isNull(targetId) || CollUtil.isEmpty(templateInfoList)) {
            return false;
        }
        // 获取模板
        TargetTemplate targetTemplate = templateService.getByTargetId(targetId);
        if (ObjectUtil.isNull(targetTemplate) || CollUtil.isEmpty(targetTemplate.getTemplateInfoList())) {
            return false;
        }

        JSON newTemplateJson = JSONUtil.parse(templateInfoList);
        JSON oldTemplateJson = JSONUtil.parse(targetTemplate.getTemplateInfoList());

        return newTemplateJson.equals(oldTemplateJson);

    }

    /**
     * 指标删除
     *
     * @param targetId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long targetId) {
        Assert.isTrue(ObjectUtil.isNotNull(targetId), "指标id不能为空");
        // 删除指标
        sysTargetManager.removeById(targetId);


    }

    /**
     * 禁用启用
     *
     * @param targetId 指标id
     * @param opType   操作类型 0：禁用  1：启用
     */
    @Override
    public void changeStatus(Long targetId, Integer opType) {
        Assert.isTrue(!ObjectUtil.hasNull(targetId, opType), "targetId、opType不能为空！");
        Assert.isTrue(opType.equals(Constant.ZERO) || opType.equals(Constant.ONE), "opType输入错误！");

        sysTargetManager.update(null, new LambdaUpdateWrapper<SysTarget>()
                .eq(SysTarget::getId, targetId)
                .set(SysTarget::getTargetStatus, opType)
                .set(SysTarget::getUpdateBy, WebUtil.getStaffId())
                .set(SysTarget::getUpdateTime, DateUtil.date()));
    }

    /**
     * 指标管理--排序
     *
     * @param sortList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(List<TargetSortDTO.InnerSort> sortList) {
        Assert.isTrue(CollUtil.isNotEmpty(sortList), "sortList不能为空");

        for (TargetSortDTO.InnerSort d : sortList) {
            sysTargetManager.update(null, new LambdaUpdateWrapper<SysTarget>() // NOSONAR
                    .eq(SysTarget::getId, d.getTargetId())
                    .set(SysTarget::getSortIndex, d.getSortIndex())
                    .set(SysTarget::getGroupId, d.getGroupId())
                    .set(SysTarget::getUpdateBy, WebUtil.getStaffId())
                    .set(SysTarget::getUpdateTime, DateUtil.date()));
        }
    }

    /**
     * 查指标详情
     *
     * @param targetId
     * @return
     */
    @Override
    public TargetDetailVO detail(Long targetId) {
        Assert.isTrue(ObjectUtil.isNotNull(targetId), "targetId不能为空");
        // 查指标
        SysTarget target = get(targetId);
        if (ObjectUtil.isNull(target)) {
            return null;
        }
        target.setIconUrl(getIconUrlPrefix() + target.getIconUrl());
        TargetDetailVO targetDetailVO = convert.toTargetDetailVO(target);

        // 设置表单json字符串
        String templateJsonStr = templateService.getTemplateJsonStrByTargetId(target.getId());
        targetDetailVO.setTemplateInfoJsonStr(templateJsonStr);
        return targetDetailVO;
    }

    /**
     * 根据指标id获取指标
     *
     * @param targetId
     * @return
     */
    @Override
    public SysTarget get(Long targetId) {
        if (ObjectUtil.isNull(targetId)) {
            return null;
        }
        SysTarget target = sysTargetManager.getOne(new LambdaQueryWrapper<SysTarget>()
                .eq(SysTarget::getId, targetId)
        );
        return target;
    }

    /**
     * 获取图标列表
     *
     * @return
     */
    @Override
    public List<String> listIcon() {
        List<String> iconUrl = new ArrayList<>();
        for (int i = 1; i <= targetIconCount; i++) {
            StringBuffer sb = new StringBuffer();
            sb.append(targetIconHttp).append("://").append(bucketName).append(StrPool.DOT).append(endPoint)
                    .append(targetIconPath).append(targetIconPrefix).append(i).append(targetIconSuffix);
            iconUrl.add(sb.toString());
        }
        return iconUrl;

    }

    /**
     * 指标填写人列表
     *
     * @return
     */
//    public List<TargetListTargetUserVO> listTargetWritePeople(ListTargetWritePeopleDaoDTO dto) {
//        // 获取今天需要发送的指标集合
//        List<TargetWriteUserVO> targetList = listTodaySendMessageTarget(dto);
//        if (CollUtil.isEmpty(targetList)) {
//            return Lists.newArrayList();
//        }
//
//        List<TargetListTargetUserVO> userList = new ArrayList<>();
//        // 遍历获取指标填写人
//        for (TargetWriteUserVO target : targetList) {
//
//            // 指标填写人为空，不发送
//            List<TargetTargetUserInnerTargetUserVO> targetUserList = target.getTargetUserList();
//            if (CollUtil.isEmpty(targetUserList)) {
//                continue;
//            }
//
//            TargetListTargetUserVO targetUser = convert.toTargetListTargetUserVO(target);
//            // 设置提醒时间
//            // 提醒时间转换为日期格式
//            Date remindTime = DateUtil.parse(DateUtil.format(new Date(), "yyyyMMdd") + target.getRemindTimeStr() + "00");
//            // 设置催办时间
//            Date urgeTime = DateUtil.offsetHour(remindTime, target.getUrgeTimeInt());
//            targetUser.setRemindTime(remindTime);
//            targetUser.setUrgeTime(urgeTime);
//
//            // 如果是非必填任务提醒,提前判断数据是否需要推送,避免非推送数据频繁调用saas接口
//            if (Constant.NO.equals(dto.getMustSubmitFlag())) {
//                Date now = DateUtil.date();
//                DateTime nowMin = DateUtil.beginOfMinute(now);
//                DateTime preNowMin = DateUtil.offsetMinute(nowMin, -1);
//                boolean flag = Boolean.FALSE;
//                if (nowMin.isAfterOrEquals(targetUser.getRemindTime()) && preNowMin.isBefore(targetUser.getRemindTime())) {
//                    flag = Boolean.TRUE;
//                }
//                if (!flag) {
//                    continue;
//                }
//            }
//
//            // 通过行政组织id获取到老师信息(老师)
//            List<TargetListTargetUserVO.InnerTargetUser> targetStaffInfoList = queryStaffInfo(targetUserList);
//
//            // 通过教务组织id获取到学生信息(家长)
//            List<TargetListTargetUserVO.InnerTargetUser> targetStudentInfoList = queryStudentInfo(targetUserList, Convert.toLong(target.getSchoolId()));
//
//            // 填写人信息合并(老师+学生)
//            targetStaffInfoList.addAll(targetStudentInfoList);
//
//            // 设置指标填写人
//            targetUser.setTargetUserList(targetStaffInfoList);
//            userList.add(targetUser);
//        }
//        return userList;
//    }
//
//    /**
//     * 通过行政组织id获取到老师信息
//     *
//     * @param targetUserList
//     * @return
//     */
//    private List<TargetListTargetUserVO.InnerTargetUser> queryStaffInfo(List<TargetTargetUserInnerTargetUserVO> targetUserList) {
//        log.info("通过行政组织id获取到老师信息,入参:[{}]", targetUserList);
//        try {
//            // 类型1:数据库中存的是行政组织id的,筛选出填写人类型为行政组织id或角色的
//            List<TargetTargetUserInnerTargetUserVO> orgRoleTargetUserList = targetUserList.stream().filter(user -> SaaSUserTypeEnum.ORGANIZATION.getCode().equals(user.getSubmitType()) || SaaSUserTypeEnum.ROLE.getCode().equals(user.getSubmitType())).collect(Collectors.toList());
//            List<BasicInfoListAllUserDTO> queryVO = convert.toBasicInfoListAllUserDTOList(orgRoleTargetUserList);
//            // 获取saas人员列表
//            List<BasicInfoListAllUserVO> saasUserList = basicInfoService.listAllUser(queryVO);
//            List<TargetListTargetUserVO.InnerTargetUser> goalUserList = convert.toTargetInnerTargetUserList(saasUserList);
//            log.info("类型1:通过数据库中存的是行政组织id的来获取saas教职工信息集合:{}", saasUserList);
//
//            // 类型2:数据库中存的就是教师id的
//            List<Long> staffIdList = targetUserList.stream().filter(user -> SaaSUserTypeEnum.STAFF.getCode().equals(user.getSubmitType())).map(TargetTargetUserInnerTargetUserVO::getSubmitValue).collect(Collectors.toList());
//            if (CollUtil.isNotEmpty(staffIdList)) {
//                // 查人员信息(老师)
//                List<StaffBatchVO> staffList = basicInfoService.queryBasicInfoByStaffIds(new StaffInfoQueryDTO().setStaffIds(staffIdList));
//                List<BasicInfoListAllUserVO> basicInfoListAllUsers = convert.toBasicInfoListAllUserVOList(staffList);
//                List<TargetListTargetUserVO.InnerTargetUser> targetStaffList = convert.toTargetInnerTargetUserList(basicInfoListAllUsers);
//                targetStaffList.forEach(staff -> staff.setUserType(SaasUserTypeQueryEnum.STAFF.getCode()));
//
//                log.info("类型2:数据库中存的就是教师id的来获取saas教职工信息集合:{}", targetStaffList);
//                goalUserList.addAll(targetStaffList);
//            }
//
//            log.info("获取指标下所有老师信息,老师信息集合:[{}]", goalUserList);
//
//            // 人员去重
//            return CollUtil.isEmpty(goalUserList) ? goalUserList : goalUserList.stream().distinct().collect(Collectors.toList());
//        } catch (Exception e) {
//            log.warn("通过教务组织id获取到老师信息异常,返回空集合", e);
//        }
//        return new ArrayList<>();
//    }

    /**
     * 通过教学组织id获取到学生信息
     *
     * @param targetUserList
     * @param schoolId
     * @return {@link List}<{@link TargetListTargetUserVO.InnerTargetUser}>
     */
    private List<TargetListTargetUserVO.InnerTargetUser> queryStudentInfo(
            List<TargetTargetUserInnerTargetUserVO> targetUserList, Long schoolId) {
        log.info("通过教学组织id获取到学生信息,入参:[{}],schoolId:{}", targetUserList, schoolId);
        try {
            // 类型1:数据库中存的是教学组织id的,筛选出填写人类型为教学组织id
            List<TargetTargetUserInnerTargetUserVO> parentTargetUserList = targetUserList.stream()
                    .filter(user -> OrgTypeEnum.getCodes().contains(Convert.toInt(user.getSubmitType())))
                    .collect(Collectors.toList());
            List<EduOrgTreeVO> eduOrgTrees = new ArrayList<>();
            parentTargetUserList.forEach(s -> {
                EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
                eduOrgQueryDTO.setCurrentId(s.getSubmitValue());
                eduOrgQueryDTO.setEndType(5);
                eduOrgQueryDTO.setCurrentIdType(OrgTypeEnum.getByCode(s.getSubmitType()));
                eduOrgQueryDTO.setIsTree(Constant.NO);
                //根据组织机构查询出班级
                List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
                List<EduOrgTreeVO> collect = eduOrgTreeVOS.stream().filter(t -> t.getType().equals(5))
                        .collect(Collectors.toList());
                eduOrgTrees.addAll(collect);
            });
            //过滤出所有班级id
            List<Long> classIds = eduOrgTrees.stream().filter(s -> s.getType().equals(5)).map(EduOrgTreeVO::getId)
                    .distinct().collect(Collectors.toList());
            log.info("类型1:通过教务组织id获取到班级id信息,班级id信息集合:{}", classIds);

            //根据班级查询所有学生信息
            List<EduStudentInfoVO> eduStudentInfoByClassIds = new ArrayList<>();
            for (Long classId : classIds) {
                EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
                eduStudentPageQueryDTO.setSchoolId(schoolId);
                eduStudentPageQueryDTO.setClassId(classId);
                eduStudentPageQueryDTO.setGraduationStatus("0");
                List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
                eduStudentInfoByClassIds.addAll(eduStudentInfoVOS);
            }
            log.info("类型1:通过班级id集合获取到学生信息,学生信息集合:{}", eduStudentInfoByClassIds);

            // 类型2:根据数据库中原本值为学生id的数据去saas查询出这些学生的信息
            List<Long> studentIds = targetUserList.stream()
                    .filter(user -> SaaSUserTypeEnum.PARENT.getCode().equals(user.getSubmitType()))
                    .map(TargetTargetUserInnerTargetUserVO::getSubmitValue).collect(Collectors.toList());
            // 通过学生id集合查询学生信息
            List<EduStudentInfoVO> eduStudentInfoByStudentIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(studentIds)) {
                EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
                eduStudentPageQueryDTO.setSchoolId(schoolId);
                eduStudentPageQueryDTO.setStudentIds(studentIds);
                eduStudentInfoByStudentIds = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
            }
            log.info("类型2:根据数据库中原本值为学生id的数据去saas查询出这些学生的信息,学生信息集合:{}",
                    eduStudentInfoByStudentIds);

            //数据信息合并去重
            eduStudentInfoByClassIds.addAll(eduStudentInfoByStudentIds);
            List<EduStudentInfoVO> collect = eduStudentInfoByClassIds.stream().distinct().collect(Collectors.toList());

            //封装数据
            List<TargetListTargetUserVO.InnerTargetUser> targetStudentList = new ArrayList<>();
            collect.forEach(s -> {
                TargetListTargetUserVO.InnerTargetUser innerTargetUser = new TargetListTargetUserVO.InnerTargetUser();
                innerTargetUser.setStaffId(Convert.toStr(s.getId()));
                innerTargetUser.setUserType(TaskRoleTypeEnum.PARENT.getCode());
                targetStudentList.add(innerTargetUser);
            });
            log.info("查询指标下所有学生信息集合:{}", targetStudentList);
            return targetStudentList;
        } catch (Exception e) {
            log.warn("通过教务组织id获取到学生信息异常,返回空集合", e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取今天需要发送的指标集合
     *
     * @return
     */
//    private List<TargetWriteUserVO> listTodaySendMessageTarget(ListTargetWritePeopleDaoDTO dto) {
//        if (ObjectUtil.isNull(dto)) {
//            // 默认查每次必须提交的
//            dto = new ListTargetWritePeopleDaoDTO().setMustSubmitFlag(Constant.YES);
//        }
//        List<TargetWriteUserVO> targetList = targetMapper.listTargetWritePeople(dto);
//        if (CollUtil.isEmpty(targetList)) {
//            return Collections.emptyList();
//        }
//        return targetList.stream().filter(target -> {
//            // 过滤出提醒时间、催办时间不为空且今天提醒的
//            boolean flag = StrUtil.isBlank(target.getRemindTimeStr()) || ObjectUtil.isNull(target.getUrgeTimeInt());
//            if (flag) {
//                return false;
//            }
//            return todayRemind(target.getHolidayNoticeFlag(), target.getSubmitType(), target.getSubmitDate());
//
//        }).collect(Collectors.toList());
//    }

    /**
     * 查询今天是否通知
     *
     * @param holidayNoticeFlag
     * @param submitType
     * @param submitDate
     * @return
     */
    private boolean todayRemind(Integer holidayNoticeFlag, Integer submitType, String submitDate) {
        if (ObjectUtil.hasNull(holidayNoticeFlag, submitType)) {
            return false;
        }
        // 类型是工作日每天，提醒日期可以为空，其他类型不可以为空
        boolean holidayFlag =
                !SubmitRateEnum.WORK_DAY.getSubmitType().equals(submitType) && !SubmitRateEnum.NO_RATE.getSubmitType()
                        .equals(submitType);
        if (holidayFlag) {
            if (StrUtil.isBlank(submitDate)) {
                return false;
            }
        }
        // 没有该类型则不发送
        String submitRateName = SubmitRateEnum.getSubmitRateName(submitType);
        if (StrUtil.isEmpty(submitRateName)) {
            return false;
        }
        // 寒暑假、节假日不通知
        if (Constant.NO.equals(holidayNoticeFlag)) {
            // 获取今天是否寒暑假、节假日
            Boolean todayIsHoliday = holidayUtil.todayIsHoliday();
            // 是则不发送
            if (todayIsHoliday) {
                return false;
            }
        }
        // 根据提醒类型判定今天是否要发送
        if (submitType.equals(SubmitRateEnum.WORK_DAY.getSubmitType())) {
            // 工作日每天--周一到周五为工作日
            return !DateUtil.isWeekend(new Date());

        } else if (submitType.equals(SubmitRateEnum.EVERY_WEEK.getSubmitType())) {
            // 每周
            // Hutool工具类dayOfWeek方法： 获得指定日期是星期几，1表示周日，2表示周一
            int day = DateUtil.dayOfWeek(new Date());
            day = day == 1 ? 7 : day - 1;
            // 提交日期包含今天则提醒
            return submitDate.contains(String.valueOf(day));
        } else if (submitType.equals(SubmitRateEnum.EVERY_MONTH.getSubmitType())) {
            // 每月
            // 今天是每月第几天
            int day = DateUtil.thisDayOfMonth();
            // 提交日期包含今天则发送
            if (submitDate.contains(String.valueOf(day))) {
                return true;
            }
            // 获取本月最后一天
            int endDay = DateUtil.dayOfMonth(DateUtil.endOfMonth(new Date()));
            if (31 == endDay) {
                return false;
            }
            // 只要提交日期有比本月最后一天的则发送
            long count = Stream.of(submitDate.split(StrPool.COMMA)).filter(date -> Integer.parseInt(date) > endDay)
                    .count();
            if (count > 0L) {
                return true;
            }
            return false;

        } else if (submitType.equals(SubmitRateEnum.EVERY_YEAR.getSubmitType())) {
            // 每年
            // 获取今天得月日 比如：0107 一月7日
            String day = DateUtil.format(new Date(), "MMdd");
            // 如果提交日期包含今天则发送
            List<String> dayArray = Arrays.asList(submitDate.split(StrPool.COMMA));
            boolean contains = dayArray.contains(day);
            if (contains) {
                return true;
            }
            // 如果今年是平年，今天是2月28日，提交日期包含2月29，则提示发送
            // 今年是否平年
            boolean leapYear = DateUtil.isLeapYear(DateUtil.thisYear());
            // 闰年则返回不提醒
            if (leapYear) {
                return false;
            }
            // 今天不是2月28则不提醒
            if (!"0228".equals(day)) {
                return false;
            }
            // 如果提醒时间包含2月29日则提醒
            if (dayArray.contains("0229")) {
                return true;
            }
            return false;
        }
        return false;
    }

    /**
     * 获取iconurl 华为云链接前缀
     *
     * @return
     */
    @Override
    public String getIconUrlPrefix() {
        return targetIconHttp + "://" + bucketName + StrPool.DOT + endPoint + StrPool.SLASH;
    }

    @Override
    public Page<TargetUserQueryVO> queryStaffListBySchool(SchoolStaffQueryDTO schoolStaffQueryDTO) {
        return null;
    }

    /**
     * 检查体测分组和指标
     *
     * @return {@link SportGroupTargetVO}
     */
    @Override
//    @RLock
    public SportGroupTargetVO checkSportGroupAndTarget() {
        String campusId = WebUtil.getCampusId();
        return redisUtil.getOrAdd(RedisKeyConstants.SPORT_DATA_GROUP_ID + campusId, this::getSportGroupTargetVO,
                CacheConstants.ONE_HOUR);
    }

    private SportGroupTargetVO getSportGroupTargetVO() {
        SportGroupTargetVO sportGroupTargetVO = new SportGroupTargetVO();
        // 获取或创建体测分组Id
        Long sportGroupId = getOrCreateSportGroupId();
        // 获取或创建体测指标Id
        Long sportTargetId = getOrCreateSportTargetId(sportGroupId);
        sportGroupTargetVO.setGroupId(sportGroupId);
        sportGroupTargetVO.setTargetId(sportTargetId);

        return sportGroupTargetVO;
    }

    @Override
    @RLock(keys = {"#targetCopyDTO.targetId"})
    @Transactional(rollbackFor = Exception.class, timeout = 60)
    public boolean copy(TargetCopyDTO targetCopyDTO) {
        Assert.isTrue(targetCopyDTO.getTargetName().length() <= 6, "指标名称不能超过6个字符");
        // 查指标
        SysTarget target = get(targetCopyDTO.getTargetId());

        if (ObjectUtil.isNull(target)) {
            log.info("[复制指标]-指标不存在，信息：{}", JSONUtil.toJsonStr(targetCopyDTO));
            return false;
        }
        // 复制表单
        String oldTemplateId = target.getTemplateId();
        TargetTemplate newTemplate = targetCopyLogic.copyTemplate(oldTemplateId);

        // 复制指标
        Long newTargetId = copySysTarget(target, targetCopyDTO, newTemplate);
        // 复制指标user
//        targetCopyLogic.copyTargetUser(target.getId(), newTargetId);

        return true;
    }

    @Override
    @RLock(waitTime = 20, leaseTime = 60, name = "checkAndCopyTargetAsync", keys = {"#tenantId", "#schoolId",
            "#campusId"}, lockTimeoutStrategy = LockTimeoutStrategy.KEEP_ACQUIRE)
    public Boolean checkAndCopyTargetAsync(String tenantId, String schoolId, String campusId) {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //入参校验
        Assert.isFalse(StrUtil.isBlank(tenantId) || StrUtil.isBlank(schoolId) || StrUtil.isBlank(campusId),
                () -> new BizException(BizExceptionEnum.PARAMETER_ERROR.getMessage()));

        Object resetValue = redisUtil.get(RESET_CAMPUS_ID_KEY + campusId);

        if (resetValue == null) {

            //查询该校区有没有综评指标，以来作为是否是新校区的标志
            long count = targetGroupService.countWithDelete(tenantId, schoolId, campusId,
                    DataSourceEnum.EVALUATE.getCode());

            if (Convert.toInt(count) > Constant.ZERO) {
                log.info("[该校区已初始过指标模板，campusId：「{}」，不再二次进行初始化]", campusId);
                return false;
            }
        }

        log.info("[指标模板复制]-开始复制");
        // 复制指标模板
        Boolean b = Boolean.TRUE;
        try {
            b = copyTarget(tenantId, schoolId, campusId);
        } catch (Exception e) {
            log.error("「指标模板复制异常」--message:{}");
        }
        stopWatch.stop();
        log.info("[指标模板复制完成]--消耗时长：[{}]", stopWatch.getLastTaskTimeMillis());

        redisUtil.deleteKey(RESET_CAMPUS_ID_KEY + campusId);

        return b;
    }

    /**
     * 获取学校所属租户id
     *
     * @param schoolId 学校 id
     * @return
     */
    @Override
    public Long getTenantId(String schoolId) {
        if (StrUtil.isBlank(schoolId)) {
            return null;
        }
        TchSchoolVO tchSchoolVO = basicInfoRemote.querySchoolById(Convert.toLong(schoolId));
        if (ObjectUtil.isNull(tchSchoolVO) || ObjectUtil.isNull(tchSchoolVO.getTenantId())) {
            log.error("[获取学校所属租户-异常]-[当前学校不存在],当前学校id:[{}]", schoolId);
            return null;
        }
        Long schoolTenantId = Convert.toLong(tchSchoolVO.getTenantId());
        return schoolTenantId;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyTarget(String tenantId, String schoolId, String campusId) {

        log.info("【指标复制开始】-tenantId: 【{}】，school_id: 【{}】，campus_id：【{}】，", tenantId, schoolId, campusId);

        //入参校验
        Assert.isFalse(StrUtil.isBlank(tenantId) || StrUtil.isBlank(schoolId) || StrUtil.isBlank(campusId),
                () -> new BizException(BizExceptionEnum.PARAMETER_ERROR.getMessage()));

        //指标分组
        List<SysTargetGroup> sysTargetGroups = sysTargetGroupManager.list()
                .stream()
                .filter(item-> item.getGroupType().equals(TargetTypeEnum.DEFAULT.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(sysTargetGroups)) {
            log.info("[指标模板复制]-没有模板，无需复制");
            return false;
        }

        //指标
        List<Target> targetList = new ArrayList<>();
        List<TargetUserPO> targetUserPOList = new ArrayList<>();

        // 查询学校信息
        SchoolParam schoolQueryDTO = new SchoolParam();
        schoolQueryDTO.setSchoolIds(Collections.singletonList(Convert.toLong(schoolId)));
        List<EduSchoolInfoVO> eduSchoolInfoVOS = basicInfoRemote.listSaasStudentInfoByStudentIds(schoolQueryDTO);

        List<TargetGroup> targetGroups = convert.toTargetGroup(sysTargetGroups);
        targetGroups.stream().forEach(s -> {
            long id = SnowFlakeIdUtil.nextId();
            //分组下面的指标
            List<SysTarget> sysTargets = sysTargetManager.list(new LambdaQueryWrapper<SysTarget>()
                    .eq(SysTarget::getGroupId, s.getId())
                    .eq(SysTarget::getTargetType, TargetTypeEnum.DEFAULT.getCode()));
            List<Target> targets = convert.toTargets(sysTargets);
            targets.stream().forEach(m -> {
                m.setGroupId(id);
                long targetId = SnowFlakeIdUtil.nextId();
                m.setId(targetId);
                TargetTemplate targetTemplate = templateService.get(m.getTemplateId());
                //重置 optionId
                List<TemplateInfoSaveDTO> templateInfoList = listTemplateInfoSaveDTOS(targetTemplate, targetId);

                TargetTemplate templateSave = new TargetTemplate()
                        .setTemplateInfoList(templateInfoList)
                        .setTargetId(targetId);
                TargetTemplate save = templateDao.save(templateSave);
                m.setTemplateId(save.getId());
                m.setTenantId(tenantId);
                m.setSchoolId(schoolId);
                m.setCampusId(campusId);
                m.setCreateBy("system");
                m.setStaffFullCheckFlag(1);
                TargetUserPO targetUserPO = this.buildTargetUser(m, eduSchoolInfoVOS);
                if (Objects.nonNull(targetUserPO)) {
                    targetUserPOList.add(targetUserPO);
                }
            });
            targetList.addAll(targets);
            s.setId(id);
            s.setTenantId(tenantId);
            s.setSchoolId(schoolId);
            s.setCampusId(campusId);
            s.setCreateBy("system");
        });
        //新增指标分组
        targetGroupService.saveBatch(targetGroups);

        //指标
        targetService.saveBatch(targetList);

        //同步初始化复制极速点评指标
        speedConfigSetUpEditService.checkAndCopySpeedTarget(tenantId, schoolId, campusId, targetList);

        // 指标填写人
        targetUserManager.saveBatch(targetUserPOList);

        return true;
    }

    private TargetUserPO buildTargetUser(Target target, List<EduSchoolInfoVO> eduSchoolInfoVOS) {
        if (CollUtil.isEmpty(eduSchoolInfoVOS)) {
            return null;
        }
        TargetUserPO targetUserPO = new TargetUserPO();
        targetUserPO.setTenantId(target.getTenantId());
        targetUserPO.setSchoolId(target.getSchoolId());
        targetUserPO.setCampusId(target.getCampusId());
        targetUserPO.setTargetId(target.getId());
        targetUserPO.setSubmitUserName(eduSchoolInfoVOS.get(0).getSchoolName());
        targetUserPO.setSubmitType(TargetUserSubmitTypeEnum.ORG.getCode());
        targetUserPO.setSubmitValue(eduSchoolInfoVOS.get(0).getOrgId());
        targetUserPO.setCreateBy("system");
        return targetUserPO;
    }

    /**
     * 重置 optionId
     *
     * @param targetTemplate
     * @return
     */
    private List<TemplateInfoSaveDTO> listTemplateInfoSaveDTOS(TargetTemplate targetTemplate, Long targetId) {
        List<TemplateInfoSaveDTO> templateInfoList = targetTemplate.getTemplateInfoList();
        List<TemplateInfoSaveDTO> infoSaveDTOS = templateInfoList.stream()
                .filter(infoSaveDTO -> !SubmitInfoTypeEnum.STUDENT.getText().equals(infoSaveDTO.getType()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(infoSaveDTOS)) {
            log.info("[重置 optionId]-指标模板id:[{}],没有需要重置的控件", targetTemplate.getId());
            return templateInfoList;
        }

        String idStr = targetId.toString();

        for (TemplateInfoSaveDTO infoSaveDTO : infoSaveDTOS) {

            String key = infoSaveDTO.getKey();
            String newKey = key.replaceFirst("\\d+_", idStr + "_");
            //控件 id
            infoSaveDTO.setKey(newKey);

            List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> infoSubSaves = infoSaveDTO.getOptions().getOptions();
            if (CollUtil.isNotEmpty(infoSubSaves)) {
                for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave infoSubSave : infoSubSaves) {
                    String optionKey = infoSubSave.getKey();
                    String newOptionKey = optionKey.replaceFirst("\\d+_", idStr + "_");
                    //选项 id
                    infoSubSave.setKey(newOptionKey);
                }
            }

            List<LinkedHashMap> list = infoSaveDTO.getList();
            if (CollUtil.isNotEmpty(list)) {
                for (LinkedHashMap linkedHashMap : list) {
                    if (linkedHashMap.containsKey("key")) {
                        String optionKey = (String) linkedHashMap.get("key");
                        linkedHashMap.put("key", optionKey.replaceFirst("\\d+_", idStr + "_"));
                    }
                    if (linkedHashMap.containsKey("options")) {
                        LinkedHashMap map1 = (LinkedHashMap) linkedHashMap.get("options");
                        if (ObjectUtil.isNotEmpty(map1)) {
                            List<LinkedHashMap> options1 = (List<LinkedHashMap>) map1.get("options");
                            if (CollUtil.isNotEmpty(options1)) {
                                for (LinkedHashMap map2 : options1) {
                                    if (map2.containsKey("key")) {
                                        String optionKey = (String) map2.get("key");
                                        map2.put("key", optionKey.replaceFirst("\\d+_", idStr + "_"));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return templateInfoList;
    }

    @Override
    public void setPictureEvaluate(TargetIdQuery query) {
        if (BeanUtil.isEmpty(query) || CollUtil.isEmpty(query.getTargetIds())) {
            log.info("运营平台-图文点评设置，参数为空，直接返回");
            return;
        }

        //原图文点评
        List<Long> originalTargetIds = this.listPictureEvaluates();
        // 设置的targetIds是否与原图文点评的targetIds相同，如果相同，无需处理
        if (query.getTargetIds() == originalTargetIds) {
            return;
        }
        if (CollUtil.isNotEmpty(originalTargetIds)) {
            //清空原图文点评
            sysTargetManager.update(null, new LambdaUpdateWrapper<SysTarget>()
                    .in(SysTarget::getId, originalTargetIds)
                    .set(SysTarget::getPictureEvaluateFlag, Constant.NO)
                    .set(SysTarget::getUpdateBy, WebUtil.getStaffId())
                    .set(SysTarget::getUpdateTime, DateUtil.now())
            );
        }
        //新增图文点评
        List<SysTarget> targets = sysTargetManager.list(
                new LambdaQueryWrapper<SysTarget>().in(SysTarget::getId, query.getTargetIds()));
        targets.forEach(target -> {
            target.setPictureEvaluateFlag(Constant.YES);
            target.setPictureSortIndex(query.getTargetIds().indexOf(target.getId()) + 1);
            target.setUpdateBy(WebUtil.getStaffId());
            target.setUpdateTime(DateUtil.date());
        });
        sysTargetManager.updateBatchById(targets);
    }

    /**
     * 图文点评指标列表
     */
    @Override
    public List<Long> listPictureEvaluates() {
        List<SysTarget> targets = sysTargetManager.list(new LambdaQueryWrapper<SysTarget>()
                .select(SysTarget::getId)
                .eq(SysTarget::getPictureEvaluateFlag, Constant.YES)
                .eq(SysTarget::getTargetType, TargetTypeEnum.DEFAULT.getCode())
                .orderByAsc(SysTarget::getPictureSortIndex));
        if (CollUtil.isEmpty(targets)) {
            return Collections.emptyList();
        }
        return targets.stream().map(SysTarget::getId).collect(Collectors.toList());
    }

    /**
     * 获取体测分组Id，如果不存在就创建
     *
     * @return {@link Long}
     */
    private Long getOrCreateSportGroupId() {
        String tenantId = WebUtil.getTenantId();
        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();
        // 查看表里有没有分组，没有自动创建，有的话 使用表里面的分组
        List<SysTargetGroup> targetGroups = sysTargetGroupManager.list(new LambdaQueryWrapper<SysTargetGroup>()
                .eq(SysTargetGroup::getModuleCode, ModuleEnum.SPORT.getCode())
                .eq(SysTargetGroup::getGroupName, DataSourceEnum.SPORT.getMessage())
                .eq(SysTargetGroup::getDataSource, DataSourceEnum.SPORT.getCode())
                .orderByAsc(SysTargetGroup::getCreateTime));

        if (CollUtil.isNotEmpty(targetGroups)) {
            if (targetGroups.size() > 1) {
                log.warn("请注意，体测分组有多个，校区id：{}", campusId);
            }
            log.info("已有体测分组, id：{}", targetGroups.get(0).getId());
            return targetGroups.get(0).getId();
        }

        SysTargetGroup targetGroup = new SysTargetGroup();
        targetGroup.setModuleCode(ModuleEnum.SPORT.getCode());
        targetGroup.setModuleName(ModuleEnum.SPORT.getMessage());
        targetGroup.setGroupName(DataSourceEnum.SPORT.getMessage());
        targetGroup.setSortIndex(999);
        targetGroup.setDataSource(DataSourceEnum.SPORT.getCode());
        targetGroup.setCreateBy("system");
        targetGroup.setCreateTime(DateUtil.date());

        sysTargetGroupManager.save(targetGroup);
        log.info("无体测分组，已新建，id：{}", targetGroup.getId());

        return targetGroup.getId();
    }

    /**
     * 获取或创建体测指标Id
     *
     * @param groupId 分组Id
     * @return {@link Long}
     */
    private Long getOrCreateSportTargetId(Long groupId) {
        String tenantId = WebUtil.getTenantId();
        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();
        // 查看表里有没有分组，没有自动创建，有的话 使用表里面的分组
        List<SysTarget> targets = sysTargetManager.list(new LambdaQueryWrapper<SysTarget>()
                .eq(SysTarget::getTargetName, DataSourceEnum.SPORT.getMessage())
                .eq(SysTarget::getDataSource, DataSourceEnum.SPORT.getCode())
                .orderByAsc(SysTarget::getCreateTime));

        if (CollUtil.isNotEmpty(targets)) {
            if (targets.size() > 1) {
                log.warn("请注意，体测指标有多个，校区id：{}", campusId);
            }
            log.info("已有体测指标, id：{}", targets.get(0).getId());
            return targets.get(0).getId();
        }
        SysTarget target = new SysTarget();

        target.setTargetName(DataSourceEnum.SPORT.getMessage());
        target.setTargetStatus(1);
        target.setGroupId(groupId);
        target.setDataSource(DataSourceEnum.SPORT.getCode());
        target.setSortIndex(999);
        // staffId转换
        target.setCreateBy("system");
        target.setCreateTime(DateUtil.date());
        // 默认字段
        target.setSubmitType(5);
        target.setSendParentFlag(1);

        sysTargetManager.save(target);
        log.info("无体测指标，已新建，id：{}", target.getId());

        return target.getId();
    }


    public Long copySysTarget(SysTarget target, TargetCopyDTO targetCopyDTO, TargetTemplate newTemplate) {
        log.info("[复制指标]-复制指标，请求参数：[{}], 新模板id：[{}]", JSONUtil.toJsonStr(targetCopyDTO),
                newTemplate.getId());
        // 当前分组下有多少指标
        List<SysTarget> targets = sysTargetManager.list(new LambdaQueryWrapper<SysTarget>()
                .eq(SysTarget::getGroupId, target.getGroupId()));

        SysTarget newTarget = new SysTarget();
        BeanUtil.copyProperties(target, newTarget);

        newTarget.setTargetStatus(0);
        newTarget.setTemplateId(newTemplate.getId());
        newTarget.setSortIndex(targets.size() + 1);
        newTarget.setTargetName(targetCopyDTO.getTargetName());
        newTarget.setId(null);

        sysTargetManager.save(newTarget);
        log.info("[复制指标]-复制指标，成功，新指标id：{}", newTarget.getId());

        // 将指标id更新到MongoDB（和表单绑定）
        TargetTemplateSaveDTO templateSave = new TargetTemplateSaveDTO()
                .setTemplateId(newTemplate.getId())
                .setTargetId(newTarget.getId());
        templateService.save(templateSave);
        log.info("[复制指标]-复制指标，模板对应的指标Id更新成功");

        return newTarget.getId();
    }
}
