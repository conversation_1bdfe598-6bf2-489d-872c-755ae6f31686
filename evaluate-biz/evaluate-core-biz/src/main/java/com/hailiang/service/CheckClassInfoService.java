package com.hailiang.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hailiang.model.dto.query.*;
import com.hailiang.model.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【check_class_info(班级检查记录)】的数据库操作Service
* @createDate 2023-07-19 11:38:00
*/
public interface CheckClassInfoService {

    IPage<DutyRecordPageVO> queryCheckClassInfoPageWithAuth(DutyRecordPageDTO param);

    IPage<DutyRecordPageVO> queryCheckClassInfoPage(DutyRecordPageMapperDTO param);

    ClassMobileCompareVO queryClassCompareScoreListWithAuth(ClassCompareListDTO param);

    ClassMobileCompareVO queryClassCompareScoreList(ClassCompareListDTO param);

    ClassCompareListVO queryClassScoreListWithAuth(ClassCompareListDTO param);
    /**
     * pc端--班级排行导出
     */
    void export(ClassCompareListDTO param,HttpServletResponse response) throws IOException;
    void exportOld(ClassCompareListDTO param,HttpServletResponse response) throws IOException;


    List<GradeClassCompareListVO> queryGradeClassScoreListWithAuth(ClassCompareListDTO param);

    Boolean queryShowClassLevelFlag(ClassCompareLevelListDTO param);

    QueryClassLevelListWithScoreVO queryClassWithLevelScoreInfo(ClassCompareLevelListDTO param);

    QueryClassLevelListWithScoreVO queryClassGroupByLevelWithAuth(ClassLevelListWithAuthDTO param);

    Map<String, BigDecimal> queryClassIdTotalScoreMap(CheckClassInfoScoreListDTO param);

    /**
     * 导出值日记录
     * @param response
     * @param dto
     */
    void exportDutyRecord(HttpServletResponse response, DutyRecordPageDTO dto) throws IOException;
}
