package com.hailiang.service;

import com.hailiang.dto.DeleteDTO;
import com.hailiang.model.dto.*;
import com.hailiang.model.vo.CheckItemFormVO;
import com.hailiang.model.vo.CheckItemManagementVO;
import com.hailiang.model.vo.SaaSOnDutyRoleVO;
import com.hailiang.model.vo.TemplateVO;

import java.util.List;

/**
 * 运营后台检查项
 *
 * @Description: 运营后台检查项
 * @Author: TanJian
 * @Date: Created in 2024-04-18
 * @Version: 1.6.0
 */
public interface SysCheckItemService {

    /**
     * 查询检查项
     * @param dto
     * @return
     */
    List<CheckItemManagementVO> queryItem(QueryCheckItemDTO dto);

    /**
     * 查询检查项详情
     * @param dto
     * @return
     */
    CheckItemFormVO findOne(CheckItemDTO dto);

    /**
     * 创建检查项
     * @param dto
     */
    void createItem(SaveCheckItemDTO dto);

    /**
     * 编辑检查项
     * @param dto
     */
    void editItem(UpdateCheckItemDTO dto);

    /**
     * 删除检查项
     * @param dto
     */
    void deleteItem(DeleteDTO dto);

    /**
     * 复制检查项
     * @param dto
     */
    void copyItem(CopyCheckItemDTO dto);

    /**
     * 切换检查项状态
     * @param dto
     */
    void switchItem(SwitchCheckItemDTO dto);

    /**
     * 检查项排序
     * @param dtoList
     */
    void sortItem(List<SortCheckItemDTO> dtoList);

    /**
     * 查询动态表单
     * @param dto
     * @return
     */
    TemplateVO templateQuery(QueryDynamicFormDTO dto);

    /**
     * icon列表
     * @return
     */
    List<String> listIcon();

    /**
     * 查询角色
     * @return
     */
    List<SaaSOnDutyRoleVO> querySaaSOnDutyRoleList();

    /**
     * 获取id
     * @return
     */
    String getSnowflakeId();
}
