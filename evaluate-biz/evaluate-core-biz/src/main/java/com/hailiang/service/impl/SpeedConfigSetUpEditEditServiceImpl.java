package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.hailiang.constant.Constant;
import com.hailiang.convert.SpeedTargetGroupConvert;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.manager.SpeedTargetGroupManager;
import com.hailiang.manager.SpeedTargetGroupRTargetManager;
import com.hailiang.model.dto.request.speed.SpeedConfigDetailEditRequest;
import com.hailiang.model.dto.request.speed.SpeedConfigGroupSortRequest;
import com.hailiang.model.entity.SpeedTargetGroupPO;
import com.hailiang.model.entity.SpeedTargetGroupRTargetPO;
import com.hailiang.model.entity.Target;
import com.hailiang.service.SpeedConfigSetUpEditService;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.hailiang.constant.SpeedMsgTemplateConstant.SYSTEM_TEMPLATE;
import static com.hailiang.exception.SpeedExceptionEnum.NOT_EXIST_GROUP_EXCEPTION;

/**
 * 极速点评配置后台设置
 *
 * @Description: 极速点评配置后台设置
 * @Author: Jovi
 * @Date: Created in 2024-01-29
 * @Version: 1.6.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SpeedConfigSetUpEditEditServiceImpl implements SpeedConfigSetUpEditService {

    public static final String SYSTEM_TEMPLATE_CREATOR = "System_Template";

    private final SpeedTargetGroupRTargetManager speedTargetGroupRTargetManager;

    private final SpeedTargetGroupManager speedTargetGroupManager;

    private final SpeedTargetGroupConvert speedTargetGroupConvert;

    @Override
    public Boolean sortIndexGroup(List<SpeedConfigGroupSortRequest> requests) {

        List<Long> groupIds = requests.stream().map(SpeedConfigGroupSortRequest::getSpeedTargetGroupId).collect(Collectors.toList());

        Map<Long, Integer> groupMap = requests.stream()
                .collect(Collectors.toMap(SpeedConfigGroupSortRequest::getSpeedTargetGroupId, SpeedConfigGroupSortRequest::getSpeedTargetGroupSortIndex));

        List<SpeedTargetGroupPO> targetGroupPOS = speedTargetGroupManager.listByIds(groupIds);
        Assert.notEmpty(targetGroupPOS, () -> new BizException("指标分组ID不存在"));
        List<Long> oldGroupIds = targetGroupPOS.stream().map(SpeedTargetGroupPO::getId).collect(Collectors.toList());
        Assert.isTrue(new HashSet<>(groupIds).containsAll(oldGroupIds), () -> new BizException("指标分组非法"));

        for (SpeedTargetGroupPO targetGroupPO : targetGroupPOS) {
            targetGroupPO.setSortIndex(groupMap.get(targetGroupPO.getId()));
        }

        return speedTargetGroupManager.updateBatchById(targetGroupPOS);
    }

    @Override
    public Boolean deleteGroup(Long speedGroupId) {

        SpeedTargetGroupPO speedTargetGroupPO = Optional.ofNullable(speedTargetGroupManager.getById(speedGroupId))
                .orElseThrow(() -> new BizException(NOT_EXIST_GROUP_EXCEPTION));

        return speedTargetGroupManager.removeById(speedTargetGroupPO);
    }

    @Override
    public Boolean saveOrUpdateSpeedGroup(String name, Long id, Boolean isTemplate) {

        SpeedTargetGroupPO speedTargetGroupPO;

        String campusId = WebUtil.getCampusId();

        //更新
        if (id != null) {
            speedTargetGroupPO = Optional.ofNullable(speedTargetGroupManager.getById(id))
                    .orElseThrow(() -> new BizException(NOT_EXIST_GROUP_EXCEPTION));
            speedTargetGroupPO.setUpdateBy(WebUtil.getStaffId());
        }
        //新增
        else {
            speedTargetGroupPO = new SpeedTargetGroupPO();
            speedTargetGroupPO.setCreateBy(WebUtil.getStaffId());

            Long count = speedTargetGroupManager.countByCampusId(campusId);

            if (count > 0) {
                speedTargetGroupPO.setSortIndex(count.intValue() + 1);
            }

            if (isTemplate) {
                speedTargetGroupPO.setTenantId(SYSTEM_TEMPLATE);
                speedTargetGroupPO.setCampusId(SYSTEM_TEMPLATE);
                speedTargetGroupPO.setSchoolId(SYSTEM_TEMPLATE);
            } else {
                speedTargetGroupPO.setTenantId(WebUtil.getTenantId());
                speedTargetGroupPO.setCampusId(campusId);
                speedTargetGroupPO.setSchoolId(WebUtil.getSchoolId());
            }
        }

        speedTargetGroupPO.setGroupName(name);

        return speedTargetGroupManager.saveOrUpdate(speedTargetGroupPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateSpeedEvaluateConfig(SpeedConfigDetailEditRequest detailRequests) {

        Long speedGroupId = detailRequests.getSpeedGroupId();

        Optional.ofNullable(speedTargetGroupManager.getById(speedGroupId))
                .orElseThrow(() -> new BizException(NOT_EXIST_GROUP_EXCEPTION));

        List<SpeedTargetGroupRTargetPO> existsPos = speedTargetGroupRTargetManager.listByGroupIds(Lists.newArrayList(speedGroupId), null);

        List<SpeedTargetGroupRTargetPO> insertPos = new ArrayList<>();
        List<SpeedTargetGroupRTargetPO> deletePos = new ArrayList<>();
        List<SpeedTargetGroupRTargetPO> updatePos = new ArrayList<>();
        List<SpeedTargetGroupRTargetPO> upsertPos = new ArrayList<>();

        List<SpeedTargetGroupRTargetPO> newPos = speedTargetGroupConvert.editRequestToGroupRTargetPO(detailRequests.getOptionEditRequests(), speedGroupId);

        Map<String, SpeedTargetGroupRTargetPO> newPosMap = newPos.stream()
                .collect(Collectors.toMap(pos -> pos.getTargetId().toString() + pos.getOptionId(), Function.identity()));


        if (CollectionUtils.isEmpty(existsPos)) {
            insertPos.addAll(newPos);
        } else {

            Set<String> existsKeys = new HashSet<>();

            for (SpeedTargetGroupRTargetPO existsPo : existsPos) {
                String key = existsPo.getTargetId().toString() + existsPo.getOptionId();
                if (newPosMap.keySet().contains(key)) {

                    SpeedTargetGroupRTargetPO speedTargetGroupRTargetPO = newPosMap.get(key);

                    BeanUtil.copyProperties(speedTargetGroupRTargetPO,
                            existsPo,
                            CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("createBy", "createTime", "id"));

                    existsPo.setUpdateBy(existsPo.getCreateBy());
                    existsPo.setUpdateTime(new Date());
                    updatePos.add(existsPo);
                } else {
                    deletePos.add(existsPo);
                }
                existsKeys.add(key);
            }

            for (Map.Entry<String, SpeedTargetGroupRTargetPO> newPosEntry : newPosMap.entrySet()) {
                if (!existsKeys.contains(newPosEntry.getKey())) {
                    insertPos.add(newPosEntry.getValue());
                }
            }
        }

        upsertPos.addAll(insertPos);
        upsertPos.addAll(updatePos);
        if (!CollectionUtils.isEmpty(upsertPos)) {
            speedTargetGroupRTargetManager.saveOrUpdateBatch(upsertPos);
        }
        if (!CollectionUtils.isEmpty(deletePos)) {
            speedTargetGroupRTargetManager.removeBatchByIds(deletePos);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkAndCopySpeedTarget(String tenantId, String schoolId, String campusId, List<Target> targetList) {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        Assert.isFalse(StrUtil.isBlank(tenantId)
                || StrUtil.isBlank(schoolId)
                || StrUtil.isBlank(campusId), () -> new BizException(BizExceptionEnum.PARAMETER_ERROR.getMessage()));

        Long count = speedTargetGroupManager.countByCampusId(campusId);
        if (Convert.toInt(count) > Constant.ZERO) {
            log.warn("「该校区已初始过极速点评指标模板，不再二次进行初始化」");
            return;
        }

        log.info("「极速点评指标模板复制」-开始复制");
        copySpeedTarget(tenantId, schoolId, campusId, targetList);
        stopWatch.stop();
        log.info("「极速点评指标模板复制完成」--消耗时长：[{}]", stopWatch.getLastTaskTimeMillis());
    }

    private void copySpeedTarget(String tenantId, String schoolId, String campusId, List<Target> targets) {

        //运营后台极速点评分组模板
        List<SpeedTargetGroupPO> templateSpeedTargetGroupPOS = speedTargetGroupManager.listByCampusId(SYSTEM_TEMPLATE);
        if (CollUtil.isEmpty(templateSpeedTargetGroupPOS)) {
            log.warn("「极速指标模板复制」-没有模板，无需复制");
            return;
        }

        List<Long> speedTargetGroupIds = templateSpeedTargetGroupPOS.stream().map(SpeedTargetGroupPO::getId).collect(Collectors.toList());

        //运营平台对应极速分组下的所有已选的极速点评指标的信息
        List<SpeedTargetGroupRTargetPO> speedTargetGroupRTargetTemplatePOS = speedTargetGroupRTargetManager.listByGroupIds(speedTargetGroupIds, null);

        //以源指标ID为KEY，该校区下已复制的指标
        Map<Long, Target> initTargetMap = targets.stream()
                .collect(Collectors.toMap(Target::getSourceTargetId, Function.identity()));

        List<SpeedTargetGroupRTargetPO> newGroupRTargetPos = new ArrayList<>();
        List<SpeedTargetGroupPO> newSpeedGroup = new ArrayList<>();

        for (SpeedTargetGroupPO oldTemplateSpeedTargetGroup : templateSpeedTargetGroupPOS) {

            SpeedTargetGroupPO newSpeedTargetGroup = new SpeedTargetGroupPO();

            //运营平台极速点评分组ID
            Long oldTemplateSpeedTargetGroupId = oldTemplateSpeedTargetGroup.getId();

            //新校区极速点评分组ID
            long newGroupId = SnowFlakeIdUtil.nextId();

            newSpeedTargetGroup.setTenantId(tenantId);
            newSpeedTargetGroup.setSchoolId(schoolId);
            newSpeedTargetGroup.setCampusId(campusId);
            newSpeedTargetGroup.setGroupName(oldTemplateSpeedTargetGroup.getGroupName());
            newSpeedTargetGroup.setId(newGroupId);
            newSpeedTargetGroup.setCreateBy(SYSTEM_TEMPLATE_CREATOR);

            if (CollectionUtils.isEmpty(speedTargetGroupRTargetTemplatePOS)) {
                continue;
            }

            for (SpeedTargetGroupRTargetPO speedTargetGroupRTargetTemplatePO : speedTargetGroupRTargetTemplatePOS) {

                //如果该分组ID和模板ID不相等说明不是该模板分组下的指标
                if (!oldTemplateSpeedTargetGroupId.equals(speedTargetGroupRTargetTemplatePO.getSpeedTargetGroupId())) {
                    continue;
                }

                Long oldTargetId = speedTargetGroupRTargetTemplatePO.getTargetId();
                Target target = initTargetMap.get(oldTargetId);
                if (target == null) {
                    continue;
                }

                SpeedTargetGroupRTargetPO newSpeedTargetPo = new SpeedTargetGroupRTargetPO();

                newSpeedTargetPo.setSpeedTargetGroupId(newGroupId);
                //把源targetID替换为初始化后的TargetId
                Long newTargetId = target.getId();
                newSpeedTargetPo.setTargetId(newTargetId);

                String templateOptionId = speedTargetGroupRTargetTemplatePO.getOptionId();
                String newOptionId = templateOptionId.replaceFirst("\\d+_", newTargetId + "_");

                newSpeedTargetPo.setOptionId(newOptionId);
                newSpeedTargetPo.setInfoType(speedTargetGroupRTargetTemplatePO.getInfoType());
                newSpeedTargetPo.setAppType(speedTargetGroupRTargetTemplatePO.getAppType());
                newSpeedTargetPo.setSortIndex(speedTargetGroupRTargetTemplatePO.getSortIndex());
                newSpeedTargetPo.setCreateBy(SYSTEM_TEMPLATE_CREATOR);

                newGroupRTargetPos.add(newSpeedTargetPo);
            }

            newSpeedGroup.add(newSpeedTargetGroup);
        }


        speedTargetGroupManager.saveBatch(newSpeedGroup);

        speedTargetGroupRTargetManager.saveBatch(newGroupRTargetPos);
    }
}
