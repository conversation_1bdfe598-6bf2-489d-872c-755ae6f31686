package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.hailiang.convert.CheckDimConvert;
import com.hailiang.convert.CheckGroupConvert;
import com.hailiang.dto.DeleteDTO;
import com.hailiang.dto.SortDTO;
import com.hailiang.enums.SourceTypeEnum;
import com.hailiang.manager.CheckDimManager;
import com.hailiang.manager.CheckGroupManager;
import com.hailiang.model.dto.CheckGroupDTO;
import com.hailiang.model.dto.SaveCheckGroupDTO;
import com.hailiang.model.dto.UpdateCheckGroupDTO;
import com.hailiang.model.entity.CheckDim;
import com.hailiang.model.entity.CheckGroupPO;
import com.hailiang.model.vo.CheckDimVO;
import com.hailiang.model.vo.CheckGroupVO;
import com.hailiang.service.CheckDimService;
import com.hailiang.service.CheckGroupService;
import com.hailiang.service.CheckItemService;
import com.hailiang.service.SysCheckGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 运营后台检查项分组
 *
 * @Description: 运营后台检查项分组
 * @Author: TanJian
 * @Date: Created in 2024-04-18
 * @Version: 1.6.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysCheckGroupServiceImpl implements SysCheckGroupService {

    private static final String MINUS_ONE = "-1";

    private final CheckDimService checkDimService;
    private final CheckItemService checkItemService;
    private final CheckGroupService checkGroupService;
    private final CheckDimManager checkDimManager;
    private final CheckDimConvert checkDimConvert;
    private final CheckGroupConvert checkGroupConvert;
    private final CheckGroupManager checkGroupManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CheckDimVO> queryDim() {
        List<CheckDim> dimList = checkDimManager.lambdaQuery().eq(CheckDim::getCampusId, MINUS_ONE)
                .eq(CheckDim::getTenantId, MINUS_ONE)
                .eq(CheckDim::getSchoolId, MINUS_ONE)
                .eq(CheckDim::getSourceType, SourceTypeEnum.LDHQ.getCode())
                .list();
        if (CollUtil.isNotEmpty(dimList)) {
            return checkDimConvert.toCheckDimVOS(dimList);
        }
        return checkDimConvert.toCheckDimVOS(checkDimService.createDefaultTemplates(MINUS_ONE, MINUS_ONE, MINUS_ONE));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createGroup(SaveCheckGroupDTO dto) {
        CheckGroupPO group = checkGroupConvert.toCheckGroup(dto);
        group.setSourceType(SourceTypeEnum.LDHQ.getCode());
        group.setTenantId(MINUS_ONE);
        group.setSchoolId(MINUS_ONE);
        group.setCampusId(MINUS_ONE);
        group.setSortIndex(System.currentTimeMillis());
        group.setDeleted(Boolean.FALSE);
        checkGroupManager.save(group);
    }

    @Override
    public void editGroup(UpdateCheckGroupDTO dto) {
        Long id = Convert.toLong((dto.getId()));
        checkGroupService.verifyGroupExists(id);
        checkGroupService.editGroup(dto);
    }

    @Override
    public void deleteGroupIfItemDoesNotExist(DeleteDTO dto) {
        Long id = Convert.toLong(dto.getId());
        checkGroupService.verifyGroupExists(id);
        checkItemService.throwAnExceptionIfItExists(Convert.toStr(id));
        checkGroupManager.removeById(id);
    }

    @Override
    public void sortGroup(SortDTO dto) {
        List<String> ids = dto.getIds();
        List<CheckGroupPO> checkGroupPOS = checkGroupService.fillSortIndex(ids);
        checkGroupManager.updateBatchById(checkGroupPOS);
    }

    @Override
    public List<CheckGroupVO> queryGroupsByDim(CheckGroupDTO dto) {
        List<CheckGroupPO> groups = checkGroupManager.lambdaQuery().eq(CheckGroupPO::getDimId, dto.getDimId()).list();
        return checkGroupConvert.toCheckGroupVOS(groups);
    }
}
