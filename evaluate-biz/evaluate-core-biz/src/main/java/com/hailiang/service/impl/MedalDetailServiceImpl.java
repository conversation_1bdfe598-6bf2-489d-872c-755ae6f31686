package com.hailiang.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.constant.Constant;
import com.hailiang.convert.MedalDetailConvert;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.SaasClassTypeEnum;
import com.hailiang.enums.medal.*;
import com.hailiang.exception.BaseErrorCode;
import com.hailiang.exception.BizException;
import com.hailiang.manager.*;
import com.hailiang.mapper.MedalActivityGradeMapper;
import com.hailiang.mapper.MedalRuleMatchRecordMapper;
import com.hailiang.mapper.MedalUserOperationRecordMapper;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.datastatistics.dto.MedalOperationRecordDTO;
import com.hailiang.model.dto.activity.detail.*;
import com.hailiang.model.dto.activity.rule.save.OuterLayerRuleDTO;
import com.hailiang.model.dto.activity.rule.save.TargetLevelDTO;
import com.hailiang.model.entity.*;
import com.hailiang.model.medal.vo.MedalInfoVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.CacheSaasManager;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.dto.student.StudentByIdQuery;
import com.hailiang.remote.saas.vo.educational.EduAuthVO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.staff.StaffBatchQueryDTO;
import com.hailiang.remote.saas.vo.staff.StaffBatchVO;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.vo.StudentInfo1VO;
import com.hailiang.service.ActivityRuleService;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.MedalDetailService;
import com.hailiang.service.MedalUserAcquireRecordService;
import com.hailiang.util.EasyPoiUtil;
import com.hailiang.util.WebUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/21 13:51
 */
@Slf4j
@Service
public class MedalDetailServiceImpl implements MedalDetailService {

    @Value("${oss.urlPrefix}")
    private String urlPrefix;

    @Resource
    private MedalDetailConvert medalDetailConvert;

    @Resource
    private MedalUserAcquireRecordManager medalUserAcquireRecordManager;

    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private MedalActivityGradeMapper medalActivityGradeMapper;

    @Resource
    private MedalTaskCompletionManager medalTaskCompletionManager;

    @Resource
    private MedalActivityManager medalActivityManager;

    @Resource
    private CacheSaasManager cacheSaasManager;

    @Resource
    private SaasStudentManager saasStudentManager;

    @Resource
    private MedalActivityGradeManager medalActivityGradeManager;

    @Resource
    private BasicInfoService basicInfoService;

    @Resource
    private MedalTaskManager medalTaskManager;

    @Resource
    private MedalInfoManager medalInfoManager;

    @Resource
    private MedalUserOperationRecordMapper medalUserOperationRecordMapper;

    @Resource
    private MedalTaskRuleManager medalTaskRuleManager;

    @Resource
    private MedalTaskRuleTargetManager medalTaskRuleTargetManager;

    @Resource
    private MedalRuleMatchRecordMapper medalRuleMatchRecordMapper;

    @Resource
    private ActivityRuleService activityRuleService;

    @Autowired
    private MedalUserAcquireRecordService medalUserAcquireRecordService;


    /**
     * 颁章明细查询
     *
     * @param query 查询条件
     * @return
     */
    @Override
    public Page<MedalDetailVO> listMedalDetail(MedalDetailPage query) {
/*        List<EduOrgTreeVO> eduOrgTreeVOS = medalUserAcquireRecordService.listClassByCampusId();
        if (CollUtil.isEmpty(eduOrgTreeVOS)) {
            log.info("获取颁章明细-班级列表为空-直接返回");
            return new Page<>();
        }
        List<Long> classIds = eduOrgTreeVOS.stream().map(EduOrgTreeVO::getId).distinct().collect(Collectors.toList());*/
        List<String> classIds = new ArrayList<>();
        EduAuthVO currentStaffAuth = basicInfoService.getCurrentStaffAuth(WebUtil.getStaffId(), query.getSchoolYear());
        if (!currentStaffAuth.getIsAdmin()) {
            classIds = currentStaffAuth.getClassIdStrs();
            if (CollUtil.isEmpty(classIds)) {
                log.info("获取颁章明细-用户班级权限为空-直接返回");
                return new Page<>();
            }
        }
        log.info("获取颁章明细-classIds信息:{}", JSONUtil.toJsonStr(classIds));

        //全部年级、班级，传参为-1，查全部直接置为null
        query.setGradeId(StringUtils.isNotBlank(query.getGradeId()) && "-1".equals(query.getGradeId()) ? null : query.getGradeId());
        query.setClassId(StringUtils.isNotBlank(query.getClassId()) && "-1".equals(query.getClassId()) ? null : query.getClassId());

        Page<MedalUserAcquireRecord> recordPage = medalUserAcquireRecordManager.page(new Page<>(query.getPageNum(), query.getPageSize()), new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .eq(MedalUserAcquireRecord::getCampusId, WebUtil.getCampusId())
                .like(StrUtil.isNotBlank(query.getStudentName()), MedalUserAcquireRecord::getStudentName, query.getStudentName())
                .in(CollUtil.isNotEmpty(classIds), MedalUserAcquireRecord::getClassId, classIds)
                .eq(StrUtil.isNotBlank(query.getClassId()), MedalUserAcquireRecord::getClassId, query.getClassId())//班级id
                .eq(StringUtils.isNotEmpty(query.getGradeId()), MedalUserAcquireRecord::getGradeId, query.getGradeId())//年级id
                .eq(StringUtils.isNotEmpty(query.getCampusSectionId()), MedalUserAcquireRecord::getCampusSectionId, query.getCampusSectionId())//学段id
                .eq(Objects.nonNull(query.getMedalInfoId()), MedalUserAcquireRecord::getMedalInfoId, query.getMedalInfoId())
                .eq(Objects.nonNull(query.getActivityId()), MedalUserAcquireRecord::getMedalActivityId, query.getActivityId())
                .eq(Objects.nonNull(query.getAwardType()), MedalUserAcquireRecord::getAwardType, query.getAwardType())
                .ge(Objects.nonNull(query.getStartTime()), MedalUserAcquireRecord::getAwardDate,
                        query.getStartTime())
                .le(Objects.nonNull(query.getEndTime()), MedalUserAcquireRecord::getAwardDate,
                        Objects.nonNull(query.getEndTime()) ? DateUtil.endOfDay(query.getEndTime()) : null)
                .orderByDesc(MedalUserAcquireRecord::getId));
        long total = recordPage.getTotal();
        log.info("获取颁章明细-总条数:{}", total);

        List<MedalDetailVO> medalDetails = capsulationInfo(recordPage.getRecords());
        Page<MedalDetailVO> resultPage = new Page<>(query.getPageNum(), query.getPageSize());
        resultPage.setTotal(recordPage.getTotal());
        resultPage.setRecords(medalDetails);

        return resultPage;
    }

    /**
     * 封装颁章明细信息
     *
     * @param records 明细记录
     * @return
     */
    private List<MedalDetailVO> capsulationInfo(List<MedalUserAcquireRecord> records) {
        // 奖章名称
        Map<Long, String> medalInfoNameMap = new HashMap<>();
        // 活动名称
        Map<Long, String> medalActivityNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(records)) {
            List<Long> medalInfoIds = records.stream().map(MedalUserAcquireRecord::getMedalInfoId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(medalInfoIds)) {
                List<MedalInfoVO> medalInfos = medalInfoManager.listInfoByIds(medalInfoIds);
                medalInfoNameMap = medalInfos.stream().collect(Collectors.toMap(MedalInfoVO::getId, MedalInfoVO::getName));
            }

            List<Long> medalActivityIds = records.stream().map(MedalUserAcquireRecord::getMedalActivityId).distinct().collect(Collectors.toList());
            List<MedalActivityInfoVO> medalActivityInfos = medalActivityManager.listMedalActivityInfo(medalActivityIds);
            medalActivityNameMap = medalActivityInfos.stream().collect(Collectors.toMap(MedalActivityInfoVO::getActivityId, MedalActivityInfoVO::getActivityName));
        }

        List<MedalDetailVO> medalDetails = medalDetailConvert.toMedalDetailVOList(records);
        Map<Long, String> finalMedalInfoNameMap = medalInfoNameMap;
        Map<Long, String> finalMedalActivityNameMap = medalActivityNameMap;
        medalDetails.forEach(s -> {
            if (MedalAwardStatusEnum.ARTIFICIAL_WITHDRAW.getCode().equals(s.getAwardStatus())) {
                s.setAwardStatus(MedalAwardStatusEnum.AUTO_WITHDRAW.getCode());
            }
            if (StrUtil.isNotBlank(finalMedalInfoNameMap.get(s.getMedalInfoId()))) {
                s.setMedalInfoName(finalMedalInfoNameMap.get(s.getMedalInfoId()));
            }
            if (StrUtil.isNotBlank(finalMedalActivityNameMap.get(s.getMedalActivityId()))) {
                s.setMedalActivityName(finalMedalActivityNameMap.get(s.getMedalActivityId()));
            }
            if (Objects.nonNull(s.getAwardDate())) {
                s.setAwardDateStr(DateUtil.format(s.getAwardDate(), DatePattern.NORM_DATE_PATTERN));
            }
            if (Objects.nonNull(s.getAwardType())) {
                //颁发类型  1：自动颁发  2：手动颁发
                if (1 == s.getAwardType()){
                    s.setAwardTypeName("自动颁发");
                }else if (2 == s.getAwardType()){
                    s.setAwardTypeName("手动颁发");
                }
            }
            if (Objects.nonNull(s.getAwardStatus())) {
                //颁发状态  1：待颁发  2：已撤回  3：已完成
                if (1 == s.getAwardStatus()){
                    s.setAwardStatusName("待颁发");
                }else if (2 == s.getAwardStatus()){
                    s.setAwardStatusName("已撤回");
                }else if (3 == s.getAwardStatus()){
                    s.setAwardStatusName("已完成");
                }
            }
        });
        return medalDetails;
    }

    @Override
    @SneakyThrows
    public void exporterMedalDetail(MedalDetailQuery query, HttpServletResponse response) {
        List<String> classIds = new ArrayList<>();
        EduAuthVO currentStaffAuth = basicInfoService.getCurrentStaffAuth(WebUtil.getStaffId(), query.getSchoolYear());
        if (!currentStaffAuth.getIsAdmin()) {
            classIds = currentStaffAuth.getClassIdStrs();
            if (CollUtil.isEmpty(classIds)) {
                log.info("获取颁章明细-用户班级权限为空-直接返回");
                throw new BizException(BaseErrorCode.NO_DATA_AUTH);
            }
        }
        log.info("获取颁章明细-classIds信息:{}", JSONUtil.toJsonStr(classIds));
        //全部年级、班级，传参为-1，查全部直接置为null
        query.setGradeId(StringUtils.isNotBlank(query.getGradeId()) && "-1".equals(query.getGradeId()) ? null : query.getGradeId());
        query.setClassId(StringUtils.isNotBlank(query.getClassId()) && "-1".equals(query.getClassId()) ? null : query.getClassId());


        List<MedalUserAcquireRecord> records = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .eq(MedalUserAcquireRecord::getCampusId, WebUtil.getCampusId())
                .like(StrUtil.isNotBlank(query.getStudentName()), MedalUserAcquireRecord::getStudentName, query.getStudentName())
                .in(CollUtil.isNotEmpty(classIds), MedalUserAcquireRecord::getClassId, classIds)
                .eq(StrUtil.isNotBlank(query.getClassId()), MedalUserAcquireRecord::getClassId, query.getClassId())//班级id
                .eq(StringUtils.isNotEmpty(query.getGradeId()), MedalUserAcquireRecord::getGradeId, query.getGradeId())//年级id
                .eq(StringUtils.isNotEmpty(query.getCampusSectionId()), MedalUserAcquireRecord::getCampusSectionId, query.getCampusSectionId())//学段id
                .eq(Objects.nonNull(query.getMedalInfoId()), MedalUserAcquireRecord::getMedalInfoId, query.getMedalInfoId())
                .eq(Objects.nonNull(query.getActivityId()), MedalUserAcquireRecord::getMedalActivityId, query.getActivityId())
                .eq(Objects.nonNull(query.getAwardType()), MedalUserAcquireRecord::getAwardType, query.getAwardType())
                .ge(Objects.nonNull(query.getStartTime()), MedalUserAcquireRecord::getAwardDate,
                        query.getStartTime())
                .le(Objects.nonNull(query.getEndTime()), MedalUserAcquireRecord::getAwardDate,
                        Objects.nonNull(query.getEndTime()) ? DateUtil.endOfDay(query.getEndTime()) : null)
                .orderByDesc(MedalUserAcquireRecord::getId));
        List<MedalDetailVO> medalDetails = capsulationInfo(records);

        List<ExcelExportEntity> entitys = new ArrayList<>();
        entitys.add(new ExcelExportEntity("学生姓名", "studentName", 18));
        entitys.add(new ExcelExportEntity("所在班级", "className", 18));
        entitys.add(new ExcelExportEntity("奖章", "medalInfoName", 18));
        entitys.add(new ExcelExportEntity("争章活动", "medalActivityName", 18));
        entitys.add(new ExcelExportEntity("颁发日期", "awardDateStr", 18));
        entitys.add(new ExcelExportEntity("颁发类型", "awardTypeName", 18));
        entitys.add(new ExcelExportEntity("颁发状态", "awardStatusName", 18));
        entitys.add(new ExcelExportEntity("颁发人员", "awardUserName", 18));
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), entitys, medalDetails);
        EasyPoiUtil.adjustStyle(workbook.getSheetAt(0));
        EasyPoiUtil.downLoadExcelWithFormat("奖章明细数据" + DateUtil.format(new Date(), "yyyy-MM-dd"), response, workbook);
    }


    /**
     * 查询颁章明细操作记录
     *
     * @param dto 查询参数
     * @return
     */
    @Override
    public Page<MedalOperationRecordVO> listOperationRecord(MedalBusinessIdDTO dto) {
        Assert.notNull(dto.getId(), () -> new BizException("颁章记录id不能为空"));
        MedalUserAcquireRecord medalUserAcquireRecord = medalUserAcquireRecordManager.getById(dto.getId());
        Assert.notNull(medalUserAcquireRecord, () -> new BizException("颁章记录不存在"));

        // 获取颁章明细下的操作记录
        Page<MedalOperationRecordDTO> result = medalUserOperationRecordMapper.listMedalOperationRecord(new Page(dto.getPageNumber(), dto.getPageSize()), medalUserAcquireRecord.getMedalTaskId(), medalUserAcquireRecord.getStudentId());
        List<Long> taskRuleIds = result.getRecords().stream().map(MedalOperationRecordDTO::getMedalTaskRuleId).distinct().collect(Collectors.toList());

        Map<Long, Integer> ruleTypeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(taskRuleIds)) {
            List<MedalTaskRule> medalTaskRules = medalTaskRuleManager.listByIds(taskRuleIds);
            ruleTypeMap = medalTaskRules.stream().collect(Collectors.toMap(MedalTaskRule::getId, MedalTaskRule::getType));
        }

        Map<Long, Integer> finalRuleTypeMap = ruleTypeMap;
        List<MedalOperationRecordVO> collect = result.getRecords().stream().map(s -> {
            MedalOperationRecordVO medalOperationRecordVO = new MedalOperationRecordVO();
            medalOperationRecordVO.setDataSource(s.getDataSource());
            medalOperationRecordVO.setTargetName(s.getTargetName());
            medalOperationRecordVO.setOptionName(s.getInfoName());
            medalOperationRecordVO.setValue(s.getScore());
            medalOperationRecordVO.setBehaviourRecordId(s.getId());
            medalOperationRecordVO.setOperationId(s.getOperationId());
            medalOperationRecordVO.setOperationTime(s.getCreateTime());
            medalOperationRecordVO.setUserName(s.getAppraisalName());
            medalOperationRecordVO.setType(s.getType());
            if (Objects.nonNull(finalRuleTypeMap.get(s.getMedalTaskRuleId()))) {
                medalOperationRecordVO.setRuleType(finalRuleTypeMap.get(s.getMedalTaskRuleId()));
            }
            // 如果是点评次数,值为1次
            if (MedalRuleTypeEnum.COMMENT_NUM.getCode().equals(medalOperationRecordVO.getRuleType())) {
                medalOperationRecordVO.setValue(Convert.toBigDecimal(1));
            }
            // 如果是删除的操作
            if (s.getType().equals(2)) {
                // 分值取反
                if (Objects.nonNull(medalOperationRecordVO.getValue())) {
                    medalOperationRecordVO.setValue(medalOperationRecordVO.getValue().negate());
                }

                // 时间取值
                if (DataSourceEnum.SPORT.getCode().equals(s.getDataSource())) {
                    // 体测数据
                    if (Objects.nonNull(s.getBehaviorUpdateTime())) {
                        medalOperationRecordVO.setOperationTime(s.getBehaviorUpdateTime());
                    }
                } else {
                    // 非体测数据
                    // 如果是删除的操作
                    medalOperationRecordVO.setOperationTime(s.getBehaviorUpdateTime());
                }
            }

            return medalOperationRecordVO;
        }).collect(Collectors.toList());
        Page page = new Page(dto.getPageNumber(), dto.getPageSize());
        page.setTotal(result.getTotal());
        page.setRecords(collect);
        return page;
    }

    /**
     * 手动颁发奖章
     *
     * @param dto 请求参数
     * @return
     */
    @Override
    public Boolean artificialIssueMedal(IssueMedalDTO dto) {
        Assert.notEmpty(dto.getIssueMedalDetailDTOS(), "请选择学生");
        // 因为前端可能传过来整个年级id、班级id、或者学生id，所以需要全部转化成学生id并去重
        List<Long> studentIds = this.listStudentIds(dto.getIssueMedalDetailDTOS());
        log.info("手动颁发奖章,查询去重后学生数量:{}", studentIds.size());
        StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
        studentByIdQuery.setStudentIds(studentIds);
        List<UcStudentClassBffVO> ucStudentClassBffVOS = basicInfoRemote.listByStudentIds(studentByIdQuery, SaasClassTypeEnum.XINGZHENG.getCode());
        Assert.notEmpty(ucStudentClassBffVOS, () -> new BizException("学生不存在"));

        // ucStudentClassBffVOS根据毕业状态过滤 只保留upgradeStatus为0的数据 并返回一个map key为studentId value为对象数组
        Map<Long, UcStudentClassBffVO> studentClassBffVOMap = CollStreamUtil.toIdentityMap(ucStudentClassBffVOS.stream().filter(s -> s.getUpgradeStatus().equals("0")).collect(Collectors.toList()), UcStudentClassBffVO::getStudentId);

        StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
        staffBatchQueryDTO.setStaffIdList(Arrays.asList(Convert.toLong(WebUtil.getStaffId())));
        List<StaffBatchVO> staffBatchVOS = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
        Assert.notEmpty(staffBatchVOS, () -> new BizException("颁发人不存在"));
        StaffBatchVO staffBatchVO = CollUtil.getFirst(staffBatchVOS);

        List<MedalUserAcquireRecord> medalUserAcquireRecords = new ArrayList<>(256);
        for (Map.Entry<Long, UcStudentClassBffVO> entry : studentClassBffVOMap.entrySet()){
            UcStudentClassBffVO studentClassBffVO = entry.getValue();
            MedalUserAcquireRecord medalUserAcquireRecord = new MedalUserAcquireRecord();
            medalUserAcquireRecord.setTenantId(WebUtil.getTenantId());
            medalUserAcquireRecord.setSchoolId(WebUtil.getSchoolId());
            medalUserAcquireRecord.setCampusId(WebUtil.getCampusId());
            medalUserAcquireRecord.setStudentId(Convert.toStr(studentClassBffVO.getStudentId()));
            medalUserAcquireRecord.setStudentName(studentClassBffVO.getStudentName());
            medalUserAcquireRecord.setCampusSectionId(Convert.toStr(studentClassBffVO.getCampusSectionId()));
            medalUserAcquireRecord.setCampusSectionCode(studentClassBffVO.getSectionCode());
            medalUserAcquireRecord.setGradeId(Convert.toStr(studentClassBffVO.getGradeId()));
            medalUserAcquireRecord.setGradeCode(studentClassBffVO.getGradeCode());
            medalUserAcquireRecord.setClassId(Convert.toStr(studentClassBffVO.getClassId()));
            medalUserAcquireRecord.setClassName(studentClassBffVO.getClassName());
            medalUserAcquireRecord.setMedalActivityId(dto.getActivityId());
            medalUserAcquireRecord.setMedalInfoId(dto.getMedalInfoId());
            medalUserAcquireRecord.setAwardStatus(MedalAwardStatusEnum.IS_ISSUE.getCode());
            medalUserAcquireRecord.setAwardType(MedalAwardTypeEnum.MANUAL.getCode());
            medalUserAcquireRecord.setAwardUserId(WebUtil.getStaffId());
            medalUserAcquireRecord.setAwardUserName(staffBatchVO.getName());
            medalUserAcquireRecord.setAwardDate(DateUtil.offsetHour(DateUtil.beginOfDay(DateUtil.tomorrow()), 8));
            medalUserAcquireRecord.setAwardContent(dto.getContent());

            medalUserAcquireRecords.add(medalUserAcquireRecord);
        }

        medalUserAcquireRecordManager.saveBatch(medalUserAcquireRecords);

        return Boolean.TRUE;
    }

    private List<Long> listStudentIds(List<IssueMedalDetailDTO> issueMedalStudentDTOS) {
        List<Long> studentIds = new ArrayList<>(256);
        for (IssueMedalDetailDTO d : issueMedalStudentDTOS){
            Integer idType = d.getIdType();
            // 学生类型
            if (idType.equals(0)){
                studentIds.add(d.getId());
            }
            // 年级类型
            if (idType.equals(4)){
                List<StudentInfo1VO> studentInfo1VOS = saasStudentManager.queryStudentPageByGradeId(WebUtil.getSchoolIdLong(), d.getId());
                studentIds.addAll(CollStreamUtil.toList(studentInfo1VOS, StudentInfo1VO::getId));
            }
            // 班级类型
            if (idType.equals(5)){
                List<StudentInfo1VO> studentInfo1VOS = saasStudentManager.queryStudentPageByClassId(WebUtil.getSchoolIdLong(), d.getId());
                studentIds.addAll(CollStreamUtil.toList(studentInfo1VOS, StudentInfo1VO::getId));
            }
        }

        return CollUtil.distinct(studentIds);
    }

    /**
     * 手动撤回奖章
     *
     * @param dto 请求参数
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean artificialWithdrawMedal(MedalBusinessIdDTO dto) {
        Assert.notNull(dto.getId(), () -> new BizException("颁章记录id不能为空"));
        MedalUserAcquireRecord medalUserAcquireRecord = medalUserAcquireRecordManager.getById(dto.getId());
        Assert.notNull(medalUserAcquireRecord, () -> new BizException("颁章记录不存在"));

        // 如果是手动颁章的手动撤回
        if (MedalAwardTypeEnum.MANUAL.getCode().equals(medalUserAcquireRecord.getAwardType())) {
            medalUserAcquireRecord.setAwardStatus(MedalAwardStatusEnum.ARTIFICIAL_WITHDRAW.getCode());
            medalUserAcquireRecordManager.updateById(medalUserAcquireRecord);
            return Boolean.TRUE;
        }
        // 如果是自动颁章的手动撤回
        if (MedalAwardTypeEnum.AUTO.getCode().equals(medalUserAcquireRecord.getAwardType())) {
            // 1.修改颁章明细状态
            medalUserAcquireRecord.setAwardStatus(MedalAwardStatusEnum.ARTIFICIAL_WITHDRAW.getCode());
            medalUserAcquireRecordManager.updateById(medalUserAcquireRecord);

            // 2.修改二级任务状态为已撤回
            if (Objects.isNull(medalUserAcquireRecord.getCompletionId())) {
                return Boolean.TRUE;
            }
            MedalTaskCompletion medalTaskCompletion = medalTaskCompletionManager.getById(medalUserAcquireRecord.getCompletionId());
            if (Objects.isNull(medalTaskCompletion)) {
                return Boolean.TRUE;
            }
            medalTaskCompletion.setStatus(MedalTaskCompleteStatusEnum.ARTIFICIAL_WITHDRAW.getCode());
            medalTaskCompletionManager.updateById(medalTaskCompletion);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<MedalActivityInfoDTO> listActivityMedalInfo(MedalActivityIdDTO dto) {
        Assert.notNull(dto.getActivityId(), () -> new BizException("活动id不能为空"));
        List<MedalTask> medalTasks = medalTaskManager.list(new LambdaQueryWrapper<MedalTask>()
                .eq(MedalTask::getMedalActivityId, dto.getActivityId()));
        if (CollUtil.isEmpty(medalTasks)) {
            return Collections.emptyList();
        }
        // 奖章id
        List<Long> medalInfoIds = medalTasks.stream().map(MedalTask::getMedalInfoId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(medalInfoIds)) {
            return Collections.emptyList();
        }
        List<MedalInfoVO> medalInfos = medalInfoManager.listInfoByIds(medalInfoIds);
        Map<Long, MedalInfoVO> medalInfoMap = medalInfos.stream().collect(Collectors.toMap(MedalInfoVO::getId, Function.identity()));

        List<MedalActivityInfoDTO> list = new ArrayList<>();
        for (Long medalInfoId : medalInfoIds) {
            MedalActivityInfoDTO medalActivityInfoDTO = new MedalActivityInfoDTO();
            medalActivityInfoDTO.setMedalInfoId(Convert.toStr(medalInfoId));
            if (Objects.nonNull(medalInfoMap.get(medalInfoId))) {
                MedalInfoVO medalInfo = medalInfoMap.get(medalInfoId);
                medalActivityInfoDTO.setMedalName(medalInfo.getName());
                medalActivityInfoDTO.setMedalIcon(medalInfo.getLogoUrl());
                if (!medalInfo.getDeleted()) {
                    list.add(medalActivityInfoDTO);
                }
            }
        }

        return list;
    }

    /**
     * 获取活动列表
     *
     * @param dto 请求参数
     * @return
     */
    @Override
    public List<MedalActivityVO> listActivity(MedalActivityDTO dto) {
        List<MedalActivity> medalActivities = medalActivityManager.list(new LambdaQueryWrapper<MedalActivity>()
                .in(CollUtil.isNotEmpty(dto.getStatus()), MedalActivity::getStatus, dto.getStatus())
                .eq(MedalActivity::getTenantId, WebUtil.getTenantId())
                .eq(MedalActivity::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalActivity::getCampusId, WebUtil.getCampusId())
                .orderByDesc(MedalActivity::getCreateTime));
        if (CollUtil.isEmpty(medalActivities)) {
            return Collections.emptyList();
        }
        List<MedalActivityVO> collect = medalActivities.stream().map(s -> {
            MedalActivityVO medalActivityVO = new MedalActivityVO();
            medalActivityVO.setMedalActivityId(Convert.toStr(s.getId()));
            medalActivityVO.setMedalActivityName(s.getName());
            return medalActivityVO;
        }).collect(Collectors.toList());

        return collect;
    }

    /**
     * 平铺转换为树形结构
     */
    private List<EduOrgTreeVO> toTree(List<EduOrgTreeVO> resultList) {
        HashMap<Long, List<EduOrgTreeVO>> parentIdVOMap = new HashMap();
        for (EduOrgTreeVO orgTreeVO : resultList) {
            if (Objects.nonNull(parentIdVOMap.get(orgTreeVO.getParentId()))) {
                List<EduOrgTreeVO> eduOrgTreeVOList = parentIdVOMap.get(orgTreeVO.getParentId());
                eduOrgTreeVOList.add(orgTreeVO);
                parentIdVOMap.put(orgTreeVO.getParentId(), eduOrgTreeVOList);
            } else {
                List<EduOrgTreeVO> eduOrgTreeVOList = new ArrayList<>();
                eduOrgTreeVOList.add(orgTreeVO);
                parentIdVOMap.put(orgTreeVO.getParentId(), eduOrgTreeVOList);
            }
        }
        List<EduOrgTreeVO> collect = resultList.stream().filter(s -> s.getType().equals(4)).collect(Collectors.toList());
        setChildren(collect, parentIdVOMap);
        return collect;
    }

    /**
     * 递归设置子树
     */
    private void setChildren(List<EduOrgTreeVO> resultList, HashMap<Long, List<EduOrgTreeVO>> parentIdVOMap) {
        for (EduOrgTreeVO eduOrgTreeVO : resultList) {
            if (Objects.nonNull(parentIdVOMap.get(eduOrgTreeVO.getId()))) {
                eduOrgTreeVO.setChildren(parentIdVOMap.get(eduOrgTreeVO.getId()));
                setChildren(parentIdVOMap.get(eduOrgTreeVO.getId()), parentIdVOMap);
            }
        }
    }

    /**
     * 查询活动下的年级班级树形结构
     *
     * @param dto 请求参数
     * @return
     */
    @Override
    public List<EduOrgTreeVO> listMedalOrgTree(MedalActivityIdDTO dto) {
        Assert.notNull(dto.getActivityId(), () -> new BizException("活动id不能为空"));
        List<MedalActivityGrade> medalActivityGrades = medalActivityGradeManager.list(new LambdaQueryWrapper<MedalActivityGrade>()
                .eq(MedalActivityGrade::getMedalActivityId, dto.getActivityId()));
        if (CollUtil.isEmpty(medalActivityGrades)) {
            return Collections.emptyList();
        }
        List<String> gradeIds = medalActivityGrades.stream().map(MedalActivityGrade::getGradeId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(gradeIds)) {
            return Collections.emptyList();
        }

        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentIdType(2);
        eduOrgQueryDTO.setCurrentId(WebUtil.getCampusIdLong());
        eduOrgQueryDTO.setIsTree(Constant.NO);
        List<EduOrgTreeVO> eduOrgTreeVOList = cacheSaasManager.queryEducationalOrgTree(eduOrgQueryDTO);


        List<EduOrgTreeVO> orgTreeVOS = eduOrgTreeVOList.stream().filter(t -> CollUtil.newArrayList(4, 5).contains(t.getType())).filter(s -> {
            Boolean flag = Boolean.FALSE;
            if (gradeIds.contains(Convert.toStr(s.getId())) || gradeIds.contains(Convert.toStr(s.getParentId()))) {
                flag = Boolean.TRUE;
            }
            return flag;
        }).collect(Collectors.toList());

        // 构建树形结构
        List<EduOrgTreeVO> eduOrgTreeVOS = toTree(orgTreeVOS);
        return eduOrgTreeVOS;
    }

    /**
     * 通过班级id查询学生信息
     *
     * @param dto 请求参数
     * @return
     */
    @Override
    public List<MedalStudentVO> listStudentInfo(MedalStudentDTO dto) {
        Assert.notNull(dto.getClassId(), () -> new BizException("班级id不能为空"));
        Assert.notNull(dto.getMedalInfoId(), () -> new BizException("奖章id不能为空"));

        EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
        eduStudentPageQueryDTO.setSchoolId(Convert.toLong(WebUtil.getSchoolId()));
        eduStudentPageQueryDTO.setNameLike(null);
        eduStudentPageQueryDTO.setCampusId(Convert.toLong(WebUtil.getCampusId()));
        eduStudentPageQueryDTO.setClassId(Convert.toLong(dto.getClassId()));
        // saas获取数据(不做名字搜索)
        List<EduStudentInfoVO> eduStudentInfos = basicInfoService.queryStudentPage(eduStudentPageQueryDTO);
        if (CollUtil.isEmpty(eduStudentInfos)) {
            return Collections.emptyList();
        }

        // 查询学生获得的奖章数
        List<Long> studentIds = eduStudentInfos.stream().map(EduStudentInfoVO::getId).distinct().collect(Collectors.toList());
        List<MedalUserAcquireRecord> medalUserAcquireRecords = medalUserAcquireRecordManager.list(new LambdaQueryWrapper<MedalUserAcquireRecord>()
                .eq(MedalUserAcquireRecord::getMedalInfoId, dto.getMedalInfoId())
                .in(MedalUserAcquireRecord::getStudentId, studentIds)
                .eq(MedalUserAcquireRecord::getAwardStatus, MedalAwardStatusEnum.IS_COMPLETION.getCode()));
        Map<String, Long> studentMedalNumMap = medalUserAcquireRecords.stream().collect(Collectors.groupingBy(MedalUserAcquireRecord::getStudentId, Collectors.counting()));

        List<MedalStudentVO> collect = eduStudentInfos.stream().map(s -> {
            MedalStudentVO medalStudentVO = new MedalStudentVO();
            medalStudentVO.setStudentId(Convert.toStr(s.getId()));
            medalStudentVO.setStudentNo(s.getStudentNo());
            medalStudentVO.setStudentName(s.getStudentName());
            if (Objects.nonNull(studentMedalNumMap.get(Convert.toStr(s.getId())))) {
                medalStudentVO.setMedalNum(studentMedalNumMap.get(Convert.toStr(s.getId())));
            } else {
                medalStudentVO.setMedalNum(0L);
            }
            return medalStudentVO;
        }).collect(Collectors.toList());

        return collect;
    }

    /**
     * 获取奖章列表
     *
     * @return
     */
    @Override
    public List<MedalCampusInfoDTO> listMedalInfo() {
        List<MedalInfo> medalInfos = medalInfoManager.list(new LambdaQueryWrapper<MedalInfo>()
                .eq(MedalInfo::getTenantId, WebUtil.getTenantId())
                .eq(MedalInfo::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalInfo::getCampusId, WebUtil.getCampusId()));
        if (CollUtil.isEmpty(medalInfos)) {
            return Collections.emptyList();
        }
        List<MedalCampusInfoDTO> medalCampusInfoDTOS = medalInfos.stream().map(s -> {
            MedalCampusInfoDTO medalCampusInfoDTO = new MedalCampusInfoDTO();
            medalCampusInfoDTO.setMedalInfoId(s.getId());
            medalCampusInfoDTO.setMedalInfoName(s.getName());
            return medalCampusInfoDTO;
        }).collect(Collectors.toList());

        return medalCampusInfoDTOS;
    }


}
