package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.constant.SaasConstant;
import com.hailiang.enums.LogSourceEnum;
import com.hailiang.enums.MessageLogUserTypeEnum;
import com.hailiang.mapper.LoginLogMapper;
import com.hailiang.model.dto.query.LoginLogQueryDTO;
import com.hailiang.model.dto.save.LoginLogSaveDTO;
import com.hailiang.model.entity.LoginLog;
import com.hailiang.model.vo.LoginLogVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.vo.staff.StaffBatchQueryDTO;
import com.hailiang.remote.saas.vo.staff.StaffBatchVO;
import com.hailiang.service.LoginLogService;
import com.hailiang.util.IpInfoUtil;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Objects;

/**
 * 登录日志 服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LoginLogServiceImpl extends ServiceImpl<LoginLogMapper, LoginLog> implements LoginLogService {
    @Resource
    private IpInfoUtil ipInfoUtil;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 分页查询登录日志
     *
     * @param page        分页参数
     * @param loginLogDTO 查询参数
     * @return 日志查询结果
     */
    @Override
    public IPage<LoginLogVO> pageLoginLog(Page page, LoginLogQueryDTO loginLogDTO) {
        //拼接查询条件
        LambdaQueryWrapper<LoginLog> queryWrapper = new LambdaQueryWrapper<LoginLog>().eq(StrUtil.isNotEmpty(WebUtil.getSchoolId()), LoginLog::getSchoolId, WebUtil.getSchoolId())
                .eq(StrUtil.isNotEmpty(loginLogDTO.getMobile()), LoginLog::getMobile, loginLogDTO.getMobile())
                .eq(Objects.nonNull(loginLogDTO.getUserId()) && loginLogDTO.getUserId() != 0, LoginLog::getUserId, loginLogDTO.getUserId());
        //返回VO数据：
        return this.page(page, queryWrapper).convert(i -> {
            LoginLogVO loginLogVO = new LoginLogVO();
            BeanUtils.copyProperties(i, loginLogVO);
            return loginLogVO;
        });
    }

    /**
     * 保存日志
     */
    @Override
    public boolean saveLoginLog(HttpServletRequest request) {
        LoginLogSaveDTO loginLogSaveDTO = new LoginLogSaveDTO();
        loginLogSaveDTO.setIp(ipInfoUtil.getIpAddr(request));
        loginLogSaveDTO.setSource(LogSourceEnum.PC.getCode());
        loginLogSaveDTO.setUserId(WebUtil.getStaffId());
        loginLogSaveDTO.setUserType(MessageLogUserTypeEnum.STAFF.getCode());
        loginLogSaveDTO.setSchoolId(WebUtil.getSchoolId());
        loginLogSaveDTO.setCampusId(WebUtil.getCampusId());
        loginLogSaveDTO.setTenantId(WebUtil.getTenantIdLong());
        loginLogSaveDTO.setSuccess(Constant.YES);
        if (StrUtil.isNotBlank(WebUtil.getStaffId())) {
            loginLogSaveDTO.setMobile(getStaffMobile());
        }
        LoginLog loginLog = new LoginLog();
        BeanUtil.copyProperties(loginLogSaveDTO, loginLog);
        return this.save(loginLog);
    }

    /**
     * 保存登录日志  1：钉钉， 2：飞书 ，3：微信，4：浙里办，5，积分柜  9：其他
     */
    @Override
    public Object saveLoginLogAll(HttpServletRequest request) {

        // 使用StringBuilder来构建请求头信息的字符串
        StringBuilder headersSb = new StringBuilder();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headersSb.append(headerName).append("：").append(headerValue).append("\n");
        }

        log.info("【登录日志】-【请求头信息】：{}", headersSb);


        Integer sourcePlatform = Convert.toInt(request.getHeader(SaasConstant.SOURCE_PLATFORM));
        String mobile = request.getHeader(SaasConstant.MOBILE);

        //工厂模式获取不同日志对象
        LoginLogSaveDTO loginLogSaveDTO = getBySource(sourcePlatform, mobile);

        loginLogSaveDTO.setIp(ipInfoUtil.getIpAddr(request));
        loginLogSaveDTO.setSource(sourcePlatform);
        loginLogSaveDTO.setSchoolId(WebUtil.getSchoolId());
        loginLogSaveDTO.setCampusId(WebUtil.getCampusId());
        loginLogSaveDTO.setTenantId(WebUtil.getTenantIdLong());
        loginLogSaveDTO.setSuccess(Constant.YES);

        LoginLog loginLog = new LoginLog();
        BeanUtil.copyProperties(loginLogSaveDTO, loginLog);
        return this.save(loginLog);
    }

    /**
     * 工厂模式，根据不同参数获取不同日志对象
     *
     * @param sourcePlatform
     * @return
     */
    private LoginLogSaveDTO getBySource(Integer sourcePlatform, String mobile) {
        LoginLogSaveDTO loginLogSaveDTO = new LoginLogSaveDTO();
        if (LogSourceEnum.WEIXIN.getCode().equals(sourcePlatform)) {
            loginLogSaveDTO.setUserId(WebUtil.getStudentCode());
            loginLogSaveDTO.setUserType(MessageLogUserTypeEnum.PARENT.getCode());
            loginLogSaveDTO.setMobile(mobile);
            return loginLogSaveDTO;
        } else if (LogSourceEnum.FEISHU.getCode().equals(sourcePlatform)
                || LogSourceEnum.DINGDING.getCode().equals(sourcePlatform)
                || LogSourceEnum.H5.getCode().equals(sourcePlatform)
                || LogSourceEnum.TEACHER_MOBILE.getCode().equals(sourcePlatform)) {
            if (StrUtil.isNotBlank(WebUtil.getStudentIdStr())) {
                loginLogSaveDTO.setUserId(WebUtil.getStudentIdStr());
                loginLogSaveDTO.setUserType(MessageLogUserTypeEnum.STUDENT.getCode());
            } else {
                loginLogSaveDTO.setUserId(WebUtil.getStaffId());
                loginLogSaveDTO.setUserType(MessageLogUserTypeEnum.STAFF.getCode());
                if (Objects.nonNull(WebUtil.getStaffId())) {
                    loginLogSaveDTO.setMobile(getStaffMobile());
                }
            }
            return loginLogSaveDTO;
        } else if (LogSourceEnum.CABINET.getCode().equals(sourcePlatform)) {
            loginLogSaveDTO.setUserId(WebUtil.getStudentCode());
            loginLogSaveDTO.setUserType(MessageLogUserTypeEnum.STUDENT.getCode());
            return loginLogSaveDTO;
        }
        return loginLogSaveDTO;
    }

    /**
     * 获取登录教职工手机号
     */
    private String getStaffMobile() {
        log.info("获取登录教职工手机号-教职工id:{}", WebUtil.getStaffId());
        String mobile = "";
        String staffIdMobile = redisUtil.getStr(RedisKeyConstants.OPERATE_LOG_STAFFID + WebUtil.getStaffId());
        if (CharSequenceUtil.isNotEmpty(staffIdMobile)) {
            mobile = staffIdMobile;
        } else {
            log.debug("登录日志-准备获取手机号");
            StaffBatchQueryDTO staffBatchQueryDTO = new StaffBatchQueryDTO();
            List<Long> staffIds = new ArrayList<>();
            staffIds.add(WebUtil.getStaffIdLong());
            staffBatchQueryDTO.setStaffIdList(staffIds);
            List<StaffBatchVO> staffBatchVOS = basicInfoRemote.queryBatchStaffList(staffBatchQueryDTO);
            if (!staffBatchVOS.isEmpty()) {
                mobile = staffBatchVOS.get(0).getMobile();
            }
        }
        return mobile;
    }

}
