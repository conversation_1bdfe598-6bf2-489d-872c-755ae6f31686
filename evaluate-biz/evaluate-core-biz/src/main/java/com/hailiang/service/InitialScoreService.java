package com.hailiang.service;

import com.hailiang.model.dto.query.InitScoreSaveRequest;
import com.hailiang.model.vo.InitScoreVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import org.springframework.transaction.annotation.Transactional;

/**
 * 初始分相关逻辑服务
 *
 * @Description: 初始分相关逻辑服务
 * @Author: Jovi
 * @Date: Created in 2024/8/20
 * @Version: 2.0.0
 */
public interface InitialScoreService {

    /**
     * 查询初始分
     *
     * @return
     */
    InitScoreVO getInitScore();


    /**
     * 【定时任务】初始化各个校区初始分
     *
     * @param schoolId
     * @param campusId
     * @param newTerm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean saveInitScoreJob(String schoolId, String campusId, TermVo newTerm);

    /**
     * 【手动】提交初始分
     *
     * @param dto
     * @return
     */
    String saveInitScore(InitScoreSaveRequest dto);

}