package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hailiang.constant.Constant;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.manager.SubjectInfoManager;
import com.hailiang.model.report.entity.SubjectInfoPO;
import com.hailiang.model.response.SectionInfoResponse;
import com.hailiang.model.response.SubjectInfoResponse;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.CampusGeneralService;
import com.hailiang.util.WebUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description: 校区通用服务
 * @Author: huyouting
 * @Date: Created in 2025-07-31
 * @Version: v2.6.7
 */
@Slf4j
@Service
public class CampusGeneralServiceImpl implements CampusGeneralService {

    @Resource
    private BasicInfoService basicInfoService;

    @Resource
    private SubjectInfoManager subjectInfoManager;

    @Override
    public List<SectionInfoResponse> listSectionInfo() {
        String tenantId = WebUtil.getTenantId();
        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();
        Long campusIdLong = WebUtil.getCampusIdLong();

        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(campusIdLong);
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.CAMPUS.getCode());
        eduOrgQueryDTO.setEndType(SaasCurrentIdTypeEnum.SECTION.getCode());
        eduOrgQueryDTO.setIsTree(Constant.NO);
        List<EduOrgTreeVO> eduOrgTrees = basicInfoService.queryEducationalOrgTreeV2(eduOrgQueryDTO);
        if (CollUtil.isEmpty(eduOrgTrees)) {
            return Collections.emptyList();
        }

        // 获取学段信息
        List<SectionInfoResponse> sectionInfos = eduOrgTrees.stream().filter(s-> CharSequenceUtil.isNotBlank(s.getCode())).map(s -> {
            SectionInfoResponse sectionInfoResponse = new SectionInfoResponse();
            sectionInfoResponse.setSectionId(s.getId());
            sectionInfoResponse.setSectionCode(s.getCode());
            sectionInfoResponse.setSectionName(s.getName());
            return sectionInfoResponse;
        }).collect(Collectors.toList());
        if (CollUtil.isEmpty(sectionInfos)) {
            return Collections.emptyList();
        }

        List<String> sectionCodes = sectionInfos.stream()
                .map(SectionInfoResponse::getSectionCode)
                .distinct()
                .collect(Collectors.toList());

        List<SubjectInfoPO> subjectInfos = subjectInfoManager.listBySectionCodeList(
                sectionCodes,
                tenantId,
                schoolId,
                campusId);
        if (CollUtil.isEmpty(subjectInfos)){
            return sectionInfos;
        }

        // 获取所有课程信息
        List<SubjectInfoPO> courseInfos = subjectInfos.stream().filter(s -> ObjectUtil.equal(s.getSubjectType(), 1))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(courseInfos)){
            return sectionInfos;
        }
        Map<String, List<SubjectInfoResponse>> courseInfoMap = courseInfos.stream().map(s -> {
            SubjectInfoResponse subjectInfoResponse = new SubjectInfoResponse();
            subjectInfoResponse.setSectionCode(s.getSectionCode());
            subjectInfoResponse.setSectionName(s.getSectionName());
            subjectInfoResponse.setCourseId(Convert.toLong(s.getSubjectId()));
            subjectInfoResponse.setCourseCode(s.getSubjectCode());
            subjectInfoResponse.setCourseName(s.getSubjectName());
            subjectInfoResponse.setSubjectId(s.getParentDisciplineId());
            subjectInfoResponse.setSubjectCode(s.getParentDisciplineCode());
            subjectInfoResponse.setSubjectName(s.getParentDisciplineName());
            return subjectInfoResponse;
        }).collect(Collectors.groupingBy(SubjectInfoResponse::getSectionCode));

        for (SectionInfoResponse sectionInfo : sectionInfos) {
            if (CollUtil.isEmpty(courseInfoMap.get(sectionInfo.getSectionCode()))){
                continue;
            }
            List<SubjectInfoResponse> subjectInfoResponses = courseInfoMap.get(sectionInfo.getSectionCode());

            // 根据courseId去重
            if (CollUtil.isNotEmpty(subjectInfoResponses)) {
                subjectInfoResponses = subjectInfoResponses.stream()
                        .collect(Collectors.toMap(
                                SubjectInfoResponse::getCourseId,
                                java.util.function.Function.identity(),
                                (existing, replacement) -> existing
                        ))
                        .values()
                        .stream()
                        .collect(Collectors.toList());
            }

            subjectInfoResponses.forEach(s -> s.setSectionId(sectionInfo.getSectionId()));
            sectionInfo.setSubjectInfoList(subjectInfoResponses);
        }
        return sectionInfos;
    }
}
