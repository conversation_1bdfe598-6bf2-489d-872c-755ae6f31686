package com.hailiang.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.hailiang.constant.Constant;
import com.hailiang.convert.SpeedConfigConvert;
import com.hailiang.convert.SpeedConfigRTargetGroupConvert;
import com.hailiang.convert.SpeedTargetGroupConvert;
import com.hailiang.enums.SaasCurrentIdTypeEnum;
import com.hailiang.enums.speed.SortFieldTypeEnum;
import com.hailiang.enums.speed.SortTypeEnum;
import com.hailiang.enums.speed.UncommentedTimeTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.internal.InternalDriveRemote;
import com.hailiang.internal.model.request.PlanListsRequest;
import com.hailiang.internal.model.response.PlanListsResponse;
import com.hailiang.manager.BehaviourHandleManager;
import com.hailiang.manager.SpeedConfigManager;
import com.hailiang.manager.SpeedConfigRTargetGroupManager;
import com.hailiang.manager.SpeedTargetGroupManager;
import com.hailiang.model.entity.SpeedConfigPO;
import com.hailiang.model.entity.SpeedConfigRTargetGroupPO;
import com.hailiang.model.entity.SpeedTargetGroupPO;
import com.hailiang.model.request.speed.ClassQueryRequest;
import com.hailiang.model.request.speed.GroupSortRequest;
import com.hailiang.model.request.speed.SpeedConfigQueryRequest;
import com.hailiang.model.request.speed.SpeedConfigRequest;
import com.hailiang.model.response.speed.SpeedConfigResponse;
import com.hailiang.model.response.speed.StudentResponse;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.vo.educational.EduClassInfoLinkVO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeVO;
import com.hailiang.response.GroupResponse;
import com.hailiang.saas.SaasSchoolCacheManager;
import com.hailiang.saas.SaasStudentCacheManager;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.dto.StudentPageQueryDTO;
import com.hailiang.saas.model.dto.school.SchoolIdsRequest;
import com.hailiang.saas.model.vo.StudentInfo1VO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.service.SpeedConfigService;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Service
@RequiredArgsConstructor
public class SpeedConfigServiceImpl implements SpeedConfigService {

    final SpeedConfigConvert configConvert;
    final SpeedConfigManager configManager;
    final BasicInfoService basicInfoService;
    final SaasStudentManager saasStudentManager;
    final SpeedTargetGroupConvert targetGroupConvert;
    final SpeedTargetGroupManager targetGroupManager;
    final BehaviourHandleManager behaviourHandleManager;
    final SpeedConfigRTargetGroupManager configRTargetGroupManager;
    final SpeedConfigRTargetGroupConvert configRTargetGroupConvert;
    final BasicInfoRemote basicInfoRemote;
    final InternalDriveRemote internalDriveRemote;
    final SaasStudentCacheManager saasStudentCacheManager;

    @Override
    public void saveOrUpdate(SpeedConfigRequest request) {

        String staffId = Assert.notBlank(WebUtil.getStaffId(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        String campusId = Assert.notBlank(WebUtil.getCampusId(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));

        SpeedConfigPO speedConfigPO = configManager.getOneByStaffIdAndCampusId(staffId, campusId);
        Assert.isTrue(configManager.saveOrUpdate(configConvert.requestToEntity(speedConfigPO, request)),
                () -> new BizException("配置更新失败"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGroupOrder(GroupSortRequest request) {

        String staffId = Assert.notBlank(WebUtil.getStaffId(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        String campusId = Assert.notBlank(WebUtil.getCampusId(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        List<Long> groupIds = Assert.notEmpty(request.getGroupIds(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));

        List<SpeedTargetGroupPO> targetGroupPOS = targetGroupManager.listByIds(groupIds);
        Assert.notEmpty(targetGroupPOS, () -> new BizException("指标分组不存在"));
        List<Long> oldGroupIds = targetGroupPOS.stream().map(SpeedTargetGroupPO::getId).collect(Collectors.toList());
        Assert.isTrue(new HashSet<>(groupIds).containsAll(oldGroupIds), () -> new BizException("指标分组非法"));

        SpeedConfigPO speedConfigPO = configManager.getOneByStaffIdAndCampusId(staffId, campusId);
        if (ObjectUtil.isNull(speedConfigPO)) {
            speedConfigPO = this.getDefaultSpeedConfigPO();
            Assert.isTrue(configManager.save(speedConfigPO), () -> new BizException("配置初始化失败"));
        }

        List<SpeedConfigRTargetGroupPO> configRTargetGroups = configRTargetGroupConvert.groupIdListToPOList(groupIds,
                speedConfigPO.getId());
        Assert.isTrue(configRTargetGroupManager.updateBatch(configRTargetGroups), () -> new BizException("指标分组更新失败"));
    }

    @Override
    public List<EduOrgTreeVO> listClass(ClassQueryRequest request) {

        String staffId = Assert.notBlank(WebUtil.getStaffId(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        String campusId = Assert.notBlank(WebUtil.getCampusId(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        Boolean isShowAll = Assert.notNull(request.getIsShowAll(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));

        log.info("【查询H5班级列表】-【教职工任教班级】-【教职工ID:{},校区ID:{}", staffId, campusId);

        //获取教职工所属班级（包含权限过滤）
        List<EduOrgTreeVO> limitedClassList = basicInfoService.queryEducationalOrgTree(request);
        log.info("【查询H5班级列表】-【教职工任教班级】-【当前教职工在海思谷的教学关系情况：{}】",
                JSONUtil.toJsonStr(limitedClassList));
        //填充班级积分板列表
        this.fillPlanList(limitedClassList);

        log.info("【查询H5班级列表】-【权限内所有班级】-【limitedClassList：{}】",
                JSONUtil.toJsonStr(limitedClassList));
        if (Boolean.TRUE.equals(isShowAll)) {
            log.info("【查询H5班级列表】-【设置为显示全部】- 直接返回权限内所有班级");
            return limitedClassList;
        }

        SpeedConfigPO speedConfigPO = configManager.getOneByStaffIdAndCampusId(staffId, campusId);
        log.info("【查询H5班级列表】-【当前教职工极速点评配置】-【speedConfigPO：{}】", JSONUtil.toJsonStr(speedConfigPO));
        if (ObjectUtil.isNull(speedConfigPO) || StringUtils.isBlank(speedConfigPO.getClassIds())) {
            log.info("【查询H5班级列表】-【配置或者可选班级不存在】- 直接返回权限内所有班级");
            return limitedClassList;
        }

        /**
         * 自定义设置的班级ID集合
         */
        List<String> customClassIds = Arrays.asList(speedConfigPO.getClassIds().split(Constant.ENGLISH_COMMA));

        if (CollUtil.isEmpty(customClassIds)) {
            log.warn("【查询H5班级列表】-【可选班级不存在，直接返回权限内所有班级】");
            return limitedClassList;
        }

        // 查询自定义设置的班级的状态
        List<EduClassInfoLinkVO> eduClassInfos = basicInfoRemote.listUnderByClassIds(Convert.toList(Long.class, customClassIds));

        // 过滤掉未升级的班级
        List<String> filterNotUpgradedCustomerClassIds = new ArrayList<>();
        for (EduClassInfoLinkVO eduClassInfo : eduClassInfos) {
            if (Constant.ZERO.equals(Convert.toInt(eduClassInfo.getUpgradeStatus()))) {
                filterNotUpgradedCustomerClassIds.add(eduClassInfo.getId().toString());
            }
        }
        if (CollUtil.isEmpty(filterNotUpgradedCustomerClassIds)) {
            log.warn("【查询H5班级列表】-【当前教职工极速点评已配置班级可能都已毕业，直接返回权限内所有班级】");

            return limitedClassList;
        }

        this.filterUnselectedClass(limitedClassList, customClassIds);
        log.info("【查询H5班级列表】-【只显示已选择班级】-【过滤后limitedClassList：{}】", JSONUtil.toJsonStr(limitedClassList));
        this.filterEmptyChildren(limitedClassList);
        log.info("【查询H5班级列表】-【过滤空班级】-【过滤后limitedClassList：{}】", JSONUtil.toJsonStr(limitedClassList));
        List<EduOrgTreeVO> orgTreeVOS = limitedClassList.stream().filter(e -> CollUtil.isNotEmpty(e.getChildren()))
                .collect(Collectors.toList());
        log.info("【查询H5班级列表】-【最终返回的数据】-【orgTreeVOS：{}】", JSONUtil.toJsonStr(orgTreeVOS));

        // 增加一段逻辑 如果老师在极速点评设置中配置的班级id没有了教学关系，按照之前逻辑是显示空，不够友好，如果是这种情况，认为配置的常见班级失效，
        // 直接返回当前老师权限内所有班级
        if (CollUtil.isEmpty(orgTreeVOS)) {
            log.warn("【查询H5班级列表】-【过滤后的组织树为空，可能是配置的班级全部失效，直接返回权限内所有班级】");
//            orgTreeVOS = limitedClassListTemp.stream().filter(e -> CollUtil.isNotEmpty(e.getChildren()))
//                    .collect(Collectors.toList());

            return orgTreeVOS;
        }

        return orgTreeVOS;
    }

    /**
     * 填充班级积分板列表
     */
    private void fillPlanList(List<EduOrgTreeVO> eduOrgTreeVOS) {
        if (CollUtil.isEmpty(eduOrgTreeVOS)) {
            log.warn("【查询H5班级列表-填充班级积分板数据-入参为空直接返回】");
            return;
        }

        //获取班级节点
        List<EduOrgTreeVO> orgTreeVOS = this.findClassNodes(eduOrgTreeVOS);
        if (CollUtil.isEmpty(orgTreeVOS)) {
            log.warn("【查询H5班级列表-填充班级积分板数据-班级节点为空,直接返回】");
            return;
        }

        //积分板
        List<PlanListsResponse> planListsResponses = this.listPlanResponses(orgTreeVOS);
        log.info("【查询H5班级列表-填充班级积分板数据-班级积分板列表】星动力返回:{}", JSONUtil.toJsonStr(planListsResponses));
        if (CollUtil.isEmpty(planListsResponses)) {
            log.warn("【查询H5班级列表-填充班级积分板数据-班级积分板列表为空,直接返回】");
            return;
        }

        Map<Long, List<PlanListsResponse>> classPlanMap = CollStreamUtil
                .groupByKey(planListsResponses, PlanListsResponse::getSaasClassId);

        orgTreeVOS.forEach(e -> e.setPlanLists(classPlanMap.getOrDefault(e.getId(), Collections.emptyList())));
    }

    /**
     * 获取班级积分板列表
     */
    private List<PlanListsResponse> listPlanResponses(List<EduOrgTreeVO> orgTreeVOS) {
        if (CollUtil.isEmpty(orgTreeVOS)) {
            log.warn("【查询H5班级列表-填充班级积分板数据-orgTreeVOS为空，直接返回】");
            return Collections.emptyList();
        }
        List<Long> classIds = orgTreeVOS.stream().map(EduOrgTreeVO::getId).collect(Collectors.toList());
        PlanListsRequest planListsRequest = new PlanListsRequest();
        planListsRequest.setStaffId(WebUtil.getStaffIdLong());
        planListsRequest.setSaasSchoolId(WebUtil.getSchoolIdLong());
        planListsRequest.setSaasClassIds(classIds);
        return internalDriveRemote.queryPlanLists(planListsRequest);
    }


    // 递归方法，用于找到班级的节点
    public List<EduOrgTreeVO> findClassNodes(List<EduOrgTreeVO> nodes) {
        List<EduOrgTreeVO> result = new ArrayList<>();
        if (CollUtil.isEmpty(nodes)) {
            log.warn("【查询H5班级列表-递归方法，用于找到班级的节点，入参为空直接返回】nodes:{}", nodes);
            return result;
        }
        for (EduOrgTreeVO node : nodes) {
            if (SaasCurrentIdTypeEnum.CLASS.getCode().equals(node.getType())) {
                // 如果当前节点是type为班级，则添加到结果列表中
                result.add(node);
            }
            // 递归检查子节点
            if (node.getChildren() != null) {
                result.addAll(findClassNodes(node.getChildren()));
            }
        }

        return result;
    }

    @Override
    public List<StudentResponse> listStudent(SpeedConfigQueryRequest request) {

        Integer sortType = request.getSortType();
        Integer sortField = request.getSortField();

        Assert.isTrue(SortTypeEnum.isValid(sortType),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        Assert.isTrue(SortFieldTypeEnum.isValid(sortField),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        Long classId = Assert.notNull(request.getClassId(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        String staffId = Assert.notBlank(WebUtil.getStaffId(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        String campusId = Assert.notBlank(WebUtil.getCampusId(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        Boolean isUncommented = Assert.notNull(request.getIsUncommented(),
                () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));

        TimeInterval timeInterval = DateUtil.timer();

        List<StudentInfo1VO> saasStudents = saasStudentCacheManager.queryStudentPageCache(this.buildQueryStudentDto(classId));
        if (CollUtil.isEmpty(saasStudents)) {
            log.warn("【查询H5学生列表】-【学生不存在直接返回】-【班级ID:{},校区ID:{}】", classId, campusId);
            return Collections.emptyList();
        }

        log.warn("【查询H5学生列表】-【获取Saas对应班级下的学生信息】-【结束】-【学生数量：{}】，耗时：【{}】", saasStudents.size(), timeInterval.intervalMs());
        timeInterval.restart();

        List<StudentResponse> studentResponses = this.studentInfoListToResponse(saasStudents);


        this.fillStudentScore(sortField, studentResponses);
        log.warn("【查询H5学生列表】-【填充学生近一个月的点评分数】-【结束】，耗时：【{}】", timeInterval.intervalMs());
        timeInterval.restart();


        List<StudentResponse> sorted = this.sorted(saasStudents, studentResponses, Convert.toStr(classId), campusId, staffId, sortField, sortType, isUncommented);
        log.warn("【查询H5学生列表】-【多维度排序】-【结束】，耗时：【{}】", timeInterval.intervalMs());

        return sorted;
    }

    @Override
    public SpeedConfigResponse getOneByCampusIdAndStaffId(String campusId, String staffId) {

        Assert.notBlank(campusId, () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));
        Assert.notBlank(staffId, () -> new BizException(BizExceptionEnum.PARAMETER_NOT_VALIDATE));

        SpeedConfigPO speedConfigPO = configManager.getOneByStaffIdAndCampusId(staffId, campusId);
        List<GroupResponse> groupResponses = this.listTargetGroupOrder(speedConfigPO, campusId, staffId);
        return this.configConvert.entityToResponse(speedConfigPO, groupResponses);
    }

    /**
     * 获取指标分组顺序
     * 优先获取教职工的指标分组顺序
     * 其次获取默认的校区指标分组顺序
     */
    private List<GroupResponse> listTargetGroupOrder(SpeedConfigPO speedConfigPO, String campusId, String staffId) {
        List<SpeedTargetGroupPO> campusTargetGroups = targetGroupManager.listByCampusId(campusId);
        if (CollUtil.isEmpty(campusTargetGroups)) {
            log.info("【查询H5极速点评配置】-【校区未配置指标分组】-【教职工ID:{},校区ID:{}】", staffId, campusId);
            return Collections.emptyList();
        }

        if (ObjectUtil.isNull(speedConfigPO)) {
            return targetGroupConvert.campusGroupListToResponseList(campusTargetGroups);
        }

        List<SpeedConfigRTargetGroupPO> staffTargetGroups =
                configRTargetGroupManager.listBySpeedConfigId(speedConfigPO.getId());
        if (CollUtil.isEmpty(staffTargetGroups)) {
            return targetGroupConvert.campusGroupListToResponseList(campusTargetGroups);
        }


        Optional<Integer> max = staffTargetGroups.stream().map(SpeedConfigRTargetGroupPO::getSortIndex).max(Integer::compareTo);
        Integer maxCustomSort = max.get() + 1;

        Map<Long, Integer> groupStortMap = staffTargetGroups.stream()
                .collect(Collectors.toMap(SpeedConfigRTargetGroupPO::getSpeedTargetGroupId,
                        SpeedConfigRTargetGroupPO::getSortIndex));
        return targetGroupConvert.staffGroupListToResponseList(campusTargetGroups, groupStortMap, maxCustomSort);
    }

    private StudentPageQueryDTO buildQueryStudentDto(Long classId) {
        StudentPageQueryDTO dto = new StudentPageQueryDTO();
        dto.setSchoolId(WebUtil.getSchoolIdLong());
        dto.setCampusId(WebUtil.getCampusIdLong());
        dto.setClassId(classId);
        dto.setPageNum(1);
        dto.setPageSize(1000);
        dto.setGraduationStatus("0");
        return dto;
    }

    private List<StudentResponse> studentInfoListToResponse(List<StudentInfo1VO> saasStudents) {
        Map<Long, Integer> leaveStatusFlagMap = basicInfoService.getLeaveStatusFlagMap(
                saasStudents.stream().map(StudentInfo1VO::getId).distinct().collect(Collectors.toList()));

        return saasStudents.stream().map(studentInfo -> {
            StudentResponse studentResponse = new StudentResponse();
            studentResponse.setStudentId(studentInfo.getId().toString());
            studentResponse.setStudentNo(studentInfo.getStudentNo());
            studentResponse.setStudentName(studentInfo.getStudentName());
            studentResponse.setLeaveStatusFlag(leaveStatusFlagMap.getOrDefault(studentInfo.getId(), Constant.NO));
            return studentResponse;
        }).collect(Collectors.toList());
    }


    /**
     * 填充学生近一个月的点评分数
     *
     * @param sortField        排序字段（1按学号、2按姓名、3按分数）
     * @param studentResponses
     */
    private void fillStudentScore(Integer sortField, List<StudentResponse> studentResponses) {
        if (!SortFieldTypeEnum.STUDENT_SCORE.getCode().equals(sortField)) {
            return;
        }

        DateTime endTime = DateUtil.date();
        DateTime startTime = DateUtil.endOfDay(DateUtil.offsetDay(endTime, -30));

        List<String> studentIds = studentResponses
                .stream()
                .map(StudentResponse::getStudentId)
                .collect(Collectors.toList());


        Map<String, BigDecimal> studentScoreMap = behaviourHandleManager.listStudentTotalScore(studentIds, startTime,
                endTime);
        studentResponses.forEach(studentResponse -> studentResponse.setScore(studentScoreMap.getOrDefault(studentResponse.getStudentId(), BigDecimal.ZERO)));
    }

    /**
     * 多维度排序
     *
     * @param saasStudents
     * @param studentResponses
     * @param campusId
     * @param staffId
     * @param sortField        排序字段（1按学号、2按姓名、3按分数）
     * @param sortType         排序类型（1 正序、2 倒序）
     * @param isUncommented
     * @return
     */
    private List<StudentResponse> sorted(List<StudentInfo1VO> saasStudents,
                                         List<StudentResponse> studentResponses,
                                         String classId,
                                         String campusId,
                                         String staffId,
                                         Integer sortField,
                                         Integer sortType,
                                         Boolean isUncommented) {
        TimeInterval timeInterval = DateUtil.timer();

        UncommentedTimeTypeEnum uncommentedTimeEnum = this.getUncommentedTimeEnum(staffId, campusId, isUncommented);
        log.warn("【查询H5学生列表】-【多维度排序】-【获取配置】-【结束】，耗时：【{}】", timeInterval.intervalMs());
        timeInterval.restart();

        //默认排序规则
        if (UncommentedTimeTypeEnum.DEFAULT.getCode().equals(uncommentedTimeEnum.getCode())) {
            return this.defaultRuleSort(studentResponses, sortField, sortType);
        }

        //超时未点评排序
        DateTime endTime = DateUtil.date();
        DateTime startTime = DateUtil.offsetDay(endTime, uncommentedTimeEnum.getOffSet());

        List<String> saasStudentIds = saasStudents
                .stream()
                .map(e -> String.valueOf(e.getId()))
                .distinct()
                .collect(Collectors.toList());

        List<String> evaluatedStudentIds = behaviourHandleManager.listStudentExistRecord(
                staffId,
                classId,
                startTime,
                endTime);
        log.warn("【查询H5学生列表】-【多维度排序】-【获取设置超时时间内点评数据学生】-【结束】-【学生点评数量：{}】，耗时：【{}】",
                evaluatedStudentIds.size(),
                timeInterval.intervalMs());
        timeInterval.restart();

        //部分学生未点评
        if (CollUtil.isNotEmpty(evaluatedStudentIds) && saasStudentIds.size() != evaluatedStudentIds.size()) {

            Map<Boolean, List<StudentResponse>> mapByUncommented = studentResponses.stream()
                    .map(studentResponse -> {
                        studentResponse.setIsUncommented(!evaluatedStudentIds.contains(studentResponse.getStudentId()));
                        return studentResponse;
                    })
                    .collect(Collectors.toList()).stream()
                    .collect(Collectors.partitioningBy(StudentResponse::getIsUncommented));

            return Stream.concat(
                            mapByUncommented
                                    .get(Boolean.TRUE)
                                    .stream(),
                            this.defaultRuleSort(
                                            mapByUncommented.get(Boolean.FALSE),
                                            sortField,
                                            sortType)
                                    .stream())
                    .collect(Collectors.toList());
        }

        log.warn("【查询H5学生列表】-【多维度排序】-【部分学生未点评】-【结束】，耗时：【{}】",
                timeInterval.intervalMs());


        return this.defaultRuleSort(studentResponses, sortField, sortType);
    }

    /**
     * 获取超时未点评配置项
     *
     * @param staffId
     * @param campusId
     * @param isUncommented
     * @return
     */
    private UncommentedTimeTypeEnum getUncommentedTimeEnum(String staffId, String campusId, Boolean isUncommented) {
        if (StringUtils.isBlank(staffId) || Boolean.FALSE.equals(isUncommented)) {
            return UncommentedTimeTypeEnum.DEFAULT;
        }

        SpeedConfigPO speedConfigPO = configManager.getOneByStaffIdAndCampusId(staffId, campusId);
        if (ObjectUtil.isNull(speedConfigPO)) {
            return UncommentedTimeTypeEnum.THREE_DAY;
        }
        UncommentedTimeTypeEnum typeEnum = UncommentedTimeTypeEnum.getByCode(speedConfigPO.getUncommentedTimeType());
        return ObjectUtil.isNull(typeEnum) ? UncommentedTimeTypeEnum.DEFAULT : typeEnum;
    }

    /**
     * 默认值按照列表排序规则处理
     *
     * @param studentResponses
     * @param sortField        排序字段（1按学号、2按姓名、3按分数）
     * @param sortType         排序类型（1 正序、2 倒序）
     * @return
     */
    private List<StudentResponse> defaultRuleSort(List<StudentResponse> studentResponses,
                                                  Integer sortField,
                                                  Integer sortType) {

        Comparator<StudentResponse> sorted = this.getSortField(sortField);

        //倒序
        if (SortTypeEnum.DESC.getCode().equals(sortType)) {
            return studentResponses
                    .stream()
                    .sorted(sorted.reversed())
                    .collect(Collectors.toList());
        }
        //正序
        return studentResponses
                .stream()
                .sorted(sorted)
                .collect(Collectors.toList());
    }

    /**
     * 根据请求入参排序字段获取具体的排序规则
     *
     * @param sortField 排序字段（1按学号、2按姓名、3按分数）
     * @return
     */
    private Comparator<StudentResponse> getSortField(Integer sortField) {

        Map<Integer, Comparator<StudentResponse>> comparatorMap = new HashMap<>();

        comparatorMap.put(SortFieldTypeEnum.STUDENT_SCORE.getCode(), Comparator.comparing(StudentResponse::getScore));
        comparatorMap.put(SortFieldTypeEnum.STUDENT_NO.getCode(), Comparator.comparing(StudentResponse::getStudentNo));

        Comparator<StudentResponse> chinaComparing = Comparator.comparing(StudentResponse::getStudentName,
                Collator.getInstance(Locale.CHINA));
        comparatorMap.put(SortFieldTypeEnum.STUDENT_NAME.getCode(), chinaComparing);

        return comparatorMap.getOrDefault(sortField, Comparator.comparing(StudentResponse::getStudentNo));
    }

    /**
     * 过滤掉未选择的班级
     */
    private void filterUnselectedClass(List<EduOrgTreeVO> limitedClassList, List<String> classIds) {
        limitedClassList.removeIf(node -> SaasCurrentIdTypeEnum.CLASS.getCode().equals(node.getType()) && !classIds.contains(String.valueOf(node.getId())));
        limitedClassList.forEach(node -> {
            if (CollUtil.isNotEmpty(node.getChildren())) {
                // 递归处理子元素
                this.filterUnselectedClass(node.getChildren(), classIds);
            }
        });
    }

    /**
     * 过滤掉为空的子元素
     */
    private void filterEmptyChildren(List<EduOrgTreeVO> nodeList) {
        nodeList.removeIf(node -> !SaasCurrentIdTypeEnum.CLASS.getCode().equals(node.getType()) && CollUtil.isEmpty(node.getChildren()));
        nodeList.forEach(node -> {
            if (CollUtil.isNotEmpty(node.getChildren())) {
                // 递归处理子元素
                this.filterEmptyChildren(node.getChildren());
            }
        });
    }

    private SpeedConfigPO getDefaultSpeedConfigPO() {
        SpeedConfigPO speedConfigPO = new SpeedConfigPO();
        speedConfigPO.setStaffId(WebUtil.getStaffId());
        speedConfigPO.setTenantId(WebUtil.getTenantId());
        speedConfigPO.setSchoolId(WebUtil.getSchoolId());
        speedConfigPO.setCampusId(WebUtil.getCampusId());
        return speedConfigPO;
    }
}
