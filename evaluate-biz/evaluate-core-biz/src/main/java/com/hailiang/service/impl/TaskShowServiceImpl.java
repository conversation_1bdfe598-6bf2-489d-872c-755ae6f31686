package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.convert.TaskConvert;
import com.hailiang.manager.EvaluateTaskManager;
import com.hailiang.model.entity.TaskPO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.service.TaskShowService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class TaskShowServiceImpl implements TaskShowService {

    @Resource
    private EvaluateTaskManager taskManager;

    @Resource
    private TaskConvert taskConvert;

    @Resource
    private BasicInfoRemote basicInfoRemote;

    @Resource
    private RedisUtil redisUtil;


    @Override
    public Boolean isExistsTodoTask() {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.restart();

        //查询当前老师下有多少学生
        String staffId = WebUtil.getStaffId();
        List<String> studentIds = redisUtil.getOrAdd("evaluate:listTeacherCurrentEvaluateTask:queryStudentStrByStaffId:" + staffId,
                () -> basicInfoRemote.queryStudentStrByStaffId(staffId), CacheConstants.HALF_HOUR);

        log.warn("【H5】-【查询教师获取待办任务】-【根据教师ID从Saas获取对应任教学生ID集合】-【消耗时长:{}】", timeInterval.intervalMs());
        timeInterval.restart();

        //防止数据为空sql查询异常
        if (CollUtil.isEmpty(studentIds)) {
            studentIds.add("1");
        }
        List<TaskPO> evaluateTaskPOS = taskManager.listToDoTask(studentIds);
        log.warn("【H5】-【查询教师获取待办任务】-【查询数据库对应的待办任务】-【消耗时长:{}】", timeInterval.intervalMs());
        timeInterval.restart();

        if (CollUtil.isEmpty(evaluateTaskPOS)) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

}
