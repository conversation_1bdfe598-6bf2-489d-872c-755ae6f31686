package com.hailiang.service;

import com.hailiang.model.dto.activity.rule.save.RuleOptionInfoDTO;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TargetGroup;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/1 13:56
 */
public interface MedalActivityRuleService {

    /**
     * @param targetId 指标id
     * @param type     操作类型  1:修改指标出发规则填写项失效  2:删除指标  3:禁用指标  4:分组删除
     * @return
     */
    Boolean updateRuleStatusFailure(Long targetId, Integer type);

    /**
     * 根据指标事件批量修改争章活动任务状态
     * @param targets 指标集合
     * @param type     操作类型  1:修改指标出发规则填写项失效  2:删除指标  3:禁用指标
     * @return
     */
    Boolean batchUpdateRuleStatusFailureByTargets(Collection<Target> targets, Integer type);

    /**
     * 根据指标分组事件批量修改争章活动任务状态
     * @param targetGroupList 指标分组集合
     * @param type     操作类型  4:分组删除
     * @return
     */
    Boolean batchUpdateRuleStatusFailureByTargetGroups(Collection<TargetGroup> targetGroupList, Integer type);


    /**
     * 获取指标下的所有选项
     *
     * @param targetId 指标id
     * @return
     */
    List<RuleOptionInfoDTO> listTargetOptions(Long targetId);
}
