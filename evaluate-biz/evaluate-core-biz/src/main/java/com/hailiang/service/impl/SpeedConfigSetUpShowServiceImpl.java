package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.manager.SpeedTargetGroupManager;
import com.hailiang.manager.SpeedTargetGroupRTargetManager;
import com.hailiang.manager.TargetGroupManager;
import com.hailiang.manager.TargetManager;
import com.hailiang.mapper.mongo.TargetTemplateDao;
import com.hailiang.model.dto.request.speed.remote.SpeedGroupDetailRemoteRequest;
import com.hailiang.model.dto.request.speed.remote.SpeedGroupDetailRequest;
import com.hailiang.model.dto.response.speed.*;
import com.hailiang.model.dto.response.speed.remote.RemoteGroupSelectedOptionsDTO;
import com.hailiang.model.dto.response.speed.remote.SpeedTargetGroupDetailResponse;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.dto.target.TargetAndGroupDTO;
import com.hailiang.model.entity.SpeedTargetGroupPO;
import com.hailiang.model.entity.Target;
import com.hailiang.model.entity.TargetGroup;
import com.hailiang.model.entity.mongo.TargetTemplate;
import com.hailiang.model.response.speed.SpeedConfigResponse;
import com.hailiang.model.vo.ListAllEvaluateTargetVOInnerTarget;
import com.hailiang.model.vo.ListAllEvaluateTargetVOModule;
import com.hailiang.response.GroupResponse;
import com.hailiang.service.SpeedConfigService;
import com.hailiang.service.SpeedConfigSetUpShowService;
import com.hailiang.service.TargetGroupService;
import com.hailiang.util.WebUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.hailiang.constant.Constant.ONE;
import static com.hailiang.constant.Constant.TWO;
import static com.hailiang.constant.SpeedMsgTemplateConstant.SYSTEM_TEMPLATE;

/**
 * 极速点评配置后台设置
 *
 * @Description: 极速点评配置后台设置
 * @Author: Jovi
 * @Date: Created in 2024-01-29
 * @Version: 1.6.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpeedConfigSetUpShowServiceImpl implements SpeedConfigSetUpShowService {

    //明细控件下的控件是否是计分控件
    private static final String IS_SCORE = "isScore";

    private static final String OPTIONS = "options";

    private static final Integer SCORE = 1;

    private static final Integer NORMAL = 2;


    private final SpeedTargetGroupRTargetManager groupRTargetManager;

    private final SpeedTargetGroupManager groupManager;

    private final TargetTemplateDao templateDao;

    private final TargetGroupManager targetGroupManager;
    private final TargetGroupService targetGroupService;
    private final TargetManager targetManager;
    private final SpeedConfigService speedConfigService;

    @Override
    public List<SpeedConfigGroupResponse> listGroups(Boolean isTemplate) {

        List<SpeedTargetGroupPO> speedTargetGroupPOS;
        if (isTemplate) {
            speedTargetGroupPOS = groupManager.listByCampusId(SYSTEM_TEMPLATE);
        } else {
            speedTargetGroupPOS = groupManager.listByCampusId(WebUtil.getCampusId());
        }

        if (CollectionUtils.isEmpty(speedTargetGroupPOS)) {
            return Collections.emptyList();
        }

        return speedTargetGroupPOS.stream()
                .map(groupPO -> {
                    SpeedConfigGroupResponse response = new SpeedConfigGroupResponse();
                    response.setSpeedTargetGroupName(groupPO.getGroupName());
                    response.setSpeedTargetGroupId(groupPO.getId());
                    response.setSpeedTargetGroupSortIndex(groupPO.getSortIndex());
                    return response;
                })
                .sorted(Comparator.comparing(SpeedConfigGroupResponse::getSpeedTargetGroupSortIndex))
                .collect(Collectors.toList());
    }

    @Override
    public SpeedGroupOptionsResponse tree(Long speedGroupId, Boolean isTemplate) {

        SpeedGroupOptionsResponse response = new SpeedGroupOptionsResponse();

        List<TargetAndGroupDTO> targetAndGroupDTOS;

        if (isTemplate) {
            targetAndGroupDTOS = targetGroupManager.listSysTargetAndSysTargetGroups();
        } else {
            targetAndGroupDTOS = targetGroupManager.listTargetAndTargetGroups(WebUtil.getCampusId(), WebUtil.getSchoolId());
        }

        if (CollectionUtils.isEmpty(targetAndGroupDTOS)) {
            log.warn("对应校区下无指标");
            return response;
        }

        //该校区该极速点评分组下的所有指标ID集合
        List<Long> targetIds = targetAndGroupDTOS
                .stream()
                .map(TargetAndGroupDTO::getTargetId)
                .collect(Collectors.toList());

        Map<Long, List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave>> targetIdOptionMap =
                mapTargetOptionByTargetIds(targetIds);

        Map<Long, SpeedGroupTreeResponse> moduleMap = new HashMap<>();
        Map<Long, SpeedGroupTreeResponse> groupMap = new HashMap<>();


        for (TargetAndGroupDTO targetAndGroupDTO : targetAndGroupDTOS) {
            buildTree(targetAndGroupDTO, moduleMap, groupMap, targetIdOptionMap);
        }


        List<SpeedGroupSelectedOptionsResponse> responses = listSelectedOptions(speedGroupId, isTemplate);

        Comparator<SpeedGroupSelectedOptionsResponse> sortComparator = Comparator.comparingInt(SpeedGroupSelectedOptionsResponse::getSortIndex);
        responses.sort(sortComparator);

        response.setSelectedOptionsResponses(responses);
        response.setTreeResponses(new ArrayList<>(moduleMap.values()));
        // 进行五育和其他育排序，其他育放最后
        response.getTreeResponses().sort((o1, o2) -> {
            int moduleCode1 = Integer.parseInt(o1.getInfoId());
            int moduleCode2 = Integer.parseInt(o2.getInfoId());

            // 如果o1的moduleCode为0，而o2的moduleCode不为0，则o1应该排在o2之后
            if (moduleCode1 == 0 && moduleCode2 != 0) {
                return 1;
            }
            // 如果o1的moduleCode不为0，而o2的moduleCode为0，则o1应该排在o2之前
            else if (moduleCode1 != 0 && moduleCode2 == 0) {
                return -1;
            }
            // 如果两个对象的moduleCode都为0或都不为0，则保持它们的相对顺序不变（或者可以根据需要进一步比较）
            else {
                return 0;
            }
        });

        return response;
    }

    /**
     * 极速点评分组及详情
     */
    @Override
    public List<SpeedTargetGroupDetailResponse> listGroupsAndDetails(SpeedGroupDetailRequest request) {
        Assert.notBlank(request.getCampusId(), BizExceptionEnum.CAMPUS_ID_NOT_NULL.getMessage());
        Assert.notNull(request.getStaffId(), BizExceptionEnum.STAFF_ID_NOT_NULL.getMessage());
        Assert.notBlank(request.getSchoolId(), BizExceptionEnum.SCHOOL_ID_NOT_NULL.getMessage());

        //获取校区下所有极速点评分组
        SpeedConfigResponse speedConfigResponse = speedConfigService
                .getOneByCampusIdAndStaffId(request.getCampusId(), Convert.toStr(request.getStaffId()));

        List<SpeedTargetGroupPO> speedTargetGroupPOS = this.toSpeedTargetGroups(speedConfigResponse);

        //极速点评分组
        List<SpeedTargetGroupDetailResponse> responses = this.buildSpeedTargetGroups(speedTargetGroupPOS);

        //获取分组详情
        return this.buildSpeedTargetGroupsAndDetails(request, speedTargetGroupPOS, responses);
    }

    private List<SpeedTargetGroupPO> toSpeedTargetGroups(SpeedConfigResponse speedConfigResponse) {
        if (BeanUtil.isEmpty(speedConfigResponse) || CollUtil.isEmpty(speedConfigResponse.getGroupResponses())) {
            log.warn("【极速点评分组及详情】-【极速点评配置为空】,直接返回, speedConfigResponse：【{}】",
                    JSONUtil.toJsonStr(speedConfigResponse));
            return Collections.emptyList();
        }
        List<GroupResponse> groupResponses = speedConfigResponse.getGroupResponses();
        List<SpeedTargetGroupPO> speedTargetGroupPOS = new ArrayList<>();
        for (GroupResponse groupResponse : groupResponses) {
            SpeedTargetGroupPO speedTargetGroupPO = new SpeedTargetGroupPO();
            speedTargetGroupPO.setId(groupResponse.getGroupId());
            speedTargetGroupPO.setSortIndex(groupResponse.getSortIndex());
            speedTargetGroupPO.setGroupName(groupResponse.getGroupName());
            speedTargetGroupPOS.add(speedTargetGroupPO);
        }
        return speedTargetGroupPOS;
    }

    @Override
    public List<SpeedTargetGroupDetailResponse> listGroupsAndDetailsByTargetId(SpeedGroupDetailRemoteRequest request) {
        Assert.notBlank(request.getCampusId(), BizExceptionEnum.CAMPUS_ID_NOT_NULL.getMessage());
        Assert.notNull(request.getStaffId(), BizExceptionEnum.STAFF_ID_NOT_NULL.getMessage());

        //获取校区下所有极速点评分组
        List<SpeedTargetGroupPO> speedTargetGroupPOS = groupManager.listByCampusIdAndGroupId(request.getCampusId(), request.getTagId());
        if (CollectionUtils.isEmpty(speedTargetGroupPOS)) {
            log.warn("【极速点评分组及详情】，对应校区下无极速点评分组，campusId:{}", request.getCampusId());
            return Collections.emptyList();
        }

        // 极速点评分组
        List<SpeedTargetGroupDetailResponse> responses = this.buildSpeedTargetGroups(speedTargetGroupPOS);
        // 获取分组详情
        List<SpeedTargetGroupDetailResponse> speedTargetGroupDetailResponses = this.buildSpeedTargetGroupsAndDetailsByTargetId(request, request.getTargetIds(), responses);
        // 过滤掉没有查到指标的分组
        speedTargetGroupDetailResponses =
                speedTargetGroupDetailResponses.stream()
                        .filter(item -> CollUtil.isNotEmpty(item.getSelectedOptionsResponses()))
                        .collect(Collectors.toList());

        log.info("根据指标查询点评项明细,request:{},speedTargetGroupDetailResponses:{}", JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(speedTargetGroupDetailResponses));
        // 如果只查指标
        if (CollUtil.isEmpty(request.getInfoIds())) {
            return speedTargetGroupDetailResponses;
        }

        // 如果查的是点评项则进行过滤
        List<SpeedTargetGroupDetailResponse> result = new ArrayList<>();
        for (SpeedTargetGroupDetailResponse speedTargetGroupDetailResponse : speedTargetGroupDetailResponses) {
            List<SpeedGroupSelectedOptionsResponse> selectedOptionsResponses = new ArrayList<>();
            if (CollUtil.isEmpty(speedTargetGroupDetailResponse.getSelectedOptionsResponses())) {
                continue;
            }
            for (SpeedGroupSelectedOptionsResponse selectedOptionsResponse : speedTargetGroupDetailResponse.getSelectedOptionsResponses()) {
                if (request.getInfoIds().contains(selectedOptionsResponse.getInfoId())) {
                    selectedOptionsResponses.add(selectedOptionsResponse);
                }
            }
            speedTargetGroupDetailResponse.setSelectedOptionsResponses(selectedOptionsResponses);
            if (CollUtil.isNotEmpty(speedTargetGroupDetailResponse.getSelectedOptionsResponses())) {
                result.add(speedTargetGroupDetailResponse);
            }
        }
        return result;
    }

    /**
     * 极速点评详情
     */
    private List<SpeedTargetGroupDetailResponse> buildSpeedTargetGroupsAndDetails(SpeedGroupDetailRequest request,
                                                                                  List<SpeedTargetGroupPO> speedTargetGroupPOS,
                                                                                  List<SpeedTargetGroupDetailResponse> responses) {

        Assert.notBlank(request.getCampusId(), BizExceptionEnum.CAMPUS_ID_NOT_NULL.getMessage());
        Assert.notNull(request.getStaffId(), BizExceptionEnum.STAFF_ID_NOT_NULL.getMessage());
        Assert.notBlank(request.getSchoolId(), BizExceptionEnum.SCHOOL_ID_NOT_NULL.getMessage());
        Assert.notEmpty(responses, "极速点评分组及详情不能为空");
        Assert.notEmpty(speedTargetGroupPOS, "极速点评分组不能为空");

        List<Long> groupIds = speedTargetGroupPOS.stream()
                .map(SpeedTargetGroupPO::getId).collect(Collectors.toList());

        //分组下所有选中的指标和选项
        List<RemoteGroupSelectedOptionsDTO> existsOptionsDTOS = groupRTargetManager
                .listRemoteSelectedOptions(groupIds, request.getCampusId());

        //填充分组详情
        return this.fillSpeedTargetGroupDetailResponses(request, responses, existsOptionsDTOS);
    }

    private List<SpeedTargetGroupDetailResponse> fillSpeedTargetGroupDetailResponses(SpeedGroupDetailRequest request,
                                                                                     List<SpeedTargetGroupDetailResponse> responses,
                                                                                     List<RemoteGroupSelectedOptionsDTO> existsOptionsDTOS) {

        if (CollectionUtils.isEmpty(existsOptionsDTOS)) {
            log.warn("【极速点评分组及详情】入参为空，existsOptionsDTOS:{}", JSONUtil.toJsonStr(existsOptionsDTOS));
            return responses;
        }
        Map<Long, List<RemoteGroupSelectedOptionsDTO>> groupIdMap = CollStreamUtil
                .groupByKey(existsOptionsDTOS, RemoteGroupSelectedOptionsDTO::getGroupId);

        List<Long> existsTargetIds = existsOptionsDTOS
                .stream()
                .map(SpeedGroupSelectedOptionsDTO::getTargetId)
                .distinct()
                .collect(Collectors.toList());

        //获取该老师能点评的指标
        List<Long> finalTargetIds = this.listFinalTargetIds(request, existsTargetIds);
        if (CollectionUtils.isEmpty(finalTargetIds)) {
            log.warn("【极速点评分组及详情】对应校区下无该老师能点评的指标，request:{}，existsTargetIds:{}",
                    JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(existsTargetIds));
            return responses;
        }
        //根据已选指标ID集合查询对应指标ID集合下所有的选项
        List<TargetTemplate> targetTemplates = templateDao.listByTargetIds(finalTargetIds);
        if (CollectionUtils.isEmpty(targetTemplates)) {
            log.warn("【极速点评分组及详情】，对应应校区下无指标模板，existsTargetIds:{}", JSONUtil.toJsonStr(finalTargetIds));
            return responses;
        }

        this.fillGroupWithDetails(responses, groupIdMap, targetTemplates);

        //指标-五育映射关系
        this.fillDetailsWithModuleCode(responses, finalTargetIds);

        return responses;
    }

    /**
     * 指标-五育映射关系
     */
    private void fillDetailsWithModuleCode(List<SpeedTargetGroupDetailResponse> responses, List<Long> finalTargetIds) {
        if (CollUtil.isEmpty(finalTargetIds)) {
            log.warn("【指标-五育映射关系】 入参为空，finalTargetIds:{}", JSONUtil.toJsonStr(finalTargetIds));
            return;
        }
        Map<Long, Integer> targetIdToModuleCode = this.buildTargetIdtoModuleMap(finalTargetIds);
        if (CollUtil.isEmpty(targetIdToModuleCode)) {
            log.warn("【指标-五育映射关系】 为空，finalTargetIds:{}", JSONUtil.toJsonStr(finalTargetIds));
            return;
        }
        for (SpeedTargetGroupDetailResponse response : responses) {
            List<SpeedGroupSelectedOptionsResponse> selectedOptionsResponses = response.getSelectedOptionsResponses();
            if (CollUtil.isEmpty(selectedOptionsResponses)) {
                continue;
            }
            for (SpeedGroupSelectedOptionsResponse selectedOptionsResponse : selectedOptionsResponses) {
                if (BeanUtil.isEmpty(selectedOptionsResponse)) {
                    continue;
                }
                Integer moduleCode = targetIdToModuleCode.getOrDefault(selectedOptionsResponse.getTargetId(), 0);
                selectedOptionsResponse.setModuleCode(moduleCode);
            }
        }
    }

    /**
     * 获取指标-五育映射关系
     *
     * @param finalTargetIds 指标 id
     */
    @Override
    public Map<Long, Integer> buildTargetIdtoModuleMap(List<Long> finalTargetIds) {
        if (CollUtil.isEmpty(finalTargetIds)) {
            log.warn("【获取指标-五育映射关系】，指标 id 为空，finalTargetIds:{}", JSONUtil.toJsonStr(finalTargetIds));
            return Collections.emptyMap();
        }
        //根据指标ID集合查询指标对应的五育模块
        List<Target> targets = targetManager.listByIds(finalTargetIds);
        if (CollUtil.isEmpty(targets)) {
            log.warn("【极速点评分组及详情】，根据指标 id 查询指标信息为空，finalTargetIds:{}", JSONUtil.toJsonStr(finalTargetIds));
            return Collections.emptyMap();
        }
        //指标对应的分组
        List<Long> targetGroupIds = targets.stream().map(Target::getGroupId).distinct().collect(Collectors.toList());
        List<TargetGroup> targetGroups = targetGroupManager.listByIds(targetGroupIds);
        if (CollUtil.isEmpty(targetGroups)) {
            log.warn("【极速点评分组及详情】，指标对应的分组为空，targetGroupIds:{}", JSONUtil.toJsonStr(targetGroupIds));
            return Collections.emptyMap();
        }
        // 创建一个映射，将groupId映射到其对应的moduleCode
        Map<Long, Integer> groupIdToModuleCode = targetGroups.stream()
                .collect(Collectors.toMap(TargetGroup::getId, TargetGroup::getModuleCode));

        // 创建一个映射，将targetId映射到其对应的moduleCode
        return targets.stream()
                .collect(Collectors.toMap(
                        Target::getId,
                        target -> groupIdToModuleCode.getOrDefault(target.getGroupId(), 0)
                ));
    }

    private List<SpeedTargetGroupDetailResponse>
    buildSpeedTargetGroupsAndDetailsByTargetId(SpeedGroupDetailRequest request,
                                               List<Long> targetIds,
                                               List<SpeedTargetGroupDetailResponse> responses) {
        Assert.notBlank(request.getCampusId(), "校区ID不能为空");
        Assert.notNull(request.getStaffId(), "教职工ID不能为空");
        Assert.notEmpty(responses, "极速点评分组及详情不能为空");
        Assert.notEmpty(targetIds, "指标ID不能为空");
        List<RemoteGroupSelectedOptionsDTO> remoteGroupSelectedOptionsDTOS =
                groupRTargetManager.listRemoteSelectedOptionsByTargetId(targetIds, request.getCampusId());

        return this.fillSpeedTargetGroupDetailResponses(request, responses, remoteGroupSelectedOptionsDTOS);

    }

    /**
     * 获取老师能点评的指标
     */
    private List<Long> listFinalTargetIds(SpeedGroupDetailRequest request, List<Long> existsTargetIds) {
        if (CollUtil.isEmpty(existsTargetIds) || BeanUtil.hasNullField(request)) {
            log.warn("极速点评分组及详情-获取老师能点评的指标为空,直接返回分组, request:{},existsTargetIds:{}",
                    JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(existsTargetIds));
            return Collections.emptyList();
        }
        List<Long> finalTargetIds = new ArrayList<>();
        //该老师能点评的指标
        List<ListAllEvaluateTargetVOModule> listAllEvaluateTargetVOModules =
                targetGroupService.queryStaffInfoModules(request.getStaffId(), request.getCampusId(), request.getSchoolId());
        //该老师能点评的指标 id
        List<String> teacherTargetIds = this.listTeacherTargetIds(listAllEvaluateTargetVOModules);
        if (CollUtil.isEmpty(teacherTargetIds)) {
            log.warn("[极速点评分组及详情]-[该老师能点评的指标为空],直接返回分组, staffId:[{}]", request.getStaffId());
            return finalTargetIds;
        }

        //分组下老师可点评的指标 ids
        finalTargetIds = new ArrayList<>(CollUtil.intersection(existsTargetIds, Convert.toList(Long.class,
                teacherTargetIds)));
        if (CollUtil.isEmpty(finalTargetIds)) {
            log.warn("[极速点评分组及详情]-[该老师能点评的指标为空],直接返回分组, staffId:[{}]，existsTargetIds:{}，teacherTargetIds:{}",
                    request.getStaffId(), existsTargetIds, teacherTargetIds);
            return finalTargetIds;
        }
        return finalTargetIds;
    }

    private List<String> listTeacherTargetIds(List<ListAllEvaluateTargetVOModule> listAllEvaluateTargetVOModules) {
        if (CollUtil.isEmpty(listAllEvaluateTargetVOModules)) {
            return Collections.emptyList();
        }
        return listAllEvaluateTargetVOModules.stream()
                .flatMap(module -> Optional.ofNullable(module)
                        .map(m -> m.getTargetGroupList().stream())
                        .orElse(Stream.empty()))
                .flatMap(group -> Optional.ofNullable(group)
                        .map(g -> g.getTargetList().stream())
                        .orElse(Stream.empty()))
                .filter(BeanUtil::isNotEmpty)
                .map(ListAllEvaluateTargetVOInnerTarget::getTargetId)
                .collect(Collectors.toList());
    }

    /**
     * 填充分组下的详情
     */
    private void fillGroupWithDetails(List<SpeedTargetGroupDetailResponse> responses,
                                      Map<Long, List<RemoteGroupSelectedOptionsDTO>> groupIdMap,
                                      List<TargetTemplate> targetTemplates) {
        if (CollectionUtils.isEmpty(responses) || CollectionUtils.isEmpty(targetTemplates) || CollectionUtils.isEmpty(groupIdMap)) {
            log.warn("【填充分组下的详情】，参数为空 ，直接返回，responses:{}, groupIdMap:{}, targetTemplates:{} ",
                    JSONUtil.toJsonStr(responses), JSONUtil.toJsonStr(groupIdMap), JSONUtil.toJsonStr(targetTemplates));
            return;
        }

        for (Map.Entry<Long, List<RemoteGroupSelectedOptionsDTO>> entry : groupIdMap.entrySet()) {
            List<RemoteGroupSelectedOptionsDTO> options = entry.getValue();
            Long groupId = entry.getKey();

            Optional<SpeedTargetGroupDetailResponse> first = responses
                    .stream().filter(response -> response.getSpeedTargetGroupId().equals(groupId)).findFirst();
            if (!first.isPresent()) {
                continue;
            }

            List<Long> groupExistsTargetIds = options
                    .stream()
                    .map(SpeedGroupSelectedOptionsDTO::getTargetId)
                    .distinct()
                    .collect(Collectors.toList());

            List<TargetTemplate> templates = targetTemplates
                    .stream()
                    .filter(targetTemplate -> groupExistsTargetIds.contains(targetTemplate.getTargetId()))
                    .collect(Collectors.toList());

            List<String> existsOptionIds = options
                    .stream()
                    .map(SpeedGroupSelectedOptionsDTO::getOptionId)
                    .collect(Collectors.toList());

            Map<String, SpeedGroupSelectedOptionsDTO> existsSpeedGroupSelectedOptionIdsMap = options.stream()
                    .collect(Collectors.toMap(dto -> dto.getTargetId() + "-" + dto.getOptionId(), dto -> dto));


            List<SpeedGroupSelectedOptionsResponse> selectResponses = new ArrayList<>();
            first.get().setSelectedOptionsResponses(selectResponses);
            for (TargetTemplate targetTemplate : templates) {
                this.dealOneTargetTemplateInfo(targetTemplate, existsOptionIds, existsSpeedGroupSelectedOptionIdsMap, selectResponses);
                //排序
                selectResponses.sort(Comparator.comparing(SpeedGroupSelectedOptionsResponse::getSortIndex));
            }
        }

        responses.stream()
                .filter(response -> CollUtil.isEmpty(response.getSelectedOptionsResponses()))
                .forEach(response -> response.setSelectedOptionsResponses(Collections.emptyList()));

    }

    /**
     * 极速点评分组
     */
    private List<SpeedTargetGroupDetailResponse> buildSpeedTargetGroups(List<SpeedTargetGroupPO> speedTargetGroupPOS) {
        if (CollectionUtils.isEmpty(speedTargetGroupPOS)) {
            log.warn("【极速点评分组】，入参为空，直接返回，speedTargetGroupPOS:{}", JSONUtil.toJsonStr(speedTargetGroupPOS));
            return Collections.emptyList();
        }
        return speedTargetGroupPOS.stream()
                .map(groupPO -> {
                    SpeedTargetGroupDetailResponse response = new SpeedTargetGroupDetailResponse();
                    response.setSpeedTargetGroupName(groupPO.getGroupName());
                    response.setSpeedTargetGroupId(groupPO.getId());
                    response.setSpeedTargetGroupSortIndex(groupPO.getSortIndex());
                    return response;
                })
                .sorted(Comparator.comparing(SpeedTargetGroupDetailResponse::getSpeedTargetGroupSortIndex))
                .collect(Collectors.toList());
    }

    private List<SpeedGroupSelectedOptionsResponse> listSelectedOptions(Long speedGroupId, Boolean isTemplate) {


        List<SpeedGroupSelectedOptionsDTO> existsOptionsDTOS;

        if (isTemplate) {
            existsOptionsDTOS = groupRTargetManager
                    .listSelectedOptions(speedGroupId, SYSTEM_TEMPLATE);
        } else {
            existsOptionsDTOS = groupRTargetManager
                    .listSelectedOptions(speedGroupId, WebUtil.getCampusId());
        }


        if (CollectionUtils.isEmpty(existsOptionsDTOS)) {
            log.warn("对应校区下无已存在极速点评指标");
            return Collections.emptyList();
        }

        List<Long> existsTargetIds = existsOptionsDTOS
                .stream()
                .distinct()
                .map(SpeedGroupSelectedOptionsDTO::getTargetId)
                .collect(Collectors.toList());

        List<String> existsOptionIds = existsOptionsDTOS
                .stream()
                .map(SpeedGroupSelectedOptionsDTO::getOptionId)
                .collect(Collectors.toList());

        Map<String, SpeedGroupSelectedOptionsDTO> existsSpeedGroupSelectedOptionIdsMap = existsOptionsDTOS.stream()
                .collect(Collectors.toMap(dto -> dto.getTargetId() + "-" + dto.getOptionId(), dto -> dto));


        //根据已选指标ID集合查询对应指标ID集合下所有的选项
        List<TargetTemplate> targetTemplates = templateDao.listByTargetIds(existsTargetIds);
        if (CollectionUtils.isEmpty(targetTemplates)) {
            log.warn("对应已存在指标ID下无指标项，指标ID集合为：「{}」", existsOptionIds);
            return Collections.emptyList();
        }

        List<SpeedGroupSelectedOptionsResponse> responses = new ArrayList<>();

        for (TargetTemplate targetTemplate : targetTemplates) {
            dealOneTargetTemplateInfo(targetTemplate, existsOptionIds, existsSpeedGroupSelectedOptionIdsMap, responses);
        }


        return responses;
    }

    /**
     * 处理一个指标下的模板信息
     *
     * @param targetTemplate                       当前TargetID下的模板信息
     * @param existsOptionIds                      已存在极速点评分组表中选项ID
     * @param existsSpeedGroupSelectedOptionIdsMap 以ID为KEY，已存在极速点评选项表中指标项信息
     * @param responses
     */
    private void dealOneTargetTemplateInfo(TargetTemplate targetTemplate, List<String> existsOptionIds, Map<String, SpeedGroupSelectedOptionsDTO> existsSpeedGroupSelectedOptionIdsMap, List<SpeedGroupSelectedOptionsResponse> responses) {

        List<TemplateInfoSaveDTO> templateInfoList = targetTemplate.getTemplateInfoList();

        Long targetId = targetTemplate.getTargetId();

        if (CollectionUtils.isEmpty(templateInfoList)) {
            return;
        }

        //指标项处理
        for (TemplateInfoSaveDTO templateInfoSaveDTO : templateInfoList) {
            dealOneTemplateOptionsInfo(existsSpeedGroupSelectedOptionIdsMap, existsOptionIds, responses, templateInfoSaveDTO, targetId);
        }
    }


    /**
     * 处理一个模板下的指标项信息
     *
     * @param existsSpeedGroupSelectedOptionIdsMap
     * @param existsOptionIds
     * @param responses
     * @param templateInfoSaveDTO                  当前模板下的每一个指标项，这个指标项有三种可能性需要做处理：明细，分值控件，单选多选计分选项
     */
    private void dealOneTemplateOptionsInfo(Map<String, SpeedGroupSelectedOptionsDTO> existsSpeedGroupSelectedOptionIdsMap,
                                            List<String> existsOptionIds,
                                            List<SpeedGroupSelectedOptionsResponse> responses,
                                            TemplateInfoSaveDTO templateInfoSaveDTO,
                                            Long targetIdKey) {

        if (detail(targetIdKey, existsOptionIds, templateInfoSaveDTO, responses, existsSpeedGroupSelectedOptionIdsMap)) {
            return;
        }

        if (scoringControl(targetIdKey, existsOptionIds, templateInfoSaveDTO, responses, existsSpeedGroupSelectedOptionIdsMap)) {
            return;
        }

        radioOrCheckBox(targetIdKey, existsOptionIds, templateInfoSaveDTO, responses, existsSpeedGroupSelectedOptionIdsMap);
    }

    /**
     * 明细控件处理
     *
     * @param existsOptionIds
     * @param responses
     * @param templateInfoSaveDTO
     * @param existsSpeedGroupSelectedOptionIdsMap
     * @return
     */
    private boolean detail(
            Long targetIdKey,
            List<String> existsOptionIds,
            TemplateInfoSaveDTO templateInfoSaveDTO,
            List<SpeedGroupSelectedOptionsResponse> responses,
            Map<String, SpeedGroupSelectedOptionsDTO> existsSpeedGroupSelectedOptionIdsMap) {
        if (!SubmitInfoTypeEnum.DETAIL.getText().equals(templateInfoSaveDTO.getType())) {
            return false;
        }
        List<LinkedHashMap> list = templateInfoSaveDTO.getList();
        for (LinkedHashMap linkedHashMap : list) {
            dealOneOptionFromDetail(targetIdKey, existsOptionIds, responses, existsSpeedGroupSelectedOptionIdsMap, linkedHashMap);
        }
        return true;
    }


    /**
     * 处理明细中的一个选项
     *
     * @param existsOptionIds
     * @param responses
     * @param existsSpeedGroupSelectedOptionIdsMap
     * @param linkedHashMap
     */
    private void dealOneOptionFromDetail(Long targetIdKey, List<String> existsOptionIds, List<SpeedGroupSelectedOptionsResponse> responses, Map<String, SpeedGroupSelectedOptionsDTO> existsSpeedGroupSelectedOptionIdsMap, LinkedHashMap linkedHashMap) {
        //明细下的控件不是计分控件直接跳过
        if (Convert.toBool(linkedHashMap.get(IS_SCORE)) == null || !Convert.toBool(linkedHashMap.get(IS_SCORE))) {
            return;
        }

        Object innerOption = linkedHashMap.get(OPTIONS);
        String jsonStr = JSON.toJSONString(innerOption);
        TemplateInfoSaveDTO.InnerSubmitOptionInfoSave option = JSON.parseObject(jsonStr, TemplateInfoSaveDTO.InnerSubmitOptionInfoSave.class);
        List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> options = option.getOptions();
        for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave innerSubmitOptionInfoSubSave : options) {
            dealOneOption(targetIdKey, existsOptionIds, responses, existsSpeedGroupSelectedOptionIdsMap, innerSubmitOptionInfoSubSave);
        }
    }


    /**
     * 分值控件处理
     *
     * @param existsOptionIds
     * @param templateInfoSaveDTO
     * @param responses
     * @param existsSpeedGroupSelectedOptionIdsMap
     * @return
     */
    private boolean scoringControl(Long targetIdKey,
                                   List<String> existsOptionIds,
                                   TemplateInfoSaveDTO templateInfoSaveDTO,
                                   List<SpeedGroupSelectedOptionsResponse> responses,
                                   Map<String, SpeedGroupSelectedOptionsDTO> existsSpeedGroupSelectedOptionIdsMap) {
        //分值控件
        String key = templateInfoSaveDTO.getKey();
        if (!templateInfoSaveDTO.getType().equals(SubmitInfoTypeEnum.SCORE.getText()) || !existsOptionIds.contains(key)) {
            return false;
        }

        SpeedGroupSelectedOptionsDTO existsSpeedGroupSelectedOptionsDTO = existsSpeedGroupSelectedOptionIdsMap.get(targetIdKey + "-" + key);

        if (existsSpeedGroupSelectedOptionsDTO == null) {
            return false;
        }


        SpeedGroupSelectedOptionsResponse response = new SpeedGroupSelectedOptionsResponse();
        Long targetId = existsSpeedGroupSelectedOptionsDTO.getTargetId();
        response.setInfoId(key);
        response.setTargetId(targetId);
        response.setScoreControlType(templateInfoSaveDTO.getScoreType());

        if (templateInfoSaveDTO.getScoreType() == 1) {
            response.setValue(templateInfoSaveDTO.getScore());
        } else {
            response.setValue(templateInfoSaveDTO.getScore().negate());
        }

        response.setNodeName(existsSpeedGroupSelectedOptionsDTO.getTargetName());

        response.setNodeType(ONE);

        response.setAppType(Arrays.stream(existsSpeedGroupSelectedOptionsDTO.getAppType().split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList()));
        response.setSortIndex(existsSpeedGroupSelectedOptionsDTO.getSortIndex());
        responses.add(response);
        return true;
    }


    /**
     * 单选或多选计分控件处理
     *
     * @param existsOptionIds
     * @param templateInfoSaveDTO
     * @param responses
     * @param existsSpeedGroupSelectedOptionIdsMap
     */
    private void radioOrCheckBox(Long targetIdKey,
                                 List<String> existsOptionIds,
                                 TemplateInfoSaveDTO templateInfoSaveDTO,
                                 List<SpeedGroupSelectedOptionsResponse> responses,
                                 Map<String, SpeedGroupSelectedOptionsDTO> existsSpeedGroupSelectedOptionIdsMap) {
        //原来是计分控件，后改为不是计分控件
        if (templateInfoSaveDTO.getIsScore() == null
                || !templateInfoSaveDTO.getIsScore()) {
            return;
        }

        //是单选或者是多选控件
        if (templateInfoSaveDTO.getType().equals(SubmitInfoTypeEnum.SINGLE_CHECK.getText())
                || templateInfoSaveDTO.getType().equals(SubmitInfoTypeEnum.MULTI_CHECK.getText())) {

            TemplateInfoSaveDTO.InnerSubmitOptionInfoSave option = templateInfoSaveDTO.getOptions();

            List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> options = option.getOptions();

            if (CollectionUtils.isEmpty(options)) {
                return;
            }

            for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave innerSubmitOptionInfoSubSave : options) {
                dealOneOption(targetIdKey, existsOptionIds, responses, existsSpeedGroupSelectedOptionIdsMap, innerSubmitOptionInfoSubSave);
            }
        }
    }

    /**
     * 处理单选或多选下的一个选项
     *
     * @param existsOptionIds
     * @param responses
     * @param existsSpeedGroupSelectedOptionIdsMap
     * @param innerSubmitOptionInfoSubSave
     */
    private void dealOneOption(Long targetIdKey,
                               List<String> existsOptionIds,
                               List<SpeedGroupSelectedOptionsResponse> responses,
                               Map<String, SpeedGroupSelectedOptionsDTO> existsSpeedGroupSelectedOptionIdsMap,
                               TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave innerSubmitOptionInfoSubSave) {
        String key = innerSubmitOptionInfoSubSave.getKey();
        if (!existsOptionIds.contains(key)) {
            return;
        }

        SpeedGroupSelectedOptionsDTO existsSpeedGroupSelectedOptionsDTO = existsSpeedGroupSelectedOptionIdsMap.get(targetIdKey + "-" + key);

        if (existsSpeedGroupSelectedOptionsDTO == null) {
            return;
        }

        SpeedGroupSelectedOptionsResponse response = new SpeedGroupSelectedOptionsResponse();
        response.setInfoId(key);
        response.setTargetId(existsSpeedGroupSelectedOptionsDTO.getTargetId());
        response.setNodeName(innerSubmitOptionInfoSubSave.getLabel());
        response.setNodeType(TWO);
        response.setValue(innerSubmitOptionInfoSubSave.getValue());
        response.setAppType(Arrays.stream(existsSpeedGroupSelectedOptionsDTO.getAppType().split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList()));
        response.setSortIndex(existsSpeedGroupSelectedOptionsDTO.getSortIndex());

        responses.add(response);
    }

    /**
     * 树状结构拼装
     *
     * @param targetAndGroupDTO
     * @param moduleMap
     * @param groupMap
     * @param targetIdOptionMap
     */
    private void buildTree(TargetAndGroupDTO targetAndGroupDTO,
                           Map<Long, SpeedGroupTreeResponse> moduleMap,
                           Map<Long, SpeedGroupTreeResponse> groupMap,
                           Map<Long, List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave>> targetIdOptionMap
    ) {
        Integer moduleCode = targetAndGroupDTO.getModuleCode();
        Long groupId = targetAndGroupDTO.getGroupId();
        Long targetId = targetAndGroupDTO.getTargetId();

        //组装五育
        SpeedGroupTreeResponse moduleResponse = moduleMap.get(moduleCode.longValue());
        if (moduleResponse == null) {
            moduleResponse = new SpeedGroupTreeResponse();
            moduleResponse.setInfoId(moduleCode.toString());
            moduleResponse.setNodeName(ModuleEnum.getModuleName(moduleCode));
            moduleResponse.setChildren(new ArrayList<>());
            moduleMap.put(moduleCode.longValue(), moduleResponse);
        }

        //组装五育下的分组
        SpeedGroupTreeResponse groupResponse = groupMap.get(groupId);
        if (groupResponse == null) {
            groupResponse = new SpeedGroupTreeResponse();
            groupResponse.setInfoId(targetAndGroupDTO.getGroupId().toString());
            groupResponse.setNodeName(targetAndGroupDTO.getGroupName());
            groupResponse.setChildren(new ArrayList<>());
            groupMap.put(groupId, groupResponse);
            moduleResponse.getChildren().add(groupResponse);
        }

        //组装五育下分组的指标和指标项
        buildLeafNode(targetAndGroupDTO, targetIdOptionMap, targetId, groupResponse);

        //分组下没指标或者指标项的，移除该分组
        if (CollectionUtils.isEmpty(groupResponse.getChildren())) {
            moduleResponse.getChildren().remove(groupResponse);
            groupMap.remove(groupId);
        }

        //五育下没分组的，移除该五育
        if (CollectionUtils.isEmpty(moduleResponse.getChildren())) {
            moduleMap.remove(moduleCode.longValue());
        }
    }

    /**
     * 叶子结点拼接
     *
     * @param targetAndGroupDTO
     * @param targetIdOptionMap
     * @param targetId
     * @param groupResponse
     */
    private void buildLeafNode(TargetAndGroupDTO targetAndGroupDTO,
                               Map<Long, List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave>> targetIdOptionMap,
                               Long targetId,
                               SpeedGroupTreeResponse groupResponse) {
        SpeedGroupTreeResponse targetResponse = new SpeedGroupTreeResponse();

        targetResponse.setInfoId(targetId.toString());
        targetResponse.setTargetId(targetId);
        targetResponse.setNodeName(targetAndGroupDTO.getTargetName());
        //分值控件
        if (StringUtils.isNotEmpty(targetAndGroupDTO.getScoreControlName())) {
            List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> subSaves = targetIdOptionMap.get(targetId);
            //此处为了防止空指针，正常业务分值控件应该是不会为空，主键ID中必须包含“分值”才算真正的分值控件，否则就是由分值控件改为的其他控件
            if (!CollectionUtils.isEmpty(subSaves) && subSaves.get(0) != null) {
                String key = subSaves.get(0).getKey();
                if (key.contains("分值")) {
                    //记录该分值控件的MongoDB主键ID
                    targetResponse.setInfoId(StrUtil.subAfter(key, "分值", false));
                    //从target表中获取分值控件的类型
                    targetResponse.setScoreControlType(targetAndGroupDTO.getScoreControlType());
                    //从target表中获取分值控件的值
                    targetResponse.setValue(targetAndGroupDTO.getScore());
                    targetResponse.setNodeType(SCORE);

                    groupResponse.getChildren().add(targetResponse);
                }
            }
        }
        //不是分值控件
        else {
            List<SpeedGroupTreeResponse> responses = buildTargetOptionChildren(targetIdOptionMap, targetId);
            if (!CollectionUtils.isEmpty(responses)) {
                targetResponse.setChildren(responses);
                groupResponse.getChildren().add(targetResponse);
            }
        }
    }

    /**
     * 指标项子节点拼装
     *
     * @param targetIdOptionMap
     * @param targetId
     * @return
     */
    private List<SpeedGroupTreeResponse> buildTargetOptionChildren(
            Map<Long, List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave>> targetIdOptionMap,
            Long targetId
    ) {

        List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> subSaves = targetIdOptionMap.get(targetId);

        if (CollectionUtils.isEmpty(subSaves)) {
            return Collections.emptyList();
        }

        List<SpeedGroupTreeResponse> targetChildren = new ArrayList<>();

        for (TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave subSave : subSaves) {

            SpeedGroupTreeResponse response = new SpeedGroupTreeResponse();
            response.setInfoId(subSave.getKey());
            response.setNodeName(subSave.getLabel());
            response.setNodeType(NORMAL);
            response.setChildren(new ArrayList<>());
            response.setValue(subSave.getValue());
            response.setTargetId(targetId);

            targetChildren.add(response);
        }


        return targetChildren;
    }

    /**
     * 根据指标ID集合获取对应指标ID下的所有指标项集合
     *
     * @param targetIds
     * @return
     */
    private Map<Long, List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave>> mapTargetOptionByTargetIds(List<Long> targetIds) {

        List<TargetTemplate> targetTemplates = templateDao.listByTargetIds(targetIds);

        if (CollectionUtils.isEmpty(targetTemplates)) {
            log.warn("「对应指标ID集合下没有指标项信息，targetIds：{}」", targetIds);
            return new HashMap<>();
        }

        Map<Long, List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave>> innerOptionInfoMap = new HashMap<>();

        for (TargetTemplate targetTemplate : targetTemplates) {
            buildTargetOptionsInTree(targetTemplate, innerOptionInfoMap);
        }

        return innerOptionInfoMap;
    }


    /**
     * 组装树状结构中指标选项集合
     *
     * @param targetTemplate
     * @param innerSubmitOptionInfoSubSavesMap 以指标ID为KEY，该指标下所有的指标项集合
     */
    private void buildTargetOptionsInTree(TargetTemplate targetTemplate,
                                          Map<Long, List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave>> innerSubmitOptionInfoSubSavesMap) {

        Long targetId = targetTemplate.getTargetId();

        List<TemplateInfoSaveDTO> templateInfoList = targetTemplate.getTemplateInfoList();

        List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> optionInfoLists = new ArrayList<>();

        for (TemplateInfoSaveDTO templateInfoSaveDTO : templateInfoList) {

            if (detailTree(templateInfoSaveDTO, optionInfoLists)) {
                continue;
            }

            if (scoringControlTree(templateInfoSaveDTO, optionInfoLists)) {
                continue;
            }

            radioOrCheckBoxTree(templateInfoSaveDTO, optionInfoLists);
        }

        if (!CollectionUtils.isEmpty(optionInfoLists)) {
            innerSubmitOptionInfoSubSavesMap.put(targetId, optionInfoLists);
        }
    }

    /**
     * 分值控件处理
     *
     * @return
     */
    private boolean scoringControlTree(TemplateInfoSaveDTO templateInfoSaveDTO, List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> innerSubmitOptionInfoSubSaves) {
        //分值控件
        String key = templateInfoSaveDTO.getKey();
        if (!templateInfoSaveDTO.getType().equals(SubmitInfoTypeEnum.SCORE.getText())) {
            return false;
        }

        TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave infoSave = new TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave();
        /**
         * 当是分值控件的时候，只需记录分值控件的KEY也就是ID即可，具体控件的分值已target表中的数据为准
         * 此处加“分值”字符串是用来在后续获取时，判断历史数据是否是分值控件，因为历史存在从分值控件改为其他控件情况，
         * target表中的分值相关字段不会清除的BUG
         */
        infoSave.setKey("分值" + key);
        innerSubmitOptionInfoSubSaves.add(infoSave);

        return true;
    }

    /**
     * 组装树中明细
     *
     * @param templateInfoSaveDTO
     * @param innerSubmitOptionInfoSubSaves
     * @return
     */
    private boolean detailTree(TemplateInfoSaveDTO templateInfoSaveDTO, List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> innerSubmitOptionInfoSubSaves) {

        if (!SubmitInfoTypeEnum.DETAIL.getText().equals(templateInfoSaveDTO.getType())) {
            return false;
        }
        List<LinkedHashMap> list = templateInfoSaveDTO.getList();
        for (LinkedHashMap linkedHashMap : list) {
            innerSubmitOptionInfoSubSaves.addAll(dealOneOptionFromDetailTree(linkedHashMap));
        }
        return true;
    }

    /**
     * 组装某一个明细中的所有选项
     *
     * @param linkedHashMap
     * @return
     */
    private List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> dealOneOptionFromDetailTree(LinkedHashMap linkedHashMap) {
        //明细下的控件不是计分控件直接跳过
        if (Convert.toBool(linkedHashMap.get(IS_SCORE)) == null || !Convert.toBool(linkedHashMap.get(IS_SCORE))) {
            return new ArrayList<>();
        }

        Object innerOption = linkedHashMap.get(OPTIONS);
        String jsonStr = JSON.toJSONString(innerOption);
        TemplateInfoSaveDTO.InnerSubmitOptionInfoSave option = JSON.parseObject(jsonStr, TemplateInfoSaveDTO.InnerSubmitOptionInfoSave.class);
        return option.getOptions();
    }

    /**
     * 组装树中单选或者多选
     *
     * @param templateInfoSaveDTO
     * @param innerSubmitOptionInfoSubSaves
     */
    private void radioOrCheckBoxTree(TemplateInfoSaveDTO templateInfoSaveDTO, List<TemplateInfoSaveDTO.InnerSubmitOptionInfoSubSave> innerSubmitOptionInfoSubSaves) {
        //此处判断用来校验原来是计分控件，后改为不是计分控件，则不算在内
        if (templateInfoSaveDTO.getIsScore() == null
                || !templateInfoSaveDTO.getIsScore()) {
            return;
        }

        TemplateInfoSaveDTO.InnerSubmitOptionInfoSave option = templateInfoSaveDTO.getOptions();

        if (!CollectionUtils.isEmpty(option.getOptions())) {
            innerSubmitOptionInfoSubSaves.addAll(option.getOptions());
        }
    }


}