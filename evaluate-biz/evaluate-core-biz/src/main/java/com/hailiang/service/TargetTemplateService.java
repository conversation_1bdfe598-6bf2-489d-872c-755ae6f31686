package com.hailiang.service;

import com.hailiang.model.dto.save.TargetTemplateSaveDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.entity.mongo.TargetTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface TargetTemplateService {


    /**
     * 指标表单保存
     *
     * @param dto
     * @return
     */
    TargetTemplate save(TargetTemplateSaveDTO dto);

    /**
     * 指标表单保存
     *
     * @param targetTemplates
     * @return
     */
    Collection<TargetTemplate> bulkSave(List<TargetTemplate> targetTemplates);

    TargetTemplate get(String templateId);

    /**
     * 批量获取指标模板信息
     * @param targetIdList
     * @return
     */
    List<TargetTemplate> getByTargetIdList(List<Long> targetIdList);

    TargetTemplate getByTargetId(Long targetId);

    TargetTemplate getByTargetIdAndCreateTime(Long targetId, Date createTime);


    String getTemplateJsonStrByTargetId(Long targetId);

    boolean templateChangeOrNot(String templateId, List<TemplateInfoSaveDTO> templateInfoList);

    /**
     * 创建指标控件表单
     * @param dto
     * @return
     */
    TargetTemplate create(TargetTemplateSaveDTO dto);

    List<TargetTemplate> listByTemplateIds(Query query);

    void batchSave(List<TargetTemplate> targetTemplates);
}