package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.constant.Constant;
import com.hailiang.mapper.DailyNoticePushRuleMapper;
import com.hailiang.model.dto.save.ReportPushRuleSaveDTO;
import com.hailiang.model.entity.DailyNoticePushRule;
import com.hailiang.service.DailyNoticePushRuleService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报告推送规则表(ReportPushRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-13 11:30:31
 */
@Slf4j
@Service
public class DailyNoticePushRuleServiceImpl extends ServiceImpl<DailyNoticePushRuleMapper, DailyNoticePushRule> implements DailyNoticePushRuleService {

    /**
     * 保存每日点评推送规则
     * @param reportPushRuleSaveDTO 保存规则参数
     */
    @Override
    public void saveDailyPushRule(ReportPushRuleSaveDTO reportPushRuleSaveDTO) {
        log.info("saving DailyNoticePushRule for campusId: {} and schoolId: {} and reportPushRuleSaveDTO: {}",
                WebUtil.getCampusId(), WebUtil.getSchoolId(),reportPushRuleSaveDTO);

        List<DailyNoticePushRule> reportPushRuleList = super.list(new LambdaQueryWrapper<DailyNoticePushRule>()
                .eq(DailyNoticePushRule::getCampusId, WebUtil.getCampusId())
                .eq(DailyNoticePushRule::getDeleted, Constant.NO));

        if (CollUtil.isEmpty(reportPushRuleList)) {
            if (Constant.NO.equals(reportPushRuleSaveDTO.getCommentParentPushFlag())) {
                log.info("No DailyNoticePushRule to save for campusId: {}  and reportPushRuleSaveDTO: {}",
                        WebUtil.getCampusId(), reportPushRuleSaveDTO);
               return;
            }
            //新增
            DailyNoticePushRule dailyNoticePushRule = new DailyNoticePushRule();
            dailyNoticePushRule.setCampusId(WebUtil.getCampusId());
            dailyNoticePushRule.setSchoolId(WebUtil.getSchoolId());
            dailyNoticePushRule.setTenantId(WebUtil.getTenantId());
            dailyNoticePushRule.setParentPushFlag(reportPushRuleSaveDTO.getCommentParentPushFlag());
            dailyNoticePushRule.setPushWay(reportPushRuleSaveDTO.getPushWay());
            dailyNoticePushRule.setPushDay(reportPushRuleSaveDTO.getDailyCommentDTO().getPushDay());
            dailyNoticePushRule.setPushTime(reportPushRuleSaveDTO.getDailyCommentDTO().getPushTime());
            dailyNoticePushRule.setDeleted(false);
            boolean saveResult = super.save(dailyNoticePushRule);
            log.info("[saveDailyPushRule]saveResult: {}", saveResult);

        } else {
            //修改
            DailyNoticePushRule dailyNoticePushRule = reportPushRuleList.get(0);
            dailyNoticePushRule.setParentPushFlag(reportPushRuleSaveDTO.getCommentParentPushFlag());
            if (Constant.YES.equals(reportPushRuleSaveDTO.getCommentParentPushFlag())) {
                dailyNoticePushRule.setPushDay(reportPushRuleSaveDTO.getDailyCommentDTO().getPushDay());
                dailyNoticePushRule.setPushTime(reportPushRuleSaveDTO.getDailyCommentDTO().getPushTime());
            }
            dailyNoticePushRule.setPushWay(reportPushRuleSaveDTO.getPushWay());
            boolean updateByIdResult = super.updateById(dailyNoticePushRule);
            log.info("[saveDailyPushRule]updateByIdResult: {}", updateByIdResult);
        }
    }

}

