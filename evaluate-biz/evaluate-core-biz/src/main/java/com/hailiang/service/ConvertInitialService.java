package com.hailiang.service;

import com.hailiang.model.dto.motivate.BehaviourPointExchangeDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.point.dto.PointInitialMsgConvertDTO;

import java.util.Date;
import java.util.List;

/**
 * 转积分服务类
 *
 * @Description: 转积分服务类
 * @Author: Jovi
 * @Date: Created in 2024/11/8
 * @Version: 2.0.0
 */
public interface ConvertInitialService {

    /**
     * 发送MQ转积分
     *
     * @param businessType       参考 {@link com.hailiang.enums.PointMqBusinessTypeEnum}
     * @param dataOperateType    参考 {@link com.hailiang.enums.PointMqDataOperateTypeEnum}
     * @param records
     * @param oldBehaviourRecord
     * @return
     */
    Boolean sendMQ(Integer businessType,
                   Integer dataOperateType,
                   List<BehaviourRecord> records,
                   BehaviourRecord oldBehaviourRecord);

    Boolean initialScoreSendMQ(Integer businessType,
                               Integer dataOperateType,
                               Date submitTime,
                               PointInitialMsgConvertDTO pointInitialMsgConvertDTO);

    /**
     * 发送金币兑换MQ(新)
     *
     * @param behaviourPointExchanges
     * @return
     */
    Boolean sendBehaviourExchangeMq(List<BehaviourPointExchangeDTO> behaviourPointExchanges);


}
