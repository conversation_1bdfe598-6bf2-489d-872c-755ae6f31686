package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.hailiang.entity.EvaluateBehaviourRecordExtPO;
import com.hailiang.entity.EvaluateBehaviourRecordOptExtPO;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.logic.TaskOperateLogManager;
import com.hailiang.manager.EvaluateBehaviourRecordExtManager;
import com.hailiang.manager.EvaluateBehaviourRecordOptExtManager;
import com.hailiang.manager.EvaluateHelpBehaviourRecordManager;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.EvaluateHelpBehaviourRecordPO;
import com.hailiang.model.entity.TaskOperateLog;
import com.hailiang.model.entity.ThirdDataInfoPO;
import com.hailiang.service.SpeedInfoSaveService;
import com.hailiang.service.ThirdDataInfoService;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 极速点评保存服务
 *
 * @Description: 极速点评保存服务
 * @Author: TanJian
 * @Date: Created in 2024-09-19
 * @Version: 1.6.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SpeedInfoSaveServiceImpl implements SpeedInfoSaveService {

    private final TaskOperateLogManager taskOperateLogManager;
    private final EvaluateBehaviourRecordExtManager evaluateBehaviourRecordExtManager;
    private final EvaluateHelpBehaviourRecordManager evaluateHelpBehaviourRecordManager;
    private final BehaviourRecordManager behaviourRecordManager;
    private final EvaluateBehaviourRecordOptExtManager evaluateBehaviourRecordOptExtManager;
    private final ThirdDataInfoService thirdDataInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSpeed(List<BehaviourRecord> behaviourRecords,
            List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS,
            List<EvaluateBehaviourRecordExtPO> classifyPOS,
            TaskOperateLog taskOperateLog,
            List<EvaluateBehaviourRecordOptExtPO> behaviourRecordOptExtPOS,
            List<ThirdDataInfoPO> thirdDataInfoPOList) {

        TimeInterval TIME_INTERVAL = DateUtil.timer();


        if(CollUtil.isNotEmpty(behaviourRecords)){

            behaviourRecordManager.saveBatchByCustomerSql(behaviourRecords,1000);
            log.warn("【H5】-【极速点评】-【保存点评记录日志】，【条数：{}，耗时：{}】", behaviourRecords.size(),TIME_INTERVAL.intervalMs());
            TIME_INTERVAL.restart();
        }


        if(CollUtil.isNotEmpty(classifyPOS)){
            evaluateBehaviourRecordExtManager.saveBatchByCustomerSql(classifyPOS,1000);
            log.warn("【H5】-【极速点评】-【分类记录】，【条数：{}，耗时：{}】", classifyPOS.size(),TIME_INTERVAL.intervalMs());
            TIME_INTERVAL.restart();
        }


        if(CollUtil.isNotEmpty(helpBehaviourRecordPOS)){
            evaluateHelpBehaviourRecordManager.saveBatch(helpBehaviourRecordPOS);
            log.warn("【H5】-【极速点评】-【帮扶记录】，【条数：{}，耗时：{}】", helpBehaviourRecordPOS.size(),TIME_INTERVAL.intervalMs());
            TIME_INTERVAL.restart();
        }

        behaviourRecordOptExtPOS =  behaviourRecordOptExtPOS.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(behaviourRecordOptExtPOS)){
            evaluateBehaviourRecordOptExtManager.saveBatch(behaviourRecordOptExtPOS);
            log.warn("【H5】-【极速点评】-【必填多行文本记录】，【条数：{}，耗时：{}】", behaviourRecordOptExtPOS.size(),TIME_INTERVAL.intervalMs());
            TIME_INTERVAL.restart();
        }

        if(CollUtil.isNotEmpty(thirdDataInfoPOList)){
            thirdDataInfoService.saveBatch(thirdDataInfoPOList);
            log.warn("【H5】-【极速点评】-【第三方数据记录】，【条数：{}，耗时：{}】", thirdDataInfoPOList.size(),TIME_INTERVAL.intervalMs());
            TIME_INTERVAL.restart();
        }


        taskOperateLogManager.save(taskOperateLog);
        log.warn("【H5】-【极速点评】-【操作记录】，【耗时：{}】", TIME_INTERVAL.intervalMs());
    }
}
