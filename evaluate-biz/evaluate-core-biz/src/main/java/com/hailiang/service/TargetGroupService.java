package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.dto.query.TargetGroupListByConditionsDTO;
import com.hailiang.model.dto.query.TargetGroupListByConditionsDaoDTO;
import com.hailiang.model.dto.query.TargetGroupListDTO;
import com.hailiang.model.dto.query.TargetIdQuery;
import com.hailiang.model.dto.request.InternalConfigRequest;
import com.hailiang.model.dto.response.TargetGroupCommonResponse;
import com.hailiang.model.dto.response.speed.ModuleInfoResponse;
import com.hailiang.model.dto.save.TargetGroupSaveDTO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.model.dto.target.TargetGroupInfoResponse;
import com.hailiang.model.entity.TargetGroup;
import com.hailiang.model.response.InternalConfigResponse;
import com.hailiang.model.vo.ListAllEvaluateTargetVOModule;
import com.hailiang.model.vo.TargetGroupListVO;
import com.hailiang.model.vo.TargetGroupTargetListByConditionsVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【evaluate_target_group(指标分组表)】的数据库操作Service
* @createDate 2022-12-29 11:08:23
*/
public interface TargetGroupService extends IService<TargetGroup> {

    List<TargetGroupCommonResponse> listGroupIdsByCampusIdAndModuleCode(Integer moduleCode);

    void save(TargetGroupSaveDTO dto);

    void delete(String groupId);

    /**
     * 根据id直接删除
     * @param groupIds
     * @return
     */
    int deleteDirectByIds( List<Long> groupIds, Long updator);

    /**
     * 指标分组批量排序
     * @param groupIds
     * @return
     */
    int batchUpdateSortIndex(List<Long> groupIds,List<TargetGroup> targetGroupList);

    /**
     * 获取指标分组最大序号
     * @param campusId
     * @return
     */
    int getMaxSortIndex(String campusId,Integer moduleCode);

    List<TargetGroupListVO> list(TargetGroupListDTO dto);

    void sort(List<String> dtoList);

    TargetGroup get(Long groupId);

    List<TargetGroupTargetListByConditionsVO> listByConditions(TargetGroupListByConditionsDTO dto);

    /**
     * 根据条件查询分组
     * @param dto
     * @return
     */
    List<TargetGroup> listGroupByConditions(TargetGroupListByConditionsDaoDTO dto);

    /**
     * 获取当前登录人所有指标
     * @param userType  用户类型1:老师 2:家长
     * @return
     */
    List<ListAllEvaluateTargetVOModule> listAllEvaluateTarget(Integer userType);
    List<ListAllEvaluateTargetVOModule> queryStaffInfoModules(boolean pictureEvaluate);

    /**
     * 老师能点评的指标
     * @param staffId 教职工 id
     * @param campusId 校区 id
     * @param schoolId 学校 id
     * @return
     */
    List<ListAllEvaluateTargetVOModule> queryStaffInfoModules(Long staffId, String campusId, String schoolId);
    /**
     * 获取指标点评项
     *
     * @param targetId 指标id
     * @return
     */
    List<TemplateInfoSaveDTO> getTargetOption(Long targetId);

    long countWithDelete(String tenantId, String schoolId, String campusId, Integer code);

    /**
     * 指标树（不带选项）
     */
    List<ModuleInfoResponse> listGroupTargets();

    /**
     * 图文点评设置
     */
    void setPictureEvaluate(TargetIdQuery query);
    /**
     * 图文点评指标列表
     */
    List<Long> listPictureEvaluates();


    /**
     * 获取开关状态
     * @return
     */
    @Deprecated
    Integer listPointSwitch(String campusId);

    /**
     * 更新开关状态
     * @param status
     */
    @Deprecated
    void updatePointSwitchStatusByCampusId(Integer status);

    /**
     * 批量保存
     * @param targetGroupList
     * @return
     */
    int batchSave(List<TargetGroup> targetGroupList);

    /**
     * 根据指标id查询指标以及指标分组信息
     *
     * @param targetIds
     * @return
     */
    List<TargetGroupInfoResponse> listTargetGroupInfoByTargetIds(List<Long> targetIds);

    boolean updateInternalConfigByCampusId(InternalConfigRequest request);

    InternalConfigResponse getInternalConfig(String campusId);

    /**
     * 初始化当前校区下系统推荐的指标分组和指标
     */
    void initializeSystemRecommendTargetGroupAndTarget();
}
