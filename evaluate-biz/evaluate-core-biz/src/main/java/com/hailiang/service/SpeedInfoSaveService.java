package com.hailiang.service;

import com.hailiang.entity.EvaluateBehaviourRecordExtPO;
import com.hailiang.entity.EvaluateBehaviourRecordOptExtPO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.EvaluateHelpBehaviourRecordPO;
import com.hailiang.model.entity.TaskOperateLog;

import com.hailiang.model.entity.ThirdDataInfoPO;
import java.util.List;

/**
 * 极速点评保存服务
 *
 * @Description: 极速点评保存服务
 * @Author: TanJian
 * @Date: Created in 2024-09-19
 * @Version: 1.6.0
 */
public interface SpeedInfoSaveService {

    void saveSpeed(List<BehaviourRecord> behaviourRecords,
                   List<EvaluateHelpBehaviourRecordPO> helpBehaviourRecordPOS,
                   List<EvaluateBehaviourRecordExtPO> classifyPOS,
                   TaskOperateLog taskOperateLog,
                   List<EvaluateBehaviourRecordOptExtPO> behaviourRecordOptExtPOS,
                   List<ThirdDataInfoPO> thirdDataInfoPOList);
}
