package com.hailiang.service;

import com.hailiang.model.dto.check.appeal.CheckAppealInfoDTO;
import com.hailiang.model.dto.check.appeal.CheckApprovalInfoDTO;
import com.hailiang.model.vo.check.CheckDimVO;
import com.hailiang.model.vo.check.CheckItemVO;
import com.hailiang.model.vo.check.ClassDutyDetailVO;
import com.hailiang.model.vo.check.ClassDutyQuery;
import com.hailiang.remote.saas.vo.educational.EduStaffMasterClassVO;
import com.hailiang.remote.saas.vo.staff.StaffVO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/17 15:52
 */
public interface CheckDutyService {

    /**
     * 班主任获取班级值日详情
     *
     * @param query 查询条件
     * @return
     */
    ClassDutyDetailVO getClassDutyDetail(ClassDutyQuery query);

    /**
     * 获取当前用户担任班主任的班级集合
     *
     * @return
     */
    List<EduStaffMasterClassVO> listMasterCurrentClass();

    /**
     * 获取学校的学生从主任信息
     *
     * @param schoolId 学校id
     * @return
     */
    Set<StaffVO> getStudentDirectors(Long schoolId);

    /**
     * 获取检查维度列表
     *
     * @return
     */
    List<CheckDimVO> listCheckDim(String campusId);


}
