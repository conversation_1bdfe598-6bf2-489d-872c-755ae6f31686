package com.hailiang.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hailiang.base.saasHolder.SaasContextHolder;
import com.hailiang.base.saasHolder.StudentHolder;
import com.hailiang.constant.Constant;
import com.hailiang.constant.SaasConstant;
import com.hailiang.convert.StuPortraitQueryConvert;
import com.hailiang.enums.AppraisalEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.exception.BizException;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.logic.TermLogic;
import com.hailiang.model.dto.query.BehaviourRecordQueryDTO;
import com.hailiang.model.entity.InitialScorePO;
import com.hailiang.model.entity.StudentAbilityModel;
import com.hailiang.model.report.vo.ReportTermInfoVO;
import com.hailiang.model.response.studentmodel.CommonStudentAbilityModelResponse;
import com.hailiang.model.vo.ListCommonModuleDetailVOBehaviour;
import com.hailiang.portrait.StuPortraitSearchService;
import com.hailiang.portrait.query.CommonStuPortraitPageQuery;
import com.hailiang.portrait.query.CommonStuPortraitQuery;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.portrait.vo.StuBehaviorInfoVO;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import com.hailiang.portrait.vo.StuPortraitInfoVO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasClassManager;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.vo.StudentInfoVO;
import com.hailiang.saas.model.vo.educational.TchClassOpenV2VO;
import com.hailiang.service.CommonPortraitService;
import com.hailiang.service.ReportPushRuleService;
import com.hailiang.service.StudentAbilityModelService;
import com.hailiang.util.PageConvert;
import com.hailiang.util.RequestUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @description:
 * @author: panjian
 * @create: 2024/9/9 17:44
 * @Version 1.0
 */
@Slf4j
@Service
public class CommonPortraitServiceImpl implements CommonPortraitService {
    @Resource
    private TermLogic termLogic;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private StuPortraitSearchService stuPortraitSearchService;
    @Resource
    private StuPortraitQueryConvert stuPortraitQueryConvert;
    @Resource
    private SaasClassManager saasClassManager;
    @Lazy
    @Resource
    private BehaviourRecordManager behaviourRecordManager;
    @Resource
    private StudentAbilityModelService studentAbilityModelService;
    @Resource
    private ReportPushRuleService reportPushRuleService;
    @Resource
    private SaasStudentManager saasStudentManager;

    @Override
    public ReportTermInfoVO getCurrentTermVo(String classId) {
        // 获取学期
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(WebUtil.getSchoolIdLong());
        termQuery.setCampusId(WebUtil.getCampusIdLong());
        if (Objects.isNull(classId)) {
            classId = RequestUtil.getHeader(SaasConstant.CLASS_ID);
        }
        Assert.notEmpty(classId, "班级不能为空");
        //设置学段id
        List<TchClassOpenV2VO> tchClassOpenV2VOS = saasClassManager
                .queryClassByIds(Lists.newArrayList(classId));
        if (CollUtil.isEmpty(tchClassOpenV2VOS) || ObjectUtil.isNull(tchClassOpenV2VOS.get(0).getCampusSectionId())) {
            log.warn("【从saas获取班级信息】返回结果空：{}", JSONUtil.toJsonStr(tchClassOpenV2VOS));
            return null;
        }
        termQuery.setCampusSectionId(tchClassOpenV2VOS.get(0).getCampusSectionId().toString());
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        TermVo currentTerm = termLogic.getCurrentTerm(termVos, new Date());
        if (Objects.isNull(currentTerm)) {
            throw new BizException("无法获取当前学期数据");
        }
        ReportTermInfoVO reportTermInfoVO = new ReportTermInfoVO();
        reportTermInfoVO.setCampusSectionId(currentTerm.getCampusSectionId());
        reportTermInfoVO.setCampusSectionCode(currentTerm.getSectionCode());
        reportTermInfoVO.setCampusSectionName(currentTerm.getSectionName());
        reportTermInfoVO.setSort(Convert.toInt(currentTerm.getSectionCode()));
        reportTermInfoVO.setReportTerms(Lists.newArrayList(currentTerm));
        return reportTermInfoVO;
    }

    @Override
    public ReportTermInfoVO getCurrentTermVoByClassIdAndStudentId(String classId, String studentId) {
        // 获取学期
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(WebUtil.getSchoolIdLong());
        termQuery.setCampusId(WebUtil.getCampusIdLong());
        if (Objects.isNull(classId)) {
            classId = RequestUtil.getHeader(SaasConstant.CLASS_ID);
        }
        if (Objects.isNull(classId)){
            List<StudentInfoVO> studentInfoVOS = saasStudentManager.studentDetailV2(Convert.toLong(studentId));
            StudentInfoVO studentInfoVO = studentInfoVOS.stream()
                    .filter(item -> Objects.equals(item.getUpgradeStatus(), "0") && Objects.equals(item.getClassType(),
                            "0")).findFirst().orElse(null);
            classId = Convert.toStr(studentInfoVO == null ? null:studentInfoVO.getClassId());
        }
        Assert.notNull(classId, "班级id不能为空");
        //设置学段id
        List<TchClassOpenV2VO> tchClassOpenV2VOS = saasClassManager
                .queryClassByIds(Lists.newArrayList(classId));
        if (CollUtil.isEmpty(tchClassOpenV2VOS) || ObjectUtil.isNull(tchClassOpenV2VOS.get(0).getCampusSectionId())) {
            log.warn("【从saas获取班级信息】返回结果空：{}", JSONUtil.toJsonStr(tchClassOpenV2VOS));
            return null;
        }
        TchClassOpenV2VO tchClassOpenV2VO = tchClassOpenV2VOS.get(0);
        termQuery.setCampusSectionId(Convert.toStr(tchClassOpenV2VO.getCampusSectionId()));
        if (ObjectUtil.isEmpty(termQuery.getSchoolId())){
            termQuery.setSchoolId(tchClassOpenV2VO.getSchoolId());
        }
        if (ObjectUtil.isEmpty(termQuery.getCampusId())){
            termQuery.setCampusId(tchClassOpenV2VO.getCampusId());
        }
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        TermVo currentTerm = termLogic.getCurrentTerm(termVos, new Date());
        if (Objects.isNull(currentTerm)) {
            throw new BizException("无法获取当前学期数据");
        }
        ReportTermInfoVO reportTermInfoVO = new ReportTermInfoVO();
        reportTermInfoVO.setCampusSectionId(currentTerm.getCampusSectionId());
        reportTermInfoVO.setCampusSectionCode(currentTerm.getSectionCode());
        reportTermInfoVO.setCampusSectionName(currentTerm.getSectionName());
        reportTermInfoVO.setSort(Convert.toInt(currentTerm.getSectionCode()));
        reportTermInfoVO.setReportTerms(Lists.newArrayList(currentTerm));
        return reportTermInfoVO;
    }

    @Override
    public StuPortraitInfoVO getSimpleStuPortraitInfo(CommonStuPortraitQuery dto) {
        //未传时间，填充默认时间
        StuPortraitQuery stuPortraitQuery = checkAndBuildStuPortraitQuery(dto);
        StuPortraitInfoVO stuPortraitInfo = stuPortraitSearchService.getStuPortraitInfo(stuPortraitQuery);
        if (Objects.equals(dto.isNeedModuleDetail(), Boolean.FALSE)) {
            stuPortraitInfo.getModuleDetailNewVO().setDateBehaviourList(Collections.emptyList());
        }
        return stuPortraitInfo;
    }

    @Override
    public StuBehaviorInfoVO getSimpleStuBehaviorInfo(CommonStuPortraitQuery dto) {
        //未传时间，填充默认时间
        StuPortraitQuery stuPortraitQuery = checkAndBuildStuPortraitQuery(dto);
        return stuPortraitSearchService.getStuBehaviorInfo(stuPortraitQuery);
    }

    @Override
    public BehaviourRecordQueryDTO checkAndFillBehaviourRecordQueryDTO(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
        CommonStuPortraitQuery commonStuPortraitQuery = new CommonStuPortraitQuery();
        commonStuPortraitQuery.setClassId(behaviourRecordQueryDTO.getClassId());
        commonStuPortraitQuery.setStartTime(behaviourRecordQueryDTO.getStartTime());
        commonStuPortraitQuery.setEndTime(behaviourRecordQueryDTO.getEndTime());
        checkAndBuildStuPortraitQuery(commonStuPortraitQuery);
        Date startTime = behaviourRecordQueryDTO.getStartTime();
        Date endTime = behaviourRecordQueryDTO.getEndTime();
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            behaviourRecordQueryDTO.setStartTime(commonStuPortraitQuery.getStartTime());
            behaviourRecordQueryDTO.setEndTime(commonStuPortraitQuery.getEndTime());
        }
        behaviourRecordQueryDTO.setIsCurrentYear(commonStuPortraitQuery.getIsCurrentYear());
        behaviourRecordQueryDTO.setTermName(commonStuPortraitQuery.getTermName());
        behaviourRecordQueryDTO.setSchoolYear(commonStuPortraitQuery.getSchoolYear());
        if (Objects.isNull(WebUtil.getStaffId())) {
            StudentHolder.setStaffId(Constant.LONG_ZERO);
        }
        if (Objects.isNull(WebUtil.getTenantId())) {
            StudentHolder.setTenantId(Constant.LONG_ZERO);
        }
        return behaviourRecordQueryDTO;
    }

    @Override
    public Page<ListCommonModuleDetailVOBehaviour> getModuleDetailPage(CommonStuPortraitPageQuery pageQuery) {
        //检查并填充查询参数
        checkAndFillModuleDetailPageQO(pageQuery);
        StuPortraitQuery stuPortraitQuery = stuPortraitQueryConvert.toStuPortraitQuery(pageQuery);
        Boolean showAppraisalFlag = reportPushRuleService.getShowAppraisalFlag(stuPortraitQuery.getCampusId());
        //获取行为记录表记录
        List<StuBehaviorOptionVO> optionVOList = behaviourRecordManager.listBehaviourGroupDoris(stuPortraitQuery);
        List<ListCommonModuleDetailVOBehaviour> behaviourList = new ArrayList<>();
        if(CollectionUtils.isEmpty(optionVOList) && CollectionUtils.isEmpty(behaviourList)){
            return PageConvert.emptyPage(pageQuery.getPageNum(),pageQuery.getPageSize(),ListCommonModuleDetailVOBehaviour.class);
        }
        for (StuBehaviorOptionVO optionVO : optionVOList) {
            //转化行为记录表记录
            ListCommonModuleDetailVOBehaviour behaviour = convertBehaviourVO(optionVO);
            behaviour.setAppraisalName(behaviour.getAppraisalName());
            behaviour.setShowAppraisalFlag(showAppraisalFlag);
            behaviourList.add(behaviour);
        }
        CollUtil.sort(behaviourList,
                Comparator.comparing(ListCommonModuleDetailVOBehaviour::getSubmitTime).reversed());
        return page(behaviourList,pageQuery.getPageNum(),pageQuery.getPageSize());
    }

    /**
     * 检查并填充查询参数
     * @param pageQuery
     */
    private void checkAndFillModuleDetailPageQO(CommonStuPortraitPageQuery pageQuery) {
        if (Objects.isNull(pageQuery.getStartTime()) || Objects.isNull(pageQuery.getEndTime())) {
            setCurrentTermData(pageQuery);
        }
        pageQuery.setStartTime(DateUtil.beginOfDay(pageQuery.getStartTime()));
        pageQuery.setEndTime(DateUtil.endOfDay(pageQuery.getEndTime()));
        if(StringUtils.isBlank(pageQuery.getClassId())){
            pageQuery.setClassId(RequestUtil.getHeader(SaasConstant.CLASS_ID));
        }
        if(StringUtils.isBlank(pageQuery.getCampusId())){
            pageQuery.setCampusId(RequestUtil.getHeader(SaasConstant.CAMPUS_ID));
        }
        if(Objects.isNull(pageQuery.getIncludeHelpBehaviour())){
            pageQuery.setIncludeHelpBehaviour(Boolean.TRUE);
        }
        if(StringUtils.isBlank(pageQuery.getSchoolId())){
            pageQuery.setSchoolId(RequestUtil.getHeader(SaasConstant.SCHOOL_ID));;
        }
    }


    private Page<ListCommonModuleDetailVOBehaviour> page(List<ListCommonModuleDetailVOBehaviour> behaviourList,Integer pageNum,Integer pageSize){
        int total = behaviourList.size();
        int pages = total / pageSize;
        if(total % pageSize > 0){
            pages++;
        }
        int fromIndex = ((pageNum - 1) * pageSize);
        int toIndexIndex = ((pageNum) * pageSize);
        if(fromIndex > total){
            fromIndex = total;
        }
        if(toIndexIndex > total){
            toIndexIndex = total;
        }
        Page<ListCommonModuleDetailVOBehaviour> page = new Page<>();
        page.setPages(pages);
        page.setTotal(total);
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        page.setRecords(behaviourList.subList(fromIndex,toIndexIndex));
        return page;
    }

    /**
     * 转化行为记录表记录
     * @param optionVO
     * @return
     */
    private ListCommonModuleDetailVOBehaviour convertBehaviourVO(StuBehaviorOptionVO optionVO) {
        ListCommonModuleDetailVOBehaviour behaviour = new ListCommonModuleDetailVOBehaviour();
        behaviour.setInfoId(optionVO.getInfoId());
        behaviour.setInfoType(optionVO.getInfoType());
        behaviour.setScore(toBigDecimal(optionVO.getBehaviourScore()).stripTrailingZeros().toPlainString());
        behaviour.setTargetId(optionVO.getTargetId());
        behaviour.setTargetName(optionVO.getTargetName());
        if(Objects.equals(optionVO.getDataSource(), 15) || Objects.equals(optionVO.getDataSource(), 16)){
            String module = optionVO.getOptionId().substring(optionVO.getOptionId().length() - 1);
            behaviour.setTargetName(behaviour.getTargetName() + ModuleEnum.getModuleName(Convert.toInt(module)));
            optionVO.setTargetName(behaviour.getTargetName() + ModuleEnum.getModuleName(Convert.toInt(module)));
        }
        behaviour.setDataSource(optionVO.getDataSource());
        behaviour.setCreateTime(optionVO.getCreateTime());
        behaviour.setSubmitTime(optionVO.getSubmitTime());
        behaviour.setId(Long.valueOf(optionVO.getId()));
        behaviour.setBehaviourName(optionVO.getInfoName());
        String appraisalName = optionVO.getAppraisalName();
        if (Objects.nonNull(optionVO.getAppraisalType())){
            appraisalName = appraisalName + AppraisalEnum.getMessageByCode(optionVO.getAppraisalType());
        }
        behaviour.setAppraisalName(appraisalName);
        BigDecimal behaviourScore = optionVO.getBehaviourScore();
        behaviour.setBehaviourScore(Objects.isNull(behaviourScore)? BigDecimal.ZERO.stripTrailingZeros().toPlainString() : behaviourScore.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        return behaviour;
    }

//    TODO 初始分改版注释
//    /**
//     * 转化初始分
//     * @param allocation
//     * @return
//     */
//    private ListCommonModuleDetailVOBehaviour convertInitialScore(InitialScoreAllocation allocation) {
//        ListCommonModuleDetailVOBehaviour behaviour = new ListCommonModuleDetailVOBehaviour();
//        behaviour.setScore(allocation.getInitialScore().stripTrailingZeros().toPlainString());
//        behaviour.setDataSource(0);
//        behaviour.setTargetName(Objects.isNull(allocation.getInfoName()) ? "学期初始化分值" : allocation.getInfoName());
//        behaviour.setInfoType(1);
//        behaviour.setCreateTime(allocation.getCreateTime());
//        behaviour.setHaveDetail(0);
//        String behaviourName = "";
//        if (allocation.getScoreType() == 1) {
//            behaviourName = behaviourName + "加" + allocation.getScoreValue().stripTrailingZeros().toPlainString() + "分";
//        }
//        if (allocation.getScoreType() == 2) {
//            behaviourName = behaviourName + "减" + allocation.getScoreValue().stripTrailingZeros().toPlainString() + "分";
//        }
//        behaviour.setBehaviourName(behaviourName);
//        behaviour.setBehaviourScore(allocation.getInitialScore().stripTrailingZeros().toPlainString());
//        behaviour.setSubmitTime(allocation.getCreateTime());
//        return behaviour;
//    }


    private ListCommonModuleDetailVOBehaviour convertInitialScoreV2(InitialScorePO initialScorePO) {
        ListCommonModuleDetailVOBehaviour behaviour = new ListCommonModuleDetailVOBehaviour();
        behaviour.setScore(initialScorePO.getInitialScore().stripTrailingZeros().toPlainString());
        behaviour.setDataSource(0);
        behaviour.setTargetName("学期初始化分值");
        behaviour.setInfoType(1);
        behaviour.setCreateTime(initialScorePO.getCreateTime());
        behaviour.setHaveDetail(0);
        String behaviourName = "";
        BigDecimal initialScore = initialScorePO.getInitialScore();
        int scoreType = initialScore.compareTo(new BigDecimal("0"));
        if (scoreType >= 0) {
            behaviourName = behaviourName + "加" + initialScorePO.getInitialScore().stripTrailingZeros().toPlainString() + "分";
        }
        if (scoreType < 0) {
            behaviourName = behaviourName + "减" + initialScorePO.getInitialScore().stripTrailingZeros().toPlainString() + "分";
        }
        behaviour.setBehaviourName(behaviourName);
        behaviour.setBehaviourScore(initialScorePO.getInitialScore().stripTrailingZeros().toPlainString());
        behaviour.setSubmitTime(initialScorePO.getAllocationTime());
        return behaviour;
    }

    @Override
    public CommonStudentAbilityModelResponse getStudentModel(BehaviourRecordQueryDTO behaviourRecordQueryDTO) {
        this.checkAndFillBehaviourRecordQueryDTO(behaviourRecordQueryDTO);
        CommonStudentAbilityModelResponse commonStudentAbilityModelResponse = new CommonStudentAbilityModelResponse();
        StudentAbilityModel studentAbilityModel = studentAbilityModelService.getOne(Wrappers.<StudentAbilityModel>lambdaQuery().eq(StudentAbilityModel::getCampusId, WebUtil.getCampusId()));
        if (ObjectUtil.isEmpty(studentAbilityModel)){
            log.info("[学生能力模型]-当前校区未配置学生能力模型，直接返回");
            commonStudentAbilityModelResponse.setAbilityModelName("学生能力模型");
            return commonStudentAbilityModelResponse;
        }
        commonStudentAbilityModelResponse.setId(studentAbilityModel.getId());
        commonStudentAbilityModelResponse.setAbilityModelName(studentAbilityModel.getAbilityModelName());
        commonStudentAbilityModelResponse.setContrastRange(studentAbilityModel.getContrastRange());
        return commonStudentAbilityModelResponse;
    }

    protected BigDecimal toBigDecimal(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }


    @Override
    public StuPortraitQuery checkAndBuildStuPortraitQuery(CommonStuPortraitQuery dto) {
        //设置学期时间
        setCurrentTermData(dto);
        //设置默认为当前学年
        if(Objects.isNull(dto.getIsCurrentYear())) {
            dto.setIsCurrentYear(Boolean.TRUE);
        }
        //设置默认包含学生帮扶数据
        if(Objects.isNull(dto.getIncludeHelpBehaviour())) {
            dto.setIncludeHelpBehaviour(Boolean.TRUE);
        }
        //设置计算服务忽略权限校验标志
        setIgnoreAuthCheck();
        //转换对象
        StuPortraitQuery stuPortraitQuery = stuPortraitQueryConvert.toStuPortraitQuery(dto);
        return stuPortraitQuery;
    }

    /**
     * 填充权限校验默认值
     */
    private void setIgnoreAuthCheck(){
        SaasContextHolder.getSaasContext().setIgnoreAuthCheck(Boolean.TRUE);
    }


    /**
     * 设置本学期数据
     * @param dto
     */
    private void setCurrentTermData(CommonStuPortraitQuery dto) {
        Date startTime = dto.getStartTime();
        Date endTime = dto.getEndTime();
//        ReportTermInfoVO currentTermVo = getCurrentTermVo(StringUtils.isNotBlank(dto.getClassId()) ? dto.getClassId() : null);
        // 班牌查询可能没有班级id
        ReportTermInfoVO currentTermVo = getCurrentTermVoByClassIdAndStudentId(
                StringUtils.isNotBlank(dto.getClassId()) ? dto.getClassId() : null, dto.getStudentId());
        if (Objects.isNull(currentTermVo)) {
            throw new BizException("无法获取当前学期");
        }
        List<TermVo> reportTerms = currentTermVo.getReportTerms();
        if (CollectionUtils.isEmpty(reportTerms)) {
            throw new BizException("无法获取当前学期");
        }
        TermVo termVo = reportTerms.get(0);
        dto.setTermName(termVo.getTermName());
        dto.setSchoolYear(termVo.getSchoolYear());
        DateTime termStartTime = DateUtil.parse(termVo.getStartTime(), DatePattern.NORM_DATE_FORMATTER);
        DateTime termEndTime = DateUtil.parse(termVo.getEndTime(), DatePattern.NORM_DATE_FORMATTER);
        if (Objects.isNull(startTime)) {
            dto.setStartTime(termStartTime);
        }
        if(Objects.isNull(endTime)){
            dto.setEndTime(termEndTime);
        }
        Date now = DateUtil.endOfDay(new Date());
        if(dto.getEndTime().after(now)){
            dto.setEndTime(now);
        }
    }
}