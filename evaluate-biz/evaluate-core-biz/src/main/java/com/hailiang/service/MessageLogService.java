package com.hailiang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.entity.MessageLog;

import java.util.List;

public interface MessageLogService extends IService<MessageLog> {

    Boolean saveMessage(MessageLog messageLog);

    Boolean saveMessageBatch(List<MessageLog> messageLog);

    List<MessageLog> listByBusinessId(Long businessId);

    List<MessageLog> listByBusinessIdsAndMessageType(List<Long> businessIds,
                                                     Integer messageType,
                                                     String dayStr);

}