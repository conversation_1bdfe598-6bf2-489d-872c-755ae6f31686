package com.hailiang.service.impl;


import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.mapper.MessageLogMapper;
import com.hailiang.model.entity.MessageLog;
import com.hailiang.service.MessageLogService;
import com.hailiang.util.WebUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class MessageLogServiceImpl extends ServiceImpl<MessageLogMapper, MessageLog> implements MessageLogService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveMessage(MessageLog messageLog) {

        if (Objects.nonNull(WebUtil.getStaffId())) {
            messageLog.setCreateBy(WebUtil.getStaffId());
        }
        if (Objects.nonNull(WebUtil.getSchoolId())) {
            messageLog.setSchoolId(WebUtil.getSchoolId());
        }

        if (Objects.nonNull(WebUtil.getTenantId())) {
            messageLog.setTenantId(WebUtil.getTenantId());
        }
        messageLog.setCreateTime(DateUtil.date());
        return save(messageLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveMessageBatch(List<MessageLog> messageLogList) {
//        for (MessageLog messageLog : messageLogList) {
//            if (Objects.nonNull(WebUtil.getStaffId())) {
//                messageLog.setCreateBy(WebUtil.getStaffId());
//            } else {
//                messageLog.setCreateBy("admin");
//            }
//            if (Objects.nonNull(WebUtil.getSchoolId())) {
//                messageLog.setSchoolId(WebUtil.getSchoolId());
//            }
//            if (Objects.nonNull(WebUtil.getTenantId())) {
//                messageLog.setTenantId(WebUtil.getTenantId());
//            }
//        }
        return saveBatch(messageLogList);
    }

    @Override
    public List<MessageLog> listByBusinessId(Long businessId) {
        return this.list(new LambdaQueryWrapper<MessageLog>().eq(MessageLog::getBusinessId, businessId));
    }

    @Override
    public List<MessageLog> listByBusinessIdsAndMessageType(List<Long> businessIds,
                                                            Integer messageType,
                                                            String dayStr) {
        return this.list(new LambdaQueryWrapper<MessageLog>()
                .in(MessageLog::getBusinessId, businessIds)
                .eq(MessageLog::getMessageType, messageType)
                .like(MessageLog::getCreateTime, dayStr));
    }
}
