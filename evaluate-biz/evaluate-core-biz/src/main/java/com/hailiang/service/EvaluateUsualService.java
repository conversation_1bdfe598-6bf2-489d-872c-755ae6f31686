/**
 * Hailiang.com Inc. Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.hailiang.service;

import com.hailiang.internal.model.request.EvaluateUsualWordRemainRequest;
import com.hailiang.internal.model.request.EvaluateUsualWordRequest;
import com.hailiang.internal.model.response.EvaluateUsualWordResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version : EvaluateUsualService.java, v 0.1 2025年07月29日 14:07  chenbw Exp $
 */
public interface EvaluateUsualService {

    /**
     * 保存常用语
     * @param req
     * @return
     */
    List<EvaluateUsualWordResponse> usualSave(EvaluateUsualWordRequest req);

    /**
     * 剩余的常用语
     * @param req
     * @return
     */
    List<EvaluateUsualWordResponse> usualRemain(EvaluateUsualWordRemainRequest req);

    /**
     * 常用语列表
     * @param keyList
     * @return
     */
    Map<String, List<EvaluateUsualWordResponse>> usualList(List<String> keyList);
}
