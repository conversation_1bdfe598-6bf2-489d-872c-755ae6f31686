package com.hailiang.service;

import com.hailiang.model.dto.CheckItemYardDTO;
import com.hailiang.model.dto.SaveCheckEvaluatorDTO;

import java.util.List;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/20 11:56
 */
public interface CheckEvaluatorService {

    String formatName(List<SaveCheckEvaluatorDTO> evaluators);

    /**
     * 转换并创建评估人
     * @param evaluators
     * @param itemId
     */
    void convertAndCreateEvaluator(List<SaveCheckEvaluatorDTO> evaluators, String itemId);


    void convertAndCreateYard(List<CheckItemYardDTO> checkItemYardList, Long itemId);

    void convertAndCreateSysEvaluator(List<SaveCheckEvaluatorDTO> evaluators, String itemId);

    Boolean checkEvaluatorChangeOrNot(String itemId, List<SaveCheckEvaluatorDTO> newEvaluatorDTOS);
}
