package com.hailiang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hailiang.constant.Constant;
import com.hailiang.convert.MedalConvert;
import com.hailiang.manager.MedalLogoManager;
import com.hailiang.model.entity.MedalLogo;
import com.hailiang.model.medal.dto.MedalLogoSaveDTO;
import com.hailiang.model.medal.vo.MedalLogoVO;
import com.hailiang.service.MedalLogoService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @Author: JJL
 * @Date: 2023/5/31 14:30
 */
@Service
@Slf4j
public class MedalLogoServiceImpl implements MedalLogoService {
    @Autowired
    private MedalLogoManager medalLogoManager;
    @Resource
    private MedalConvert convert;

    @Value("${oss.urlPrefix}")
    private String urlPrefix;
    /**
     * 模块名称
     */
    private final static String MODULE_NAME = "[学生争章-";


    /**
     * 新增奖章logo
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMedalLogo(MedalLogoSaveDTO dto) {
        if(BeanUtil.isEmpty(dto)){
            return;
        }
        log.info(MODULE_NAME + "新增奖章logo], 入参:{} ", JSONUtil.toJsonStr(dto));
        if (Constant.TWO.equals(dto.getLogoType())) {
            dto.setTenantId(WebUtil.getTenantId());
            dto.setSchoolId(WebUtil.getSchoolId());
            dto.setCampusId(WebUtil.getCampusId());
        }
        //新增奖章logo
        medalLogoManager.save(convert.toMedalLogo(dto));
    }

    /**
     * 删除奖章logo
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMedalLogo(Long id) {
        if(ObjectUtil.isNull(id)){
            return;
        }
        log.info(MODULE_NAME + "删除奖章logo], 入参:{} ", id);
        //逻辑删除
        medalLogoManager.removeById(id);
    }

    /**
     * 查询奖章logo
     *
     * @return
     */
    @Override
    public List<MedalLogoVO> listMedalLogo() {
        List<MedalLogo> medalLogos = medalLogoManager.list(new LambdaQueryWrapper<MedalLogo>()
                .eq(MedalLogo::getTenantId, WebUtil.getTenantId())
                .eq(MedalLogo::getSchoolId, WebUtil.getSchoolId())
                .eq(MedalLogo::getCampusId, WebUtil.getCampusId())
                .or().eq(MedalLogo::getLogoType, Constant.ONE)
                .orderByDesc(MedalLogo::getLogoType, MedalLogo::getCreateTime));
        if(CollectionUtil.isEmpty(medalLogos)){
            return Collections.EMPTY_LIST;
        }
        medalLogos.stream().forEach(s -> {
            s.setLogoUrl(urlPrefix + s.getLogoUrl());
        });
        return convert.toMedalLogoVO(medalLogos);
    }
}
