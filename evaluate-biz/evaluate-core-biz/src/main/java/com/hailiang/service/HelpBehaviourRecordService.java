package com.hailiang.service;

import com.hailiang.model.dto.BehaviourRecordBatchQueryDTO;
import com.hailiang.model.dto.behaviour.help.HelpBehaviourRecordQueryDTO;
import com.hailiang.model.entity.EvaluateHelpBehaviourRecordPO;
import com.hailiang.model.vo.BehaviourStudentFileVO;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.query.StuPortraitQuery;

import java.util.List;

/**
 * 行为记录表-师徒帮扶
 * @description:
 * @author: panjian
 * @create: 2024/9/19 10:39
 * @Version 1.0
 */
public interface HelpBehaviourRecordService {

    /**
     * 根据学生信息查询
     * @param recordQueryCondition
     * @return
     */
    List<EvaluateHelpBehaviourRecordPO> listByStudent(HelpBehaviourRecordQueryDTO recordQueryCondition);

    /**
     * 获取某个学生帮扶五育统计数据并合并到行为记录统计数据中
     * 必须传入学生id
     * @param dto
     * @return
     */
    List<StudentDailyStatisticsPO> getAndMergeHelpBehaviourClassStatistics(List<StudentDailyStatisticsPO> behaviourClassStatisticsList, StuPortraitQuery dto);

    /**
     * 填充帮扶记录
     * @param dto
     * @param includeHelpBehaviour
     * @param behaviourStudentFileVOS
     * @return
     */
    List<BehaviourStudentFileVO> getAndMergeHelpBehaviourRecord(BehaviourRecordBatchQueryDTO dto, Boolean includeHelpBehaviour, List<BehaviourStudentFileVO> behaviourStudentFileVOS);

}
