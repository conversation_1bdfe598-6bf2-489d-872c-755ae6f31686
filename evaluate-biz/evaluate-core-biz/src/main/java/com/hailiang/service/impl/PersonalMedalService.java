package com.hailiang.service.impl;

import com.hailiang.model.dto.activity.detail.MedalTaskProgressQuery;
import com.hailiang.model.dto.activity.detail.MedalTaskProgressVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 9:55
 */
public interface PersonalMedalService {

    /**
     * 查询个人争章任务进度
     *
     * @param query 查询参数
     * @return
     */
    List<MedalTaskProgressVO> listPersonalTaskProgress(MedalTaskProgressQuery query);
}
