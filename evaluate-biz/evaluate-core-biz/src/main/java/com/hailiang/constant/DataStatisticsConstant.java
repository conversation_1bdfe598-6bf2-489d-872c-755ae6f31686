package com.hailiang.constant;

/**
 * 数据统计常量
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * @create 2024/7/3
 */
public class DataStatisticsConstant {
    /**
     * 校级老师参与率标题
     */
    public final static String SCHOOL_TITLE = "全校老师参与率";
    /**
     * 年级老师参与率标题
     */
    public final static String GRADE_TITLE = "{0}老师参与率";
    /**
     * 班级老师参与率标题
     */
    public final static String CLASS_TITLE = "{0}老师参与率";
    /**
     * 各年级老师参与率
     */
    public final static String SCHOOL_CHILDREN_TITLE = "各年级老师参与率";
    /**
     * 各班级老师参与率
     */
    public final static String GRADE_CHILDREN_TITLE = "各班级老师参与率";
    /**
     * 各老师参与点评天数
     */
    public final static String CLASS_CHILDREN_TITLE = "各老师参与点评天数";
    /**
     * 校级老师参与率标题
     */
    public final static String SCHOOL_EVALUATE_TITLE = "全校老师点评总次数";
    /**
     * 年级老师参与率标题
     */
    public final static String GRADE_EVALUATE_TITLE = "{0}点评总次数";
    /**
     * 班级老师参与率标题
     */
    public final static String CLASS_EVALUATE_TITLE = "{0}点评总次数";
    /**
     * 各班级老师参与率
     */
    public final static String GRADE_EVALUATE_CHILDREN_TITLE = "各班级老师点评次数";
    /**
     * 各年级老师参与率
     */
    public final static String SCHOOL_EVALUATE_CHILDREN_TITLE = "各年级老师点评次数";
    /**
     * 各老师参与点评天数
     */
    public final static String CLASS_EVALUATE_CHILDREN_TITLE = "各老师点评次数";
    /**
     * 老师参与率班级看板智能建议
     */
    public final static String CLASS_PARTICIPATION_SUGGESTION = "老师参与评价天数少于人均评价{0}天";
    /**
     * 老师参与率年级看板智能建议
     */
    public final static String GRADE_PARTICIPATION_SUGGESTION = "参与率低于平均参与率{0}，建议提升老师的点评参与率";
    /**
     * 各老师点评次数智能建议
     */
    public final static String CLASS_EVALUATE_SUGGESTION = "老师点评次数低于平均值{0}次，建议引导老师更多点评";
    /**
     * 各班级点评次数智能建议
     */
    public final static String GRADE_EVALUATE_SUGGESTION = "点评次数低于平均值{0}次，建议引导老师更多点评";
    /**
     * 全校学生点评覆盖率
     */
    public final static String SCHOOL_STUDENT_EVALUATE_TITLE = "全校学生点评覆盖率";
    /**
     * 年级学生点评覆盖率
     */
    public final static String GRADE_STUDENT_EVALUATE_TITLE = "{0}学生点评覆盖率";
    /**
     * 班级学生点评点评覆盖率
     */
    public final static String CLASS_STUDENT_EVALUATE_TITLE = "{0}学生点评覆盖率";
    /**
     * 各年级学生点评覆盖率
     */
    public final static String SCHOOL_STUDENT_EVALUATE_CHILDREN_TITLE = "各年级学生点评覆盖率";
    /**
     * 各班级学生点评覆盖率
     */
    public final static String GRADE_STUDENT_EVALUATE_CHILDREN_TITLE = "各班级学生点评覆盖率";
    /**
     * 各老师学生点评覆盖率
     */
    public final static String CLASS_STUDENT_EVALUATE_CHILDREN_TITLE = "各老师学生点评覆盖率";
    /**
     * 各班级点评学生覆盖率智能建议
     */
    public final static String GRADE_STUDENT_EVALUATE_SUGGESTION = "点评学生覆盖率低于平均值{0}，建议引导老师更多点评";
    /**
     * 各老师点评学生覆盖率智能建议
     */
    public final static String CLASS_STUDENT_EVALUATE_SUGGESTION = "老师点评学生覆盖率低于平均值{0}，建议引导老师更多点评";
    /**
     * 老师点评榜下载名称
     */
    public final static String TEACHER_EXCEL_NAME = "{0}至{1}老师点评榜单";
}
