package com.hailiang.constant;


import com.google.common.collect.ImmutableMap;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class LevelConstant {

    public static final Map<String, Integer> STAR_LEVEL_MAP = ImmutableMap.<String, Integer>builder()
            .put("十星", 10)
            .put("九星", 9)
            .put("八星", 8)
            .put("七星", 7)
            .put("六星", 6)
            .put("五星", 5)
            .put("四星", 4)
            .put("三星", 3)
            .put("二星", 2)
            .put("一星", 1)
            .build();

    public static final Map<String, Integer> LEVEL_MAP = ImmutableMap.<String, Integer>builder()
            .put("A", 10)
            .put("B", 9)
            .put("C", 8)
            .put("D", 7)
            .put("E", 6)
            .put("F", 5)
            .put("G", 4)
            .put("H", 3)
            .put("I", 2)
            .put("J", 1)
            .build();

    public static List<String> getStarLevelList() {
        return new ArrayList<>(STAR_LEVEL_MAP.keySet());
    }

    public static List<String> getLevelList() {
        return new ArrayList<>(LEVEL_MAP.keySet());
    }
}

