package com.hailiang.portrait;

import com.hailiang.agg.AbstractAggHandler;
import com.hailiang.agg.AggBizTypeEnum;
import com.hailiang.enums.StuPortraitBizTypeEnum;
import com.hailiang.event.dto.BaseDTO;
import com.hailiang.exception.BizException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName AbstractStuPortraitDetailHandler
 * @Description 收拢指责
 * <AUTHOR>
 * @Date 2024/7/4 13:58
 */
@Component
public class PortraitDetailHandler<T extends BaseDTO> {

    private final Map<StuPortraitBizTypeEnum, AbstractPortraitDetailHandler<T>> handlerMap;

    public PortraitDetailHandler(List<AbstractPortraitDetailHandler<T>> aggServices) {
        this.handlerMap = aggServices.stream().collect(Collectors.toMap(AbstractPortraitDetailHandler::getBizType, Function.identity()));
    }

    public AbstractPortraitDetailHandler<T> getHandler(StuPortraitBizTypeEnum bizType){
        AbstractPortraitDetailHandler<T> handler = handlerMap.get(bizType);
        if (Objects.isNull(handler)){
            throw new BizException("没有处理类");
        }
        return handler;
    }
}
