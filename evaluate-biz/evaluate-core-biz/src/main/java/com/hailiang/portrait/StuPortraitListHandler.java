package com.hailiang.portrait;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.convert.BehaviourRecordConvert;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.manager.InitialScoreManager;
import com.hailiang.manager.StudentDailyStatisticsManager;
import com.hailiang.mapper.doris.DorisStudentPortraitMapper;
import com.hailiang.model.datastatistics.dto.StudentDailyStatisticsDTO;
import com.hailiang.model.dto.BehaviourRecordBatchQueryDTO;
import com.hailiang.model.dto.CalculateScoreDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.InitialScorePO;
import com.hailiang.model.vo.BehaviourStudentFileVO;
import com.hailiang.portrait.query.StuPortraitListQuery;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.service.HelpBehaviourRecordService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * 学生画像列表抽象类
 *
 * @Description: 学生画像列表抽象类
 * @Author: Jovi
 * @Date: Created in 2024/7/25
 * @Version: 2.0.0
 */
@Slf4j
@Component
public abstract class StuPortraitListHandler {
    @Resource
    private StudentDailyStatisticsManager studentDailyStatisticsManager;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private BehaviourRecordManager behaviourRecordManager;
    @Resource
    private DorisStudentPortraitMapper dorisStudentPortraitMapper;
    @Resource
    private HelpBehaviourRecordService helpBehaviourRecordService;
    @Resource
    private InitialScoreManager initialScoreManager;

    protected abstract List<EduStudentInfoVO> listCurrentCacheStudent(Long schoolId, Long campusId, String campusSectionId, String schoolYear, Boolean isCurrentYear, String gradeId, String classId, Date checkTime, String staffId);

    /**
     * 获取学生信息（带权限校验）
     */
    protected abstract List<EduStudentInfoVO> queryStudentPage(Long schoolId, Long campusId, String campusSectionId, String schoolYear, Boolean isCurrentYear, String gradeId, String classId, Date checkTime);

    public Page<BehaviourStudentFileVO> pageStuPortraitInfo(StuPortraitListQuery query) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();

        String key = StrUtil.format(
                RedisKeyConstants.LIST_STUDENT_PORTRAIT_INFO,
                WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                WebUtil.getStaffIdLong(),
                query.getCampusSectionId(),
                query.getSchoolYear(),
                query.getTermName(),
                query.getGradeId(),
                query.getClassId(),
                query.getModuleCode(),
                query.getStartTime(),
                query.getEndTime(),
                query.getIncludeInternalScoreFlag(),
                query.getIncludeHelpBehaviour()
        );
//        List<BehaviourStudentFileVO> statisticsResult = this.listStuPortraitInfo(query);
        List<BehaviourStudentFileVO> statisticsResult = this.listStuPortraitInfo(query);

        // 学生姓名模糊过滤
        if (StrUtil.isNotBlank(query.getNameLike())) {
            statisticsResult = statisticsResult.stream().filter(s -> s.getStudentName().contains(query.getNameLike())).collect(Collectors.toList());
        }
        int pageNum = query.getPageNum();
        int pageSize = query.getPageSize();
        boolean empty = CollUtil.isEmpty(statisticsResult);
        Page<BehaviourStudentFileVO> behaviourStudentFileVOPage = new Page<>(pageNum, pageSize, empty ? Constant.ZERO : statisticsResult.size());
        if (empty) return behaviourStudentFileVOPage;
        log.warn("StuPortraitListHandler.pageStuPortraitInfo 学生画像-列表 - step.1.6 - 计算学生名次-数据转换 --结束，消耗时长： {} ，画像列表返回列表数：{}", TIME_INTERVAL.intervalMs(), statisticsResult.size());
        // 手动分页
        int totalSize = (pageNum - 1) * pageSize;
        if (totalSize > statisticsResult.size()) return behaviourStudentFileVOPage;
        statisticsResult = statisticsResult.subList(totalSize, Math.min(pageNum * pageSize, statisticsResult.size()));
        behaviourStudentFileVOPage.setRecords(statisticsResult);
        return behaviourStudentFileVOPage;
    }

    public List<EduStudentInfoVO> listStudentInfo(StuPortraitListQuery query){
        return this.listCurrentCacheStudent(
                WebUtil.getSchoolIdLong(),
                WebUtil.getCampusIdLong(),
                query.getCampusSectionId(),
                query.getSchoolYear(),
                query.getIsCurrentYear(),
                query.getGradeId(),
                query.getClassId(),
                query.getStartTime(),
                WebUtil.getStaffId());
    }

    /**
     * 获取学生画像列表
     *
     * @param query
     * @return
     */
    private List<BehaviourStudentFileVO> listStuPortraitInfo(StuPortraitListQuery query) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        // 获取指定条件下所有学生信息
        List<EduStudentInfoVO> saasStudentInfoVOList =
                this.listCurrentCacheStudent(
                        WebUtil.getSchoolIdLong(),
                        WebUtil.getCampusIdLong(),
                        query.getCampusSectionId(),
                        query.getSchoolYear(),
                        query.getIsCurrentYear(),
                        query.getGradeId(),
                        query.getClassId(),
                        query.getStartTime(),
                        WebUtil.getStaffId());

        log.warn("【学生画像】-【列表】 - 【step.1】 - 获取saas学生数据条数：「{}」 --结束，消耗时长： {}", saasStudentInfoVOList.size(), TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        if (CollUtil.isEmpty(saasStudentInfoVOList)) {
            return new ArrayList<>();
        }

        // 根据班级id筛选出数据
        List<String> classIds = new ArrayList<>();
        List<String> gradeIds = new ArrayList<>();

        List<String> saasStudentIds = saasStudentInfoVOList.stream().map(item -> {
            classIds.add(Convert.toStr(item.getClassId()));
            gradeIds.add(Convert.toStr(item.getGradeId()));
            return Convert.toStr(item.getId());
        }).distinct().collect(Collectors.toList());

        List<String> newGradeIds = gradeIds.stream().distinct().collect(Collectors.toList());
        List<String> newClassIds = classIds.stream().distinct().collect(Collectors.toList());
        if (query.getClassId() == null) {
            newClassIds = null;
        }
        if (query.getGradeId() == null) {
            newGradeIds = null;
        }
//        BigDecimal initialScore = BigDecimal.ZERO;
//        // 初始分的积分
//        InitialScoreDetail initialScoreDetail;
//        if (CollUtil.isNotEmpty(saasStudentIds)) {
//            initialScoreDetail = initialScoreDetailManager.getOne(new LambdaQueryWrapper<InitialScoreDetail>()
//                    .eq(InitialScoreDetail::getCampusId, WebUtil.getCampusId())
//                    // 支持归档数据，时间为学期起止时间，直接使用的话，可能会存在当前学期修改历史学期的数据，导致创建时间不是历史的时间，统计存在问题
//                    .between(InitialScoreDetail::getCreateTime, query.getStartTime(), query.getEndTime())
//                    .orderByDesc(InitialScoreDetail::getCreateTime)
//                    .last("limit 1"));
//            if (initialScoreDetail != null) {
//                initialScore = initialScoreDetail.getAllocationScore();
//            }
//        }

        log.warn("【学生画像】-【列表】 - 【step.2】 - 【获取学生初始分数据】 --结束，消耗时长： {}", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 计算分数
        /*List<BehaviourStudentFileVO> statisticsRecords =
                this.queryStatisticsRecords(
                        saasStudentInfoVOList,
                        totalInitialScoreAllocations,
                        newClassIds,
                        newGradeIds,
                        query);

        List<BehaviourStudentFileVO> statisticsResult = statisticsRecords
                .stream()
                .sorted(Comparator.comparing(BehaviourStudentFileVO::getSumScore, Comparator.nullsFirst(BigDecimal::compareTo))
                        .thenComparing(BehaviourStudentFileVO::getSubtractScore, Comparator.nullsFirst(BigDecimal::compareTo))
                        .thenComparing(BehaviourStudentFileVO::getStudentId, Comparator.nullsFirst(String::compareTo)).reversed())
                .collect(Collectors.toList());

        log.warn("【学生画像】-【列表】 - 【step.5】 - 【计算分数-数据转换】--结束，消耗时长： {}", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        // 学生名次计算
        Map<BigDecimal, Integer> rankMap = this.getClassRank(statisticsResult);
        statisticsResult.forEach(s -> s.setRank(rankMap.get(s.getSumScore())));*/
        BehaviourRecordBatchQueryDTO dto = new BehaviourRecordBatchQueryDTO();
        dto.setCampusId(WebUtil.getCampusId());
        dto.setCampusSectionId(query.getCampusSectionId());
        dto.setModuleCode(query.getModuleCode());
        dto.setGradeIdList(newGradeIds);
        dto.setClassIdList(newClassIds);
        dto.setStartTime(query.getStartTime());
        dto.setEndTime(query.getEndTime());
        dto.setStudentIdList(saasStudentIds);
        //不包含老师自建指标加分
        if (Boolean.FALSE.equals(query.getIncludeInternalScoreFlag())) {
            dto.setDataSourceList(DataSourceEnum.getCodesFromSchoolLevel());
            dto.setIncludeInternalScoreFlag(false);
        }

        List<BehaviourStudentFileVO> behaviourStudentFileVOS = dorisStudentPortraitMapper.listStudentPortrait(dto);
        log.warn("【学生画像】-【列表】 - 【step.6】 - 【从Doris获取学生分数和排名数据】 --结束，消耗时长： {}", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        //填充帮扶数据
        helpBehaviourRecordService.getAndMergeHelpBehaviourRecord(dto, query.getIncludeHelpBehaviour(), behaviourStudentFileVOS);
        log.warn("【学生画像】-【列表】 - 【step.7】 - 【从Doris获取学生帮扶数据】 --结束，消耗时长： {}", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        Map<String, BehaviourStudentFileVO> behaviourStudentFileVOMap = CollStreamUtil.toIdentityMap(behaviourStudentFileVOS, BehaviourStudentFileVO::getStudentId);
        List<BehaviourStudentFileVO> statisticsResult = new ArrayList<>(2048);
        for (EduStudentInfoVO d : saasStudentInfoVOList) {
            BehaviourStudentFileVO item = behaviourStudentFileVOMap.get(Convert.toStr(d.getId()));
            if (item == null) {
                item = new BehaviourStudentFileVO();
                item.setStudentId(Convert.toStr(d.getId()));
                item.setStudentName(d.getStudentName());
            } else {
                item.setStudentName(d.getStudentName());
            }
            item.setClassId(Convert.toStr(d.getClassId()));
            item.setClassName(d.getClassName());
            item.setGradeId(Convert.toStr(d.getGradeId()));
            item.setGradeName(d.getGradeName());
            statisticsResult.add(item);
        }
        // TODO: 2024/11/8 初始分重新设计，无需加入初始分，暂时注释
//        // 加上初始分(如果筛选条件有为五育 就不加初始分)
////        if (ObjectUtil.isEmpty(query.getModuleCode()) && !NumberUtil.equals(initialScore, BigDecimal.ZERO)){
//        if (ObjectUtil.isEmpty(query.getModuleCode())) {
//            fillInitialScore(query, statisticsResult);
//        }
        //初始分
        this.fillInitialScoreNew(query, statisticsResult);

        List<BehaviourStudentFileVO> statisticsResultNew = statisticsResult
                .stream()
                .sorted(Comparator.comparing(BehaviourStudentFileVO::getSumScore, Comparator.nullsFirst(BigDecimal::compareTo))
                        .thenComparing(BehaviourStudentFileVO::getSubtractScore, Comparator.nullsFirst(BigDecimal::compareTo))
                        .thenComparing(BehaviourStudentFileVO::getStudentId, Comparator.nullsFirst(String::compareTo)).reversed())
                .collect(Collectors.toList());


        // 学生名次计算
        Map<BigDecimal, Integer> rankMap = this.getClassRank(statisticsResultNew);
        statisticsResultNew.forEach(s -> s.setRank(rankMap.get(s.getSumScore())));
        log.warn("【学生画像】-【列表】 - 【step.8】 - 【学生综合得分加上初始分】 --结束，消耗时长： {}", TIME_INTERVAL.intervalMs());

        if(Objects.equals(query.getSortType(), Constant.ONE)){

            // 按照分数升序排序
            statisticsResultNew = statisticsResultNew.stream().sorted(Comparator.comparing(BehaviourStudentFileVO::getSumScore, Comparator.nullsFirst(BigDecimal::compareTo))
                            .thenComparing(BehaviourStudentFileVO::getSubtractScore, Comparator.nullsFirst(BigDecimal::compareTo))
                            .thenComparing(BehaviourStudentFileVO::getStudentId, Comparator.nullsFirst(String::compareTo)))
                    .collect(Collectors.toList());
        }

        return statisticsResultNew;
    }

    /**
     * 填充初始分
     */
    private void fillInitialScoreNew(StuPortraitListQuery query, List<BehaviourStudentFileVO> statisticsResult) {
        // 查询其他育
        if(Objects.equals(query.getModuleCode(), 0)){
            statisticsResult.forEach(item -> item.setInitialScore(BigDecimal.ZERO));
            return;
        }

        InitialScorePO initialScorePO = initialScoreManager.getOneByBaseInfo(WebUtil.getSchoolId(),
                WebUtil.getCampusId(),
                query.getSchoolYear(),
                query.getTermName(),
                ObjectUtil.isEmpty(query.getModuleCode()) ? ModuleEnum.OTHER.getCode() : query.getModuleCode());

        BigDecimal initialScore = BigDecimal.ZERO;
        if (BeanUtil.isNotEmpty(initialScorePO) && ObjectUtil.isNotNull(initialScorePO.getInitialScore())) {
            initialScore = initialScorePO.getInitialScore();
        }

        for (BehaviourStudentFileVO item : statisticsResult) {
            item.setSumScore(NumberUtil.add(item.getSumScore(), initialScore));
            item.setInitialScore(initialScore);
        }
    }

//    TODO 初始分改版注释
//    /**
//     * 填充初始分
//     *
//     * @param query
//     * @param statisticsResult
//     */
//    private void fillInitialScore(StuPortraitListQuery query, List<BehaviourStudentFileVO> statisticsResult) {
//        List<InitialScoreAllocation> totalInitialScoreAllocations = initialScoreAllocationManager.list(new LambdaQueryWrapper<InitialScoreAllocation>()
//                .eq(InitialScoreAllocation::getTenantId, WebUtil.getTenantId())
//                .eq(InitialScoreAllocation::getSchoolId, WebUtil.getSchoolId())
//                .eq(InitialScoreAllocation::getCampusId, WebUtil.getCampusId())
//                .eq(InitialScoreAllocation::getCampusSectionId, query.getCampusSectionId())
//                .eq(StringUtils.isNotEmpty(query.getGradeId()), InitialScoreAllocation::getGradeId, query.getGradeId())
//                .eq(StringUtils.isNotEmpty(query.getClassId()), InitialScoreAllocation::getClassId, query.getClassId())
//                .eq(StringUtils.isNotEmpty(query.getSchoolYear()), InitialScoreAllocation::getSchoolYear, query.getSchoolYear())
//                .eq(StringUtils.isNotEmpty(query.getTermName()), InitialScoreAllocation::getTermName, query.getTermName())
//                // 支持归档数据，时间为学期起止时间，直接使用的话，可能会存在当前学期修改历史学期的数据，导致创建时间不是历史的时间，统计存在问题
//                .between(InitialScoreAllocation::getCreateTime, query.getStartTime(), query.getEndTime()));
//
//        // 积分总数据
//        Map<String/*学生ID*/, List<InitialScoreAllocation>> totalInitialMap = new HashMap<>();
//
//        if (CollUtil.isNotEmpty(totalInitialScoreAllocations)) {
//            totalInitialMap = CollStreamUtil.groupByKey(totalInitialScoreAllocations, InitialScoreAllocation::getStudentId);
//        }
//        for (BehaviourStudentFileVO item : statisticsResult) {
//            List<InitialScoreAllocation> initialScoreAllocations = totalInitialMap.get(item.getStudentId());
//            //计算初始分加减分情况
//            CalculateScoreDTO calculateScoreDTO = initialCalculateScore(initialScoreAllocations);
//            BigDecimal addScore = calculateScoreDTO.getAddScore();
//            BigDecimal subtractScore = calculateScoreDTO.getSubtractScore();
//            item.setSumScore(NumberUtil.add(item.getSumScore(), addScore.add(subtractScore)));
//        }
//    }


//    TODO 初始分改版注释
//    /**
//     * @param behaviourStudentFileVO
//     * @param moduleCode
//     * @param totalCalculateScoreDTO
//     * @param totalInitialScoreAllocationList
//     * @return
//     */
//    protected BehaviourStudentFileVO singleStatisticsTest(BehaviourStudentFileVO behaviourStudentFileVO,
//                                                          Integer moduleCode,
//                                                          CalculateScoreDTO totalCalculateScoreDTO,
//                                                          List<InitialScoreAllocation> totalInitialScoreAllocationList) {
//        // 总积分
//        CalculateScoreDTO totalInitialScoreDTO = this.initialCalculateScore(totalInitialScoreAllocationList);
//        BigDecimal sumScore = this.getSumScore(moduleCode, totalCalculateScoreDTO, totalInitialScoreDTO);
//
//        behaviourStudentFileVO.setSumScore(ObjectUtil.isNull(sumScore) ? null :
//                new BigDecimal(sumScore.setScale(Constant.TWO).stripTrailingZeros().toPlainString()));
//        // 总计
//        behaviourStudentFileVO.setAddScore(totalCalculateScoreDTO.getAddScore());
//        behaviourStudentFileVO.setSubtractScore(totalCalculateScoreDTO.getSubtractScore());
//
//        return behaviourStudentFileVO;
//    }

    /**
     * 获取总分
     *
     * @param moduleCode
     * @param totalCalculateScoreDTO
     * @param totalInitialScoreDTO
     * @return
     */
    private BigDecimal getSumScore(Integer moduleCode, CalculateScoreDTO totalCalculateScoreDTO, CalculateScoreDTO totalInitialScoreDTO) {
        BigDecimal totalInitialScore = new BigDecimal(Constant.ZERO);
        // 筛选单个育时不计算初始分
        if (Objects.nonNull(totalInitialScoreDTO.getAddScore()) && Objects.isNull(moduleCode)) {
            totalInitialScore = totalInitialScore.add(totalInitialScoreDTO.getAddScore());
        }
        if (Objects.nonNull(totalInitialScoreDTO.getSubtractScore()) && Objects.isNull(moduleCode)) {
            totalInitialScore = totalInitialScore.add(totalInitialScoreDTO.getSubtractScore());
        }
        return totalCalculateScoreDTO.getAddScore().add(totalCalculateScoreDTO.getSubtractScore()).add(totalInitialScore);
    }

    /**
     * 获得本周一0点时间
     *
     * @return
     */
    protected static Date getTimesWeekMorning() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return cal.getTime();
    }

//   TODO 初始分改版注释
//
//    /**
//     * 计算分数
//     *
//     * @param totalInitialScoreAllocationList
//     * @return
//     */
//    private CalculateScoreDTO initialCalculateScore(List<InitialScoreAllocation> totalInitialScoreAllocationList) {
//        if (CollUtil.isEmpty(totalInitialScoreAllocationList)) {
//            CalculateScoreDTO calculateScoreDTO = new CalculateScoreDTO();
//            calculateScoreDTO.setAddScore(BigDecimal.ZERO);
//            calculateScoreDTO.setSubtractScore(BigDecimal.ZERO);
//            return calculateScoreDTO;
//        }
//        //加分
//        List<InitialScoreAllocation> addScoreBehaviourRecordList = totalInitialScoreAllocationList.stream()
//                .filter(d -> Constant.ONE.equals(d.getScoreType())).collect(Collectors.toList());
//
//        BigDecimal addScore = Constant.ZERO.equals(addScoreBehaviourRecordList.size()) ? BigDecimal.ZERO :
//                new BigDecimal(addScoreBehaviourRecordList.stream().map(InitialScoreAllocation::getInitialScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO).stripTrailingZeros().toPlainString());
//        //减分
//        List<InitialScoreAllocation> subtractScoreBehaviourRecordList =
//                totalInitialScoreAllocationList.stream().filter(d -> Constant.TWO.equals(d.getScoreType())).collect(Collectors.toList());
//
//        BigDecimal subtractScore = Constant.ZERO.equals(subtractScoreBehaviourRecordList.size()) ? BigDecimal.ZERO :
//                new BigDecimal(subtractScoreBehaviourRecordList.stream().map(InitialScoreAllocation::getInitialScore).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO).stripTrailingZeros().toPlainString());
//
//        CalculateScoreDTO calculateScoreDTO = new CalculateScoreDTO();
//        calculateScoreDTO.setAddScore(addScore);
//        calculateScoreDTO.setSubtractScore(subtractScore);
//        return calculateScoreDTO;
//    }

    /**
     * 该方法可用于学生综合得分排名计算操作
     * 传入需要处理的学生信息集合和分数
     *
     * @param list 学生信息集合
     * @return
     */
    private Map<BigDecimal, Integer> getClassRank(List<BehaviourStudentFileVO> list) {
        // 思路: 按照综合得分来进行排名,在然后排名的时候进行比较,如果这一名的综合得分和上一名的相同,那么名次相同,如果比上一名分数低,那么排名加一
        List<BigDecimal> arrayList = new ArrayList<>();
        Map<BigDecimal, Integer> rankMap = new HashMap<>();
        //将需要排序的字段放入集合
        for (BehaviourStudentFileVO behaviourStudentFileVO : list) {
            arrayList.add(behaviourStudentFileVO.getSumScore());
        }
        //学生总分为key，循坏下标为value生成map
        for (int i = 0; i < arrayList.size(); i++) {
            if (i == 0) {
                rankMap.put(arrayList.get(0), 1);
            }
            if (!rankMap.containsKey(arrayList.get(i))) {
                rankMap.put(arrayList.get(i), i + 1);
            }
        }
        return rankMap;
    }


    /**
     * 根据行为记录获取学生的加减分
     *
     * @param dailyStatisticsList 行为记录数据
     */
    private CalculateScoreDTO calculateScore(List<StudentDailyStatisticsDTO> dailyStatisticsList) {
        CalculateScoreDTO calculateScoreDTO = new CalculateScoreDTO();
        BigDecimal[] bigDecimals = this.multiKeySum(dailyStatisticsList);
        calculateScoreDTO.setAddScore(bigDecimals[1]);
        calculateScoreDTO.setSubtractScore(bigDecimals[2]);
        return calculateScoreDTO;
    }

    /**
     * 多个BigDecimal字段遍历一次求和
     *
     * @return 集合中三个字段各自和的数组
     */
    private BigDecimal[] multiKeySum(List<StudentDailyStatisticsDTO> studentDailyStatisticsList) {
        if (CollUtil.isEmpty(studentDailyStatisticsList)) return initArr();
        // 自定义收集器，遍历一次集合计算多个BigDecimal的字段和
        return studentDailyStatisticsList.stream().collect(Collector.of(this::initArr, (acc, item) -> {
                    if (Objects.isNull(item)) return;
                    acc[0] = acc[0].add(item.getTotalScore() == null ? BigDecimal.ZERO : item.getTotalScore());
                    acc[1] = acc[1].add(item.getPlusTotalScore() == null ? BigDecimal.ZERO : item.getPlusTotalScore());
                    acc[2] = acc[2].add(item.getMinusTotalScore() == null ? BigDecimal.ZERO : item.getMinusTotalScore());
                }, (left, right) -> { // 组合器
                    left[0] = left[0].add(right[0]);
                    left[1] = left[1].add(right[1]);
                    left[2] = left[2].add(right[2]);
                    return left;
                }, acc -> acc
        ));
    }

    private BigDecimal[] initArr() {
        return new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
    }
}
