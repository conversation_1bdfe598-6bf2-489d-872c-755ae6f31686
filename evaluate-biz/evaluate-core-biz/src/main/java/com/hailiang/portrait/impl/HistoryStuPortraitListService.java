package com.hailiang.portrait.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.hailiang.convert.BasicConvert;
import com.hailiang.model.datastatistics.vo.CheckPermissionVo;
import com.hailiang.portrait.StuPortraitListHandler;
import com.hailiang.remote.saas.vo.educational.EduAuthVO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.saas.SaasHistoryStudentCacheManager;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 历史学年学生列表
 *
 * @Description: 历史学年学生列表
 * @Author: zhanshuchan
 * @Date: Created in 2024/7/25
 * @Version: 2.0.0
 */
@Slf4j
@Service
public class HistoryStuPortraitListService extends StuPortraitListHandler {
    @Resource
    private SaasHistoryStudentCacheManager saasHistoryStudentCacheManager;
    @Resource
    private BasicInfoService basicInfoService;


    /**
     * 查询某学校某一学年（时刻）的学生列表
     * 缓存一周
     */
    @Override
    public List<EduStudentInfoVO> listCurrentCacheStudent(Long schoolId, Long campusId, String campusSectionId, String schoolYear, Boolean isCurrentYear, String gradeId, String classId, Date checkTime, String staffId) {
        return queryStudentPage(schoolId, campusId, campusSectionId, schoolYear, isCurrentYear, gradeId, classId, checkTime);
    }

    @Override
    protected List<EduStudentInfoVO> queryStudentPage(
            Long schoolId,
            Long campusId,
            String campusSectionId,
            String schoolYear,
            Boolean isCurrentYear,
            String gradeId,
            String classId,
            Date checkTime) {

        // 获取历史学生数据（该接口不支持分页）
        Long campusSectionIdL = Convert.toLong(campusSectionId);
        List<SassStudentVO> hisStudentVOS = saasHistoryStudentCacheManager.listHisStudentByGradeId(schoolId, schoolYear, campusId, campusSectionIdL, Convert.toLong(gradeId), Convert.toLong(classId));
        if (CollUtil.isEmpty(hisStudentVOS)) {
            return new ArrayList<>();
        }
        // 数据权限校验
        /*CheckPermissionVo permissionVo = basicInfoService.checkPermission(campusSectionIdL, schoolYear, isCurrentYear, gradeId, classId, checkTime);
        if (permissionVo == null || !Objects.equals(Boolean.TRUE, permissionVo.getHasPermission())) {
            return new ArrayList<>();
        }*/
        EduAuthVO currentStaffAuth = basicInfoService.getCurrentStaffAuth(WebUtil.getStaffId(), schoolYear);
        if (currentStaffAuth.getIsAdmin()){
            return hisStudentVOS.stream().map(item -> BasicConvert.INSTANCE.toEduStudentInfoVO(item)).collect(Collectors.toList());
        }
        return hisStudentVOS.stream().map(item -> {
            boolean inGrade = CollUtil.isEmpty(currentStaffAuth.getGradeIdStrs()) || currentStaffAuth.getGradeIdStrs().contains(Convert.toStr(item.getGradeId()));
            if (!inGrade) return null;
            boolean inClass = CollUtil.isEmpty(currentStaffAuth.getClassIdStrs()) || currentStaffAuth.getClassIdStrs().contains(Convert.toStr(item.getClassId()));
            return inClass ? BasicConvert.INSTANCE.toEduStudentInfoVO(item) : null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

}