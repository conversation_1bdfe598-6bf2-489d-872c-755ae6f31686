package com.hailiang.portrait.impl;

import static com.hailiang.common.cache.constant.RedisKeyConstants.DOWNLOAD_STUDENT_PORTRAIT;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.convert.HelpBehaviourRecordConvert;
import com.hailiang.enums.*;
import com.hailiang.enums.error.ReportErrorEnum;
import com.hailiang.enums.report.ReportBehaviourTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.logic.TargetLogic;
import com.hailiang.manager.InitialScoreManager;
import com.hailiang.mapper.doris.DorisStudentPortraitMapper;
import com.hailiang.model.dto.behaviour.help.HelpBehaviourRecordQueryDTO;
import com.hailiang.model.entity.*;
import com.hailiang.model.response.BehaviourStudentExportResponse;
import com.hailiang.model.vo.BehaviourIntelligentEvaluationVO;
import com.hailiang.model.vo.BehaviourRecordSynthesisScoreRankVO;
import com.hailiang.model.vo.BehaviourStudentExportVO;
import com.hailiang.model.vo.BehaviourStudentFileVO;
import com.hailiang.model.vo.ListModuleDetailVODateNewInfo;
import com.hailiang.model.vo.ListOrgIdAndRoleIdVO;
import com.hailiang.portrait.*;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.handler.FillStuDataDTO;
import com.hailiang.portrait.query.StuPortraitListQuery;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.portrait.vo.StuBehaviorInfoVO;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import com.hailiang.portrait.vo.StuPortraitInfoVO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.saas.SaasHistoryStudentCacheManager;
import com.hailiang.saas.SaasStudentCacheManager;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.dto.ParentQueryDTO;
import com.hailiang.saas.model.vo.ParentInfoVO;
import com.hailiang.saas.model.vo.StudentInfo1VO;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import com.hailiang.saas.model.vo.student.StudentBasicInfoVO;
import com.hailiang.service.*;
import com.hailiang.util.AssertUtil;
import com.hailiang.util.NumUtil;
import com.hailiang.util.WebUtil;
import java.math.RoundingMode;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName StuPortraitSearchServiceImpl
 * @Description 学生画像查询
 * <AUTHOR>
 * @Date 2024/6/19 17:20
 */
@Service
@Slf4j
public class StuPortraitSearchServiceImpl implements StuPortraitSearchService {


    @Resource
    private BehaviourRecordManager behaviourRecordManager;
    @Resource
    private SaasStudentManager saasStudentManager;
    @Resource
    private StuPortraitService stuPortraitService;
    @Resource
    private SaasHistoryStudentCacheManager saasHistoryStudentCacheManager;
    @Resource
    private StuPortraitListFactory stuPortraitListFactory;
    @Resource
    private ReportBehaviourRecordService reportBehaviourRecordService;
    @Resource
    private ReportReviewTaskService reportReviewTaskService;
    @Resource
    private ReportReviewDetailService reportReviewDetailService;
    @Resource
    private PortraitDetailHandler<FillStuDataDTO> portraitDetailHandler;
    @Resource
    private SaasStudentCacheManager saasStudentCacheManager;
    @Resource
    private TargetGroupService targetGroupService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private TargetLogic targetLogic;

    @Resource
    private HelpBehaviourRecordService helpBehaviourRecordService;
    @Resource
    private InitialScoreManager initialScoreManager;
    @Resource
    private DorisStudentPortraitMapper dorisStudentPortraitMapper;


    @Override
    public Page<BehaviourStudentFileVO> pageStuPortraitInfo(StuPortraitListQuery query) {
        StuPortraitListHandler service;
        if (Objects.equals(Boolean.FALSE, query.getIsCurrentYear())) {
            service = stuPortraitListFactory.getService(StuPortraitBizTypeEnum.HISTORY);
        } else {
            // 默认走当前学年
            service = stuPortraitListFactory.getService(StuPortraitBizTypeEnum.CURRENT);
        }
        query.setStartTime(DateUtil.beginOfDay(query.getStartTime()));
        query.setEndTime(DateUtil.endOfDay(query.getEndTime()));
        return service.pageStuPortraitInfo(query);
    }

    @Override
    public StuPortraitInfoVO getStuPortraitInfo(StuPortraitQuery dto) {
        TimeInterval start = DateUtil.timer();
        log.info("【学生画像-查询学生信息接口】入参：{}", dto);
        TimeInterval TIME_INTERVAL = DateUtil.timer();

        dto.setCampusId(WebUtil.getCampusId());
        dto.setStartTime(DateUtil.beginOfDay(dto.getStartTime()));
        dto.setEndTime(DateUtil.endOfDay(dto.getEndTime()));
        StuPortraitInfoVO stuPortraitInfoVO = new StuPortraitInfoVO();
        stuPortraitInfoVO.setStudentId(dto.getStudentId());
        // 默认40个学生
        stuPortraitInfoVO.setStudentCount(40);

        SassStudentVO studentVO;
        List<SassStudentVO> sassStudentVOList = new ArrayList<>();
        //全学期
        if (Objects.equals(dto.getSchoolYear(), Constant.MINUS_ONE)) {
            dto.setStartTime(DateUtil.offset(DateUtil.beginOfDay(new Date()), DateField.YEAR, -10));
            dto.setEndTime(DateUtil.endOfDay(new Date()));
            StudentBasicInfoVO studentBasicInfoVO = saasStudentManager.getStuBasicInfoById(Long.valueOf(dto.getStudentId()));
            AssertUtil.checkNotNull(studentBasicInfoVO, "学生不存在，请关闭重新选择");
            stuPortraitInfoVO.setSex(studentBasicInfoVO.getSex());
            stuPortraitInfoVO.setStudentName(studentBasicInfoVO.getName());

            // 学生家长信息
            ParentQueryDTO parentQueryDTO = new ParentQueryDTO();
            parentQueryDTO.setStudentIds(Collections.singletonList(Convert.toLong(dto.getStudentId())));
            List<ParentInfoVO> parentInfoList = saasStudentCacheManager.queryParentInfoList(parentQueryDTO);
            if (CollUtil.isNotEmpty(parentInfoList)) {
                ParentInfoVO parentInfo = parentInfoList.get(0);
                if (CollUtil.isNotEmpty(parentInfo.getParentInfos())) {
                    Map<Integer, String> nameMap = new HashMap<>();
                    parentInfo.getParentInfos().forEach(parent -> nameMap.put(Convert.toInt(parent.getRelationCode()), parent.getName()));
                    stuPortraitInfoVO.setGuardianName(this.getParentName(parentInfo.getParentInfos().get(0).getName(), nameMap));
                }
            }
        } else {
            // 学生基础信息
            List<SassStudentVO> studentVOList = saasHistoryStudentCacheManager
                    .listStudentByStudentId(
                            WebUtil.getSchoolIdLong(),
                            WebUtil.getCampusIdLong(),
                            null,
                            null,
                            Convert.toLong(dto.getClassId()),
                            dto.getSchoolYear(),
                            Long.valueOf(dto.getStudentId()),
                            dto.getIsCurrentYear(),
                            "0",
                            null);

            AssertUtil.checkNotEmpty(studentVOList, "该学期暂无数据");

            //班级下的学生列表
            sassStudentVOList = saasHistoryStudentCacheManager
                    .listAllStudentByClassId(
                            WebUtil.getSchoolIdLong(),
                            dto.getSchoolYear(),
                            WebUtil.getCampusIdLong(),
                            Long.valueOf(dto.getClassId()),
                            dto.getIsCurrentYear(),
                            "0",
                            null,
                            null);

            AssertUtil.checkNotEmpty(sassStudentVOList, "该学期暂无数据");

            studentVO = studentVOList.get(0);
            // 家长信息
            if (CollUtil.isNotEmpty(studentVO.getParentList())) {
                Map<Integer, String> nameMap = new HashMap<>();
                studentVO.getParentList().forEach(parent -> nameMap.put(Convert.toInt(parent.getRelationCode()), parent.getParentName()));
                stuPortraitInfoVO.setGuardianName(this.getParentName(studentVO.getParentList().get(0).getParentName(), nameMap));
            }
            stuPortraitInfoVO.setHeadMasterName(studentVO.getHeadTeacherName());
            stuPortraitInfoVO.setClassName(studentVO.getClassName());
            stuPortraitInfoVO.setSex(studentVO.getSex());
            stuPortraitInfoVO.setStudentName(studentVO.getStudentName());
            stuPortraitInfoVO.setStudentCount(sassStudentVOList.size());
        }

        log.info("【学生画像-查询学生信息接口】1耗时：{}", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        //计算综合得分
        // 1.分配分
//        List<InitialScoreAllocation> initialScoreAllocations = initialScoreAllocationManager.list(dto);
        List<InitialScorePO> initialScorePOS = new ArrayList<>();
        if ("-1".equals(dto.getSchoolYear())) {
            initialScorePOS = initialScoreManager.listBySchoolIdAndCampusIdOrderByAllocationTimeDesc(WebUtil.getSchoolId(), WebUtil.getCampusId());
        } else {
            initialScorePOS =  initialScoreManager.listByBaseInfoOrderByAllocationTimeDesc(WebUtil.getTenantId(),
                    WebUtil.getSchoolId(),
                    WebUtil.getCampusId(),
                    dto.getSchoolYear(),
                    dto.getTermName());
        }

        log.info("[学生画像-查询学生信息接口] 1-1 [查询初始分] 耗时：{}", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        //家长查未发送的报告时
        Integer source = WebUtil.getSource();
        List<StuBehaviorOptionVO> behaviourGroup;
        boolean includeInitScore = true;
        //行为分
        List<StudentDailyStatisticsPO> classDailyStatistics;
        //不包含老师自建指标加分
        if (Boolean.FALSE.equals(dto.getIncludeInternalScoreFlag())) {
            dto.setDataSourceList(DataSourceEnum.getCodesFromSchoolLevel());
        }
        // B端查询
        if (Objects.isNull(source) || Objects.isNull(dto.getReviewDetailId())) {
//            behaviourGroup = behaviourRecordManager.getBehaviourGroup(dto);
            behaviourGroup = behaviourRecordManager.listBehaviourGroupDoris(dto);
//            classDailyStatistics = stuPortraitService.getClassStatistics(dto, true);
        } else {
//            classDailyStatistics = stuPortraitService.getClassStatistics(dto, false);
            if (Objects.equals(source, Constant.TWO) && Objects.equals(dto.getSendType(), Constant.ZERO)) {
                behaviourGroup = getByH5ForCheck(dto);
                includeInitScore = false;
            } else {
                // H5端家长查询或者老师查询已发送的报告
                behaviourGroup = getByH5ForParent(dto);
            }
        }
        log.info("[学生画像-查询学生信息接口] 1-2 [查询积分明细] 耗时：{}", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        // 实时从Doris里面查行为记录表
        classDailyStatistics = stuPortraitService.listStudentDailyStatisticsDoris(dto);
        log.info("【学生画像-查询学生信息接口】2耗时：{}", TIME_INTERVAL.intervalMs());
        if (Objects.nonNull(dto.getIncludeHelpBehaviour()) && dto.getIncludeHelpBehaviour()) {
            TIME_INTERVAL.restart();
            //帮扶行为
            helpBehaviourRecordService.getAndMergeHelpBehaviourClassStatistics(classDailyStatistics, dto);
            log.info("[学生画像-查询学生信息接口] [学生帮扶数据填充] 2-1耗时：{}", TIME_INTERVAL.intervalMs());
        }
        TIME_INTERVAL.restart();
        FillStuDataDTO fillStuDataDTO = new FillStuDataDTO();
        fillStuDataDTO.setQueryDTO(dto);
        fillStuDataDTO.setStatisticsList(classDailyStatistics);
        fillStuDataDTO.setBehaviourGroup(behaviourGroup);
        fillStuDataDTO.setInitialScorePOs(initialScorePOS);
        if("-1".equals(dto.getSchoolYear())){
            fillStuDataDTO.setAllTermFlag(true);
        }
        //班级学生
        List<BehaviourRecordSynthesisScoreRankVO> ranks = stuPortraitService.getRanks(sassStudentVOList, initialScorePOS, classDailyStatistics);
        fillStuDataDTO.setRankList(ranks);
        fillStuDataDTO.setStuPortraitInfoVO(stuPortraitInfoVO);
        // TODO: 2024/11/8 初始分重新设计，无需加入初始分，暂时注释
//        if (includeInitScore) {
//            fillInitOption(initialScoreiAllocations, dataBehaviourList);
//            fillStuDataDTO.setDataBehaviourList(dataBehaviourList);
//        }

        log.info("【学生画像-查询学生信息接口】3耗时：{}", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        //2.综合得分
        portraitDetailHandler.getHandler(StuPortraitBizTypeEnum.SYNTHESIS_SCORE).fill(fillStuDataDTO);

        log.info("【学生画像-查询学生信息接口】4耗时：{}", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        //3.五育雷达
        portraitDetailHandler.getHandler(StuPortraitBizTypeEnum.QUALITY).fill(fillStuDataDTO);

        log.info("【学生画像-查询学生信息接口】5耗时：{}", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        //4.成长趋势
        if (!dto.isSimple()) {
            //2024-09-05 非简版查询成长趋势
            portraitDetailHandler.getHandler(StuPortraitBizTypeEnum.GROWTH_TREND).fill(fillStuDataDTO);
            log.info("【学生画像-查询学生信息接口】6耗时：{}", TIME_INTERVAL.intervalMs());
        }
        TIME_INTERVAL.restart();
        //5.五育明细
        portraitDetailHandler.getHandler(StuPortraitBizTypeEnum.DETAIL).fill(fillStuDataDTO);

        log.info("【学生画像-查询学生信息接口】7耗时：{}", TIME_INTERVAL.intervalMs());
        log.info("【学生画像-查询学生信息接口】总耗时:{}", start.intervalMs());
        return stuPortraitInfoVO;
    }

    private List<StuBehaviorOptionVO> getByH5ForParent(StuPortraitQuery dto) {
        List<ReportBehaviourRecord> reportBehaviourRecords = reportBehaviourRecordService.listSendRecordByDetailList(dto.getReviewDetailId());
        //行为记录表需要的id
        List<Long> behaviourRecordIds = new ArrayList<>();
        //帮扶记录表需要的id
        List<Long> helpBehaviourRecordIds = new ArrayList<>();
        for (ReportBehaviourRecord reportBehaviourRecord : reportBehaviourRecords) {
            if (Objects.isNull(reportBehaviourRecord.getBehaviourRecordId())) {
                continue;
            }
            if (Objects.equals(reportBehaviourRecord.getRecordType(), ReportBehaviourTypeEnum.BEHAVIOUR_RECORD.getCode())) {
                behaviourRecordIds.add(reportBehaviourRecord.getBehaviourRecordId());
            } else if (Objects.equals(reportBehaviourRecord.getRecordType(), ReportBehaviourTypeEnum.HELP_BEHAVIOUR_RECORD.getCode())) {
                helpBehaviourRecordIds.add(reportBehaviourRecord.getBehaviourRecordId());
            }
        }
        log.warn("getByH5ForParent方法查询到的行为记录表id:{},帮扶记录表id:{}", JSONObject.toJSONString(behaviourRecordIds), JSONObject.toJSONString(helpBehaviourRecordIds));
        if (CollUtil.isNotEmpty(behaviourRecordIds) || CollectionUtils.isNotEmpty(helpBehaviourRecordIds)) {
            dto.setIds(behaviourRecordIds);
            dto.setHelpBehaviourRecordIds(helpBehaviourRecordIds);
            return behaviourRecordManager.listBehaviourGroupDorisForIds(dto);
        }
        return Collections.emptyList();
    }

    private List<StuBehaviorOptionVO> getByH5ForCheck(StuPortraitQuery dto) {
        List<StuBehaviorOptionVO> behaviourGroup;
        Assert.isTrue(dto.getReviewDetailId() != null, () -> new BizException("审核明细id不能为空"));
        List<ReportBehaviourRecord> reportBehaviourRecords = reportBehaviourRecordService.listNoSendRecordByDetailList(dto.getReviewDetailId());
        List<Long> unNeedBehaviourRecordIds = new ArrayList<>();
        List<Long> unNeedHelpBehaviourRecordIds = new ArrayList<>();
        for (ReportBehaviourRecord reportBehaviourRecord : reportBehaviourRecords) {
            if (Objects.equals(reportBehaviourRecord.getRecordType(), ReportBehaviourTypeEnum.BEHAVIOUR_RECORD.getCode())) {
                unNeedBehaviourRecordIds.add(reportBehaviourRecord.getBehaviourRecordId());
            } else if (Objects.equals(reportBehaviourRecord.getRecordType(), ReportBehaviourTypeEnum.HELP_BEHAVIOUR_RECORD.getCode())) {
                unNeedHelpBehaviourRecordIds.add(reportBehaviourRecord.getBehaviourRecordId());
            }
        }
        Assert.notNull(dto.getReviewDetailId(), () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL_ID));

        ReportReviewDetail reviewDetail = reportReviewDetailService.getById(dto.getReviewDetailId());
        Assert.notNull(reviewDetail, () -> new BizException(ReportErrorEnum.EMPTY_TASK_DETAIL));

        ReportReviewTask reportReviewTask = reportReviewTaskService.getById(reviewDetail.getReviewTaskId());
        Assert.notNull(reportReviewTask, () -> new BizException(ReportErrorEnum.EMPTY_TASK));

        Assert.isTrue(reviewDetail.getPushModule().contains(Convert.toStr(ReportPushRuleEnum.DETAIL.getCode())),
                () -> new BizException(ReportErrorEnum.GET_DETAIL_ERROR));

        //学生行为明细(查全部)
        dto.setStudentId(reviewDetail.getStudentId());
        dto.setUnNeedBehaviourRecordIds(unNeedBehaviourRecordIds);
        dto.setUnNeedHelpBehaviourRecordIds(unNeedHelpBehaviourRecordIds);
        behaviourGroup = behaviourRecordManager.listBehaviourGroupDoris(dto);
        return behaviourGroup;
    }

    @Override
    public StuBehaviorInfoVO getStuBehaviorInfo(StuPortraitQuery dto) {
        log.info("【学生画像-查询学生行为接口】,入参{}", dto);
        TimeInterval start = DateUtil.timer();
        dto.setCampusId(WebUtil.getCampusId());
        dto.setStartTime(DateUtil.beginOfDay(dto.getStartTime()));
        dto.setEndTime(DateUtil.endOfDay(dto.getEndTime()));
        StuBehaviorInfoVO stuBehaviorInfoVO = new StuBehaviorInfoVO();
        stuBehaviorInfoVO.setStudentId(dto.getStudentId());
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        TIME_INTERVAL.restart();

        //全学期
        String studentName;
        if (Objects.equals(dto.getSchoolYear(), Constant.MINUS_ONE)) {
            dto.setStartTime(DateUtil.offset(DateUtil.beginOfDay(new Date()), DateField.YEAR, -10));
            dto.setEndTime(DateUtil.endOfDay(new Date()));
            StudentBasicInfoVO studentBasicInfoVO = saasStudentManager.getStuBasicInfoById(Long.valueOf(dto.getStudentId()));
            AssertUtil.checkNotNull(studentBasicInfoVO, "该学期暂无数据");
            studentName = studentBasicInfoVO.getName();
        } else {
            // 学生基础信息
            List<SassStudentVO> studentVOList = saasHistoryStudentCacheManager
                    .listStudentByStudentId(
                            WebUtil.getSchoolIdLong(),
                            WebUtil.getCampusIdLong(),
                            null,
                            null,
                            Convert.toLong(dto.getClassId()),
                            dto.getSchoolYear(),
                            Long.valueOf(dto.getStudentId()),
                            dto.getIsCurrentYear(),
                            "0",
                            null);

            AssertUtil.checkNotEmpty(studentVOList, "该学期暂无数据");
            studentName = studentVOList.get(0).getStudentName();
        }
        log.warn("【学生画像】-【查询学生信息接口】-1.1，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        //不包含老师自建指标加分
        if (Boolean.FALSE.equals(dto.getIncludeInternalScoreFlag())) {
            dto.setDataSourceList(DataSourceEnum.getCodesFromSchoolLevel());
        }
        //指标数据
//        List<StuBehaviorOptionVO> stuBehaviorOptionVOList = behaviourRecordManager.listStuBehaviourOption(dto);
        List<StuBehaviorOptionVO> stuBehaviorOptionVOList = this.getStuBehaviorOptionList(dto);
        log.warn("【学生画像】-【查询学生指标数据接口】-1.2，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        if (CollUtil.isEmpty(stuBehaviorOptionVOList)) {
            return stuBehaviorInfoVO;
        }
        //历史数据（包含今天）
//        List<StudentDailyStatisticsPO> classStatistics = stuPortraitService.getClassStatistics(dto, true);
        List<StudentDailyStatisticsPO> classStatistics = stuPortraitService.listStudentDailyStatisticsDoris(dto);
        if (Objects.nonNull(dto.getIncludeHelpBehaviour()) && dto.getIncludeHelpBehaviour()) {
            //帮扶行为
            helpBehaviourRecordService.getAndMergeHelpBehaviourClassStatistics(classStatistics, dto);
        }
        log.warn("【学生画像】-【查询学生历史点评记录数据接口】-1.3，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        FillStuDataDTO fillStuDataDTO = new FillStuDataDTO();
        fillStuDataDTO.setQueryDTO(dto);
        fillStuDataDTO.setStatisticsList(classStatistics);
        fillStuDataDTO.setBehaviourGroup(stuBehaviorOptionVOList);
        fillStuDataDTO.setStuBehaviorInfoVO(stuBehaviorInfoVO);

        // 行为表现
        portraitDetailHandler.getHandler(StuPortraitBizTypeEnum.EXPRESS).fill(fillStuDataDTO);
        // 智能评价
        portraitDetailHandler.getHandler(StuPortraitBizTypeEnum.EVALUATION).fill(fillStuDataDTO);
        BehaviourIntelligentEvaluationVO evaluationVO = stuBehaviorInfoVO.getEvaluationVO();
        evaluationVO.setStartTime(dto.getStartTime());
        evaluationVO.setEndTime(dto.getEndTime());
        evaluationVO.setStudentName(studentName);
        // 评价占比-饼图
        if (!dto.isSimple()) {
            //2024-09-05 非简版-查询评价占比
            // 评价占比部分只统计五育 不统计其他育
            portraitDetailHandler.getHandler(StuPortraitBizTypeEnum.ANALYSIS).fill(fillStuDataDTO);
        }
        log.info("【学生画像】-【学生数据填充】-1.4，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        log.info("【学生画像-查询学生行为接口】,总耗时:{}", start.intervalMs());
        return stuBehaviorInfoVO;
    }

    @Override
    public List<BehaviourStudentExportResponse> listStudentPortraitExport(StuPortraitListQuery query) {
        String termName = query.getTermName();
        String schoolYear = query.getSchoolYear();
        DateTime startTime = DateUtil.beginOfDay(query.getStartTime());
        DateTime endTime = DateUtil.endOfDay(query.getEndTime());
        String gradeId = query.getGradeId();
        String classId = query.getClassId();
        Integer moduleCode = query.getModuleCode();

        StuPortraitQuery dto = new StuPortraitQuery();
        dto.setCampusId(WebUtil.getCampusId());
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setSchoolYear(schoolYear);
        dto.setTermName(termName);
        dto.setClassId(gradeId);
        dto.setGradeId(classId);
        dto.setModuleCode(moduleCode);
        if(Objects.equals(query.getIncludeInternalScoreFlag(), Boolean.TRUE)){
            dto.setIncludeInternalScoreFlag(query.getIncludeInternalScoreFlag());
        }

        String tenantId = WebUtil.getTenantId();
        String schoolId = WebUtil.getSchoolId();
        String campusId = WebUtil.getCampusId();

        // 初始分
        List<InitialScorePO> initialScorePOS = initialScoreManager.listByBaseInfoOrderByAllocationTimeDesc(tenantId,
                schoolId, campusId, schoolYear, termName);

        List<BehaviourStudentExportVO> behaviourStudentExportVOS = dorisStudentPortraitMapper.listStudentPortraitExport(
                dto);
        // 组装返回数据
        return this.convertBehaviourStudentExportResponse(behaviourStudentExportVOS, query, initialScorePOS);
    }

    /**
     * 获取学生列表导出数据
     * @param behaviourStudentExportVOS
     * @param query
     * @return
     */
    private List<BehaviourStudentExportResponse> convertBehaviourStudentExportResponse(List<BehaviourStudentExportVO> behaviourStudentExportVOS,
                                                                                       StuPortraitListQuery query,
                                                                                       List<InitialScorePO> initialScorePOS) {
        List<BehaviourStudentExportResponse> result = new ArrayList<>();
        String studentName = query.getNameLike();
        Map<String, BehaviourStudentExportVO> behaviourStudentExportVOMap = behaviourStudentExportVOS.stream()
                .collect(Collectors.toMap(BehaviourStudentExportVO::getStudentId, Function.identity(), (k1, k2) -> k1));

        // 查询学生
        StuPortraitListHandler service;
        if (Objects.equals(Boolean.FALSE, query.getIsCurrentYear())) {
            service = stuPortraitListFactory.getService(StuPortraitBizTypeEnum.HISTORY);
        } else {
            // 默认走当前学年
            service = stuPortraitListFactory.getService(StuPortraitBizTypeEnum.CURRENT);
        }
        List<EduStudentInfoVO> studentInfoVOS = service.listStudentInfo(query);

        // 按名称筛选
        if(CharSequenceUtil.isNotBlank(studentName)){
            studentInfoVOS = studentInfoVOS.stream()
                    .filter(item -> item.getStudentName().contains(studentName)).collect(Collectors.toList());
        }


        for (EduStudentInfoVO eduStudentInfoVO : studentInfoVOS) {
            BehaviourStudentExportResponse behaviourStudentExportResponse = new BehaviourStudentExportResponse();
            Long studentId = eduStudentInfoVO.getId();

            BehaviourStudentExportVO behaviourStudentExportVO = behaviourStudentExportVOMap.get(Convert.toStr(studentId));
            behaviourStudentExportResponse.setClassName(eduStudentInfoVO.getClassName());
            behaviourStudentExportResponse.setStudentName(eduStudentInfoVO.getStudentName());
            behaviourStudentExportResponse.setGradeName(eduStudentInfoVO.getGradeName());
            behaviourStudentExportResponse.setTotalScore("0");
            if(Objects.nonNull(behaviourStudentExportVO)){
                this.buildBehaviourStudentExportResponse(behaviourStudentExportVO, behaviourStudentExportResponse, query);
            }else{
                // 没有点评记录获取初始分
                if(CollUtil.isNotEmpty(initialScorePOS)){
                    behaviourStudentExportResponse.setTotalScore(this.getInitScore(initialScorePOS, ModuleEnum.OTHER.getCode()).toString());
                    behaviourStudentExportResponse.setInitScore(this.getInitScore(initialScorePOS, ModuleEnum.OTHER.getCode()).toString());
                    behaviourStudentExportResponse.setMoralScore(this.getInitScore(initialScorePOS, ModuleEnum.MORAL.getCode()).toString());
                    behaviourStudentExportResponse.setIntellectualScore(this.getInitScore(initialScorePOS, ModuleEnum.WISDOM.getCode()).toString());
                    behaviourStudentExportResponse.setPhysicalScore(this.getInitScore(initialScorePOS, ModuleEnum.SPORT.getCode()).toString());
                    behaviourStudentExportResponse.setAestheticScore(this.getInitScore(initialScorePOS, ModuleEnum.PRETTY.getCode()).toString());
                    behaviourStudentExportResponse.setLaborScore(this.getInitScore(initialScorePOS, ModuleEnum.WORK.getCode()).toString());
                }
            }
            result.add(behaviourStudentExportResponse);
        }

        // 排序
        if(Objects.equals(query.getSortType(), 1)){
            result.sort(Comparator.comparing(item -> Convert.toBigDecimal(item.getTotalScore())));
        }else {
            result.sort((o1, o2) -> new BigDecimal(o2.getTotalScore()).compareTo(new BigDecimal(o1.getTotalScore())));
        }
        return result;
    }

    /**
     * 获取初始分情况
     * @param initialScorePOS
     * @param type
     * @return
     */
    private BigDecimal getInitScore(List<InitialScorePO> initialScorePOS, Integer type){
        InitialScorePO initialScorePO = initialScorePOS.stream()
                .filter(item -> Objects.equals(item.getInitialScoreType(), type)).findFirst().orElse(null);

        return Objects.nonNull(initialScorePO) ? initialScorePO.getInitialScore() : BigDecimal.ZERO;
    }

    /**
     * 填充导出数据
     * @param behaviourStudentExportVO
     * @param behaviourStudentExportResponse
     * @param query
     */
    private void buildBehaviourStudentExportResponse(BehaviourStudentExportVO behaviourStudentExportVO,
                                                     BehaviourStudentExportResponse behaviourStudentExportResponse,
                                                     StuPortraitListQuery query) {
        // 如果分值为null 则设置为0
        BigDecimal totalAddScore = behaviourStudentExportVO.getTotalAddScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getTotalAddScore();
        BigDecimal totalMinusScore = behaviourStudentExportVO.getTotalMinusScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getTotalMinusScore();
        BigDecimal moralScore = behaviourStudentExportVO.getMoralScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getMoralScore();
        BigDecimal intellectualScore = behaviourStudentExportVO.getIntellectualScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getIntellectualScore();
        BigDecimal physicalScore = behaviourStudentExportVO.getPhysicalScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getPhysicalScore();
        BigDecimal aestheticScore = behaviourStudentExportVO.getAestheticScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getAestheticScore();
        BigDecimal laborScore = behaviourStudentExportVO.getLaborScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getLaborScore();
        BigDecimal schoolScore = behaviourStudentExportVO.getSchoolScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getSchoolScore();
        BigDecimal personalScore = behaviourStudentExportVO.getPersonalScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getPersonalScore();
        BigDecimal eisTotalScore = behaviourStudentExportVO.getEisTotalScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEisTotalScore();
        BigDecimal eisMoralScore = behaviourStudentExportVO.getEisMoralScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEisMoralScore();
        BigDecimal eisIntellectualScore = behaviourStudentExportVO.getEisIntellectualScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEisIntellectualScore();
        BigDecimal eisPhysicalScore = behaviourStudentExportVO.getEisPhysicalScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEisPhysicalScore();
        BigDecimal eisAestheticScore = behaviourStudentExportVO.getEisAestheticScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEisAestheticScore();
        BigDecimal eisLaborScore = behaviourStudentExportVO.getEisLaborScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEisLaborScore();
        BigDecimal ehbrTotalAddScore = behaviourStudentExportVO.getEhbrTotalAddScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEhbrTotalAddScore();
        BigDecimal ehbrTotalMinusScore = behaviourStudentExportVO.getEhbrTotalMinusScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEhbrTotalMinusScore();
        BigDecimal ehbrMoralScore = behaviourStudentExportVO.getEhbrMoralScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEhbrMoralScore();
        BigDecimal ehbrIntellectualScore = behaviourStudentExportVO.getEhbrIntellectualScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEhbrIntellectualScore();
        BigDecimal ehbrPhysicalScore = behaviourStudentExportVO.getEhbrPhysicalScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEhbrPhysicalScore();
        BigDecimal ehbrAestheticScore = behaviourStudentExportVO.getEhbrAestheticScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEhbrAestheticScore();
        BigDecimal ehbrLaborScore = behaviourStudentExportVO.getEhbrLaborScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEhbrLaborScore();
        BigDecimal ehbrSchoolScore = behaviourStudentExportVO.getEhbrSchoolScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEhbrSchoolScore();
        BigDecimal ehbrPersonalScore = behaviourStudentExportVO.getEhbrPersonalScore() == null ? BigDecimal.ZERO : behaviourStudentExportVO.getEhbrPersonalScore();
        // 查看帮扶积分
        if(Objects.equals(query.getIncludeHelpBehaviour(), Boolean.TRUE) && Objects.equals(query.getIncludeInternalScoreFlag(), Boolean.FALSE)){
            behaviourStudentExportResponse.setTotalScore(schoolScore.add(eisTotalScore).add(ehbrSchoolScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setAddScore(totalAddScore.add(ehbrTotalAddScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setMinusScore(totalMinusScore.add(ehbrTotalMinusScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setMoralScore(moralScore.add(eisMoralScore).add(ehbrMoralScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setIntellectualScore(intellectualScore.add(eisIntellectualScore).add(ehbrIntellectualScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setPhysicalScore(physicalScore.add(eisPhysicalScore).setScale(2, RoundingMode.HALF_UP).add(ehbrPhysicalScore).toString());
            behaviourStudentExportResponse.setAestheticScore(aestheticScore.add(eisAestheticScore).setScale(2, RoundingMode.HALF_UP).add(ehbrAestheticScore).toString());
            behaviourStudentExportResponse.setLaborScore(laborScore.add(eisLaborScore).setScale(2, RoundingMode.HALF_UP).add(ehbrLaborScore).toString());
            behaviourStudentExportResponse.setInitScore(eisTotalScore.setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setSchoolScore(schoolScore.add(ehbrSchoolScore).toString());
            behaviourStudentExportResponse.setPersonalScore("0");
            behaviourStudentExportResponse.setHelpScore(ehbrTotalAddScore.add(ehbrTotalMinusScore).setScale(2, RoundingMode.HALF_UP).toString());
        }else if (Objects.equals(query.getIncludeHelpBehaviour(), Boolean.FALSE) && Objects.equals(query.getIncludeInternalScoreFlag(), Boolean.TRUE)){
            // 查看老师自建指标积分
            behaviourStudentExportResponse.setTotalScore(totalAddScore.add(totalMinusScore).add(eisTotalScore).toString());
            behaviourStudentExportResponse.setAddScore(totalAddScore.toString());
            behaviourStudentExportResponse.setMinusScore(totalMinusScore.toString());
            behaviourStudentExportResponse.setMoralScore(moralScore.add(eisMoralScore).toString());
            behaviourStudentExportResponse.setIntellectualScore(intellectualScore.add(eisIntellectualScore).toString());
            behaviourStudentExportResponse.setPhysicalScore(physicalScore.add(eisPhysicalScore).toString());
            behaviourStudentExportResponse.setAestheticScore(aestheticScore.add(eisAestheticScore).toString());
            behaviourStudentExportResponse.setLaborScore(laborScore.add(eisLaborScore).toString());
            behaviourStudentExportResponse.setInitScore(eisTotalScore.toString());
            behaviourStudentExportResponse.setSchoolScore(schoolScore.toString());
            behaviourStudentExportResponse.setPersonalScore(personalScore.toString());
            behaviourStudentExportResponse.setHelpScore("0");
        }else if(Objects.equals(query.getIncludeHelpBehaviour(), Boolean.TRUE) && Objects.equals(query.getIncludeInternalScoreFlag(), Boolean.TRUE)){
            // 同时查看帮扶积分和自建指标分
            behaviourStudentExportResponse.setTotalScore(totalAddScore.add(totalMinusScore).add(eisTotalScore).add(ehbrTotalAddScore).add(ehbrTotalMinusScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setAddScore(totalAddScore.add(ehbrTotalAddScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setMinusScore(totalMinusScore.add(ehbrTotalMinusScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setMoralScore(moralScore.add(eisMoralScore).add(ehbrMoralScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setIntellectualScore(intellectualScore.add(eisIntellectualScore).add(ehbrIntellectualScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setPhysicalScore(physicalScore.add(eisPhysicalScore).add(ehbrPhysicalScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setAestheticScore(aestheticScore.add(eisAestheticScore).add(ehbrAestheticScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setLaborScore(laborScore.add(eisLaborScore).add(ehbrLaborScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setInitScore(eisTotalScore.toString());
            behaviourStudentExportResponse.setSchoolScore(schoolScore.add(ehbrSchoolScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setPersonalScore(personalScore.add(ehbrPersonalScore).setScale(2, RoundingMode.HALF_UP).toString());
            behaviourStudentExportResponse.setHelpScore(ehbrTotalAddScore.add(ehbrTotalMinusScore).setScale(2, RoundingMode.HALF_UP).toString());
        }else{
            // 不查看帮扶分也不查看自建指标分
            behaviourStudentExportResponse.setTotalScore(totalAddScore.add(totalMinusScore).add(eisTotalScore).toString());
            behaviourStudentExportResponse.setAddScore(totalAddScore.toString());
            behaviourStudentExportResponse.setMinusScore(totalMinusScore.toString());
            behaviourStudentExportResponse.setMoralScore(moralScore.add(eisMoralScore).toString());
            behaviourStudentExportResponse.setIntellectualScore(intellectualScore.add(eisIntellectualScore).toString());
            behaviourStudentExportResponse.setPhysicalScore(physicalScore.add(eisPhysicalScore).toString());
            behaviourStudentExportResponse.setAestheticScore(aestheticScore.add(eisAestheticScore).toString());
            behaviourStudentExportResponse.setLaborScore(laborScore.add(eisLaborScore).toString());
            behaviourStudentExportResponse.setInitScore(eisTotalScore.toString());
            behaviourStudentExportResponse.setSchoolScore(schoolScore.toString());
            behaviourStudentExportResponse.setPersonalScore(personalScore.toString());
            behaviourStudentExportResponse.setHelpScore("0");
        }
    }

    private List<StuBehaviorOptionVO> getStuBehaviorOptionList(StuPortraitQuery dto) {
        TimeInterval TIME_INTERVAL = DateUtil.timer();
        Boolean detailFlag = false;
        if (Objects.nonNull(dto.getReviewDetailId()) && dto.getReviewDetailId() > Constant.ZERO) {
            List<ReportBehaviourRecord> reportBehaviourRecords = reportBehaviourRecordService.listSendRecordByDetailList(dto.getReviewDetailId());
            //行为记录表需要的id
            List<Long> behaviourRecordIds = new ArrayList<>();
            //帮扶记录表需要的id
            List<Long> helpBehaviourRecordIds = new ArrayList<>();
            for (ReportBehaviourRecord reportBehaviourRecord : reportBehaviourRecords) {
                if (Objects.isNull(reportBehaviourRecord.getBehaviourRecordId())) {
                    continue;
                }
                if (Objects.equals(reportBehaviourRecord.getRecordType(), ReportBehaviourTypeEnum.BEHAVIOUR_RECORD.getCode())) {
                    behaviourRecordIds.add(reportBehaviourRecord.getBehaviourRecordId());
                } else if (Objects.equals(reportBehaviourRecord.getRecordType(), ReportBehaviourTypeEnum.HELP_BEHAVIOUR_RECORD.getCode())) {
                    helpBehaviourRecordIds.add(reportBehaviourRecord.getBehaviourRecordId());
                }
            }
            detailFlag = true;
            dto.setIds(behaviourRecordIds);
            dto.setHelpBehaviourRecordIds(helpBehaviourRecordIds);
        }
        log.warn("【学生画像】-【填充报告单审核详情】-1.2.1- 【是否填充：[{}]】【消耗时长：[{}]】", detailFlag, TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();

        List<BehaviourRecord> behaviourRecords = behaviourRecordManager.list(new LambdaQueryWrapper<BehaviourRecord>()
                .eq(BehaviourRecord::getCampusId, dto.getCampusId())
                .eq(BehaviourRecord::getClassId, dto.getClassId())
                .eq(BehaviourRecord::getStudentId, dto.getStudentId())
                .ge(ObjectUtil.isNotNull(dto.getStartTime()), BehaviourRecord::getSubmitTime, dto.getStartTime())
                .le(ObjectUtil.isNotNull(dto.getEndTime()), BehaviourRecord::getSubmitTime, dto.getEndTime())
                .in(CollectionUtils.isNotEmpty(dto.getIds()), BehaviourRecord::getId, dto.getIds())
                .in(CollUtil.isNotEmpty(dto.getDataSourceList()), BehaviourRecord::getDataSource, dto.getDataSourceList()));

        // 过滤不加入学生画像综合得分和点评记录的数据
        behaviourRecords = behaviourRecords
                .stream()
                .filter(item -> !Objects.equals(item.getNotPartCount(), 1))
                .collect(Collectors.toList());


        log.warn("【学生画像】-【查询学生指标数据接口】-1.2.2-【当前学生行为记录条数：{}】，消耗时长：[{}]", behaviourRecords.size(), TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        if (Objects.nonNull(dto.getIncludeHelpBehaviour()) && dto.getIncludeHelpBehaviour()) {
            HelpBehaviourRecordQueryDTO helpBehaviourRecordQueryDTO = new HelpBehaviourRecordQueryDTO();
            helpBehaviourRecordQueryDTO.setStudentId(dto.getStudentId());
            helpBehaviourRecordQueryDTO.setStartTime(dto.getStartTime());
            helpBehaviourRecordQueryDTO.setEndTime(dto.getEndTime());
            helpBehaviourRecordQueryDTO.setIdList(dto.getHelpBehaviourRecordIds());
            if (Boolean.FALSE.equals(dto.getIncludeInternalScoreFlag())) {
                helpBehaviourRecordQueryDTO.setIncludeInternalScoreFlag(false);
            }
            List<EvaluateHelpBehaviourRecordPO> evaluateHelpBehaviourRecordPOS = helpBehaviourRecordService.listByStudent(helpBehaviourRecordQueryDTO);
            if (CollectionUtils.isNotEmpty(evaluateHelpBehaviourRecordPOS)) {
                List<BehaviourRecord> behaviourRecordList = HelpBehaviourRecordConvert.toBehaviourRecordListNoScale(evaluateHelpBehaviourRecordPOS);
                behaviourRecords.addAll(behaviourRecordList);
            }
        }
        log.warn("【学生画像】-【查询学生指标数据接口】-1.2.3- ,是否统计学生帮扶记录:{},【当前学生帮扶行为记录条数：{}】，消耗时长：[{}]", dto.getIncludeHelpBehaviour(), behaviourRecords.size(), TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        if (CollUtil.isEmpty(behaviourRecords)) {
            return Collections.emptyList();
        }

        //极速点评过滤掉非校标的加分
        if (Boolean.FALSE.equals(dto.getIncludeInternalScoreFlag())) {
            behaviourRecords = behaviourRecords.stream()
                    .filter(obj -> !(DataSourceEnum.INTERNAL_DRIVE_SPEED.getCode().equals(obj.getDataSource())
                            && (InfoTypeEnum.SPEED_OPTION_SCHOOL_TEACHER.getCode().equals(obj.getInfoType()) || InfoTypeEnum.SPEED_OPTION_TEACHER_TEACHER.getCode().equals(obj.getInfoType())))
                            && !(DataSourceEnum.EVALUATE_SPEED.getCode().equals(obj.getDataSource())
                            && (InfoTypeEnum.SPEED_OPTION_SCHOOL_TEACHER.getCode().equals(obj.getInfoType()) || InfoTypeEnum.SPEED_OPTION_TEACHER_TEACHER.getCode().equals(obj.getInfoType()))))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(dto.getUnNeedBehaviourRecordIds())) {
            behaviourRecords = behaviourRecords.stream().filter(d -> !dto.getUnNeedBehaviourRecordIds().contains(d.getId())).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(behaviourRecords)) {
            return Collections.emptyList();
        }
        List<StuBehaviorOptionVO> stuBehaviorOptionVOList = new ArrayList<>();
        // 将behaviourRecords聚合分组 key为moduleCode、targetId、score、optionId、infoName组合以_拼接 value为list数组
        Map<String, List<BehaviourRecord>> behaviourRecordMap = behaviourRecords.stream().collect(Collectors.groupingBy(d -> d.getModuleCode() + "_" + d.getTargetId() + "_" + d.getScore() + "_" + d.getOptionId() + "_" + d.getInfoName()));

        log.warn("【学生画像】-【查询学生指标数据接口】-1.2.4，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();
        Map<Long, Target> targetMap = new HashMap<>();
        Map<Long/**targetId*/, TargetGroup> targetGroupMap = new HashMap<>();
        // 填充指标信息
        fillTargetMap(behaviourRecords, targetMap, targetGroupMap);
        for (Map.Entry<String, List<BehaviourRecord>> entry : behaviourRecordMap.entrySet()) {
            StuBehaviorOptionVO stuBehaviorOptionVO = new StuBehaviorOptionVO();
            List<BehaviourRecord> list = entry.getValue();
            if (CollUtil.isEmpty(list)) {
                continue;
            }

            // 总分
            BehaviourRecord behaviourRecord = list.get(0);
            // 获取指标信息
//            Target target = redisUtil.getOrAdd(RedisKeyConstants.EVALUATE_TARGET_ID + behaviourRecord.getTargetId(), () -> targetLogic.getByIdWithOutDeleted(behaviourRecord.getTargetId()), CacheConstants.ONE_HOUR);
//            TargetGroup targetGroup = null;
//            if (ObjectUtil.isNotNull(target) && ObjectUtil.isNotNull(target.getGroupId())) {
//                // 获取分组信息
//                targetGroup = redisUtil.getOrAdd(RedisKeyConstants.EVALUATE_GROUP_ID + target.getGroupId(), () -> targetGroupService.get(target.getGroupId()), CacheConstants.ONE_HOUR);
//            }
            Target target = null;
            TargetGroup targetGroup = null;
            if (Objects.nonNull(behaviourRecord.getTargetId())) {
                target = targetMap.get(behaviourRecord.getTargetId());
                targetGroup = targetGroupMap.get(behaviourRecord.getTargetId());
            }
            stuBehaviorOptionVO.setModuleCode(behaviourRecord.getModuleCode());
            stuBehaviorOptionVO.setModuleName(ModuleEnum.getModuleName(behaviourRecord.getModuleCode()));
            stuBehaviorOptionVO.setTargetId(behaviourRecord.getTargetId());
            stuBehaviorOptionVO.setTargetName(behaviourRecord.getTargetName());
            stuBehaviorOptionVO.setInfoName(this.getInfoName(behaviourRecord));
            stuBehaviorOptionVO.setTemplateId(behaviourRecord.getTemplateId());
            stuBehaviorOptionVO.setOptionId(behaviourRecord.getOptionId());
            stuBehaviorOptionVO.setInfoType(behaviourRecord.getInfoType());
            stuBehaviorOptionVO.setScoreType(behaviourRecord.getScoreType());
            stuBehaviorOptionVO.setId(Convert.toStr(behaviourRecord.getId()));
            stuBehaviorOptionVO.setSubmitCount(list.size());
            stuBehaviorOptionVO.setBehaviourScoreNoScale(list.stream().filter(d -> ObjectUtil.isNotNull(d.getScore())).map(BehaviourRecord::getScore).reduce(BigDecimal.ZERO, BigDecimal::add));
            stuBehaviorOptionVO.setBehaviourScore(NumUtil.formatFloatNumber(stuBehaviorOptionVO.getBehaviourScoreNoScale()));
            stuBehaviorOptionVO.setTargetSortIndex(ObjectUtil.isNotNull(target) ? target.getSortIndex() : 1000);
            stuBehaviorOptionVO.setGroupSortIndex(ObjectUtil.isAllNotEmpty(target, targetGroup) ? targetGroup.getSortIndex() : 1000);// NOSONAR
            stuBehaviorOptionVO.setDataSource(behaviourRecord.getDataSource());

            stuBehaviorOptionVOList.add(stuBehaviorOptionVO);
        }

        log.info("【学生画像】-【查询学生指标数据接口】-1.2.5-【填充行为记录选项】，消耗时长：[{}]", TIME_INTERVAL.intervalMs());
        TIME_INTERVAL.restart();


        // stuBehaviorOptionVOList根据behaviourScore 降序排列
        stuBehaviorOptionVOList.sort(Comparator.comparing(StuBehaviorOptionVO::getBehaviourScore).reversed());
        return stuBehaviorOptionVOList;
    }

    /**
     * 构建指标映射
     *
     * @param behaviourRecords
     * @param targetMap
     * @param targetGroupMap
     */
    private void fillTargetMap(List<BehaviourRecord> behaviourRecords, Map<Long, Target> targetMap, Map<Long, TargetGroup> targetGroupMap) {
        if (CollectionUtils.isEmpty(behaviourRecords)) {
            return;
        }
        List<Long> targetIdList = behaviourRecords.stream().filter(b -> Objects.nonNull(b.getTargetId())).map(BehaviourRecord::getTargetId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(targetIdList)) {
            return;
        }
        //  查询指标信息
        List<Target> targets = targetLogic.listByIds(targetIdList);
        if (CollectionUtils.isEmpty(targets)) {
            return;
        }
        //指标与分组映射
        Map<Long/**targetGroupId*/, List<Long/**targetId*/>> targetWithGroupMap = new HashMap<>();
        // 获取分组信息
        for (Target target : targets) {
            targetMap.put(target.getId(), target);
            if (Objects.nonNull(target.getGroupId())) {
                targetWithGroupMap.computeIfAbsent(target.getGroupId(), key -> new ArrayList<>()).add(target.getId());
            }
        }
        List<Long> targetGroupIdList = targets.stream().filter(t -> Objects.nonNull(t.getGroupId())).map(Target::getGroupId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(targetGroupIdList)) {
            return;
        }
        //查询指标分组信息
        List<TargetGroup> targetGroups = targetGroupService.listByIds(targetGroupIdList);
        if (CollectionUtils.isEmpty(targetGroups)) {
            return;
        }
        //构建分组与指标的关系
        for (TargetGroup targetGroup : targetGroups) {
            List<Long> targetIds = targetWithGroupMap.get(targetGroup.getId());
            for (Long targetId : targetIds) {
                targetGroupMap.put(targetId, targetGroup);
            }
        }
    }

    private String getInfoName(BehaviourRecord behaviourRecord) {
        if (ObjectUtil.isEmpty(behaviourRecord)) {
            return "";
        }
        Integer dataSource = behaviourRecord.getDataSource();
        // 德育的
        if (DataSourceEnum.MORAL_CAMPUS.getCode().equals(dataSource) || DataSourceEnum.MORAL_OFF_CAMPUS.getCode().equals(dataSource)) {
            return behaviourRecord.getTargetName() + ModuleEnum.getModuleName(behaviourRecord.getModuleCode()) + behaviourRecord.getInfoName();
        }
        return behaviourRecord.getInfoName();
    }

//    TODO 初始分改版注释
//    private void fillInitOption(List<InitialScoreAllocation> initialScoreAllocations, List<ListModuleDetailVODateNewInfo> list) {
//        if (CollUtil.isEmpty(initialScoreAllocations)) {
//            return;
//        }
//
//        Map<String, List<InitialScoreAllocation>> initMap = initialScoreAllocations.stream()
//                .collect(Collectors.groupingBy(initScore -> DateUtil.formatDate(initScore.getCreateTime())));
//        initMap.forEach((createTime, allocations) -> {
//            ListModuleDetailVODateNewInfo newInfo = new ListModuleDetailVODateNewInfo();
//            newInfo.setDate(createTime);
//
//            allocations.forEach(allocation -> {
//                ListModuleDetailVOBehaviour behaviour = new ListModuleDetailVOBehaviour();
//                behaviour.setScore(allocation.getInitialScore().stripTrailingZeros().toPlainString());
//                behaviour.setDataSource(0);
//                behaviour.setTargetName(Objects.isNull(allocation.getInfoName()) ? "学期初始化分值" : allocation.getInfoName());
//                behaviour.setInfoType(1);
//                behaviour.setCreateTime(allocation.getCreateTime());
//                behaviour.setHaveDetail(0);
//
//                ListModuleDetailVOInnerBehaviour innerBehaviour = getBehaviour(allocation);
//                behaviour.setDetails(CollUtil.newArrayList(innerBehaviour));
//                newInfo.getBehaviourList().add(behaviour);
//            });
//
//            list.add(newInfo);
//        });
//    }

//    private ListModuleDetailVOInnerBehaviour getBehaviour(InitialScoreAllocation allocation) {
//        ListModuleDetailVOInnerBehaviour behaviour = new ListModuleDetailVOInnerBehaviour();
//        behaviour.setInfoType(1);
//        behaviour.setDataSource(0);
//        String behaviourName = "";
//        if (allocation.getScoreType() == 1) {
//            behaviourName = behaviourName + "加" + allocation.getScoreValue().stripTrailingZeros().toPlainString() + "分";
//        }
//        if (allocation.getScoreType() == 2) {
//            behaviourName = behaviourName + "减" + allocation.getScoreValue().stripTrailingZeros().toPlainString() + "分";
//        }
//        behaviour.setBehaviourName(behaviourName);
//        behaviour.setBehaviourScore(allocation.getInitialScore().stripTrailingZeros().toPlainString());
//        return behaviour;
//    }

    private String getParentName(String parentName, Map<Integer, String> nameMap) {
        String mom = nameMap.get(SaaSStudentRelationEnum.MOTHER.getCode());
        if (Objects.nonNull(mom)) {
            return mom;
        }
        String dad = nameMap.get(SaaSStudentRelationEnum.FATHER.getCode());
        if (Objects.nonNull(dad)) {
            return dad;
        }
        String guardian = nameMap.get(SaaSStudentRelationEnum.GUARDIAN.getCode());
        if (Objects.nonNull(guardian)) {
            return guardian;
        }
        return parentName;
    }
}
