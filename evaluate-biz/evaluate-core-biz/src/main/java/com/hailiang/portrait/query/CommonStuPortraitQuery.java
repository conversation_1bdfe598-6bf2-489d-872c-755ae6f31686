package com.hailiang.portrait.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 学生公共画像
 * @ClassName CommonStuPortraitQuery
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/19 16:57
 */
@Data
public class CommonStuPortraitQuery{

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
//    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
//    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    @NotBlank(message = "学生id不能为空")
    private String studentId;

    private String classId;

    private String schoolYear;

    private String termName;

    /**
     * 学校id
     */
    private String schoolId;
    /**
     * 校区id
     */
    private String campusId;
    /**
     * 评价人类型  1：老师  2：家长
     */
    private Integer appraisalType;

    private List<Long> unNeedBehaviourRecordIds;


    /**
     * 是否统计学生帮扶积分
     */
    private Boolean includeHelpBehaviour;

    private List<Long> ids;
    private List<String> studentIdList;
    private List<String> staffIdList;

    /**
     * web、默认：0
     * h5-老师：1
     * h5-家长：2
     */
    private Integer source = 0;

    private Long reviewDetailId;

    private Boolean isCurrentYear;

    private Integer sendType;

    private String staffId;

    /**
     * 是否为简版信息
     */
    private boolean simple = Boolean.FALSE;


    /**
     * 是否需要五育得分明细
     */
    private boolean needModuleDetail = Boolean.FALSE;

}
