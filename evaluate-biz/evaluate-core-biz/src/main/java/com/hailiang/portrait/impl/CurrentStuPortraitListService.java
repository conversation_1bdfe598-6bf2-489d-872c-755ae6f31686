package com.hailiang.portrait.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.portrait.StuPortraitListHandler;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.service.BasicInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;

/**
 * 当前学年学生画像列表
 *
 * @Description: 当前学年学生画像列表
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: Created in 2024/7/25
 * @Version: 2.0.0
 */
@Slf4j
@Service
public class CurrentStuPortraitListService extends StuPortraitListHandler {
    @Resource
    private BasicInfoService basicInfoService;

    @Resource
    private RedisUtil redisUtil;


    /**
     * 查询某学校某一学年（时刻）的学生列表
     * 缓存一周
     */
    @Override
    public List<EduStudentInfoVO> listCurrentCacheStudent(Long schoolId,
                                                          Long campusId,
                                                          String campusSectionId,
                                                          String schoolYear,
                                                          Boolean isCurrentYear,
                                                          String gradeId,
                                                          String classId,
                                                          Date checkTime,
                                                          String staffId) {

        String key = StrUtil.join(StrPool.COLON, RedisKeyConstants.SAAS_RESULT_CURRENT_STUDENT,
                Convert.toStr(schoolYear),
                Convert.toStr(schoolId),
                Convert.toStr(campusId),
                Convert.toStr(campusSectionId),
                Convert.toStr(gradeId),
                Convert.toStr(classId),
                staffId
        );

        log.info("【准备缓存获取数据】-【查询某学校当前学年的学生列表(半小时缓存)】，key：{}", key);
        return redisUtil.getOrAddNonNull(key, () -> this.queryStudentPage(schoolId,campusId,campusSectionId,schoolYear,isCurrentYear,gradeId,classId,checkTime), CacheConstants.TEN_MINUTE);
    }

    @Override
    public List<EduStudentInfoVO> queryStudentPage(Long schoolId, Long campusId, String campusSectionId, String schoolYear, Boolean isCurrentYear, String gradeId, String classId, Date checkTime) {
        // saas获取数据(不做名字搜索)
        EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
        eduStudentPageQueryDTO.setSchoolId(schoolId);
        eduStudentPageQueryDTO.setCampusId(campusId);
        eduStudentPageQueryDTO.setCampusSectionId(Convert.toLong(campusSectionId));
        if (!Objects.equals(Constant.MINUS_ONE, gradeId)) {
            eduStudentPageQueryDTO.setGradeId(Convert.toLong(gradeId));
        }
        if (!Objects.equals(Constant.MINUS_ONE, classId)) {
            eduStudentPageQueryDTO.setClassId(Convert.toLong(classId));
        }

        return basicInfoService.queryStudentPageV2(eduStudentPageQueryDTO);
    }

}
