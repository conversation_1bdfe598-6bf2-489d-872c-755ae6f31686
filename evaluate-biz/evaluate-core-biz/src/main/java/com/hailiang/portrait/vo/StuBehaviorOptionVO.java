package com.hailiang.portrait.vo;

import com.hailiang.constant.Constant;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 指标分组信息--保存
 */
@Data
public class StuBehaviorOptionVO {
    private String id;

    private String templateId;

    private String optionId;

    private Integer infoType;

    private Integer scoreType;

    /**
     * 模块code：
     德育：1
     智育：2
     体育：3
     美育：4
     劳育：5
     */
    private Integer moduleCode;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 指标id
     */
    private Long targetId;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标或选项名称
     */
    private String infoName;
    /**
     * 指标分数
     */
    private BigDecimal behaviourScore;
    /**
     * 指标分数（不做精度处理）
     */
    private BigDecimal behaviourScoreNoScale;

    /**
     * 选项提交次数
     */
    private Integer submitCount;

    /**
     * 指标sortIndex 用于指标排序
     */
    private Integer targetSortIndex;

    /**
     * 分组sortIndex 用于指标排序
     */
    private Integer groupSortIndex;

    /**
    * 是否有详情
    */
    private Integer haveDetail = Constant.ONE;

    private Date submitTime;

    private String infoId;

    private Integer dataSource;

    private Date createTime;

    private String appraisalId;

    private String appraisalName;

    private Integer appraisalType;
}