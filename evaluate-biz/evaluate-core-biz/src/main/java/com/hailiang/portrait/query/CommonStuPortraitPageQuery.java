package com.hailiang.portrait.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 公共学生画像分页查询参数
 * @ClassName CommonStuPortraitQuery
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/19 16:57
 */
@Data
public class CommonStuPortraitPageQuery extends CommonStuPortraitQuery {
    /**
     * 分页页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1,message = "页码最小值为1")
    private Integer pageNum = 1;
    /**
     * 分页页数
     */
    @NotNull(message = "页数不能为空")
    private Integer pageSize = 10;

}
