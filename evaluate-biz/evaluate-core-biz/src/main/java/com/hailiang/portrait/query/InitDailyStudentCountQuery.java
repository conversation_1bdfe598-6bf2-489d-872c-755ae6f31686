package com.hailiang.portrait.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 初始化学生归档统计表点评次数
 *
 * @Description: 初始化学生归档统计表点评次数
 * @Author: Tan<PERSON>ian
 * @Date: Created in 2024-09-10
 * @Version: 1.6.0
 */
@Data
public class InitDailyStudentCountQuery {
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @NotNull(message = "结束时间不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
