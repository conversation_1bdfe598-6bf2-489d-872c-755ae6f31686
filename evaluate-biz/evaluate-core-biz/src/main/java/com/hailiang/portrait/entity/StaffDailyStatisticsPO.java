package com.hailiang.portrait.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 指标行为教职工统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Getter
@Setter
@TableName("evaluate_behaviour_staff_daily_statistics")
public class StaffDailyStatisticsPO extends BaseEntity {

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 学校id
     */
    @TableField("school_id")
    private String schoolId;

    /**
     * 校区id
     */
    @TableField("campus_id")
    private String campusId;

    /**
     * 学段id
     */
    @TableField("campus_section_id")
    private String campusSectionId;

    /**
     * 学段code
     */
    @TableField("campus_section_code")
    private String campusSectionCode;

    /**
     * 年级id
     */
    @TableField("grade_id")
    private String gradeId;

    /**
     * 年级code
     */
    @TableField("grade_code")
    private String gradeCode;

    /**
     * 班级id
     */
    @TableField("class_id")
    private String classId;

    /**
     * 评价人人id
     */
    @TableField("appraisal_id")
    private String appraisalId;

    /**
     * 模块code 1：德育 2：智育 3：体育 4：美育 5：劳育
     */
    @TableField("module_code")
    private Integer moduleCode;

    /**
     * 指标id
     */
    @TableField("target_id")
    private Long targetId;

    /**
     * 总加分
     */
    @TableField("plus_total_score")
    private BigDecimal plusTotalScore;

    /**
     * 总减分
     */
    @TableField("minus_total_score")
    private BigDecimal minusTotalScore;

    /**
     * 总分值
     */
    @TableField("total_score")
    private BigDecimal totalScore;

    /**
     * 统计日期
     */
    @TableField("statistics_time")
    private Date statisticsTime;

    /**
     * 数据来源 0：综合素质评价 1：内驱力 2：成绩管理 3:体测管理
     */
    @TableField("data_source")
    private Integer dataSource;

    @TableField("appraisal_count")
    private Integer appraisalCount;

    /**
     * 学年
     */
    @TableField("school_year")
    private String schoolYear;

    /**
     * 学期（上学期、下学期）
     */
    @TableField("term_name")
    private String termName;

    public static final String TENANT_ID = "tenant_id";

    public static final String SCHOOL_ID = "school_id";

    public static final String CAMPUS_ID = "campus_id";

    public static final String CAMPUS_SECTION_ID = "campus_section_id";

    public static final String CAMPUS_SECTION_CODE = "campus_section_code";

    public static final String GRADE_ID = "grade_id";

    public static final String GRADE_CODE = "grade_code";

    public static final String CLASS_ID = "class_id";

    public static final String APPRAISAL_ID = "appraisal_id";

    public static final String MODULE_CODE = "module_code";

    public static final String TARGET_ID = "target_id";

    public static final String PLUS_TOTAL_SCORE = "plus_total_score";

    public static final String MINUS_TOTAL_SCORE = "minus_total_score";

    public static final String TOTAL_SCORE = "total_score";

    public static final String STATISTICS_TIME = "statistics_time";

    public static final String DATA_SOURCE = "data_source";

}
