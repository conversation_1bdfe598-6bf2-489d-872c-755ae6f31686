package com.hailiang.portrait.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 指标行为学生统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Getter
@Setter
@TableName("evaluate_behaviour_student_daily_statistics")
public class StudentDailyStatisticsPO extends BaseEntity {

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 学校id
     */
    @TableField("school_id")
    private String schoolId;

    /**
     * 校区id
     */
    @TableField("campus_id")
    private String campusId;

    /**
     * 学段id
     */
    @TableField("campus_section_id")
    private String campusSectionId;

    /**
     * 学段code
     */
    @TableField("campus_section_code")
    private String campusSectionCode;

    /**
     * 年级id
     */
    @TableField("grade_id")
    private String gradeId;

    /**
     * 年级code
     */
    @TableField("grade_code")
    private String gradeCode;

    /**
     * 班级id
     */
    @TableField("class_id")
    private String classId;

    /**
     * 学生id
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 德育加分
     */
    @TableField("plus_moral_score")
    private BigDecimal plusMoralScore;

    /**
     * 德育减分
     */
    @TableField("minus_moral_score")
    private BigDecimal minusMoralScore;

    /**
     * 智育加分
     */
    @TableField("plus_wisdom_score")
    private BigDecimal plusWisdomScore;

    /**
     * 智育减分
     */
    @TableField("minus_wisdom_score")
    private BigDecimal minusWisdomScore;

    /**
     * 体育加分
     */
    @TableField("plus_sport_score")
    private BigDecimal plusSportScore;

    /**
     * 体育减分
     */
    @TableField("minus_sport_score")
    private BigDecimal minusSportScore;

    /**
     * 美育加分
     */
    @TableField("plus_pretty_score")
    private BigDecimal plusPrettyScore;

    /**
     * 美育减分
     */
    @TableField("minus_pretty_score")
    private BigDecimal minusPrettyScore;

    /**
     * 劳育加分
     */
    @TableField("plus_work_score")
    private BigDecimal plusWorkScore;

    /**
     * 劳育减分
     */
    @TableField("minus_work_score")
    private BigDecimal minusWorkScore;

    /**
     * 其他育加分
     */
    @TableField("plus_other_score")
    private BigDecimal plusOtherScore;

    /**
     * 其他育减分
     */
    @TableField("minus_other_score")
    private BigDecimal minusOtherScore;

    /**
     * 总加分
     */
    @TableField("plus_total_score")
    private BigDecimal plusTotalScore;

    /**
     * 总减分
     */
    @TableField("minus_total_score")
    private BigDecimal minusTotalScore;

    /**
     * 总分值
     */
    @TableField("total_score")
    private BigDecimal totalScore;

    /**
     * 统计日期
     */
    @TableField("statistics_time")
    private Date statisticsTime;

    /**
     * 数据来源 0：综合素质评价 1：内驱力 2：成绩管理 3:体测管理
     */
    @TableField("data_source")
    private Integer dataSource;

    @TableField("school_year")
    private String schoolYear;

    @TableField("term_name")
    private String termName;

    @TableField("appraisal_count")
    private Integer appraisalCount;

    public static final String TENANT_ID = "tenant_id";

    public static final String SCHOOL_ID = "school_id";

    public static final String CAMPUS_ID = "campus_id";

    public static final String CAMPUS_SECTION_ID = "campus_section_id";

    public static final String CAMPUS_SECTION_CODE = "campus_section_code";

    public static final String GRADE_ID = "grade_id";

    public static final String GRADE_CODE = "grade_code";

    public static final String CLASS_ID = "class_id";

    public static final String STUDENT_ID = "student_id";

    public static final String PLUS_MORAL_SCORE = "plus_moral_score";

    public static final String MINUS_MORAL_SCORE = "minus_moral_score";

    public static final String PLUS_WISDOM_SCORE = "plus_wisdom_score";

    public static final String MINUS_WISDOM_SCORE = "minus_wisdom_score";

    public static final String PLUS_SPORT_SCORE = "plus_sport_score";

    public static final String MINUS_SPORT_SCORE = "minus_sport_score";

    public static final String PLUS_PRETTY_SCORE = "plus_pretty_score";

    public static final String MINUS_PRETTY_SCORE = "minus_pretty_score";

    public static final String PLUS_WORK_SCORE = "plus_work_score";

    public static final String MINUS_WORK_SCORE = "minus_work_score";

    public static final String PLUS_TOTAL_SCORE = "plus_total_score";

    public static final String MINUS_TOTAL_SCORE = "minus_total_score";

    public static final String TOTAL_SCORE = "total_score";

    public static final String STATISTICS_TIME = "statistics_time";

    public static final String DATA_SOURCE = "data_source";

    public static final String APPRAISAL_COUNT = "appraisal_count";

}
