package com.hailiang.portrait.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.constant.Constant;
import com.hailiang.convert.BehaviourRecordConvert;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.InfoTypeEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.TimeEnum;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.logic.TermLogic;
import com.hailiang.manager.StudentDailyStatisticsManager;
import com.hailiang.mapper.doris.DorisStudentPortraitMapper;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.InitialScorePO;
import com.hailiang.model.vo.*;
import com.hailiang.portrait.StuPortraitService;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.portrait.vo.StuBehaviorInfoVO;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import com.hailiang.portrait.vo.StuBehaviourCountVO;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.SaasTermManager;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import com.hailiang.saas.model.vo.term.TermVo;
import com.hailiang.util.DateSplitter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName StuPortraitServiceImpl
 * @Description 学生画像服务方法
 * <AUTHOR>
 * @Date 2024/6/21 10:25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StuPortraitServiceImpl implements StuPortraitService {

    final RedisUtil redisUtil;
    final BehaviourRecordConvert convert;
    final SaasStudentManager saasStudentManager;
    final StudentDailyStatisticsManager studentDailyStatisticsManager;
    final BehaviourRecordManager behaviourRecordManager;
    final TermLogic termLogic;
    final DorisStudentPortraitMapper dorisStudentPortraitMapper;
    final SaasTermManager saasTermManager;

//    TODO 初始分改版注释
//    /**
//     * 初始分排名
//     */
//    private Map<String, BigDecimal> getInitScoreMap(List<InitialScoreAllocation> allocations) {
//        return allocations.stream()
//                .collect(Collectors.groupingBy(InitialScoreAllocation::getStudentId,
//                        Collectors.mapping(InitialScoreAllocation::getInitialScore,
//                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
//    }

    /**
     * 学生成绩排名
     */
    private Map<String, BigDecimal> getScoreMap(List<StudentDailyStatisticsPO> statistics) {
        return statistics.stream()
                .collect(Collectors.groupingBy(StudentDailyStatisticsPO::getStudentId,
                        Collectors.mapping(StudentDailyStatisticsPO::getTotalScore,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    @Override
    public List<StudentDailyStatisticsPO> listStudentDailyStatisticsDoris(StuPortraitQuery dto) {
        Date startTime = dto.getStartTime();
        Date endTime = dto.getEndTime();
        String classId = dto.getClassId();
        if (CollUtil.isNotEmpty(dto.getIds())) {
            dto.setNeedIds(dto.getIds());
            dto.setStartTime(null);
            dto.setEndTime(null);
            dto.setClassId(null);
        }
        List<StudentDailyStatisticsPO> studentDailyStatisticsPOS = dorisStudentPortraitMapper.listStudentDailyStatistics(dto);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setClassId(classId);
        return studentDailyStatisticsPOS;
    }

    @Override
    public List<BehaviourRecordSynthesisScoreRankVO> getRanks(List<SassStudentVO> voList,
                                                              List<InitialScorePO> initialScorePOs,
                                                              List<StudentDailyStatisticsPO> dailyStatistics) {
        List<BehaviourRecordSynthesisScoreRankVO> ranks = new ArrayList<>();

        Map<String, BigDecimal> scoreMap = getScoreMap(dailyStatistics);

        BigDecimal initialScore = BigDecimal.ZERO;
        Optional<InitialScorePO> first = initialScorePOs
                .stream()
                .filter(s -> ModuleEnum.OTHER.getCode().equals(s.getInitialScoreType()))
                .findFirst();
        if(first.isPresent() && BeanUtil.isNotEmpty(first.get()) && ObjectUtil.isNotNull(first.get().getInitialScore())){
            initialScore = first.get().getInitialScore();
        }

        for (SassStudentVO vo : voList) {
            BehaviourRecordSynthesisScoreRankVO rank = new BehaviourRecordSynthesisScoreRankVO();
            BigDecimal behaviourScore = scoreMap.getOrDefault(vo.getStudentId().toString(), BigDecimal.ZERO);
            rank.setStudentId(vo.getStudentId());
            rank.setStudentName(vo.getStudentName());
            rank.setBehaviourScore(behaviourScore);
            rank.setInitialScore(initialScore);
            rank.setSumScore(NumberUtil.add(behaviourScore, initialScore));

            ranks.add(rank);
        }

        return ranks;
    }

//    TODO 初始分改版注释
//    @Override
//    public BehaviourRecordSynthesisScoreVO fillSynthesisScore(List<InitialScoreAllocation> initialScoreAllocations,
//                                                              List<StudentDailyStatisticsPO> dailyStatistics, StuPortraitQuery dto,
//                                                              List<BehaviourRecordSynthesisScoreRankVO> ranks) {
//
//        BehaviourRecordSynthesisScoreVO synthesisScoreVO = new BehaviourRecordSynthesisScoreVO();
//
//        //综合总加分
//        BigDecimal normalAddScore = dailyStatistics.stream()
//                .filter(d -> d.getStudentId().equals(dto.getStudentId()))
//                .map(StudentDailyStatisticsPO::getPlusTotalScore)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        synthesisScoreVO.setAddScore(normalAddScore);
//        //分配分加分
//        BigDecimal initAddScore = initialScoreAllocations.stream()
//                .filter(d -> Constant.ONE.equals(d.getScoreType()) && d.getStudentId().equals(dto.getStudentId()))
//                .map(InitialScoreAllocation::getInitialScore)
//                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO, RoundingMode.HALF_UP);
//        //总加分 = 综合总加分 + 分配分加分
//        BigDecimal totalAddScore = normalAddScore.add(initAddScore);
//        synthesisScoreVO.setTotalSumScore(totalAddScore);
//
//        //综合总减分
//        BigDecimal normalSubScore = dailyStatistics.stream()
//                .filter(d -> d.getStudentId().equals(dto.getStudentId()))
//                .map(StudentDailyStatisticsPO::getMinusTotalScore)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        synthesisScoreVO.setSubtractScore(normalSubScore);
//        //分配分减分
//        BigDecimal initSubScore = initialScoreAllocations.stream()
//                .filter(d -> Constant.TWO.equals(d.getScoreType()) && d.getStudentId().equals(dto.getStudentId()))
//                .map(InitialScoreAllocation::getInitialScore)
//                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO, RoundingMode.HALF_UP);
//        //总减分 = 综合总减分 - 分配分减分
//        BigDecimal totalSubScore = normalSubScore.add(initSubScore);
//        synthesisScoreVO.setTotalSubScore(totalSubScore);
//
//        // 总得分 = 总加分值-总减分值
//        BigDecimal sumScore = totalAddScore.add(totalSubScore);
//        synthesisScoreVO.setSumScore(sumScore);
//
//        //总分= 总加分+总减分（取绝对值）
//        BigDecimal totalScore = normalAddScore.add(normalSubScore.abs());
//        //加分占比  加分总分(不加分配分） / 总分
//        if (totalScore.compareTo(BigDecimal.ZERO) > 0) {
//            BigDecimal addRate = NumberUtil.equals(totalAddScore, BigDecimal.ZERO) ?
//                    BigDecimal.ZERO : normalAddScore.divide(totalScore, Constant.THREE, RoundingMode.HALF_UP);
//            synthesisScoreVO.setAddScoreRate(NumUtil.convertToPercent(addRate, Constant.ONE));
//
//            //减分占比 减分总分(不加分配分）/ 总分
//            BigDecimal subRate = NumberUtil.equals(totalAddScore, BigDecimal.ZERO) ?
//                    BigDecimal.ZERO : normalSubScore.divide(totalScore, Constant.THREE, RoundingMode.HALF_UP);
//            synthesisScoreVO.setSubtractScoreRate(NumUtil.convertToPercent(subRate.abs(), Constant.ONE));
//        }
//
//        // 全班得分排名list， 超越了多少学生人数, 这个分数不加分配分 用于比较班内总分排比
//        //超越全班百分比 计算公式为 超越人数/全部总人数，如果就一个人，正分超越100%，负分0%
//        long passPeopleNum = ranks.stream().filter(d -> Constant.NEGATIVE_ONE.equals(d.getSumScore().compareTo(sumScore))).count();
//
//        String surpassRate = null;
//        if (!ranks.isEmpty()) {
//            surpassRate = new BigDecimal(passPeopleNum)
//                    .divide(BigDecimal.valueOf(ranks.size()), Constant.THREE, RoundingMode.HALF_UP)
//                    .multiply(new BigDecimal(Constant.HUNDRED))
//                    .setScale(Constant.ONE, RoundingMode.HALF_UP)
//                    .stripTrailingZeros().toPlainString() + Constant.PERCENT;
//        }
//
//        //只有一个人且是一条行为情况且分数为0的情况
//        if (Constant.ZERO.equals((int) passPeopleNum) && Constant.ONE.equals(dailyStatistics.size())) {
//            surpassRate = dailyStatistics.get(0).getTotalScore().signum() > 0 ?
//                    new BigDecimal(Constant.HUNDRED).toPlainString() + Constant.PERCENT :
//                    BigDecimal.ZERO.toPlainString() + Constant.PERCENT;
//        }
//        synthesisScoreVO.setSurpassRate(surpassRate);
//
//        return synthesisScoreVO;
//    }

    @Override
    public List<StudentDailyStatisticsPO> fillDailyStatistics(Date daily, List<BehaviourRecord> behaviourRecordList) {
        List<StudentDailyStatisticsPO> studentDailyStatisticsPOList = new ArrayList<>();

        //按学生指标分组，学生id_数据来源
        Map<String, List<BehaviourRecord>> stuRecrodMap = behaviourRecordList.stream().collect(Collectors.groupingBy(this::groupingBy));
        for (Map.Entry<String, List<BehaviourRecord>> entry : stuRecrodMap.entrySet()) {
            List<BehaviourRecord> behaviourRecords = entry.getValue();
            List<String> keys = Lists.newArrayList(entry.getKey().split("_"));

            StudentDailyStatisticsPO studentDailyStatisticsPO = new StudentDailyStatisticsPO();
            fillBasicInfo(daily, keys, studentDailyStatisticsPO);

            BigDecimal subTotalScore = BigDecimal.ZERO;
            BigDecimal addTotalScore = BigDecimal.ZERO;
            Map<Integer, List<BehaviourRecord>> moduleMap = behaviourRecords.stream().collect(Collectors.groupingBy(BehaviourRecord::getModuleCode));
            for (Map.Entry<Integer, List<BehaviourRecord>> mapEntry : moduleMap.entrySet()) {
                Integer code = mapEntry.getKey();
                List<BehaviourRecord> records = mapEntry.getValue();
                //总加分
                BigDecimal moduleAddScore = records.stream()
                        .filter(behaviourRecord -> Constant.ONE.equals(behaviourRecord.getScoreType()))
                        .map(BehaviourRecord::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .setScale(Constant.TWO, RoundingMode.HALF_UP);
                //总减分
                BigDecimal moduleSubScore = records.stream()
                        .filter(behaviourRecord -> Constant.TWO.equals(behaviourRecord.getScoreType()))
                        .map(BehaviourRecord::getScore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .setScale(Constant.TWO, RoundingMode.HALF_UP);
                ModuleEnum moduleEnum = ModuleEnum.parseByCode(code);
                // 剔除不包含五育的
                if (moduleEnum == null) {
                    continue;
                }
                //明细
                fillModuleInfo(moduleEnum, studentDailyStatisticsPO, moduleAddScore, moduleSubScore);

                //总加
                addTotalScore = addTotalScore.add(moduleAddScore);
                //总减
                subTotalScore = subTotalScore.add(moduleSubScore);
            }
            //填充五育默认值
            fillModuleDefaultValue(studentDailyStatisticsPO);

            //  总加分
            studentDailyStatisticsPO.setPlusTotalScore(addTotalScore);
            //  总减分
            studentDailyStatisticsPO.setMinusTotalScore(subTotalScore);
            //总分 = 总加分 + 总减分（带符号）
            studentDailyStatisticsPO.setTotalScore(addTotalScore.add(subTotalScore));

            studentDailyStatisticsPO.setAppraisalCount(behaviourRecords.size());
            studentDailyStatisticsPOList.add(studentDailyStatisticsPO);
        }

        return studentDailyStatisticsPOList;
    }

    private void fillModuleDefaultValue(StudentDailyStatisticsPO studentDailyStatisticsPO) {
        if (Objects.isNull(studentDailyStatisticsPO.getPlusMoralScore())) {
            studentDailyStatisticsPO.setPlusMoralScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getMinusMoralScore())) {
            studentDailyStatisticsPO.setMinusMoralScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getPlusWisdomScore())) {
            studentDailyStatisticsPO.setPlusWisdomScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getMinusWisdomScore())) {
            studentDailyStatisticsPO.setMinusWisdomScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getPlusSportScore())) {
            studentDailyStatisticsPO.setPlusSportScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getMinusSportScore())) {
            studentDailyStatisticsPO.setMinusSportScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getPlusPrettyScore())) {
            studentDailyStatisticsPO.setPlusPrettyScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getMinusPrettyScore())) {
            studentDailyStatisticsPO.setMinusPrettyScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getPlusWorkScore())) {
            studentDailyStatisticsPO.setPlusWorkScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getMinusWorkScore())) {
            studentDailyStatisticsPO.setMinusWorkScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getPlusTotalScore())) {
            studentDailyStatisticsPO.setPlusTotalScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getMinusTotalScore())) {
            studentDailyStatisticsPO.setMinusTotalScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getPlusOtherScore())) {
            studentDailyStatisticsPO.setPlusOtherScore(BigDecimal.ZERO);
        }
        if (Objects.isNull(studentDailyStatisticsPO.getMinusOtherScore())) {
            studentDailyStatisticsPO.setMinusOtherScore(BigDecimal.ZERO);
        }

    }

    private void fillModuleInfo(ModuleEnum moduleEnum, StudentDailyStatisticsPO studentDailyStatisticsPO, BigDecimal moduleAddScore, BigDecimal moduleSubScore) {
        switch (Objects.requireNonNull(moduleEnum)) {
            case MORAL:
                studentDailyStatisticsPO.setPlusMoralScore(moduleAddScore);
                studentDailyStatisticsPO.setMinusMoralScore(moduleSubScore);
                break;
            case WISDOM:
                studentDailyStatisticsPO.setPlusWisdomScore(moduleAddScore);
                studentDailyStatisticsPO.setMinusWisdomScore(moduleSubScore);
                break;
            case SPORT:
                studentDailyStatisticsPO.setPlusSportScore(moduleAddScore);
                studentDailyStatisticsPO.setMinusSportScore(moduleSubScore);
                break;
            case PRETTY:
                studentDailyStatisticsPO.setPlusPrettyScore(moduleAddScore);
                studentDailyStatisticsPO.setMinusPrettyScore(moduleSubScore);
                break;
            case WORK:
                studentDailyStatisticsPO.setPlusWorkScore(moduleAddScore);
                studentDailyStatisticsPO.setMinusWorkScore(moduleSubScore);
                break;
            case OTHER:
                studentDailyStatisticsPO.setPlusOtherScore(moduleAddScore);
                studentDailyStatisticsPO.setMinusOtherScore(moduleSubScore);
                break;
            default:
                break;
        }
    }

    private void fillBasicInfo(Date daily, List<String> keys, StudentDailyStatisticsPO studentDailyStatisticsPO) {
        studentDailyStatisticsPO.setDeleted(false);
        studentDailyStatisticsPO.setTenantId(keys.get(0));
        studentDailyStatisticsPO.setSchoolId(keys.get(1));
        studentDailyStatisticsPO.setCampusId(keys.get(2));
        studentDailyStatisticsPO.setCampusSectionId(keys.get(3));
        studentDailyStatisticsPO.setCampusSectionCode(keys.get(4));
        studentDailyStatisticsPO.setGradeId(keys.get(5));
        studentDailyStatisticsPO.setGradeCode(keys.get(6));
        studentDailyStatisticsPO.setClassId(keys.get(7));
        studentDailyStatisticsPO.setStudentId(keys.get(8));
        studentDailyStatisticsPO.setStatisticsTime(daily);
        if (StringUtils.isNotEmpty(studentDailyStatisticsPO.getSchoolId())
                && StringUtils.isNotEmpty(studentDailyStatisticsPO.getCampusId())
                && StringUtils.isNotEmpty(studentDailyStatisticsPO.getCampusSectionId())) {
            TermVo termVo = saasTermManager.getTermVoByCampusIdCache(Long.valueOf(studentDailyStatisticsPO.getSchoolId()),
                    Long.valueOf(studentDailyStatisticsPO.getCampusId()),
                    Long.valueOf(studentDailyStatisticsPO.getCampusSectionId()), daily);
            if (termVo != null) {
                studentDailyStatisticsPO.setSchoolYear(termVo.getSchoolYear());
                studentDailyStatisticsPO.setTermName(termVo.getTermName());
            }
        }
        studentDailyStatisticsPO.setDataSource(DataSourceEnum.EVALUATE.getCode());
    }

    private String groupingBy(BehaviourRecord behaviourRecord) {
        return String.join("_", behaviourRecord.getTenantId(), behaviourRecord.getSchoolId(),
                behaviourRecord.getCampusId(), behaviourRecord.getCampusSectionId(),
                behaviourRecord.getCampusSectionCode() == null ? "" : behaviourRecord.getCampusSectionCode(),
                behaviourRecord.getGradeId() == null ? "" : behaviourRecord.getGradeId(),
                behaviourRecord.getGradeCode() == null ? "" : behaviourRecord.getGradeCode(), behaviourRecord.getClassId(),
                behaviourRecord.getStudentId() == null ? "" : behaviourRecord.getStudentId());
    }

    @Override
    public List<BehaviourGrowthTrendVOInner> fillGrowthTrend(StuPortraitQuery dto, Integer code,
                                                             List<StudentDailyStatisticsPO> classDailyStatistics) {
        long between = DateUtil.between(dto.getStartTime(), dto.getEndTime(), DateUnit.DAY);
        List<DateSplitter.Segment> dateSegments;
        if (between <= 30) {
            dateSegments = DateSplitter.splitIntoWeeks(LocalDateTimeUtil.of(dto.getStartTime()), 5);
        } else {
            dateSegments = DateSplitter.splitInfoMonth(LocalDateTimeUtil.of(dto.getStartTime()), LocalDateTimeUtil.of(dto.getEndTime()));
        }

        List<BehaviourGrowthTrendVOInner> growthTrendInnerList = new ArrayList<>();
        List<StudentDailyStatisticsPO> studentDailyStatisticPOS = classDailyStatistics.stream()
                .filter(statistics -> dto.getStudentId().equals(statistics.getStudentId()))
                .collect(Collectors.toList());

        //周计数器，用来表示一周前，两周前
        int count = 0;
        List<DateSplitter.Segment> collect = dateSegments.stream()
                .sorted(Comparator.comparing(DateSplitter.Segment::getEndDate).reversed())
                .collect(Collectors.toList());
        for (DateSplitter.Segment segment : collect) {
            //学生分数
            BigDecimal stuScore = studentDailyStatisticPOS.stream()
                    .filter(statistics -> DateUtil.toLocalDateTime(statistics.getStatisticsTime()).isAfter(segment.getStartDate()))
                    .filter(statistics -> DateUtil.toLocalDateTime(statistics.getStatisticsTime()).isBefore(segment.getEndDate()))
                    .map(StudentDailyStatisticsPO::getTotalScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(Constant.TWO, RoundingMode.HALF_UP);
            //班级总分
            List<StudentDailyStatisticsPO> dailyStatistics = classDailyStatistics.stream()
                    .filter(statistics -> DateUtil.toLocalDateTime(statistics.getStatisticsTime()).isAfter(segment.getStartDate()))
                    .filter(statistics -> DateUtil.toLocalDateTime(statistics.getStatisticsTime()).isBefore(segment.getEndDate()))
                    .collect(Collectors.toList());
            BigDecimal classScore = dailyStatistics.stream()
                    .map(StudentDailyStatisticsPO::getTotalScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(Constant.TWO, RoundingMode.HALF_UP);

            //班级评分人数
            long stuSize = dailyStatistics.stream().map(StudentDailyStatisticsPO::getStudentId).distinct().count();

            //班级平均分（总分/班级总人数）
            BehaviourGrowthTrendVOInner behaviourGrowthTrendVoInner = new BehaviourGrowthTrendVOInner();
            behaviourGrowthTrendVoInner.setStudentScore(stuScore);
            behaviourGrowthTrendVoInner.setClassAvgScore(BigDecimal.ZERO);
            if (stuSize > 0) {
                behaviourGrowthTrendVoInner.setClassAvgScore(classScore.divide(BigDecimal.valueOf(stuSize), Constant.TWO, RoundingMode.HALF_UP));
            }
            behaviourGrowthTrendVoInner.setWeekMonthNumber(code.equals(TimeEnum.WEEK.getCode()) ? count : segment.getStartDate().getMonthValue());
            growthTrendInnerList.add(Constant.ZERO, behaviourGrowthTrendVoInner);
            count++;
        }
        return growthTrendInnerList;
    }

    /**
     * 该方法无调用 故不设置其他育分数
     * @param dto
     * @param classDailyStatistics
     * @param moduleEnum
     * @return
     */
    @Override
    public BehaviourComprehensiveQualityVO fillQualityVO(StuPortraitQuery dto,
                                                         List<StudentDailyStatisticsPO> classDailyStatistics,
                                                         ModuleEnum moduleEnum) {
        BigDecimal stuScore = BigDecimal.ZERO;
        BigDecimal maxScore = BigDecimal.ZERO;
        BigDecimal sumScore = BigDecimal.ZERO;

        Map<String, List<StudentDailyStatisticsPO>> stuDailyMap = classDailyStatistics.stream()
                .collect(Collectors.groupingBy(StudentDailyStatisticsPO::getStudentId));

        BehaviourComprehensiveQualityVO moralVo = new BehaviourComprehensiveQualityVO()
                .setModuleCode(moduleEnum.getCode())
                .setModuleName(ModuleEnum.getModuleName(moduleEnum.getCode()));
        for (Map.Entry<String, List<StudentDailyStatisticsPO>> entry : stuDailyMap.entrySet()) {
            String stuId = entry.getKey();
            List<StudentDailyStatisticsPO> statistics = entry.getValue();
            BigDecimal score = BigDecimal.ZERO;
            //学生评价模块总得分累计
            switch (moduleEnum) {
                case MORAL:
                    score = statistics.stream()
                            .map(s -> s.getPlusMoralScore().add(s.getMinusMoralScore()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO, RoundingMode.HALF_UP);
                    break;
                case WISDOM:
                    score = statistics.stream()
                            .map(s -> s.getPlusWisdomScore().add(s.getMinusWisdomScore()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO, RoundingMode.HALF_UP);
                    break;
                case SPORT:
                    score = statistics.stream()
                            .map(s -> s.getPlusSportScore().add(s.getMinusSportScore()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO, RoundingMode.HALF_UP);
                    break;
                case PRETTY:
                    score = statistics.stream()
                            .map(s -> s.getPlusPrettyScore().add(s.getMinusPrettyScore()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO, RoundingMode.HALF_UP);
                    break;
                case WORK:
                    score = statistics.stream()
                            .map(s -> s.getPlusWorkScore().add(s.getMinusWorkScore()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO, RoundingMode.HALF_UP);
                    break;
                default:
                    break;
            }

            //学生得分
            if (dto.getStudentId().equals(stuId)) {
                stuScore = score;
            }

            //最高得分
            if (Constant.ONE.equals(score.compareTo(maxScore))) {
                maxScore = score;
            }

            //班级总分
            sumScore = sumScore.add(score);
        }

        moralVo.setMaxScore(maxScore);
        moralVo.setStudentScore(stuScore);

        int stuSize = stuDailyMap.keySet().size();
        if (stuSize > 0) {
            moralVo.setAvgScore(sumScore.divide(BigDecimal.valueOf(stuSize), Constant.TWO, RoundingMode.HALF_UP));
        }
        return moralVo;
    }

    @Override
    public BehaviourIntelligentEvaluationVO fillRank(StuPortraitQuery dto,
                                                     List<StuBehaviorOptionVO> stuBehaviorOptionVOList,
                                                     List<StudentDailyStatisticsPO> classStatistics) {
        BehaviourIntelligentEvaluationVO evaluationVO = new BehaviourIntelligentEvaluationVO();
        //总分=各选项总分之和
        BigDecimal score = stuBehaviorOptionVOList.stream()
                .map(StuBehaviorOptionVO::getBehaviourScore)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(Constant.TWO, RoundingMode.HALF_UP);
        evaluationVO.setSumScore(toBigDecimal(score));
        evaluationVO.setStartTime(dto.getStartTime());
        evaluationVO.setEndTime(dto.getEndTime());

        //本周
        DateTime beginOfWeek = DateUtil.beginOfWeek(dto.getEndTime());
        DateTime endOfWeek = DateUtil.endOfWeek(dto.getEndTime());
        //上周
        DateTime lastBeginOfWeek = DateUtil.offsetWeek(beginOfWeek, Constant.NEGATIVE_ONE);
        DateTime lastEndOfWeek = DateUtil.endOfWeek(lastBeginOfWeek);

        //本周班级排名
        Map<String, BigDecimal> weekMap = fillMap(classStatistics, beginOfWeek, endOfWeek);
        //筛选时间截止日期所在周得分
        evaluationVO.setLastWeekScore(weekMap.get(dto.getStudentId()));
        //上周班级排名
        Map<String, BigDecimal> lassWeekMap = fillMap(classStatistics, lastBeginOfWeek, lastEndOfWeek);

        //上周或者本周没数据，则不参与排名
        if (MapUtil.isEmpty(lassWeekMap) || MapUtil.isEmpty(weekMap)) {
            evaluationVO.setProgressRank(null);
        } else {
            // 上周名次 - 本周名次
            evaluationVO.setProgressRank(getRanking(lassWeekMap, dto.getStudentId()) - getRanking(weekMap, dto.getStudentId()));
        }

        return evaluationVO;
    }

    /**
     * 一段时间段内的成绩（学生，总分）
     */
    private Map<String, BigDecimal> fillMap(List<StudentDailyStatisticsPO> classStatistics, DateTime startTime, DateTime endTime) {
        return classStatistics.stream()
                .filter(statistics -> statistics.getStatisticsTime().getTime() >= startTime.getTime())
                .filter(statistics -> statistics.getStatisticsTime().getTime() <= endTime.getTime())
                .collect(Collectors.groupingBy(StudentDailyStatisticsPO::getStudentId,
                        Collectors.mapping(StudentDailyStatisticsPO::getTotalScore,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .collect(Collectors.toMap(Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldV, newV) -> oldV, LinkedHashMap::new));
    }

    @Override
    public BehavioralExpressionVO fillOption(List<StuBehaviorOptionVO> vos, StuBehaviorInfoVO stuBehaviorInfoVO) {
        BehavioralExpressionVO expressionVO = new BehavioralExpressionVO();

        //加减分分组
        Map<Integer, List<StuBehaviorOptionVO>> praiseMap = vos.stream()
                .filter(stuBehaviorOptionVO -> toBigDecimal(stuBehaviorOptionVO.getBehaviourScore()).compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.groupingBy(StuBehaviorOptionVO::getScoreType));
        //表扬
        List<BehaviourNameInfoVO> praiseBehaviourList = new ArrayList<>();
        //待改进
        List<BehaviourNameInfoVO> toImproveBehaviourList = new ArrayList<>();

        for (Map.Entry<Integer, List<StuBehaviorOptionVO>> entry : praiseMap.entrySet()) {
            Integer scoreType = entry.getKey();
            List<StuBehaviorOptionVO> behaviorOptionVOList = entry.getValue();
            behaviorOptionVOList = behaviorOptionVOList.stream()
                    .sorted(Comparator.comparing(StuBehaviorOptionVO::getBehaviourScore).reversed())
                    .collect(Collectors.toList());
            for (StuBehaviorOptionVO stuBehaviorOptionVO : behaviorOptionVOList) {
                BehaviourNameInfoVO behaviourNameInfoVO = getBehaviourNameInfoVO(stuBehaviorOptionVO);
                if (Constant.ONE.equals(scoreType)) {
                    praiseBehaviourList.add(behaviourNameInfoVO);
                }
                if (Constant.TWO.equals(scoreType)) {
                    toImproveBehaviourList.add(behaviourNameInfoVO);
                }
            }
        }

        // 取前四位，不满四位全返回
        expressionVO.setPraiseBehaviourList(ListUtil.sub(praiseBehaviourList, Constant.ZERO, Constant.FOUR));
        expressionVO.setToImproveBehaviourList(ListUtil.sub(toImproveBehaviourList, Constant.ZERO, Constant.FOUR));

        return expressionVO;
    }

    private BehaviourNameInfoVO getBehaviourNameInfoVO(StuBehaviorOptionVO stuBehaviorOptionVO) {
        BehaviourNameInfoVO behaviourNameInfoVO = new BehaviourNameInfoVO();
        behaviourNameInfoVO.setBehaviourId(stuBehaviorOptionVO.getId());
        behaviourNameInfoVO.setBehaviourName(stuBehaviorOptionVO.getInfoName());
        behaviourNameInfoVO.setScore(stuBehaviorOptionVO.getBehaviourScore());
        // 如果是选项名称的填充指标名称
        if (InfoTypeEnum.OPTION.getCode().equals(stuBehaviorOptionVO.getInfoType())) {
            behaviourNameInfoVO.setTargetName(stuBehaviorOptionVO.getTargetName());
        }
        return behaviourNameInfoVO;
    }

    @Override
    public ListModuleDetailNewVO fillBehaviourDetail(List<StuBehaviorOptionVO> behaviourGroup,
                                                     List<ListModuleDetailVODateNewInfo> list) {
        Map<Date, List<StuBehaviorOptionVO>> behaviorDateMap = behaviourGroup.stream()
                .sorted(Comparator.comparing(StuBehaviorOptionVO::getSubmitTime).reversed())
                .collect(Collectors.groupingBy(StuBehaviorOptionVO::getSubmitTime));
        for (Map.Entry<Date, List<StuBehaviorOptionVO>> entry : behaviorDateMap.entrySet()) {
            Date date = entry.getKey();
            List<StuBehaviorOptionVO> optionVOList = entry.getValue();
            ListModuleDetailVODateNewInfo dateNewInfo = new ListModuleDetailVODateNewInfo();
            dateNewInfo.setDate(DateUtil.formatDate(date));
            Map<String, List<StuBehaviorOptionVO>> infoMap = optionVOList.stream()
                    .collect(Collectors.groupingBy(StuBehaviorOptionVO::getInfoId));

            List<ListModuleDetailVOBehaviour> behaviourList = new ArrayList<>();// NOSONAR
            for (StuBehaviorOptionVO optionVO : optionVOList) {
                ListModuleDetailVOBehaviour behaviour = new ListModuleDetailVOBehaviour();
                behaviour.setInfoId(optionVO.getInfoId());
                behaviour.setInfoType(optionVO.getInfoType());
                behaviour.setScore(toBigDecimal(optionVO.getBehaviourScore()).stripTrailingZeros().toPlainString());
                behaviour.setTargetId(optionVO.getTargetId());
                behaviour.setTargetName(optionVO.getTargetName());
                behaviour.setDataSource(optionVO.getDataSource());
                behaviour.setCreateTime(optionVO.getCreateTime());
                behaviour.setDetails(infoMap.get(optionVO.getInfoId()).stream()
                        .map(vo -> {
                            ListModuleDetailVOInnerBehaviour innerBehaviour = convert.toListModuleDetailVOInnerBehaviour(vo);
                            innerBehaviour.setBehaviourScore(toBigDecimal(vo.getBehaviourScore()).stripTrailingZeros().toPlainString());
                            return innerBehaviour;
                        })
                        .collect(Collectors.toList()));

                behaviourList.add(behaviour);
            }

            List<ListModuleDetailVOBehaviour> collect = behaviourList.stream()
                    .sorted(Comparator.comparing(ListModuleDetailVOBehaviour::getCreateTime, Comparator.nullsLast(Date::compareTo)).reversed())
                    .collect(Collectors.toList());
            dateNewInfo.setBehaviourList(collect);

            list.add(dateNewInfo);
        }

        ListModuleDetailNewVO listModuleDetailNewVO = new ListModuleDetailNewVO();
        listModuleDetailNewVO.setDateBehaviourList(CollUtil.sort(list,
                Comparator.comparing(ListModuleDetailVODateNewInfo::getDate).reversed()));
        return listModuleDetailNewVO;
    }

    @Override
    public void fillGrowthTopAndLowScore(List<BehaviourGrowthTrendVOInner> growthTrendVOList,
                                         BehaviourGrowthTrendVO behaviourGrowthTrendVO) {
        List<BigDecimal> classAvgScoreList = growthTrendVOList.stream()
                .map(BehaviourGrowthTrendVOInner::getClassAvgScore)
                .collect(Collectors.toList());
        List<BigDecimal> studentScoreList = growthTrendVOList.stream()
                .map(BehaviourGrowthTrendVOInner::getStudentScore)
                .collect(Collectors.toList());
        classAvgScoreList.addAll(studentScoreList);
        Collections.sort(classAvgScoreList);
        behaviourGrowthTrendVO.setMaxScore(classAvgScoreList.get(classAvgScoreList.size() - 1));
        behaviourGrowthTrendVO.setMinScore(classAvgScoreList.get(0));
    }

    @Override
    public void fillStudentDailyStatistics(List<StuBehaviourCountVO> stuBehaviourCountVOS) {
        this.handleRecords(stuBehaviourCountVOS);
//        Integer pageNum = 1;
//        Integer pageSize = 500;
//        boolean hasNext = true;
//        TimeInterval timeInterval = DateUtil.timer();
//        while (hasNext){
//            Page<StudentDailyStatisticsPO> page = studentDailyStatisticsManager.selectPage(new Page(pageNum, pageSize), startTime, endTime);
//            hasNext = page.hasNext();
//            pageNum++;
//            List<StudentDailyStatisticsPO> records = page.getRecords();
//            if(CollUtil.isNotEmpty(records)){
//                this.handleRecords(records);
//            }
//        }
//        log.info("更新学生统计表完成，更新起始时间:{},更新结束时间:{}, 方法耗时:{}",startTime, endTime, timeInterval.intervalMs());
    }

    private void handleRecords(List<StuBehaviourCountVO> stuBehaviourCountVOS) {
//        List<Date> submitTimes = records.stream().map(StudentDailyStatisticsPO::getStatisticsTime).distinct().sorted(Date::compareTo).collect(Collectors.toList());
//        List<String> studentIds = records.stream().map(StudentDailyStatisticsPO::getStudentId).distinct().collect(Collectors.toList());
//        DateTime endTime = DateUtil.endOfDay(submitTimes.get(submitTimes.size() - 1));
//        DateTime startTime = DateUtil.beginOfDay(submitTimes.get(0));
        TimeInterval timeInterval = DateUtil.timer();
//
//        List<StuBehaviourCountVO> stuBehaviourCountVOS = behaviourRecordManager.listByTimeAndStudentIds(startTime, endTime, studentIds);
//        stuBehaviourCountVOS = stuBehaviourCountVOS.stream().filter(item -> studentIds.contains(item.getStudentId())).collect(Collectors.toList());
//        log.info("统计表获取行为记录表数据结束, 方法耗时:{}", timeInterval.intervalMs());

        // 切片处理
        List<List<StuBehaviourCountVO>> partition = ListUtil.partition(stuBehaviourCountVOS, 5000);
        for (List<StuBehaviourCountVO> behaviourCountVOS : partition) {
            studentDailyStatisticsManager.updateBatchByTimesAndStudentId(behaviourCountVOS);
        }
        log.info("更新学生统计表完成，方法耗时:{}", timeInterval.intervalMs());
    }


    private int getRanking(Map<String, BigDecimal> map, String key) {
        List<BigDecimal> values = new ArrayList<>(map.values());
        Collections.sort(values);

        BigDecimal valueToRank = map.get(key);
        return values.indexOf(valueToRank) + 1;
    }

    private BigDecimal toBigDecimal(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }
}
