package com.hailiang.portrait.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hailiang.constant.Constant;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.StuPortraitBizTypeEnum;
import com.hailiang.model.entity.InitialScorePO;
import com.hailiang.model.vo.BehaviourComprehensiveQualityVO;
import com.hailiang.portrait.AbstractPortraitDetailHandler;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.portrait.vo.StuPortraitInfoVO;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.hailiang.enums.StuPortraitBizTypeEnum.QUALITY;

/**
 * @ClassName GrowthTrendHandler
 * @Description 五育雷达
 * <AUTHOR>
 * @Date 2024/7/4 14:19
 */
@Component
public class QualityHandler extends AbstractPortraitDetailHandler<FillStuDataDTO> {
    @Override
    public StuPortraitBizTypeEnum getBizType() {
        return QUALITY;
    }

    @Override
    public void fill(FillStuDataDTO data) {
        StuPortraitQuery dto = data.getQueryDTO();
        StuPortraitInfoVO stuPortraitInfoVO = data.getStuPortraitInfoVO();
        List<InitialScorePO> initialScorePOs = data.getInitialScorePOs();
        Boolean allTermFlag = data.getAllTermFlag();
        Integer studentCount = stuPortraitInfoVO.getStudentCount();
        List<StudentDailyStatisticsPO> classDailyStatistics = data.getStatisticsList();
        ArrayList<BehaviourComprehensiveQualityVO> qualityVos = new ArrayList<>(5);
        // 班级下各个模块的最高分
        for (ModuleEnum moduleEnum : ModuleEnum.values()) {
            // 其他育不参与统计
            if (Objects.equals(moduleEnum.getCode(), ModuleEnum.OTHER.getCode())) {
                continue;
            }
            qualityVos.add(getModuleData(classDailyStatistics, moduleEnum, dto, studentCount, initialScorePOs, allTermFlag));
        }

        data.getStuPortraitInfoVO().setQualityVOList(qualityVos);
    }

    private BehaviourComprehensiveQualityVO getModuleData(List<StudentDailyStatisticsPO> classDailyStatistics,
                                                          ModuleEnum moduleEnum,
                                                          StuPortraitQuery dto,
                                                          Integer studentCount,
                                                          List<InitialScorePO> initialScorePOs,
                                                          Boolean allTermFlag) {
        BigDecimal stuScore = BigDecimal.ZERO;
        BigDecimal maxScore = BigDecimal.ZERO;
        BigDecimal sumScore = BigDecimal.ZERO;

        //五育初始分
        BigDecimal initialScore = BigDecimal.ZERO;
        if (Boolean.TRUE.equals(allTermFlag)) {
            initialScore = initialScorePOs
                    .stream()
                    .filter(s -> moduleEnum.getCode().equals(s.getInitialScoreType()))
                    .map(InitialScorePO::getInitialScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO, RoundingMode.HALF_UP);
        } else {
            Optional<InitialScorePO> first = initialScorePOs
                    .stream()
                    .filter(s -> moduleEnum.getCode().equals(s.getInitialScoreType()))
                    .findFirst();
            if (first.isPresent() && BeanUtil.isNotEmpty(first.get()) && ObjectUtil.isNotNull(first.get().getInitialScore())) {
                initialScore = first.get().getInitialScore();
            }
        }

        Map<String, List<StudentDailyStatisticsPO>> stuDailyMap = classDailyStatistics.stream()
                .collect(Collectors.groupingBy(StudentDailyStatisticsPO::getStudentId));

        BehaviourComprehensiveQualityVO moralVo = new BehaviourComprehensiveQualityVO()
                .setModuleCode(moduleEnum.getCode())
                .setModuleName(ModuleEnum.getModuleName(moduleEnum.getCode()));
        for (Map.Entry<String, List<StudentDailyStatisticsPO>> entry : stuDailyMap.entrySet()) {
            String stuId = entry.getKey();
            List<StudentDailyStatisticsPO> statistics = entry.getValue();
            BigDecimal score = BigDecimal.ZERO;
            //学生评价模块总得分累计
            switch (moduleEnum) {
                case MORAL:
                    score = statistics.stream()
                            .map(s -> {
                                BigDecimal plusScore = Optional.ofNullable(s.getPlusMoralScore()).orElse(BigDecimal.ZERO);
                                BigDecimal minusScore = Optional.ofNullable(s.getMinusMoralScore()).orElse(BigDecimal.ZERO);
                                return plusScore.add(minusScore);
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO, RoundingMode.HALF_UP);
                    break;
                case WISDOM:
                    score = statistics.stream()
                            .map(s -> {
                                BigDecimal plusScore = Optional.ofNullable(s.getPlusWisdomScore()).orElse(BigDecimal.ZERO);
                                BigDecimal minusScore = Optional.ofNullable(s.getMinusWisdomScore()).orElse(BigDecimal.ZERO);
                                return plusScore.add(minusScore);
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO, RoundingMode.HALF_UP);
                    break;
                case SPORT:
                    score = statistics.stream()
                            .map(s -> {
                                BigDecimal plusScore = Optional.ofNullable(s.getPlusSportScore()).orElse(BigDecimal.ZERO);
                                BigDecimal minusScore = Optional.ofNullable(s.getMinusSportScore()).orElse(BigDecimal.ZERO);
                                return plusScore.add(minusScore);
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO, RoundingMode.HALF_UP);
                    break;
                case PRETTY:
                    score = statistics.stream()
                            .map(s -> {
                                BigDecimal plusScore = Optional.ofNullable(s.getPlusPrettyScore()).orElse(BigDecimal.ZERO);
                                BigDecimal minusScore = Optional.ofNullable(s.getMinusPrettyScore()).orElse(BigDecimal.ZERO);
                                return plusScore.add(minusScore);
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO, RoundingMode.HALF_UP);
                    break;
                case WORK:
                    score = statistics.stream()
                            .map(s -> {
                                BigDecimal plusScore = Optional.ofNullable(s.getPlusWorkScore()).orElse(BigDecimal.ZERO);
                                BigDecimal minusScore = Optional.ofNullable(s.getMinusWorkScore()).orElse(BigDecimal.ZERO);
                                return plusScore.add(minusScore);
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(Constant.TWO, RoundingMode.HALF_UP);
                    break;
                default:
                    break;
            }
            //学生得分
            if (dto.getStudentId().equals(stuId)) {
                stuScore = score;
            }
            //最高得分
            if (Constant.ONE.equals(score.compareTo(maxScore))) {
                maxScore = score;
            }
            //班级总分
            sumScore = sumScore.add(score).add(initialScore);
        }

        moralVo.setMaxScore(maxScore.add(initialScore));
        moralVo.setStudentScore(stuScore.add(initialScore));
        int stuSize = stuDailyMap.keySet().size();
        if (stuSize > 0) {
            moralVo.setAvgScore(sumScore.divide(BigDecimal.valueOf(stuSize), Constant.TWO, RoundingMode.HALF_UP));
        }else {
            moralVo.setAvgScore(initialScore);
        }
        return moralVo;
    }
}
