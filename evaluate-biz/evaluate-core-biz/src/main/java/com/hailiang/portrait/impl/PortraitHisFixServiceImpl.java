package com.hailiang.portrait.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.hailiang.agg.AggBizTypeEnum;
import com.hailiang.agg.AggDataHandler;
import com.hailiang.constant.Constant;
import com.hailiang.convert.BasicConvert;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.model.dto.EvaluateAggDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.portrait.PortraitHisFixService;
import com.hailiang.portrait.query.ArchivedInitQuery;
import com.hailiang.portrait.query.StuPortraitQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName PortraitHisFixServiceImpl
 * <AUTHOR>
 * @Date 2024/6/27 10:38
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class PortraitHisFixServiceImpl implements PortraitHisFixService {
    final BehaviourRecordManager behaviourRecordManager;
    final AggDataHandler aggDataHandler;

    @Override
    public void fixStaffStatics(ArchivedInitQuery query) {
        if (Objects.isNull(query.getStartTime()) || Objects.isNull(query.getEndTime())) {
            return;
        }
        log.info("【评价人评价T+1数据】开始处理，{}, {}", DateUtil.formatDateTime(query.getStartTime()), DateUtil.formatDateTime(query.getEndTime()));

        EvaluateAggDTO agg = BasicConvert.INSTANCE.toEvaluateAggDTO(query);
        Date startTime = DateUtil.beginOfDay(query.getStartTime());
        Date endTime = DateUtil.endOfDay(query.getEndTime());
        //根据日期捞评价明细表
        StuPortraitQuery dto = new StuPortraitQuery();
        dto.setSchoolId(agg.getSchoolId());
        dto.setCampusId(agg.getCampusId());
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        // 暂定仅归档老师点评记录
        dto.setAppraisalType(Constant.ONE);
        List<BehaviourRecord> totalBehaviourRecords = behaviourRecordManager.listRecordByTime(dto, startTime, endTime, null);
        log.info("【评价人评价T+1数据】开始处理, 共处理【{}】条记录", totalBehaviourRecords.size());

        // 创建一个Map来按天分组
        Map<String, List<BehaviourRecord>> groupedByDay = new HashMap<>();
        SimpleDateFormat dayFormatter = new SimpleDateFormat("yyyy-MM-dd");
        // 遍历行为记录列表
        for (BehaviourRecord record : totalBehaviourRecords) {
            // 将submitDate格式化为"yyyy-MM-dd"
            String dayKey = dayFormatter.format(record.getSubmitTime());

            // 将格式化后的日期作为Map的key
            groupedByDay.computeIfAbsent(dayKey, k -> new ArrayList<>()).add(record);
        }


        // 按天
        List<DateTime> times = DateUtil.rangeToList(query.getStartTime(), query.getEndTime(), DateField.DAY_OF_MONTH);
        log.info("【评价人评价T+1数据】开始处理 共【{}】天", times.size());


        for (DateTime dateTime : times) {
            log.info("【评价人评价T+1数据】开始处理，日期：【{}】", DateUtil.formatDate(dateTime));

            String timesString = dayFormatter.format(dateTime);

            List<BehaviourRecord> behaviourRecords = groupedByDay.get(timesString);

            if (CollectionUtil.isEmpty(behaviourRecords)) {
                continue;
            }
            agg.setDaily(dateTime);
            agg.setDailyStr(DateUtil.formatDate(dateTime));
            aggDataHandler.getService(AggBizTypeEnum.BEHAVIOUR_STAFF).doExecute(agg, behaviourRecords);
        }
    }

    @Override
    public void fixStuStatics(ArchivedInitQuery query) {
        if (Objects.isNull(query.getStartTime()) || Objects.isNull(query.getEndTime())) {
            return;
        }
        log.info("【学生评价T+1数据】开始处理，{}, {}", DateUtil.formatDateTime(query.getStartTime()), DateUtil.formatDateTime(query.getEndTime()));

        EvaluateAggDTO agg = BasicConvert.INSTANCE.toEvaluateAggDTO(query);
        Date startTime = DateUtil.beginOfDay(query.getStartTime());
        Date endTime = DateUtil.endOfDay(query.getEndTime());
        //根据日期捞评价明细表
        StuPortraitQuery dto = new StuPortraitQuery();
        dto.setSchoolId(agg.getSchoolId());
        dto.setCampusId(agg.getCampusId());
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);

        List<BehaviourRecord> totalBehaviourRecords = behaviourRecordManager.listRecordByTime(dto, startTime, endTime, null);
        log.info("【学生评价T+1数据】开始处理, 共处理【{}】条记录", totalBehaviourRecords.size());

        // 创建一个Map来按天分组
        Map<String, List<BehaviourRecord>> groupedByDay = new HashMap<>();
        SimpleDateFormat dayFormatter = new SimpleDateFormat("yyyy-MM-dd");
        // 遍历行为记录列表
        for (BehaviourRecord record : totalBehaviourRecords) {
            // 将submitDate格式化为"yyyy-MM-dd"
            String dayKey = dayFormatter.format(record.getSubmitTime());

            if (!record.getIsScore()) {
                continue;
            }

            // 将格式化后的日期作为Map的key
            groupedByDay.computeIfAbsent(dayKey, k -> new ArrayList<>()).add(record);
        }

        // 按天
        List<DateTime> times = DateUtil.rangeToList(query.getStartTime(), query.getEndTime(), DateField.DAY_OF_MONTH);
        log.info("【学生评价T+1数据】开始处理，{}, {}，共处理【{}】天", DateUtil.formatDateTime(query.getStartTime()), DateUtil.formatDateTime(query.getEndTime()), times.size());
        for (DateTime dateTime : times) {
            log.info("【学生评价T+1数据】开始处理，日期：【{}】", DateUtil.formatDate(dateTime));

            String timesString = dayFormatter.format(dateTime);

            List<BehaviourRecord> behaviourRecords = groupedByDay.get(timesString);

            if (CollectionUtil.isEmpty(behaviourRecords)) {
                continue;
            }
            agg.setDaily(dateTime);
            agg.setDailyStr(DateUtil.formatDate(dateTime));
            aggDataHandler.getService(AggBizTypeEnum.BEHAVIOUR_STU).doExecute(agg, behaviourRecords);
        }
    }

    @Override
    public void fixStuClassifyStatics(ArchivedInitQuery query) {
        List<DateTime> times = DateUtil.rangeToList(query.getStartTime(), query.getEndTime(), DateField.DAY_OF_MONTH);
        EvaluateAggDTO agg = BasicConvert.INSTANCE.toEvaluateAggDTO(query);
        log.info("【学生行为指标分类T+1历史数据】开始处理，{}, {}，共处理【{}】天", DateUtil.formatDateTime(query.getStartTime()), DateUtil.formatDateTime(query.getEndTime()), times.size());
        for (DateTime dateTime : times) {
            log.info("【学生行为指标分类T+1历史数据】开始处理，日期：【{}】", DateUtil.formatDate(dateTime));
            agg.setDaily(dateTime);
            agg.setDailyStr(DateUtil.formatDate(dateTime));
            aggDataHandler.getService(AggBizTypeEnum.BEHAVIOUR_CLASSIFY_STU).doExecute(agg, null);
        }
    }


}
