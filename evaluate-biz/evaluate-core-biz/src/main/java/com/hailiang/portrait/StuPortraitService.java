package com.hailiang.portrait;

import com.hailiang.enums.ModuleEnum;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.entity.InitialScorePO;
import com.hailiang.model.vo.*;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.vo.StuBehaviorInfoVO;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import com.hailiang.portrait.vo.StuBehaviourCountVO;
import com.hailiang.saas.model.vo.history.SassStudentVO;

import java.util.*;

/**
 * @ClassName StuPortraitSearchService
 * @Description 学生画像服务方法
 * <AUTHOR>
 * @Date 2024/6/19 17:17
 */
public interface StuPortraitService {

    List<StudentDailyStatisticsPO> listStudentDailyStatisticsDoris(StuPortraitQuery dto);


    /**
     * 班级排名
     */
    List<BehaviourRecordSynthesisScoreRankVO> getRanks(List<SassStudentVO> voList,
                                                       List<InitialScorePO> initialScorePO,
                                                       List<StudentDailyStatisticsPO> dailyStatistics);

//    /**
//     * 学生画像-综合得分
//     */
//    BehaviourRecordSynthesisScoreVO fillSynthesisScore(List<InitialScoreAllocation> allocations,
//                                                       List<StudentDailyStatisticsPO> dailyStatistics,
//                                                       StuPortraitQuery dto,
//                                                       List<BehaviourRecordSynthesisScoreRankVO> ranks);

    /**
     * 学生画像-得分趋势
     */
    List<BehaviourGrowthTrendVOInner> fillGrowthTrend(StuPortraitQuery dto, Integer code,
                                                      List<StudentDailyStatisticsPO> classDailyStatistics);

    /**
     * 学生画像-统计分析
     */
    List<StudentDailyStatisticsPO> fillDailyStatistics(Date daily,
                                                       List<BehaviourRecord> behaviourRecordList);

    /**
     * 学生画像-五育雷达
     */
    BehaviourComprehensiveQualityVO fillQualityVO(StuPortraitQuery dto,
                                                  List<StudentDailyStatisticsPO> statistics,
                                                  ModuleEnum moduleEnum);

    /**
     * 学生画像-行为表现（班级排行）
     */
    BehaviourIntelligentEvaluationVO fillRank(StuPortraitQuery dto,
                                              List<StuBehaviorOptionVO> stuBehaviorOptionVOList,
                                              List<StudentDailyStatisticsPO> statistics);

    /**
     * 学生画像-智能评价
     */
    BehavioralExpressionVO fillOption(List<StuBehaviorOptionVO> vos,
                                      StuBehaviorInfoVO stuBehaviorInfoVO);

    /**
     * 学生画像-五育明细
     */
    ListModuleDetailNewVO fillBehaviourDetail(List<StuBehaviorOptionVO> behaviourGroup,
                                              List<ListModuleDetailVODateNewInfo> list);

    /**
     * 趋势top 1 和 last 1
     */
    void fillGrowthTopAndLowScore(List<BehaviourGrowthTrendVOInner> growthTrendVOList,
                                  BehaviourGrowthTrendVO behaviourGrowthTrendVO);

    void fillStudentDailyStatistics(List<StuBehaviourCountVO> stuBehaviourCountVOS);

}
