package com.hailiang.portrait.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 学校能力模型归档表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Getter
@Setter
@TableName(value = StudentAbilityModelArchivedPO.TABLE_NAME)
public class StudentAbilityModelArchivedPO extends BaseEntity {
    public static final String TABLE_NAME = "evaluate_student_ability_model_archived";

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 学校id
     */
    @TableField("school_id")
    private String schoolId;

    /**
     * 校区id
     */
    @TableField("campus_id")
    private String campusId;

    /**
     * 能力名称
     */
    @TableField("ability_model_name")
    private String abilityModelName;

    /**
     * 对比范围 1：班级 2：年级
     */
    @TableField("contrast_range")
    private Integer contrastRange;

    /**
     * 学年
     */
    @TableField("school_year")
    private String schoolYear;

    /**
     * 详细配置（Json形式）
     */
    @TableField("config")
    private String config;

    @TableField("rule_module_list")
    private String ruleModuleList;

    @TableField("exam_source")
    private String examSource;

    public static final String TENANT_ID = "tenant_id";

    public static final String SCHOOL_ID = "school_id";

    public static final String CAMPUS_ID = "campus_id";

    public static final String ABILITY_MODEL_NAME = "ability_model_name";

    public static final String CONTRAST_RANGE = "contrast_range";

    public static final String SCHOOL_YEAR = "school_year";

    public static final String TERM_NAME = "term_name";

    public static final String CONFIG = "config";

}
