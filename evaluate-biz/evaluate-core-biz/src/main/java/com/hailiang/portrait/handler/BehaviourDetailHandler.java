package com.hailiang.portrait.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hailiang.convert.BehaviourRecordConvert;
import com.hailiang.enums.AppraisalEnum;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.StuPortraitBizTypeEnum;
import com.hailiang.enums.UserIdTypeEnum;
import com.hailiang.model.entity.ReportPushRule;
import com.hailiang.model.vo.ListModuleDetailNewVO;
import com.hailiang.model.vo.ListModuleDetailVOBehaviour;
import com.hailiang.model.vo.ListModuleDetailVODateNewInfo;
import com.hailiang.model.vo.ListModuleDetailVOInnerBehaviour;
import com.hailiang.portrait.AbstractPortraitDetailHandler;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import com.hailiang.service.ReportPushRuleService;
import com.hailiang.util.NumUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.hailiang.enums.StuPortraitBizTypeEnum.DETAIL;

/**
 * @ClassName BehaviourDetailHandler
 * @Description 五育明细
 * <AUTHOR>
 * @Date 2024/7/4 14:29
 */
@Component
@Slf4j
public class BehaviourDetailHandler extends AbstractPortraitDetailHandler<FillStuDataDTO> {
    @Resource
    private BehaviourRecordConvert convert;
    @Resource
    private ReportPushRuleService reportPushRuleService;
    @Override
    public StuPortraitBizTypeEnum getBizType() {
        return DETAIL;
    }

    private static final String STAFF_NAME = "积分卡点评";

    @Override
    public void fill(FillStuDataDTO data) {
        // 是否显示点评人
        Boolean showAppraisalFlag = reportPushRuleService.getShowAppraisalFlag(WebUtil.getCampusId());
        List<StuBehaviorOptionVO> behaviourGroup = data.getBehaviourGroup();
        List<ListModuleDetailVODateNewInfo> list = data.getDataBehaviourList();

        Map<Date, List<StuBehaviorOptionVO>> behaviorDateMap = behaviourGroup.stream()
                .sorted(Comparator.comparing(StuBehaviorOptionVO::getSubmitTime).reversed())
                .collect(Collectors.groupingBy(StuBehaviorOptionVO::getSubmitTime));

        for (Map.Entry<Date, List<StuBehaviorOptionVO>> entry : behaviorDateMap.entrySet()) {
            Date date = entry.getKey();
            List<StuBehaviorOptionVO> optionVOList = entry.getValue();
            ListModuleDetailVODateNewInfo dateNewInfo = new ListModuleDetailVODateNewInfo();
            dateNewInfo.setDate(DateUtil.formatDate(date));
            Map<String/*主键ID*/, List<StuBehaviorOptionVO>> infoMap = optionVOList.stream()
                    .collect(Collectors.groupingBy(StuBehaviorOptionVO::getId));

            List<ListModuleDetailVOBehaviour> behaviourList = new ArrayList<>();// NOSONAR
            for (StuBehaviorOptionVO optionVO : optionVOList) {
                ListModuleDetailVOBehaviour behaviour = new ListModuleDetailVOBehaviour();
                behaviour.setInfoId(optionVO.getInfoId());
                behaviour.setInfoType(optionVO.getInfoType());
                behaviour.setScore(NumUtil.formatFloatNumber(toBigDecimal(optionVO.getBehaviourScore()).stripTrailingZeros()).toPlainString());
                behaviour.setTargetId(optionVO.getTargetId());
                behaviour.setTargetName(optionVO.getTargetName());
                behaviour.setHaveDetail(optionVO.getHaveDetail());
                if(Objects.equals(optionVO.getDataSource(), 15) || Objects.equals(optionVO.getDataSource(), 16)){
                    String module = optionVO.getOptionId().substring(optionVO.getOptionId().length() - 1);
                    behaviour.setTargetName(behaviour.getTargetName() + ModuleEnum.getModuleName(Convert.toInt(module)));
                    optionVO.setTargetName(behaviour.getTargetName() + ModuleEnum.getModuleName(Convert.toInt(module)));
                }
                behaviour.setDataSource(optionVO.getDataSource());
                behaviour.setCreateTime(optionVO.getCreateTime());
                behaviour.setDetails(infoMap.get(optionVO.getId()).stream()
                        .map(vo -> {
                            ListModuleDetailVOInnerBehaviour innerBehaviour = convert.toListModuleDetailVOInnerBehaviour(vo);
                            innerBehaviour.setBehaviourScore(toBigDecimal(vo.getBehaviourScore()).stripTrailingZeros().toPlainString());
                            return innerBehaviour;
                        })
                        .collect(Collectors.toList()));
                // 点评人
                String appraisalName = optionVO.getAppraisalName();
                if (Objects.nonNull(optionVO.getAppraisalType())){
                    appraisalName = appraisalName + AppraisalEnum.getMessageByCode(optionVO.getAppraisalType());
                }
                // 积分卡点评
                if (DataSourceEnum.EVALUATE_POINT_CARD_RECYCLABLE.getCode().equals(optionVO.getDataSource())
                        || DataSourceEnum.EVALUATE_POINT_CARD_DISPOSABLE.getCode().equals(optionVO.getDataSource())) {
                    behaviour.setTargetName("分值卡");
                }
                behaviour.setAppraisalName(appraisalName);
                behaviour.setShowAppraisalFlag(showAppraisalFlag);
                behaviourList.add(behaviour);
            }

            List<ListModuleDetailVOBehaviour> collect = behaviourList.stream()
                    .sorted(Comparator.comparing(ListModuleDetailVOBehaviour::getCreateTime, Comparator.nullsLast(Date::compareTo)).reversed())
                    .collect(Collectors.toList());
            dateNewInfo.setBehaviourList(collect);

            list.add(dateNewInfo);
        }
        // 针对list数据做处理：检测到如果是相同的date，则对里面的behaviourList进行合并，返回一个新的list
        list = list.stream().collect(Collectors.groupingBy(ListModuleDetailVODateNewInfo::getDate))
                .entrySet().stream().map(entry -> {
                    ListModuleDetailVODateNewInfo dateNewInfo = entry.getValue().get(0);
                    List<ListModuleDetailVOBehaviour> behaviourList = entry.getValue().stream().map(ListModuleDetailVODateNewInfo::getBehaviourList).flatMap(List::stream).collect(Collectors.toList());
                    dateNewInfo.setBehaviourList(behaviourList);
                    return dateNewInfo;
                }).collect(Collectors.toList());


        ListModuleDetailNewVO listModuleDetailNewVO = new ListModuleDetailNewVO();
        listModuleDetailNewVO.setDateBehaviourList(CollUtil.sort(list,
                Comparator.comparing(ListModuleDetailVODateNewInfo::getDate).reversed()));
        listModuleDetailNewVO.setAddSumScore(data.getStuPortraitInfoVO().getSynthesisScoreVO().getAddScore());
        listModuleDetailNewVO.setSubtractSumScore(data.getStuPortraitInfoVO().getSynthesisScoreVO().getSubtractScore());

        data.getStuPortraitInfoVO().setModuleDetailNewVO(listModuleDetailNewVO);
    }
}
