package com.hailiang.portrait.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName StuAbilityUpgradeDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/24 19:23
 */
@Data
public class StuAbilityUpgradeQuery {

    private String tenantId;

    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 学年
     */
    private String schoolYear;

    /**
     * 学期名字
     */
    private String termName;

    /**
     * 历史学年起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startTime;

    /**
     * 历史学年结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endTime;

    private String campusSectionId;
}
