package com.hailiang.portrait.handler;

import cn.hutool.core.collection.ListUtil;
import com.hailiang.constant.Constant;
import com.hailiang.enums.StuPortraitBizTypeEnum;
import com.hailiang.model.vo.BehavioralExpressionVO;
import com.hailiang.model.vo.BehaviourNameInfoVO;
import com.hailiang.portrait.AbstractPortraitDetailHandler;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.hailiang.enums.StuPortraitBizTypeEnum.EXPRESS;

/**
 * @ClassName BehaviourOptionHandler
 * @Description 行为表现
 * <AUTHOR>
 * @Date 2024/7/4 14:36
 */
@Component
@Slf4j
public class ExpressionHandler extends AbstractPortraitDetailHandler<FillStuDataDTO> {
    @Override
    public StuPortraitBizTypeEnum getBizType() {
        return EXPRESS;
    }

    @Override
    public void fill(FillStuDataDTO data) {

        List<StuBehaviorOptionVO> vos = data.getBehaviourGroup();

        BehavioralExpressionVO expressionVO = new BehavioralExpressionVO();

        //加减分分组
        Map<Integer, List<StuBehaviorOptionVO>> praiseMap = vos.stream()
                .filter(stuBehaviorOptionVO -> toBigDecimal(stuBehaviorOptionVO.getBehaviourScore()).compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.groupingBy(StuBehaviorOptionVO::getScoreType));
        //表扬
        List<BehaviourNameInfoVO> praiseBehaviourList = new ArrayList<>();
        //待改进
        List<BehaviourNameInfoVO> toImproveBehaviourList = new ArrayList<>();

        for (Map.Entry<Integer, List<StuBehaviorOptionVO>> entry : praiseMap.entrySet()) {
            Integer scoreType = entry.getKey();
            List<StuBehaviorOptionVO> behaviorOptionVOList = entry.getValue();
            behaviorOptionVOList = behaviorOptionVOList.stream()
                    .sorted(Comparator.comparing(StuBehaviorOptionVO::getBehaviourScore).reversed())
                    .collect(Collectors.toList());
            for (StuBehaviorOptionVO stuBehaviorOptionVO : behaviorOptionVOList) {
                BehaviourNameInfoVO behaviourNameInfoVO = getBehaviourNameInfoVO(stuBehaviorOptionVO);
                if (Constant.ONE.equals(scoreType)) {
                    praiseBehaviourList.add(behaviourNameInfoVO);
                }
                if (Constant.TWO.equals(scoreType)) {
                    toImproveBehaviourList.add(behaviourNameInfoVO);
                }
            }
        }

        // 取前四位，不满四位全返回
        expressionVO.setPraiseBehaviourList(ListUtil.sub(praiseBehaviourList, Constant.ZERO, Constant.FOUR));
        expressionVO.setToImproveBehaviourList(ListUtil.sub(toImproveBehaviourList, Constant.ZERO, Constant.FOUR));

        data.getStuBehaviorInfoVO().setExpressionVO(expressionVO);
    }
}
