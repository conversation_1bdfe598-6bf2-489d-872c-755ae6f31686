package com.hailiang.portrait.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @ClassName StuPortaitQueryDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/19 16:57
 */
@Data
public class StuPortraitQuery {

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
//    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
//    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    @NotBlank(message = "学生id不能为空")
    private String studentId;

    @NotBlank(message = "学生班级id不能为空")
    private String classId;

    @NotBlank(message = "学年不能为空")
    private String schoolYear;

    @NotBlank(message = "学期不能为空")
    private String termName;

    /**
     * 学校id
     */
    private String schoolId;
    /**
     * 校区id
     */
    private String campusId;
    /**
     * 评价人类型  1：老师  2：家长
     */
    private Integer appraisalType;

    private List<Long> unNeedBehaviourRecordIds;


    private List<Long> unNeedHelpBehaviourRecordIds;

    /**
     * 推送的学生画像查看时行为记录表需要的id
     */
    private List<Long> ids;
    /**
     * 推送的学生画像查看时帮扶记录表需要的id
     */
    private List<Long> helpBehaviourRecordIds;
    private List<Long> needIds;
    private List<String> studentIdList;
    private List<String> staffIdList;

    /**
     * web、默认：0
     * h5-老师：1
     * h5-家长：2
     */
    private Integer source = 0;

    /**
     * 是否统计学生帮扶积分
     */
    private Boolean includeHelpBehaviour;
    /**
     * 是否统计老师自建指标加分（星动力侧加分）
     */
    private Boolean includeInternalScoreFlag;

    private Long reviewDetailId;

    private Boolean isCurrentYear;

    private Integer sendType;

    private String staffId;

    /**
     * 是否为简版信息
     */
    private boolean simple = Boolean.FALSE;

    private List<Integer> dataSourceList;

    private String gradeId;

    private Integer moduleCode;
}
