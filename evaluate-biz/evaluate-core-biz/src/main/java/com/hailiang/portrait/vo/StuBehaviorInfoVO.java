package com.hailiang.portrait.vo;

import com.hailiang.model.vo.*;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName StuBehaviorInfo
 * @Description 学生画像行为信息
 * <AUTHOR>
 * @Date 2024/6/19 16:42
 */
@Data
public class StuBehaviorInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String studentId;

    /**
     * 行为表现
     */
    private BehavioralExpressionVO expressionVO;
    /**
     * 智能评价
     */
    private BehaviourIntelligentEvaluationVO evaluationVO;
    /**
     * 智能评价
     */
    private BehaviourAnalysisVO behaviourAnalysisVO;
}
