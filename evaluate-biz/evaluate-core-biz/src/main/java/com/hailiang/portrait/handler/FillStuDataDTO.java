package com.hailiang.portrait.handler;

import com.hailiang.event.dto.BaseDTO;
import com.hailiang.model.entity.InitialScorePO;
import com.hailiang.model.vo.BehaviourRecordSynthesisScoreRankVO;
import com.hailiang.model.vo.ListModuleDetailVODateNewInfo;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.portrait.vo.StuBehaviorInfoVO;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import com.hailiang.portrait.vo.StuPortraitInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName FIllStuDataDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/4 14:08
 */
@Data
public class FillStuDataDTO extends BaseDTO implements Serializable {

    private StuPortraitInfoVO stuPortraitInfoVO;

    private StuBehaviorInfoVO stuBehaviorInfoVO;

    private StuPortraitQuery queryDTO;

    /**
     * 初始分
     */
    private List<InitialScorePO> initialScorePOs;

    private List<StudentDailyStatisticsPO> statisticsList;
    // TODO: 2024/11/8 初始分重新设计，无需加入初始分，暂时注释
//    private List<InitialScoreAllocation> allocationList;

    private List<BehaviourRecordSynthesisScoreRankVO> rankList;

    private List<StuBehaviorOptionVO> behaviourGroup;

    private List<ListModuleDetailVODateNewInfo> dataBehaviourList = new ArrayList<>();
    /**
     * 是否全部学期 1 全部学期 0 不是全部学期
     */
    private Boolean allTermFlag = false;
}
