package com.hailiang.portrait;

import cn.hutool.core.lang.Pair;
import com.alibaba.druid.util.StringUtils;
import com.hailiang.enums.InfoTypeEnum;
import com.hailiang.enums.StuPortraitBizTypeEnum;
import com.hailiang.event.dto.BaseDTO;
import com.hailiang.model.vo.BehaviourNameInfoVO;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName AbstractStuPortraitDetailHandler
 * @Description 收拢指责
 * <AUTHOR>
 * @Date 2024/7/4 13:58
 */
@Component
public abstract class AbstractPortraitDetailHandler<T extends BaseDTO> {

    protected abstract StuPortraitBizTypeEnum getBizType();

    public abstract void fill(T data);

    protected BigDecimal toBigDecimal(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }

    protected int getRanking(Map<String, BigDecimal> map, String key) {
        Pair<String, BigDecimal> pairKey = null;
        List<Pair<String, BigDecimal>> pairs = new ArrayList<>();
        Set<Map.Entry<String, BigDecimal>> entries = map.entrySet();
        //封装排序列表
        for(Map.Entry<String, BigDecimal> entry : entries){
            Pair<String, BigDecimal> targetPair = new Pair<>(entry.getKey(), entry.getValue());
            if(StringUtils.equals(key, entry.getKey())){
                pairKey = targetPair;
            }
            pairs.add(targetPair);
        }
        //降序排列
        pairs.sort((o1, o2) -> o2.getValue().compareTo(o1.getValue()));
        return pairs.indexOf(pairKey) + 1;
    }

    protected BehaviourNameInfoVO getBehaviourNameInfoVO(StuBehaviorOptionVO stuBehaviorOptionVO) {
        BehaviourNameInfoVO behaviourNameInfoVO = new BehaviourNameInfoVO();
        behaviourNameInfoVO.setBehaviourId(stuBehaviorOptionVO.getId());
        behaviourNameInfoVO.setBehaviourName(stuBehaviorOptionVO.getInfoName());
        behaviourNameInfoVO.setScore(Objects.nonNull(stuBehaviorOptionVO.getBehaviourScore()) ? stuBehaviorOptionVO.getBehaviourScore().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        // 如果是选项名称的填充指标名称
        if (InfoTypeEnum.OPTION.getCode().equals(stuBehaviorOptionVO.getInfoType())) {
            behaviourNameInfoVO.setTargetName(stuBehaviorOptionVO.getTargetName());
        }
        return behaviourNameInfoVO;
    }

    /**
     * 一段时间段内的成绩（学生，总分）
     */
    protected Map<String, BigDecimal> fillMap(List<StudentDailyStatisticsPO> classStatistics, Date startTime, Date endTime) {
        LinkedHashMap<String, BigDecimal> weekMap = classStatistics.stream()
                .filter(statistics -> statistics.getStatisticsTime().getTime() >= startTime.getTime())
                .filter(statistics -> statistics.getStatisticsTime().getTime() <= endTime.getTime())
                .collect(Collectors.groupingBy(StudentDailyStatisticsPO::getStudentId,
                        Collectors.mapping(StudentDailyStatisticsPO::getTotalScore,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .collect(Collectors.toMap(Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldV, newV) -> oldV, LinkedHashMap::new));
        // TODO: 2024/11/18  初始分重新设计，智能评价得分无需初始分，暂时注释
//        //填充初始分数据
//        List<InitialScoreAllocation> list = initialScoreAllocations.stream()
//                .filter(initialScoreAllocation -> initialScoreAllocation.getCreateTime().getTime() >= startTime.getTime())
//                .filter(initialScoreAllocation -> initialScoreAllocation.getCreateTime().getTime() <= endTime.getTime()).collect(Collectors.toList());
//        for (InitialScoreAllocation initialScoreAllocation : list) {
//            BigDecimal score = weekMap.get(initialScoreAllocation.getStudentId());
//            if(Objects.nonNull(score)){
//                    score = score.add(initialScoreAllocation.getInitialScore());
//            }else{
//                score = initialScoreAllocation.getInitialScore();
//            }
//            weekMap.put(initialScoreAllocation.getStudentId(), score);
//        }
        return weekMap;
    }
}
