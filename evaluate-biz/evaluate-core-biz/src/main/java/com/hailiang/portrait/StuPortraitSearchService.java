package com.hailiang.portrait;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.response.BehaviourStudentExportResponse;
import com.hailiang.model.vo.BehaviourStudentFileVO;
import com.hailiang.portrait.query.StuPortraitListQuery;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.portrait.vo.StuBehaviorInfoVO;
import com.hailiang.portrait.vo.StuPortraitInfoVO;
import java.util.List;

/**
 * @ClassName StuPortraitSearchService
 * @Description 学生画像查询方法
 * <AUTHOR>
 * @Date 2024/6/19 17:17
 */
public interface StuPortraitSearchService {

    /**
     * 学生画像列表（分页查询）
     */
    Page<BehaviourStudentFileVO> pageStuPortraitInfo(StuPortraitListQuery query);

    /**
     * 查询学生画像信息
     */
    StuPortraitInfoVO getStuPortraitInfo(StuPortraitQuery dto);

    /**
     * 查询学生行为信息
     */
    StuBehaviorInfoVO getStuBehaviorInfo(StuPortraitQuery dto);

    /**
     * 获取学生画像下载数据
     * @param dto
     * @return
     */
    List<BehaviourStudentExportResponse> listStudentPortraitExport(StuPortraitListQuery query);

}
