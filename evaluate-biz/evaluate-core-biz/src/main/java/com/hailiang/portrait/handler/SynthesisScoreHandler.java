package com.hailiang.portrait.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hailiang.constant.Constant;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.enums.StuPortraitBizTypeEnum;
import com.hailiang.model.entity.InitialScorePO;
import com.hailiang.model.vo.BehaviourRecordSynthesisScoreRankVO;
import com.hailiang.model.vo.BehaviourRecordSynthesisScoreVO;
import com.hailiang.portrait.AbstractPortraitDetailHandler;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.util.NumUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName SynthesisScoreHandler
 * @Description 综合得分
 * <AUTHOR>
 * @Date 2024/7/4 14:05
 */
@Component
public class SynthesisScoreHandler extends AbstractPortraitDetailHandler<FillStuDataDTO> {
    @Override
    public StuPortraitBizTypeEnum getBizType() {
        return StuPortraitBizTypeEnum.SYNTHESIS_SCORE;
    }

    @Override
    public void fill(FillStuDataDTO data) {
        BehaviourRecordSynthesisScoreVO synthesisScoreVO = new BehaviourRecordSynthesisScoreVO();
        List<StudentDailyStatisticsPO> dailyStatistics = data.getStatisticsList();
        StuPortraitQuery dto = data.getQueryDTO();
        List<InitialScorePO> initialScorePOs = data.getInitialScorePOs();
        List<BehaviourRecordSynthesisScoreRankVO> ranks = data.getRankList();

        //五育初始分
        BigDecimal initScore = BigDecimal.ZERO;
        if (data.getAllTermFlag()) {
            initScore = initialScorePOs.stream()
                    .filter(s -> ModuleEnum.OTHER.getCode().equals(s.getInitialScoreType()))
                    .map(InitialScorePO::getInitialScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO, RoundingMode.HALF_UP);
        } else {
            Optional<InitialScorePO> first = initialScorePOs
                    .stream()
                    .filter(s -> ModuleEnum.OTHER.getCode().equals(s.getInitialScoreType()))
                    .findFirst();
            if (first.isPresent() && BeanUtil.isNotEmpty(first.get()) && ObjectUtil.isNotNull(first.get().getInitialScore())) {
                initScore = first.get().getInitialScore();
            }
        }
        //综合总加分
        BigDecimal normalAddScore = dailyStatistics.stream()
                .filter(d -> d.getStudentId().equals(dto.getStudentId()))
                .map(StudentDailyStatisticsPO::getPlusTotalScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO, RoundingMode.HALF_UP);
        synthesisScoreVO.setAddScore(normalAddScore);
        // TODO: 2024/11/8 初始分重新设计，无需加入初始分，暂时注释
//        //分配分加分
//        BigDecimal initAddScore = initialScoreAllocations.stream()
//                .filter(d -> Constant.ONE.equals(d.getScoreType()) && d.getStudentId().equals(dto.getStudentId()))
//                .map(InitialScoreAllocation::getInitialScore)
//                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO, RoundingMode.HALF_UP);
        //总加分 = 综合总加分 + 分配分加分
        BigDecimal totalAddScore = normalAddScore;
        synthesisScoreVO.setTotalSumScore(totalAddScore);
        //综合总减分
        BigDecimal normalSubScore = dailyStatistics.stream()
                .filter(d -> d.getStudentId().equals(dto.getStudentId()))
                .map(StudentDailyStatisticsPO::getMinusTotalScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO, RoundingMode.HALF_UP);
        synthesisScoreVO.setSubtractScore(normalSubScore);
        // TODO: 2024/11/8 初始分重新设计，无需加入初始分，暂时注释
//        //分配分减分
//        BigDecimal initSubScore = initialScoreAllocations.stream()
//                .filter(d -> Constant.TWO.equals(d.getScoreType()) && d.getStudentId().equals(dto.getStudentId()))
//                .map(InitialScoreAllocation::getInitialScore)
//                .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(Constant.TWO, RoundingMode.HALF_UP);
        //总减分 = 综合总减分 - 分配分减分
        BigDecimal totalSubScore = normalSubScore;
        synthesisScoreVO.setTotalSubScore(totalSubScore);

        // 总得分 = 总加分值-总减分值 + 初始分
        BigDecimal sumScore = totalAddScore.add(totalSubScore).add(initScore);
        //初始分
        synthesisScoreVO.setInitialScore(initScore);
        synthesisScoreVO.setSumScore(sumScore);

        //总分= 总加分+总减分（取绝对值）
        BigDecimal totalScore = normalAddScore.add(normalSubScore.abs());
        //加分占比  加分总分(不加分配分） / 总分
        if (totalScore.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal addRate = NumberUtil.equals(totalScore, BigDecimal.ZERO) ?
                    BigDecimal.ZERO : normalAddScore.divide(totalScore, Constant.THREE, RoundingMode.HALF_UP);
            synthesisScoreVO.setAddScoreRate(NumUtil.convertToPercent(addRate, Constant.ONE));

            //减分占比 减分总分(不加分配分）/ 总分
            BigDecimal subRate = NumberUtil.equals(totalScore, BigDecimal.ZERO) ?
                    BigDecimal.ZERO : normalSubScore.divide(totalScore, Constant.THREE, RoundingMode.HALF_UP);
            synthesisScoreVO.setSubtractScoreRate(NumUtil.convertToPercent(subRate.abs(), Constant.ONE));
        }

        // 全班得分排名list， 超越了多少学生人数, 这个分数不加分配分 用于比较班内总分排比
        //超越全班百分比 计算公式为 超越人数/全部总人数，如果就一个人，正分超越100%，负分0%
        long passPeopleNum = ranks.stream().filter(d -> Constant.NEGATIVE_ONE.equals(d.getSumScore().compareTo(sumScore))).count();

        String surpassRate = null;
        if (!ranks.isEmpty()) {
            surpassRate = new BigDecimal(passPeopleNum)
                    .divide(BigDecimal.valueOf(ranks.size()), Constant.THREE, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(Constant.HUNDRED))
                    .setScale(Constant.ONE, RoundingMode.HALF_UP)
                    .stripTrailingZeros().toPlainString() + Constant.PERCENT;
        }

        //只有一个人且是一条行为情况且分数为0的情况
        if (Constant.ZERO.equals((int) passPeopleNum) && Constant.ONE.equals(dailyStatistics.size())) {
            surpassRate = dailyStatistics.get(0).getTotalScore().signum() > 0 ?
                    new BigDecimal(Constant.HUNDRED).toPlainString() + Constant.PERCENT :
                    BigDecimal.ZERO.toPlainString() + Constant.PERCENT;
        }
        synthesisScoreVO.setSurpassRate(surpassRate);
        //结果集
        data.getStuPortraitInfoVO().setSynthesisScoreVO(synthesisScoreVO);
    }
}
