package com.hailiang.portrait.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 数据初始化、修复
 * <AUTHOR>
 * @date 2024/7/26
 */
@Data
public class ArchivedInitQuery {

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    /**
     * 是否需要重试/重新计算
     */
    private Boolean retry;

    /**
     * 学校id
     */
    private String schoolId;
    /**
     * 校区id
     */
    private String campusId;

}
