package com.hailiang.portrait.vo;

import com.hailiang.model.vo.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName StuPortraitInfoVO
 * @Description 学生画像基础信息
 * <AUTHOR>
 * @Date 2024/6/19 16:42
 */
@Data
public class StuPortraitInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    //学生基础信息
    /**
     * 学生id
     */
    private String studentId;
    /**
     * 学生姓名
     */
    private String studentName;
    /**
     * 学生性别：0未知；1男；2女
     */
    private Integer sex;
    /**
     * 班级名称
     */
    private String className;
    /**
     * 班主任名称
     */
    private String headMasterName;
    /**
     * 监护人姓名
     */
    private String guardianName;
    /**
     * 学生数量
     */
    private Integer studentCount;
    /**
     * 家长姓名列表
     */
    private List<String> parentNameList = new ArrayList<>();

    /**
     * 综合得分
     */
    private BehaviourRecordSynthesisScoreVO synthesisScoreVO;
    /**
     * 五育雷达
     */
    private List<BehaviourComprehensiveQualityVO> qualityVOList;
    /**
     * 成长趋势
     */
    private BehaviourGrowthTrendVO growthTrendVO;
    /**
     * 五育明细
     */
    private ListModuleDetailNewVO moduleDetailNewVO;
}
