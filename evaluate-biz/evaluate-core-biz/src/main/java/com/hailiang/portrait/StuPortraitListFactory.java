package com.hailiang.portrait;

import com.hailiang.enums.StuPortraitBizTypeEnum;
import com.hailiang.exception.BizException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Component
public class StuPortraitListFactory {

    @Resource
    private Map<String, StuPortraitListHandler> handlerMap;

    public StuPortraitListHandler getService(StuPortraitBizTypeEnum bizType){
        StuPortraitListHandler handler = handlerMap.get(bizType.getName());
        if (Objects.isNull(handler)){
            throw new BizException("没有处理器类");
        }
        return handler;
    }

}
