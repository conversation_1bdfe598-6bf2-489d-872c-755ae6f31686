package com.hailiang.portrait.handler;

import com.hailiang.enums.StuPortraitBizTypeEnum;
import com.hailiang.model.vo.BehaviourAnalysisVO;
import com.hailiang.model.vo.BehaviourAnalysisVOInnerModuleVO;
import com.hailiang.portrait.AbstractPortraitDetailHandler;
import com.hailiang.service.BehaviourRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.hailiang.enums.StuPortraitBizTypeEnum.ANALYSIS;
import static com.hailiang.enums.StuPortraitBizTypeEnum.EXPRESS;

/**
 * @ClassName BehaviourOptionHandler
 * @Description 行为表现
 * <AUTHOR>
 * @Date 2024/7/4 14:36
 */
@Component
@Slf4j
public class AnalysisHandler extends AbstractPortraitDetailHandler<FillStuDataDTO> {

    @Resource
    private BehaviourRecordService behaviourRecordService;

    @Override
    public StuPortraitBizTypeEnum getBizType() {
        return ANALYSIS;
    }

    @Override
    public void fill(FillStuDataDTO data) {
        List<BehaviourAnalysisVOInnerModuleVO> behaviourAnalysis = behaviourRecordService.getBehaviourAnalysis(data.getBehaviourGroup());
        BehaviourAnalysisVO behaviourAnalysisVO = new BehaviourAnalysisVO();
        behaviourAnalysisVO.setModuleList(behaviourAnalysis);

        data.getStuBehaviorInfoVO().setBehaviourAnalysisVO(behaviourAnalysisVO);
    }
}
