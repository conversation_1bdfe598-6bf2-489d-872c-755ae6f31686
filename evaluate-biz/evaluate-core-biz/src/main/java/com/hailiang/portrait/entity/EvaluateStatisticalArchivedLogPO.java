package com.hailiang.portrait.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 指标行为聚合任务执行记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Getter
@Setter
@TableName("evaluate_statistical_archived_log")
public class EvaluateStatisticalArchivedLogPO extends BaseEntity {

    /**
     * 任务标志 0：重算任务, 1：教师任务 2：学生任务 3：兜底重试任务 4：重试接口
     */
    @TableField("biz_type")
    private Integer bizType;

    @TableField("daily")
    private Date daily;

    /**
     * 归档状态，1-成功，2-失败，默认成功
     */
    @TableField("status")
    private Integer status;

    /**
     * 归档记录备注信息（明细记录、失败原因等）
     */
    @TableField("remark")
    private String remark;

    public static final String BIZ_TYPE = "biz_type";

    public static final String DAILY = "daily";

    public static final String DELETED = "deleted";

    public static final String START_TIME = "start_time";

    public static final String END_TIME = "end_time";

}
