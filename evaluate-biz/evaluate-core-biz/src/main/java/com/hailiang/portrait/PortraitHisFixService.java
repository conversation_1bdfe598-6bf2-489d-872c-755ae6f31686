package com.hailiang.portrait;

import com.hailiang.portrait.query.ArchivedInitQuery;

/**
 * @ClassName StuPortraitSearchService
 * @Description 学生画像-历史数据处理
 * <AUTHOR>
 * @Date 2024/6/19 17:17
 */
public interface PortraitHisFixService {

    /**
     * 处理评价人评价T+1历史数据
     */
    void fixStaffStatics(ArchivedInitQuery dto);

    /**
     * 处理学生评价T+1历史数据
     */
    void fixStuStatics(ArchivedInitQuery dto);

    /**
     * 处理学生行为指标分类T+1历史数据
     */
    void fixStuClassifyStatics(ArchivedInitQuery dto);

}
