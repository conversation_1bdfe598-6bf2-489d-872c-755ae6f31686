package com.hailiang.portrait.query;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hailiang.constant.Constant;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class StuPortraitListQuery {
    /**
     * 学段id
     */
    @NotBlank(message = "校区学段不能为空")
    private String campusSectionId;

    /**
     * 学年
     */
    private String schoolYear;

    /**
     * 是否是当前学期
     * 暂时未使用到，查历史学年时自由选择时间框隐藏，前端默认传当前学期开始/结束时间
     */
    private Boolean isCurrentTerm;

    /**
     * 是否当前学年
     */
    private Boolean isCurrentYear;

    /**
     * 学期（上学期、下学期）
     */
    private String termName;

    /**
     * 年级id，前端传[-1/null]表示查全部年级
     */
    private String gradeId;

    /**
     * 班级id，前端传[-1/null]表示查全部班级
     */
    private String classId;

    /**
     * 班级id集合
     */
    private List<Long> classIds;
    /**
     * 学生id
     */
    private String studentId;

    /**
     * 表单提交开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    /**
     * 表单提交结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    /**
     * 学生姓名模糊查询
     */
    private String nameLike;

    /**
     * 指标 id
     */
    private String targetId;

    /**
     * 模块code
     * @see com.hailiang.enums.ModuleEnum
     */
    private Integer moduleCode;

    /**
     * 是否统计学生帮扶积分
     */
    private Boolean includeHelpBehaviour;

    /**
     * 是否统计老师自建指标加分（星动力侧加分）
     */
    private Boolean includeInternalScoreFlag;

    /**
     * 分页页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1,message = "页码最小值为1")
    private Integer pageNum = 1;
    /**
     * 分页页数
     */
    @NotNull(message = "页数不能为空")
    private Integer pageSize = 10;

    /**
     * 排序类型 1 升序 2 降序
     */
    private Integer sortType;

    /**
     * 前端传[-1/null]查全部年级/班级，数据库查询时直接传空
     * @return
     */
    public String getGradeId() {
        if (Objects.equals(Constant.MINUS_ONE, this.gradeId)) {
            this.gradeId = null;
        }
        return this.gradeId;
    }
    public String getClassId() {
        if (Objects.equals(Constant.MINUS_ONE, this.classId)) {
            this.classId = null;
        }
        return this.classId;
    }

/*    public void setStartTime(String startTime) {

        this.startTime =StrUtil.isNotBlank(startTime) ? DateUtil.beginOfDay(DateUtil.parse(startTime)) : null;
    }

    public void setEndTime(String endTime) {
        this.endTime = StrUtil.isNotBlank(endTime) ? DateUtil.endOfDay(DateUtil.parse(endTime)) : null;
    }*/

}
