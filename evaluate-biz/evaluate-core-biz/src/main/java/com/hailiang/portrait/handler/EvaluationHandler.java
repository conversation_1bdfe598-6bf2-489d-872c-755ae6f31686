package com.hailiang.portrait.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.map.MapUtil;
import com.deepoove.poi.xwpf.NumFormat;
import com.hailiang.constant.Constant;
import com.hailiang.enums.StuPortraitBizTypeEnum;
import com.hailiang.logic.TermLogic;
import com.hailiang.model.vo.BehaviourIntelligentEvaluationVO;
import com.hailiang.model.vo.BehaviourNameInfoVO;
import com.hailiang.portrait.AbstractPortraitDetailHandler;
import com.hailiang.portrait.StuPortraitService;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.portrait.vo.StuBehaviorOptionVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.service.HelpBehaviourRecordService;
import com.hailiang.util.NumUtil;
import io.netty.util.internal.ThrowableUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.hailiang.enums.StuPortraitBizTypeEnum.EVALUATION;

/**
 * @ClassName BehaviourOptionHandler
 * @Description 智能评价
 * <AUTHOR>
 * @Date 2024/7/4 14:36
 */
@Component
@Slf4j
public class EvaluationHandler extends AbstractPortraitDetailHandler<FillStuDataDTO> {

    @Resource
    private StuPortraitService stuPortraitService;
    @Resource
    private TermLogic termLogic;
    @Resource
    private HelpBehaviourRecordService helpBehaviourRecordService;

    @Override
    public StuPortraitBizTypeEnum getBizType() {
        return EVALUATION;
    }

    @Override
    public void fill(FillStuDataDTO data) {
        BehaviourIntelligentEvaluationVO evaluationVO = new BehaviourIntelligentEvaluationVO();

        List<StuBehaviorOptionVO> vos = data.getBehaviourGroup();
        StuPortraitQuery dto = data.getQueryDTO();
        List<StudentDailyStatisticsPO> statisticsList = data.getStatisticsList();

        //总分=各选项总分之和
        BigDecimal score = vos.stream()
                .map(StuBehaviorOptionVO::getBehaviourScoreNoScale)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        //本周
        DateTime beginOfWeek = DateUtil.beginOfWeek(dto.getEndTime());
        DateTime endOfWeek = DateUtil.endOfWeek(dto.getEndTime());
        //上周
        DateTime lastBeginOfWeek = DateUtil.offsetWeek(beginOfWeek, Constant.NEGATIVE_ONE);
        DateTime lastEndOfWeek = DateUtil.endOfWeek(lastBeginOfWeek);
        Date startTime = dto.getStartTime();
        Date endTime = dto.getEndTime();
        StuPortraitQuery weekQuery = BeanUtil.copyProperties(dto, StuPortraitQuery.class);
        if(startTime.after(lastBeginOfWeek) || endTime.before(endOfWeek)){
            //查询时间不包含当前周和上一周的时间，则直接重新计算
            try {
                TimeInterval start = DateUtil.timer();
                statisticsList = rebuildWeekStatisticsList(statisticsList, weekQuery, lastBeginOfWeek, endOfWeek);
                log.info("[学生画像]-[查询学生指标数据接口]-重构周统计数据耗时:{}", start.intervalMs());
            } catch (Exception ex) {
                log.error("[学生画像]-[查询学生指标数据接口]-重构周统计数据出现异常，异常信息为:{}", ThrowableUtil.stackTraceToString(ex));
            }
        }

        // 初始分
        TimeInterval start = DateUtil.timer();
        // TODO: 2024/11/18  初始分重新设计，智能评价得分无需初始分，暂时注释
//        List<InitialScoreAllocation> initialScoreAllocations = initialScoreAllocationManager.list(dto);

        //综合得分添加初始分
//        double initialScoreSum = initialScoreAllocations
//                .stream()
//                .filter(initialScoreAllocation -> StringUtils.equals(initialScoreAllocation.getStudentId(),dto.getStudentId()))
//                .filter(initialScoreAllocation -> initialScoreAllocation.getCreateTime().getTime() >= startTime.getTime())
//                .filter(initialScoreAllocation -> initialScoreAllocation.getCreateTime().getTime() <= endTime.getTime())
//                .mapToDouble(initialScoreAllocation -> initialScoreAllocation.getInitialScore().doubleValue())
//                .sum();
        //综合得分加入初始分
        evaluationVO.setSumScore(NumUtil.formatFloatNumber(toBigDecimal(score).setScale(Constant.TWO,RoundingMode.HALF_UP)));
        evaluationVO.setStartTime(dto.getStartTime());
        evaluationVO.setEndTime(dto.getEndTime());

        log.info("[学生画像-查询学生指标数据接口] [查询初始分] 耗时：{}", start.intervalMs());
        //本周班级排名
        Map<String, BigDecimal> weekMap = fillMap(statisticsList, beginOfWeek, endOfWeek);
        //筛选时间截止日期所在周得分
        evaluationVO.setLastWeekScore(NumUtil.formatFloatNumber(weekMap.get(dto.getStudentId())));
        //上周班级排名
        Map<String, BigDecimal> lassWeekMap = fillMap(statisticsList, lastBeginOfWeek, lastEndOfWeek);

        //上周或者本周没数据，则不参与排名
        if (MapUtil.isEmpty(lassWeekMap) || MapUtil.isEmpty(weekMap)) {
            evaluationVO.setProgressRank(null);
        } else {
            // 上周名次 - 本周名次
            evaluationVO.setProgressRank(getRanking(lassWeekMap, dto.getStudentId()) - getRanking(weekMap, dto.getStudentId()));
        }

        // 2个最高分
        List<BehaviourNameInfoVO> maxScoreList = data.getStuBehaviorInfoVO().getExpressionVO().getPraiseBehaviourList().stream()
                .sorted(Comparator.comparing(BehaviourNameInfoVO::getScore).reversed())
                .limit(Constant.TWO)
                .collect(Collectors.toList());
        evaluationVO.setBestBehaviour(maxScoreList);
        // 2个最低分
        List<BehaviourNameInfoVO> minScoreList = data.getStuBehaviorInfoVO().getExpressionVO().getToImproveBehaviourList().stream()
                .sorted(Comparator.comparing(BehaviourNameInfoVO::getScore))
                .limit(Constant.TWO)
                .collect(Collectors.toList());
        evaluationVO.setWorstBehaviour(minScoreList);

        data.getStuBehaviorInfoVO().setEvaluationVO(evaluationVO);
    }

    /**
     * 重新构建上周数据
     *
     * @param statisticsList
     * @param weekQuery
     */
    private List<StudentDailyStatisticsPO> rebuildWeekStatisticsList(List<StudentDailyStatisticsPO> statisticsList, StuPortraitQuery weekQuery, Date startTime, Date endTime) {
        Long campusSectionId = null;
        Long schoolId = null;
        Long campusId = null;
        //重新计算时间
        if (CollectionUtil.isEmpty(statisticsList)) {
            return statisticsList;
        }
        //获取当前学期
        campusSectionId = Long.valueOf(statisticsList.get(0).getCampusSectionId());
        schoolId = Long.valueOf(statisticsList.get(0).getSchoolId());
        campusId = Long.valueOf(statisticsList.get(0).getCampusId());
        TermVo currentTermVo = termLogic.getCurrentTermVo(schoolId, campusId, campusSectionId, weekQuery.getEndTime());
        if (Objects.isNull(currentTermVo)) {
            return statisticsList;
        }
        //重新计算上周数据
        DateTime termStartTime = DateUtil.parseDate(currentTermVo.getStartTime());
        DateTime termEndTime = DateUtil.parseDate(StringUtils.isBlank(currentTermVo.getEndTimeWithVacation()) ? currentTermVo.getEndTime() : currentTermVo.getEndTimeWithVacation());
        //校验查询时间与学期时间
        if (termStartTime.after(endTime) || termEndTime.before(startTime)) {
            //查询时间超过当前学期范围，不予查询
            return statisticsList;
        }
        if (startTime.before(termStartTime)) {
            startTime = termStartTime;
        }
        if (endTime.after(termEndTime)) {
            endTime = termEndTime;
        }
        weekQuery.setStartTime(startTime);
        weekQuery.setEndTime(endTime);
        List<StudentDailyStatisticsPO> newClassStatistics = stuPortraitService.listStudentDailyStatisticsDoris(weekQuery);
        if (Objects.nonNull(weekQuery.getIncludeHelpBehaviour()) && weekQuery.getIncludeHelpBehaviour()) {
            //帮扶行为
            helpBehaviourRecordService.getAndMergeHelpBehaviourClassStatistics(newClassStatistics, weekQuery);
        }
        return newClassStatistics;
    }
}
