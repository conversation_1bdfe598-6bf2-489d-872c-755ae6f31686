package com.hailiang.portrait.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName BehaviourRecordStaticsVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/21 16:46
 */
@Data
public class BehaviourRecordStaticsVO implements Serializable {

    private String tenantId;

    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 学段id
     */
    private String campusSectionId;

    private String campusSectionCode;

    /**
     * 年级id
     */
    private String gradeId;

    private String gradeCode;

    private BigDecimal score;

    private Integer scoreType;

    private String studentId;

    private String classId;

    private Integer moduleCode;
}
