package com.hailiang.portrait.handler;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.hailiang.constant.Constant;
import com.hailiang.enums.StuPortraitBizTypeEnum;
import com.hailiang.enums.TimeEnum;
import com.hailiang.model.vo.BehaviourGrowthTrendVO;
import com.hailiang.model.vo.BehaviourGrowthTrendVOInner;
import com.hailiang.portrait.AbstractPortraitDetailHandler;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.query.StuPortraitQuery;
import com.hailiang.portrait.vo.StuPortraitInfoVO;
import com.hailiang.util.DateSplitter;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.hailiang.enums.StuPortraitBizTypeEnum.GROWTH_TREND;

/**
 * @ClassName GrowthTrendHandler
 * @Description 得分趋势
 * <AUTHOR>
 * @Date 2024/7/4 14:19
 */
@Component
public class GrowthTrendHandler extends AbstractPortraitDetailHandler<FillStuDataDTO> {
    @Override
    public StuPortraitBizTypeEnum getBizType() {
        return GROWTH_TREND;
    }

    @Override
    public void fill(FillStuDataDTO data) {
        StuPortraitQuery dto = data.getQueryDTO();
        List<StudentDailyStatisticsPO> classDailyStatistics = data.getStatisticsList();
        StuPortraitInfoVO stuPortraitInfoVO = data.getStuPortraitInfoVO();
        Integer studentCount = stuPortraitInfoVO.getStudentCount();
        long betweenDay = DateUtil.betweenDay(dto.getStartTime(), dto.getEndTime(), true);
        BehaviourGrowthTrendVO behaviourGrowthTrendVO = new BehaviourGrowthTrendVO();
        behaviourGrowthTrendVO.setWeekOrMonthCode(betweenDay > Constant.THIRTY ? TimeEnum.MONTH.getCode() : TimeEnum.WEEK.getCode());
        behaviourGrowthTrendVO.setWeekOrMonthName(TimeEnum.getValueByCode(behaviourGrowthTrendVO.getWeekOrMonthCode()));

        long between = DateUtil.between(dto.getStartTime(), dto.getEndTime(), DateUnit.DAY);
        List<DateSplitter.Segment> dateSegments;
        if (between <= 30) {
            dateSegments = DateSplitter.splitIntoWeeks(LocalDateTimeUtil.of(dto.getEndTime()), 5);
        } else {
            dateSegments = DateSplitter.splitInfoMonth(LocalDateTimeUtil.of(dto.getStartTime()), LocalDateTimeUtil.of(dto.getEndTime()));
        }

        List<BehaviourGrowthTrendVOInner> growthTrendInnerList = new ArrayList<>();
        List<StudentDailyStatisticsPO> studentDailyStatisticPOS = classDailyStatistics.stream()
                .filter(statistics -> dto.getStudentId().equals(statistics.getStudentId()))
                .collect(Collectors.toList());

        //周计数器，用来表示一周前，两周前
        int count = 0;
        List<DateSplitter.Segment> collect = dateSegments.stream()
                .sorted(Comparator.comparing(DateSplitter.Segment::getEndDate).reversed())
                .collect(Collectors.toList());
        for (DateSplitter.Segment segment : collect) {
            //学生分数
            BigDecimal stuScore = studentDailyStatisticPOS.stream()
                    .filter(statistics -> {
                        long startEpochMilli = segment.getStartDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                        long endEpochMilli = segment.getEndDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                        return statistics.getStatisticsTime().getTime() >= startEpochMilli && statistics.getStatisticsTime().getTime() <= endEpochMilli;
                    }).collect(Collectors.toList()).stream()
                    .map(StudentDailyStatisticsPO::getTotalScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(Constant.TWO, RoundingMode.HALF_UP);
            //班级总分
            List<StudentDailyStatisticsPO> dailyStatistics = classDailyStatistics.stream()
                    .filter(statistics -> {
                        long startEpochMilli = segment.getStartDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                        long endEpochMilli = segment.getEndDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                        return statistics.getStatisticsTime().getTime() >= startEpochMilli && statistics.getStatisticsTime().getTime() <= endEpochMilli;
                    })
                    .collect(Collectors.toList());
            BigDecimal classScore = dailyStatistics.stream()
                    .map(StudentDailyStatisticsPO::getTotalScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(Constant.TWO, RoundingMode.HALF_UP);

            //班级评分人数
            long stuSize = dailyStatistics.stream().map(StudentDailyStatisticsPO::getStudentId).distinct().count();

            //班级平均分（总分/班级总人数）
            BehaviourGrowthTrendVOInner behaviourGrowthTrendVoInner = new BehaviourGrowthTrendVOInner();
            behaviourGrowthTrendVoInner.setStudentScore(stuScore);
            behaviourGrowthTrendVoInner.setClassAvgScore(BigDecimal.ZERO);
            if (stuSize > 0) {
                behaviourGrowthTrendVoInner.setClassAvgScore(classScore.divide(BigDecimal.valueOf(stuSize), Constant.TWO, RoundingMode.HALF_UP));
            }
            behaviourGrowthTrendVoInner.setWeekMonthNumber(behaviourGrowthTrendVO.getWeekOrMonthCode().equals(TimeEnum.WEEK.getCode()) ? count : segment.getStartDate().getMonthValue());
            growthTrendInnerList.add(Constant.ZERO, behaviourGrowthTrendVoInner);
            count++;
        }
        //最高分以及最低分
        fillGrowthTopAndLowScore(growthTrendInnerList, behaviourGrowthTrendVO);
        behaviourGrowthTrendVO.setBehaviourGrowthTrendVOInnerList(growthTrendInnerList);
        data.getStuPortraitInfoVO().setGrowthTrendVO(behaviourGrowthTrendVO);
    }

    public void fillGrowthTopAndLowScore(List<BehaviourGrowthTrendVOInner> growthTrendVOList,
                                         BehaviourGrowthTrendVO behaviourGrowthTrendVO) {
        List<BigDecimal> classAvgScoreList = growthTrendVOList.stream()
                .map(BehaviourGrowthTrendVOInner::getClassAvgScore)
                .collect(Collectors.toList());
        List<BigDecimal> studentScoreList = growthTrendVOList.stream()
                .map(BehaviourGrowthTrendVOInner::getStudentScore)
                .collect(Collectors.toList());
        classAvgScoreList.addAll(studentScoreList);
        Collections.sort(classAvgScoreList);
        behaviourGrowthTrendVO.setMaxScore(classAvgScoreList.get(classAvgScoreList.size() - 1));
        behaviourGrowthTrendVO.setMinScore(classAvgScoreList.get(0));
    }
}
