package com.hailiang.model.domain.studentmodel.compare;

import com.hailiang.model.domain.studentmodel.StudentAbilityModelSubjectBusinessItemConfigDO;
import com.hailiang.model.domain.studentmodel.StudentAbilityModelTargetBusinessItemConfigDO;
import com.hailiang.model.entity.SubjectBusinessMergePO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 学生能力模型科目-业务对比领域对象
 * @author: panjian
 * @create: 2024/11/25 14:46
 * @Version 1.0
 */
@Data
public class StudentAbilityModelSubjectBusinessItemConfigCompareDO implements Serializable {

    /**
     * 是否更新能力项配置
     */
    private Boolean updateConfig;

    /**
     * 是否删除能力项配置
     */
    private Boolean deleteConfig;

    /**
     * 是否新增能力项配置
     */
    private Boolean addConfig;

    /**
     * 学科-业务关联对象
     */
    private List<SubjectBusinessMergePO> subjectBusinessMergePOList;
}