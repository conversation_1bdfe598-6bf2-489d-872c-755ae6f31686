package com.hailiang.model.response.aliai;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 模型相关信息
 *
 * @Description: 模型相关信息
 * @Author: JJl
 * @Date: Created in 2024-12-16
 * @Version: 2.1.1
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AiWriterUsageModelVO {
    /**
     * 用户输入文本转换成Token后的长度
     */
    @JsonProperty("input_tokens")
    private int    inputTokens;

    /**
     * 模型生成回复转换为Token后的长度
     */
    @JsonProperty("output_tokens")
    private int    outputTokens;

    /**
     * 本次应用调用到的模型
     */
    @JsonProperty("model_id")
    private String modelId;
}
