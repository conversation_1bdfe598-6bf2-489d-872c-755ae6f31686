/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.model.report.vo.report;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v0.1: ReportTemplateSnapshotVO.java, v 0.1 2023年11月09日 09:19  zhousx Exp $
 */
@Data
public class ReportTemplateSnapshotVO {

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long                         id;

    /**
     * 模板名称
     */
    private String                       reportTemplateName;

    /**
     * 校区学段关联ID
     */
    private String                       campusSectionId;

    /**
     * 学段code
     */
    private String                       campusSectionCode;

    /**
     * 模板关联年级列表
     */
    private List<ReportTemplateGradeVO>  gradeList;

    /**
     * @see com.hailiang.enums.report.ReportTemplateTypeEnum
     * 报告模板类型 1:期末报告;2:日常报告
     */
    private Integer                      reportTemplateType;

    /**
     * 取数开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date                         startDate;

    /**
     * 取数结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date                         endDate;

    /**
     * 栏目列表
     */
    private List<ReportTemplateColumnVO> templateColumnList;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date                         createTime;

    /**
     * 通用背景URL
     */
    private String                       backgroundUrl;

    /**
     * 通用字体颜色
     */
    private String                       fontColor;
}