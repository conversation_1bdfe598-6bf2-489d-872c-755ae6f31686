package com.hailiang.model.dto;

import com.hailiang.enums.report.ReportEvaluateTypeEnum;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class StudentKuderDataQueryDTO {

    /**
     * 需要导出的班级信息
     */
    @NotEmpty(message = "classIds不能为空")
    private List<Long> classIds;

    /**
     * 校区id
     */
    private Long campusId;

    /**
     * 评语所在学年
     */
    private String schoolYear;

    /**
     * 评语所在学期
     */
    private String termName;

    /**
     * 评语类型类型：１日常评语、２期末评语 {@link ReportEvaluateTypeEnum}
     */
    private Integer evaluateType;

    /**
     * 评价人类型
     *
     * @see com.hailiang.enums.report.ReportEvaluatorTypeEnum 1 班主任 2学科老师
     */
    private Integer evaluatorType;
}
