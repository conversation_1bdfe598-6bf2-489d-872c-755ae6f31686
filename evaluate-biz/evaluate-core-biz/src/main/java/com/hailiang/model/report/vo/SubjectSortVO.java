package com.hailiang.model.report.vo;

import cn.hutool.core.text.StrPool;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/11 09:57
 */
@Data
public class SubjectSortVO {

    /**
     * 科目id
     */
    private String subjectId;

    /**
     * 科目code
     */
    private String subjectCode;

    /**
     * 科目名称
     */
    private String subjectName;

    /**
     * 使用状态  1:正常  2:备选
     */
    private Integer useStatus;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 科目类型
     * {@link com.hailiang.enums.SubjectTypeEnum }
     * 1课程 2科目
     */
    private Integer subjectType;

    /**
     * 父级科目id
     */
    private String parentDisciplineId;

    /**
     * 父级科目code
     */
    private String parentDisciplineCode;

    /**
     * 子集课程列表
     */
    private List<SubjectSortVO> subjectSortVOS;


    /**
     * 创建一个基于subjectCode和subjectType的唯一键
     */
    public String createUniqueKey() {
        return subjectCode + StrPool.AT + subjectType; // 使用某个分隔符连接两个字段的值
    }
}
