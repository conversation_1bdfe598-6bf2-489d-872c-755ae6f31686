package com.hailiang.model.report.response;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 报告单id请求参数
 * @Author: huyouting
 * @Date: Created in 2024-12-21
 * @Version: v2.1.1
 */
@Data
public class ReportFilePackageResponse {

    /**
     * 打包状态 1：转pdf中（快速返回）  2：转pdf失败，重新触发生成pdf（快速返回,提示同状态0）3：转pdf完成 4：正在打包中（返回用户正在打包中，用户可关闭窗口） 5：打包完成 （前端直接使用zipUrl下载）
     */
    private Integer packageStatus;
    /**
     * 压缩包的url
     */
    private String zipUrl;
    /**
     * 返回给用户的信息
     */
    private String message;
    /**
     * taskId
     */
    private String taskId;
}
