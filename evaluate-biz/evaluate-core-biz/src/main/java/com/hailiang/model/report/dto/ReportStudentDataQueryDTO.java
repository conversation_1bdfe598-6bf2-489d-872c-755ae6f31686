/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.model.report.dto;

import cn.hutool.core.lang.Assert;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v0.1: ReportStudentDataQueryDTO.java, v 0.1 2023年11月27日 15:53  zhousx Exp $
 */
@Data
public class ReportStudentDataQueryDTO {
    /**
     * 报告单id
     */
    @NotNull(message = "reportDataId不能为空")
    private Long reportDataId;

    /**
     * 学生ids
     */
    private List<String> studentIds;

    public void valid() {
        Assert.notNull(reportDataId,() -> new IllegalArgumentException("报告单ID不能为空，请核对！"));
    }
}