package com.hailiang.model.report.query.realdata;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 学生报告单查询请求参数
 * @Author: huyouting
 * @Date: Created in 2025-05-09
 * @Version: v2.5.7
 */
@Data
public class ReportStudentBehaviourQry {
    /**
     * 报告单id
     */
    @NotNull
    private Long reportDataId;
    /**
     * 学校id
     */
    private Long schoolId;
    /**
     * 校区id
     */
    private Long campusId;

    /**
     * 班级id
     */
    private Long classId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 学生ID
     */
    private List<Long> studentIds;

    /**
     * 学年
     */
    private String schoolYear;
    /**
     * 学期
     */
    private String termName;
    /**
     * 学段id
     */
    @NotNull
    private String campusSectionId;
}
