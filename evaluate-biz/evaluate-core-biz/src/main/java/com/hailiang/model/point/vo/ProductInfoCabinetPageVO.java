package com.hailiang.model.point.vo;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品信息表分页视图对象
 *
 * <AUTHOR> 2023-06-29 16:32:36
 */
@Data
public class ProductInfoCabinetPageVO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品类型 1：实体 2：虚拟
     */
    private String productType;
    /**
     * 需要的积分
     */
    private BigDecimal needPoint;
    /**
     * 图片地址
     */
    private List<String> pictureUrl;
    /**
     * 积分柜库存
     */
    private Integer stockCabinet;
  }