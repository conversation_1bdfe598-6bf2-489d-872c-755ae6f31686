package com.hailiang.model.dto;

import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.point.dto.PointConvertDTO;
import com.hailiang.model.point.dto.PointInitialMsgConvertDTO;
import com.hailiang.pipeline.ProcessModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 行为记录转积分消息参数
 *
 * @Description: 行为记录转积分参数
 * @Author: Jovi
 * @Date: Created in 2024/11/8
 * @Version: 2.0.0
 */
@Data
public class BehaviourRecordMqDTO implements ProcessModel {

    /**
     * 消息类型(null：原始点评记录转积分，1：初始分转积分)
     */
    private Integer msgType;

    /**
     * 实际操作类型 1：添加 2：修改 3：删除(触发时方式的业务操作，业务可能是修改， 但是对应到数据变动类型上可能是1条删除 1条添加)
     */
    private Integer businessType;

    /**
     * 数据操作类型 1：添加 2：修改 3：删除
     * @see com.hailiang.enums.point.DataOperateTypeEnum
     */
    private Integer dataOperateType;

    /**
     * 日志父id
     */
    private Long logParentId;

    /**
     * 行为记录数据
     */
    private List<BehaviourRecord> records;

    /**
     * 旧行为记录
     */
    private BehaviourRecord oldBehaviourRecord;

    /**
     * 新的转换后的
     */
    private List<PointConvertDTO> pointConvertDTOs;

    /**
     * 初始分转积分消息
     */
    private PointInitialMsgConvertDTO pointInitialMsgConvertDTO;


    private Date createTime;
}
