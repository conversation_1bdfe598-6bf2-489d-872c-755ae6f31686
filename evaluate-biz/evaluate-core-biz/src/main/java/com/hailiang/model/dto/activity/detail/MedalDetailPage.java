package com.hailiang.model.dto.activity.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/21 13:55
 */
@Data
public class MedalDetailPage {

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 班级id
     */
    private String classId;

    /**
     * 奖章id
     */
    private Long medalInfoId;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 颁发类型  1：自动颁发  2：手动颁发
     */
    private Integer awardType;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endTime;

    private int pageNum = 1;

    private int pageSize = 20;

    /**
     * 学年
     */
    private String schoolYear;

    /**
     * 学期
     */
    private String termName;

    /**
     * 学段ID
     */
    private String campusSectionId;

    /**
     * 年级ID
     */
    private String gradeId;


}
