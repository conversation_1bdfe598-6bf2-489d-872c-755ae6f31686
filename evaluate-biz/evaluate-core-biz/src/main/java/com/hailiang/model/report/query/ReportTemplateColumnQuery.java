package com.hailiang.model.report.query;

import lombok.Data;

/**
 * Date：2023-11-08
 * Time：16:15
 * Description：模板栏目查询
 *
 * <AUTHOR>
 */
@Data
public class ReportTemplateColumnQuery {
    /**
     * 模板id
     */
    private Long id;

    /**
     * 模板id或模板快照id
     */
    private Long businessId;

    /**
     * 枚举 1:模板id;2:模板快照id
     */
    private Integer businessType;

    /**
     * 枚举值
     */
    private String columnCode;

    /**
     * 是否逻辑删除
     */
    private Boolean deleted = Boolean.FALSE;

    /**
     * 排序条件
     */
    private String sortCriteria;

    /**
     * 每页大小
     */
    private Integer limit;

    /**
     * 查询位置
     */
    private Integer offset;


    public ReportTemplateColumnQuery() {
    }

}
