package com.hailiang.model.dto.save;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class BehaviourRecordSaveDTO {


    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 学段id
     */
    private String campusSectionId;

    /**
     * 学段code
     */
    private String campusSectionCode;

    /**
     * 年级id
     */
    private String gradeId;

    /**
     * 年级code
     */
    private String gradeCode;

    /**
     * 班级id
     */
    private String classId;

    /**
     * 学生id
     */
    private String studentId;

    /**
     * 模块code：\n德育：1  \n智育：2\n体育：3\n美育：4\n劳育：5
     */
    private Integer moduleCode;

    /**
     * 指标id
     */
    private Long targetId;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标评价模版mongo表id
     */
    private String templateId;

    /**
     * 题目id
     */
    private String titleId;

    /**
     * 选项id
     */
    private String optionId;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 指标评价提交模版mongo表id
     */
    private String infoId;

    /**
     * 分数统计类型 1:指标名称 2:选项名称
     */
    private Integer infoType;

    /**
     * 指标或选项名称
     */
    private String infoName;

    /**
     * 加分类型，“2”为减分，“1”为加分
     */
    private Integer scoreType;
    /**
     * 是否开启加分
     */
    private Boolean isScore;

    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 分值 （分数的绝对值）
     */
    private BigDecimal scoreValue;

    /**
     * 数据来源 0：综合素质评价 1：星动力 2：成绩管理
     */
    private Integer dataSource;

    /**
     * 表单提交时间
     */
    private Date submitTime;

    /**
     * 评价人类型  1：老师  2：家长
     */
    private Integer appraisalType;

    /**
     * 评价人人id
     */
    private String appraisalId;
    /**
     * 紧急更新人
     */
    private String updateBy;

    /**
     * 评价人姓名
     */
    private String appraisalName;

    /**
     * 分值是否不加入画像综合得分和点评记录中 0否 1是
     */
    private Integer notPartCount;
}
