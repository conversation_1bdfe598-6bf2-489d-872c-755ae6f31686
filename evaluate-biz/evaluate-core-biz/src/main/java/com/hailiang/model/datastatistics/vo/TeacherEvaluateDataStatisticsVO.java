package com.hailiang.model.datastatistics.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 老师点评数据统计结果
 *
 * <AUTHOR>
 * @date 2024/06/25
 */
@Data
@Accessors(chain = true)
public class TeacherEvaluateDataStatisticsVO {

    /**
     * 点评总次数
     */
    private DataStatisticsEvaluateNumVO evaluateNum;

    /**
     * 老师参与率
     */
    private DataStatisticsTeacherParticipationRateVO teacherParticipationRate;

    /**
     * 表扬与待改进
     */
    private PraiseImproveVO praiseImprove;

    /**
     * 五育分布
     */
    private FiveEducationVO fiveEducation;

    /**
     * 指标覆盖率(只有校级数据,指标配置只有校级维度)
     */
    private TargetCoverageVO targetCoverage;

    /**
     * 指标覆盖情况(只有校级数据,指标配置只有校级维度)
     */
    private List<TargetFrequencyVO> targetFrequency;

    /**
     * 老师点评榜（返回点评榜前三条）
     */
    private List<TeacherVO> listTeacher;
}
