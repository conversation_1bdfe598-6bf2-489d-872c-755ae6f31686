package com.hailiang.model.report.request;

import com.hailiang.model.report.response.ClassAlbumRecordStudentResponse;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 班级相册-保存学生配置请求参数
 * @Author: huyouting
 * @Date: Created in 2024-12-18
 * @Version: v2.1.1
 */@Data
public class ClassAlbumStudentSaveRequest {

    /**
     * 报告单id
     */
    @NotNull(message = "报告单id不能为空")
    private Long reportDataId;

    /**
     * 学生id
     */
    @NotNull(message = "学生id不能为空")
    private Long studentId;

    /**
     * 最新配置的班级相册记录数据
     */
    private List<ClassAlbumRecordStudentResponse> classAlbumRecords;
}
