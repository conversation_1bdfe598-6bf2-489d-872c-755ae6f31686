package com.hailiang.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.InfoTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 指标评价任务统计表
 *
 * <AUTHOR> 2022-05-26 14:01:59
 */
@Data
@TableName("evaluate_behaviour_record")
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BehaviourRecord extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 学段id
     */
    private String campusSectionId;

    /**
     * 学段code
     */
    private String campusSectionCode;

    /**
     * 年级id
     */
    private String gradeId;

    /**
     * 年级code
     */
    private String gradeCode;

    /**
     * 班级id
     */
    private String classId;

    /**
     * 学生id
     */
    private String studentId;

    /**
     * 模块code：0：其他 \n德育：1  \n智育：2\n体育：3\n美育：4\n劳育：5
     */
    private Integer moduleCode;

    /**
     * 指标id
     */
    private Long targetId;
    /**
     * 指标名称（指标名称或者是师级分组名称，星未来指标就是指标名称）
     */
    private String targetName;

    /**
     * 指标评价模版mongo表id（图文点评情况下，MongoDB中 evaluate_target_template 主键 ， 对应 evaluate_target 表中 template_id，极速点评为null 或者是""）
     */
    private String templateId;

    /**
     * 题目id（星动力传过来的时候为null，图文点评情况下是控件ID，极速点评、扫脸情况下是如果是校级点评项则是控件ID，如果是师级点评项则是null）
     */
    private String titleId;

    /**
     * 选项id（图文点评情况下，点评项ID，极速点评情况下，点评项ID，师级点评项就是星动力那边的点评项主键ID， 但是图文点评情况下，文本，数字，日期，说明，分值这些控件不会有值，只会为空）
     * 当datasource=15时代表德育活动的作品id，datasource=16代表校外写实提交任务id
     */
    private String optionId;

    /**
     * 任务id（图文点评情况下，evaluate_task主键表，除图文点评情况下都是没有值）
     */
    private Long taskId;

    /**
     * 指标评价提交模版mongo表id
     * <p>
     * 图文点评提交情况下，MongoDB 中 evaluate_info的 主键， 旧星动力点评为空，体侧也为空 极速点评情况下， 星动力过来的数据是同一批次的雪花算法ID，
     * 星未来下的极速点评，还有扫脸点评是点击一次提交的雪花算法ID，
     */
    private String infoId;

    /**
     * 分数统计类型
     *
     * @see InfoTypeEnum
     */
    private Integer infoType;

    /**
     * 指标或选项名称（有选项就是选项名称，没有选项就用指标名称）
     */
    private String infoName;

    /**
     * 加分类型，“1”为加分，“2”为减分
     */
    private Integer scoreType;
    /**
     * 是否开启加分
     */
    private Boolean isScore;

    /**
     * 分数（可以有正负数，实际得分）
     */
    private BigDecimal score;

    /**
     * 分值 （分数的绝对值）
     */
    private BigDecimal scoreValue;

    /**
     * 推送状态  1：已推送    2：未推送    3：不推送
     */
    private Integer sendType;

    /**
     * 数据来源 0：老图文+老极速 1：星动力 2： 3:体测 4：星未来融合后新极速点评 5：星未来融合后新图文点评 6：星动力极速点评
     *
     * @see DataSourceEnum
     */
    private Integer dataSource;

    /**
     * 表单提交时间
     */
    private Date submitTime;

    /**
     * 评价人类型  1：老师  2：家长
     */
    private Integer appraisalType;

    /**
     * 评价人人id
     */
    private String appraisalId;

    /**
     * 评价人姓名
     */
    private String appraisalName;

    /**
     * 学科CODE
     */
    private String subjectCode;

    /**
     * 分值是否不加入画像综合得分和点评记录中 0否 1是
     */
    private Integer notPartCount;

    /**
     * 积分板id
     * <p>
     * 业务字段示例：不需要持久化到数据库
     */
    @TableField(exist = false)
    private Long planId;

    /**
     * 第三方指标类型（1：校级指标--极速点评分类、2：师级指标--星动力点评分类）
     *
     * 业务字段示例：不需要持久化到数据库
     */
    @TableField(exist = false)
    private Integer thirdTargetType;

    /**
     * 扣分延续,记录老的行为记录id
     *
     * 业务字段示例：不需要持久化到数据库
     */
    @TableField(exist = false)
    private Long oldBehaviourId;


    public static final String CLASS_ID = "br.class_id";

    public static final String SCHOOL_ID = "br.school_id";

    public static final String CAMPUS_ID = "br.campus_id";

    public static final String CAMPUS_SECTION_ID = "br.campus_section_id";

    public static final String DELETED = "br.deleted";

    public static final String GRADE_ID = "br.grade_id";

    public static final String SUBMIT_TIME = "br.submit_time";

    public static final String SUBJECT_CODE = "br.subject_code";

    public static final String OPTION_ID = "br.option_id";

    public static final String TARGET_ID = "br.target_id";

    public static final String APPRAISAL_NAME = "br.appraisal_name";

}
