package com.hailiang.model.report.vo.report;

import com.hailiang.model.report.dto.ReportDataColumnDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> hljy
 * @date 2023/8/31 09:48
 */
@Data
public class ReportDataColumnVO {

    /**
     * 班级评价情况（是否有数据）
     */
    private List<ReportDataColumnClassVO> classData;
    /**
     * 学生数据-学生信息
     */
    private List<ReportDataColumnStudentInfoVO> studentInfoData;

    /**
     * 学生数据-教师评语
     */
    private List<ReportDataColumnEvaluateVO> evaluateData;
    /**
     * 学生数据-争章成长
     */
    private List<ReportDataColumnMedalVO> medalData;
    /**
     * 学生数据-争章成长
     */
    private List<ReportDataColumnBehaviourVO> behaviourData;
    /**
     * 学生数据-学业表现
     */
    private List<ReportDataColumnRankVO> rankData;
    /**
     * 学生数据-体质健康
     */
    private List<ReportDataColumnSportVO> sportData;
    /**
     * 学生数据-学校信息
     */
    private ReportDataColumnSchoolVO schoolData;
    /**
     * 学生数据-指标
     */
    private List<ReportDataColumnTargetVO> targetData;
    /**
     * 查询参数
     */
    private ReportDataColumnDTO queryParam;

    /**
     * 班级相册
     */
    private List<ReportDataColumnClassAlbumResponse> classAlbumData;

    /**
     * 学生数据-德育活动
     */
    private List<ReportMoralActivityVO> reportMoralActivityVOS;

    /**
     * 学生数据-视频栏目
     */
    private List<ReportVideoVO> reportVideoVOS;
}
