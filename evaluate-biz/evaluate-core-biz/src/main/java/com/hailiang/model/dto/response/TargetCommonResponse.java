package com.hailiang.model.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 指标公共返回
 *
 * @Description: 指标公共返回
 * @Author: Jovi
 * @Date: Created in 2024-12-02
 * @Version: 2.0.0
 */
@Data
@AllArgsConstructor
@Builder
public class TargetCommonResponse {

    /**
     * 指标ID
     */
    private Long targetId;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 指标顺序，指标排序按此字段升序，从1开始
     */
    private Integer sortIndex;
}
