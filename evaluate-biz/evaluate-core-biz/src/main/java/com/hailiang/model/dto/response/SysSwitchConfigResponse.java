package com.hailiang.model.dto.response;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 学校开关配置
 *
 * @Description: 学校开关配置
 * @Author: JiangJun<PERSON>i
 * @Date: Created in 2024-05-16
 * @Version: 1.7.4
 */
@Data
public class SysSwitchConfigResponse {
    /**
     * saas租户id
     */
    private String tenantId;

    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id,如果是学校层面校区 id 为空
     */
    private String campusId;

    /**
     * 开关状态， 0：关闭, 1：开启
     */
    private Integer status;

    /**
     * 业务描述，1:运营后台人脸识别管理
     */
    private String key;
    /**
     * 业务值 1:运营后台人脸识别管理
     */
    private Integer value;

}
