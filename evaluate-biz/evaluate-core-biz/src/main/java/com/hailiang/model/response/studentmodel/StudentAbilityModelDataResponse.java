package com.hailiang.model.response.studentmodel;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class StudentAbilityModelDataResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 能力Id
     */
    private Long abilityId;
    /**
     * 能力类型 1：五育评价 2：体测成绩 3：学科成绩
     */
    private Integer abilityType;
    /**
     * 能力名称
     */
    private String abilityName;
    /**
     * 学生得分
     */
    private BigDecimal studentScore;
    /**
     * 平均分
     */
    private BigDecimal avgScore;
    /**
     * 最高分
     */
    private BigDecimal maxScore;
    /**
     * 排序字段
     */
    private Integer sortIndex;
}
