package com.hailiang.model.dto.save;

import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 班级用户表 查询对象
 *
 * <AUTHOR> 2022-03-21 10:25:52
 */
@Data
public class InfoSaveDTO {
    /**
     * 学期
     */
    private String schoolYear;
    /**
     * 学年
     */
    private String termName;
    /**
     * 是否是当前学年
     */
    private Boolean isCurrentYear;

    /**
     * 指标表id
     */
    private Long targetId;

    /**
     * 指标评价任务表id
     */
    private Long taskId;

    /**
     * 评价任务名称
     */
    private String taskName;

    /**
     * 提交人id
     */
    private String submitStaffId;

    /**
     * 操作批量提交标识（0：否，1：全班，2：全年级，3：全学段）
     */
    @NotNull(message = "操作批量提交标识不能为空，请刷新后重新操作")
    private Integer isBatchFlag;

    private Date submitTime;

    /**
     * 积分板id
     */
    private String planId;

    /**
     * 班级id
     */
    private String classId;

    /**
     * 方案类型  枚举：1|常规分组 2|不分组 3|师徒帮扶分组
     */
    private Integer planType;

    /**
     * 班级关联学科
     */
    private List<SubjectRelDTO> subjectRelList;

    /**
     * 答案集合
     */
    private List<SubmitInfoSaveDTO> submitInfoList;


}