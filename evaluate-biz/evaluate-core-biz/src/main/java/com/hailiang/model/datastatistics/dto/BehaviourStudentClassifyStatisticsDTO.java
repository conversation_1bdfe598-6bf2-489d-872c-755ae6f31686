package com.hailiang.model.datastatistics.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 学生指标统计数据传输实体
 * @author: pan<PERSON>an
 * @create: 2024/8/30 16:56
 * @Version 1.0
 */
@Data
public class BehaviourStudentClassifyStatisticsDTO implements Serializable {

    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 学校id
     */
    private String schoolId;
    /**
     * 校区id
     */
    private String campusId;
    /**
     * 学段id
     */
    private String campusSectionId;
    /**
     * 学段code
     */
    private String campusSectionCode;
    /**
     * 年级id
     */
    private String gradeId;
    /**
     * 年级code
     */
    private String gradeCode;
    /**
     * 班级id
     */
    private String classId;
    /**
     * 学生id
     */
    private String studentId;
    /**
     * 分类id
     */
    private Long classifyId;
    /**
     * 分类名称
     */
    private String classifyName;
    /**
     * 统计记录数
     */
    private Integer recordCount;
    /**
     * 加分类型 1：加分 2：减分
     */
    private Integer scoreType;
    /**
     * 分数（可以有正负数，实际得分）
     */
    private BigDecimal score;
    /**
     * 统计时间，根据statistics_time决定
     */
    private Date statisticsTime;

}