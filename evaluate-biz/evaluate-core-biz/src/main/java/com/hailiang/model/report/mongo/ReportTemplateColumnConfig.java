package com.hailiang.model.report.mongo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.hailiang.IdEntity;
import com.hailiang.enums.report.ReportStudyPerformanceDimTypeEnum;
import com.hailiang.enums.report.ReportStudyPerformanceShowCodeEnum;
import com.hailiang.enums.report.ReportStudyPerformanceShowTypeEnum;
import com.hailiang.model.hai.HaiClassifyTagDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Objects;

@Data
@ToString(callSuper = true)
@Document("report_template_column_config")
@EqualsAndHashCode(callSuper = true)
public class ReportTemplateColumnConfig extends IdEntity {
    /**
     * 封面
     */
    private Cover cover;
    /**
     * 学校寄语
     */
    private SchoolMessage schoolMessage;
    /**
     * 学生信息
     */
    private StudentInfo studentInfo;
    /**
     * 行为表现
     */
    private Behavior behavior;
    /**
     * 学业表现
     */
    private StudyPerformance studyPerformance;
    /**
     * 体质健康
     */
    private PhysicalHealth physicalHealth;
    /**
     * 争章活动
     */
    private ChapterActivity chapterActivity;
    /**
     * 教师评语
     */
    private TeacherComment teacherComment;
    /**
     * 封底
     */
    private BackCover backCover;
    /**
     * 家长反馈
     */
    private ParentFeedback parentFeedback;
    /**
     * 家长评价
     */
    private ParentComment parentComment;
    /**
     * 班级相册
     */
    private ClassAlbum classAlbum;
    /**
     * 自定义栏目
     */
    private Custom custom;
    /**
     * 综合实践活动
     */
    private MoralActivityDTO moralActivityDTO;

    /**
     * 视频栏目
     */
    private VideoDTO videoDTO;

    @Data
    public static class Cover {

        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;

        /**
         * 背景图片URL
         */
        private String backgroundUrl;

        /**
         * 是否展示校徽，0否1是
         */
        private Integer isShowSchoolBadge;

        /**
         * 学校名字
         */
        private String  schoolName;

        /**
         * 校徽URL
         */
        private String schoolBadgeUrl;

        /**
         * 校徽布局 :left页面左上,right页面右上
         */
        private String schoolBadgeLocation;

        /**
         * x轴坐标
         */
        private String  xPosition;

        /**
         * y轴坐标
         */
        private String  yPosition;
        /**
         * x轴坐标
         */
        private Integer xTitlePosition;

        /**
         * y轴坐标
         */
        private Integer yTitlePosition;
        /**
         * 自定义学期名称
         */
        private String termName;

    }

    @Data
    public static class SchoolMessage {
        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;

        /**
         * 内容
         */
        private String content;

        /**
         * 背景图片URL
         */
        private String backgroundUrl;

    }

    @Data
    public static class StudentInfo {
        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;
        /**
         * 说明
         */
        private String explain;
    }

    @Data
    public static class Behavior {

        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;

        /**
         * 展示内容code 如过程性评价，结果性评价等
         */
        private String showCodes;
        /**
         * 说明
         */
        private String explain;
    }

    @Data
    public static class StudyPerformance {
        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;

        /**
         * 展示内容code
         *
         * @see ReportStudyPerformanceShowCodeEnum
         */
        private String showCodes;

        /**
         * 是否显示分数，1是0否
         */
        private Integer isShowScore;

        /**
         * 样式显示类型 1:表格样式，2:图文模式
         *
         * @see ReportStudyPerformanceShowTypeEnum
         */
        private Integer showType = ReportStudyPerformanceShowTypeEnum.TABLE.getType();
        /**
         * 学科分纬度显示 1:学生能力模型纬度 2:学科纬度
         * showType为2时生效
         *
         * @see ReportStudyPerformanceDimTypeEnum
         */
        private Integer dimType;

        /**
         * 是否展示学科评语 0:不展示，1:展示
         */
        private Integer showSubjectComment;

        /**
         * 科目分类列表
         */
        private List<SubjectCategory> subjectCategoryList;
        /**
         * 说明
         */
        private String explain;
    }

    @Data
    public static class PhysicalHealth {

        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;


        /**
         * 是否显示分数，1是0否
         */
        private Integer isShowScore;
        /**
         * 说明
         */
        private String explain;
        /**
         * 是否显示学年总分，true是false否，默认 true
         */
        private Boolean isShowYearScore = true;
        /**
         * 是否显示等级评定，true是false否，默认 true
         */
        private Boolean isShowLevelScore = true;
        /**
         * 是否显示标准分数，true是false否，默认 true
         */
        private Boolean isShowStandardScore = true;
    }

    @Data
    public static class ChapterActivity {
        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;
        /**
         * 说明
         */
        private String explain;
    }

    @Data
    public static class TeacherComment {
        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;
        /**
         * 说明
         */
        private String explain;
    }

    @Data
    public static class BackCover {
        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;

        /**
         * 内容
         */
        private String content;

        /**
         * 背景图片URL
         */
        private String backgroundUrl;
    }

    @Data
    public static class ParentFeedback {
        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;

        /**
         * 内容
         */
        private String content;
        /**
         * 说明
         */
        private String explain;

    }

    @Data
    public static class ParentComment {
        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;

        /**
         * 内容
         */
        private String content;
        /**
         * 说明
         */
        private String explain;
    }

    @Data
    public static class Custom {
        /**
         * 标题
         */
        private String title;

        /**
         * 字体颜色
         */
        private String fontColor;

        /**
         * 内容类型，1：指标，其他：富文本
         */
        private Integer contentType;

        /**
         * 内容
         */
        private String content;

        /**
         * 背景图片URL
         */
        private String backgroundUrl;
        /**
         * 说明
         */
        private String explain;
    }

    @Data
    public static class SubjectCategory {
        /**
         * 是否显示分数，1是0否
         */
        private Integer isShowScore;
        /**
         * 是否显示等级，1是0否
         */
        private Integer isShowLevel;
        /**
         * 科目类别名称
         */
        private String subjectCategoryName;

        private List<Subject> subjectList;
    }

    @Data
    public static class Subject {
        /**
         * 科目ID
         */
        private String subjectId;

        /**
         * 科目code
         */
        private String subjectCode;

        /**
         * 科目名称
         */
        private String subjectName;

        /**
         * 科目类型 1课程 2科目
         */
        private Integer subjectType;
    }

    @Data
    public static class ClassAlbum {

        /**
         * 栏目名称
         */
        private String title;

        /**
         * 分类标签详情集合
         */
        List<Long> tagIds;

        /**
         * 最大记录限制
         */
        private Integer recordMaxLimit;

        /**
         * 相册最大记录限制
         */
        private Integer albumMaxLimit;

        /**
         * 是否拉取班级相册 1:是 0:否
         * 学生相关照片为0时，按时间拉取班级记录：勾选，则当生成报告单拉取照片时，与学生相关照片（人脸识别或发布班级相册是指定学生）数目为0时，则按照数量限制时间倒序拉取班级照片；
         */
        private Integer pullClassAlbumFlag;

        public void valid() {
            if (StrUtil.isBlank(title)){
                throw new IllegalArgumentException("【班级相册】标题参数异常，请校验");
            }
            if (CollUtil.isEmpty(tagIds)){
                throw new IllegalArgumentException("【班级相册】标签参数异常，请校验");
            }
            if (Objects.isNull(pullClassAlbumFlag)){
                throw new IllegalArgumentException("【班级相册】是否拉取参数异常，请校验");
            }
            if (StringUtils.isBlank(title) || title.length() > 25) {
                throw new IllegalArgumentException("【自定义栏目】标题长度异常，请校验");
            }
            if (recordMaxLimit > 15) {
                throw new IllegalArgumentException("【班级相册】参数异常，最大记录数配置异常，请校验");
            }
            if (albumMaxLimit > 15) {
                throw new IllegalArgumentException("【班级相册】参数异常，最大相册数配置异常，请校验");
            }
        }
    }


    @Data
    public static class MoralActivityDTO{
        /**
         * 标题
         */
        private String title;

        /**
         * 显示活动列表
         */
        private List<String> activityIds;

        /**
         * 不展示不合格 0 否 1 是
         */
        private Integer notShowFailed;

        /**
         * 只展示学生相关记录
         * 0 否 1 是
         */
        private Integer showRelated;

        /**
         * 活动记录最多取limitPicNum条图片
         */
        private Integer limitPicNum;

        public void valid() {
            if (StringUtils.isBlank(title)
                    || Objects.isNull(notShowFailed)
                    || Objects.isNull(showRelated)
//                    || CollUtil.isEmpty(activityIds)
                    || Objects.isNull(limitPicNum)) {
                throw new IllegalArgumentException("【综合实践活动】参数异常，请校验");
            }
        }
    }

    @Data
    public static class VideoDTO{

        /**
         * 栏目标题
         */
        private String title;

        /**
         * 视频对象
         */
        private List<InnerVideoDTO> innerVideoDTOS;

        public void valid(){
            if(StringUtils.isBlank(title)
                    || innerVideoDTOS.stream().anyMatch(item -> !item.valid())){
                throw new IllegalArgumentException("【视频栏目】参数异常，请校验");
            }
        }

    }

    @Data
    public static class InnerVideoDTO{

        /**
         * 视频排序
         */
        private Integer sort;

        /**
         * 视频描述
         */
        private String videoDesc;

        /**
         * 视频相对地址url
         */
        private String videoUrl;

        /**
         * 视频封面图片相对地址url
         */
        private String imgUrl;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * 文件大小
         */
        private Long fileSize;

        /**
         * 文件格式
         */
        private String format;

        /**
         * 图片绝对地址
         */
        private String absoluteImgUrl;

        /**
         * 视频绝对地址
         */
        private String absoluteVideoUrl;

        public boolean valid(){
            if (StringUtils.isBlank(videoUrl)
                    || StringUtils.isBlank(imgUrl)
                    || Objects.isNull(sort)) {
                return false;
            }
            return true;
        }
    }
}
