package com.hailiang.model.response.speed;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 学生分数
 *
 * @Description: 学生分数
 * @Author: gaoxin
 * @Date: Created in 2024-02-06
 * @Version: 1.6.0
 */
@Data
public class StudentScoreResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 学生ID
     */
    private String studentId;

    /**
     * 学生分数
     */
    private BigDecimal score;

}
