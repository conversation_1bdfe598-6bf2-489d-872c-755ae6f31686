package com.hailiang.model.report.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 报告单id请求参数
 * @Author: huyouting
 * @Date: Created in 2024-12-21
 * @Version: v2.1.1
 */
@Data
public class ReportFilePackageRequest {

    /**
     * 报告单id
     */
    private Long reportDataId;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 文件类型 1:一页纸 2：全部
     */
    private Integer fileType;
    /**
     * 用户选择的要下载的班级id
     */
    private List<String> classIds;

    // 增加校验参数的方法
    public void valid() {
        if (reportDataId == null || reportDataId <= 0) {
            throw new IllegalArgumentException("报告单id不能为空");
        }
        if (fileType == null || fileType <= 0) {
            throw new IllegalArgumentException("文件类型不能为空");
        }
        if (classIds == null || classIds.isEmpty()) {
            throw new IllegalArgumentException("班级id不能为空");
        }
    }
}
