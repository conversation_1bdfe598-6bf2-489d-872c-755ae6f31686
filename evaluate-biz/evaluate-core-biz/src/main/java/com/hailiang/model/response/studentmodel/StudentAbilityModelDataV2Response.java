package com.hailiang.model.response.studentmodel;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class StudentAbilityModelDataV2Response implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 能力Id
     */
    private Long id;
    /**
     * 能力名称（命名尽量简化，方便兼容历史归档数据和后续变更）
     */
    private String name;
    /**
     * 学生得分
     */
    private BigDecimal studentScore = BigDecimal.ZERO;
    /**
     * 平均分
     */
    private BigDecimal avgScore = BigDecimal.ZERO;
    /**
     * 最高分
     */
    private BigDecimal maxScore = BigDecimal.ZERO;
    /**
     * 排序字段
     */
    private Integer sortIndex;

    List<StudentAbilityModelDataItemV2Response> studentAbilityModelDataItemV2Responses;
}
