package com.hailiang.model.report.request;

import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * h5生成评语入参
 *
 * @Description: h5生成评语入参
 * @Author: JiangJL
 * @Date: Created in 2025/5/20
 * @Version: 3.0.0
 */
@Data
public class ReportGenerateCommentRequest {

    /**
     * 报告单id
     */
    @NotNull(message = "报告单id不能为空")
    private Long reportDataId;

    /**
     * 消息ID
     */
    private String msgId;

    /**
     * 班级ID
     */
    @NotBlank(message = "班级ID不能为空")
    private String classId;

    /**
     * 评价人类型 1 班主任 2学科老师
     */
    @NotNull(message = "评价人类型不能为空")
    private Integer evaluatorType;

    /**
     * 评价身份
     */
    @NotBlank(message = "评价身份不能为空")
    private String roleName;

    /**
     * 学科ID
     */
    private String subjectId;
    /**
     * 学科code
     */
    private String subjectCode;
    /**
     * 1：课程，2：科目
     */
    private Integer subjectType;

    /**
     * 学生ID
     */
    private String studentId;

    /**
     * 学生标签
     */
    private List<String> studentLabels;

    /**
     * 生成类型 1：AI 自动生成，2：手动填写 {@link com.hailiang.enums.report.GenerationTypeEnum}
     */
    private Integer generationType;
}
