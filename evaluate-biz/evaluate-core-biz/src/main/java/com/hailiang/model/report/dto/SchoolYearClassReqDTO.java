package com.hailiang.model.report.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/7 10:20
 */
@Data
public class SchoolYearClassReqDTO {

    /**
     * 学年(必传)
     */
    private String schoolYear;
    /**
     * 学期
     */
    private String termName;
    /**
     * 学段code(必传)
     */
    private String sectionCode;

    /**
     * 年级code
     */
    private String gradeCode;

    /**
     * 班级类型：0行政班、1选考班、2学考班、3选修班、4兴趣班  不传或者传的是空集合默认查所有
     */
    private List<Integer> classTypes;

    /**
     * 毕业状态（0未毕业1已毕业），不传查所有
     */
    private Integer graduationStatus;

}
