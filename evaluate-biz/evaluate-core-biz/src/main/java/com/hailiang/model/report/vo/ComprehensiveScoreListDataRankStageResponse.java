package com.hailiang.model.report.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 报告单指标/成绩等级排行榜
 */
@Data
public class ComprehensiveScoreListDataRankStageResponse {

    /**
     * 分类id
     */
    private String categoryId;

    /**
     * 1:过程性评价  2:结果性评价  3:阶段性评价
     */
    private String categoryType;

    /**
     * 分类名称（例如:过程性评价-第一阶段,过程性评价-第二阶段，结果性评价）
     */
    private String categoryName;

    /**
     * 维度名称（指标/考试类型）
     */
    private String dimName;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 总占比权重
     */
    private String categoryWeight;

    private String totalWeight;

    /**
     * 分数
     */
    private BigDecimal subjectScore;

    /**
     * 分数
     */
    private String subjectScoreStr;

    /**
     * 等级
     */
    private String subjectLevel;

    /**
     * 排序索引
     */
    private String sortIndex;

    /**
     * 是否缺考 true:缺考  false:正常
     */
    private Boolean missExamFlag ;

    /**
     * 等级名称
     */
    private String levelName;

    @Override
    public String toString() {
        return JSON.toJSONString(this, SerializerFeature.WriteMapNullValue);
    }

}
