package com.hailiang.model.entity.mongo;

import com.hailiang.IdEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
@Document("evaluate_info")
@Accessors(chain = true)
public class Info extends IdEntity {

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 指标表id
     */
    private Long targetId;

    /**
     * 指标评价任务表id
     */
    private Long taskId;

    /**
     * 评价任务名称
     */
    private String taskName;

    /**
     * 提交人id
     */
    private String submitStaffId;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 被评价学生集合
     */
    private List<StudentInfo> studentInfos;

    /**
     * 答案集合
     */
    private List<SubmitInfo> submitInfoList;


    private String msg;



}
