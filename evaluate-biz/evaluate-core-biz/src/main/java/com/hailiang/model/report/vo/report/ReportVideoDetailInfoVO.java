package com.hailiang.model.report.vo.report;

import lombok.Data;

/**
 * 报告单视频栏目明细数据包含视频url
 *
 * @Description: 报告单视频栏目明细数据包含视频url
 * @Author: Tan<PERSON>ian
 * @Date: Created in 2025-01-06
 * @Version: 1.0.0
 */
@Data
public class ReportVideoDetailInfoVO {

    /**
     * 视频排序
     */
    private Integer sort;

    /**
     * 视频描述
     */
    private String videoDesc;

    /**
     * 视频绝对地址url
     */
    private String absoluteVideoUrl;

    /**
     * 图片绝对地址url
     */
    private String absoluteImgUrl;

    /**
     * 视频封面图片相对地址url
     */
    private String imgUrl;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件格式
     */
    private String format;

    /**
     * 文件大小
     */
    private String fileSize;

    /**
     * 视频相对路径url
     */
    private String videoUrl;
}
