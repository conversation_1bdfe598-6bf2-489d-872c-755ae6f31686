package com.hailiang.model.dto.studentmodel.v2.config;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 课程信息
 *
 * <AUTHOR>
 * @date 2024/11/25
 */
@Data
public class SubjectInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 课程id
     */
    private String subjectId;
    /**
     * 课程code
     */
    private String subjectCode;
    /**
     * 课程名称
     */
    private String subjectName;
    /**
     * 课程权重
     */
    private BigDecimal weight = BigDecimal.ZERO;
}
