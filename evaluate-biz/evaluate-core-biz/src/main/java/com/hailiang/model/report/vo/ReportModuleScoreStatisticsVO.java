package com.hailiang.model.report.vo;

import com.hailiang.model.report.Pair;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> hljy
 * @date 2023/10/23 19:41
 */
@Data
public class ReportModuleScoreStatisticsVO {
    /**
     * 学生Id
     */
    private String studentId;
    /**
     * 模块code：1德育 2智育 3体育 4美育 5劳育
     */
    private Integer moduleCode;

    /**
     * 最高加分项 点评名称、分数
     */
    private List<Pair<String, BigDecimal>> plusScore;

    /**
     * 最高减分项 点评名称、分数
     */
    private List<Pair<String, BigDecimal>> minusScore;
}
