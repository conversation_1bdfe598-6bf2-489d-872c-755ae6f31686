package com.hailiang.model.dto.save;

import cn.hutool.core.util.StrUtil;
import com.hailiang.enums.TargetReviewTypeEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 */
@Data
public class TargetSaveDTO implements Serializable {
    /**
     * 指标id，添加时为空
     */
    private Long id;
    /**
     * 图标url
     */
    @NotBlank(message = "图标url不能为空")
    private String iconUrl;

    /**
     * 指标名称
     */
    @NotBlank(message = "指标名称不能为空")
    private String targetName;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 分组id
     */
    @NotNull(message = "分组id不能为空")
    private Long groupId;

    /**
     * 1：工作日每天 2：每周 3：每月 4：每年 5：不限定频次　６:一学期一次
     *　需要发送消息提醒的类型：１、２、３、４
     *  家长提交次数限制的类型：１、２、３、４、６
     *  老师提交次数限制的类型：6
     */
    @NotNull(message = "提交频次类型不能为空")
    private Integer submitType;

    /**
     * 工作日每天：此项为空
     每周：（1,2,3 数字表示周几）
     每月：（5,15,20，30 数字表示几号） 注意小于10号给一位数字
     每年：（0915， 0627 数字前两位表示月，后两位表示日）

     */
    private String submitDate;

    /**
     * 提醒时间：0941 表示9点47分
     */

    private String remindTime;

    /**
     * 法定节假日、寒暑假通知flag 1：发送 0：不发送
     */

    private Integer holidayNoticeFlag;

    /**
     * 是否每次必须提交 0：否 1： 是
     */

    private Integer mustSubmitFlag;

    /**
     * 超过多少小时系统催交 单位：小时
     */
    private Integer urgeTime = 24;

    /**
     * 是否推送给家长 0：否 1：是
     */
    @NotNull(message = "是否推送给家长flag不能为空")
    private Integer sendParentFlag;

    /**
     * 教职工是否全选：0：否  1：是
     */
//    @NotNull(message = "教职工是否全选flag不能为空")
    private Integer staffFullCheckFlag;

    /**
     * 是否开启分值不参与兑换 0否 1是
     */
    private Integer notPartExchange;

    /**
     * 老师提交后是否通知班主任 0否 1是
     */
//    @NotNull(message = "是否推送给家长flag不能为空")
    private Integer isNotifyTeacher = 0;

    /**
     * 是否开启分值不加入画像综合得分和点评记录中 0否 1是
     */
    private Integer targetNotPartCount;
    /**
     * 限定家长提交次数开关，1：开启，0：关闭，默认关闭
     */
    private Integer parentSubmitLimitFlag;
    /**
     * 限定家长提交次数，默认1次，数值范围1-9999
     */
    @Range(min = 1, max = 9999, message = "限定家长提交次数必须在1到9999之间")
    private Integer parentSubmitLimitTimes;
    /**
     * 家长提交需审核开关，1：开启，0：关闭，默认开启
     */
    private Integer parentSubmitReviewFlag;

    /**
     * 学生提交需审核开关，1：开启，0：关闭，默认开启
     */
    private Integer studentSubmitReviewFlag;

    /**
     * 违纪学生的扣分需要延续到下一个学年 0否 1是
     */
    private Integer scoreContinuationFlag = 0;

    /**
     * 审核人类型，1:班级内的学科老师， 2：指定老师， 3：班主任
     * @see TargetReviewTypeEnum
     */
    private Integer reviewType;

    /**
     * 审核人是班主任或者课程老师
     */
    private InnerTargetReviewTeacher classTeacher;
    /**
     * 审核人是指定老师
     */
    private List<InnerTargetReviewTeacher> reviewTeachers;

    /**
     * 学生审核人类型，1:班级内的学科老师， 2：指定老师， 3：班主任
     * @see TargetReviewTypeEnum
     */
    private Integer studentReviewType;

    /**
     * 审核人是班主任或者课程老师
     */
    private InnerTargetReviewTeacher studentClassTeacher;
    /**
     * 审核人是指定老师
     */
    private List<InnerTargetReviewTeacher> studentReviewTeachers;

    /**
     * 模板信息列表
     */
    @NotEmpty(message = "模板信息列表不能为空")
    @Valid
    private List<TemplateInfoSaveDTO> templateInfoList;

    /**
     * 加分控件
     * @param id
     */
    private InnerScoreControl scoreControl;

    public void setId(String id) {
        Long targetId = StrUtil.isBlank(id) ? null : Long.parseLong(id);
        this.id = targetId;
    }

    /**
     * 指标填写人集合
     */
    private List<InnerEvaluateTargetUser> targetUserList;


    @Data
    public static class InnerEvaluateTargetUser implements Serializable{
        /**
         * 填写人名称
         */
        @NotNull(message = "填写人不能为空")
        private String submitUserName;

        /**
         * 指标填写人头像url
         */
        private String iconUrl;

        /**
         * 指标填写人（选中组织时）该组织下多少人被选中
         */
        private Integer userCount;

        /**
         * 填报类型编码： 用于唯一确定各层级id、家长id或教师id，数字枚举
         *  1 组织架构id  2.角色id 3:教职工staffId 4:家长id 10:课程id
         */
        @NotNull(message = "填报类型不能为空")
        private Integer submitType;

        /**
         * 填报对象对应的值
         */
        @NotNull(message = "填报对象对应的值不能为空")
        private Long submitValue;

        /**
         * 学生任职传递json字符串对象
         */
        private InnerSubmitObj submitObj;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InnerSubmitObj {
        private Integer courseRepresentativeFlag;

        private Long id;

        private String code;

        private Long postId;

        private Long sectionId;

        private Long gradeId;

        private Integer levelType;

        private Long subjectId;

        private Integer subjectType;

    }

    /**
     * 加分控件
     */
    @Data
    public class InnerScoreControl implements Serializable{
        /**
         * 加分控件名称
         */
        private String scoreControlName;

        /**
         * 加分控件类型 （1:加分 2:减分）
         */
        private Integer scoreControlType;

        /**
         * 分数
         */
        private BigDecimal score;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InnerTargetReviewTeacher implements Serializable{

        /**
         * saas课程id或者老师staffId，-1表示班主任
         */
        private Long businessId;

        /**
         * saas课程名称或者老师姓名
         */
        private String businessName;
        /**
         * 前端用
         */
        private Integer submitType;
    }
}
