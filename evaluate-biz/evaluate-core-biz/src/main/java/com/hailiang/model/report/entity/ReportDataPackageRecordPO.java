package com.hailiang.model.report.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Table: report_data_package_record
 */
@Data
@TableName("report_data_package_record")
@EqualsAndHashCode(callSuper = true)
public class ReportDataPackageRecordPO extends BaseEntity {

    /**
     * Column: business_type
     * Type: VARCHAR(32)
     * Default value: ''
     * Remark: 业务类型，student_report:学生报告   comprehensive_score:综合成绩单
     * {@link com.hailiang.enums.report.PackageBusinessTypeEnum}
     */
    private String businessType;

    /**
     * Column: business_id
     * Type: INT
     * Default value: 0
     * Remark: 业务唯一标识 报告单ID
     */
    private Long businessId;

    /**
     * Column: task_id
     * Type: BIGINT
     * Default value: 0
     * Remark: 打包任务id
     */
    private Long taskId;

    /**
     * Column: zip_url
     * Type: VARCHAR(512)
     * Remark: 压缩包地址
     */
    private String zipUrl;

    /**
     * Column: response_code
     * Type: INT
     * Default value: 0
     * Remark: 下载中心返回结果编码
     */
    private Integer responseCode;

    /**
     * 打包状态：0:打包中 1:打包成功 2:打包失败
     * {@link com.hailiang.enums.report.PackageStatusEnum}
     */
    private Integer packageStatus;
}