package com.hailiang.model.dto.request.evaluate.imports;

import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 根据指标ID查询表格列头入参
 *
 * @Description: 根据指标ID查询表格列头入参
 * @Author: Jovi
 * @Date: Created in 2024/12/5
 * @Version: 2.0.0
 */
@Data
public class ImportsEvaluateQueryTableColumnRequest {


    /**
     * 指标ID
     */
    @NotNull(message = "指标ID不能为空")
    private Long targetId;

}
