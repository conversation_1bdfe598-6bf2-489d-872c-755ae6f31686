package com.hailiang.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR> gaoxin
 * @create 2023/8/3 18:00
 */
@Data
public class CheckAwardRecordDTO {

    /**
     * 学年
     */
    @NotBlank(message = "学年不能为空")
    private String schoolYear;

    /**
     * 学段ID
     */
    @NotBlank(message = "学段ID不能为空")
    private String campusSectionId;

    /**
     * 学段CODE
     */
    @NotBlank(message = "学段CODE不能为空")
    private String campusSectionCode;

    /**
     * 颁发类型 (1 按班级、2 按层级、3 按年级)
     */
    @NotNull(message = "颁发类型不能为空")
    private Integer awardType;

    /**
     * 考核周期起始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "考核起始时间不能为空")
    private Date startTime;

    /**
     * 考核周期结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "考核结束时间不能为空")
    private Date endTime;

    /**
     * 是否通知班主任
     */
    private Boolean isNotifyTeacher = Boolean.FALSE;

    /**
     * 是否通知班排
     */
    private Boolean isNotifyClass = Boolean.FALSE;

    /**
     * 初始分数
     */
    @NotNull(message = "初始分数不能为空")
    @Min(value = 0, message = "初始分数必须大于等于0")
    @Max(value = 999, message = "初始分数必须小于等于999")
    private Integer initialScore;

    //=================== 业务填充 ===================

    /**
     * 租户ID (系统填充)
     */
    private String tenantId;

    /**
     * 学校ID (系统填充)
     */
    private String schoolId;

    /**
     * 校区ID (系统填充)
     */
    private String campusId;
}
