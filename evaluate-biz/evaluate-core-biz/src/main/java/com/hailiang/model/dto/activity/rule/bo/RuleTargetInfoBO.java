package com.hailiang.model.dto.activity.rule.bo;

import com.hailiang.model.dto.activity.rule.query.RuleTargetInfoVO;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: pan<PERSON><PERSON>
 * @create: 2024/10/21 17:39
 * @Version 1.0
 */
@Data
public class RuleTargetInfoBO implements Serializable {

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 指标id
     */
    private Long targetId;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 序号
     */
    private Integer sortIndex;

    /**
     * 类型 1:五育code 2:分组id 3:指标id 4:点评项id
     */
    private Integer submitType;

    /**
     * 指标填写项
     */
    private List<TemplateInfoSaveDTO> targetOptionsList;

}