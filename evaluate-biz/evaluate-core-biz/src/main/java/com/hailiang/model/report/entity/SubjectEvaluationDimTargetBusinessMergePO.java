package com.hailiang.model.report.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import com.hailiang.enums.report.ReportTargetBusinessTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 【学科综合成绩】-【学科评价维度设置】-指标关联信息配置表实体
 *
 * @Description:
 * @Author: Jovi
 * @Date: Created in 2024/12/16
 * @Version: 2.0.0
 */
@Data
@ToString(callSuper = true)
@TableName(value = SubjectEvaluationDimTargetBusinessMergePO.TABLE_NAME)
@EqualsAndHashCode(callSuper = true)
public class SubjectEvaluationDimTargetBusinessMergePO extends BaseEntity {

    public static final String TABLE_NAME = "report_target_business_merge";

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 学校ID
     */
    private String schoolId;

    /**
     * 校区ID
     */
    private String campusId;

    /**
     * 维度配置ID
     */
    private Long dimId;

    /**
     * 业务类型 1 学科成绩
     * {@link ReportTargetBusinessTypeEnum}
     */
    private Integer businessType;

    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 评价纬度名称
     */
    private String dimName;

    /**
     * 1德育 2智育 3 体育 4 美育 5 劳育
     */
    private String moduleCode;

    /**
     * 分组ID
     */
    private Long targetGroupId;

    /**
     * 指标ID
     */
    private Long targetId;

    /**
     * 点评ID
     */
    private Long optionId;

    /**
     * 选项名称
     */
    private String submitName;

    /**
     * 选择的最后一级类型 1:五育code 2:分组id 3:指标id 4:点评项id
     */
    private Integer submitType;

    /**
     * 选择的最后一级id
     */
    private String submitId;

    /**
     * 归档标志
     */
    private Boolean archiveFlag;

    @Override
    public String toString() {
        return JSON.toJSONString(this, SerializerFeature.WriteMapNullValue);
    }
}