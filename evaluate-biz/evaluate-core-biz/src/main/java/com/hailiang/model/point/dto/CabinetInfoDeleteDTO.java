package com.hailiang.model.point.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 应用退出密码修改
 * @Author: JJL
 * @Date: 2023/7/25 11:13
 */
@Data
public class CabinetInfoDeleteDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 是否删除 1:删除， 2:不删除
     */
    @NotNull(message = "删除标记不能为空")
    private Integer deleteFlag;

}
