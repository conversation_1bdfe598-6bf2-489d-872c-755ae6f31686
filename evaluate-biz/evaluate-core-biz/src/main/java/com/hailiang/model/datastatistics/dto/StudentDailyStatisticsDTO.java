package com.hailiang.model.datastatistics.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 指标行为学生统计
 *
 * <AUTHOR>
 * @since 2024-07-4
 */
@Getter
@Setter
public class StudentDailyStatisticsDTO {

    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 学段id
     */
    private String campusSectionId;

    /**
     * 学段code
     */
    private String campusSectionCode;

    /**
     * 年级id
     */
    private String gradeId;

    /**
     * 年级code
     */
    private String gradeCode;

    /**
     * 班级id
     */
    private String classId;

    /**
     * 评价人人id
     */
    private String studentId;

    /**
     * 德育加分
     */
    private BigDecimal plusMoralScore;

    /**
     * 德育减分
     */
    private BigDecimal minusMoralScore;

    /**
     * 智育加分
     */
    private BigDecimal plusWisdomScore;

    /**
     * 智育减分
     */
    private BigDecimal minusWisdomScore;

    /**
     * 体育加分
     */
    private BigDecimal plusSportScore;

    /**
     * 体育减分
     */
    private BigDecimal minusSportScore;

    /**
     * 美育加分
     */
    private BigDecimal plusPrettyScore;

    /**
     * 美育减分
     */
    private BigDecimal minusPrettyScore;

    /**
     * 劳育加分
     */
    private BigDecimal plusWorkScore;

    /**
     * 劳育减分
     */
    private BigDecimal minusWorkScore;

    /**
     * 总加分
     */
    private BigDecimal plusTotalScore;

    /**
     * 总减分
     */
    private BigDecimal minusTotalScore;

    /**
     * 总分值
     */
    private BigDecimal totalScore;

    /**
     * 统计日期
     */
    private Date statisticsTime;

    /**
     * 数据来源 0：综合素质评价 1：内驱力 2：成绩管理 3:体测管理
     */
    private Integer dataSource;

    /**
     * 被点评次数
     */
    private Integer appraisalCount;

}
