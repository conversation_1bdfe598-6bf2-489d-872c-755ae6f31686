package com.hailiang.model.report.query;

import lombok.Data;

import java.util.Date;

/**
 * Date：2023-11-08
 * Time：16:15
 * Description：报告目标查询
 *
 * <AUTHOR>
 */
@Data
public class ReportTemplateQuery {
    /**
     * 模板id
     */
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 学段id
     */
    private String campusSectionId;

    /**
     * 学段code
     */
    private String campusSectionCode;

    /**
     * 报告模板类型 1:期末报告;2:日常报告
     */
    private Integer reportTemplateType;

    /**
     * 周期类型 1:周; 2:月; 3:年
     */
    private Integer timeType;

    /**
     * 月份（周期为年时）
     */
    private Integer monthNumber;

    /**
     * 日期（周期为周和月时）
     */
    private Integer dayNumber;

    /**
     * 是否启用，0否 1是
     */
    private Integer enableFlag;

    private Date startTime;

    private Date endTime;

    /**
     * 是否逻辑删除
     */
    private Boolean deleted = Boolean.FALSE;

    /**
     * 排序条件
     */
    private String sortCriteria;

    /**
     * 每页大小
     */
    private Integer limit;

    /**
     * 查询位置
     */
    private Integer offset;

    public ReportTemplateQuery() {
    }

    public ReportTemplateQuery(Integer reportTemplateType, Integer timeType, Integer monthNumber, Integer dayNumber,
                               Date startTime, Date endTime) {
        this.reportTemplateType = reportTemplateType;
        this.timeType = timeType;
        this.monthNumber = monthNumber;
        this.dayNumber = dayNumber;
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
