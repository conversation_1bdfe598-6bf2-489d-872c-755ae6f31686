package com.hailiang.model.dto.studentmodel;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * 基础数据-通过组织机构、角色获取所有教职工
 */
@Data
@Builder
public class BehaviourDTO {
    /**
     * 学校id
     */
    private String schoolId;
    /**
     * 学年
     */
    private String schoolYear;
    /**
     * 学期名称
     */
    private String termName;
    /**
     * 五育
     */
    private List<Integer> moduleCodes;

    /**
     * 校区id
     */
    private String campusId;
    /**
     * 班级id
     */
    private String classId;
    /**
     * 年级id
     */
    private String gradeId;
    /**
     * 学生id
     */
    private String studentId;

    private Integer contrastRange;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    private Set<String> submitIds;

    private Set<Long> targetIds;

    private Set<String> optionIds;

    /**
     * 不需要统计的记录id
     */
    private List<Long> unNeedBehaviourRecordIds;

    /**
     * 是否统计学生帮扶积分
     */
    private Boolean includeHelpBehaviour;

}