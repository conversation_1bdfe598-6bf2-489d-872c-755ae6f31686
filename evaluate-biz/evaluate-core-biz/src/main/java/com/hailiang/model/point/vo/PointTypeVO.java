package com.hailiang.model.point.vo;


import com.hailiang.model.point.dto.PointTypeClassLimitDTO;
import com.hailiang.remote.saas.vo.term.TermVo;
import java.util.List;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 积分类型（币种）分页视图对象
 *
 * <AUTHOR> 2023-06-29 16:32:36
 */
@Data
public class PointTypeVO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 币种名称
     */
    private String pointName;
    /**
     * 币种单位(对应字典id)
     */
    private Long pointUnitId;
    /**
     * 币种单位名称
     */
    private String pointUnitName;
    /**
     * 币种图片地址
     */
    private String pointLogoUrl;
    /**
     * 币种总数
     */
    private BigDecimal pointTotalCount;
    /**
     * 班级最多上浮限制
     */
    private BigDecimal classMaxRange;
    /**
     * 学生数量
     */
    private Integer studentCount;
    /**
     * 人均数量
     */
    private BigDecimal averageCount;
    /**
     * 已累计总数量
     */
    private BigDecimal point;
    /**
     * 学期
     */
    private TermVo termVo;
    /**
     * 班级限制是否开启
     */
    private Integer classLimitFlag;
    /**
     * 当前学期未兑完金币学前末是否清零，1清零，0不清零，默认清零
     */
    private Integer pointClearFlag;
    /**
     * 积分班级限制
     */
    /*List<PointTypeClassLimitGradeVO> classLimitGradeList;
    *//**
     * 剩余班级可分配数量-积分班级限制
     *//*
    private BigDecimal remainCount;*/
    private PointTypeClassLimitInfoVO pointTypeClassLimitInfoVO;
  }