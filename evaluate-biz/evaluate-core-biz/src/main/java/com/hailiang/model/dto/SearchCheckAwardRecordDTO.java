package com.hailiang.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> gaoxin
 * @create 2023/8/8 14:46
 */
@Data
public class SearchCheckAwardRecordDTO {

    /**
     * 学段ID
     */
    @NotBlank(message = "学段ID不能为空")
    private String campusSectionId;

    /**
     * 学年
     */
    @NotBlank(message = "学年不能为空")
    private String schoolYear;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 考核周期起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startTime;

    /**
     * 考核周期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date endTime;

    /**
     * 红旗id列表 （系统填充）
     */
    List<String> awardIds;

    /**
     * 年级id
     */
    @NotBlank(message = "年级ID不能为空")
    private String gradeId;

    /**
     * 班级id
     */
    @NotBlank(message = "班级ID不能为空")
    private String classId;
}
