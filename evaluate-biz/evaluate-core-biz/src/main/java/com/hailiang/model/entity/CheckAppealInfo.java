package com.hailiang.model.entity;

import java.util.Date;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * 检查申诉记录表(CheckAppealInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-08-08 13:42:51
 */
@Data
public class CheckAppealInfo extends BaseEntity {

    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 班级检查记录id
     */
    private Long checkClassInfoId;

    /**
     * 提交人id
     */
    private String submitUserId;

    /**
     * 提交人姓名
     */
    private String submitUserName;

    /**
     * 审核人id
     */
    private String approvalUserId;

    /**
     * 审核姓名
     */
    private String approvalUserName;

    /**
     * 申诉状态  1：待审核  2：通过  3：驳回
     */
    private Integer appealStatus;

}

