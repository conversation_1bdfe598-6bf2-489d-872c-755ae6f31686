package com.hailiang.model.point.vo;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 积分类型（币种） 查询对象
 *
 * <AUTHOR> 2023-06-29 16:32:36
 */
@Data
public class PointTypeClassLimitInfoVO {
    private static final long serialVersionUID = 1L;
    /**
     * 剩余年级分数
     */
    private BigDecimal remainCount;
    /**
     * 是否首次编辑
     */
    private Boolean isFirstEdit;
    /**
     * 年级对应的班级情况
     */
   List<PointTypeClassLimitGradeVO> gradeLimitList;
}