package com.hailiang.model.dto.activity.detail;

import com.hailiang.model.entity.MedalTask;
import com.hailiang.model.entity.MedalTaskCompletion;
import com.hailiang.model.entity.MedalTaskRule;
import com.hailiang.model.entity.MedalTaskRuleTarget;
import com.hailiang.model.medal.vo.MedalInfoVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/6 14:54
 */
@Data
public class MedalTaskProgressMapDTO {

    /**
     * 活动任务
     */
    private List<MedalTask> activityTasks = new ArrayList<>();

    /**
     * 活动状态map
     */
    private Map<Long, Integer> medalTaskStatusMap = new HashMap<>();

    /**
     * 二级任务信息
     */
    private List<MedalTaskCompletion> completions = new ArrayList<>();

    /**
     * 规则信息(包含一二级规则)
     */
    private List<MedalTaskRule> taskRules = new ArrayList<>();

    /**
     * 奖章信息map
     */
    private Map<Long, MedalInfoVO> medalInfoMap = new HashMap<>();

    /**
     * 活动信息map
     */
    private Map<Long, MedalActivityInfoVO> activityInfoMap = new HashMap<>();

    /**
     * 规则详情map
     */
    private Map<Long, MedalTaskRuleTarget> ruleTargetMap = new HashMap<>();

    /**
     * 颁章状态map
     */
    private Map<Long, Integer> awardStatusMap = new HashMap<>();
}
