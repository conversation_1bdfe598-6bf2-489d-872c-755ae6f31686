/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.model.report.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v0.1: ReportTemplateVO.java, v 0.1 2023年11月09日 09:19  zhousx Exp $
 */
@Data
public class ReportTemplateVO {

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long                         id;

    /**
     * 模板名称
     */
    private String                       reportTemplateName;

    /**
     * 是否启用，0否 1是
     */
    private Integer                      enableFlag;

    /**
     * 校区学段关联ID
     */
    private String                       campusSectionId;

    /**
     * 学段code
     */
    private String                       campusSectionCode;
    /**
     * 模板关联年级列表
     */
    private List<ReportTemplateGradeVO>  gradeList;

    /**
     * @see com.hailiang.enums.report.ReportTemplateTypeEnum
     * 报告模板类型 1:期末报告;2:日常报告
     */
    private Integer                      reportTemplateType;

    /**
     * @see com.hailiang.enums.report.ReportTimeTypeEnum
     * 周期类型 1:周; 2:月; 3:年
     */
    private Integer                      timeType;

    /**
     * 月份（周期为年时）
     */
    private Integer                      monthNumber;

    /**
     * 日期（周期为周和月时）
     */
    private Integer                      dayNumber;

    /**
     * 栏目列表
     */
    private List<ReportTemplateColumnVO> templateColumnList;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date                         createTime;

    /**
     * 通用背景URL
     */
    private String                       backgroundUrl;

    /**
     * 通用字体颜色
     */
    private String                       fontColor;
}