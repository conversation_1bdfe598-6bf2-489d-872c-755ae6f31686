package com.hailiang.model.dto.internaldrive.modify;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SubmitOptionInfoSubModifyDTO {


    /**
     * 选项值
     */
    @JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
    private String label;

    /**
     * 开启加分是会有
     */
    @JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
    private BigDecimal value;

    /**
     * 唯一值
     */
    @JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
    private String key;





}
