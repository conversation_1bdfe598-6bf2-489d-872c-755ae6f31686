package com.hailiang.model.point.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * 积分柜信息表
 * @TableName cabinet_info
 */
@TableName(value ="cabinet_info")
@Data
public class CabinetInfo extends BaseEntity implements Serializable {


    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 学校id
     */
    @TableField(value = "school_id")
    private String schoolId;

    /**
     * 校区id
     */
    @TableField(value = "campus_id")
    private String campusId;

    /**
     * 学段id
     */
    @TableField(value = "campus_section_id")
    private String campusSectionId;

    /**
     * 学段code
     */
    @TableField(value = "campus_section_code")
    private String campusSectionCode;

    /**
     * 积分柜编码
     */
    @TableField(value = "cabinet_code")
    private String cabinetCode;

    /**
     * 积分柜名称
     */
    @TableField(value = "cabinet_name")
    private String cabinetName;

    /**
     * 厂家id
     */
    @TableField(value = "factory_id")
    private Long factoryId;

    /**
     * 积分柜描述
     */
    @TableField(value = "cabinet_description")
    private String cabinetDescription;

    /**
     * 版本号名称
     */
    @TableField(value = "version_name")
    private String versionName;

    /**
     * 是否启用
     */
    @TableField(value = "enable_flag")
    private Integer enableFlag;
    /**
     * 是否启用
     */
    @TableField(value = "open_time")
    private String openTime;
    /**
     * 是否启用
     */
    @TableField(value = "shutdown_time")
    private String shutdownTime;
    /**
     * 是否启用
     */
    @TableField(value = "volume_value")
    private Integer volumeValue;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}