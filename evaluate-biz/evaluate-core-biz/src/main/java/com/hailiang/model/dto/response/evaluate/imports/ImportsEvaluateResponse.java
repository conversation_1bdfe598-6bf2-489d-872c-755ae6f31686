package com.hailiang.model.dto.response.evaluate.imports;

import com.hailiang.constant.Constant;
import com.hailiang.model.dto.request.evaluate.imports.ImportsEvaluateTableDataRequest;
import java.util.List;
import lombok.Data;

/**
 * 点评导入返回
 *
 * @Description: 点评导入返回
 * @Author: Jovi
 * @Date: Created in 2024/12/2
 * @Version: 2.0.0
 */
@Data
public class ImportsEvaluateResponse {

    /**
     * 是否操作成功
     * １：成功
     * ２：提交参数有误，错误信息failRecords
     * ３: 有未选择的学生，错误信息errorStudents
     * ４：有重复的提交记录，错误信息repeatImportRequests
     */
    private Integer operateSuccess = Constant.ONE;

    /**
     * 导入失败的错误信息
     * 导入不通过，有值
     * 导入通过，无值
     */
    private List<ImportsEvaluateErrorDataResponse> failRecords;

    /**
     * 导入被限制的学生信息
     */
    private List<Student> errorStudents;

    /**
     * 存在重复的数据
     */
    private List<ImportsEvaluateTableDataRequest> repeatImportRequests;


    @Data
    public static class Student {
        /**
         * 学生姓名
         */
        private String studentName;
        /**
         * 学生id
         */
        private String studentId;

        /**
         * 学号
         */
        private String studentNo;

        /**
         * 班级名称
         */
        private String className;

        /**
         * 班级id
         */
        private String classId;
    }

}