package com.hailiang.model.point.vo;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 积分兑换记录表（订单表）分页视图对象
 *
 * <AUTHOR> 2023-06-29 16:32:36
 */
@Data
public class ProductExchangeDetailVO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;
      /**
     * 租户id
     */
    private String tenantId;
      /**
     * 学校id
     */
    private String schoolId;
      /**
     * 校区id
     */
    private String campusId;
      /**
     * 学段id
     */
    private String campusSectionId;
      /**
     * 学段code
     */
    private String campusSectionCode;
      /**
     * 年级id
     */
    private String gradeId;
      /**
     * 年级code
     */
    private String gradeCode;
      /**
     * 班级id
     */
    private String classId;
      /**
     * 用户id（包括学生id）
     */
    private String userId;
      /**
     * 商品id
     */
    private Long productId;
      /**
     * 商品名称
     */
    private String productName;
      /**
     * 商品类型 1：实体 2：虚拟
     */
    private Integer productType;
      /**
     * 状态 0：待支付 1：已完成 2：已关闭 
     */
    private Integer status;
      /**
     * 兑换积分
     */
    private BigDecimal exchangePoint;
      /**
     * 兑换方式 1:积分柜兑换 2：老师兑换
     */
    private Integer exchangeType;
      /**
     * 兑换人id（积分柜id或老师id）
     */
    private String exchangeId;
      /**
     * 兑换时间
     */
    private Date exchangeTime;
  }