package com.hailiang.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

/**
 * 活动对应年级表
 * @TableName medal_activity_grade
 */
@TableName(value ="medal_activity_grade")
@Data
public class MedalActivityGrade extends BaseEntity implements Serializable {


    /**
     * saas租户id
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 学校id
     */
    @TableField(value = "school_id")
    private String schoolId;

    /**
     * 校区id
     */
    @TableField(value = "campus_id")
    private String campusId;

    /**
     * 争章活动id
     */
    @TableField(value = "medal_activity_id")
    private Long medalActivityId;

    /**
     * 年级code
     */
    @TableField(value = "grade_code")
    private String gradeCode;

    /**
     * 年级名称
     */
    @TableField(value = "grade_name")
    private String gradeName;

    /**
     * 年级id

     */
    @TableField(value = "grade_id")
    private String gradeId;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}