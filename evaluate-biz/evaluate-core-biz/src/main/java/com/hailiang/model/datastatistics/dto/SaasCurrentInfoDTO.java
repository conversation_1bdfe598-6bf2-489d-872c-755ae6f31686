package com.hailiang.model.datastatistics.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * saas学段/年级/班级信息
 *
 * <AUTHOR>
 * @date 2023/5/8 10:42
 */
@Data
public class SaasCurrentInfoDTO {


    /**
     * 根据(学段/年级/班级)获取对应的(年级/班级/老师)列表
     */
    Map<String, String> businessMap;

    /**
     * 业务类型顺序
     */
    private List<String> sortBusinessKey;

    /**
     * 当前学段/年级/班级信息
     */
    private String currentBusinessName;
}
