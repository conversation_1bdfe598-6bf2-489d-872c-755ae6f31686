package com.hailiang.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import com.hailiang.model.entity.mongo.TargetTemplate;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 指标表
 *
 * @TableName evaluate_target
 */
@TableName(value = Target.TABLE_NAME)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Target extends BaseEntity implements Serializable {

    public static final String TABLE_NAME = "evaluate_target";
    public static final String DELETED = "et.deleted";
    public static final String TARGET_STATUS = "et.target_status";
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 图标url
     */
    private String iconUrl;
    /**
     * 指标名称
     */
    private String targetName;
    /**
     * 1:启用 0：禁用
     */
    private Integer targetStatus;
    /**
     * 学校id
     */
    private String schoolId;
    /**
     * 校区id
     */
    private String campusId;
    /**
     * 分组id
     */
    private Long groupId;
    /**
     * 指标模板id，指标对应模板在MongoDB里的id
     */
    private String templateId;
    /**
     * 1：工作日每天 2：每周 3：每月 4：每年 5：不限定频次　６:一学期一次
     *　需要发送消息提醒的类型：１、２、３、４
     *  家长提交次数限制的类型：１、２、３、４、６
     *  老师提交次数限制的类型：6
     */
    private Integer submitType;
    /**
     * 工作日每天：此项为空 每周：（1,2,3 数字表示周几） 每月：（15,20，30 数字表示几号） 每年：（0915， 0627 数字前两位表示月，后两位表示日）
     */
    private String submitDate;
    /**
     * 提醒时间：0941 表示9点47分
     */
    private String remindTime;
    /**
     * 法定节假日、寒暑假通知flag 1：发送 0：不发送
     */
    private Integer holidayNoticeFlag;
    /**
     * 是否每次必须提交 0：否 1： 是
     */
    private Integer mustSubmitFlag;
    /**
     * 超过多少小时系统催交 单位：小时
     */
    private Integer urgeTime;
    /**
     * 是否推送给家长 0：否 1：是
     */
    private Integer sendParentFlag;
    /**
     * 教职工是否全选：0：否  1：是
     */
    private Integer staffFullCheckFlag;
    /**
     * 指标顺序，指标排序按此字段升序，从1开始
     */
    private Integer sortIndex;
    /**
     * 数据来源  0：综合素质评价 1：星动力 2：成绩管理
     */
    private Integer dataSource;
    /**
     * 加分控件名称
     */
    private String scoreControlName;
    /**
     * 加分控件类型 -1：减分  1：加分
     */
    private Integer scoreControlType;
    /**
     * 分数
     */
    private BigDecimal score;
    /**
     * 分值 （分数的绝对值）
     */
    private BigDecimal scoreValue;
    /**
     * saas租户id
     */
    private String tenantId;
    /**
     * 源指标 id
     */
    @TableField(value = "source_target_id")
    private Long sourceTargetId;
    /**
     * 是否图文点评  1：是
     */
    private Integer pictureEvaluateFlag;
    /**
     * 图文点评排序
     */
    private Integer pictureSortIndex;
    /**
     * 指标表单信息JSON串
     *
     * @see TargetTemplate
     */
    private String templateInfoJson;
    /**
     * 是否开启分值不参与兑换 0否 1是
     */
    private Integer notPartExchange;
    /**
     * 限定家长提交次数开关，1：开启，0：关闭，默认关闭
     */
    private Integer parentSubmitLimitFlag;
    /**
     * 限定家长提交次数，默认1次，数值范围1-9999
     */
    private Integer parentSubmitLimitTimes;
    /**
     * 家长提交需审核开关，1：开启，0：关闭，默认开启
     */
    private Integer parentSubmitReviewFlag;
    /**
     * 学生提交需审核开关，1：开启，0：关闭，默认开启
     */
    private Integer studentSubmitReviewFlag;
    /**
     * 是否开启分值不加入画像综合得分和点评记录中 0否 1是
     */
    private Integer targetNotPartCount;
    /**
     * 违纪学生的扣分需要延续到下一个学年 0否 1是
     */
    private Integer scoreContinuationFlag = 0;
    /**
     * 老师提交后是否通知班主任 0否 1是
     */
    private Integer isNotifyTeacher;
    /**
     * 指标类型：0普通、1系统推荐、2系统内置
     */
    private Integer targetType;
}
