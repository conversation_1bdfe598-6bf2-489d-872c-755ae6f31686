package com.hailiang.model.report.response;

import com.hailiang.remote.hai.domain.dto.response.HaiAlbumTagInfoResponse;
import com.hailiang.remote.hai.domain.dto.response.HaiClassAlbumImgResponse;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 班级相册-学生个人相册返回结果
 * @Author: huyouting
 * @Date: Created in 2024-12-17
 * @Version: v2.1.1
 */
@Data
public class ClassAlbumRecordStudentInfoResponse {

    /**
     * 发布记录id
     */
    private Long id;

    /**
     * 本发布记录发布时间
     */
    private Date publishTime;

    /**
     * 相册发布记录说明（即title）, 最多500个字符
     */
    private String memo;

    /**
     * 本发布记录涉及到的照片数据
     */
    private List<HaiClassAlbumImgResponse> haiClassAlbumImgInfos;

    /**
     * 本发布记录涉及到的标签数据
     */
    private List<HaiAlbumTagInfoResponse> tagInfoVOList;

}
