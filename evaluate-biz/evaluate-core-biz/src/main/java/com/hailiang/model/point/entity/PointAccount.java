package com.hailiang.model.point.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * PointAccount实体类
 *
 * <AUTHOR> 2023-06-29 16:32:36
 */
@Data
@TableName("point_account")
public class PointAccount extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private String tenantId;
      /**
     * 学校id
     */
    private String schoolId;
      /**
     * 校区id
     */
    private String campusId;
      /**
     * 用户id（比如学生id）
     */
    private String userId;
      /**
     * 币种id
     */
    private Long pointTypeId;
      /**
     * 积分余额
     */
    private BigDecimal point;
  }
