package com.hailiang.model.point.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品信息表 查询对象
 *
 * <AUTHOR> 2023-06-29 16:32:36
 */
@Data
public class ProductInfoUpdateDTO {
    private static final long serialVersionUID = 1L;
    /**
     * 商品id
     */
    @NotNull(message = "商品信息不能为空")
    private Long id;
    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String productName;
    /**
     * 商品类型 1：实体 2：虚拟
     */
    @NotNull(message = "商品类型不能为空")
    private Integer productType;
    /**
     * 需要的积分
     */
    @NotNull(message = "兑换所需积分不能为空")
    private BigDecimal needPoint;
    /**
     * 库存
     */
    @NotNull(message = "库存不能为空")
    private Integer changeStock;
    /**
     * 图片
     */
    private List<ProductPictureDTO> pictureDTOs;
}