package com.hailiang.model.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 初始分基础表
 *
 * @Description: 初始分基础表
 * @Author: Jovi
 * @Date: Created in 2024/11/4
 * @Version: 2.0.0
 */
@Data
@TableName("evaluate_initial_score")
public class InitialScorePO extends BaseEntity {

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 学校代码
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 初始分数
     */
    private BigDecimal initialScore;

    /**
     * 是否自动分配，1：分配，0：不分配，默认 0
     */
    private Integer apportionFlag;

    /**
     * 是否参与积分兑换， 1：是  0：否  默认 0
     */
    private Integer exchangeFlag;

    /**
     * 是否拆分到五育下， 1：是  0：否  默认 0
     */
    private Integer splitFlag;

    /**
     * 初始分类型（0：总分、1：德育、2：智育、3：体育、4：美育、5、劳育）
     */
    private Integer initialScoreType;

    /**
     * 学年
     */
    private String schoolYear;
    /**
     * 学期
     */
    private String termName;

    /**
     * 分配时间
     */
    private Date allocationTime;


    /**
     * 分配记录ID（同一批次相同ID）
     */
    private Long allocationId;


    @Override
    public String toString() {
        return JSON.toJSONString(this, SerializerFeature.WriteMapNullValue);
    }
}

