package com.hailiang.model.datastatistics.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.List;

/**
 * @Author: JJL
 * @Date: 2023/4/24 19:08
 */
@Data
public class TeacherVO {
    /**
     * 老师id
     */
    private String teacherId;

    /**
     * 老师姓名
     */
    @Excel(name = "老师姓名", width = 18)
    private String teacherName;

    /**
     * 次数
     */
    @Excel(name = "点评次数", width = 18)
    private Integer evaluateNum;

    /**
     * 天数
     */
    @Excel(name = "点评天数", width = 18)
    private Integer dayNum;
    /**
     * 头像
     */
    private String iconUrl;
    /**
     * 年级或者班级
     */
    @Excel(name = "所在年级", width = 14)
    private String gradeOrClass;


}
