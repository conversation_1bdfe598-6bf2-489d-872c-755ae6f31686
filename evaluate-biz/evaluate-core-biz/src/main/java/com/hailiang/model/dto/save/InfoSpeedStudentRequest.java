package com.hailiang.model.dto.save;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * 【极速点评】-【学生相关信息】
 *
 * @Description:
 * @Author: Jovi
 * @Date: Created in 2024/10/18
 * @Version: 2.0.0
 */
@Data
public class InfoSpeedStudentRequest {

    /**
     * 学生 id
     */
    @NotBlank(message = "学生 id不能为空")
    private String studentId;

    /**
     * 学生姓名
     */
    @NotBlank(message = "学生姓名不能为空")
    private String name;

    /**
     * 点评项
     */
    @NotEmpty(message = "请选择具体的点评项")
    @Valid
    private List<InfoSpeedOptionRequest> speedOptions;

}