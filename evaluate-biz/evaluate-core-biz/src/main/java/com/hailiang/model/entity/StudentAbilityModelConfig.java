package com.hailiang.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseBusinessEntity;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

/**
 * StudentAbilityModelConfig实体类
 *
 * <AUTHOR> 2024-04-19 15:29:38
 */
@Data
@TableName("evaluate_student_ability_model_config")
public class StudentAbilityModelConfig  extends BaseBusinessEntity {
    private static final long serialVersionUID = 1L;
      /**
     * 能力id
     */
    private Long abilityModelId;
      /**
     * 能力名称
     */
    private String abilityName;
      /**
     * 能力类型 1：五育评价 2：体测成绩 3：学科成绩
     */
    private Integer abilityType;
      /**
     * 能力配置信息（五育评价和学科成绩）
     */
    private String abilityConfig;
      /**
     * 排序字段
     */
    private Integer sortIndex;
  }
