package com.hailiang.model.report.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hailiang.model.dto.query.BasePageQuery;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 查询学生栏目数据DTO
 *
 * @Description: 查询学生栏目数据DTO
 * @Author: TanJian
 * @Date: Created in 2024-12-18
 * @Version: 1.0.0
 */
@Data
public class ReportDataStudentColumnQuery extends BasePageQuery implements Serializable {
    private static final long serialVersionUID = -8225154658145663034L;

    @NotNull(message = "栏目Id不能为空")
    private String columnId;

    private String studentId;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 作品等级
     * EXCELLENT(1, "优秀"),
     * PASS(2, "良好"),
     * FAILED(3, "不符合主题，需重新提交"),
     * QUALIFIED(4, "合格")
     */
    private Integer productionLevel;

    /**
     * 活动类型 1：校园活动，2：校外写实
     */
    private Integer activityType;

    /**
     * 是否只看我提交的 0 否 1 是
     */
    private Integer viewMySubmit;

    @NotNull(message = "请输入活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activityStartTime;

    @NotNull(message = "请输入活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activityEndTime;
}
