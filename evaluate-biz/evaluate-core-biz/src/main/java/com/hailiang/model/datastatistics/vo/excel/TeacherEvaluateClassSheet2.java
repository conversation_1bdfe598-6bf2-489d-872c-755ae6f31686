package com.hailiang.model.datastatistics.vo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/8 17:55
 */
@ExcelTarget("TeacherEvaluateClassSheet2")
@Data
public class TeacherEvaluateClassSheet2 {

    /**
     * 老师姓名
     */
    @Excel(name = "老师姓名", width = 18)
    private String teacherName;

    /**
     * 点评次数
     */
    @Excel(name = "点评次数", width = 18)
    private String evaluateNum;

    /**
     * 点评天数
     */
    @Excel(name = "点评天数", width = 18)
    private String dayNum;

    /**
     * 所在班级
     */
    @Excel(name = "所在班级", width = 18)
    private String gradeOrClass;
}
