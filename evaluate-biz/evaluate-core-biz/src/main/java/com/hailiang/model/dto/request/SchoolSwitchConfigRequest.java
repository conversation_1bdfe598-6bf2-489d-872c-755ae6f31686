package com.hailiang.model.dto.request;

import com.hailiang.enums.SysSwitchConfigEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 开关配置查询
 *
 * @Description: 开关配置查询
 * @Author: JJl
 * @Date: Created in 2024-08-15
 * @Version: 1.9.1
 */
@Data
public class SchoolSwitchConfigRequest {
    /**
     * 业务 1：德育人脸识别，2：星未来扫脸评价
     * {@link  SysSwitchConfigEnum}
     */
    @NotNull(message = "请选择业务类型")
    private Integer type;
}
