
package com.hailiang.model.report.query;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 导出家长评价报告请求参数
 * <AUTHOR>
 */
@Data
public class ReportParentExportRequest {

    @NotNull(message = "报告单id不能为空")
    private Long reportDataId;

    @NotNull(message = "报告单快照id不能为空")
    private Long reportSnapshotId;
    /**
     * 班级id
     */
    @NotEmpty(message = "班级id不能为空")
    private List<String> classIds;
}