package com.hailiang.model.report.dto;

import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 【学科评价维度配置】-查询学科信息入参
 *
 * @Description: 查询学科信息入参
 * @Author: Jovi
 * @Date: Created in 2024/12/31
 * @Version: 2.0.0
 */
@Data
public class EvaluationDimConfigListSubjectQuery {

    /**
     * 学段code
     */
    @NotBlank(message = "学段code不能为空")
    private String sectionCode;

    /**
     * 年级code
     */
    private String gradeCode;
}
