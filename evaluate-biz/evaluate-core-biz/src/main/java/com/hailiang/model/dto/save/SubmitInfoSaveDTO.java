package com.hailiang.model.dto.save;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;

@Data
@Accessors(chain = true)
public class SubmitInfoSaveDTO {

    /**
     * 是否可评价班级其他学生
     */
    private Boolean isOutClassSideStudent;

    /**
     * 是否选择到学生
     */
    private Boolean isStudent;

    /**
     * 多选提示文案
     */
    private String multipleText;


    /**
     * 类型 1:五育code 2:分组id 3:指标id 4:点评项id
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private Integer submitType;

    /**
     * 指标id
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private Long targetId;

    /**
     * 类型
     */
    private String type;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 类型名称 前端回显
     */
    private String reTypeName;

    /**
     * 说明名称最大字数
     */
    private Integer nameMaxlength;

    /**
     * 唯一值
     */
    private String key;

    /**
     * 名称
     */
    private String name;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 是否开启加分
     */
    private Boolean isScore;

    /**
     * 提示语
     */
    private String placeholder;
    /**
     * 提示语长度
     */
    private Integer placeholderLength;

    /**
     * 是否只拍照
     */
    private Boolean isPhoto;

    /**
     * 加分类型，“2”为减分，“1”为加分
     */
    private Integer scoreType;

    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 是否多选
     */
    private Boolean isMultiple;

    /**
     * 模块编码
     */
    private String model;

    /**
     * 单位
     */
    private String unit;

    /**
     * 手动调整分值
     */
    private Boolean isAdjustScore;

    /**
     * 分值调整值
     */
    private BigDecimal adjustScore;

    /**
     * 手动调整的分值
     */
    private BigDecimal adjustValue;

    /**
     * 明细列表
     */
    private List<LinkedHashMap> list;

    /**
     * 复制完后的明细列表答案
     */
    private List<List<LinkedHashMap>> submitList;

    /**
     * 选项
     */
    private SubmitOptionInfoSaveDTO options;

    /**
     * 选项值
     */
    private Object submitValue;


}
