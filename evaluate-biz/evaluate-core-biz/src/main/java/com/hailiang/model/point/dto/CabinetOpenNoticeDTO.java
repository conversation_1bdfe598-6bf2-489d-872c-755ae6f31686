package com.hailiang.model.point.dto;

import lombok.Data;

import java.util.List;

/**
 * 商品信息表 查询对象
 *
 * <AUTHOR> 2023-06-29 16:32:36
 */
@Data
public class CabinetOpenNoticeDTO {
    private static final long serialVersionUID = 1L;
    /**
     * 消息id
     */
    private String messageId;
    /**
     * 积分柜编码
     */
    private String cabinetCode;
    /**
     * 积分柜类型
     */
    private String cabinetType;
    /**
     * 柜子成功编号集合
     */
    private List<String> successLatticeNumber;
    /**
     * 柜子失败编号集合
     */
    private List<String> failLatticeNumber;
}