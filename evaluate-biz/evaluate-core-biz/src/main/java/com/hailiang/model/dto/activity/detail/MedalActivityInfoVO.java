package com.hailiang.model.dto.activity.detail;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/28 16:51
 */
@Data
public class MedalActivityInfoVO {

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 活动状态 1：未发布，2：未开始，3：进行中，4：已结束，5：已失效
     */
    private Integer status;
}
