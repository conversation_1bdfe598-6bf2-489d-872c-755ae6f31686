package com.hailiang.model.report.dto;

import com.hailiang.enums.report.ScoreStandardExamTypeEnum;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> hljy
 * @date 2023/8/29 18:00
 */
@Data
public class ExamConfigQueryDTO {
    /**
     * 校区id
     */
    private String campusId;
    /**
     * 课程code
     */
    private List<String> subjectCodes;
    /**
     * 开始时间
     */
    private Date termStartTime;
    /**
     * 结束时间
     */
    private Date termEndTime;
    /**
     * 学年
     */
    private String schoolYear;
    /**
     * 学期
     */
    private String termName ;

    /**
     * 考试类型
     * 1月考2 期中考试 3期末考试 4其他类型考试 5日常检测
     * @see ScoreStandardExamTypeEnum
     */
    private Integer examType;

    /**
     * 是否是全部考试（如果是全部考试就不用指定考试id）
     */
    private Boolean allExamFlag;

    /**
     * 考试id列表
     */
    private List<String> examIds;
    /**
     * 学科成绩 1平均值 2最高值
     */
    Integer calculateType;
}
