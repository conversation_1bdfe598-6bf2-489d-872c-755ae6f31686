package com.hailiang.model.dto.save;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.Objects;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;

@Data
@Accessors(chain = true)
public class TemplateInfoSaveDTO implements Serializable{
    /**
     * 类型 1:五育code 2:分组id 3:指标id 4:点评项id
     */
    private Integer submitType;

    /**
     * 指标id
     */
    private Long targetId;

    /**
     * 类型
     */
    private String type;

    /**
     * 提示语
     */
    private String hintText;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 类型名称 前端回显
     */
    private String reTypeName;

    /**
     * 说明名称最大字数
     */
    private Integer nameMaxlength;

    /**
     * 名称
     */
    @NotBlank(message = "控件名称不能为空")
    private String name;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 是否开启加分
     */
    private Boolean isScore;

    /**
     * 提示语
     */
    private String placeholder;
    /**
     * 提示语长度
     */
    private Integer placeholderLength;
    /**
     * 是否只拍照
     */
    private Boolean isPhoto;

    /**
     * 加分类型，“2”为减分，“1”为加分,"0"不加减
     */
    private Integer scoreType;

    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 是否多选
     */
    private Boolean isMultiple;

    /**
     * 是否选择到学生
     */
    private Boolean isStudent;

    /**
     * 多选提示文案
     */
    private String multipleText;

    /**
     * 手动调整分值
     */
    private Boolean isAdjustScore;

    /**
     * 是否扣分延续
     */
    private Boolean isContinuousRecording;

    /**
     * 是否可点评任教外的学生
     */
    private Boolean isOutSideStudent;

    /**
     * 是否可人脸识别选人
     */
    private Boolean isSelectByFace;

    /**
     * 班级下的学生是否需要全选
     */
    private Boolean isAllStudent;

    /**
     * 是否可评价班级其他学生
     */
    private Boolean isOutClassSideStudent;

    /**
     * 支持老师添加常用语
     */
    private Boolean isUsualWords;

    /**
     * 分值调整值
     */
    private BigDecimal adjustScore;

    /**
     * 调整值
     */
    private BigDecimal adjustValue;

    /**
     * 单位
     */
    private String unit;

    /**
     * 组件唯一值
     */
    private String key;

    /**
     * 模块编码
     */
    private String model;

    /**
     * 明细列表
     */
    private List<LinkedHashMap> list;

    /**
     * 明细列表
     */
    private List<List<LinkedHashMap>> submitList;

    /**
     * 选项
     */
    private InnerSubmitOptionInfoSave options;

    @Data
    public static class InnerSubmitOptionInfoSave{
        /**
         * 是否开启加分
         */
        private Boolean isScore;

        private List<InnerSubmitOptionInfoSubSave> options;
    }

    @Data
    public static class InnerSubmitOptionInfoSubSave{

        /**
         * 五育code
         */
        private Integer moduleCode;

        /**
         * 选项值
         */
        @JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
        private String label;

        /**
         * 开启加分是会有
         */
        @JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
        private BigDecimal value;

        /**
         * 唯一值
         */
        @JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
        private String key;

        /**
         * 指标id
         */
        @JsonIgnoreProperties(ignoreUnknown = true)
        private String targetId;

        /**************************科学控件用到**************************/

        /**
         * 确定唯一值
         */
        private String keyValue;

        /**
         * 学段id
         */
        private String sectionId;

        /**
         * 学段code
         */
        private String sectionCode;

        /**
         * 学段名称
         */
        private String sectionName;

        /**
         * 课程id
         */
        private String courseId;

        /**
         * 课程code
         */
        private String courseCode;

        /**
         * 课程名称
         */
        private String courseName;

        /**
         * 科目id
         */
        private String subjectId;

        /**
         * 科目code
         */
        private String subjectCode;

        /**
         * 科目名称
         */
        private String subjectName;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            InnerSubmitOptionInfoSubSave that = (InnerSubmitOptionInfoSubSave) o;

            return Objects.equals(keyValue, that.keyValue);
        }

        @Override
        public int hashCode() {
            return Objects.hash(keyValue);
        }

        /**************************科学控件用到**************************/

    }
}
