package com.hailiang.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode
public class StudentTreeStudentVO {

    /**
     * 学生id
     */
    private Long id;

    /**
     * 学生姓名
     */
    private String name;

    /**
     * 层级 4：年级 5：班级 6：学生
     */
    private Integer type;

    /**
     * 班级ID
     */
    private Long classId;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 寝室id
     */
    private Long roomId;

    /**
     * 寝室名称
     */
    private String roomName;

    /**
     * 学生照片
     */
    private String avatar;

}