package com.hailiang.model.response.report;

import lombok.Data;

/**
 * @Description:
 * @Author: huyouting
 * @Date: Created in 2025-05-20
 * @Version: v2.5.7
 */
@Data
public class ReportSubmitTypeInfoResponse {

    /**
     * 选择的最后一级类型。
     * <p>1 - 五育code
     * <p>2 - 分组id
     * <p>3 - 指标id
     * <p>4 - 点评项id
     */
    private Integer submitType;

    /**
     * 选择的最后一级id。
     * 根据submitType的不同，标识不同层级的唯一标识符。
     */
    private String submitId;

}
