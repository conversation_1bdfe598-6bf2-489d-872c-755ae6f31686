package com.hailiang.model.point.vo;

import com.hailiang.model.point.dto.LayoutDTO;
import lombok.Data;

import java.util.List;

/**
 * @Author: JJL
 * @Date: 2023/7/27 11:56
 */
@Data
public class LatticeTypeVO {
    /**
     * 货品柜布局id
     */
    private Long id;
    /**
     * 货品柜id
     */
    private Long cabinetLatticeId;
    /**
     * 货品柜名称
     */
    private String cabinetLatticeName;
    /**
     * 货品柜地址
     */
    private String cabinetLatticeAddress;
    /**
     * 厂家id
     */
    private Long factoryId;
    /**
     * 货品柜类型id
     */
    private Long cabinetLatticeTypeId;
    /**
     * 货品柜类型名称
     */
    private String factoryTypeLatticeName;
    /**
     * 布局配置信息
     */
    private String layoutConfigInfo;
    /**
     * 布局配置信息
     */
    private List<LayoutDTO> layoutDTOS;
    /**
     * @see com.hailiang.enums.cabinet.CabinetLatticeTypeClassifyEnum
     * 货品柜分类：1-货品柜；2-弹簧柜
     */
    private Integer cabinetLatticeTypeClassify;
}
