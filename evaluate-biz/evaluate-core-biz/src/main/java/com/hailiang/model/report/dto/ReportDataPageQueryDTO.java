package com.hailiang.model.report.dto;

import lombok.Data;

@Data
public class ReportDataPageQueryDTO {
    /**
     * 学段code  1:幼儿园，2:小学，3:初中，4:高中
     */
    private Integer sectionCode;

    /**
     * 年级id
     */
    private String gradeId;

    /**
     * 学年
     */
    private String schoolYear;

    /**
     * 学期名字
     */
    private String termName;

    /**
     * @see com.hailiang.enums.report.ReportTemplateTypeEnum
     * 报告模板类型 1:日常报告;2:期末报告
     */
    private Integer reportTemplateType;

    /**
     * 第几页
     */
    private long pageNum = 1;

    /**
     * 每页大小
     */
    private long pageSize = 10;

}
