package com.hailiang.model.point.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 应用退出密码修改
 * @Author: JJL
 * @Date: 2023/7/25 11:13
 */
@Data
public class UpdatePasswordDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 应用退出密码
     */
    @NotBlank(message = "应用退出密码不能为空")
    private String newPassword;

}
