package com.hailiang.model.dto.save;

import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 快速点评选项
 *
 * @Description: 快速点评学生分组
 * @Author: JiangJunLi
 * @Date: Created in 2024-01-25
 * @Version: 1.6.0
 */
@Data
public class InfoSpeedOptionRequest {

    /**
     * 极速点评分组 id
     */
    @NotBlank(message = "极速点评分组 id不能为空")
    private String speedGroupId;

    /**
     * 极速点评分组名
     */
    private String speedGroupName;

    /**
     * 指标表id
     */
    private Long targetId;

    /**
     * 指标提交限制类型
     */
    private Integer targetSubmitType;

    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 是否开启分值不加入画像综合得分和点评记录中 0否 1是
     */
    private Integer targetNotPartCount;

    /**
     * 类型（分值、多选、单选）
     */
    private String type;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 唯一值
     */
    private String key;
    /**
     * 第三方 id
     */
    private Long thirdDataId;

    /**
     * 选项值
     */
    private Object submitValue;

    /**
     * 手动调整分值
     */
    private Boolean isAdjustScore;

    /**
     * 分值调整值
     */
    private BigDecimal adjustScore;

    /**
     * 手动调整的分值
     */
    private BigDecimal adjustValue;


    /*=================================================星动力=================================================*/
    /**
     * 极速点评分类的类型 枚举： general|普通 school|校级
     *
     * @see com.hailiang.enums.speed.SpeedApplyLevelEnum
     */
    @NotBlank(message = "师标校标分组标识不能为空")
    private String applyLevelGroup;

    /**
     * 极速点评点评项的分类 枚举： general|普通 school|校级
     *
     * @see com.hailiang.enums.speed.SpeedApplyLevelEnum
     */
    @NotBlank(message = "师级校级点评项标识不能为空")
    private String applyLevel;
    /*=================================================星动力=================================================*/

    /**
     * 模块code 1：德育 2：智育 3：体育 4：美育 5：劳育
     */
    private Integer moduleCode;

    /**
     * 分值控件名称
     */
    private String scoreName;

    /**
     * 分值控件分数
     */
    private BigDecimal scoreValue;

    /**
     * 必填多行文本内容
     */
    private List<String> textareaList;
}
