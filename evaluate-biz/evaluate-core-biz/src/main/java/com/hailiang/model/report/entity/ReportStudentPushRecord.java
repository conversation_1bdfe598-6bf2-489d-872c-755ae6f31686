package com.hailiang.model.report.entity;

import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportStudentPushRecord extends BaseEntity {

    /**
     * Column: tenant_id
     * Type: VARCHAR(64)
     * Remark: 租户id
     */
    private String tenantId;

    /**
     * Column: school_id
     * Type: VARCHAR(64)
     * Remark: 学校id
     */
    private String schoolId;

    /**
     * Column: campus_id
     * Type: VARCHAR(64)
     * Remark: 校区id
     */
    private String campusId;

    /**
     * Column: campus_section_id
     * Type: VARCHAR(64)
     * Remark: 学段id
     */
    private String campusSectionId;

    /**
     * Column: campus_section_code
     * Type: VARCHAR(64)
     * Remark: 学段code
     */
    private String campusSectionCode;

    /**
     * Column: campus_section_name
     * Type: VARCHAR(64)
     * Remark: 学段名称
     */
    private String campusSectionName;

    /**
     * Column: report_data_id
     * Type: VARCHAR(256)
     * Remark: 报告单id(废弃)
     */
    private Long reportDataId;
    /**
     * Column: report_data_number
     * Type: VARCHAR(256)
     * Remark: 报告单编号
     */
    private String reportDataNumber;
    /**
     * Column: report_data_name
     * Type: VARCHAR(256)
     * Remark: 报告单名称
     */
    private String reportDataName;

    /**
     * Column: report_template_snapshot_id
     * Type: BIGINT
     * Remark: 模板快照id
     */
    private Long reportTemplateSnapshotId;

    /**
     * Column: reportTemplateType
     * Type: BIGINT
     * Remark: 模板快照类型
     */
    private Integer reportTemplateType;

    /**
     * 学生Id
     * */
    private String studentId;

    /**
     * 学号
     * */
    private String studentNo;

    /**
     * 学生姓名
     * */
    private String studentName;

    /**
     * 家长Id
     * */
    private String parentId;

    /**
     * 家长姓名
     * */
    private String parentName;

    /**
     * 联系方式
     * */
    private String mobile;
    /**
     * 推送方式：1：hai 家校，2：短信
     * */
    private Integer pushWay;
    /**
     * 推送日期
     * */
    private Date pushTime;

    /**
     * 推送失败时的原始数据, 用于再次点击推送时将该部分数据进行重新推送
     * */
    private String originData;

    /**
     * 推送状态
     * */
    private Integer pushStatus;

    /**
     * 推送返回结果
     * */
    private String pushResult;

    /**
     * 报告单版本 1-老 2-新
     */
    private Integer reportVersion;
}
