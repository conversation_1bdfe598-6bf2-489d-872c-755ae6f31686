package com.hailiang.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class StudentWithClassGradeInfoSortListVO {
    @ApiModelProperty(value = "学生id")
    private Long studentId;

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value="班级ID")
    private Long classId;

    @ApiModelProperty(value="班级名称")
    private String className;

    @ApiModelProperty(value = "班级流水号")
    private Integer classNum;

    @ApiModelProperty(value="年级ID")
    private Long gradeId;

    @ApiModelProperty(value="年级名称")
    private String gradeName;

    @ApiModelProperty("年级统一标识")
    private String gradeCode;

    @ApiModelProperty(value = "房间id")
    private Long roomId;

    @ApiModelProperty(value = "房间名称")
    private String roomName;
    @ApiModelProperty(value = "学生照片")
    private String avatar;
}
