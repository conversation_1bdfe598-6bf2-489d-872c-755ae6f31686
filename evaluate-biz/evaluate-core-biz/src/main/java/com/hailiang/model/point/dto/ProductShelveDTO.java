package com.hailiang.model.point.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商品信息表 查询对象
 *
 * <AUTHOR> 2023-06-29 16:32:36
 */
@Data
public class ProductShelveDTO {
    private static final long serialVersionUID = 1L;
    /**
     * 商品id
     */
    @NotNull(message = "商品信息不能为空")
    private Long productId;
    /**
     * 积分柜编码
     */
    @NotNull(message = "请选择积分柜")
    private String cabinetCode;
    /**
     * 格子柜编号
     */
    @NotNull(message = "请选择柜子")
    private List<String> layoutNumbers;

}