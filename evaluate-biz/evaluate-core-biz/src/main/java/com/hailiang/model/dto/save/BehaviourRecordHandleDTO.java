package com.hailiang.model.dto.save;

import com.hailiang.model.entity.BehaviourRecord;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 行为记录新增/修改记录
 *
 * <AUTHOR>
 * @date 2023/6/15 17:14
 */
@Data
@Accessors(chain = true)
public class BehaviourRecordHandleDTO {

    /**
     * 新增的行为记录
     */
    private List<Long> addBehaviourRecordIds;

    /**
     * 删除的行为记录;
     */
    private List<Long> deletedBehaviourRecordIds;

    /**
     * 新增的点评记录
     */
    private List<BehaviourRecord> addBehaviourRecords;

    /**
     * 被限制的学生列表
     */
    private List<StudentDTO> restrictedStudentList;

    @Data
    public static class StudentDTO {

        private String studentId;

        private String studentName;

    }
}
