package com.hailiang.model.report.dto;

import com.hailiang.enums.report.ScoreStandardExamTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> ji<PERSON><PERSON><PERSON>
 * @create 2023/9/5 10:50
 */
@Data
public class HistoryIssueDTO {
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 考试类型
     * 1月考2 期中考试 3期末考试 4其他类型考试 5日常检测
     * {@link ScoreStandardExamTypeEnum}
     */
    @NotNull(message = "考试类型不能为空")
    private Integer examType;

}
