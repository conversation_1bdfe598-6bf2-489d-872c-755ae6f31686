package com.hailiang.model.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 第三方信息表 查询对象
 *
 * <AUTHOR> 2023-02-17 17:40:34
 */
@Data
@Builder
public class ThirdDataInfoDTO  {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 原始信息json
     */
    private String sourceDataInfo;
    /**
     *  数据操作标识  1：添加 2：修改 3：删除 4：启用 5：禁用
     */
    private Integer operateFlag;
    /**
     * 数据处理标志 0：未处理 1：处理成功 2：处理失败
     */
    private Integer disposeFlag;
    /**
     * 数据来源  0：综合素质评价 1：星动力 2：成绩管理
     */
    private Integer dataSource;
    /**
     * 数据类型  1：指标  2：指标填写记录
     */
    private Integer dataType;
    /**
     * 外部记录主键id，结合渠道和数据类型唯一确定该记录
     */
    private String thirdRecordId;
    /**
     * 本地表记录id（根据data_type区分业务，此id为处理成功数据在对应业务表id，第三方修改删除该记录时会用到）
     */
    private Long localRecordId;
    /**
     * 备注（可记录失败原因）
     */
    private String remark;

    /**
     * 校区id
     */
    private String campusId;
}