package com.hailiang.model.point.vo;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 积分类型（币种） 查询对象
 *
 * <AUTHOR> 2023-06-29 16:32:36
 */
@Data
public class PointTypeClassLimitVO {
    private static final long serialVersionUID = 1L;
    /**
     * 班级id
     */
    private Long classId;
    /**
     * 班级名称
     */
    private String className;
    /**
     * 年级id
     */
    private Long gradeId;
    /**
     * 年级名称
     */
    private String gradeName;
    /**
     * 节点code
     */
    private String orgCode;
    /**
     * 班级限制分
     */
    private BigDecimal classLimitPoint;
    /**
     * 是否系统默认分配
     */
    private Boolean isSystemDefault;
}