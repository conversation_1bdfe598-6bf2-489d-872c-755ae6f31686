package com.hailiang.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 争章活动任务规则详情-点评项内容
 *
 * <AUTHOR>
 * @since 2023-06-02 13:50:24
 */
@Data
@TableName(value = "medal_task_rule_target")
public class MedalTaskRuleTarget extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -43057787119116998L;

    /**
     * saas租户id
     */
    private String tenantId;
    /**
     * 学校id
     */
    private String schoolId;
    /**
     * 校区id
     */
    private String campusId;
    /**
     * 任务id
     */
    private Long medalTaskId;
    /**
     * 规则id
     */
    private Long medalTaskRuleId;
    /**
     * 规则名称
     */
    private String name;
    /**
     * 五育code
     */
    private Integer moduleCode;

    /**
     * 指标分组id
     */
    private Long groupId;

    /**
     * 指标id
     */
    private Long targetId;

    /**
     * 点评项id
     */
    private String optionId;

    /**
     * 选项名称
     */
    private String submitName;

    /**
     * 选择的最后一级类型 1:五育code 2:分组id 3:指标id 4:点评项id
     */
    private Integer submitType;

    /**
     * 选择的最后一级id
     */
    private String submitId;

    /**
     * 达成目标: 点评次数/点评总分
     */
    private BigDecimal targetValue;

}

