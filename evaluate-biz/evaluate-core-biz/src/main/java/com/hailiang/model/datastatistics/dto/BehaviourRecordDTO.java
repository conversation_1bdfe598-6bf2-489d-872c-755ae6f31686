package com.hailiang.model.datastatistics.dto;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/4/23 9:26
 */
@Data
public class BehaviourRecordDTO implements Serializable {

    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 学段id
     */
    private String campusSectionId;
    /**
     * 年级id
     */
    private String gradeId;

    /**
     * 年级code
     */
    private String gradeCode;

    /**
     * 班级id
     */
    private String classId;

    /**
     * 学生id
     */
    private String studentId;

    /**
     * 模块code：\n德育：1  \n智育：2\n体育：3\n美育：4\n劳育：5
     */
    private Integer moduleCode;

    /**
     * 指标id
     */
    private Long targetId;

    /**
     * 指标名称
     */
    private String targetName;


    /**
     * 指标评价模版mongo表id
     */
    private String templateId;

    /**
     * 题目id
     */
    private String titleId;

    /**
     * 选项id
     */
    private String optionId;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 指标评价提交模版mongo表id
     */
    private String infoId;

    /**
     * 分数统计类型 1:指标名称 2:选项名称
     */
    private Integer infoType;

    /**
     * 指标或选项名称
     */
    private String infoName;

    /**
     * 加分类型，“1”为加分，“2”为减分
     */
    private Integer scoreType;
    /**
     * 是否开启加分
     */
    private Boolean isScore;

    /**
     * 分数（可以有正负数，实际得分）
     */
    private BigDecimal score;

    /**
     * 分值 （分数的绝对值）
     */
    private BigDecimal scoreValue;

    /**
     * 推送状态  1：已推送    2：未推送    3：不推送
     */
    private Integer sendType;

    /**
     * 数据来源 0：综合素质评价 1：星动力 2：成绩管理
     */
    private Integer dataSource;

    /**
     * 表单提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date submitTime;

    /**
     * 评价人类型  1：老师  2：家长
     */
    private Integer appraisalType;

    /**
     * 评价人人id
     */
    private String appraisalId;

    /**
     * 评价人姓名
     */
    private String appraisalName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;
    public void setSubmitTime(Date startTime) {
        this.submitTime = DateUtil.parse(DateUtil.format(startTime,"yyyy-MM-dd"));
    }
}
