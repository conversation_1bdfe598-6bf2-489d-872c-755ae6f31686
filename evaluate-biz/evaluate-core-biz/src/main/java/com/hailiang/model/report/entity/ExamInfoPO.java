package com.hailiang.model.report.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考试信息
 *
 * @TableName report_exam_info
 */
@TableName(value = "report_exam_info")
@Data
public class ExamInfoPO extends BaseEntity implements Serializable {

    /**
     * 学业监测和成绩分析里的考试id（学业检测和成绩分析里存在两个不同的表，所以此ID可能存在重复）
     */
    @TableField(value = "source_id")
    private Long sourceId;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private String tenantId;

    /**
     * 学校代码
     */
    @TableField(value = "school_id")
    private String schoolId;

    /**
     * 学校名称
     */
    @TableField(value = "school_name")
    private String schoolName;

    /**
     * 校区id
     */
    @TableField(value = "campus_id")
    private String campusId;

    /**
     * 校区名称
     */
    @TableField(value = "campus_name")
    private String campusName;

    /**
     * 学段id
     */
    @TableField(value = "section_code")
    private String sectionCode;

    /**
     * 入学年份
     */
    @TableField(value = "start_year")
    private Integer startYear;

    /**
     * 年级code
     */
    @TableField(value = "grade_code")
    private String gradeCode;

    /**
     * 年级id
     */
    @TableField(value = "grade_id")
    private String gradeId;

    /**
     * 年级名称
     */
    @TableField(value = "grade_name")
    private String gradeName;

    /**
     * 行政班id
     */
    @TableField(value = "class_id")
    private String classId;

    /**
     * 班级名称
     */
    @TableField(value = "class_name")
    private String className;

    /**
     * 考试名称
     */
    @TableField(value = "exam_name")
    private String examName;

    /**
     * 考试类型
     */
    @TableField(value = "exam_type")
    private Integer examType;

    /**
     * 开始日期
     */
    @TableField(value = "start_date")
    private Date startDate;

    /**
     * 结束日期
     */
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 考试范围 0-校考 1-联考
     */
    @TableField(value = "exam_scope")
    private Integer examScope;

    /**
     * 发布状态，1：发布  0：未发布
     */
    @TableField(value = "issue_status")
    private Integer issueStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}