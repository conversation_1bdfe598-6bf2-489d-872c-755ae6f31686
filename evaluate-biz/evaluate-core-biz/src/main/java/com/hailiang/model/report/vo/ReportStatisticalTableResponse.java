package com.hailiang.model.report.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 报告统计表返回对象
 *
 * @Description: 报告统计表返回对象
 * @Author: Jovi
 * @Date: Created in 2024/5/15
 * @Version: 2.0.0
 */
@Data
public class ReportStatisticalTableResponse {

    /**
     * 教师ID
     */
    private String staffId;

    /**
     * 教师姓名
     */
    private String staffName;

    /**
     * 学段名称
     */
    private String campusSectionName;

    /**
     * 年级Id
     */
    private String gradeId;
    /**
     * 年级Code
     */
    private String gradeCode;
    /**
     * 年级名称
     */
    private String gradeName;

    /**
     * 班级ID
     */
    private String classId;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 科目名称
     */
    private String subjectName;

    /**
     * 过程性评价
     */
    private List<ReportStatisticalTableSubjectProcessResponse> processList;

    /**
     * 结果性评价
     */
    private List<ReportStatisticalTableSubjectResultResponse> resultList;
}
