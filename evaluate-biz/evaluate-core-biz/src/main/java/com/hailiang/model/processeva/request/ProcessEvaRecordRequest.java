package com.hailiang.model.processeva.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hailiang.mp.commonsource.api.BasePageQuery;
import com.hailiang.util.AssertUtil;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 过程性评价-明细列表查询参数
 * @Author: huyouting
 * @Date: Created in 2024-12-02
 * @Version: v2.1.0
 */
@Data
public class ProcessEvaRecordRequest extends BasePageQuery {

    /**
     * 学年
     */
    @NotBlank(message = "学校学年不能为空")
    private String schoolYear;

    /**
     * 学期名字
     */
    @NotBlank(message = "学年学期不能为空")
    private String termName;

    /**
     * 学段id
     */
    @NotBlank(message = "学段不能为空")
    private String campusSectionId;

    /**
     * 年级id
     */
    private String gradeId;

    /**
     * 班级id
     */
    private String classId;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 五育
     */
    private List<Integer> moduleCodes;

    /**
     * 指标id
     */
    private List<Long> targetIds;

    /**
     * 1 全部方式 2 卡片评价方式 3 手机/一体机点评
     */
    private Integer queryType;
}
