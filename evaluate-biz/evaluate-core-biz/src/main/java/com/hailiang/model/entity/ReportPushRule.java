package com.hailiang.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 报告推送规则表(ReportPushRule)实体类
 *
 * <AUTHOR>
 * @since 2023-01-13 11:59:51
 */
@Data
@TableName(value = "evaluate_report_push_rule")
public class ReportPushRule extends BaseEntity {

    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 学校id
     */
    private String schoolId;
    /**
     * 校区id
     */
    private String campusId;
    /**
     * 推送频次 每几周一次
     */
    private Integer pushFrequency;
    /**
     * 推送时间星期
     */
    private Integer pushWeek;
    /**
     * 推送时间 精确到时分秒
     */
    private String pushTime;
    /**
     * 推送模块 1:综合评价 2:综合素质 3:成长趋势 4:行为表现 5:智能评价 6:评分占比 7:明细 8：学生能力模型
     */
    private String pushModule;
    /**
     * 是否推送给家长 0 否 1 是
     */
    private Integer parentPushFlag;
    /**
     * 是否需要班主任审核 0 否 1 是
     */
    private Integer needReview;
    /**
     * 最近一次执行时间
     */
    private Date ruleTime;

    /**
     * 推送方式：1：hai 家校，2：钉钉，3：飞书
     */
    private Integer pushWay;

    /**
     * 点评记录是否显示点评人
     */
    private Integer showAppraisalFlag;
}

