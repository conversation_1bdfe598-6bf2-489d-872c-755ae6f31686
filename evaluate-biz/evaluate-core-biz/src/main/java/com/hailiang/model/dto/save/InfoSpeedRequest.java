package com.hailiang.model.dto.save;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 极速点评保存入参
 *
 * @Description: 极速点评保存入参
 * @Author: JiangJunLi
 * @Date: Created in 2024-01-25
 * @Version: 1.6.0
 */

@Data
public class InfoSpeedRequest {

    /**
     * 操作批量提交标识（0：否，1：全班，2：全年级，3：全学段）
     */
    @NotNull(message = "操作批量提交标识不能为空，请刷新后重新操作")
    private Integer isBatchFlag;

    /**
     * 积分板id
     */
    private String planId;

    /**
     * 班级id
     */
    @NotBlank(message = "班级id不能为空")
    private String classId;

    /**
     * 积分板类型  枚举：1|常规分组 2|不分组 3|师徒帮扶分组
     */
    private Integer planType;

    /**
     * 班级关联学科
     */
    private SubjectRelDTO subjectRelRequest;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 学生相关信息
     */
    @NotEmpty(message = "请选择要点评的学生")
    @Valid
    private List<InfoSpeedStudentRequest> infoSpeedStudentRequests;

    /**
     * 是否扫脸点评（0：否，1：是；默认 0）
     */
    @NotNull(message = "是否扫脸点评")
    private Integer faceFlag;

    /**
     * 学段id
     */
    @NotBlank(message = "学段id不能为空")
    private String campusSectionId;

    /**
     * 学段code
     */
    @NotBlank(message = "学段Code不能为空")
    private String campusSectionCode;

    /**
     * 年级id
     */
    @NotBlank(message = "年级id不能为空")
    private String gradeId;

    /**
     * 年级code
     */
    @NotBlank(message = "年级code不能为空")
    private String gradeCode;
}