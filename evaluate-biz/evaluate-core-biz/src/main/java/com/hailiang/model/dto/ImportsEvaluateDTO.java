package com.hailiang.model.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 导入处理中间转换数据类
 *
 * @Description: 导入处理中间转换数据类
 * @Author: Jovi
 * @Date: Created in 2024-12-05
 * @Version: 2.0.0
 */
@Data
public class ImportsEvaluateDTO {

    /**
     * 批量导入提交ID（一次提交生成唯一的ID）
     */
    private Long submitId;

    /**
     * 提交时间
     */
    private Date submitDate;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 校区id
     */
    private String campusId;

    /**
     * 学段id
     */
    private Long sectionId;

    /**
     * 学段名称
     */
    private String sectionName;

    /**
     * 学段code
     */
    private String sectionCode;

    /**
     * 年级id
     */
    private Long gradeId;

    /**
     * 年级Code
     */
    private String gradeCode;

    /**
     * 班级id列表
     */
    private List<Long> classIds;

//    /**
//     * 班级映射
//     */
//    private Map<String,Long> classNameAndIdMap;

    /**
     * 五育模块code
     */
    private Integer moduleCode;

    /**
     * 指标分组id
     */
    private Long targetGroupId;

    /**
     * 指标分组名称
     */
    private String targetGroupName;

    /**
     * 指标id
     */
    private Long targetId;
    /**
     * 指标名称
     */
    private String targetName;

    /**
     * 学科id
     */
    private String subjectId;

    /**
     * 学科名称
     */
    private String subjectName;

    /**
     * 学科code
     */
    private String subjectCode;

    /**
     * 评价人人id
     */
    private Long appraisalId;

    /**
     * 评价人人姓名
     */
    private String appraisalName;

    /**
     * 验证通过的提交内容
     */
    private String validatedSubmitContent;

    /**
     * 指标：是否开启分值不加入画像综合得分和点评记录中 0否 1是
     */
    private Integer targetNotPartCount;

    @Override
    public String toString() {
        return JSON.toJSONString(this, SerializerFeature.WriteMapNullValue);
    }

}
