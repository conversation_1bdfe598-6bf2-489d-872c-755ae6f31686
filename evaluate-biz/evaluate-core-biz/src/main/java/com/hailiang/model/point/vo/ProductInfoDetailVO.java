package com.hailiang.model.point.vo;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品信息表分页视图对象
 *
 * <AUTHOR> 2023-06-29 16:32:36
 */
@Data
public class ProductInfoDetailVO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;
      /**
     * 租户id
     */
//    private String tenantId;
      /**
     * 学校id
     */
//    private String schoolId;
      /**
     * 校区id
     */
//    private String campusId;
      /**
     * 商品名称
     */
    private String productName;
      /**
     * 商品类型 1：实体 2：虚拟
     */
    private String productType;
      /**
     * 需要的积分
     */
    private BigDecimal needPoint;
      /**
     * 是否开启兑换
     */
    private Integer exchangeFlag;
      /**
     * 商品描述
     */
//    private String productDescription;

    /**
     * 图片地址
     */
    private List<String> pictureUrl;
    /**
     * 总库存
     */
    private Integer stock;
    /**
     * 积分柜库存
     */
    private Integer stockCabinet;
    /**
     * 线下库存（线下可兑换）
     */
    private Integer stockOffline;
    /**
     * 已兑换
     */
    private Integer exchangeCount;
    /**
     * 总件数
     */
    private Integer sumCount;
  }