package com.hailiang.model.report.dto;

import lombok.Data;

/**
 * 查询学科综合成绩计算状态入参
 *
 * @Description: 查询学科综合成绩计算状态入参
 * @Author: Jovi
 * @Date: Created in 2024/12/30
 * @Version: 2.0.0
 */
@Data
public class ComprehensiveScoreCalculateResultStatusQuery {

    /**
     * 学年,例如 2023-2024
     */
    private String schoolYear;

    /**
     * 学期名称
     */
    private String termName;

    /**
     * 学段id
     */
    private String campusSectionId;

    /**
     * 学段code
     */
    private String campusSectionCode;

    /**
     * 年级id
     */
    private String gradeId;

    /**
     * 年级code
     */
    private String gradeCode;

    private Boolean isLogFlag;


}
