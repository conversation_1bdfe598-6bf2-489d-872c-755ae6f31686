package com.hailiang.model.report.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> hljy
 * @date 2023/8/29 10:46
 */
@Data
public class ReportExamBusinessMergeVO implements Serializable {

    private static final long serialVersionUID = 562708195523251811L;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 考试类型
     */
    private Integer examType;

    /**
     * 考试ID
     */
    private String examId;
}
