package com.hailiang.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR> gaoxin
 * @create 2023/8/3 14:35
 */

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CheckAwardRecord extends BaseDO {

    /**
     * 学年
     */
    private String schoolYear;

    /**
     * 学段ID
     */
    private String campusSectionId;

    /**
     * 学段CODE
     */
    private String campusSectionCode;

    /**
     * 颁发类型 {@link com.hailiang.enums.AwardTypeEnum}
     */
    private Integer awardType;

    /**
     * 初始分数
     */
    private Integer initialScore;

    /**
     * 考核周期起始时间
     */
    private Date startTime;

    /**
     * 考核周期结束时间
     */
    private Date endTime;

    /**
     * 是否通知班主任
     */
    private Boolean isNotifyTeacher;

    /**
     * 是否通知班排
     */
    private Boolean isNotifyClass;
}
