package com.hailiang.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

/**
 * DictData实体类
 *
 * <AUTHOR> 2023-03-22 14:30:40
 */
@Data
@TableName("sys_dict_data")
public class DictData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 字典类型
     */
    private String dictType;
      /**
     * 字典标签
     */
    private String dictLabel;
      /**
     * 字典值
     */
    private String dictValue;
      /**
     * 字典排序
     */
    private Integer dictSort;
      /**
     * 状态;是否停用
     */
    private String status;
      /**
     * 备注
     */
    private String remark;
  }
