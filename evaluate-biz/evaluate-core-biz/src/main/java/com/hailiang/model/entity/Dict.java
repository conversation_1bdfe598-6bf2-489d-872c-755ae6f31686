package com.hailiang.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;

/**
 * Dict实体类
 *
 * <AUTHOR> 2023-03-22 14:30:40
 */
@Data
@TableName("sys_dict")
public class Dict extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 字典名称
     */
    private String dictName;
      /**
     * 字典类型
     */
    private String dictType;
      /**
     * 状态;是否停用
     */
    private Integer status;
      /**
     * 备注
     */
    private String remark;
  }
