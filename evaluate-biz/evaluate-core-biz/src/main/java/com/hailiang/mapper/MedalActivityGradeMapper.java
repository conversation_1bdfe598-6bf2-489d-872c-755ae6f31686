package com.hailiang.mapper;

import com.hailiang.model.entity.MedalActivityGrade;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【medal_activity_grade(活动对应年级表)】的数据库操作Mapper
 * @createDate 2023-06-06 10:53:00
 * @Entity com.hailiang.model.entity.MedalActivityGrade
 */
public interface MedalActivityGradeMapper extends BaseMapper<MedalActivityGrade> {

    /**
     * 根据年级id查询活动id集合
     *
     * @param gradeId 年级id
     * @return
     */
    List<Long> listActivityIdsByGradeId(@Param("tenantId") String tenantId, @Param("schoolId") String schoolId, @Param("campusId") String campusId, @Param("gradeId") String gradeId);

    /**
     * 根据年级id查询活动id集合
     *
     * @param gradeId 年级id
     * @return
     */
    List<Long> listActivityIdsByGradeIdFix(@Param("tenantId") String tenantId, @Param("schoolId") String schoolId, @Param("campusId") String campusId, @Param("gradeId") String gradeId);

    /**
     * 根据年级id查询活动id集合(个人争章列表使用,需要获取进行中和已失效的活动)
     *
     * @param gradeId 年级id
     * @return
     */
    List<Long> listPersonalActivityByGradeId(@Param("tenantId") String tenantId, @Param("schoolId") String schoolId, @Param("campusId") String campusId, @Param("gradeId") String gradeId);


}




