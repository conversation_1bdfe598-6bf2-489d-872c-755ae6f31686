//package com.hailiang.mapper;
//
//
//import com.hailiang.model.dto.initialScore.InitialScoreGroupByClassDTO;
//import com.hailiang.model.dto.initialScore.InitialScoreGroupByClassQueryDTO;
//import org.apache.ibatis.annotations.Param;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
///**
// * 初始分-学生分配记录表(InitialScoreAllocation)表数据库访问层
// *
// * <AUTHOR>
// * @since 2023-09-18 17:13:05
// */
//@Repository
//public interface InitialScoreAllocationMapper extends BaseMapper<InitialScoreAllocation> {
//
//    /**
//     * 根据班级查询统计数据
//     */
//    List<InitialScoreGroupByClassDTO> queryCountByClass(@Param("queryDTO") InitialScoreGroupByClassQueryDTO queryDTO);
//}
//
