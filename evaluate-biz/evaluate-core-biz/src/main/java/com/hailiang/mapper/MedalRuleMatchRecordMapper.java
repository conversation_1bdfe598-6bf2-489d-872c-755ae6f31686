package com.hailiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.datastatistics.dto.MedalOperationRecordDTO;
import com.hailiang.model.entity.MedalRuleMatchRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/13 16:26
 */
public interface MedalRuleMatchRecordMapper extends BaseMapper<MedalRuleMatchRecord> {

    /**
     * 已排查Doris 获取规则下匹配的行为记录 获取近期的点评 暂时不用修改
     * 获取规则下匹配的行为记录
     *
     * @param medalRuleId       规则id
     * @param studentId         学生id
     * @return
     */
    List<BehaviourRecordDTO> listMatchRuleBehaviour(@Param("medalRuleId") Long medalRuleId, @Param("studentId") String studentId);

}
