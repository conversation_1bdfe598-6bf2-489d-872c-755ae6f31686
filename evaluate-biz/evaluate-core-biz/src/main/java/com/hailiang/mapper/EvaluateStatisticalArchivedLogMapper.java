package com.hailiang.mapper;

import com.hailiang.portrait.entity.EvaluateStatisticalArchivedLogPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 指标行为统计归档日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Mapper
public interface EvaluateStatisticalArchivedLogMapper extends BaseMapper<EvaluateStatisticalArchivedLogPO> {

    /**
     * 此处要求物理删除
     * 如果是逻辑删除，触发重试或重新计算产生多条删除记录会导致 un_biz_daily_del 唯一键冲突
     * @param id
     */
    void deleteById(@Param("id") Long id);
}
