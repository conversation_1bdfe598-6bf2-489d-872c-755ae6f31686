package com.hailiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.model.entity.EvaluateStudentAbilityModelItemConfigPO;
import com.hailiang.model.entity.EvaluateStudentAbilityModelItemPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EvaluateStudentAbilityModelItemConfigMapper extends BaseMapper<EvaluateStudentAbilityModelItemConfigPO> {

	/**
     * 新增，插入所有字段
     *
     * @param modelItemConfigPO 新增的记录
     * @return 返回影响行数
     */
	int insert(EvaluateStudentAbilityModelItemConfigPO modelItemConfigPO);
	

	/**
     * 修改，忽略null字段
     *
     * @param abilityModelItemConfigPO 修改的记录
     * @return 返回影响行数
     */
	int updateIgnoreNull(EvaluateStudentAbilityModelItemConfigPO abilityModelItemConfigPO);


	/**
	 * 根据能力项id获取能力项配置
	 * @param itemIdList
	 * @return
	 */
	List<EvaluateStudentAbilityModelItemConfigPO> queryItemConfigByItemIdList(@Param("abilityItemId") List<Long> itemIdList);

	/**
	 * 批量新增
	 * @param addList
	 * @return
	 */
	int insertBatch(@Param("addList") List<EvaluateStudentAbilityModelItemConfigPO> addList);
}