package com.hailiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.model.entity.EvaluateStudentAbilityModelItemPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EvaluateStudentAbilityModelItemMapper extends BaseMapper<EvaluateStudentAbilityModelItemPO> {

	/**
     * 新增，插入所有字段
     *
     * @param evaluateStudentAbilityModelItem 新增的记录
     * @return 返回影响行数
     */
	int insert(EvaluateStudentAbilityModelItemPO evaluateStudentAbilityModelItem);
	

	/**
     * 修改，忽略null字段
     *
     * @param evaluateStudentAbilityModelItem 修改的记录
     * @return 返回影响行数
     */
	int updateIgnoreNull(EvaluateStudentAbilityModelItemPO evaluateStudentAbilityModelItem);

	/**
	 * 根据模型id获取模型能力项
	 * @param modelId
	 * @return
	 */
	List<EvaluateStudentAbilityModelItemPO> queryByModelId(@Param("modelId") Long modelId);

    /**
     * 批量新增
     * @param itemPOList
     * @return
     */
	int insertBatch(@Param("itemPOList") List<EvaluateStudentAbilityModelItemPO> itemPOList);
}