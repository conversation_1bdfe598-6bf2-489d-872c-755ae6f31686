package com.hailiang.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hailiang.model.dto.query.TargetGroupListByConditionsDaoDTO;
import com.hailiang.model.dto.query.listAllEvaluateTargetDaoDTO;
import com.hailiang.model.entity.SysTargetGroup;
import com.hailiang.model.vo.ListAllEvaluateTargetVOModule;
import com.hailiang.model.vo.TargetGroupListByConditionsVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_target_group(公共指标分组表)】的数据库操作Mapper
* @createDate 2023-08-29 13:48:53
* @Entity generator.domain.SysTargetGroup
*/
public interface SysTargetGroupMapper extends BaseMapper<SysTargetGroup> {

    List<TargetGroupListByConditionsVO> listByConditions(TargetGroupListByConditionsDaoDTO daoDTO);

    List<ListAllEvaluateTargetVOModule> listAllEvaluateTarget(listAllEvaluateTargetDaoDTO query);

    List<ListAllEvaluateTargetVOModule> listParentAllEvaluateTarget(listAllEvaluateTargetDaoDTO query);
}




