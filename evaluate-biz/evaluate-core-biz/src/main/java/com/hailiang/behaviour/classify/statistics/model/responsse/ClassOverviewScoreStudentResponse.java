package com.hailiang.behaviour.classify.statistics.model.responsse;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 班级概况-积分统计-学生列表
 * @author: pan<PERSON><PERSON>
 * @create: 2024/9/4 14:46
 * @Version 1.0
 */
@Data
public class ClassOverviewScoreStudentResponse implements Serializable {

    private static final long serialVersionUID = 8960371861104402733L;
    /**
     * 序号
     */
    private String sequence;

    /**
     * 学生id
     */
    private Long studentId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 总分
     */
    private BigDecimal score;

    /**
     * 总分(excel展示用)
     */
    private String scoreShow;
    /**
     * 初始分
     */
    private BigDecimal initialScore;


    /**
     * 表扬总分
     */
    private BigDecimal praiseScore;

    /**
     * 表扬总分（excel展示用）
     */
    private String praiseScoreShow;

    /**
     * 改进总分
     */
    private BigDecimal improveScore;

    /**
     * 改进总分(excel展示用)
     */
    private String improveScoreShow;

    /**
     * 分类明细列表
     */
    private List<ClassOverviewScoreClassifyDetailResponse> classifyDetailList;
}