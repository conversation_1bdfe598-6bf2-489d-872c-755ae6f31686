package com.hailiang.behaviour.classify.statistics.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 类型排名
 * @description:
 * @author: pan<PERSON><PERSON>
 * @create: 2024/9/4 17:27
 * @Version 1.0
 */
@Data
public class TypeSortDTO implements Serializable {

    /**
     * 分类id
     */
    private String typeId;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 数量
     */
    private int count;

    /**
     * 序号
     */
    private int seqence;
}