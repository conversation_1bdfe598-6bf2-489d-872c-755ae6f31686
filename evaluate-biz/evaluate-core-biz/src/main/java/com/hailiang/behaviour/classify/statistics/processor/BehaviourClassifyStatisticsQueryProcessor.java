package com.hailiang.behaviour.classify.statistics.processor;

import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsCountDTO;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsRecordDTO;

import java.util.List;

/**
 * @description: 学生行为指标统计处理器
 * @author: pan<PERSON>an
 * @create: 2024/8/30 10:38
 * @Version 1.0
 */
public interface BehaviourClassifyStatisticsQueryProcessor {

    /**
     * 查询统计列表
     * @param statisticsRecordDTO
     * @return
     */
    List<BehaviourClassifyStatisticsCountDTO> statisticsList(BehaviourClassifyStatisticsRecordDTO statisticsRecordDTO);
}
