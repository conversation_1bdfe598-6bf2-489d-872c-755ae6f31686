package com.hailiang.behaviour.classify.statistics.model.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * @description: 班级概况请求参数
 * @author: pan<PERSON>an
 * @create: 2024/9/2 14:38
 * @Version 1.0
 */
@Data
public class ClassOverviewRequest implements Serializable {

    /**
     * 学校 id
     */
    @NotNull(message = "请选择学校")
    private Long schoolId;
    /**
     * 校区 id
     */
    @NotNull(message = "请选择校区")
    private Long campusId;
    /**
     * 班级 id
     */
    @NotNull(message = "请选择班级")
    private Long classId;
    /**
     * 开始时间
     */
    @NotNull(message = "请选择开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;
    /**
     * 结束时间
     */
    @NotNull(message = "请选择结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
}