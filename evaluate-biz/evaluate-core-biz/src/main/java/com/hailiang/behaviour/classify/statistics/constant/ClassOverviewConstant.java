package com.hailiang.behaviour.classify.statistics.constant;

/**
 * @description:
 * @author: pan<PERSON><PERSON>
 * @create: 2024/9/9 16:23
 * @Version 1.0
 */
public class ClassOverviewConstant {

    private ClassOverviewConstant(){

    }

    /**
     * 其他分类
     */
    public static final String OTHER_CLASSIFY_NAME = "其他";

    /**
     * 初始分分类名称
     */
    public static final String INITIAL_SCORE_CLASSIFY_NAME = "初始分";

    /**
     * 初始分点评人
     */
    public static final String SYSTEM_GENERATOR = "系统生成";

    /**
     * scoreType为1的分数类型名称
     */
    public static final String ONE_SCORE_TYPE_NAME = "表扬";

    /**
     * scoreType为2的分数类型名称
     */
    public static final String TWO_SCORE_TYPE_NAME = "改进";
}