package com.hailiang.behaviour.classify.statistics.model.responsse;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 班级概况-学生表现-积分统计
 * @author: pan<PERSON><PERSON>
 * @create: 2024/9/3 14:02
 * @Version 1.0
 */
@Data
public class ClassOverviewScoreResponsse implements Serializable {

    private static final long serialVersionUID = -8363120874558420849L;
    /**
     * 学生列表
     */
    private List<ClassOverviewScoreStudentResponse> studentList;
}