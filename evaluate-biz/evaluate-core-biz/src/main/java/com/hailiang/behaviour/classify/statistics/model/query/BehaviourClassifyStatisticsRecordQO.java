package com.hailiang.behaviour.classify.statistics.model.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: 学生行为指标统计查询实体
 * @author: pan<PERSON><PERSON>
 * @create: 2024/8/30 10:43
 * @Version 1.0
 */
@Data
public class BehaviourClassifyStatisticsRecordQO implements Serializable {

    /**
     * 班级id
     */
    private String classId;

    /**
     * 统计开始时间
     */
    private Date startTime;

    /**
     * 统计截止时间
     */
    private Date endTime;

    /**
     * 积分类型  1:加分，2:减分
     */
    private Integer scoreType;

    /**
     * 学生id
     */
    private String studentId;

    /**
     * 分类id
     */
    private Long classifyId;

    /**
     * 统计时间
     */
    private Date statisticsTime;

    /**
     * 学生id列表
     */
    private List<String> studentIds;
}