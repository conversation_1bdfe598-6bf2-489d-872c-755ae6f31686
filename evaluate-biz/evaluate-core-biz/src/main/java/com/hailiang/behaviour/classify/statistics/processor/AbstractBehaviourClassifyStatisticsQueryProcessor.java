package com.hailiang.behaviour.classify.statistics.processor;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Snowflake;
import com.google.common.collect.Lists;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsDTO;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsDeleteDTO;
import com.hailiang.behaviour.classify.statistics.model.entity.BehaviourStudentClassifyStatisticsPO;
import com.hailiang.behaviour.classify.statistics.model.query.BehaviourClassifyStatisticsConditionQO;
import com.hailiang.constant.Constant;
import com.hailiang.convert.BehaviourRecordConvert;
import com.hailiang.manager.BehaviourTargetStatisticsManager;
import com.hailiang.model.datastatistics.dto.BehaviourStudentClassifyStatisticsDTO;
import com.hailiang.portrait.entity.StaffDailyStatisticsPO;
import com.hailiang.util.SnowFlakeIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description: 学生行为指标分类统计抽象父类
 * @author: panjian
 * @create: 2024/9/2 09:02
 * @Version 1.0
 */
@Slf4j
public abstract class AbstractBehaviourClassifyStatisticsQueryProcessor implements BehaviourClassifyStatisticsProcessor {

    @Resource
    private BehaviourRecordConvert convert;
    @Resource
    protected BehaviourTargetStatisticsManager behaviourTargetStatisticsManager;

    @Override
    public Boolean statistics(BehaviourClassifyStatisticsDTO statisticsDTO) {
        Date statisticsDate = statisticsDTO.getStatisticsDate();
        TimeInterval start = DateUtil.timer();
        log.warn("开始执行学生行为指标分类统计");
        DateTime beginOfDay = DateUtil.beginOfDay(statisticsDate);
        DateTime endOfDay = DateUtil.endOfDay(statisticsDate);
        //重试&重算，逻辑删历史数据
        if (Objects.equals(Boolean.TRUE, statisticsDTO.getRetry())) {
            BehaviourClassifyStatisticsDeleteDTO deleteDTO = new BehaviourClassifyStatisticsDeleteDTO();
            deleteDTO.setStartDate(beginOfDay);
            deleteDTO.setEndDate(endOfDay);
            this.deleteStatistics(deleteDTO);
        }
        BehaviourClassifyStatisticsConditionQO statisticsConditionQO = new BehaviourClassifyStatisticsConditionQO();
        statisticsConditionQO.setStartTime(beginOfDay);
        statisticsConditionQO.setEndTime(endOfDay);
//        statisticsConditionQO.setUnusedDatasource(unusedDatasource());
        List<String> classIdList = listDistinctClassId(statisticsConditionQO);
        int batchSize = 5;
        log.warn("待执行学生行为指标分类统计班级总数:{}",classIdList.size());
        int statisticsCount = 0;
        List<List<String>> partition = Lists.partition(classIdList,batchSize);
        for (List<String> subClassIdList : partition) {
            statisticsConditionQO.setClassIdList(subClassIdList);
            //查询统计记录数
            List<BehaviourStudentClassifyStatisticsDTO> behaviourRecordDTOS = queryStatistics(statisticsConditionQO);
            if(CollectionUtils.isEmpty(behaviourRecordDTOS)){
                continue;
            }
            List<BehaviourStudentClassifyStatisticsPO> studentTargetStatisticsPOS = convert.toBehaviourStudentTargetStatistics(behaviourRecordDTOS);
            //处理统计记录数
            dealBehaviourStudentTargetStatisticsPO(studentTargetStatisticsPOS,beginOfDay);
            //批量保存统计记录数
            List<List<BehaviourStudentClassifyStatisticsPO>> studentClassifyStatisticsList = Lists.partition(studentTargetStatisticsPOS,batchSize);
            for (List<BehaviourStudentClassifyStatisticsPO> subList : studentClassifyStatisticsList) {
                behaviourTargetStatisticsManager.insertBatch(subList);
            }
            statisticsCount += studentTargetStatisticsPOS.size();
        }
        log.warn("学生行为指标分类统计执行完成，总归档记录数：{},耗时:{}",statisticsCount, start.interval());
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return Boolean.TRUE;
    }

    /**
     * 获取去重后要统计的班级ID
     * @param statisticsConditionQO 查询条件
     * @return
     */
    protected abstract List<String> listDistinctClassId(BehaviourClassifyStatisticsConditionQO statisticsConditionQO);


    /**
     * 处理学生指标统计数据
     * @param behaviourStudentTargetStatistics
     * @param statisticsDate
     */
    private void dealBehaviourStudentTargetStatisticsPO(List<BehaviourStudentClassifyStatisticsPO> behaviourStudentTargetStatistics, Date statisticsDate) {
        for (BehaviourStudentClassifyStatisticsPO behaviourStudentTargetStatistic : behaviourStudentTargetStatistics) {
            behaviourStudentTargetStatistic.setId(SnowFlakeIdUtil.nextId());
            behaviourStudentTargetStatistic.setStatisticsTime(statisticsDate);
            behaviourStudentTargetStatistic.setDeleted(Boolean.FALSE);
            behaviourStudentTargetStatistic.setUpdateBy(Constant.DEFAULT_CREATOR);
        }
    }
}