package com.hailiang.behaviour.classify.statistics.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hailiang.behaviour.classify.statistics.constant.ClassOverviewConstant;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsCountDTO;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsRecordDTO;
import com.hailiang.behaviour.classify.statistics.model.dto.PeriodTimeDTO;
import com.hailiang.behaviour.classify.statistics.model.query.BehaviourClassifyStatisticsConditionQO;
import com.hailiang.behaviour.classify.statistics.model.query.BehaviourClassifyStatisticsRecordQO;
import com.hailiang.behaviour.classify.statistics.model.request.ClassOverviewRequest;
import com.hailiang.behaviour.classify.statistics.model.request.ClassOverviewScoreDetailRequest;
import com.hailiang.behaviour.classify.statistics.model.request.ClassOverviewScoreRequest;
import com.hailiang.behaviour.classify.statistics.model.responsse.*;
import com.hailiang.behaviour.classify.statistics.processor.DelegateBehaviourClassifyStatisticsProcessor;
import com.hailiang.behaviour.classify.statistics.service.ClassOverviewService;
import com.hailiang.constant.Constant;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.enums.ModuleEnum;
import com.hailiang.exception.BizException;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.logic.TermLogic;
import com.hailiang.manager.EvaluateHelpBehaviourRecordManager;
import com.hailiang.manager.InitialScoreManager;
import com.hailiang.model.datastatistics.dto.BehaviourStudentClassifyStatisticsDTO;
import com.hailiang.model.datastatistics.dto.ClassOverviewScoreDetailDTO;
import com.hailiang.model.dto.initialScore.InitialScoreGroupByClassQueryDTO;
import com.hailiang.model.entity.InitialScorePO;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.vo.educational.EduStudentInfoVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.saas.SaasClassManager;
import com.hailiang.saas.model.vo.educational.TchClassOpenV2VO;
import com.hailiang.service.BasicInfoService;
import com.hailiang.util.NumUtil;
import com.hailiang.util.PageConvert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 班级概况service
 * @author: panjian
 * @create: 2024/9/2 14:40
 * @Version 1.0
 */
@Slf4j
@Service
public class ClassOverviewServiceImpl implements ClassOverviewService {

    @Resource(name = "delegateBehaviourClassifyStatisticsProcessor")
    private DelegateBehaviourClassifyStatisticsProcessor statisticsProcessor;
    @Resource
    private TermLogic termLogic;
    @Resource
    private BasicInfoService basicInfoService;
    @Resource
    private SaasClassManager saasClassManager;
    @Resource
    private BehaviourRecordManager behaviourRecordManager;
    @Resource
    private EvaluateHelpBehaviourRecordManager helpBehaviourRecordManager;
    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private InitialScoreManager initialScoreManager;

    @Override
    public ClassOverviewResponse classOverview(ClassOverviewRequest classOverviewRequest) {
        log.warn("统计班级概况当前查询参数:{}", JSON.toJSONString(classOverviewRequest));
        //构建查询参数
        BehaviourClassifyStatisticsRecordDTO behaviourClassifyStatisticsRecordDTO = BehaviourClassifyStatisticsRecordDTO.convert(classOverviewRequest);
        //检查请求参数并且获取上周期时间
        PeriodTimeDTO priorPeriodTimeDTO = checkRequestAndGetPriorPeriodDTO(behaviourClassifyStatisticsRecordDTO, Boolean.TRUE);
        ClassOverviewResponse classOverviewResponse = new ClassOverviewResponse();
        //获取当前班级所有学生
        Map<Long, EduStudentInfoVO> currentTermStudentMap = getCurrentTermStudentMap(behaviourClassifyStatisticsRecordDTO);
        if (currentTermStudentMap.isEmpty()) {
            return null;
        }
        //计算当前周期班级概况统计数据
        calcNowPeriodClassOverview(behaviourClassifyStatisticsRecordDTO, classOverviewResponse, currentTermStudentMap, Boolean.TRUE);
        if (Boolean.FALSE.equals(classOverviewResponse.getIsExistPointRecord())) {
            //不存在当前周期数据
            return null;
        }
        //计算上周期班级概况统计数据
        ClassOverviewResponse priorPeriodClassOverviewResponse = calcPriorPeriodClassOverview(behaviourClassifyStatisticsRecordDTO, priorPeriodTimeDTO, currentTermStudentMap);
        //填充较上周期对比数据
        return comparePriorPeriod(classOverviewResponse, priorPeriodClassOverviewResponse);
    }

    @Override
    public ClassOverviewScoreResponsse classScoreOverview(ClassOverviewScoreRequest request) {
        //构建查询参数
        BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO = BehaviourClassifyStatisticsRecordDTO.convert(request);
        //检查请求参数
        checkRequestAndGetPriorPeriodDTO(classifyStatisticsRecordDTO, Boolean.FALSE);
        //获取当前班级所有学生
        Map<Long, EduStudentInfoVO> currentTermStudentMap = getCurrentTermStudentMap(classifyStatisticsRecordDTO);
        if (currentTermStudentMap.isEmpty()) {
            ClassOverviewScoreResponsse responsse = new ClassOverviewScoreResponsse();
            responsse.setStudentList(Collections.emptyList());
            return responsse;
        }

        TermVo currentTerm = this.getCurrentTerm(request);
        InitialScorePO oneByBaseInfo = new InitialScorePO();
        if (BeanUtil.isEmpty(currentTerm)) {
            oneByBaseInfo.setInitialScore(BigDecimal.ZERO);
        } else {
            //初始分
            oneByBaseInfo = initialScoreManager.getOneByBaseInfo(
                    Convert.toStr(request.getSchoolId()),
                    Convert.toStr(request.getCampusId()),
                    currentTerm.getSchoolYear(),
                    currentTerm.getTermName(),
                    ModuleEnum.OTHER.getCode()
            );
        }

        //获取当前周期分类统计记录
        List<BehaviourClassifyStatisticsCountDTO> nowPeroidStatisticsCountList = getNowPeroidStatisticsCountList(classifyStatisticsRecordDTO);
        //构建班级概况-积分统计返回值
        return buildClassOverviewScoreResponse(nowPeroidStatisticsCountList, currentTermStudentMap, oneByBaseInfo);
    }

    private TermVo getCurrentTerm(ClassOverviewScoreRequest request) {
        List<TchClassOpenV2VO> tchClassOpenV2VOS = saasClassManager
                .queryClassByIds(Convert.toList(String.class, request.getClassId()));
        if (CollUtil.isEmpty(tchClassOpenV2VOS) || ObjectUtil.isNull(tchClassOpenV2VOS.get(0).getCampusSectionId())) {
            log.warn("【从saas获取班级信息】返回结果空，入参：{}", JSONUtil.toJsonStr(request));
            return null;
        }

        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(Convert.toLong(request.getSchoolId()));
        termQuery.setCampusId(Convert.toLong(request.getCampusId()));
        termQuery.setCampusSectionId(Convert.toStr(tchClassOpenV2VOS.get(0).getCampusSectionId()));
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        if (CollUtil.isEmpty(termVos)) {
            log.warn("【从saas获取学期时间】返回结果空，入参：{}", JSONUtil.toJsonStr(request));
            return null;
        }

        //当前学期
        List<TermVo> currentTerm = termVos.stream().filter(TermVo::isCurrentTerm).collect(Collectors.toList());
        if (CollUtil.isEmpty(currentTerm)) {
            log.warn("【从saas获取学期时间】没有当前学期，返回结果空，入参：{}", JSONUtil.toJsonStr(request));
            return null;
        }
        return currentTerm.get(Constant.ZERO);
    }


    @Override
    public Page<ClassOverviewScoreDetailResponse> classScoreDetailOverview(ClassOverviewScoreDetailRequest request) {
        //构建查询参数
        BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO = BehaviourClassifyStatisticsRecordDTO.convert(request);
        //检查请求参数并且获取上周期时间
        checkRequestAndGetPriorPeriodDTO(classifyStatisticsRecordDTO, Boolean.FALSE);
        Page<ClassOverviewScoreDetailDTO> page = new Page<>(request.getPageNum(), request.getPageSize());
        //获取当前班级所有学生
        Map<Long, EduStudentInfoVO> currentTermStudentMap = getCurrentTermStudentMap(classifyStatisticsRecordDTO);
        if (currentTermStudentMap.isEmpty()) {
            return PageConvert.emptyPage(page.getCurrent(), page.getSize(), ClassOverviewScoreDetailResponse.class);
        }
        BehaviourClassifyStatisticsConditionQO statisticsConditionQO = new BehaviourClassifyStatisticsConditionQO();
        statisticsConditionQO.setStartTime(classifyStatisticsRecordDTO.getStartTime());
        statisticsConditionQO.setEndTime(classifyStatisticsRecordDTO.getEndTime());
        statisticsConditionQO.setUnusedDatasource(statisticsProcessor.unusedDatasource());
        statisticsConditionQO.setClassId(classifyStatisticsRecordDTO.getClassId().toString());
        statisticsConditionQO.setCampusId(request.getCampusId() + "");
        //分页查询积分明细
        Page<ClassOverviewScoreDetailDTO> classOverviewScoreDetailDTOPage = behaviourRecordManager.listClassOverviewScoreDetail(page, statisticsConditionQO);
        //构建返回值
        return buildScoreDetailOverviewResponse(classOverviewScoreDetailDTOPage, classifyStatisticsRecordDTO, currentTermStudentMap);
    }

    /**
     * 构建班级概况-积分明细下载返回值
     */
    private Page<ClassOverviewScoreDetailResponse> buildScoreDetailOverviewResponse(Page<ClassOverviewScoreDetailDTO> classOverviewScoreDetailDTOPage,
                                                                                    BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO,
                                                                                    Map<Long, EduStudentInfoVO> currentTermStudentMap) {
        List<ClassOverviewScoreDetailDTO> records = classOverviewScoreDetailDTOPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageConvert.emptyPage(classOverviewScoreDetailDTOPage.getCurrent(), classOverviewScoreDetailDTOPage.getSize(), ClassOverviewScoreDetailResponse.class);
        }
        List<ClassOverviewScoreDetailResponse> scoreDetailResponsesList = new ArrayList<>();
        for (ClassOverviewScoreDetailDTO record : records) {
            //判断当前学生是否还在该班级
            EduStudentInfoVO studentInfoVO = currentTermStudentMap.get(Long.valueOf(record.getStudentId()));
            if (Objects.isNull(studentInfoVO)) {
                continue;
            }
            //构建返回值
            ClassOverviewScoreDetailResponse classOverviewScoreDetailResponse = new ClassOverviewScoreDetailResponse();
            classOverviewScoreDetailResponse.setScore(NumUtil.formatFloatNumber(record.getScore()));
            classOverviewScoreDetailResponse.setScoreType(record.getScoreType());
            classOverviewScoreDetailResponse.setScoreTypeName(Objects.equals(record.getScoreType(), Constant.ONE) ?
                    ClassOverviewConstant.ONE_SCORE_TYPE_NAME : ClassOverviewConstant.TWO_SCORE_TYPE_NAME);
            classOverviewScoreDetailResponse.setClassifyName(record.getClassifyName());
            String appraisalName = record.getAppraisalName();
            if (StringUtils.isBlank(appraisalName) || StringUtils.equals(appraisalName, Constant.DEFAULT_CREATOR)) {
                classOverviewScoreDetailResponse.setAppraisalName(ClassOverviewConstant.SYSTEM_GENERATOR);
            } else {
                classOverviewScoreDetailResponse.setAppraisalName(appraisalName);
            }
            classOverviewScoreDetailResponse.setName(studentInfoVO.getStudentName());
            classOverviewScoreDetailResponse.setSubmitTime(record.getSubmitTime());
            //处理点评项名称
            dealClassifyDetailName(record, classOverviewScoreDetailResponse);
            scoreDetailResponsesList.add(classOverviewScoreDetailResponse);
        }
        //如果是最后一页，则加入初始分
        // TODO: 2024/11/8 初始分重新设计，明细无需加入初始分，暂时注释
//        addInitialScore(classOverviewScoreDetailDTOPage, classifyStatisticsRecordDTO, currentTermStudentMap, scoreDetailResponsesList);
        return PageConvert.copyPage(classOverviewScoreDetailDTOPage, ClassOverviewScoreDetailResponse.class, scoreDetailResponsesList);
    }

    /**
     * 处理点评项名称
     */
    private void dealClassifyDetailName(ClassOverviewScoreDetailDTO record, ClassOverviewScoreDetailResponse classOverviewScoreDetailResponse) {
        //德育活动名称特殊处理
        if (Objects.equals(record.getDataSource(), DataSourceEnum.MORAL_CAMPUS.getCode())
                || Objects.equals(record.getDataSource(), DataSourceEnum.MORAL_OFF_CAMPUS.getCode())) {
            ModuleEnum moduleEnum = ModuleEnum.parseByCode(record.getModuleCode());
            String moduleName = Objects.isNull(moduleEnum) ? null : moduleEnum.getMessage();
            classOverviewScoreDetailResponse.setClassifyDetailName(record.getTargetName() + moduleName + record.getInfoName());
        } else {
            classOverviewScoreDetailResponse.setClassifyDetailName(record.getInfoName());
        }
    }

//
//    TODO 初始分改版注释
//    /**
//     * 加入初始分
//     */
//    private void addInitialScore(Page<ClassOverviewScoreDetailDTO> classOverviewScoreDetailDTOPage, BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO, Map<Long, EduStudentInfoVO> currentTermStudentMap, List<ClassOverviewScoreDetailResponse> scoreDetailResponsesList) {
//        if (classOverviewScoreDetailDTOPage.getCurrent() == classOverviewScoreDetailDTOPage.getPages()) {
//            List<InitialScoreAllocation> initialScoreAllocations = initialScoreAllocationManager.listInitialScore(classifyStatisticsRecordDTO.getSchoolId(), classifyStatisticsRecordDTO.getCampusId(), classifyStatisticsRecordDTO.getClassId(), classifyStatisticsRecordDTO.getStartTime(), classifyStatisticsRecordDTO.getEndTime());
//            for (InitialScoreAllocation initialScoreAllocation : initialScoreAllocations) {
//                //判断当前学生是否还在该班级
//                EduStudentInfoVO studentInfoVO = currentTermStudentMap.get(Long.valueOf(initialScoreAllocation.getStudentId()));
//                if (Objects.isNull(studentInfoVO)) {
//                    continue;
//                }
//                ClassOverviewScoreDetailResponse classOverviewScoreDetailResponse = new ClassOverviewScoreDetailResponse();
//                classOverviewScoreDetailResponse.setName(studentInfoVO.getStudentName());
//                classOverviewScoreDetailResponse.setScore(initialScoreAllocation.getInitialScore());
//                classOverviewScoreDetailResponse.setScoreType(initialScoreAllocation.getScoreType());
//                classOverviewScoreDetailResponse.setClassifyName(ClassOverviewConstant.INITIAL_SCORE_CLASSIFY_NAME);
//                classOverviewScoreDetailResponse.setClassifyDetailName(ClassOverviewConstant.INITIAL_SCORE_CLASSIFY_NAME);
//                classOverviewScoreDetailResponse.setSubmitTime(initialScoreAllocation.getCreateTime());
//                classOverviewScoreDetailResponse.setScoreTypeName(Objects.equals(initialScoreAllocation.getScoreType(), Constant.ONE) ?
//                        ClassOverviewConstant.ONE_SCORE_TYPE_NAME : ClassOverviewConstant.TWO_SCORE_TYPE_NAME);
//                classOverviewScoreDetailResponse.setAppraisalName(ClassOverviewConstant.SYSTEM_GENERATOR);
//                scoreDetailResponsesList.add(classOverviewScoreDetailResponse);
//            }
//        }
//    }

    /**
     * 构建班级概况-积分统计返回值
     *
     * @param nowPeroidStatisticsCountList 当前周期分类统计记录
     * @return ClassOverviewScoreResponsse
     */
    private ClassOverviewScoreResponsse buildClassOverviewScoreResponse(
            List<BehaviourClassifyStatisticsCountDTO> nowPeroidStatisticsCountList,
            Map<Long, EduStudentInfoVO> currentTermStudentMap,
            InitialScorePO oneByBaseInfo) {
        //学生-学生记录map
        Map<Long/*studentId*/, ClassOverviewScoreStudentResponse> studentMap = new HashMap<>();
        //学生-学生分类明细map
        //2024-09-24 根据分类名称分组
        Map<Long/*studentId*/, Map<String/*分类名称*/, ClassOverviewScoreClassifyDetailResponse>> studentClassifyMap = new HashMap<>();
        //存在积分记录的学生
        Set<Long> existScoreStudentSet = new HashSet<>();
        //处理统计记录中的分类信息
        for (BehaviourClassifyStatisticsCountDTO classifyStatisticsCountDTO : nowPeroidStatisticsCountList) {
            //处理学生行为统计记录
            dealBehaviourStatisticsStudentCount(currentTermStudentMap, studentMap, studentClassifyMap, existScoreStudentSet, classifyStatisticsCountDTO, oneByBaseInfo);
        }
        //处理学生排序
        List<ClassOverviewScoreStudentResponse> studentResponses = sortStudentScore(studentMap, studentClassifyMap, oneByBaseInfo);
        for (EduStudentInfoVO studentInfoVO : currentTermStudentMap.values()) {
            if (existScoreStudentSet.contains(studentInfoVO.getId())) {
                //学生存在，跳过
                continue;
            }
            studentResponses.add(buildDefaultStudentResponse(studentInfoVO, oneByBaseInfo));
        }
        ClassOverviewScoreResponsse responsse = new ClassOverviewScoreResponsse();
        responsse.setStudentList(studentResponses);
        return responsse;
    }

    /**
     * 处理学生行为统计记录
     *
     * @param currentTermStudentMap      当前存在学生
     * @param studentMap                 学生-学生记录map
     * @param studentTypeMap             学生-学生分类明细map
     * @param existScoreStudentSet       存在行为记录的学生
     * @param classifyStatisticsCountDTO 行为记录类目统计数据
     */
    private static void dealBehaviourStatisticsStudentCount(Map<Long, EduStudentInfoVO> currentTermStudentMap,
                                                            Map<Long, ClassOverviewScoreStudentResponse> studentMap,
                                                            Map<Long, Map<String, ClassOverviewScoreClassifyDetailResponse>> studentTypeMap,
                                                            Set<Long> existScoreStudentSet, BehaviourClassifyStatisticsCountDTO classifyStatisticsCountDTO,
                                                            InitialScorePO oneByBaseInfo) {
        Long studentId = Long.valueOf(classifyStatisticsCountDTO.getStudentId());
        if (!currentTermStudentMap.containsKey(studentId)) {
            //当前学生目录中不包含该学生
            //直接跳过
            return;
        }
        EduStudentInfoVO studentInfoVO = currentTermStudentMap.get(studentId);
        existScoreStudentSet.add(studentId);
        ClassOverviewScoreStudentResponse studentResponsse = studentMap.computeIfAbsent(studentId, key -> buildDefaultStudentResponse(studentInfoVO,oneByBaseInfo));
        Integer scoreType = classifyStatisticsCountDTO.getScoreType();
        BigDecimal scoreSum = classifyStatisticsCountDTO.getScoreSum();
        if (Objects.equals(scoreType, Constant.ONE)) {
            //加分
            BigDecimal pariseScore = studentResponsse.getPraiseScore();
            if (Objects.isNull(pariseScore)) {
                pariseScore = new BigDecimal(Constant.ZERO);
            }
            pariseScore = pariseScore.add(scoreSum);
            studentResponsse.setPraiseScore(pariseScore);
        } else if (Objects.equals(scoreType, Constant.TWO)) {
            //减分
            BigDecimal improveScore = studentResponsse.getImproveScore();
            if (Objects.isNull(improveScore)) {
                improveScore = new BigDecimal(Constant.ZERO);
            }
            scoreSum = scoreSum.multiply(new BigDecimal(-1));
            improveScore = scoreSum.add(improveScore);
            studentResponsse.setImproveScore(improveScore);
        }
        Long classifyId = classifyStatisticsCountDTO.getClassifyId();
        String classifyName = classifyStatisticsCountDTO.getClassifyName();
        //计算学生分类明细
        Map<String, ClassOverviewScoreClassifyDetailResponse> classifyDetailMap = studentTypeMap.computeIfAbsent(studentId, key -> new HashMap<>());
        ClassOverviewScoreClassifyDetailResponse typeDetail = classifyDetailMap.computeIfAbsent(classifyName, key -> {
            ClassOverviewScoreClassifyDetailResponse temp = new ClassOverviewScoreClassifyDetailResponse();
            temp.setScore(new BigDecimal(Constant.ZERO));
            temp.setClassifyId(classifyId);
            temp.setClassifyName(classifyName);
            return temp;
        });
        typeDetail.setScore(typeDetail.getScore().add(scoreSum));
    }

    /**
     * 处理学生排序和点评分类
     *
     * @param studentMap     学生-学生记录map
     * @param studentTypeMap 学生-学生分类明细map
     * @return 学生积分统计排序后的结果
     */
    private List<ClassOverviewScoreStudentResponse> sortStudentScore(Map<Long, ClassOverviewScoreStudentResponse> studentMap,
                                                                     Map<Long, Map<String, ClassOverviewScoreClassifyDetailResponse>> studentTypeMap,
                                                                     InitialScorePO oneByBaseInfo) {
        BigDecimal initialScore = BigDecimal.ZERO;
        if(BeanUtil.isNotEmpty(oneByBaseInfo) && ObjectUtil.isNotNull(oneByBaseInfo.getInitialScore())){
            initialScore = oneByBaseInfo.getInitialScore();
        }
        int seqence;
        List<ClassOverviewScoreStudentResponse> studentResponses = new ArrayList<>(studentMap.values());
        for (ClassOverviewScoreStudentResponse studentResponse : studentResponses) {
            //计算总分,四舍五入得分
            BigDecimal praiseScore = NumUtil.formatFloatNumber(studentResponse.getPraiseScore());
            BigDecimal improveScore = NumUtil.formatFloatNumber(studentResponse.getImproveScore());
            BigDecimal sumScore = praiseScore.add(improveScore);
            studentResponse.setScore(sumScore.add(initialScore));
            studentResponse.setScoreShow(studentResponse.getScore().add(initialScore).toString());
            studentResponse.setInitialScore(initialScore);
            studentResponse.setImproveScore(improveScore);
            studentResponse.setImproveScoreShow(studentResponse.getImproveScore().toString());
            studentResponse.setPraiseScore(praiseScore);
            studentResponse.setPraiseScoreShow(studentResponse.getPraiseScore().toString());
            Map<String, ClassOverviewScoreClassifyDetailResponse> classifyDetailMap = studentTypeMap.get(studentResponse.getStudentId());
            List<ClassOverviewScoreClassifyDetailResponse> classifyDetailList = new ArrayList<>(classifyDetailMap.values());
            for (ClassOverviewScoreClassifyDetailResponse classifyDetail : classifyDetailList) {
                classifyDetail.setScore(NumUtil.formatFloatNumber(classifyDetail.getScore()));
                classifyDetail.setScoreShow(classifyDetail.getScore().toString());
            }
            studentResponse.setClassifyDetailList(classifyDetailList);
        }
        if (CollectionUtils.isNotEmpty(studentResponses)) {
            //学生排序
            studentResponses.sort((a, b) -> {
                int compare = b.getScore().compareTo(a.getScore());
                if (compare == 0) {
                    return b.getName().compareTo(a.getName());
                }
                return compare;
            });
            //计算不存在名次的学生
            seqence = 1;
            int index = 1;
            BigDecimal preScore = studentResponses.get(0).getScore();
            for (ClassOverviewScoreStudentResponse studentResponse : studentResponses) {
                if (preScore.compareTo(studentResponse.getScore()) == 0) {
                    //两者相等
                    studentResponse.setSequence(String.valueOf(seqence));
                    index++;
                    continue;
                }
                //不相等
                seqence = index;
                studentResponse.setSequence(String.valueOf(seqence));
                index++;
                preScore = studentResponse.getScore();
            }
        }
        return studentResponses;
    }

    /**
     * 构建默认学生记录返回值
     *
     * @param studentInfoVO 学生信息
     * @return 默认学生记录
     */
    private static ClassOverviewScoreStudentResponse buildDefaultStudentResponse(EduStudentInfoVO studentInfoVO, InitialScorePO oneByBaseInfo) {
        BigDecimal initialScore = BigDecimal.ZERO;
        if(BeanUtil.isNotEmpty(oneByBaseInfo) && ObjectUtil.isNotNull(oneByBaseInfo.getInitialScore())){
            initialScore = oneByBaseInfo.getInitialScore();
        }
        ClassOverviewScoreStudentResponse studentResponse = new ClassOverviewScoreStudentResponse();
        studentResponse.setPraiseScore(new BigDecimal(Constant.ZERO));
        studentResponse.setPraiseScoreShow(Constant.HYPHEN);
        studentResponse.setImproveScore(new BigDecimal(Constant.ZERO));
        studentResponse.setImproveScoreShow(Constant.HYPHEN);
        studentResponse.setScore(initialScore);
        studentResponse.setScoreShow(initialScore.compareTo(BigDecimal.ZERO) == 0 ? Constant.HYPHEN : initialScore.toString());
        studentResponse.setInitialScore(initialScore);
        studentResponse.setStudentId(studentInfoVO.getId());
        studentResponse.setSequence(Constant.HYPHEN);
        studentResponse.setName(studentInfoVO.getStudentName());
        studentResponse.setClassifyDetailList(Collections.emptyList());
        return studentResponse;
    }

    /**
     * 计算当前周期班级概况统计数据
     *
     * @param classifyStatisticsCountDTO 统计参数
     * @param classOverviewResponse      返回值
     * @param currentTermStudentMap      当前学期学生map
     */
    private void calcNowPeriodClassOverview(BehaviourClassifyStatisticsRecordDTO classifyStatisticsCountDTO,
                                            ClassOverviewResponse classOverviewResponse, Map<Long, EduStudentInfoVO> currentTermStudentMap,
                                            Boolean calcNoExistStudent) {
        //获取当前周期分类统计记录
        List<BehaviourClassifyStatisticsCountDTO> classifyStatisticsCountDTOS = getNowPeroidStatisticsCountList(classifyStatisticsCountDTO, Boolean.FALSE);
        //处理当前周期统计数据
        dealNowPeriodStatistics(classifyStatisticsCountDTOS, currentTermStudentMap, classOverviewResponse, calcNoExistStudent);
        log.info("处理当前周期内数据后:{}", classifyStatisticsCountDTOS.size());
        if (CollectionUtils.isEmpty(classifyStatisticsCountDTOS)) {
            classOverviewResponse.setIsExistPointRecord(Boolean.FALSE);
            return;
        }
        classOverviewResponse.setIsExistPointRecord(Boolean.TRUE);
        //计算百分比
        calcRecordPercentRatio(classOverviewResponse);
    }


    @Override
    public List<BehaviourClassifyStatisticsCountDTO> getNowPeroidStatisticsCountList(BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO) {
        return getNowPeroidStatisticsCountList(classifyStatisticsRecordDTO, Boolean.TRUE);
    }

    /**
     * 获取当前周期分类统计记录
     *
     * @param classifyStatisticsRecordDTO 学生指标统计记录
     * @return 学生指标统计记录
     */
    public List<BehaviourClassifyStatisticsCountDTO> getNowPeroidStatisticsCountList(BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO, boolean filterScoreNull) {
        TimeInterval begin = DateUtil.timer();
        //获取周期内T+1统计数据
        List<BehaviourClassifyStatisticsCountDTO> classifyStatisticsCountDTOS = statisticsProcessor.statisticsList(classifyStatisticsRecordDTO);
        log.info("获取当前周期内数据:{},耗时:{}", classifyStatisticsCountDTOS.size(), begin.interval());
        begin.restart();
        //填充师徒帮扶统计数据
        fillHelpBehaviourStatistics(classifyStatisticsRecordDTO, classifyStatisticsCountDTOS);
        log.info("填充当前周期内帮扶数据之后:{},耗时:{}", classifyStatisticsCountDTOS.size(), begin.interval());
//        begin.restart();
        //填充初始分统计数据
        // TODO: 2024/11/8 初始分重新设计，无需加入初始分，暂时注释
//        fillInitialScoreStatistics(classifyStatisticsRecordDTO, classifyStatisticsCountDTOS);
//        log.info("填充当前周期内初始分数据之后:{},耗时:{}", classifyStatisticsCountDTOS.size(), begin.interval());
        //填充今日统计数据
        begin.restart();
        fillTodayStatistics(classifyStatisticsRecordDTO, classifyStatisticsCountDTOS);
        log.info("填充今日统计数据之后:{},耗时:{}", classifyStatisticsCountDTOS.size(), begin.interval());
        if (filterScoreNull) {
            //过滤分值为null的数据
            classifyStatisticsCountDTOS.removeIf(next -> Objects.isNull(next.getScoreSum()));
        }
        return classifyStatisticsCountDTOS;
    }

    /**
     * 填充师徒帮扶统计数据
     *
     * @param classifyStatisticsRecordDTO
     * @param classifyStatisticsCountDTOS
     */
    private void fillHelpBehaviourStatistics(BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO, List<BehaviourClassifyStatisticsCountDTO> classifyStatisticsCountDTOS) {
        BehaviourClassifyStatisticsRecordQO statisticsRecordQO = new BehaviourClassifyStatisticsRecordQO();
        statisticsRecordQO.setClassId(classifyStatisticsRecordDTO.getClassId().toString());
        statisticsRecordQO.setStartTime(classifyStatisticsRecordDTO.getStartTime());
        statisticsRecordQO.setEndTime(classifyStatisticsRecordDTO.getEndTime());
        List<BehaviourClassifyStatisticsCountDTO> behaviourClassifyStatisticsCountDTOS = helpBehaviourRecordManager.queryStatisticsCountFromDoris(statisticsRecordQO);
        if (CollectionUtils.isNotEmpty(behaviourClassifyStatisticsCountDTOS)) {
            classifyStatisticsCountDTOS.addAll(behaviourClassifyStatisticsCountDTOS);
        }
    }

//   TODO 初始分改版注释
//
//    /**
//     * 填充初始分统计数据
//     *
//     * @param classifyStatisticsRecordDTO 行为指标统计查询条件
//     * @param classifyStatisticsCountDTOS 行为指标分类统计记录
//     */
//    private void fillInitialScoreStatistics(BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO, List<BehaviourClassifyStatisticsCountDTO> classifyStatisticsCountDTOS) {
//        //构建查询参数
//        InitialScoreGroupByClassQueryDTO initialScoreGroupByClassQueryDTO = buildInitialScoreQuery(classifyStatisticsRecordDTO);
//        List<InitialScoreGroupByClassDTO> initialScoreGroupByClassDTOS = initialScoreAllocationManager.queryCountByClass(initialScoreGroupByClassQueryDTO);
//        if (CollectionUtils.isNotEmpty(initialScoreGroupByClassDTOS)) {
//            for (InitialScoreGroupByClassDTO initialScoreGroupByClassDTO : initialScoreGroupByClassDTOS) {
//                BehaviourClassifyStatisticsCountDTO classifyStatisticsCountDTO = new BehaviourClassifyStatisticsCountDTO();
//                classifyStatisticsCountDTO.setClassId(initialScoreGroupByClassDTO.getClassId());
//                classifyStatisticsCountDTO.setRecordCount(initialScoreGroupByClassDTO.getRecordCount());
//                classifyStatisticsCountDTO.setScoreType(initialScoreGroupByClassDTO.getScoreType());
//                classifyStatisticsCountDTO.setScoreSum(initialScoreGroupByClassDTO.getScoreSum());
//                classifyStatisticsCountDTO.setStudentId(initialScoreGroupByClassDTO.getStudentId());
//                classifyStatisticsCountDTO.setClassifyId(Constant.NEGATIVE_ONE.longValue());
//                classifyStatisticsCountDTO.setClassifyName(ClassOverviewConstant.OTHER_CLASSIFY_NAME);
//                classifyStatisticsCountDTOS.add(classifyStatisticsCountDTO);
//            }
//        }
//    }

    /**
     * 构建查询参数
     *
     * @param classifyStatisticsRecordDTO
     * @return
     */
    private static InitialScoreGroupByClassQueryDTO buildInitialScoreQuery(BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO) {
        InitialScoreGroupByClassQueryDTO initialScoreGroupByClassQueryDTO = new InitialScoreGroupByClassQueryDTO();
        initialScoreGroupByClassQueryDTO.setClassId(String.valueOf(classifyStatisticsRecordDTO.getClassId()));
        initialScoreGroupByClassQueryDTO.setStartTime(classifyStatisticsRecordDTO.getStartTime());
        initialScoreGroupByClassQueryDTO.setEndTime(classifyStatisticsRecordDTO.getEndTime());
        return initialScoreGroupByClassQueryDTO;
    }

    /**
     * 计算上周期统计数据
     *
     * @param classifyStatisticsRecordDTO 查询参数
     * @param priorPeriodTimeDTO          上周期时间范围
     * @param currentTermStudentMap       当前学期学生
     * @return 班级概况返回值
     */
    private ClassOverviewResponse calcPriorPeriodClassOverview(BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO, PeriodTimeDTO priorPeriodTimeDTO, Map<Long, EduStudentInfoVO> currentTermStudentMap) {
        if (Objects.isNull(priorPeriodTimeDTO)) {
            return null;
        }
        //设置时间范围为上个周期
        classifyStatisticsRecordDTO.setStartTime(priorPeriodTimeDTO.getPriorPeriodStartTime());
        classifyStatisticsRecordDTO.setEndTime(priorPeriodTimeDTO.getPriorPeriodEndTime());
        //上周期班级概况数据
        ClassOverviewResponse priorPeriodClassOverviewResponse = null;
        if (checkItemTime(classifyStatisticsRecordDTO)) {
            TimeInterval begin = DateUtil.timer();
            //获取上个周期内统计数据
            List<BehaviourClassifyStatisticsCountDTO> classifyStatisticsCountDTOS = statisticsProcessor.statisticsList(classifyStatisticsRecordDTO);
            log.info("获取上周期内统计数据总量:{},耗时:{}", classifyStatisticsCountDTOS.size(), begin.interval());
//            begin.restart();
            //填充初始分统计数据
            // TODO: 2024/11/8 初始分重新设计，无需加入初始分，暂时注释
//            fillInitialScoreStatistics(classifyStatisticsRecordDTO, classifyStatisticsCountDTOS);
//            log.info("上周期填充初始分统计数据后数据总量:{},耗时:{}", classifyStatisticsCountDTOS.size(), begin.interval());
            priorPeriodClassOverviewResponse = new ClassOverviewResponse();
            //处理上个周期统计数据
            dealNowPeriodStatistics(classifyStatisticsCountDTOS, currentTermStudentMap, priorPeriodClassOverviewResponse, Boolean.FALSE);
            if (CollectionUtils.isEmpty(classifyStatisticsCountDTOS)) {
                return null;
            }
        }
        return priorPeriodClassOverviewResponse;
    }

    /**
     * 检查请求参数并且获取上周期时间
     *
     * @param statisticsRecordDTO 检查参数
     * @param calcPriorPeriod     是否计算上周期时间
     * @return 周期信息
     */
    private PeriodTimeDTO checkRequestAndGetPriorPeriodDTO(BehaviourClassifyStatisticsRecordDTO statisticsRecordDTO, boolean calcPriorPeriod) {
        //检查统计参数
        checkStatisticsRecordDTO(statisticsRecordDTO);
        //获取上个周期时间
        PeriodTimeDTO priorPeriodTimeDTO = null;
        if (calcPriorPeriod) {
            priorPeriodTimeDTO = getPriorPeriodTime(statisticsRecordDTO);
        }
        //设置学段id
        if (Objects.isNull(statisticsRecordDTO.getCampusSectionId())) {
            List<TchClassOpenV2VO> tchClassOpenV2VOS = saasClassManager
                    .queryClassByIds(Convert.toList(String.class, statisticsRecordDTO.getClassId()));
            if (CollUtil.isEmpty(tchClassOpenV2VOS) || ObjectUtil.isNull(tchClassOpenV2VOS.get(0).getCampusSectionId())) {
                log.warn("【从saas获取班级信息】返回结果空：{}", JSONUtil.toJsonStr(tchClassOpenV2VOS));
                return null;
            }
            statisticsRecordDTO.setCampusSectionId(tchClassOpenV2VOS.get(0).getCampusSectionId());
        }
        //设置当前学期
        if (Objects.isNull(statisticsRecordDTO.getCurrentTerm())) {
            TermVo currentTermVo = termLogic.getCurrentTermVo(statisticsRecordDTO.getSchoolId(), statisticsRecordDTO.getCampusId(), statisticsRecordDTO.getCampusSectionId(), new Date());
            if (Objects.isNull(currentTermVo)) {
                currentTermVo = termLogic.getCurrentTermVo(statisticsRecordDTO.getSchoolId(), statisticsRecordDTO.getCampusId(), statisticsRecordDTO.getCampusSectionId(), statisticsRecordDTO.getStartTime());
                if (Objects.isNull(currentTermVo)) {
                    throw new BizException("当前学期不存在，请重新选择日期重试");
                }
            }
            statisticsRecordDTO.setCurrentTerm(currentTermVo);
        }
        if (!checkItemTime(statisticsRecordDTO)) {
            //学期时间检查失败
            throw new BizException("当前选择时间有误");
        }
        return priorPeriodTimeDTO;
    }

    /**
     * 获取今天统计数据
     *
     * @param classifyStatisticsRecordDTO 学生行为指标查询条件
     * @param classifyStatisticsCountDTOS 分类统计信息
     */
    private void fillTodayStatistics(BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO, List<BehaviourClassifyStatisticsCountDTO> classifyStatisticsCountDTOS) {
        //当前查询范围包含今天
        if (!includeAndSetToday(classifyStatisticsRecordDTO)) {
            //不包含今天
            return;
        }
        BehaviourClassifyStatisticsConditionQO statisticsConditionQO = new BehaviourClassifyStatisticsConditionQO();
        statisticsConditionQO.setStartTime(classifyStatisticsRecordDTO.getStartTime());
        statisticsConditionQO.setEndTime(classifyStatisticsRecordDTO.getEndTime());
        statisticsConditionQO.setClassIdList(Lists.newArrayList(classifyStatisticsRecordDTO.getClassId().toString()));
        if (Objects.nonNull(classifyStatisticsRecordDTO.getCampusId())) {
            statisticsConditionQO.setCampusId(classifyStatisticsRecordDTO.getCampusId().toString());
        }
        List<BehaviourStudentClassifyStatisticsDTO> statisticsDTOList = statisticsProcessor.queryStatistics(statisticsConditionQO);
        classifyStatisticsCountDTOS.addAll(BehaviourClassifyStatisticsCountDTO.convertList(statisticsDTOList));
    }

    /**
     * 检查是否包含今天并设置开始结束时间
     *
     * @param classifyStatisticsRecordDTO 学生行为指标查询条件
     * @return 是否包含今天
     */
    private boolean includeAndSetToday(BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO) {
        Date startTime = classifyStatisticsRecordDTO.getStartTime();
        Date endTime = classifyStatisticsRecordDTO.getEndTime();
        DateTime todayBeginTime = DateUtil.beginOfDay(new Date());
        if (startTime.compareTo(todayBeginTime) <= 0 && endTime.compareTo(todayBeginTime) >= 0) {
            classifyStatisticsRecordDTO.setStartTime(todayBeginTime);
            classifyStatisticsRecordDTO.setEndTime(DateUtil.endOfDay(new Date()));
            return true;
        }
        return false;
    }

    /**
     * 填充较上周期对比数据
     *
     * @param classOverviewResponse            当前周期班级概况数据
     * @param priorPeriodClassOverviewResponse 上周期班级概况数据
     * @return 对比结果
     */
    private ClassOverviewResponse comparePriorPeriod(ClassOverviewResponse classOverviewResponse, ClassOverviewResponse priorPeriodClassOverviewResponse) {
        ClassOverviewPraiseResponse praise = classOverviewResponse.getPraise();
        ClassOverviewImproveResponse improve = classOverviewResponse.getImprove();
        if (Objects.isNull(priorPeriodClassOverviewResponse)) {
            //没有上周期数据
            improve.setPriorPeriodIsExistPointRecord(Boolean.FALSE);
            praise.setPriorPeriodIsExistPointRecord(Boolean.FALSE);
            improve.setComparePriorPeriod(BigDecimal.ZERO);
            praise.setComparePriorPeriod(BigDecimal.ZERO);
            return classOverviewResponse;
        }
        improve.setPriorPeriodIsExistPointRecord(Boolean.TRUE);
        praise.setPriorPeriodIsExistPointRecord(Boolean.TRUE);
        //上周期值得表扬数据
        ClassOverviewPraiseResponse priorPeriodPraise = priorPeriodClassOverviewResponse.getPraise();
        //上周期仍需改进数据
        ClassOverviewImproveResponse priorPeriodImprove = priorPeriodClassOverviewResponse.getImprove();
        praise.setComparePriorPeriod(praise.getPraiseScoreSum().subtract(priorPeriodPraise.getPraiseScoreSum()));
        improve.setComparePriorPeriod(improve.getImproveScoreSum().subtract(priorPeriodImprove.getImproveScoreSum()));
        log.warn("最终班级概况数据:{}", JSON.toJSONString(classOverviewResponse));
        return classOverviewResponse;
    }

    /**
     * 计算记录对应百分比
     *
     * @param classOverviewResponse 班级概况
     */
    private void calcRecordPercentRatio(ClassOverviewResponse classOverviewResponse) {
        ClassOverviewPraiseResponse praise = classOverviewResponse.getPraise();
        BigDecimal praiseScoreSum = praise.getPraiseScoreSum();
        List<ClassOverviewClassifyRecordResponse> recordList = praise.getRecordList();
        //计算值得表扬记录百分比
        calcAndSortRecordPercent(praiseScoreSum, recordList);
        ClassOverviewImproveResponse improve = classOverviewResponse.getImprove();
        BigDecimal improveScoreSum = improve.getImproveScoreSum();
        recordList = improve.getRecordList();
        //计算仍需提升记录百分比
        calcAndSortRecordPercent(improveScoreSum, recordList);
    }

    /**
     * 计算记录百分比占比
     *
     * @param scoreSum   所有记录总分
     * @param recordList 记录列表
     */
    private static void calcAndSortRecordPercent(BigDecimal scoreSum, List<ClassOverviewClassifyRecordResponse> recordList) {
        BigDecimal surplusPercent = new BigDecimal("1.0000");
        int size = recordList.size();
        int index = 0;
        for (ClassOverviewClassifyRecordResponse classOverviewRecordResponse : recordList) {
            //计算当前项百分比占比
            if (index == size - 1) {
                //最后一项获取所有剩余百分比
                classOverviewRecordResponse.setPercent(surplusPercent);
                continue;
            }
            BigDecimal score = classOverviewRecordResponse.getScore();
            BigDecimal nowPercent = score.divide(scoreSum, 4, RoundingMode.HALF_DOWN);
            surplusPercent = surplusPercent.subtract(nowPercent);
            classOverviewRecordResponse.setPercent(nowPercent);
            index++;
        }
        recordList.sort((a, b) -> b.getPercent().compareTo(a.getPercent()));
    }

    /**
     * 处理当前周期数据
     *
     * @param classifyStatisticsCountDTOS 当前周期统计数据
     * @param currentTermStudentMap       当前学期学生列表
     * @param classOverviewResponse       班级概况返回值
     * @param calcNoExistStudent          是否计算不存在的学生
     */
    private void dealNowPeriodStatistics(List<BehaviourClassifyStatisticsCountDTO> classifyStatisticsCountDTOS,
                                         Map<Long, EduStudentInfoVO> currentTermStudentMap, ClassOverviewResponse classOverviewResponse,
                                         boolean calcNoExistStudent) {
        //值得表扬返回值
        ClassOverviewPraiseResponse classOverviewPraiseResponse = new ClassOverviewPraiseResponse();
        //仍需改进返回值
        ClassOverviewImproveResponse classOverviewImproveResponse = new ClassOverviewImproveResponse();
        //总记录数 ,值得表扬总记录数 ,仍需改进总记录数
        int recordCount;
        int praiseCount = 0;
        int improveCount = 0;
        //总分
        BigDecimal scoreSum = new BigDecimal(0);
        //值得表扬总分
        BigDecimal praiseSum = new BigDecimal(0);
        //仍需改进总分(负数)
        BigDecimal improveSum = new BigDecimal(0);
        Map<String/*classifyId+classifyName*/, ClassOverviewClassifyRecordResponse> praiseMap = new HashMap<>();
        Map<String/*classifyId+classifyName*/, ClassOverviewClassifyRecordResponse> improveMap = new HashMap<>();
        Set<Long> existStudentIdSet = calcNoExistStudent ? new HashSet<>() : null;
        Iterator<BehaviourClassifyStatisticsCountDTO> iterator = classifyStatisticsCountDTOS.iterator();
        while (iterator.hasNext()) {
            BehaviourClassifyStatisticsCountDTO classifyStatisticsCountDTO = iterator.next();
            Long studentId = Long.valueOf(classifyStatisticsCountDTO.getStudentId());
            if (!currentTermStudentMap.containsKey(studentId)) {
                //当前学期不包含该学生
                iterator.remove();
                continue;
            }
            if (calcNoExistStudent) {
                existStudentIdSet.add(studentId);
            }
            if (Objects.isNull(classifyStatisticsCountDTO.getScoreSum())) {
                //分值为NULL
                iterator.remove();
                continue;
            }
            BigDecimal score = classifyStatisticsCountDTO.getScoreSum().abs();
            ClassOverviewClassifyRecordResponse classOverviewRecordResponse = null;
            if (Objects.equals(Constant.ONE, classifyStatisticsCountDTO.getScoreType())) {
                //加分
                classOverviewRecordResponse = praiseMap
                        .computeIfAbsent(classifyStatisticsCountDTO.getClassifyId() + classifyStatisticsCountDTO.getClassifyName(), key -> buildDefaultRecordResponse());
                praiseSum = praiseSum.add(score);
                praiseCount += classifyStatisticsCountDTO.getRecordCount();
            } else {
                //减分
                score = score.multiply(new BigDecimal(-1));
                classOverviewRecordResponse = improveMap
                        .computeIfAbsent(classifyStatisticsCountDTO.getClassifyId() + classifyStatisticsCountDTO.getClassifyName(), key -> buildDefaultRecordResponse());
                improveSum = improveSum.add(score);
                improveCount += classifyStatisticsCountDTO.getRecordCount();
            }
            classOverviewRecordResponse.setClassifyName(classifyStatisticsCountDTO.getClassifyName());
            classOverviewRecordResponse.setScore(classOverviewRecordResponse.getScore().add(score));
            classOverviewRecordResponse.setRecordCount(classOverviewRecordResponse.getRecordCount() + classifyStatisticsCountDTO.getRecordCount());
        }
        praiseSum = NumUtil.formatFloatNumber(praiseSum);
        improveSum = NumUtil.formatFloatNumber(improveSum);
        scoreSum = scoreSum.add(praiseSum).add(improveSum);
        //处理未点评的学生
        calcNoExistStudent(currentTermStudentMap, classOverviewResponse, calcNoExistStudent, existStudentIdSet);
        //设置值得表扬+仍需改进类目列表
        List<ClassOverviewClassifyRecordResponse> praiseRecordList = new ArrayList<>(praiseMap.values());
        List<ClassOverviewClassifyRecordResponse> improveRecordList = new ArrayList<>(improveMap.values());
        //处理表扬+仍需改进类目精度
        dealRecordScore(praiseRecordList);
        dealRecordScore(improveRecordList);
        classOverviewPraiseResponse.setPraiseScoreSum(praiseSum);
        classOverviewPraiseResponse.setPraiseRecordCount(praiseCount);
        classOverviewPraiseResponse.setRecordList(praiseRecordList);
        classOverviewPraiseResponse.setCurrentPeriodIsExistPointRecord(praiseCount > 0);
        classOverviewImproveResponse.setRecordList(improveRecordList);
        classOverviewImproveResponse.setImproveScoreSum(improveSum);
        classOverviewImproveResponse.setImproveRecordCount(improveCount);
        classOverviewImproveResponse.setCurrentPeriodIsExistPointRecord(improveCount > 0);
        //计算总记录数
        recordCount = improveCount + praiseCount;
        //计算总分
        classOverviewResponse.setRecordCount(recordCount);
        classOverviewResponse.setScoreSum(NumUtil.formatFloatNumber(scoreSum));
        classOverviewResponse.setPraise(classOverviewPraiseResponse);
        classOverviewResponse.setImprove(classOverviewImproveResponse);
        //计算表扬，改进百分比
        BigDecimal scoreSumAbs = praiseSum.abs().add(improveSum.abs());
        if (scoreSumAbs.compareTo(BigDecimal.ZERO) == 0) {
            classOverviewPraiseResponse.setPercent(BigDecimal.ZERO);
            classOverviewImproveResponse.setPercent(BigDecimal.ZERO);
        } else {
            BigDecimal praisePercent = praiseSum.abs().divide(scoreSumAbs, 4, RoundingMode.HALF_DOWN);
            classOverviewPraiseResponse.setPercent(praisePercent);
            classOverviewImproveResponse.setPercent(BigDecimal.ONE.subtract(praisePercent).setScale(4, RoundingMode.HALF_DOWN));
        }
    }

    /**
     * 处理单项记录分数
     *
     * @param recordList
     */
    private void dealRecordScore(List<ClassOverviewClassifyRecordResponse> recordList) {
        for (ClassOverviewClassifyRecordResponse classOverviewClassifyRecordResponse : recordList) {
            classOverviewClassifyRecordResponse.setScore(NumUtil.formatFloatNumber(classOverviewClassifyRecordResponse.getScore()));
        }
    }

    /**
     * 处理未点评学生
     *
     * @param currentTermStudentMap
     * @param classOverviewResponse
     * @param calcNoExistStudent
     * @param existStudentIdSet
     */
    private void calcNoExistStudent(Map<Long, EduStudentInfoVO> currentTermStudentMap, ClassOverviewResponse classOverviewResponse, boolean calcNoExistStudent, Set<Long> existStudentIdSet) {
        if (calcNoExistStudent) {
            //计算未点评学生
            List<String> nonExistStudentList = new ArrayList<>();
            ClassOverviewNoEvaluateResponse classOverviewNoEvaluateResponse = new ClassOverviewNoEvaluateResponse();
            for (EduStudentInfoVO eduStudentInfoVO : currentTermStudentMap.values()) {
                if (!existStudentIdSet.contains(eduStudentInfoVO.getId())) {
                    //不包含当前学生
                    nonExistStudentList.add(eduStudentInfoVO.getStudentName());
                }
            }
            classOverviewNoEvaluateResponse.setNoEvaluateStudentCount(nonExistStudentList.size());
            classOverviewNoEvaluateResponse.setNoEvaluateStudentNameList(nonExistStudentList);
            classOverviewResponse.setNoEvaluateStudent(classOverviewNoEvaluateResponse);
        }
    }

    private static ClassOverviewClassifyRecordResponse buildDefaultRecordResponse() {
        ClassOverviewClassifyRecordResponse recordResponse = new ClassOverviewClassifyRecordResponse();
        recordResponse.setScore(new BigDecimal(Constant.ZERO));
        recordResponse.setRecordCount(Constant.ZERO);
        return recordResponse;
    }

    /**
     * 处理需要归类到其他类目到记录到类目id为-1
     *
     * @param classifyStatisticsCountDTOS 学生指标行为统计信息
     */
    private void dealOtherType(List<BehaviourClassifyStatisticsCountDTO> classifyStatisticsCountDTOS) {
        for (BehaviourClassifyStatisticsCountDTO classifyStatisticsCountDTO : classifyStatisticsCountDTOS) {
            if (Objects.equals(classifyStatisticsCountDTO.getClassifyId(), Constant.NEGATIVE_ONE.longValue())) {
                //原来就是-1的跳过,目前初始分是-1
                continue;
            }
            //todo 处理类目
        }
    }

    /**
     * 获取当前学年学生列表
     *
     * @param statisticsRecordDTO 学生行为指标统计查询参数
     * @return 当前学期学生
     */
    private Map<Long, EduStudentInfoVO> getCurrentTermStudentMap(BehaviourClassifyStatisticsRecordDTO statisticsRecordDTO) {
        EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
        eduStudentPageQueryDTO.setSchoolId(statisticsRecordDTO.getSchoolId());
        eduStudentPageQueryDTO.setCampusId(statisticsRecordDTO.getCampusId());
        eduStudentPageQueryDTO.setCampusSectionId(Convert.toLong(statisticsRecordDTO.getCampusSectionId()));
        eduStudentPageQueryDTO.setClassId(Convert.toLong(statisticsRecordDTO.getClassId()));
        List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoService.queryStudentPageV2(eduStudentPageQueryDTO);
        if (CollectionUtils.isEmpty(eduStudentInfoVOS)) {
            return Collections.emptyMap();
        }
        Map<Long, EduStudentInfoVO> currentTermStudentMap = new HashMap<>();
        eduStudentInfoVOS.forEach(item -> currentTermStudentMap.put(item.getId(), item));
        return currentTermStudentMap;
    }

    /**
     * 获取上个周期时间
     *
     * @param classifyStatisticsRecordDTO 学生行为指标查询参数
     * @return 上周期
     */
    private PeriodTimeDTO getPriorPeriodTime(BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO) {
        PeriodTimeDTO periodTimeDTO = new PeriodTimeDTO();
        Date startTime = classifyStatisticsRecordDTO.getStartTime();
        Date endTime = classifyStatisticsRecordDTO.getEndTime();
        //获取相隔时间
        int between = (int) (-1 * DateUtil.between(startTime, endTime, DateUnit.DAY));
        //计算上周期结束时间
        Calendar endTimeCalendar = Calendar.getInstance();
        endTimeCalendar.setTime(startTime);
        endTimeCalendar.add(Calendar.DAY_OF_YEAR, -1);
        Date priorPeriodEndTime = endTimeCalendar.getTime();
        //计算上周期开始时间
        Calendar startTimeCalendar = Calendar.getInstance();
        startTimeCalendar.setTime(priorPeriodEndTime);
        startTimeCalendar.add(Calendar.DAY_OF_YEAR, between);
        Date priorPeriodStartTime = startTimeCalendar.getTime();
        priorPeriodEndTime = DateUtil.endOfDay(priorPeriodEndTime);
        //设置上周期开始、结束时间
        periodTimeDTO.setPriorPeriodStartTime(priorPeriodStartTime);
        periodTimeDTO.setPriorPeriodEndTime(priorPeriodEndTime);
        return periodTimeDTO;
    }


    /**
     * 校验统计数据查询参数
     *
     * @param statisticsRecordDTO 参数
     */
    private void checkStatisticsRecordDTO(BehaviourClassifyStatisticsRecordDTO statisticsRecordDTO) {
        Assert.notNull(statisticsRecordDTO, () -> new BizException("查询参数错误"));
        Assert.notNull(statisticsRecordDTO.getSchoolId(), () -> new BizException("学校id不能为空"));
        Assert.notNull(statisticsRecordDTO.getCampusId(), () -> new BizException("校区id不能为空"));
        Assert.notNull(statisticsRecordDTO.getClassId(), () -> new BizException("班级id不能为空"));
        Assert.notNull(statisticsRecordDTO.getStartTime(), () -> new BizException("查询时间不能为空"));
        Assert.notNull(statisticsRecordDTO.getEndTime(), () -> new BizException("查询时间不能为空"));
        if (statisticsRecordDTO.getStartTime().after(statisticsRecordDTO.getEndTime())) {
            throw new BizException("查询日期有误");
        }
        DateTime statisticsStartTime = DateUtil.beginOfDay(statisticsRecordDTO.getStartTime());
        DateTime statisticsEndTime = DateUtil.endOfDay(statisticsRecordDTO.getEndTime());
        statisticsRecordDTO.setStartTime(statisticsStartTime);
        statisticsRecordDTO.setEndTime(statisticsEndTime);
    }

    /**
     * 检查学期时间
     *
     * @param statisticsRecordDTO 参数
     * @return true:当前查询时间范围有效
     * false:当前查询时间范围无效
     */
    private boolean checkItemTime(BehaviourClassifyStatisticsRecordDTO statisticsRecordDTO) {
        Date statisticsStartTime = statisticsRecordDTO.getStartTime();
        Date statisticsEndTime = statisticsRecordDTO.getEndTime();
        TermVo currentTermVo = statisticsRecordDTO.getCurrentTerm();
        DateTime termStartTime = DateUtil.beginOfDay(DateUtil.parseDate(currentTermVo.getStartTime()));
        DateTime termEndTime = DateUtil.endOfDay(DateUtil.parseDate(currentTermVo.getEndTime()));
        if (statisticsEndTime.before(termStartTime)
                || statisticsStartTime.after(termEndTime)) {
            //查询结束时间比当前学期开始时间早,或者查询开始时间比当前学期结束时间晚
            // 表示查询的非当前学期数据
            return false;
        }
        if (statisticsStartTime.before(termStartTime)) {
            //查询开始时间比当前开始学期早
            statisticsRecordDTO.setStartTime(termStartTime);
        }
        if (statisticsEndTime.after(termEndTime)) {
            //查询结束时间比当前学期结束时间晚
            statisticsRecordDTO.setEndTime(termEndTime);
        }
        return true;
    }
}