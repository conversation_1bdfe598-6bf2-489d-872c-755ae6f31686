package com.hailiang.behaviour.classify.statistics.processor;

import com.hailiang.behaviour.classify.statistics.enums.BehaviourTargetStatisticsDateTypeEnum;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsDeleteDTO;
import com.hailiang.behaviour.classify.statistics.processor.day.DayBehaviourClassifyStatisticsProcessor;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsCountDTO;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsDTO;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsRecordDTO;
import com.hailiang.behaviour.classify.statistics.model.query.BehaviourClassifyStatisticsConditionQO;
import com.hailiang.behaviour.classify.statistics.model.query.BehaviourClassifyStatisticsRecordQO;
import com.hailiang.manager.BehaviourTargetStatisticsManager;
import com.hailiang.model.datastatistics.dto.BehaviourStudentClassifyStatisticsDTO;
import com.hailiang.model.entity.BehaviourRecord;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @description:
 * @author: panjian
 * @create: 2024/8/30 11:37
 * @Version 1.0
 */
@Component("delegateBehaviourClassifyStatisticsProcessor")
public class DelegateBehaviourClassifyStatisticsProcessor implements BehaviourClassifyStatisticsProcessor, BehaviourClassifyStatisticsQueryProcessor {

    @Resource
    private DayBehaviourClassifyStatisticsProcessor dayBehaviourTargetStatisticsProcessor;
    @Resource
    private BehaviourTargetStatisticsManager behaviourTargetStatisticsManager;


    @Override
    public Boolean statistics(BehaviourClassifyStatisticsDTO statisticsDate) {
        return dayBehaviourTargetStatisticsProcessor.statistics(statisticsDate);
    }

    @Override
    public List<BehaviourStudentClassifyStatisticsDTO> queryStatistics(BehaviourClassifyStatisticsConditionQO statisticsConditionQO) {
        if(CollectionUtils.isEmpty(statisticsConditionQO.getUnusedDatasource())){
            statisticsConditionQO.setUnusedDatasource(unusedDatasource());
        }
        return dayBehaviourTargetStatisticsProcessor.queryStatistics(statisticsConditionQO);
    }

    @Override
    public Boolean deleteStatistics(BehaviourClassifyStatisticsDeleteDTO deleteDTO) {
        return dayBehaviourTargetStatisticsProcessor.deleteStatistics(deleteDTO);
    }

    @Override
    public Boolean targetRecall(BehaviourRecord behaviourRecord) {
        dayBehaviourTargetStatisticsProcessor.targetRecall(behaviourRecord);
        return Boolean.TRUE;
    }

    @Override
    public List<BehaviourClassifyStatisticsCountDTO> statisticsList(BehaviourClassifyStatisticsRecordDTO statisticsRecordDTO) {
        BehaviourClassifyStatisticsRecordQO behaviourTargetStatisticsRecordQO = new BehaviourClassifyStatisticsRecordQO();
        behaviourTargetStatisticsRecordQO.setClassId(statisticsRecordDTO.getClassId().toString());
        behaviourTargetStatisticsRecordQO.setStartTime(statisticsRecordDTO.getStartTime());
        behaviourTargetStatisticsRecordQO.setEndTime(statisticsRecordDTO.getEndTime());
        return behaviourTargetStatisticsManager.queryStatisticsCount(behaviourTargetStatisticsRecordQO);
    }

}