package com.hailiang.behaviour.classify.statistics.enums;

import lombok.Data;
import lombok.Getter;

/**
 * @description: 学生行为指标统计表统计日期类型枚举
 * @author: pan<PERSON>an
 * @create: 2024/8/30 10:54
 * @Version 1.0
 */
@Getter
public enum BehaviourTargetStatisticsDateTypeEnum {

    DAY(1,"按日统计"),
    WEEK(2,"按周统计"),
    MONTH(3,"按月统计"),
    ;

    private Integer type;

    private String desc;

    BehaviourTargetStatisticsDateTypeEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }
}