package com.hailiang.behaviour.classify.statistics.model.dto;

import com.hailiang.model.datastatistics.dto.BehaviourStudentClassifyStatisticsDTO;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @description: 学生行为指标统计记录汇总传输实体
 * @author: panjian
 * @create: 2024/9/2 10:01
 * @Version 1.0
 */
@Data
public class BehaviourClassifyStatisticsCountDTO implements Serializable {

    /**
     * 班级id
     */
    private String classId;

    /**
     * 学生id
     */
    private String studentId;

    /**
     * 类目id
     */
    private Long classifyId;

    /**
     * 类目名称
     */
    private String classifyName;

    /**
     * 记录数
     */
    private Integer recordCount;

    /**
     * 加分类型 1：加分 2：减分
     */
    private Integer scoreType;

    /**
     * 分数（没有正负，是实际得分的绝对值）
     */
    private BigDecimal scoreSum;

    public static List<BehaviourClassifyStatisticsCountDTO> convertList(List<BehaviourStudentClassifyStatisticsDTO> statisticsDTOList){
        if(CollectionUtils.isEmpty(statisticsDTOList)){
            return Collections.emptyList();
        }
        List<BehaviourClassifyStatisticsCountDTO> resultList = new ArrayList<>();
        for (BehaviourStudentClassifyStatisticsDTO behaviourStudentTargetStatisticsDTO : statisticsDTOList) {
            BehaviourClassifyStatisticsCountDTO behaviourTargetStatisticsCountDTO = new BehaviourClassifyStatisticsCountDTO();
            behaviourTargetStatisticsCountDTO.setClassifyId(behaviourStudentTargetStatisticsDTO.getClassifyId());
            behaviourTargetStatisticsCountDTO.setClassifyName(behaviourStudentTargetStatisticsDTO.getClassifyName());
            behaviourTargetStatisticsCountDTO.setRecordCount(behaviourStudentTargetStatisticsDTO.getRecordCount());
            behaviourTargetStatisticsCountDTO.setClassId(behaviourStudentTargetStatisticsDTO.getClassId());
            behaviourTargetStatisticsCountDTO.setStudentId(behaviourStudentTargetStatisticsDTO.getStudentId());
            behaviourTargetStatisticsCountDTO.setScoreType(behaviourStudentTargetStatisticsDTO.getScoreType());
            behaviourTargetStatisticsCountDTO.setScoreSum(behaviourStudentTargetStatisticsDTO.getScore());
            resultList.add(behaviourTargetStatisticsCountDTO);
        }
        return resultList;
    }
}