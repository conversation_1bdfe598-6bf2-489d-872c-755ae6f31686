package com.hailiang.behaviour.classify.statistics.model.responsse;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 班级概况-仍需改进返回值
 * @author: panjian
 * @create: 2024/9/2 14:20
 * @Version 1.0
 */
@Data
public class ClassOverviewImproveResponse implements Serializable {


    private static final long serialVersionUID = -3235308708099971930L;
    /**
     * 仍需改进总分
     */
    private BigDecimal improveScoreSum;

    /**
     * 仍需改进总记录数
     */
    private Integer improveRecordCount;

    /**
     * 百分比(保留4位小数，例如0.5512就是55.12%)
     */
    private BigDecimal percent;

    /**
     * 较上期分数对比
     */
    private BigDecimal comparePriorPeriod;


    /**
     * 上期是否存在数据
     */
    private Boolean priorPeriodIsExistPointRecord;

    /**
     * 当前周期该板块是否存在点评数据
     */
    private Boolean currentPeriodIsExistPointRecord;

    /**
     * 分类明细
     */
    private List<ClassOverviewClassifyRecordResponse> recordList;

}