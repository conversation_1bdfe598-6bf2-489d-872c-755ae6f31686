package com.hailiang.behaviour.classify.statistics.util;

/**
 * @description: 学生行为指标-统计时间工具类
 * @author: pan<PERSON><PERSON>
 * @create: 2024/8/30 11:39
 * @Version 1.0
 */
public class BehaviourTargetStatisticsTimeUtil {

//    public static List<BehaviourTargetStatisticsTimeQO> analysisStatisticsTime(Date startTime, Date endTime) {
//        if (endTime.before(startTime)) {
//            return Collections.emptyList();
//        }
//        List<BehaviourTargetStatisticsTimeQO> resultList = new ArrayList<>();
//        Calendar startCalendar = Calendar.getInstance();
//        startCalendar.setTime(startTime);
//        int startYear = startCalendar.get(Calendar.YEAR);
//
//        Calendar endCalendar = Calendar.getInstance();
//        endCalendar.setTime(endTime);
//        int endYear = endCalendar.get(Calendar.YEAR);
//
//        while (startYear != endYear) {
//            //年份不同
//            //设置当年的结束时间
//            Calendar startYearEndCalendar = Calendar.getInstance();
//            startYearEndCalendar.set(startYear, Calendar.DECEMBER, 31);
//            resultList.addAll(analysisStatisticsTime(startCalendar.getTime(), startYearEndCalendar.getTime()));
//            //设置开始时间为下一年的1月1日
//            startYearEndCalendar.add(Calendar.DAY_OF_YEAR, 1);
//            startCalendar.setTime(startYearEndCalendar.getTime());
//            startYear = startCalendar.get(Calendar.YEAR);
//        }
//        int startMonth = startCalendar.get(Calendar.MONTH);
//        int endMonth = endCalendar.get(Calendar.MONTH);
//        if (startMonth + 1 < endMonth) {
//            //表明中间隔了几个月
//
//        }
//        //
//        return Collections.emptyList();
//    }
}