package com.hailiang.behaviour.classify.statistics.processor;

import com.google.common.collect.Sets;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsDTO;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsDeleteDTO;
import com.hailiang.behaviour.classify.statistics.model.query.BehaviourClassifyStatisticsConditionQO;
import com.hailiang.model.datastatistics.dto.BehaviourStudentClassifyStatisticsDTO;
import com.hailiang.model.entity.BehaviourRecord;

import java.util.List;
import java.util.Set;

/**
 * @description: 学生行为指标统计处理器
 * @author: panjian
 * @create: 2024/8/30 11:10
 * @Version 1.0
 */
public interface BehaviourClassifyStatisticsProcessor {

    /**
     * 统计方法
     * @param statisticsDTO 统计参数
     * @return
     */
    Boolean statistics(BehaviourClassifyStatisticsDTO statisticsDTO);

    /**
     * 统计方法
     * @param statisticsConditionQO 统计参数
     * @return
     */
    List<BehaviourStudentClassifyStatisticsDTO> queryStatistics(BehaviourClassifyStatisticsConditionQO statisticsConditionQO);


    /**
     * 删除统计数据
     * @return
     */
    Boolean deleteStatistics(BehaviourClassifyStatisticsDeleteDTO deleteDTO);

    /**
     * 指标撤回，修改统计信息
     * @param behaviourRecord
     * @return
     */
    Boolean targetRecall(BehaviourRecord behaviourRecord);

    /**
     * 不统计的datasource
     * @return
     */
    default Set<Integer> unusedDatasource(){
        return Sets.newHashSet(99);
    }
}