package com.hailiang.behaviour.classify.statistics.model.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-08-30
 */
@Data
@TableName("evaluate_behaviour_student_classify_statistics")
public class BehaviourStudentClassifyStatisticsPO extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;
    /**
     * 学校id
     */
    @TableField("school_id")
    private String schoolId;
    /**
     * 校区id
     */
    @TableField("campus_id")
    private String campusId;
    /**
     * 学段id
     */
    @TableField("campus_section_id")
    private String campusSectionId;
    /**
     * 学段code
     */
    @TableField("campus_section_code")
    private String campusSectionCode;
    /**
     * 年级id
     */
    @TableField("grade_id")
    private String gradeId;
    /**
     * 年级code
     */
    @TableField("grade_code")
    private String gradeCode;
    /**
     * 班级id
     */
    @TableField("class_id")
    private String classId;
    /**
     * 学生id
     */
    @TableField("student_id")
    private String studentId;
    /**
     * 分类id，分类为其他是为-1
     */
    @TableField("classify_id")
    private String classifyId;
    /**
     * 分类名称
     */
    @TableField("classify_name")
    private String classifyName;
    /**
     * 统计记录数
     */
    @TableField("record_count")
    private Integer recordCount;
    /**
     * 加分类型 1：加分 2：减分
     */
    @TableField("score_type")
    private Integer scoreType;
    /**
     * 分数（分数绝对值）
     */
    @TableField("score")
    private BigDecimal score;
    /**
     * 统计时间，根据statistics_time决定
     */
    @TableField("statistics_time")
    private Date statisticsTime;

}
