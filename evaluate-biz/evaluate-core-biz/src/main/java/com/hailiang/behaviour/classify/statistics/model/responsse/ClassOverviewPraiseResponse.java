package com.hailiang.behaviour.classify.statistics.model.responsse;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 班级概况-值得表扬返回值
 * @author: panjian
 * @create: 2024/9/2 14:20
 * @Version 1.0
 */
@Data
public class ClassOverviewPraiseResponse implements Serializable {


    private static final long serialVersionUID = 2605522465302905877L;
    /**
     * 值得表扬总分
     */
    private BigDecimal praiseScoreSum;

    /**
     * 值得表扬总记录数
     */
    private Integer praiseRecordCount;

    /**
     * 百分比(保留4位小数，例如0.5512就是55.12%)
     */
    private BigDecimal percent;

    /**
     * 较上期分数对比
     */
    private BigDecimal comparePriorPeriod;

    /**
     * 上期是否存在点评数据
     */
    private Boolean priorPeriodIsExistPointRecord;

    /**
     * 当前周期该板块是否存在点评数据
     */
    private Boolean currentPeriodIsExistPointRecord;

    /**
     * 分类明细列表
     */
    private List<ClassOverviewClassifyRecordResponse> recordList;

}