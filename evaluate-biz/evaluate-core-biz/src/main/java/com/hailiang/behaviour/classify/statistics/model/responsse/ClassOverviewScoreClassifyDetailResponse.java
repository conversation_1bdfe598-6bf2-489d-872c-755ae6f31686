package com.hailiang.behaviour.classify.statistics.model.responsse;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 班级概况-积分统计-类目明细
 * @author: pan<PERSON>an
 * @create: 2024/9/3 14:07
 * @Version 1.0
 */
@Data
public class ClassOverviewScoreClassifyDetailResponse implements Serializable {

    private static final long serialVersionUID = 5874232886144767579L;
    /**
     * 分类id
     */
    private Long classifyId;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 分类积分（有正有负）
     */
    private BigDecimal score;

    /**
     * 分类积分（有正有负）（excel展示用）
     */
    private String scoreShow;

}