package com.hailiang.behaviour.classify.statistics.model.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * @description: 学生行为指标统计条件查询参数
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024/9/2 09:40
 * @Version 1.0
 */
@Data
public class BehaviourClassifyStatisticsConditionQO implements Serializable {

    /**
     * 查询开始时间
     */
    private Date startTime;

    /**
     * 查询结束时间
     */
    private Date endTime;

    /**
     * 不统计的datasource
     */
    private Collection<Integer> unusedDatasource;

    /**
     * 班级idList
     */
    private List<String> classIdList;

    /**
     * 班级id
     */
    private String classId;

    /**
     * 校区id
     */
    private String campusId;
}