package com.hailiang.behaviour.classify.statistics.model.responsse;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 班级概况-未点评模块返回值
 * @author: pan<PERSON><PERSON>
 * @create: 2024/9/2 14:34
 * @Version 1.0
 */
@Data
public class ClassOverviewNoEvaluateResponse implements Serializable {

    private static final long serialVersionUID = -4167456420515586648L;
    /**
     * 未点评学生总数
     */
    private Integer noEvaluateStudentCount;

    /**
     * 未点评学生列表
     */
    private List<String> noEvaluateStudentNameList;
}