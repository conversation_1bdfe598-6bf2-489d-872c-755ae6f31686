package com.hailiang.behaviour.classify.statistics.model.responsse;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 班级概况返回值
 * @author: panjian
 * @create: 2024/9/2 14:16
 * @Version 1.0
 */
@Data
public class ClassOverviewResponse implements Serializable {

    private static final long serialVersionUID = 1216088020234309714L;
    /**
     * 积分总分
     */
    private BigDecimal scoreSum;

    /**
     * 积分明细记录总数
     */
    private Integer recordCount;

    /**
     * 当前周期是否存在点评记录
     */
    private Boolean isExistPointRecord;

    /**
     * 值得表扬板块
     */
    private ClassOverviewPraiseResponse praise;

    /**
     * 仍需改进板块
     */
    private ClassOverviewImproveResponse improve;

    /**
     * 未点评板块
     */
    private ClassOverviewNoEvaluateResponse noEvaluateStudent;
}