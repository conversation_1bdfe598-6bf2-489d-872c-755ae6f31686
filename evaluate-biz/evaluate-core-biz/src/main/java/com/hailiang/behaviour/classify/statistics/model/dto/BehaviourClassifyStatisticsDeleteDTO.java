package com.hailiang.behaviour.classify.statistics.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 学生行为指标分类统计数据删除参数
 * @author: pan<PERSON><PERSON>
 * @create: 2024/9/3 15:48
 * @Version 1.0
 */
@Data
public class BehaviourClassifyStatisticsDeleteDTO implements Serializable {

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;
}