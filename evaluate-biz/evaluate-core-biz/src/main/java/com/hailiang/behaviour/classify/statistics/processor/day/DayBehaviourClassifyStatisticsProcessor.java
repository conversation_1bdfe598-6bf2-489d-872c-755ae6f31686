package com.hailiang.behaviour.classify.statistics.processor.day;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hailiang.behaviour.classify.statistics.enums.BehaviourTargetStatisticsDateTypeEnum;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsDeleteDTO;
import com.hailiang.behaviour.classify.statistics.model.entity.BehaviourStudentClassifyStatisticsPO;
import com.hailiang.behaviour.classify.statistics.model.query.BehaviourClassifyStatisticsConditionQO;
import com.hailiang.behaviour.classify.statistics.model.query.BehaviourClassifyStatisticsRecordQO;
import com.hailiang.behaviour.classify.statistics.processor.AbstractBehaviourClassifyStatisticsQueryProcessor;
import com.hailiang.constant.Constant;
import com.hailiang.entity.EvaluateBehaviourRecordExtPO;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.manager.EvaluateBehaviourRecordExtManager;
import com.hailiang.model.datastatistics.dto.BehaviourStudentClassifyStatisticsDTO;
import com.hailiang.model.entity.BehaviourRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @description: 学生行为指标-日统计处理器
 * @author: panjian
 * @create: 2024/8/30 11:10
 * @Version 1.0
 */
@Slf4j
@Component
public class DayBehaviourClassifyStatisticsProcessor extends AbstractBehaviourClassifyStatisticsQueryProcessor {

    @Resource
    private BehaviourRecordManager behaviourRecordManager;
    @Resource
    private EvaluateBehaviourRecordExtManager evaluateBehaviourRecordClassifyManager;

    @Override
    protected List<String> listDistinctClassId(BehaviourClassifyStatisticsConditionQO statisticsConditionQO) {
        return behaviourRecordManager.listDistinctClassId(statisticsConditionQO);
    }

    @Override
    public List<BehaviourStudentClassifyStatisticsDTO> queryStatistics(BehaviourClassifyStatisticsConditionQO statisticsConditionQO) {
        if(CollectionUtils.isEmpty(statisticsConditionQO.getUnusedDatasource())){
            statisticsConditionQO.setUnusedDatasource(unusedDatasource());
        }
        return behaviourRecordManager.listBehaviourRecordByClassIds(statisticsConditionQO);
    }

    @Override
    public Boolean deleteStatistics(BehaviourClassifyStatisticsDeleteDTO deleteDTO) {
        behaviourTargetStatisticsManager.deleteStatisticsRecord(deleteDTO);
        return Boolean.TRUE;
    }

    @Override
    public Boolean targetRecall(BehaviourRecord behaviourRecord) {
        List<BehaviourRecord> behaviourRecords = Lists.newArrayList(behaviourRecord);
        behaviourRecords = filterUnStatisticsRecords(behaviourRecords);
        if(CollectionUtils.isEmpty(behaviourRecords)){
            log.info("过滤之后无统计数据需要撤回，记录为:{}", JSONObject.toJSONString(behaviourRecord));
            return Boolean.TRUE;
        }
        BehaviourClassifyStatisticsRecordQO statisticsRecordQO = buildStatisticsRecordQO(behaviourRecord, formatStatisticsTime(behaviourRecord.getSubmitTime()));
        BehaviourStudentClassifyStatisticsPO behaviourStudentTargetStatisticsPO = behaviourTargetStatisticsManager.queryStatisticsRecord(statisticsRecordQO);
        if (Objects.isNull(behaviourStudentTargetStatisticsPO)) {
            log.warn("当前统计记录不存在，行为指标记录id:{}", behaviourRecord.getId());
            return Boolean.FALSE;
        }
        BigDecimal scoreValue = behaviourRecord.getScoreValue();
        BigDecimal statisticsScore = behaviourStudentTargetStatisticsPO.getScore();
        Integer recordCount = behaviourStudentTargetStatisticsPO.getRecordCount();
        if (Objects.equals(Constant.ONE, behaviourStudentTargetStatisticsPO.getScoreType())) {
            //加分
            statisticsScore = statisticsScore.subtract(scoreValue);
            recordCount--;
        } else if (Objects.equals(Constant.TWO, behaviourStudentTargetStatisticsPO.getScoreType())) {
            //减分
            statisticsScore = statisticsScore.add(scoreValue);
            recordCount--;
        }
        behaviourStudentTargetStatisticsPO.setScore(statisticsScore);
        behaviourStudentTargetStatisticsPO.setRecordCount(recordCount);
        behaviourTargetStatisticsManager.updateById(behaviourStudentTargetStatisticsPO);
        return Boolean.TRUE;
    }

    /**
     * 过滤未统计的行为记录
     * @param behaviourRecords
     * @return
     */
    private List<BehaviourRecord> filterUnStatisticsRecords(List<BehaviourRecord> behaviourRecords) {
        Iterator<BehaviourRecord> iterator = behaviourRecords.iterator();
        Set<Integer> unusedDatasourceSet = unusedDatasource();
        while(iterator.hasNext()){
            BehaviourRecord next = iterator.next();
            if(unusedDatasourceSet.contains(next.getDataSource())){
                iterator.remove();
            }
        }
        return behaviourRecords;
    }

    /**
     * 构建统计记录查询参数
     *
     * @param behaviourRecord 原记录
     * @param statisticsTime  统计时间
     * @return
     */
    private BehaviourClassifyStatisticsRecordQO buildStatisticsRecordQO(BehaviourRecord behaviourRecord, Date statisticsTime) {
        List<EvaluateBehaviourRecordExtPO> evaluateBehaviourRecordClassifyPOS = evaluateBehaviourRecordClassifyManager.listByEvaluateIds(Lists.newArrayList(behaviourRecord.getId()));
        Long classifyId = Constant.NEGATIVE_ONE.longValue();
        if (CollectionUtils.isNotEmpty(evaluateBehaviourRecordClassifyPOS)) {
            classifyId = evaluateBehaviourRecordClassifyPOS.get(0).getClassifyId();
        }
        BehaviourClassifyStatisticsRecordQO statisticsRecordQO = new BehaviourClassifyStatisticsRecordQO();
        statisticsRecordQO.setClassId(behaviourRecord.getClassId());
        statisticsRecordQO.setStudentId(behaviourRecord.getStudentId());
        statisticsRecordQO.setClassifyId(classifyId);
        statisticsRecordQO.setScoreType(behaviourRecord.getScoreType());
        statisticsRecordQO.setStatisticsTime(statisticsTime);
        return statisticsRecordQO;
    }

    /**
     * 格式化为统计时间
     *
     * @param date
     * @return
     */
    private Date formatStatisticsTime(Date date) {
        return DateUtil.parse(DateUtil.format(date, DatePattern.NORM_DATE_PATTERN), DatePattern.NORM_DATE_PATTERN);
    }

}
