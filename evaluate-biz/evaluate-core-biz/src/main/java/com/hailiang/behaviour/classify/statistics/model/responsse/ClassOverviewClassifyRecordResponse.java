package com.hailiang.behaviour.classify.statistics.model.responsse;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 班级概况分类明细返回值
 * @author: pan<PERSON><PERSON>
 * @create: 2024/9/2 14:22
 * @Version 1.0
 */
@Data
public class ClassOverviewClassifyRecordResponse implements Serializable {

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 分类下统计分数（若为改进下的分数则为负数）
     */
    private BigDecimal score;

    /**
     * 记录数
     */
    private Integer recordCount;

    /**
     * 百分比(保留4位小数，例如0.5512就是55.12%)
     */
    private BigDecimal percent;
}