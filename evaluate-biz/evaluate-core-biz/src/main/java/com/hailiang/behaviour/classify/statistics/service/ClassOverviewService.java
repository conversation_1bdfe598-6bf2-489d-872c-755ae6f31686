package com.hailiang.behaviour.classify.statistics.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsCountDTO;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsRecordDTO;
import com.hailiang.behaviour.classify.statistics.model.request.ClassOverviewRequest;
import com.hailiang.behaviour.classify.statistics.model.request.ClassOverviewScoreDetailRequest;
import com.hailiang.behaviour.classify.statistics.model.request.ClassOverviewScoreRequest;
import com.hailiang.behaviour.classify.statistics.model.responsse.ClassOverviewResponse;
import com.hailiang.behaviour.classify.statistics.model.responsse.ClassOverviewScoreDetailResponse;
import com.hailiang.behaviour.classify.statistics.model.responsse.ClassOverviewScoreResponsse;

import java.util.List;

/**
 * @description: 班级概况service
 * @author: panjian
 * @create: 2024/9/2 14:40
 * @Version 1.0
 */
public interface ClassOverviewService {

    /**
     * 班级概况请求方法
     * @param classOverviewRequest
     * @return
     */
    ClassOverviewResponse classOverview(ClassOverviewRequest classOverviewRequest);

    /**
     * 积分统计
     * @param request
     * @return
     */
    ClassOverviewScoreResponsse classScoreOverview(ClassOverviewScoreRequest request);

    /**
     * 班级概况-积分明细
     * @param request
     * @return
     */
    Page<ClassOverviewScoreDetailResponse> classScoreDetailOverview(ClassOverviewScoreDetailRequest request);


    /**
     * 查询当前周期学生分类统计记录（包含今天）(过滤没有积分值的记录)
     * @param classifyStatisticsRecordDTO 查询参数
     * @return
     */
    List<BehaviourClassifyStatisticsCountDTO> getNowPeroidStatisticsCountList(BehaviourClassifyStatisticsRecordDTO classifyStatisticsRecordDTO);
}