package com.hailiang.behaviour.classify.statistics.model.responsse;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 班级概况-学生表现-积分明细
 * @author: pan<PERSON><PERSON>
 * @create: 2024/9/3 14:15
 * @Version 1.0
 */
@Data
public class ClassOverviewScoreDetailResponse implements Serializable {

    /**
     * 学生姓名
     */
    private String name;

    /**
     * 积分(加分为正数，减分为负数)
     */
    private BigDecimal score;

    /**
     * 积分类型 1：加分 2：减分
     */
    private Integer scoreType;

    /**
     * 积分类型显示名称
     */
    private String scoreTypeName;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 点评项名称
     */
    private String classifyDetailName;

    /**
     * 点评时间
     */
    private Date submitTime;

    /**
     * 点评人
     */
    private String appraisalName;
}