package com.hailiang.behaviour.classify.statistics.model.dto;

import com.hailiang.behaviour.classify.statistics.model.request.ClassOverviewRequest;
import com.hailiang.behaviour.classify.statistics.model.request.ClassOverviewScoreDetailRequest;
import com.hailiang.remote.saas.vo.term.TermVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 学生行为指标统计记录查询传输实体
 * @author: pan<PERSON>an
 * @create: 2024/9/2 10:01
 * @Version 1.0
 */
@Data
public class BehaviourClassifyStatisticsRecordDTO implements Serializable {

    /**
     * 学校id
     */
    private Long schoolId;

    /**
     * 校区id
     */
    private Long campusId;

    /**
     * 学段id
     */
    private Long campusSectionId;

    /**
     * 班级id
     */
    private Long classId;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 当前学期
     */
    private TermVo currentTerm;

    public static <T extends ClassOverviewRequest> BehaviourClassifyStatisticsRecordDTO convert(T overviewRequest){
        BehaviourClassifyStatisticsRecordDTO behaviourTargetStatisticsRecordDTO = new BehaviourClassifyStatisticsRecordDTO();
        behaviourTargetStatisticsRecordDTO.setStartTime(overviewRequest.getBeginTime());
        behaviourTargetStatisticsRecordDTO.setEndTime(overviewRequest.getEndTime());
        behaviourTargetStatisticsRecordDTO.setClassId(overviewRequest.getClassId());
        behaviourTargetStatisticsRecordDTO.setCampusId(overviewRequest.getCampusId());
        behaviourTargetStatisticsRecordDTO.setSchoolId(overviewRequest.getSchoolId());
        return behaviourTargetStatisticsRecordDTO;
    }

    public static BehaviourClassifyStatisticsRecordDTO convert(ClassOverviewScoreDetailRequest overviewRequest){
        BehaviourClassifyStatisticsRecordDTO behaviourTargetStatisticsRecordDTO = new BehaviourClassifyStatisticsRecordDTO();
        behaviourTargetStatisticsRecordDTO.setStartTime(overviewRequest.getBeginTime());
        behaviourTargetStatisticsRecordDTO.setEndTime(overviewRequest.getEndTime());
        behaviourTargetStatisticsRecordDTO.setClassId(overviewRequest.getClassId());
        behaviourTargetStatisticsRecordDTO.setCampusId(overviewRequest.getCampusId());
        behaviourTargetStatisticsRecordDTO.setSchoolId(overviewRequest.getSchoolId());
        return behaviourTargetStatisticsRecordDTO;
    }
}