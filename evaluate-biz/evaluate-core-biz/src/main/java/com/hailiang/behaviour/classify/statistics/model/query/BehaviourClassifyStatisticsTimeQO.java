package com.hailiang.behaviour.classify.statistics.model.query;

import com.hailiang.behaviour.classify.statistics.enums.BehaviourTargetStatisticsDateTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 学生行为指标统计时间(statistics_time)查询参数
 * @author: pan<PERSON><PERSON>
 * @create: 2024/8/30 10:44
 * @Version 1.0
 */
@Data
public class BehaviourClassifyStatisticsTimeQO implements Serializable {

    /**
     * 统计时间类型
     * @see BehaviourTargetStatisticsDateTypeEnum
     */
    private Integer statisticsDateType;

    /**
     * 统计时间指标
     */
    private List<String> statisticsTime;

}