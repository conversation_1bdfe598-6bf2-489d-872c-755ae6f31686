package com.hailiang.exception;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum TargetExceptionEnum implements ErrorInterface {
    ASSISTANT_IS_DELETED(1001, "学科助理已被删除"),
    HAVE_NO_RIGHT(1002, "暂无权限"),
    HAVE_COURSE(1003, "培训班下有课程，不可删除"),
    CLASS_USER_EMPTY(1004, "请选择学员"),
    NO_DATA(1005,"无学员数据可导出")

    ;
    private final Integer value;
    private final String message;


    @Override
    public Integer getCode() {
        return this.value;
    }

    @Override
    public String getMessage() {
        return this.message;
    }


}
