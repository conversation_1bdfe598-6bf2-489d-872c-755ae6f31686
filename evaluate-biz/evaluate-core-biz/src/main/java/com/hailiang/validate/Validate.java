package com.hailiang.validate;

import com.hailiang.model.dto.save.TemplateInfoSaveDTO;

import java.util.List;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/25 14:06
 */
public abstract class Validate {
    private Validate next;

    public void builder(Validate next) {
        this.next = next;
    }

    protected void validate(List<TemplateInfoSaveDTO> templates) {
        if (next != null) {
            next.handle(templates);
        }
    }

    public abstract void handle(List<TemplateInfoSaveDTO> templates);
}
