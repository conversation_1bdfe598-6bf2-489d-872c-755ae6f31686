package com.hailiang.validate.aspect;

import cn.hutool.core.lang.Assert;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.util.BeanUtil;
import com.hailiang.validate.Validate;
import com.hailiang.validate.annotation.TemplateReleaseCheck;
import com.hailiang.validate.factory.ValidateFactory;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.List;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/26 10:05
 */
@Aspect
@Order(1)
@Component
public class TemplateReleaseAspect {
    @Resource
    private BeanUtil beanUtil;


    @Before("@annotation(rl)")
    public void before(JoinPoint joinPoint, TemplateReleaseCheck rl) throws Exception {

        Object[] args = joinPoint.getArgs();
        List<TemplateInfoSaveDTO> list = findTemplateInfoList(args);
        Assert.notNull(list, () -> new BizException(BizExceptionEnum.TEMPLATE_RELEASE_VALIDATION_ERROR_TEMPLATE_NOT_FOUND));

        String factoryName = rl.factoryName();
        Object obj = beanUtil.getBean(factoryName);

        if (obj instanceof ValidateFactory) {
            ValidateFactory validateFactory = (ValidateFactory) obj;
            Validate validate = validateFactory.createValidateFactory();
            validate.handle(list);
        } else {
            throw new BizException("检查工厂名称非法");
        }
    }


    private List<TemplateInfoSaveDTO> findTemplateInfoList(Object[] args) throws IllegalAccessException {
        for (Object arg : args) {
            Field templateInfoListField = getFieldByName(arg);
            if (templateInfoListField != null) {
                templateInfoListField.setAccessible(true);
                return (List<TemplateInfoSaveDTO>) templateInfoListField.get(arg);
            }
        }
        return null;
    }


    // 根据字段名获取字段对象（包括父类）
    private Field getFieldByName(Object obj) {
        Field field = null;

        Class<?> clazz = obj.getClass();
        while (clazz != null && field == null) {
            try {
                field = clazz.getDeclaredField("templateInfoList");
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return field;
    }
}

