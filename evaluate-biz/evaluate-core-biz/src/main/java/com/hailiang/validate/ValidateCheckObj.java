package com.hailiang.validate;

import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.validate.constant.VerificationConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/25 14:07
 */
@Slf4j
@Component(VerificationConstant.CHECK_OBJ)
public class ValidateCheckObj extends Validate {
    @Override
    public void handle(List<TemplateInfoSaveDTO> templates) {

        Map<String, Long> typeCountMap = templates.stream().collect(Collectors.groupingBy(TemplateInfoSaveDTO::getType, Collectors.counting()));

        long typeClassCount = typeCountMap.getOrDefault(SubmitInfoTypeEnum.CLASS.getText(), 0L);
        long typeDormitoryCount = typeCountMap.getOrDefault(SubmitInfoTypeEnum.DORMITORY.getText(), 0L);

        if (typeClassCount + typeDormitoryCount == 0L) {
            throw new BizException("请选择检查对象");
        }
        if (typeClassCount + typeDormitoryCount > 1L) {
            throw new BizException("一个项指标只允许添加一个检查对象");
        }

        super.validate(templates);
    }
}
