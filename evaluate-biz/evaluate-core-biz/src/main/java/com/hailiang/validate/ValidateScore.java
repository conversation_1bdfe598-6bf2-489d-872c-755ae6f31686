package com.hailiang.validate;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/25 14:07
 */

import com.hailiang.enums.SubmitInfoTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.model.dto.save.TemplateInfoSaveDTO;
import com.hailiang.validate.constant.VerificationConstant;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component(VerificationConstant.SCORE)
public class ValidateScore extends Validate {

    @Override
    public void handle(List<TemplateInfoSaveDTO> templates) {

        Map<String, Long> scoreTypeCountMap = templates.stream()
                .filter(e -> {
                    String type = e.getType();
                    boolean isScore = Optional.ofNullable(e.getIsScore()).orElse(Boolean.FALSE);
                    return type.equals(SubmitInfoTypeEnum.SCORE.getText())
                            || (type.equals(SubmitInfoTypeEnum.SINGLE_CHECK.getText()) && isScore)
                            || (type.equals(SubmitInfoTypeEnum.MULTI_CHECK.getText()) && isScore);
                })
                .collect(Collectors.groupingBy(TemplateInfoSaveDTO::getType, Collectors.counting()));

        long typeScoreCount = scoreTypeCountMap.getOrDefault(SubmitInfoTypeEnum.SCORE.getText(), 0L);
        long typeRadioCount = scoreTypeCountMap.getOrDefault(SubmitInfoTypeEnum.SINGLE_CHECK.getText(), 0L);
        long typeCheckboxCount = scoreTypeCountMap.getOrDefault(SubmitInfoTypeEnum.MULTI_CHECK.getText(), 0L);

        if (typeScoreCount + typeRadioCount + typeCheckboxCount == 0L) {
            throw new BizException("检查项需要配置分值控件或等级计分控件其中一项");
        }

        if (typeScoreCount > 1 && typeRadioCount > 1 && typeCheckboxCount > 1) {
            throw new BizException("分值控件和等级计分不能同时存在");
        }

        super.validate(templates);
    }
}