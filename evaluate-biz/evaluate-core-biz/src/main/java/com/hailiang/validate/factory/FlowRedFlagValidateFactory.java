package com.hailiang.validate.factory;

import com.hailiang.validate.Validate;
import com.hailiang.validate.constant.FactoryConstant;
import com.hailiang.validate.constant.VerificationConstant;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/25 16:35
 */
@Component(FactoryConstant.FLOW_RED_FLAG)
public class FlowRedFlagValidateFactory implements ValidateFactory {
    @Resource
    private Map<String, Validate> verificationMap;

    @Override
    public Validate createValidateFactory() {
        Validate checkObj = verificationMap.get(VerificationConstant.CHECK_OBJ);
        Validate score = verificationMap.get(VerificationConstant.SCORE);
        checkObj.builder(score);
        return checkObj;
    }
}
