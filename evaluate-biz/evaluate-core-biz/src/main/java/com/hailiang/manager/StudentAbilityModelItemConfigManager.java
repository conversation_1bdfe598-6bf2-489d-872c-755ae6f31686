package com.hailiang.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.domain.studentmodel.StudentAbilityModelItemDO;
import com.hailiang.model.entity.EvaluateStudentAbilityModelItemConfigPO;
import com.hailiang.model.entity.EvaluateStudentAbilityModelItemPO;

import java.util.List;

/**
 * 学生能力模型-能力项 服务类
 * @author: panjian
 * @create: 2024/11/20 11:57
 * @Version 1.0
 */
public interface StudentAbilityModelItemConfigManager extends IService<EvaluateStudentAbilityModelItemConfigPO> {

    /**
     * 批量新增
     * @param addList
     */
    int insertBatch(List<EvaluateStudentAbilityModelItemConfigPO> addList);

    /**
     * 根据学生能力模型获取能力项领域对象
     * @param modelItemList
     * @return
     */
    List<StudentAbilityModelItemDO> fillStudentAbilityModelDO(List<EvaluateStudentAbilityModelItemPO> modelItemList);
}