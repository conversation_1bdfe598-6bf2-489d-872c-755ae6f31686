package com.hailiang.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.entity.SpeedConfigRTargetGroupPO;

import java.util.List;

/**
 * 极速点评设置与分组关系仓储层
 */
public interface SpeedConfigRTargetGroupManager extends IService<SpeedConfigRTargetGroupPO> {

    /**
     * 根据极速点评配置ID逻辑删除
     */
    boolean logicalDeleteBySpeedConfigId(Long speedConfigId);

    /**
     * 批量更新分组顺序
     */
    boolean updateBatch(List<SpeedConfigRTargetGroupPO> list);

    /**
     * 根据极速点评配置ID查询指标分组列表
     */
    List<SpeedConfigRTargetGroupPO> listBySpeedConfigId(Long speedConfigId);


}
