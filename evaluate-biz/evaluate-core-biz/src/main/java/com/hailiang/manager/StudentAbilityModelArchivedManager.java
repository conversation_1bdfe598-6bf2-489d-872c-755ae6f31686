package com.hailiang.manager;

import com.hailiang.portrait.entity.StudentAbilityModelArchivedPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 学校能力模型归档表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
public interface StudentAbilityModelArchivedManager extends IService<StudentAbilityModelArchivedPO> {

    StudentAbilityModelArchivedPO getByYear(String campusId, String schoolYear);

    /**
     * 根据校区、学年查询学生能力模型归档配置
     *
     * @param campusId   校区 id
     * @param schoolYear 学年
     */
    List<StudentAbilityModelArchivedPO> listByCampusAndSchoolYear(String campusId, String schoolYear);
}
