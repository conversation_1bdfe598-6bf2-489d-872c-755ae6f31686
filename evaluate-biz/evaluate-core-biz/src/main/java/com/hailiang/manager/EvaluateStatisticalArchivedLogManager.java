package com.hailiang.manager;

import com.hailiang.portrait.entity.EvaluateStatisticalArchivedLogPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;

/**
 * <p>
 * 指标行为统计归档日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface EvaluateStatisticalArchivedLogManager extends IService<EvaluateStatisticalArchivedLogPO> {

    EvaluateStatisticalArchivedLogPO getByTime(Date daily, Integer code);

    void deleteById(Long id);
}
