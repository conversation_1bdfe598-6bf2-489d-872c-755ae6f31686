package com.hailiang.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsCountDTO;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsDeleteDTO;
import com.hailiang.behaviour.classify.statistics.model.entity.BehaviourStudentClassifyStatisticsPO;
import com.hailiang.behaviour.classify.statistics.model.query.BehaviourClassifyStatisticsRecordQO;

import java.util.List;

/**
 * @description: 学生指标分类统计数据交互层
 * @author: pan<PERSON>an
 * @create: 2024/8/30 17:38
 * @Version 1.0
 */
public interface BehaviourTargetStatisticsManager extends IService<BehaviourStudentClassifyStatisticsPO> {

    /**
     * 统计每日数据
     * @param statisticsRecordQO
     * @return
     */
    List<BehaviourClassifyStatisticsCountDTO> queryStatisticsCount(BehaviourClassifyStatisticsRecordQO statisticsRecordQO);

    /**
     * 查询学生统计数据
     * @param statisticsRecordQO
     * @return
     */
    BehaviourStudentClassifyStatisticsPO queryStatisticsRecord(BehaviourClassifyStatisticsRecordQO statisticsRecordQO);

    /**
     *
     * @return
     */
    int deleteStatisticsRecord(BehaviourClassifyStatisticsDeleteDTO deleteDTO);

    /**
     * 批量保存
     * @param studentTargetStatisticsPOS
     * @return
     */
    int insertBatch(List<BehaviourStudentClassifyStatisticsPO> studentTargetStatisticsPOS);
}