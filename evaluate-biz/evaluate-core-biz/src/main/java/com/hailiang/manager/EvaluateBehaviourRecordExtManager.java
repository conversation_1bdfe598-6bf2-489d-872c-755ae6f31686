package com.hailiang.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.entity.EvaluateBehaviourRecordExtPO;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 指标行为记录表扩展表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
public interface EvaluateBehaviourRecordExtManager extends IService<EvaluateBehaviourRecordExtPO> {


    @Transactional(rollbackFor = {Exception.class})
    boolean saveBatchByCustomerSql(Collection<EvaluateBehaviourRecordExtPO> entityList, int batchSize);

    void removeByEvaluateIds(List<Long> ids, String updateBy, Date updateTime);

    List<EvaluateBehaviourRecordExtPO> listByEvaluateIds(List<Long> ids);

    List<EvaluateBehaviourRecordExtPO> listByEvaluateIdsDoris(List<Long> ids);
}
