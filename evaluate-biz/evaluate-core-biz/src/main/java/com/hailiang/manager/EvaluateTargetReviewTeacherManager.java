package com.hailiang.manager;

import com.hailiang.model.entity.EvaluateTargetReviewTeacherPO;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 * 家长提交需审核老师表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
public interface EvaluateTargetReviewTeacherManager extends IService<EvaluateTargetReviewTeacherPO> {

    /**
     * 根据targetId查询审核人
     */
    List<EvaluateTargetReviewTeacherPO> listByTargetId(Long targetId, Integer businessType);

    /**
     * 根据targetId删除审核人
     */
    boolean deleteByTargetId(Long targetId, String updateBy);
}
