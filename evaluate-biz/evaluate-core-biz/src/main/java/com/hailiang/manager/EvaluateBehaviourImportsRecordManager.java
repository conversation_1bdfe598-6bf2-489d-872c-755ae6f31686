package com.hailiang.manager;

import com.hailiang.entity.EvaluateBehaviourImportsRecordPO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 指标行为批量导入记录表 DAO层
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
public interface EvaluateBehaviourImportsRecordManager extends IService<EvaluateBehaviourImportsRecordPO> {

    Boolean saveRecord(EvaluateBehaviourImportsRecordPO record);

    EvaluateBehaviourImportsRecordPO getBySubmitId(Long submitId);
}
