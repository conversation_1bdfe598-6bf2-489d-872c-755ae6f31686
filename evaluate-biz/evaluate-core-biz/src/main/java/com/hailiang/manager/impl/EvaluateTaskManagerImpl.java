package com.hailiang.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.enums.TaskApprovalEnum;
import com.hailiang.enums.TaskStatusEnum;
import com.hailiang.manager.EvaluateTaskManager;
import com.hailiang.mapper.TaskMapper;
import com.hailiang.model.entity.TaskPO;
import com.hailiang.util.WebUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 指标行为统计归档日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class EvaluateTaskManagerImpl extends ServiceImpl<TaskMapper, TaskPO> implements EvaluateTaskManager {


    @Override
    public List<TaskPO> listToDoTask(List<String> studentIds) {
        return list(new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getCampusId, WebUtil.getCampusId())
                //提交人是教职工ID且任务状态为未提交  或者 提交人是学生ID且任务状态为审批中的
                .and(s -> s.eq(TaskPO::getSubmitStaffId, WebUtil.getStaffId())
                        .eq(TaskPO::getTaskStatus, TaskStatusEnum.NOT_SUBMIT.getCode())

                        .or(t -> t.in(TaskPO::getSubmitStaffId, studentIds)
                                .eq(TaskPO::getApprovalStatus, TaskApprovalEnum.APPROVAL_ING.getCode())
                                .eq(TaskPO::getTaskStatus, 2)))
                .orderByDesc(TaskPO::getCreateTime));
    }

}
