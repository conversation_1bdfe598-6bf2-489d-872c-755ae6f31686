package com.hailiang.manager.impl;

import cn.hutool.core.collection.CollUtil;
import com.hailiang.annotation.MethodExecuteTime;
import com.hailiang.enums.DataSourceEnum;
import com.hailiang.manager.DorisBehaviourRecordManager;
import com.hailiang.mapper.doris.DorisBehaviourRecordMapper;
import com.hailiang.model.datastatistics.dto.BehaviourRecordDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.model.processeva.entity.BehaviourRecordEntity;
import com.hailiang.model.response.report.ReportEvaStudentNumResponse;
import com.hailiang.model.response.report.ReportModuleTargetInfoResponse;
import com.hailiang.util.AssertUtil;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class DorisBehaviourRecordManagerImpl implements DorisBehaviourRecordManager {

    @Resource
    private DorisBehaviourRecordMapper dorisBehaviourRecordMapper;

    @MethodExecuteTime
    @Override
    public List<BehaviourRecord> listByInfoIdV4(String infoId) {
        return dorisBehaviourRecordMapper.listByInfoId(infoId);
    }

    @MethodExecuteTime
    @Override
    public List<BehaviourRecord> listByInfoIdV4WithoutPointCompute(String infoId) {
        List<BehaviourRecord> behaviourRecords = dorisBehaviourRecordMapper.listByInfoId(infoId);
        return behaviourRecords.stream().filter(item ->
                        !Objects.equals(item.getDataSource(), DataSourceEnum.EVALUATE_POINT_COMPUTE.getCode()))
                .collect(Collectors.toList());
    }

    /**
     * 根据行为记录id查询行为记录
     *
     * @param behaviourRecordIds
     * @return
     */
    @Override
    public List<BehaviourRecordEntity> listBehaviourRecordByRecordIds(List<Long> behaviourRecordIds) {
        AssertUtil.checkNotEmpty(behaviourRecordIds, "行为记录id不能为空");
        return dorisBehaviourRecordMapper.listBehaviourRecordByRecordIds(behaviourRecordIds);
    }

    @Override
    public List<BehaviourRecord> listRecordsByIds(List<Long> behaviourRecordIds) {
        return dorisBehaviourRecordMapper.listRecordsByIds(behaviourRecordIds);
    }

    @Override
    public List<BehaviourRecordDTO> getRecord(String schoolId, String campusId, String campusSectionId,
                                              String gradeId, String classId, Date startTime, Date endTime,
                                              Integer appraisalType, Long targetId, List<String> studentIdList) {
        return dorisBehaviourRecordMapper.listBehaviourRecordNew(schoolId, campusId, campusSectionId, gradeId, classId,
                startTime, endTime, appraisalType, targetId, studentIdList);
    }

    @Override
    public Map<Long, Integer> getStudentEvaNumGroup(List<Long> studentIds,
                                                    List<Long> targetIds,
                                                    Date startTime,
                                                    Date endTime) {
        if (CollUtil.isEmpty(studentIds)) {
            return Collections.emptyMap();
        }
        if (CollUtil.isEmpty(targetIds)) {
            targetIds.add(-1L);
        }
        List<ReportEvaStudentNumResponse> studentEvaNumList = dorisBehaviourRecordMapper.getStudentEvaNumGroup(
                studentIds,
                targetIds,
                startTime,
                endTime);

        return studentEvaNumList.stream()
                .collect(Collectors.groupingBy(ReportEvaStudentNumResponse::getTargetId,
                        Collectors.summingInt(ReportEvaStudentNumResponse::getStudentCount)));
    }

    @Override
    public List<Long> listSubmitEvaStudentIds(List<Long> studentIds, String targetId,
                                              Date startTime, Date endTime) {
        return dorisBehaviourRecordMapper.listSubmitEvaStudentIds(studentIds, targetId, startTime, endTime);
    }

    @Override
    public List<ReportModuleTargetInfoResponse> listTargetInfo(Long campusId, List<Integer> moduleCodes,
                                                               List<Long> groupIds,
                                                               List<Long> targetIds, List<String> optionIds) {
        if (CollUtil.isEmpty(moduleCodes)) {
            moduleCodes.add(-1);
        }
        if (CollUtil.isEmpty(groupIds)) {
            groupIds.add(-1L);
        }
        if (CollUtil.isEmpty(targetIds)) {
            targetIds.add(-1L);
        }
        if (CollUtil.isEmpty(optionIds)) {
            optionIds.add("-1");
        }
        return dorisBehaviourRecordMapper.listTargetInfo(campusId, moduleCodes, groupIds, targetIds, optionIds);
    }
}
