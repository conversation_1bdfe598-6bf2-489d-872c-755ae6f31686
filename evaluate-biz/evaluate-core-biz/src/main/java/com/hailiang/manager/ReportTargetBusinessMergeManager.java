package com.hailiang.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.report.entity.SubjectEvaluationDimTargetBusinessMergePO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> hljy
 * @date 2023/8/28 13:37
 */
public interface ReportTargetBusinessMergeManager extends IService<SubjectEvaluationDimTargetBusinessMergePO> {

    List<SubjectEvaluationDimTargetBusinessMergePO> queryBusinessTypeAndBusinessIds(Integer businessType, List<Long> businessIds);

    void saveBatch(List<SubjectEvaluationDimTargetBusinessMergePO> list);

    void removeByDimId(Long dimId, String staffId, Date submitDate);

    List<SubjectEvaluationDimTargetBusinessMergePO> queryByDimId(Long dimId);

    void archiveByDimId(Long dimId);
}
