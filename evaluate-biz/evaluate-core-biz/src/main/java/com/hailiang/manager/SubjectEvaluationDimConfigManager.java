package com.hailiang.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.report.entity.SubjectEvaluationDimConfigPO;

import java.util.List;

/**
 * 学科评价维度设置数据库访问层
 *
 * @Description: 学科评价维度设置数据库访问层
 * @Author: Jovi
 * @Date: Created in 2024/12/24
 * @Version: 2.0.0
 */
public interface SubjectEvaluationDimConfigManager extends IService<SubjectEvaluationDimConfigPO> {

    /**
     * 根据租户ID，学校ID，校区ID，学期信息，学段ID和年级ID
     *
     * 查询具体某一个维度的配置，
     * 如果年级ID查询为空，或者状态是不拆分，
     * 则取学段配置，如果查询不为空则直接使用年级查询出的相关配置
     *
     * @param schoolYear
     * @param termName
     * @param campusSectionId
     * @param gradeId
     * @return
     */
    SubjectEvaluationDimConfigPO getSubjectEvaluationDimConfig(
            String tenantId,
            String schoolId,
            String campusId,
            String schoolYear,
            String termName,
            String campusSectionId,
            String gradeId);

    /**
     * 根据学段ID和年级ID查询具体某一个维度的配置， 如果年级ID查询为空，或者状态是不拆分，则取学段配置，如果查询不为空则直接使用年级查询出的相关配置
     *
     * @param campusSectionId
     * @param gradeId
     * @return
     */
    SubjectEvaluationDimConfigPO getSubjectEvaluationDimConfig(String campusSectionId,
                                                               String gradeId);

    /**
     * 根据学段ID和学段CODE查询学科评价维度设置集合信息
     *
     * @param tenantId
     * @param schoolId
     * @param campusId
     * @param schoolYear
     * @param termName
     * @param campusSectionId
     * @param campusSectionCode
     * @return
     */
    List<SubjectEvaluationDimConfigPO> listDimConfigPOsByCampusSection(String tenantId,
                                                                       String schoolId,
                                                                       String campusId,
                                                                       String schoolYear,
                                                                       String termName,
                                                                       String campusSectionId,
                                                                       String campusSectionCode);

    /**
     * 根据学段ID查询学科评价维度设置集合信息
     *
     * @param tenantId
     * @param schoolId
     * @param campusId
     * @param schoolYear
     * @param termName
     * @param campusSectionId
     * @return
     */
    List<SubjectEvaluationDimConfigPO> listDimConfigPOsByCampusSection(String tenantId,
                                                                       String schoolId,
                                                                       String campusId,
                                                                       String schoolYear,
                                                                       String termName,
                                                                       String campusSectionId);

    /**
     * 根据ID查询学科评价维度设置信息
     *
     * @param id
     * @return
     */
    SubjectEvaluationDimConfigPO queryById(Long id);

    /**
     * 归档学科评价维度设置信息ID
     *
     * @param id
     */
    void archive(Long id);

    /**
     * 根据校区ID查询学科评价维度设置分页信息
     *
     * @param campusId
     * @param pageNum
     * @param pageSize
     * @return
     */
    Page<SubjectEvaluationDimConfigPO> queryPage(String campusId, Integer pageNum, Integer pageSize);


    //TODO 有问题需替换
    SubjectEvaluationDimConfigPO queryByCampusSectionId(String campusSectionId);

    /**
     * 根据学段ID查询学科评价维度设置信息
     *
     * @param campusSectionId
     * @return
     */
    List<SubjectEvaluationDimConfigPO> listByCampusSectionId(String campusSectionId);

    /**
     * 根据校区ID查询学科评价维度设置信息
     *
     * @param campusId
     * @param schoolYear
     * @param termName
     * @return
     */
    List<SubjectEvaluationDimConfigPO> queryByCampusId(String campusId, String schoolYear, String termName);


    /**
     * 根据学段ID、年级ID查询对应维度配置，如果参数传值年级ID，则查具体年级的配置，否则查学段配置。
     *
     * @param tenantId
     * @param schoolId
     * @param campusId
     * @param schoolYear
     * @param termName
     * @param campusSectionId
     * @param gradeId
     * @return
     */
    SubjectEvaluationDimConfigPO queryByInfo(String tenantId,
                                             String schoolId,
                                             String campusId,
                                             String schoolYear,
                                             String termName,
                                             String campusSectionId,
                                             String gradeId);

    /**
     * 根据学段ID、学年、学期查询学科评价维度设置信息 TODO 有问题
     *
     * @param tenantId
     * @param schoolId
     * @param campusId
     * @param schoolYear
     * @param termName
     * @param campusSectionId
     * @return
     */
    SubjectEvaluationDimConfigPO queryByTermName(String tenantId, String schoolId, String campusId,
                                                 String schoolYear, String termName, String campusSectionId);


    /**
     * 根据学段ID、学年、学期、学段查询学科评价维度设置信息
     * @param tenantId
     * @param schoolId
     * @param campusId
     * @param schoolYear
     * @param termName
     * @param campusSectionCode
     * @return
     */
    public List<SubjectEvaluationDimConfigPO> queryByTermNameAndSectionCode(String tenantId,
                                                                      String schoolId,
                                                                      String campusId,
                                                                      String campusSectionCode);
}
