package com.hailiang.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsCountDTO;
import com.hailiang.behaviour.classify.statistics.model.query.BehaviourClassifyStatisticsRecordQO;
import com.hailiang.model.dto.BehaviourRecordBatchQueryDTO;
import com.hailiang.model.dto.behaviour.help.HelpBehaviourRecordQueryDTO;
import com.hailiang.model.entity.EvaluateHelpBehaviourRecordPO;
import com.hailiang.model.vo.BehaviourStudentFileVO;
import com.hailiang.portrait.entity.StudentDailyStatisticsPO;
import com.hailiang.portrait.query.StuPortraitQuery;

import java.util.Date;
import java.util.List;

/**
 * @description: 指标行为记录表-师徒帮扶 服务类
 * @author: panjian
 * @create: 2024/9/19 10:23
 * @Version 1.0
 */
public interface EvaluateHelpBehaviourRecordManager  extends IService<EvaluateHelpBehaviourRecordPO> {

    /**
     * 条件查询
     * @param recordQueryDTO
     * @return
     */
    List<EvaluateHelpBehaviourRecordPO> queryByCondition(HelpBehaviourRecordQueryDTO recordQueryDTO);

    /**
    * @Description: 查询学生每日统计数据
    * @Author: panjian
    * @Date: 2024/9/23 10:53
    */
    List<StudentDailyStatisticsPO> listStudentDailyStatisticsFromDrois(StuPortraitQuery dto);

    /**
     * 学生画像列表
     * @param dto
     * @return
     */
    List<BehaviourStudentFileVO> listStudentHelpPortrait(BehaviourRecordBatchQueryDTO dto);

    /**
     * 统计每日数据
     * @param statisticsRecordQO
     * @return
     */
    List<BehaviourClassifyStatisticsCountDTO> queryStatisticsCountFromDoris(BehaviourClassifyStatisticsRecordQO statisticsRecordQO);

    boolean removeBatchByIds(List<Long> removeIds, String updateBy, Date updateTime);
}
