package com.hailiang.manager;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hailiang.model.entity.SysUserThirdPlatform;
import com.hailiang.model.dto.SysUserThirdPlatformDTO;

  
/**
 * 接口
 *
 * <AUTHOR> 2024-02-26 14:02:30
 */
public interface SysUserThirdPlatformManager extends IService<SysUserThirdPlatform>{

    IPage pageSysUserThirdPlatform(Page page, SysUserThirdPlatformDTO sysUserThirdPlatformDTO);

    boolean saveSysUserThirdPlatform(SysUserThirdPlatformDTO sysUserThirdPlatformDTO);

    boolean updateSysUserThirdPlatform(SysUserThirdPlatformDTO sysUserThirdPlatformDTO);

    boolean deleteSysUserThirdPlatform(SysUserThirdPlatformDTO sysUserThirdPlatformDTO);

}