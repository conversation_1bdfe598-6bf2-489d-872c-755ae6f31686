package com.hailiang.manager;

import com.hailiang.entity.EvaluateBehaviourImportsRecordDetailPO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 指标行为批量导入记录详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface EvaluateBehaviourImportsRecordDetailManager extends IService<EvaluateBehaviourImportsRecordDetailPO> {

    List<EvaluateBehaviourImportsRecordDetailPO> listByInfoId(Long infoId);

    @Transactional(rollbackFor = {Exception.class})
    boolean saveBatchByCustomerSql(Collection<EvaluateBehaviourImportsRecordDetailPO> entityList, int batchSize);
}
