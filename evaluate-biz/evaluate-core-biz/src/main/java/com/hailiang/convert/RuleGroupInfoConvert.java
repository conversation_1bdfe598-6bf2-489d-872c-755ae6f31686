package com.hailiang.convert;

import com.hailiang.model.dto.activity.detail.MedalDetailVO;
import com.hailiang.model.dto.activity.rule.bo.RuleGroupInfoBO;
import com.hailiang.model.dto.activity.rule.query.RuleGroupInfoVO;
import com.hailiang.model.entity.MedalUserAcquireRecord;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @description:
 * @author: panjian
 * @create: 2024/10/21 17:43
 * @Version 1.0
 */
@Mapper(componentModel = "spring")
public interface RuleGroupInfoConvert {

    List<RuleGroupInfoVO> toRuleGroupInfoVOList(List<RuleGroupInfoBO> ruleGroupInfoBOList);
}