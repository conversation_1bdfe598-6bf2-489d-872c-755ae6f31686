package com.hailiang.convert;

import com.hailiang.model.dto.SortCheckItemDTO;
import com.hailiang.model.entity.CheckItemPO;
import com.hailiang.model.vo.CheckItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/20 17:43
 */
@Mapper(componentModel = "spring")
public interface CheckItemConvert {

    @Mappings({@Mapping(target = "itemName", source = "name")})
    CheckItemVO toCheckItemVO(CheckItemPO item);

    List<CheckItemPO> toCheckItems(List<SortCheckItemDTO> dtoList);
}
