package com.hailiang.convert;

import com.hailiang.model.dto.activity.detail.MedalDetailVO;
import com.hailiang.model.entity.MedalUserAcquireRecord;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/26 14:22
 */
@Mapper(componentModel = "spring")
public interface MedalDetailConvert {


    List<MedalDetailVO> toMedalDetailVOList(List<MedalUserAcquireRecord> userAcquireRecords);
}
