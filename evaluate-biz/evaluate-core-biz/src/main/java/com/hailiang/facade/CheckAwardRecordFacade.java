package com.hailiang.facade;

import com.hailiang.model.dto.*;
import com.hailiang.model.dto.query.ClassCompareLevelListDTO;
import com.hailiang.model.entity.CheckAwardRecord;
import com.hailiang.model.entity.CheckClassAwardRecord;
import com.hailiang.model.vo.*;
import com.hailiang.util.R;

import java.util.List;

/**
 * <AUTHOR> gaoxin
 * @create 2023/8/3 14:58
 */
public interface CheckAwardRecordFacade {

    String getLastAwardTime(String schoolYear);

    Integer getLastAwardType(String schoolYear);

    CheckAwardRecordVO findOne(String id);

    R<RedFlagDTO> recallRedFlag(RecallRedFlagDTO dto);

    CheckAwardRecordScoreVO queryScore(String id);

    R<RedFlagDTO> awardRedFlag(AwardRedFlagDTO dto);

    CheckAwardRecordDetailVO detail(String id);

    List<SearchCheckAwardRecordVO> search(SearchCheckAwardRecordDTO dto);

    RedFlagDisplayConfigVO queryDisplayConfig(ClassCompareLevelListDTO dto);

    void asyncNotifyHeadTeacher(Integer eventType, CheckAwardRecord record, List<CheckClassAwardRecord> classRecords,
                                Long schoolId, Long campusId);
}
