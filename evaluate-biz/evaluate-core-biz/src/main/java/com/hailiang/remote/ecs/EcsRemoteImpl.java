package com.hailiang.remote.ecs;

import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> gaoxin
 * @create 2023/8/8 12:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EcsRemoteImpl implements EcsRemote {

    final RestTemplate restTemplate;
    @Value("${third.ecs.baseUrl}")
    private String baseUrl;
    @Value("${third.ecs.isOpenEcs}")
    private String isOpenEcs;

    @Override
    public Boolean isOpenEcsByCampusId(String campusId) {

        String url = baseUrl.concat(isOpenEcs);

        JSONObject request = new JSONObject();
        request.set("campusId", campusId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        try {
            log.info("调用班排系统  url:{},request:{}", url, request);
            EcsResponse response = restTemplate.postForObject(url, new HttpEntity<>(request, headers), EcsResponse.class);
            log.info("班排系统返回:{}", response);
            if (response != null && response.getStatus() == 200) return response.getData().getOpenFlag();
        } catch (Exception e) {
            log.warn("调用班排系统出现异常", e);
        }
        return null;
    }
}
