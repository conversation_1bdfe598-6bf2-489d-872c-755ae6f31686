package com.hailiang.remote.saas.vo.auth;

import lombok.Data;

/**
 * 用户数据权限-拥有的班级权限信息
 *
 * <AUTHOR>
 * @date 2023/8/9 17:12
 */
@Data
public class StaffDataClassVO {

    /**
     * 班级id
     */
    private Long id;

    private Long gradeId;

    private Long schoolId;

    private String gradeCode;

    private Long campusId;

    private String gradeName;

    private String className;

    private String classNum;

    /**
     * 班级类型：0行政班、1选考班、2学考班、3选修班、4兴趣班
     */
    private String classType;

    /**
     * 升级状态:0正常1已毕业
     */
    private String upgradeStatus;

}
