package com.hailiang.remote.saas.vo.menu;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/4 16:06
 */
@Data
public class MenuAuthVO {

    /**
     * 操作按钮类型（1-查询 2-增改 3-删除）
     */
    private String action;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 权限字符
     */
    private String authCode;

    /**
     * 子节点
     */
    private List<MenuAuthVO> childList;

    /**
     * 图标
     */
    private String icon;

    /**
     * 主键
     */
    private String id;

    /**
     * 图片
     */
    private String imgUrl;

    /**
     * 菜单或按钮名称
     */
    private String label;

    /**
     * 类型（1-菜单 2-按钮）
     */
    private String menuType;

    /**
     * 父级菜单id
     */
    private String parentId;

    /**
     * 路径
     */
    private String path;

    /**
     * 排序字段
     */
    private String sort;
}
