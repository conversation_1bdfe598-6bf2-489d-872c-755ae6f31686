package com.hailiang.remote.saas.pojo.educational;

import lombok.Data;

@Data
public class EduClassBaseInfoPojo {

    /**
     * 班级id
     */
    private Long id;

    /**
     * 班主任id
     */
    private Long headMasterId;

    /**
     * 班主任名称
     */
    private String headMasterName;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 班级类型：0行政班、1选考班、2学考班、3选修班、4兴趣班
     */
    private String classType;

    /**
     * 毕业状态（0未毕业1已毕业）
     */
    private String graduationStatus;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 校区id
     */
    private Long campusId;
    /**
     * 学校id
     */
    private Long schoolId;

    public EduClassBaseInfoPojo() {
    }

    public EduClassBaseInfoPojo(Long id, Long headMasterId, String headMasterName, String className, String classType, Long tenantId) {
        this.id = id;
        this.headMasterId = headMasterId;
        this.headMasterName = headMasterName;
        this.className = className;
        this.classType = classType;
        this.tenantId = tenantId;
    }
}