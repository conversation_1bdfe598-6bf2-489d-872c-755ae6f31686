package com.hailiang.remote.saas.vo.educational;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 学科vo
 *
 * <AUTHOR>
 * @since 2023-02-09 17:12:00
 */
@Data
public class TchSubjectInfoVO {

    @ApiModelProperty(value="主键ID")
    private Long id;

    @ApiModelProperty(value="学科code")
    private String subjectCode;

    @ApiModelProperty(value="学科名称")
    private String subjectName;

    @ApiModelProperty(value="状态：0启用  1停用")
    private Integer status;

    @ApiModelProperty(value="是否初始化科目：0否1是(不能编辑科目名称)")
    private Integer isInit;

    @ApiModelProperty(value="学段ids(多个以逗号分隔)")
    private String sectionCodes;

    @ApiModelProperty(value="学段names(多个以逗号分隔)")
    private String sectionNames;
}