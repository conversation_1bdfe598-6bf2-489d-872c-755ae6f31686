package com.hailiang.remote.saas.vo.educational;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hailiang.internal.model.response.PlanListsResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EduOrgTreeVO implements Serializable {
    private static final long serialVersionUID = 1905122041950251207L;
    /**
     * 教务组织节点id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 父节点id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 组织节点类型（1学校2校区3学段4年级5班级）
     */
    private Integer type;

    /**
     * 组织节点名称
     */
    private String name;
    /**
     * 组织节点code
     */
    private String code;
    /**
     * 学生人数
     */
    private Integer studentNum;

    /**
     * 子组织机构集合
     */
    private List<EduOrgTreeVO> children;

    /**
     * 积分版
     */
    private List<PlanListsResponse> planLists;

    public EduOrgTreeVO() {
    }

    public EduOrgTreeVO(Long id, Long parentId, Integer type, String name) {
        this.id = id;
        this.parentId = parentId;
        this.type = type;
        this.name = name;
    }

    public EduOrgTreeVO(Long id, Long parentId, Integer type, String name, Integer studentNum) {
        this.id = id;
        this.parentId = parentId;
        this.type = type;
        this.name = name;
        this.studentNum = studentNum;
    }
}
