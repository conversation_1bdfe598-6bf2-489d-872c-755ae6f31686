package com.hailiang.remote.internaldrive.handler.merge;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.hailiang.enums.PointMqDataOperateTypeEnum;
import com.hailiang.enums.motivate.CoinExchangeOperateTypeEnum;
import com.hailiang.enums.motivate.CoinExchangeStrategyEnum;
import com.hailiang.logic.BehaviourRecordManager;
import com.hailiang.manager.EvaluateBehaviourRecordExtManager;
import com.hailiang.manager.EvaluateHelpBehaviourRecordManager;
import com.hailiang.model.dto.ThirdDataInfoDTO;
import com.hailiang.model.entity.BehaviourRecord;
import com.hailiang.remote.internaldrive.Record;
import com.hailiang.service.ActivityRuleMatchService;
import com.hailiang.service.ConvertInitialService;
import com.hailiang.service.InfoService;
import com.hailiang.service.TargetGroupService;
import com.hailiang.service.ThirdDataInfoService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.hailiang.enums.InfoTypeEnum.SPEED_OPTION_SCHOOL_SCHOOL;

/**
 * 星动力数据批量新增处理器
 *
 * @Description: 星动力数据批量新增处理器
 * @Author: Jovi
 * @Date: Created in 2024/7/15
 * @Version: 2.0.0
 */
@Slf4j
public class XDLMergeRecordBatchRemoveHandler extends ExternalDataBatchHandler {

    @Override
    public void doHandle(List<Record> records) {

        // 原始需要删除的点评记录（包含个人和帮扶）
        List<Record> needDealRecords = new ArrayList<>();

        // 需要处理的行为点评记录（包含个人和帮扶）
        List<BehaviourRecord> needDealBehaviourRecords = new ArrayList<>();

        //包含个人和帮扶行为点评记录ID
        List<Long> needDealRecordIds = new ArrayList<>();

        //1、过滤出需要处理的记录
        batchDeal(records, needDealRecords, needDealBehaviourRecords, needDealRecordIds);

        if (CollUtil.isEmpty(needDealRecords)) {
            log.warn("【星动力（融合）-pipeline】-【0.4.2】-【明细删除】-【没有可处理记录】");
            return;
        }

        log.info("【星动力（融合）-pipeline】-【0.4.2】-【明细删除】-【成功记录条数】：【{}】", needDealRecords.size());

        List<BehaviourRecord> needDealExistsBehaviourRecordPOs = SpringUtil.getBean(BehaviourRecordManager.class).listByIds(needDealRecordIds);

        //2、批量处理需要删除的点评记录（行为记录表、分类拓展表、帮扶记录表）
        batchDealRemove(needDealBehaviourRecords, needDealRecordIds);

        //3、批量处理点评转积分情况
        batchToPoint(needDealBehaviourRecords, needDealExistsBehaviourRecordPOs);

        //4、批量处理活动规则匹配
        SpringUtil.getBean(ActivityRuleMatchService.class)
                .matchProcess(needDealExistsBehaviourRecordPOs.stream().map(BehaviourRecord::getId).collect(Collectors.toList()));

        //5、批量处理第三方表
        batchThirdData(needDealRecords);
    }

    /**
     * 批量处理第三方表
     *
     * @param needDealRecords
     */
    private void batchThirdData(List<Record> needDealRecords) {
        List<ThirdDataInfoDTO> thirdDataInfoDTOs = new ArrayList<>();
        for (Record record : needDealRecords) {
            ThirdDataInfoDTO thirdDataInfoDTO = ThirdDataInfoDTO
                    .builder()
                    .id(record.getThirdDataInfoPO().getId())
                    .localRecordId(record.getBehaviourRecordPO().getId())
                    .campusId(record.getBehaviourRecordPO().getCampusId())
                    .build();
            thirdDataInfoDTOs.add(thirdDataInfoDTO);
        }
        // 4、更新第三方数据状态为成功
        SpringUtil.getBean(ThirdDataInfoService.class).batchUpdateSuccess(thirdDataInfoDTOs);
    }

    /**
     * 批量处理点评转积分情况
     *
     * @param needDealBehaviourRecords
     * @param needDealExistsBehaviourRecordPOs
     */
    private void batchToPoint(List<BehaviourRecord> needDealBehaviourRecords, List<BehaviourRecord> needDealExistsBehaviourRecordPOs) {
        String campusId = needDealBehaviourRecords.get(0).getCampusId();
        List<BehaviourRecord> toPointRecords = new ArrayList<>();
        Integer pointSwitch = SpringUtil.getBean(TargetGroupService.class).listPointSwitch(campusId);

        if (Objects.equals(pointSwitch, 0)) {

            List<BehaviourRecord> behaviourRecordList = needDealExistsBehaviourRecordPOs.stream()
                    .filter(item -> Objects.equals(item.getInfoType(), SPEED_OPTION_SCHOOL_SCHOOL.getCode()))
                    .collect(Collectors.toList());
            toPointRecords.addAll(behaviourRecordList);
        } else {

            List<BehaviourRecord> behaviourRecordList = needDealExistsBehaviourRecordPOs
                    .stream()
                    .filter(item -> Objects.equals(campusId, item.getCampusId()))
                    .collect(Collectors.toList());

            toPointRecords.addAll(behaviourRecordList);
        }

        ConvertInitialService convertInitialService = SpringUtil.getBean(ConvertInitialService.class);
        convertInitialService.sendMQ(1, 3, toPointRecords, null);
        // 发送新版积分转换
        convertInitialService.sendBehaviourExchangeMq(SpringUtil.getBean(InfoService.class)
                .assemblyPointExchangeMq(toPointRecords, null, CoinExchangeOperateTypeEnum.DELETE.getCode(),
                        CoinExchangeStrategyEnum.XDL_EVALUATE.getCode()));
    }

    /**
     * 批量处理需要删除的点评记录（行为记录表、分类拓展表、帮扶记录表）
     *
     * @param needDealBehaviourRecords
     * @param needDealRecordIds
     */
    private void batchDealRemove(List<BehaviourRecord> needDealBehaviourRecords, List<Long> needDealRecordIds) {
        String updateBy = needDealBehaviourRecords.get(0).getUpdateBy();
        Date updateTime = needDealBehaviourRecords.get(0).getUpdateTime();


        SpringUtil.getBean(EvaluateHelpBehaviourRecordManager.class)
                .removeBatchByIds(
                        needDealRecordIds,
                        updateBy,
                        updateTime);

        SpringUtil.getBean(BehaviourRecordManager.class)
                .removeBatchByIds(
                        needDealRecordIds,
                        updateBy,
                        updateTime);

        SpringUtil.getBean(EvaluateBehaviourRecordExtManager.class)
                .removeByEvaluateIds(
                        needDealRecordIds,
                        updateBy,
                        updateTime);
    }

    /**
     * 批量过滤处理无需删除的记录
     *
     * @param records
     * @param needDealRecords
     * @param needDealBehaviourRecords
     * @param needDealRecordIds
     */
    private void batchDeal(List<Record> records,
                           List<Record> needDealRecords,
                           List<BehaviourRecord> needDealBehaviourRecords,
                           List<Long> needDealRecordIds) {

        for (Record record : records) {

            //如果为空说明是删除的历史的点评行为记录，没有存储过到星未来行为记录表，无需删除动作
            if (BeanUtil.isEmpty(record.getBehaviourRecordPO())
                    || ObjectUtil.isEmpty(record.getBehaviourRecordPO().getId())) {
                continue;
            }

            needDealRecords.add(record);
            needDealBehaviourRecords.add(record.getBehaviourRecordPO());
            needDealRecordIds.add(record.getBehaviourRecordPO().getId());
        }
    }
}
