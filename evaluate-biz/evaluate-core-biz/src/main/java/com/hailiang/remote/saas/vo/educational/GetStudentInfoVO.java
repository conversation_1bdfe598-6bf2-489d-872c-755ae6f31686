package com.hailiang.remote.saas.vo.educational;

import com.hailiang.remote.saas.pojo.educational.EduClassBaseInfoPojo;
import com.hailiang.remote.saas.pojo.educational.EduParentInfoPojo;
import lombok.Data;

import java.util.List;

@Data
public class GetStudentInfoVO {
    /**
     * 学生id
     */
    private Long studentId;


    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 性别：0未知；1男；2女
     */
    private Integer sex;

    /**
     * 年级id
     */
    private String gradeId;

    /**
     * 年级名称
     */
    private Long gradeName;

    /**
     * 班级id
     */
    private Long classId;

    /**
     * 班级名称
     */
    private Long className;

}