package com.hailiang.remote.saas.vo.history;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/7 10:06
 */
@Data
public class SchoolYearStudentVO {

    private Long studentId;

    private Long tenantId;

    private String studentName;

    private String schoolYear;

    private Integer sex;

    private String studentNo;

    private Long schoolId;

    private String schoolName;

    private Long gradeId;

    private String gradeCode;

    private String gradeName;

    private String startYear;

    /**
     * 历史班级Id
     */
    private Integer hisClassId;

    private Long classId;

    private String className;

    private Long campusId;

    private String campusName;

    /**
     * 班级层次Id
     */
    private Long classLevelId;

    /**
     * 班级层次
     */
    private String classLevelName;

    /**
     * 班级分科code
     */
    private String classDivisionsCode;

    /**
     * 班级分科value
     */
    private String classDivisionsValue;

    /**
     * 该学生所在历史班级集合，包括行政班和教学班
     */
    private List<Integer> hisClassIdList;

    /**
     * 是否是毕业生
     */
    private Boolean isGradUateStudent;

}
