package com.hailiang.remote.saas.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 教务组织树，endType类型
 */
public enum EndTypeEnum {
    SCHOOL(1, 0, 2, "学校"),
    CAMPUS(2, 1, 3, "校区"),
    SECTION(3, 2, 4, "学段"),
    GRA<PERSON>(4, 3, 5, "年级"),
    CLASS(5, 4, 0, "班级");

    private Integer code;
    private Integer preCode;
    private Integer nextCode;
    private String desc;

    private EndTypeEnum(Integer code, Integer preCode, Integer nextCode, String desc) {
        this.code = code;
        this.preCode = preCode;
        this.nextCode = nextCode;
        this.desc = desc;
    }

    public Integer getPreCode() {
        return this.preCode;
    }

    public Integer getCode() {
        return this.code;
    }

    public Integer getNextCode() {
        return this.nextCode;
    }

    public String getDesc() {
        return this.desc;
    }

    public static List<Integer> getCodes() {
        return Arrays.asList(SCHOOL.getCode(), CAMPUS.getCode(), SECTION.getCode(), GRADE.getCode(), CLASS.getCode());
    }

    public static EndTypeEnum getByCode(Integer code) {
        for (int i = 0; i < values().length; i++) {
            if (values()[i].code.equals(code)) {
                return values()[i];
            }
        }
        return null;
    }
}
