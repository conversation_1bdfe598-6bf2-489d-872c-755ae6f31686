/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.remote.saas.vo.educational;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version : RoomBedVO.java, v 0.1 2023年07月25日 20:33  <PERSON><PERSON> Zhu Exp $
 */
@Data
public class RoomBedVO {


    @ApiModelProperty(value = "床位：平铺时该值默认为1，上下铺时对应的是床号")
    private Integer row;

    @ApiModelProperty(value = "床位：平铺时该值是床号，上下铺时：上铺为1，下铺为2")
    private Integer col;

    @ApiModelProperty(value = "房间id")
    private Long roomId;

    @ApiModelProperty(value = "房间名称")
    private String roomName;

    @ApiModelProperty(value = "床铺类型 1-上下铺 2-平铺")
    private Integer bedType;


}

