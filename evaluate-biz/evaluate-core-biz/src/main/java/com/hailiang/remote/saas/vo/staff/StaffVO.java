package com.hailiang.remote.saas.vo.staff;

import com.hailiang.enums.SaasQueryTypeEnum;
import com.hailiang.remote.saas.vo.administration.AdminOrgAndFullPathVO;
import com.hailiang.remote.saas.vo.educational.EduStaffClassVO;
import com.hailiang.remote.saas.vo.educational.EduStaffMasterClassVO;
import com.hailiang.remote.saas.vo.educational.EduStaffTeachClassVO;
import lombok.Data;

import java.util.List;

@Data
public class StaffVO {

    /**
     * 员工id
     */
    private Long staffId;

    /**
     * 员工工号
     */
    private String staffCode;

    /**
     * 员工姓名
     */
    private String staffName;


    /**
     * 员工手机号
     */
    private String mobile;
    /**
     * 员工邮箱
     */
    private String mailbox;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 员工性别
     */
    private String sex;
    /**
     * 状态(0正常1离职)
     */
    private Integer state;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 查询类型
     */
    private Integer queryType = SaasQueryTypeEnum.STAFF.getCode();
    /**
     * 员工所属组织全路径
     */
    private List<AdminOrgAndFullPathVO> adminOrgAndFullPathVOS;

    /**
     * 员工任教情况
     */
    private List<EduStaffTeachClassVO> teachClassInfos;

    /**
     * 任教学段学科拼接
     */
    private List<String> sectionSubjectInfo;
}