/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.remote.saas.vo.staff;

import java.util.List;

import com.lark.oapi.core.utils.Lists;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 教师员工对象: ClassTeacherVO.java, v 0.1 2023年10月11日 20:12  Baojiang Yang Exp $
 */
@Data
public class ClassTeacherVO {
    /**
     * 班主任集合
     */
    private ClassStaffVO       masterTeacher;

    /**
     * 科任老师集合
     */
    private List<ClassStaffVO> subjectTeacherList = Lists.newArrayList();
}