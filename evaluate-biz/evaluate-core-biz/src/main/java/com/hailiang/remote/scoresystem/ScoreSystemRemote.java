package com.hailiang.remote.scoresystem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.report.vo.ExamInfoPOVO;
import com.hailiang.model.report.vo.ReportSubjectScoreVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/8
 */
public interface ScoreSystemRemote {

    /**
     * 根据测验 id 获取测验分数
     * @param id
     * @return
     */
    List<ReportSubjectScoreVO> listTestScoresById(Long id);

    /**
     * 根据测验 id 获取测验信息
     * @param id
     * @return
     */
    ExamInfoPOVO getTestInfoById(Long id);

    /**
     * 根据考试 id 获取考试信息
     * @param id
     * @return
     */
    ExamInfoPOVO getExamInfoById(Long id);

    /**
     * 根据考试 id 分页获取考试成绩
     * @param id
     * @param map
     * @return
     */
    Page<ReportSubjectScoreVO> getExamScoreById(Long id, Map map);

    /**
     *
     * @param id
     * @param map
     * @return
     */
    Page<ReportSubjectScoreVO> getExamScoreByIdV2(Long id, Map map);
}
