package com.hailiang.remote.internaldrive;

import cn.hutool.json.JSONArray;
import com.hailiang.pipeline.ProcessModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @date 2021/11/22
 * @description 发送消息任务模型
 */
@Data
public class TargetNew {
    /**
     * 事件编号
     */
    private String eventCode;
    /**
     * 上游服务
     */
    private String upstream;
    /**
     * 数据类型
     */
    private String dataType;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 任务id
     */
    private String taskId;
    /**
     * 跟上游约定好的内容，结构类型必须是数组
     */
    private JSONArray content;
}
