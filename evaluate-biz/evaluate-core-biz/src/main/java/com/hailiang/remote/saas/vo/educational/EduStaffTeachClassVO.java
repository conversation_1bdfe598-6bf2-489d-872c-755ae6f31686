package com.hailiang.remote.saas.vo.educational;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

@Data
public class EduStaffTeachClassVO {

    /**
     * 学段名称
     */
    private String sectionName;

    /**
     * 教师授课科目集合
     */
    private List<EduStaffSubjectVO> subjects;

    /**
     * 班级id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 班级类型：0行政班、1选考班、2学考班、3选修班、4兴趣班
     */
    private String classType;

    /**
     * 班级名称
     */
    private String className;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long gradeId;

    private Long gradeCode;

    private String gradeName;

    private String sectionCode;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long campusId;

    private String campusName;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long schoolId;


    /**
     * 需要弹窗选择的指标
     */
    private List<Long> needChooseTargets;
    /**
     * 不需要弹窗选择的指标
     */
    private List<Long> noNeedChooseTargets;
    /**
     * 无需弹窗选择的指标的学科默认值
     */
    private String defaultSubjectCode;
}