/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.remote.saas.enums;

import com.hailiang.saas.uc.api.util.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> Yang
 * @version 教师类型枚举: TeacherTypeEnum.java, v 0.1 2023年10月11日 20:14  Baojiang Yang Exp $
 */
@Getter
@AllArgsConstructor
public enum TeacherTypeEnum {
    HEADMASTER("0", "班主任"),
    CLASS_TEACHER("1", "任课教师"),
    ;

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static TeacherTypeEnum getByCode(String code) {
        AssertUtil.checkNotBlank(code, "code不能为空");
        for (TeacherTypeEnum value : TeacherTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    private final String code;
    private final String msg;
}
