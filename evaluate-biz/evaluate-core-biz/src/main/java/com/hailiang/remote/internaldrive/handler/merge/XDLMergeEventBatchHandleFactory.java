package com.hailiang.remote.internaldrive.handler.merge;

import cn.hutool.core.lang.Singleton;
import com.hailiang.enums.EventCodeEnum;
import org.springframework.stereotype.Component;

/**
 * 「星动力」融合点评记录明细处理器工厂
 *
 * @Description: 「星动力」融合点评记录明细处理器工厂
 *
 * @Author: Jovi
 * @Date: Created in 2024/7/4
 * @Version: 2.0.0
 */
@Component
public class XDLMergeEventBatchHandleFactory {
    public ExternalDataBatchHandler getHandle(String eventCode){
        if (EventCodeEnum.XDL_BEHAVIOUR_ADD_RECORD.getCode().equals(eventCode)) {
            return Singleton.get(XDLMergeRecordBatchAddHandler.class);
        } else {
            return Singleton.get(XDLMergeRecordBatchRemoveHandler.class);
        }
    }
}
