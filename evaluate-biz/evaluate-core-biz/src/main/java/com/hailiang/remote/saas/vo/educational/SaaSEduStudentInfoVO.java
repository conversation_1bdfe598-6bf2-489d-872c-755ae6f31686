package com.hailiang.remote.saas.vo.educational;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Date：2022-01-05
 * Time：19:29
 * Description：学生列表VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("学生列表VO")
@NoArgsConstructor
public class SaaSEduStudentInfoVO {
    @ApiModelProperty(value = "学生id")
    private Long id;

    @ApiModelProperty(value = "学号")
    private String studentNo;

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "性别：0未知；1男；2女")
    private Integer sex;

    @ApiModelProperty(value = "毕业状态（0未毕业1已毕业）")
    private String graduationStatus;

    @ApiModelProperty(value = "学生状态：0正常1休学")
    private String studentStatus;

    @ApiModelProperty(value = "学生对应的床位列表")
    List<RoomBedVO> roomBedList;

    @ApiModelProperty(value = "学生对应的班级列表")
    List<ClassSimpleVO> classList;

    @ApiModelProperty(value = "学生对应的照片")
    private String faceImage;


    public SaaSEduStudentInfoVO(Long id, String studentNo, String studentName, Integer sex, String graduationStatus, String studentStatus) {
        this.id = id;
        this.studentNo = studentNo;
        this.studentName = studentName;
        this.sex = sex;
        this.graduationStatus = graduationStatus;
        this.studentStatus = studentStatus;
    }
}