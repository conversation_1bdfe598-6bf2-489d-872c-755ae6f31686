package com.hailiang.remote.saas;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hailiang.util.RequestUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class SaasRequestUtil {


    @Value("${open.saas-api-url}")
    private String saasApiUrl;

    //开放平台url
    @Value("${open.saas-web-bff-url}")
    private String openApiUrl;

    //开放平台网关
    @Value("${open.feign.sys}")
    private String feignSysUrl;

    @Value("${open.appId}")
    private String appId;

    public JSONObject post(String[] urlConstants, String requestBody) {
        return postDefaultTimeout(urlConstants, requestBody, null);
    }

    public JSONObject postCustomTimeout(String[] urlConstants, String requestBody, int timeout) {
        return postDefaultTimeout(urlConstants, requestBody, timeout);
    }

    private JSONObject postDefaultTimeout(String[] urlConstants, String requestBody, Integer timeout) {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.restart();
        Integer status;
        try {

            log.info("Saas远程请求路径【{}】-请求参数：【{}】",
                    saasApiUrl + urlConstants[0], StrUtil.subWithLength(requestBody, 0, 500));

            HttpResponse response = HttpRequest.post(saasApiUrl + urlConstants[0])
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("appId", appId)
                    .body(requestBody)
                    .timeout(timeout == null ? 10000 : timeout)
                    .execute();
            log.info("Saas远程请求路径-【{}】-请求参数：【{}】-返回信息：【{}】-接口耗时：【{}】",
                    saasApiUrl + urlConstants[0],
                    StrUtil.subWithLength(requestBody, 0, 500),
                    StrUtil.subWithLength(JSONUtil.toJsonStr(response.body()), 0, 5000),
                    timeInterval.intervalMs());

            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            status = Convert.toInt(jsonObject.get("status"));

            if (ObjectUtil.notEqual(status, SaasErrorCode.SUCCESS)) {
                if ((Objects.nonNull(status)) && urlConstants[0].contains("doLogin") &&
                        (10012 == status || 3001 == status || 500 == status || 10013 == status)) {
                    log.warn("Saas远程请求发生错误，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                } else if ((Objects.nonNull(status)) && 10000 == status && urlConstants[0].contains("token/auth")) {
                    log.warn("Saas远程请求发生错误，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                } else if ((Objects.nonNull(status)) && urlConstants[0].contains("feiShuLogin") && (10013 == status || 3001 == status || 500 == status)) {
                    log.warn("Saas远程请求发生错误，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                } else if ((Objects.nonNull(status)) && 500 == status && urlConstants[0].contains("query_base_info")) {
                    log.warn("Saas远程请求发生错误，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                } else if ((Objects.nonNull(status)) && 500 == status && urlConstants[0].contains("educational/org/v1/tree/list")) {
                    log.warn("Saas远程请求发生错误，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                } else if (urlConstants[0].contains("faceQuery")) {
                    log.warn("Saas远程请求发生错误，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                } else if ((Objects.nonNull(status)) && 500 == status && urlConstants[0].contains("educational/teachManage/v1/student/list")) {
                    log.warn("Saas远程请求发生错误，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                } else {
                    log.error("Saas远程请求发生错误，错误信息={}，请求url:{}，请求参数:{}，请求头:{}",
                            JSONUtil.toJsonStr(jsonObject),
                            saasApiUrl + urlConstants[0],
                            requestBody,
                            RequestUtil.getAllHeaders());
                }
                return jsonObject;
            }
            return jsonObject;
        } catch (Exception e) {
            log.error("请求saas接口时发生异常,请求url:「{}」，请求参数:「{}」，请求头:「{}」，异常信息：「{}」",
                    saasApiUrl + urlConstants[0],
                    requestBody,
                    RequestUtil.getAllHeaders(),
                    e.getMessage(), e
            );
        }
        return new JSONObject();
    }

    public JSONObject openApiGetMenuAuth(String[] urlConstants, String requestBody) {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.restart();
        Integer status;
        try {
            String url = feignSysUrl + urlConstants[0] + "?schoolId=" + WebUtil.getSchoolId() + "&tenantId=" + WebUtil.getTenantId() + "&staffId=" + WebUtil.getStaffId() + "&appId=" + appId;
            HttpResponse response = HttpRequest.get(url).execute();
            log.info("Saas远程请求[{}]-返回信息：[{}]-接口耗时：[{}]", url, StrUtil.subWithLength(JSONUtil.toJsonStr(response.body()), 0, 5000), timeInterval.intervalMs());
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            status = Convert.toInt(jsonObject.get("status"));

            if (ObjectUtil.notEqual(status, SaasErrorCode.SUCCESS)) {
                if ((Objects.nonNull(status)) && 10012 == status && urlConstants[0].contains("doLogin")) {
                    log.warn("Saas远程请求发生错误，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                } else {
                    log.error("Saas远程请求发生错误，错误信息={},请求url:{},,请求参数:{},请求头:{}",
                            JSONUtil.toJsonStr(jsonObject),
                            RequestUtil.getRequestUri(),
                            RequestUtil.getParam(),
                            RequestUtil.getAllHeaders());
                }
                return jsonObject;
            }
            return jsonObject;
        } catch (Exception e) {
            log.error("请求saas接口时发生异常", e);
        }
        return new JSONObject();
    }


    public JSONObject postWithoutBody(String[] urlConstants) {
        HttpResponse response = HttpRequest.post(saasApiUrl + urlConstants[0])
                .header(Header.CONTENT_TYPE, "application/json")
                .header("appId", appId)
                .timeout(10000)
                .execute();

        JSONObject jsonObject = JSONUtil.parseObj(response.body());
        log.info("Saas远程请求[{}]--返回信息：[{}]", saasApiUrl + urlConstants[0], StrUtil.subWithLength(JSONUtil.toJsonStr(response.body()), 0, 5000));

        Integer status = Convert.toInt(jsonObject.get("status"));

        if (ObjectUtil.notEqual(status, SaasErrorCode.SUCCESS)) {
            log.error("Saas远程请求发生错误：，错误信息={}", JSONUtil.toJsonStr(jsonObject));
            return jsonObject;
        }
        return jsonObject;
    }

    public JSONObject openApiPost(String[] urlConstants, String requestBody) {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.restart();
        Integer status;
        try {
            HttpResponse response = HttpRequest.post(feignSysUrl + urlConstants[0])
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("appId", appId)
                    .body(requestBody)
                    .timeout(10000)
                    .execute();
            log.info("Saas远程请求[{}]-请求参数：[{}]-返回信息：[{}]-接口耗时：[{}]", feignSysUrl + urlConstants[0], StrUtil.subWithLength(requestBody, 0, 500), StrUtil.subWithLength(JSONUtil.toJsonStr(response.body()), 0, 5000), timeInterval.intervalMs());
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            status = Convert.toInt(jsonObject.get("status"));
            if (ObjectUtil.notEqual(status, SaasErrorCode.SUCCESS)) {
                log.error("Saas远程请求发生错误：，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                return jsonObject;
            }
            return jsonObject;
        } catch (Exception e) {
            log.error("请求saas接口时发生异常", e);
        }
        return new JSONObject();

    }

    public JSONObject openApiGet(String[] urlConstants, String param) {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.restart();
        Integer status;
        try {
            HttpResponse response = HttpRequest.get(feignSysUrl + urlConstants[0] + param)
                    .header(Header.CONTENT_TYPE, "application/json")
                    .header("appId", appId)
                    .timeout(10000)
                    .execute();
            log.info("Saas远程请求[{}]-请求参数：[{}]-返回信息：[{}]-接口耗时：[{}]", feignSysUrl + urlConstants[0], param, StrUtil.subWithLength(JSONUtil.toJsonStr(response.body()), 0, 5000), timeInterval.intervalMs());
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            status = Convert.toInt(jsonObject.get("status"));
            if (ObjectUtil.notEqual(status, SaasErrorCode.SUCCESS)) {
                log.error("Saas远程请求发生错误：，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                return jsonObject;
            }
            return jsonObject;
        } catch (Exception e) {
            log.error("请求saas接口时发生异常", e);
        }
        return new JSONObject();

    }

    public JSONObject openApiPostWithForm(String[] urlConstants, String requestBody) {
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.restart();
        Integer status;
        Map<String, Object> map = JSONUtil.toBean(JSONUtil.parseObj(requestBody), Map.class);
        try {
            String responseStr = HttpUtil.post(feignSysUrl + urlConstants[0], map, 10000);
            log.info("Saas远程请求[{}]-请求参数：[{}]-返回信息：[{}]-接口耗时：[{}]", feignSysUrl + urlConstants[0], StrUtil.subWithLength(requestBody, 0, 500), StrUtil.subWithLength(responseStr, 0, 5000), timeInterval.intervalMs());
            JSONObject jsonObject = JSONUtil.parseObj(responseStr);
            status = Convert.toInt(jsonObject.get("status"));
            if (ObjectUtil.notEqual(status, SaasErrorCode.SUCCESS)) {
                log.error("Saas远程请求发生错误：，错误信息={}", JSONUtil.toJsonStr(jsonObject));
                return jsonObject;
            }
            return jsonObject;
        } catch (Exception e) {
            log.error("请求saas接口时发生异常", e);
        }
        return new JSONObject();

    }

    public JSONObject getSaaSGateWay(String[] urlConstants, Map<String, Object> paramMap) {

        StringBuilder param = new StringBuilder();

        List<String> keyList = paramMap.keySet().stream().collect(Collectors.toList());
        if (keyList.size() > 0) {
            param.append("?");
            for (int i = 0; i < keyList.size(); i++) {
                // 值为空的不拼接
                if (ObjectUtil.isNull(paramMap.get(keyList.get(i)))) {
                    continue;
                }
                param.append(keyList.get(i) + "=" + paramMap.get(keyList.get(i)));
                if (i != keyList.size() - 1) {
                    param.append("&");
                }
            }
        }
        TimeInterval timeInterval = DateUtil.timer();
        timeInterval.restart();
        Integer status;
        String paramStr = param.toString();
        try {
            HttpResponse response = HttpRequest.get(openApiUrl + urlConstants[0] + paramStr)
                    .header("xt", RequestUtil.getHeader("xt"))
                    .execute();
            log.info("Saas远程请求[{}]-请求参数：[{}]-返回信息：[{}]-接口耗时：[{}]", openApiUrl + urlConstants[0], paramMap, StrUtil.subWithLength(JSONUtil.toJsonStr(response.body()), 0, 5000), timeInterval.intervalMs());
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            status = Convert.toInt(jsonObject.get("status"));
            if ((Objects.nonNull(status)) && !Convert.toInt(status).equals(SaasErrorCode.SUCCESS)) {
                log.error("Saas远程请求[{}]-请求参数：[{}]-返回信息：[{}]" +
                        "Saas远程请求发生错误：，错误信息={},接口耗时：[{}]", openApiUrl + urlConstants[0], paramMap, JSONUtil.toJsonStr(jsonObject), JSONUtil.toJsonStr(jsonObject), timeInterval.intervalMs());
                return jsonObject;
            }
            return jsonObject;
        } catch (Exception e) {
            log.error("请求saas接口时发生异常", e);
        }
        return new JSONObject();
    }
}
