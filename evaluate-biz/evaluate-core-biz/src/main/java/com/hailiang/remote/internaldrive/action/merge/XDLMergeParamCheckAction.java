package com.hailiang.remote.internaldrive.action.merge;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hailiang.enums.EventCodeEnum;
import com.hailiang.enums.EventScoreTypeEnum;
import com.hailiang.model.dto.internaldrive.merge.ThirdFromXdlRecordMessageDTO;
import com.hailiang.model.entity.ThirdDataInfoPO;
import com.hailiang.pipeline.BusinessProcess;
import com.hailiang.pipeline.ProcessContext;
import com.hailiang.remote.internaldrive.Record;
import com.hailiang.remote.internaldrive.SendTaskModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 2、星动力（融合）消息参数校验处理器
 *
 * @Description: 2、星动力（融合）消息参数校验处理器
 * @Author: Jovi
 * @Date: Created in 2024/7/8
 * @Version: 2.0.0
 */
@Slf4j
@Component
public class XDLMergeParamCheckAction implements BusinessProcess<SendTaskModel> {


    @Override
    public void process(ProcessContext<SendTaskModel> context) {

        log.info("「星动力(融合)-pipeline」-「0.2」-「参数校验」");
        SendTaskModel processModel = context.getProcessModel();

        checkRecord(processModel);
    }


    private void checkRecord(SendTaskModel processModel) {

        String eventCode = processModel.getEventCode();

        List<Record> records = processModel.getRecords();
        for (Record record : records) {
            if (EventCodeEnum.XDL_BEHAVIOUR_ADD_RECORD.getCode().equals(eventCode)) {
                validateThirdAddRecord(record);
            } else {
                validateThirdRemoveRecord(record);
            }
        }
    }

    private void validateThirdRemoveRecord(Record record) {

        ThirdFromXdlRecordMessageDTO thirdRecordDTO = BeanUtil.toBean(
                record.getContent(),
                ThirdFromXdlRecordMessageDTO.class);

        // 必填字段校验
        List<Object> requiredFields = Arrays.asList(
                thirdRecordDTO.getTenantId(),
                thirdRecordDTO.getSchoolId(),
                thirdRecordDTO.getCampusId(),
                thirdRecordDTO.getThirdRecordId(),
                thirdRecordDTO.getCreateBy(),
                thirdRecordDTO.getCreateByName(),
                thirdRecordDTO.getCreateTime()
        );

        // 检查是否为空
        if (StrUtil.hasBlank(requiredFields.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toArray(String[]::new))
                || requiredFields.stream().anyMatch(Objects::isNull)) {
            logAndSetFailInfo(record, "删除明细-校验异常，必填字段为空");
            return;
        }

        // 标签id为0的校验
        if (ObjectUtil.equal(thirdRecordDTO.getThirdTargetId(), 0)) {
            logAndSetFailInfo(record, "删除明细-校验异常，标签id为0");
            return;
        }

        record.setThirdFromXdlRecordMessageDTO(thirdRecordDTO);
    }

    private void validateThirdAddRecord(Record record) {

        ThirdFromXdlRecordMessageDTO thirdRecordDTO = BeanUtil.toBean(record.getContent(), ThirdFromXdlRecordMessageDTO.class);

        // 必填字段校验
        List<Object> requiredFields = Arrays.asList(
                thirdRecordDTO.getTenantId(),
                thirdRecordDTO.getSchoolId(),
                thirdRecordDTO.getCampusId(),
                thirdRecordDTO.getCampusSectionId(),
                thirdRecordDTO.getCampusSectionCode(),
                thirdRecordDTO.getGradeId(),
                thirdRecordDTO.getGradeCode(),
                thirdRecordDTO.getClassId(),
                thirdRecordDTO.getStudentId(),
                thirdRecordDTO.getModuleCode(),
                thirdRecordDTO.getThirdTargetId(),
                thirdRecordDTO.getThirdTargetType(),
                thirdRecordDTO.getThirdTargetName(),
                thirdRecordDTO.getScoreType(),
                thirdRecordDTO.getScore(),
                thirdRecordDTO.getThirdOptionId(),
                thirdRecordDTO.getThirdOptionName(),
                thirdRecordDTO.getThirdOperationType(),
//                thirdRecordDTO.getSubjectCode(),
                thirdRecordDTO.getThirdRecordId(),
                thirdRecordDTO.getCreateBy(),
                thirdRecordDTO.getCreateByName(),
                thirdRecordDTO.getCreateTime()
        );

        // 检查是否为空
        if (StrUtil.hasBlank(requiredFields.stream()
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .toArray(String[]::new))
                || requiredFields.stream().anyMatch(Objects::isNull)) {
            logAndSetFailInfo(record, "新增明细-校验异常，必填字段为空");
            return;
        }

        // 标签id为0的校验
        if (ObjectUtil.equal(thirdRecordDTO.getThirdTargetId(), 0)) {
            logAndSetFailInfo(record, "新增明细-校验异常，标签id为0");
            return;
        }
        // 加分类型校验
        if (!EventScoreTypeEnum.hasCode(thirdRecordDTO.getScoreType())) {
            logAndSetFailInfo(record, "新增明细-校验异常，评分类型有误");
            return;
        }

        //TODO 数据库精度改为(32,7)，此处暂时去掉
//        // 小数点位数校验
//        int decimalCount = thirdRecordDTO.getScore().scale();
//        if (decimalCount > 2) {
//            logAndSetFailInfo(record, "新增明细-校验异常，分数小数点位数不等于2位");
//            return;
//        }

        record.setThirdFromXdlRecordMessageDTO(thirdRecordDTO);
    }


    private void logAndSetFailInfo(Record record, String message) {
        log.error("「星动力(融合)-pipeline」-「0.2」-「参数校验」-校验异常：{}", message);
        setFailInfo(record, message);
    }

    private void setFailInfo(Record record, String failInfo) {
        record.setSuccessFlag(false);

        ThirdDataInfoPO thirdDataInfoPO = record.getThirdDataInfoPO();
        thirdDataInfoPO.setDisposeFlag(2);
        thirdDataInfoPO.setRemark(failInfo);
    }

}
