package com.hailiang.remote.saas;

import com.hailiang.mp.commonsource.api.PageResult;
import com.hailiang.remote.saas.config.SaaSAPIRemoteConfig;
import com.hailiang.remote.saas.dto.educational.EduDutyRoleQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.fallback.SaaSAPIRemoteFallBack;
import com.hailiang.remote.saas.vo.educational.EduDutyRoleVO;
import com.hailiang.remote.saas.vo.educational.EduOrgTreeResultVO;
import com.hailiang.remote.saas.vo.educational.SaaSEduStudentInfoVO;
import com.hailiang.saas.common.base.CommonResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
/**
 * 废弃：禁止使用此类方式调用，同时使用evaluate-remote模块
 */
@FeignClient(url = "${open.saas-api-url}", value="educational", fallback = SaaSAPIRemoteFallBack.class, configuration = SaaSAPIRemoteConfig.class)
@Deprecated
public interface SaaSAPI {

    @ApiOperation(value = "查询学生列表")
    @PostMapping("/educational/teachManage/v1/student/list")
    CommonResult<PageResult<SaaSEduStudentInfoVO>> queryStudentPage(EduStudentPageQueryDTO query);

    @ApiOperation("获取用户值日组角色信息列表批量接口(值日学生、值日教师、值日干部)")
    @PostMapping({"/educational/duty_group/v1/role/list"})
    CommonResult<List<EduDutyRoleVO>> queryDutyRoleList(EduDutyRoleQueryDTO var1);
}
