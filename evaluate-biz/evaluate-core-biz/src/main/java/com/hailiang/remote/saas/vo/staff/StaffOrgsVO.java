package com.hailiang.remote.saas.vo.staff;

import com.hailiang.remote.saas.vo.administration.AdminOrgAndFullPathVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class StaffOrgsVO {
    /**
     * 员工id
     */
    private Long staffId;

    /**
     * 员工工号
     */
    private String staffCode;

    /**
     * 员工姓名
     */
    private String staffName;

    /**
     * 员工所属组织及其全路径
     */
    private List<AdminOrgAndFullPathVO> orglevelPaths = new ArrayList<>();
}