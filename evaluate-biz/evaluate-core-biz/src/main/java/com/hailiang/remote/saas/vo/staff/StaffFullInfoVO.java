package com.hailiang.remote.saas.vo.staff;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: JJL
 * @Date: 2023/4/25 19:20
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StaffFullInfoVO {
    /**
     * 员工id
     */
    private Long staffId;

    /**
     * 员工工号
     */
    private String staffCode;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 员工手机号
     */
    private String mobile;

    /**
     * 员工邮箱
     */
    private String mailbox;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 员工性别
     */
    private String sex;

    /**
     * 头像
     */
    private String avatar;


    /**
     * 状态 0: 正常  1： 离职
     */
    private Integer state;

    /**
     * 班级列表
     */
    private List<ClassFullInfoVO> classList;


    /**
     * 年级列表
     */
    private List<GradeFullInfoVO> gradeList;

    /**
     * 学段
     */
    private String campusSectionId;

    /**
     * 校区id
     */
    private String campusId;
    /**
     * 学校id
     */
    private String schoolId;

    /**
     * 老师角色类型
     */
    private Integer teacherRoleType;

    /**
     * 钉钉通知内容
     */
    private String inform;

    /**
     * 跳转url
     */
    private String messageUrl;
    /**
     * 通知标题
     */
    private String title;
    /**
     * 班主任所在班级，年级组长所在年级，学生处主任所在学校
     */
    private Long classOrGrade;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 是否发送该条消息，0：发送  1：不发送
     */
    private Integer sendFlag = 0;
//    /**
//     * 老师唯一id
//     */
//    private String uniqueId;

}
