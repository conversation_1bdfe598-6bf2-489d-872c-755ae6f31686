package com.hailiang.remote.saas.vo.student;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 学校-学生关系表(UcStudentClass)实体类
 *
 * <AUTHOR>
 * @since 2021-10-09 11:06:01
 */
@Data
public class UcStudentClassBffVO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    @ApiModelProperty("学生信息id")
    private Long studentId;
    @ApiModelProperty("租户id")
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 学校id
     */
    private Long schoolId;
    /**
     * 学校名称
     */
    private String schoolName;
    /**
     * 校区id
     */
    private Long campusId;
    @ApiModelProperty(value = "所属校区名称")
    private String campusName;
    @ApiModelProperty("年级id")
    private Long gradeId;
    @ApiModelProperty(value = "年级统一标识")
    private String gradeCode;
    @ApiModelProperty(value = "入学年份")
    private Integer startYear;

    @ApiModelProperty("班级id")
    private Long classId;
    @ApiModelProperty("行政班班级id")
    private Long formalClassId;

    @ApiModelProperty(value="更新人")
    private String modifier;

    @ApiModelProperty(value = "学生状态0正常1休学")
    private String studentStatus;
    @ApiModelProperty("升级状态:0正常1已毕业")
    private String upgradeStatus;
    @ApiModelProperty("是否删除（1-删除， 0-未删除）")
    private Boolean isDeleted;

    @ApiModelProperty("学生学号")
    private String studentNo;
    @ApiModelProperty("学生姓名")
    private String studentName;
    @ApiModelProperty(value="性别：0未知；1男；2女")
    private Integer sex;
    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "年级名称")
    private String gradeName;

    @ApiModelProperty(value = "更新时间")
    private Date gmtModified;

    @ApiModelProperty("班级流水")
    private Integer classNum;
    @ApiModelProperty("班级类型")
    private String classType;

    @ApiModelProperty("学段id")
    private Long campusSectionId;

    @ApiModelProperty(value = "匹配年级信息里的学段code")
    private String sectionCode;

    @ApiModelProperty(value = "学段名称 1-幼儿园 2-小学 3-初中 4- 高中")
    private String sectionName;
}
