package com.hailiang.remote.internaldrive;

import cn.hutool.json.JSONArray;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.hailiang.pipeline.ProcessModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @date 2021/11/22
 * @description 发送消息任务模型
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendTaskModel implements ProcessModel {

    /**
     * 事件编号
     */
    private String eventCode;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 上游服务
     */
    private String upstream;

    /**
     * 任务id（taskId+eventCode可以用来防重复消费）
     */
    private String taskId;

    /**
     * 跟上游约定好的内容，结构类型必须是数组
     */
    private JSONArray content;

    /**
     * 具体业务记录
     */
    private List<Record> records;


    private Integer operationType;

    @Override
    public String toString() {
        return JSON.toJSONString(this, SerializerFeature.WriteMapNullValue);
    }

}
