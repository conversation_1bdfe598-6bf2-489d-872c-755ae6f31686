package com.hailiang.remote.saas.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 数据库与saas对应教务组织机构对应关系
 */
public enum OrgTypeEnum {
    SCHOOL(5, 1,  "学校"),
    CAMPUS(6, 2, "校区"),
    SECTION(7, 3, "学段"),
    GRADE(8, 4,  "年级"),
    CLASS(9, 5,  "班级");

    private Integer localValue;
    private Integer saasCode;
    private String desc;

    private OrgTypeEnum(Integer localValue, Integer preCode,String desc) {
        this.localValue = localValue;
        this.saasCode = preCode;
        this.desc = desc;
    }

    public Integer getSaasCode() {
        return this.saasCode;
    }

    public Integer getLocalValue() {
        return this.localValue;
    }

    public String getDesc() {
        return this.desc;
    }

    public static List<Integer> getCodes() {
        return Arrays.asList(SCHOOL.getLocalValue(), CAMPUS.getLocalValue(), SECTION.getLocalValue(), GRADE.getLocalValue(), CLASS.getLocalValue());
    }

    public static Integer getByCode(Integer code) {
        for (int i = 0; i < values().length; i++) {
            if (values()[i].localValue.equals(code)) {
                return values()[i].getSaasCode();
            }
        }
        return null;
    }
}
