package com.hailiang.remote.saas.dto.educational;

import com.hailiang.enums.ModuleCodeEnum;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

@Data
@Accessors(chain = true)
public class EduOrgQueryDTO {

    /**
     * 根据currentIdType，1为学校id，2为校区id，3为学段id，4为年级id，5为班级id
     */
    private Long currentId;

    /**
     * 学年
     */
    private String schoolYear;

    /**
     * 学期名字
     */
    private String termName;

    /**
     * 到哪一层为止（1学校2校区3学段4年级5班级 默认为班级）
     *
     *
     */
    private Integer endType;

    /**
     * currentId的类型
     */
    private Integer currentIdType;

    /**
     * 是否返回树形：0否1是 （默认返回树形结构）
     */
    @Range(min = 0, max = 1, message = "isTree只能返回0或1")
    private Integer isTree;

    /**
     * classType = 99时返回全部，否则默认只返回行政班，0行政班、1选考班、2学考班、3选修班、4兴趣班
     */
    private Integer classType;
    /**
     * 班级类型：0行政班、1选考班、2学考班、3选修班、4兴趣班  不传或者传的是空集合默认行政班
     */
    private List<Integer> classTypes;

    /**
     * 只展示哪些层级，比如2,4,5 则查出当前学校下所有层级，但是只展示校区、年级和班级，顶层为校区
     * 1学校，2校区，3学段，4年级，5班级
     */
    @NotEmpty(message = "展示层级列表不能为空")
    private List<Integer> showViewTypeList;

    /**
     * 业务模块编码
     * @see ModuleCodeEnum
     */
    private String moduleCode;

    /**
     * 是否按角色过滤
     */
    private Boolean roleFilter = Boolean.TRUE;
    /**
     * 是否需要学生人数：0否1是
     */
    @Range(min = 0, max = 1, message = "needStudentNum只能是0或1")
    private Integer needStudentNum;

}