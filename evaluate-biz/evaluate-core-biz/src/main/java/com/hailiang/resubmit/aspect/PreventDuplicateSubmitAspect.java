package com.hailiang.resubmit.aspect;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.enums.ResponseEnum;
import com.hailiang.exception.BizException;
import com.hailiang.exception.enums.BizExceptionEnum;
import com.hailiang.resubmit.annotation.PreventDuplicateSubmit;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> gaoxin
 * @create 2023/7/27 14:54
 */
@Aspect
@Order(99)
@Component
public class PreventDuplicateSubmitAspect {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Before("@annotation(rl)")
    public void before(JoinPoint joinPoint, PreventDuplicateSubmit rl) throws Exception {
        String key = generateKey(joinPoint);
        Boolean isLock = redisTemplate.opsForValue().setIfAbsent(key, 1, rl.expireTime(), TimeUnit.MILLISECONDS);
        if (Boolean.FALSE.equals(isLock)) {
            throw new BizException(BizExceptionEnum.ALREADY_REPORTED);
        }
    }

    @AfterThrowing(pointcut = "@annotation(rl)", throwing = "ex")
    public void afterThrowing(JoinPoint joinPoint, PreventDuplicateSubmit rl, Exception ex) throws Exception {
        if (ex instanceof BizException) return;
        String key = generateKey(joinPoint);
        redisTemplate.delete(key);
    }

    @AfterReturning(pointcut = "@annotation(rl)", returning = "rvt")
    public void afterMethod(JoinPoint joinPoint, PreventDuplicateSubmit rl, Object rvt) throws Exception {

        JSONObject response = (JSONObject) JSONObject.toJSON(rvt);
        Integer status = response.getInteger("status");

        boolean isSuccess = ResponseEnum.OK.getCode().equals(status);
        if (!isSuccess) {
            String key = generateKey(joinPoint);
            redisTemplate.delete(key);
        }
    }

    private String generateKey(JoinPoint joinPoint) throws NoSuchAlgorithmException {

        String className = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();

        StringBuilder sb = new StringBuilder();

        sb.append(className).append(methodName);
        for (Object arg : args) {
            sb.append(Convert.toStr(arg));
        }

        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] digest = md5.digest(sb.toString().getBytes(StandardCharsets.UTF_8));

        StringBuilder result = new StringBuilder();
        for (byte b : digest) {
            result.append(String.format("%02x", b));
        }

        return result.toString();
    }

}
