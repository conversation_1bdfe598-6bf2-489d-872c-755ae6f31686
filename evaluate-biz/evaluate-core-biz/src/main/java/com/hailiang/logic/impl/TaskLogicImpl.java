package com.hailiang.logic.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hailiang.enums.TaskApprovalEnum;
import com.hailiang.enums.TaskStatusEnum;
import com.hailiang.logic.TaskLogic;
import com.hailiang.mapper.TaskMapper;
import com.hailiang.model.entity.TaskPO;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class TaskLogicImpl extends ServiceImpl<TaskMapper, TaskPO> implements TaskLogic {

    @Resource
    private TaskMapper mapper;


    @Override
    public List<TaskPO> listByCampusIdAndStaffIdAndStatus(String campusId, String staffId, Integer taskStatus) {
        return mapper.selectList(new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getCampusId, campusId)
                .eq(TaskPO::getSubmitStaffId, staffId)
                .eq(TaskPO::getTaskStatus, taskStatus)
                .orderByDesc(TaskPO::getCreateTime));
    }

    @Override
    public List<TaskPO> listByTargetIdAndStaffId(Long targetId, String staffId) {
        return mapper.selectList(new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getTargetId, targetId)
                .eq(TaskPO::getSubmitStaffId, staffId)
                .orderByDesc(TaskPO::getCreateTime));
    }

    @Override
    public List<TaskPO> listRemindTask(DateTime now, DateTime beginOfDay) {
        //获取老师的提醒任务
        List<TaskPO> teacherTaskPOS = mapper.listTeacherRemindTask(now, beginOfDay);
        //获取家长的提醒任务
        List<TaskPO> parentTaskPOS = mapper.listParentRemindTask(now, beginOfDay);

        teacherTaskPOS.addAll(parentTaskPOS);
        return teacherTaskPOS;
    }

    @Override
    public List<TaskPO> listUrgeTask(DateTime now) {
        //获取老师的催办任务
        DateTime beginOfDay = DateUtil.offsetDay(now, -7);
        List<TaskPO> teacherTaskPOS = mapper.listTeacherUrgeTask(now, beginOfDay);
        //获取家长的催办任务
        List<TaskPO> parentTaskPOS = mapper.listParentUrgeTask(now, beginOfDay);

        teacherTaskPOS.addAll(parentTaskPOS);
        return teacherTaskPOS;
    }

    @Override
    public boolean update(TaskPO taskPO) {
        return mapper.updateById(taskPO) > 0;
    }

    @Override
    public boolean updateBatch(List<TaskPO> taskPOS) {
        if (CollUtil.isEmpty(taskPOS)) {
            return false;
        }
        return this.updateBatchById(taskPOS);
    }

    @Override
    public long countByCondition(String submitStaffId, Long targetId, DateTime beginTime, DateTime endTime) {
        Assert.notBlank(submitStaffId, "提交人不能为空");
        Assert.notNull(targetId, "指标不能为空");
        Assert.notNull(beginTime, "开始时间不能为空");
        Assert.notNull(endTime, "结束时间不能为空");
        return this.count(new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getSubmitStaffId, submitStaffId)
                .eq(TaskPO::getTargetId, targetId)
                .between(TaskPO::getSubmitTime, beginTime, endTime)
        );
    }

    @Override
    public List<TaskPO> listByCondition(String campusId , String staffId, List<String> studentIds) {
        List<TaskPO> evaluateTaskPOS = new ArrayList<>();
        // 第1部分：提交人是自己且未提交
        evaluateTaskPOS.addAll(this.list(new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getCampusId, campusId)
                .eq(TaskPO::getSubmitStaffId, staffId)
                .eq(TaskPO::getTaskStatus, TaskStatusEnum.NOT_SUBMIT.getCode())));

        // 第2部分：学生列表审批中
        evaluateTaskPOS.addAll(this.list(new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getCampusId, campusId)
                .in(TaskPO::getSubmitStaffId, studentIds)
                .eq(TaskPO::getApprovalStatus, TaskApprovalEnum.APPROVAL_ING.getCode())
                .eq(TaskPO::getTaskStatus, 2)
                .isNull(TaskPO::getReviewTeacherId)));

        // 第3部分：评审老师包含自己（需要特殊处理）
        evaluateTaskPOS.addAll(this.list(new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getCampusId, campusId)
                .eq(TaskPO::getApprovalStatus, TaskApprovalEnum.APPROVAL_ING.getCode())
                .eq(TaskPO::getTaskStatus, 2)
                .apply("MATCH(review_teacher_id) AGAINST ({0} IN BOOLEAN MODE)", "*"+ staffId +"*")));
        return evaluateTaskPOS;
    }
}
