<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.CabinetLatticeTypeMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.point.entity.CabinetLatticeType">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="factoryId" column="factory_id" jdbcType="VARCHAR"/>
        <result property="latticeConfigInfo" column="lattice_config_info" jdbcType="VARCHAR"/>
        <result property="cabinetLatticeTypeClassify" column="cabinet_lattice_type_classify" jdbcType="INTEGER"/>
        <result property="capacity" column="capacity" jdbcType="INTEGER"/>
        <result property="layerCount" column="layer_count" jdbcType="INTEGER"/>
        <result property="latticeCount" column="lattice_count" jdbcType="INTEGER"/>
        <result property="layoutConfigInfo" column="layout_config_info" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,factory_id,factory_type_id,lattice_config_info,cabinet_lattice_type_classify,capacity,
        factory_type_code,factory_type_name,layer_count,
        lattice_count,layout_config_info,create_by,
        create_time,update_by,update_time,
        deleted
    </sql>
</mapper>
