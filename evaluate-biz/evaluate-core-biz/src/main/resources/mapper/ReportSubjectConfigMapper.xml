<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.ReportSubjectConfigMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.report.entity.SubjectEvaluationDimSubjectConfigPO">
        <!--@mbg.generated-->
        <!--@Table report_subject_config-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="campus_id" jdbcType="VARCHAR" property="campusId"/>
        <result column="campus_section_id" jdbcType="VARCHAR" property="campusSectionId"/>
        <result column="campus_section_code" jdbcType="VARCHAR" property="campusSectionCode"/>
        <result column="dim_id" jdbcType="BIGINT" property="dimId"/>
        <result column="subject_id" jdbcType="VARCHAR" property="subjectId"/>
        <result column="subject_code" jdbcType="VARCHAR" property="subjectCode"/>
        <result column="subject_name" jdbcType="VARCHAR" property="subjectName"/>
        <result column="subject_score" jdbcType="INTEGER" property="subjectScore"/>
        <result column="level_type" jdbcType="INTEGER" property="levelType"/>
        <result column="archive_flag" jdbcType="TINYINT" property="archiveFlag"/>
        <result column="parent_discipline_code" jdbcType="VARCHAR" property="parentDisciplineCode"/>
        <result column="parent_discipline_id" jdbcType="VARCHAR" property="parentDisciplineId"/>
        <result column="subject_type" jdbcType="INTEGER" property="subjectType"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="level_name" jdbcType="VARCHAR" property="levelName"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id, school_id, campus_id, campus_section_id, campus_section_code, dim_id, subject_id, subject_code,
        subject_name, subject_score, level_type,archive_flag, deleted, create_by, update_by, create_time, update_time, level_name
    </sql>

    <insert id="saveBatch">
        insert into report_subject_config
        (id, tenant_id, school_id, campus_id, campus_section_id, campus_section_code,dim_id,
        grade_id,grade_code,
        subject_id,subject_code,subject_name, subject_score, level_type,create_by, create_time,
        parent_discipline_code, parent_discipline_id, subject_type, level_name)
        values
        <foreach collection="subjects" item="subject" separator=",">
            (#{subject.id,jdbcType=BIGINT},#{subject.tenantId,jdbcType=VARCHAR},#{subject.schoolId,jdbcType=VARCHAR},
            #{subject.campusId,jdbcType=VARCHAR},
            #{subject.campusSectionId,jdbcType=VARCHAR},
            #{subject.campusSectionCode,jdbcType=VARCHAR},
            #{subject.dimId,jdbcType=BIGINT},
            #{subject.gradeId,jdbcType=VARCHAR},
            #{subject.gradeCode,jdbcType=VARCHAR},
            #{subject.subjectId,jdbcType=VARCHAR},
            #{subject.subjectCode,jdbcType=VARCHAR},#{subject.subjectName,jdbcType=VARCHAR},#{subject.subjectScore,jdbcType=INTEGER},
            #{subject.levelType,jdbcType=INTEGER},#{subject.createBy,jdbcType=VARCHAR},now(),
            #{subject.parentDisciplineCode,jdbcType=VARCHAR},#{subject.parentDisciplineId,jdbcType=VARCHAR},#{subject.subjectType,jdbcType=INTEGER},
            #{subject.levelName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="listBySchoolId" resultType="com.hailiang.model.report.entity.SubjectEvaluationDimSubjectConfigPO">
        select * from report_subject_config
        ${ew.customSqlSegment}
    </select>
</mapper>