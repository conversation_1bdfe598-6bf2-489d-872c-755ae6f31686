<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.doris.DorisSportMapper">
    <select id="listStudentScore" resultType="com.hailiang.model.vo.studentmodel.SportStudentScore">
        SELECT
        student_id studentId,
        ROUND(SUM(total_score), 2) total_score
        FROM
        sport_data
        WHERE
        campus_id = #{campusId}
        AND school_year = #{schoolYear}
        AND term_name = #{termName}
        AND deleted = 0
        GROUP BY
        student_id;
    </select>

</mapper>
