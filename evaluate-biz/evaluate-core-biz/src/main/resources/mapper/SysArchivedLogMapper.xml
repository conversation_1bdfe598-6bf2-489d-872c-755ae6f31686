<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.SysArchivedLogMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.SysArchivedLogPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
            <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
            <result property="campusSectionId" column="campus_section_id" jdbcType="VARCHAR"/>
            <result property="schoolYear" column="school_year" jdbcType="VARCHAR"/>
            <result property="termName" column="term_name" jdbcType="VARCHAR"/>
            <result property="dimFlag" column="dim_flag" jdbcType="INTEGER"/>
            <result property="dimTableName" column="dim_table_name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,school_id,campus_id,campus_section_id,school_year,term_name,
        dim_flag,dim_table_name,dim_table_name, status, remark,
        create_time,create_by,update_time,update_by,deleted
    </sql>
</mapper>
