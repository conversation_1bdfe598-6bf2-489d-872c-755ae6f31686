<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hailiang.mapper.EvaluateHelpBehaviourRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.EvaluateHelpBehaviourRecordPO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="school_id" property="schoolId"/>
        <result column="campus_id" property="campusId"/>
        <result column="campus_section_id" property="campusSectionId"/>
        <result column="campus_section_code" property="campusSectionCode"/>
        <result column="grade_id" property="gradeId"/>
        <result column="grade_code" property="gradeCode"/>
        <result column="class_id" property="classId"/>
        <result column="student_id" property="studentId"/>
        <result column="student_name" property="studentName"/>
        <result column="module_code" property="moduleCode"/>
        <result column="target_id" property="targetId"/>
        <result column="target_name" property="targetName"/>
        <result column="classify_id" property="classifyId"/>
        <result column="classify_name" property="classifyName"/>
        <result column="option_id" property="optionId"/>
        <result column="option_name" property="optionName"/>
        <result column="help_desc" property="helpDesc"/>
        <result column="score_type" property="scoreType"/>
        <result column="score" property="score"/>
        <result column="score_value" property="scoreValue"/>
        <result column="data_type" property="dataType"/>
        <result column="data_channel" property="dataChannel"/>
        <result column="submit_time" property="submitTime"/>
        <result column="appraisal_id" property="appraisalId"/>
        <result column="appraisal_name" property="appraisalName"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <!-- 表字段 -->
    <sql id="baseColumns">
    t.id, t.tenant_id , t.school_id , t.campus_id , t.campus_section_id ,
        t.campus_section_code , t.grade_id , t.grade_code , t.class_id ,
        t.student_id , t.student_name , t.module_code , t.target_id ,
        t.target_name , t.classify_id , t.classify_name , t.option_id ,
        t.option_name , t.help_desc , t.score_type , t.score , t.score_value ,
        t.data_type , t.data_channel , t.submit_time , t.appraisal_id , t.appraisal_name ,
        t.not_part_count ,
        t.create_by , t.create_time , t.update_by , t.update_time , t.deleted
        </sql>

    <select id="queryByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="baseColumns"/>
        FROM evaluate_help_behaviour_record t
        where deleted = 0
        <if test="condition.classId != null">
            and t.class_id = #{condition.classId}
        </if>
        <if test="condition.studentId != null">
            and t.student_id = #{condition.studentId}
        </if>
        <if test="condition.startTime != null and condition.endTime != null">
            and t.submit_time between #{condition.startTime} and #{condition.endTime}
        </if>
        <if test="condition.studentIds != null and condition.studentIds.size > 0">
            and t.student_id in
            <foreach collection="condition.studentIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="condition.idList != null and condition.idList.size > 0">
            and t.id in
            <foreach collection="condition.idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="condition.unNeedHelpBehaviourRecordIds != null and condition.unNeedHelpBehaviourRecordIds.size > 0">
            and t.id not in
            <foreach collection="condition.unNeedHelpBehaviourRecordIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="condition.includeInternalScoreFlag == false">
            AND data_type = 4
        </if>
    </select>




</mapper>
