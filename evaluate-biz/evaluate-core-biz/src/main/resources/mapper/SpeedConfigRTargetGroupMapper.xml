<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.SpeedConfigRTargetGroupMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.SpeedConfigRTargetGroupPO">
        <!--@Table evaluate_speed_config_r_target_group-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="staff_id" jdbcType="VARCHAR" property="staffId"/>
        <result column="config_id" jdbcType="BIGINT" property="configId"/>
        <result column="speed_target_group_id" jdbcType="BIGINT" property="speedTargetGroupId"/>
        <result column="sort_index" jdbcType="INTEGER" property="sortIndex"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        staff_id,
        config_id,
        speed_target_group_id,
        sort_index,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted
    </sql>

    <insert id="saveBatch">
        insert into evaluate_speed_config_r_target_group(id,staff_id,config_id,speed_target_group_id, sort_index,
        create_by,create_time, deleted) values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT},#{item.staffId,jdbcType=VARCHAR},#{item.configId,jdbcType=BIGINT},
            #{item.speedTargetGroupId,jdbcType=BIGINT},#{item.sortIndex,jdbcType=INTEGER},#{item.createBy,jdbcType=VARCHAR}
            ,now(),0)
        </foreach>
    </insert>
</mapper>