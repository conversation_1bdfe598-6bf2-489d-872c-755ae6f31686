<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.MedalLogoMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.MedalLogo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
            <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
            <result property="logoType" column="logo_type" jdbcType="TINYINT"/>
            <result property="logoUrl" column="logo_url" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,school_id,
        campus_id,logo_type,logo_url,
        create_by,create_time,update_by,
        update_time,deleted
    </sql>
    <select id="listMedalLogos" resultType="com.hailiang.model.entity.MedalLogo">
        select
        <include refid="Base_Column_List" />
        from medal_logo
        WHERE
        id IN
        <foreach collection='logoIds' open='(' close=')' index='index' item='item' separator=','>
            #{item}
        </foreach>

    </select>
    <select id="listMedalLogo" resultType="com.hailiang.model.entity.MedalLogo">
        select
        <include refid="Base_Column_List" />
        from medal_logo
    </select>
</mapper>
