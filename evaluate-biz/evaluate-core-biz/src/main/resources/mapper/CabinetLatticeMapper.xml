<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.CabinetLatticeMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.point.entity.CabinetLattice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="cabinetId" column="cabinet_id" jdbcType="VARCHAR"/>
            <result property="cabinetLatticeName" column="cabinet_lattice_name" jdbcType="VARCHAR"/>
            <result property="cabinetLatticeAddress" column="cabinet_lattice_address" jdbcType="VARCHAR"/>
            <result property="cabinetLatticeTypeId" column="cabinet_lattice_type_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,cabinet_id,cabinet_lattice_name,cabinet_lattice_address,
        cabinet_lattice_type_id,create_by,create_time,
        update_by,update_time,deleted
    </sql>
</mapper>
