<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.TargetUserArchivedMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.TargetUserArchivedPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
            <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
            <result property="targetId" column="target_id" jdbcType="BIGINT"/>
            <result property="submitType" column="submit_type" jdbcType="INTEGER"/>
            <result property="submitValue" column="submit_value" jdbcType="BIGINT"/>
            <result property="currentIds" column="current_ids" jdbcType="VARCHAR"/>
            <result property="schoolYear" column="school_year" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,school_id,campus_id,
        target_id,submit_user_name,icon_url,submit_type,
        submit_value,user_count,current_ids,school_year,create_by,create_time,
        update_by,update_time,deleted
    </sql>

    <!--获取当前校区下历史上所有指标配置为老师和行政组织机构的数据-->
    <select id="historyTargetSubmitInfos" resultType="com.hailiang.model.datastatistics.dto.TargetSubmitHistoryDTO">
        SELECT
        etua.target_id,
        etua.submit_type,
        etua.submit_value,
        etua.current_ids
        FROM
        evaluate_target_archived eta
        INNER JOIN evaluate_target_user_archived etua ON etua.target_id = eta.target_id
        WHERE
        eta.deleted = 0
        AND eta.target_status = 1
        AND etua.deleted = 0
        AND etua.submit_type IN ( 1, 2, 3 )
        AND eta.school_id = #{schoolId}
        AND eta.campus_id = #{campusId}
    </select>

</mapper>
