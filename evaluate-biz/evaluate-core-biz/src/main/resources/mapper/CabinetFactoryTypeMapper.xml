<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.CabinetFactoryTypeMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.point.entity.CabinetFactoryType">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="factoryId" column="factory_id" jdbcType="VARCHAR"/>
            <result property="factoryTypeCode" column="factory_type_code" jdbcType="VARCHAR"/>
            <result property="factoryTypeName" column="factory_type_name" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,factory_id,factory_type_code,
        factory_type_name,create_by,create_time,
        update_by,update_time,deleted
    </sql>
</mapper>
