<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.MedalRuleMatchRecordMapper">

    <select id="listMatchRuleBehaviour" resultType="com.hailiang.model.datastatistics.dto.BehaviourRecordDTO">
        SELECT
            ebr.id,
            ebr.module_code,
            ebr.tenant_id,
            ebr.school_id,
            ebr.campus_id,
            ebr.student_id,
            ebr.grade_code,
            et.group_id,
            ebr.target_id,
            ebr.option_id,
            ebr.info_id,
            ebr.score
        FROM
            medal_rule_match_record mrmr
                INNER JOIN evaluate_behaviour_record ebr ON ebr.id = mrmr.behaviour_record_id
                AND ebr.deleted = 0
                INNER JOIN evaluate_target et ON et.id = ebr.target_id
                AND et.deleted = 0
                INNER JOIN evaluate_target_group etg ON etg.id = et.group_id
                AND etg.deleted = 0
        WHERE
            mrmr.deleted = 0
          AND mrmr.student_id = #{studentId}
          AND mrmr.medal_task_rule_id = #{medalRuleId}
    </select>

</mapper>
