<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.CheckClassAwardRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.CheckClassAwardRecord">
        <!--@mbg.generated-->
        <!--@Table check_class_award_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="campus_id" jdbcType="VARCHAR" property="campusId"/>
        <result column="award_id" jdbcType="VARCHAR" property="awardId"/>
        <result column="grade_id" jdbcType="VARCHAR" property="gradeId"/>
        <result column="grade_code" jdbcType="VARCHAR" property="gradeCode"/>
        <result column="grade_name" jdbcType="VARCHAR" property="gradeName"/>
        <result column="class_id" jdbcType="VARCHAR" property="classId"/>
        <result column="class_name" jdbcType="VARCHAR" property="className"/>
        <result column="total_score" jdbcType="DECIMAL" property="totalScore"/>
        <result column="level_id" jdbcType="VARCHAR" property="levelId"/>
        <result column="level_name" jdbcType="VARCHAR" property="levelName"/>
        <result column="is_award" jdbcType="TINYINT" property="isAward"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cext" jdbcType="VARCHAR" property="cext"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id, school_id, campus_id, award_id,grade_id, grade_code, grade_name, class_id, class_name,
        total_score, level_id, level_name, is_award, deleted, create_by, update_by, create_time,
        update_time, cext
    </sql>

    <insert id="saveBatch">
        insert into check_class_award_record( id, tenant_id, school_id, campus_id, award_id, grade_id, grade_code,
        grade_name, class_id, class_name,
        total_score, level_id, level_name, is_award, deleted, create_by, update_by, create_time,
        update_time, cext)
        values
        <foreach collection="records" item="item" separator=",">
            (#{item.id},#{item.tenantId},#{item.schoolId},#{item.campusId},#{item.awardId},#{item.gradeId},#{item.gradeCode},
            #{item.gradeName},#{item.classId},#{item.className},#{item.totalScore},#{item.levelId},#{item.levelName},#{item.isAward},
            0,#{item.createBy},#{item.updateBy},now(),now(),#{item.cext})
        </foreach>
    </insert>
</mapper>