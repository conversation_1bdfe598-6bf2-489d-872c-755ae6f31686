<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hailiang.mapper.EvaluateStudentAbilityModelItemMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.EvaluateStudentAbilityModelItemPO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="school_id" property="schoolId"/>
        <result column="campus_id" property="campusId"/>
        <result column="ability_model_id" property="abilityModelId"/>
        <result column="ability_item_name" property="abilityItemName"/>
        <result column="sort_index" property="sortIndex"/>
        <result column="parent_id" property="parentId"/>
        <result column="level" property="level"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <!-- 表字段 -->
    <sql id="baseColumns">id
    , tenant_id , school_id , campus_id , ability_model_id , ability_item_name  ,
    sort_index , parent_id , level , create_by , create_time , update_by , update_time , deleted</sql>
    <!-- 插入全部字段 -->
    <insert id="insert" parameterType="com.hailiang.model.entity.EvaluateStudentAbilityModelItemPO" keyProperty="id"
            keyColumn="id" useGeneratedKeys="true">INSERT INTO evaluate_student_ability_model_item
        <trim prefix="(" suffix=")" suffixOverrides=",">id, tenant_id, school_id, campus_id, ability_model_id,
            ability_item_name, sort_index, parent_id, level, create_by, create_time,
            update_by, update_time, deleted,
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">#{id}, #{tenantId}, #{schoolId}, #{campusId},
            #{abilityModelId}, #{abilityItemName}, #{sortIndex}, #{parentId}, #{level},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{deleted},
        </trim>
    </insert>
    <insert id="insertBatch">
        INSERT INTO evaluate_student_ability_model_item
        (id, tenant_id, school_id, campus_id, ability_model_id,
            ability_item_name, sort_index, parent_id, level, create_by, create_time,
            update_by, update_time, deleted)
        values
        <foreach collection="itemPOList" item="item" separator=",">
            (#{item.id}, #{item.tenantId}, #{item.schoolId}, #{item.campusId},
            #{item.abilityModelId}, #{item.abilityItemName}, #{item.sortIndex}, #{item.parentId}, #{item.level},
            #{item.createBy}, now(), #{item.updateBy}, now(), 0)
        </foreach>
    </insert>
    <!-- 更新不为NULL的字段 -->
    <update id="updateIgnoreNull" parameterType="com.hailiang.model.entity.EvaluateStudentAbilityModelItemPO">UPDATE
        evaluate_student_ability_model_item
        <set>
            <if test="tenantId != null">tenant_id=#{tenantId},</if>
            <if test="schoolId != null">school_id=#{schoolId},</if>
            <if test="campusId != null">campus_id=#{campusId},</if>
            <if test="abilityModelId != null">ability_model_id=#{abilityModelId},</if>
            <if test="abilityItemName != null">ability_item_name=#{abilityItemName},</if>
            <if test="sortIndex != null">sort_index=#{sortIndex},</if>
            <if test="parentId != null">parent_id=#{parentId},</if>
            <if test="level != null">level=#{level},</if>
            <if test="createBy != null">create_by=#{createBy},</if>
            <if test="createTime != null">create_time=#{createTime},</if>
            <if test="updateBy != null">update_by=#{updateBy},</if>
            <if test="updateTime != null">update_time=#{updateTime},</if>
            <if test="deleted != null">deleted=#{deleted},</if>
        </set>
        WHERE id = #{id}
    </update>
    <select id="queryByModelId" resultMap="BaseResultMap">
        select
            <include refid="baseColumns" />
            from evaluate_student_ability_model_item
        where deleted = 0
        and ability_model_id = #{modelId}
    </select>
</mapper>
