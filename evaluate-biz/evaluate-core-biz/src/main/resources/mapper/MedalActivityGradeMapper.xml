<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.MedalActivityGradeMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.MedalActivityGrade">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
            <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
            <result property="medalActivityId" column="medal_activity_id" jdbcType="BIGINT"/>
            <result property="gradeCode" column="grade_code" jdbcType="VARCHAR"/>
            <result property="gradeName" column="grade_name" jdbcType="VARCHAR"/>
            <result property="gradeId" column="grade_id" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,school_id,
        campus_id,medal_activity_id,grade_code,
        grade_name,grade_id,create_by,
        create_time,update_by,update_time,
        deleted
    </sql>

    <select id="listActivityIdsByGradeId" resultType="java.lang.Long">
        SELECT
            ma.id
        FROM
            medal_activity_grade mag
            INNER JOIN medal_activity ma ON ma.id = mag.medal_activity_id
            AND ma.deleted = 0
        WHERE
            mag.deleted = 0
          AND ma.status = 3
          AND ma.tenant_id  = #{tenantId}
          AND ma.school_id  = #{schoolId}
          AND ma.campus_id  = #{campusId}
          AND mag.grade_id = #{gradeId}
    </select>

    <select id="listActivityIdsByGradeIdFix" resultType="java.lang.Long">
        SELECT
            ma.id
        FROM
            medal_activity_grade mag
                INNER JOIN medal_activity ma ON ma.id = mag.medal_activity_id
                AND ma.deleted = 0
        WHERE
            mag.deleted = 0
          AND ma.tenant_id  = #{tenantId}
          AND ma.school_id  = #{schoolId}
          AND ma.campus_id  = #{campusId}
          AND mag.grade_id = #{gradeId}
    </select>

    <select id="listPersonalActivityByGradeId" resultType="java.lang.Long">
        SELECT
            ma.id
        FROM
            medal_activity_grade mag
                INNER JOIN medal_activity ma ON ma.id = mag.medal_activity_id
                AND ma.deleted = 0
        WHERE
            mag.deleted = 0
          AND ma.status in (3,4,5)
          AND ma.tenant_id  = #{tenantId}
          AND ma.school_id  = #{schoolId}
          AND ma.campus_id  = #{campusId}
          AND mag.grade_id = #{gradeId}
    </select>
</mapper>
