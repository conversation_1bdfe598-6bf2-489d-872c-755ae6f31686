<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.ReportReviewDetailMapper">
    <update id="updateSumScore">
        update evaluate_report_review_detail set sum_score = #{param.sumScore} where id = #{param.id}
    </update>


    <select id="listDetailForPushParent" resultType="com.hailiang.model.vo.ReportDetailInfoDTO">
        SELECT DISTINCT
            b.*,
            b.id as review_detail_id,
            a.class_id,
            a.class_name,
            a.report_start_time,
            a.report_end_time,
            a.need_review,
            a.id
        FROM
        evaluate_report_review_task a
        LEFT JOIN evaluate_report_review_detail b ON a.id = b.review_task_id
        AND b.deleted = 0 and b.status in (1,4)
        WHERE
        a.deleted = 0
--         AND b.id is not null
        and b.id IN
        <foreach collection='reviewDetailIds' open='(' close=')' index='index' item='item' separator=','>
            #{item}
        </foreach>
    </select>

</mapper>
