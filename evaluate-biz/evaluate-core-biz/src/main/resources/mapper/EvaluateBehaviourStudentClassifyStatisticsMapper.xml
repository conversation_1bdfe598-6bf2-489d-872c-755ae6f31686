<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.EvaluateBehaviourStudentClassifyStatisticsMapper">

    <resultMap type="com.hailiang.behaviour.classify.statistics.model.entity.BehaviourStudentClassifyStatisticsPO"
               id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="schoolId" column="school_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="campusSectionId" column="campus_section_id"/>
        <result property="campusSectionCode" column="campus_section_code"/>
        <result property="gradeId" column="grade_id"/>
        <result property="gradeCode" column="grade_code"/>
        <result property="classId" column="class_id"/>
        <result property="studentId" column="student_id"/>
        <result property="classifyId" column="classify_id"/>
        <result property="classifyName" column="classify_name"/>
        <result property="recordCount" column="record_count"/>
        <result property="scoreType" column="score_type"/>
        <result property="score" column="score"/>
        <result property="statisticsTime" column="statistics_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="BASE_COLUMN_SQL">
        id,tenant_id,school_id,campus_id,campus_section_id,campus_section_code,
               grade_id,
               grade_code,
               class_id,
               student_id,
               classify_id,
               classify_name,
               record_count,
               score_type,
               score,
               statistics_time,
               create_by,
               create_time,
               update_by,
               update_time,
               deleted
    </sql>
    <insert id="insertBatch">
        INSERT INTO evaluate_behaviour_student_classify_statistics (id,tenant_id, school_id, campus_id,campus_section_id,campus_section_code,
        grade_id,grade_code,class_id,student_id, classify_id,classify_name,record_count,
        score_type,score,statistics_time,create_by,create_time,update_by,update_time,deleted)
        VALUES
            <foreach collection="list" item="item" separator=",">
                (#{item.id},#{item.tenantId},#{item.schoolId},#{item.campusId},#{item.campusSectionId},#{item.campusSectionCode},#{item.gradeId}
                ,#{item.gradeCode},#{item.classId},#{item.studentId},#{item.classifyId},#{item.classifyName},#{item.recordCount},#{item.scoreType},#{item.score},#{item.statisticsTime}
                ,#{item.createBy},now(),#{item.updateBy},now(),#{item.deleted})
            </foreach>
    </insert>
    <update id="deleteStatisticsRecord">
        update evaluate_behaviour_student_classify_statistics
        set deleted = 1
        where statistics_time >= #{deleteDTO.startDate}
        and statistics_time &lt;= #{deleteDTO.endDate}
    </update>


    <resultMap id="statisticsCountMap" type="com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsCountDTO">
        <result property="classId" column="class_id"/>
        <result property="studentId" column="student_id"/>
        <result property="classifyId" column="classify_id"/>
        <result property="classifyName" column="classify_name"/>
        <result property="recordCount" column="record_count"/>
        <result property="scoreType" column="score_type"/>
        <result property="scoreSum" column="score"/>
    </resultMap>
    <select id="queryStatisticsCount" resultMap="statisticsCountMap">
        select
        class_id,
        student_id,
        classify_id,
        classify_name,
        sum(record_count) as record_count,
        score_type,
        sum(score) as score
        from evaluate_behaviour_student_classify_statistics
        where deleted = 0
        and statistics_time >= #{statisticsRecordQO.startTime}
        and statistics_time &lt;= #{statisticsRecordQO.endTime}
        and class_id = #{statisticsRecordQO.classId}
        <if test="statisticsRecordQO.studentId != null">
            and student_id = #{statisticsRecordQO.studentId}
        </if>
        <if test="statisticsRecordQO.studentIds != null and statisticsRecordQO.studentIds.size > 0">
            and student_id in
            <foreach collection="statisticsRecordQO.studentIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by class_id,student_id,classify_id,classify_name,score_type
    </select>
    <select id="queryStatisticsRecord" resultMap="BaseResultMap">
        select <include refid="BASE_COLUMN_SQL" />
            from evaluate_behaviour_student_classify_statistics
        where deleted = 0
        and class_id = #{statisticsRecordQO.classId}
        and student_id = #{statisticsRecordQO.studentId}
        and score_type = #{statisticsRecordQO.scoreType}
        and classify_id = #{statisticsRecordQO.classifyId}
        and statistics_time = #{statisticsRecordQO.statisticsTime}

    </select>


</mapper>
