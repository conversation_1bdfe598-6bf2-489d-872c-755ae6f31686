<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.MedalInfoMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.MedalInfo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="medalCatalogueId" column="medal_catalogue_id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
        <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="logoId" column="logo_id" jdbcType="BIGINT"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,medal_catalogue_id,tenant_id,
        school_id,campus_id,name,
        logo_url,description,create_by,
        create_time,update_by,update_time,
        deleted
    </sql>
    <select id="listMedalInfos" resultType="com.hailiang.model.entity.MedalInfo">
        select id,
               logo_id
        from medal_info
        where tenant_id = #{tenantId}
          AND school_id = #{schoolId}
          AND campus_id = #{campusId}
    </select>
    <select id="listMedalInfosBySchool" resultType="com.hailiang.model.entity.MedalInfo">
        select
               *
        from medal_info
        where school_id = #{schoolId}
    </select>
    <select id="getMedalInfo" resultType="com.hailiang.model.dto.activity.rule.query.MedalInfoDTO">
        SELECT mi.id,
               mi.`name`,
               ml.logo_url
        FROM medal_info mi
                 INNER JOIN medal_logo ml ON ml.id = mi.logo_id
        WHERE mi.id = #{medalInfoId}
    </select>

    <select id="listInfoByIds" resultType="com.hailiang.model.medal.vo.MedalInfoVO">
        SELECT mi.id,
        mi.name,
        mi.create_time,
        mc.name medalCatalogueName,
        ml.logo_url,
        mi.deleted
        FROM medal_info mi
        left JOIN medal_logo ml ON ml.id = mi.logo_id
        left join medal_catalogue mc on mc.id = mi.medal_catalogue_id
        where mi.id in
        <foreach item="item" collection="list" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="listMedalCatalogues" resultType="java.lang.Long">
        SELECT DISTINCT mi.medal_catalogue_id
        FROM medal_activity_grade mag
                 LEFT JOIN medal_task mt ON mag.medal_activity_id = mt.medal_activity_id and mt.deleted = 0
                 LEFT JOIN medal_info mi ON mi.id = mt.medal_info_id and mi.deleted = 0
        WHERE mag.tenant_id = #{tenantId}
          AND mag.school_id = #{schoolId}
          AND mag.campus_id = #{campusId}
          and mag.grade_id = #{gradeId}
          AND mag.deleted = 0
          and mi.medal_catalogue_id is not null
    </select>

    <select id="selectMedalInfoIdsByStudentId" resultType="com.hailiang.model.medal.vo.MedalInfoVO">
        SELECT DISTINCT mi.id,
                        mi.medal_catalogue_id,
                        mi.name,
                        mi.logo_id,
                        ml.logo_url,
                        count(0) medalCount
        FROM medal_info mi
                 inner JOIN
             medal_user_acquire_record ar on mi.id = ar.medal_info_id
                 inner JOIN
             medal_logo ml on ml.id = mi.logo_id
        WHERE ar.tenant_id = #{tenantId}
          AND ar.school_id = #{schoolId}
          AND ar.campus_id = #{campusId}
          AND ar.student_id = #{studentId}
          and ar.award_status = 3
          and ar.deleted = 0
        GROUP BY ar.medal_info_id
    </select>
</mapper>
