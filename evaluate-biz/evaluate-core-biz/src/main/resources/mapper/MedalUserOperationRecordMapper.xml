<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.MedalUserOperationRecordMapper">


    <select id="listMedalOperationRecord" resultType="com.hailiang.model.datastatistics.dto.MedalOperationRecordDTO">
        SELECT
            ebr.id,
            ebr.module_code,
            ebr.tenant_id,
            ebr.school_id,
            ebr.campus_id,
            ebr.student_id,
            ebr.grade_code,
            et.group_id,
            ebr.target_id,
            ebr.option_id,
            ebr.info_id,
            ebr.score,
            et.target_name,
            ebr.create_time,
            ebr.update_time as behaviorUpdateTime,
            muor.update_time,
            muor.type,
            muor.medal_task_rule_id,
            ebr.deleted,
            ebr.info_name,
            ebr.data_source,
            ebr.appraisal_name,
            muor.id as operationId
        FROM
            medal_user_operation_record muor
                INNER JOIN evaluate_behaviour_record ebr ON ebr.id = muor.behaviour_record_id
                INNER JOIN evaluate_target et ON et.id = ebr.target_id
                AND et.deleted = 0
                INNER JOIN evaluate_target_group etg ON etg.id = et.group_id
                AND etg.deleted = 0
        WHERE
            muor.deleted = 0
          AND muor.student_id = #{studentId}
          AND muor.medal_task_id = #{medalTaskId}
          ORDER BY ebr.create_time DESC, muor.id DESC
    </select>


</mapper>

