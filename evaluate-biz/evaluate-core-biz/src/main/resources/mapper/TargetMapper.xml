<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.TargetMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.Target">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="iconUrl" column="icon_url" jdbcType="VARCHAR"/>
        <result property="schoolId" column="school_id" />
        <result property="campusId" column="campus_id" />
        <result property="targetName" column="target_name" jdbcType="VARCHAR"/>
        <result property="targetStatus" column="target_status" jdbcType="TINYINT"/>
        <result property="groupId" column="group_id" jdbcType="BIGINT"/>
        <result property="templateId" column="template_id" jdbcType="VARCHAR"/>
        <result property="submitType" column="submit_type" jdbcType="TINYINT"/>
        <result property="submitDate" column="submit_date" jdbcType="VARCHAR"/>
        <result property="remindTime" column="remind_time" jdbcType="VARCHAR"/>
        <result property="holidayNoticeFlag" column="holiday_notice_flag" jdbcType="TINYINT"/>
        <result property="mustSubmitFlag" column="must_submit_flag" jdbcType="TINYINT"/>
        <result property="urgeTime" column="urge_time" jdbcType="INTEGER"/>
        <result property="sendParentFlag" column="send_parent_flag" jdbcType="TINYINT"/>
        <result property="sortIndex" column="sort_index" />
        <result property="dataSource" column="data_source" />
        <result property="scoreControlName" column="score_control_name" />
        <result property="scoreControlType" column="score_control_type" />
        <result property="score" column="score" />
        <result property="scoreValue" column="score_value" />
        <result property="tenantId" column="tenant_id" />
        <result property="sourceTargetId" column="source_target_id" />
        <result property="pictureEvaluateFlag" column="picture_evaluate_flag" />
        <result property="pictureSortIndex" column="picture_sort_index" />
        <result property="staffFullCheckFlag" column="staff_full_check_flag" />
        <result property="templateInfoJson" column="template_info_json" />
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="listTargetWritePeopleMap" type="com.hailiang.model.vo.TargetWriteUserVO">
        <id property="targetId" column="id" jdbcType="BIGINT"/>
        <result property="targetName" column="target_name" jdbcType="VARCHAR"/>
        <result property="schoolId" column="school_id" jdbcType="BIGINT"/>
        <result property="campusId" column="campus_id" jdbcType="BIGINT"/>
        <result property="remindTimeStr" column="remind_time" jdbcType="VARCHAR"/>
        <result property="urgeTimeInt" column="urge_time" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="submitType" column="submitType"/>
        <result property="submitDate" column="submit_date"/>
        <result property="holidayNoticeFlag" column="holiday_notice_flag"/>
        <collection property="targetUserList" ofType="com.hailiang.model.vo.TargetTargetUserInnerTargetUserVO">
            <result property="submitType" column="submit_type" jdbcType="INTEGER"/>
            <result property="submitValue" column="submit_value" jdbcType="BIGINT"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        icon_url,school_id,campus_id,target_name,target_status,group_id,template_id,submit_type,submit_date,
        remind_time,holiday_notice_flag,must_submit_flag,urge_time,send_parent_flag,sort_index,data_source,
        score_control_name,score_control_type,score,score_value,tenant_id,source_target_id,picture_evaluate_flag,
        picture_sort_index,template_info_json,create_by,create_time,update_by,update_time,deleted
    </sql>

    <sql id="Base_Column_List_NO_TEXT">
        id,
        icon_url,school_id,campus_id,target_name,target_status,group_id,template_id,submit_type,submit_date,
        remind_time,holiday_notice_flag,must_submit_flag,urge_time,send_parent_flag,sort_index,data_source,
        score_control_name,score_control_type,score,score_value,tenant_id,source_target_id,picture_evaluate_flag,
        picture_sort_index,create_by,create_time,update_by,update_time,deleted
    </sql>
    <insert id="insertBatch" >
        insert into evaluate_target (id,
        icon_url,school_id,campus_id,target_name,target_status,group_id,template_id,submit_type,submit_date,
        remind_time,holiday_notice_flag,must_submit_flag,urge_time,send_parent_flag,sort_index,data_source,
        score_control_name,score_control_type,score,score_value,tenant_id,source_target_id,picture_evaluate_flag,
        picture_sort_index,staff_full_check_flag,template_info_json,create_by,create_time,update_by,update_time,deleted)
        values
        <foreach collection="targets" item="item"  separator=",">
            (#{item.id},#{item.iconUrl},#{item.schoolId},#{item.campusId},#{item.targetName},#{item.targetStatus},
            #{item.groupId},#{item.templateId},#{item.submitType},#{item.submitDate},
            #{item.remindTime},#{item.holidayNoticeFlag},#{item.mustSubmitFlag},#{item.urgeTime},
            #{item.sendParentFlag},#{item.sortIndex},#{item.dataSource},#{item.scoreControlName},#{item.scoreControlType},
            #{item.score},#{item.scoreValue},#{item.tenantId},
            #{item.sourceTargetId},#{item.pictureEvaluateFlag},#{item.pictureSortIndex},#{item.staffFullCheckFlag},#{item.templateInfoJson},
            #{item.createBy},now(),#{item.updateBy},now(),
            #{item.deleted})
        </foreach>
    </insert>
    <update id="batchDeleteByIds">
        update EVALUATE_TARGET
            set deleted = 1,update_by = #{updator},update_time = now()
        where deleted = 0 and id in
              <foreach collection="targetIdList" open="(" separator="," item="item" close=")">
                  #{item}
              </foreach>
    </update>
    <update id="batchUpdateStatus">
        update evaluate_target
            set target_status = #{status},update_by = #{updator},update_time = now()
        where id in
            <foreach collection="targetIdList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
    </update>

    <select id="listTargetWritePeople" resultMap="listTargetWritePeopleMap">
        select t1.school_id,
        t1.campus_id,
        t1.tenant_id,
        t1.id,
        t1.target_name,
        t1.remind_time,
        t1.urge_time,
        t1.submit_type submitType,
        t1.submit_date,
        t1.holiday_notice_flag,
        t2.submit_type,
        t2.submit_value
        from evaluate_target t1
        join evaluate_target_user t2 on t1.id = t2.target_id
        where t1.deleted = 0
        and t1.target_status = 1
        and t1.submit_type not in (5,6)
        and t1.must_submit_flag = #{mustSubmitFlag}
        and t1.data_source = 0
        and t2.deleted = 0;
    </select>

    <select id="getByIdWithOutDeleted" resultType="com.hailiang.model.entity.Target">
        select *
        from evaluate_target
        where id=#{id}
    </select>

    <select id="listTargetByIds" resultType="com.hailiang.model.entity.Target">
        select *
        from evaluate_target
        where id in
        <foreach collection='targetIds' open='(' close=')' index='index' item='item' separator=','>
            #{item}
        </foreach>
    </select>

    <!--获取当前校区下所有指标配置为老师和行政组织机构的数据-->
    <select id="listTargetSubmitInfos" resultType="com.hailiang.model.datastatistics.dto.TargetSubmitDTO">
        SELECT
            etu.submit_type,
            etu.submit_value
        FROM
            evaluate_target et
                INNER JOIN evaluate_target_user etu ON etu.target_id = et.id
        WHERE
            et.deleted = 0
          AND et.target_status = 1
          AND etu.deleted = 0
          AND etu.submit_type IN ( 1, 2, 3 )
          AND et.school_id = #{schoolId}
          AND et.campus_id = #{campusId}
    </select>

    <select id="listEnableTarget" resultType="com.hailiang.model.datastatistics.dto.TargetDTO">
        SELECT
            et.*,
            etg.group_name,
            etg.module_code
        FROM
            evaluate_target et
                INNER JOIN evaluate_target_group etg ON etg.id = et.group_id
        WHERE
            et.deleted = 0
          AND etg.deleted = 0
          AND et.target_status = 1
          AND et.school_id = #{schoolId}
          AND et.campus_id = #{campusId}
    </select>
    <select id="queryByCondition" resultType="com.hailiang.model.entity.Target">
        select
            <include refid="Base_Column_List_NO_TEXT" />
            from evaluate_target
        where deleted = 0
        <if test="targetQO.campusId != null">
            and campus_id = #{targetQO.campusId}
        </if>
        <if test="targetQO.dataSource != null">
            and data_source = #{targetQO.dataSource}
        </if>
        <if test="targetQO.dataSourceList != null and targetQO.dataSourceList.size > 0">
            and data_source in
            <foreach collection="targetQO.dataSourceList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="targetQO.status != null">
            and target_status = #{targetQO.status}
        </if>
        <if test="targetQO.targetGroupIdList != null and targetQO.targetGroupIdList.size > 0">
            and group_id in
                <foreach collection="targetQO.targetGroupIdList" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
        </if>
        <if test="targetQO.targetType != null">
            and target_type = #{targetQO.targetType}
        </if>
    </select>

</mapper>
