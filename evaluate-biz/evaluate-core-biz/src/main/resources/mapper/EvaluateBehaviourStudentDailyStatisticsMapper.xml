<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.EvaluateBehaviourStudentDailyStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hailiang.portrait.entity.StudentDailyStatisticsPO">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="school_id" property="schoolId"/>
        <result column="campus_id" property="campusId"/>
        <result column="campus_section_id" property="campusSectionId"/>
        <result column="campus_section_code" property="campusSectionCode"/>
        <result column="grade_id" property="gradeId"/>
        <result column="grade_code" property="gradeCode"/>
        <result column="class_id" property="classId"/>
        <result column="student_id" property="studentId"/>
        <result column="plus_moral_score" property="plusMoralScore"/>
        <result column="minus_moral_score" property="minusMoralScore"/>
        <result column="plus_wisdom_score" property="plusWisdomScore"/>
        <result column="minus_wisdom_score" property="minusWisdomScore"/>
        <result column="plus_sport_score" property="plusSportScore"/>
        <result column="minus_sport_score" property="minusSportScore"/>
        <result column="plus_pretty_score" property="plusPrettyScore"/>
        <result column="minus_pretty_score" property="minusPrettyScore"/>
        <result column="plus_work_score" property="plusWorkScore"/>
        <result column="minus_work_score" property="minusWorkScore"/>
        <result column="plus_total_score" property="plusTotalScore"/>
        <result column="minus_total_score" property="minusTotalScore"/>
        <result column="total_score" property="totalScore"/>
        <result column="statistics_time" property="statisticsTime"/>
        <result column="data_source" property="dataSource"/>
        <result column="school_year" property="schoolYear"/>
        <result column="term_name" property="termName"/>
        <result column="appraisal_count" property="appraisalCount"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        tenant_id, school_id, campus_id, campus_section_id, campus_section_code, grade_id, grade_code, class_id,
        student_id, plus_moral_score, minus_moral_score, plus_wisdom_score, minus_wisdom_score, plus_sport_score,
        minus_sport_score, plus_pretty_score, minus_pretty_score, plus_work_score, minus_work_score, plus_total_score,
        minus_total_score, total_score, statistics_time, data_source,school_year,term_name,appraisal_count
    </sql>
    <insert id="insertBatch">
        INSERT INTO evaluate_behaviour_student_daily_statistics (id,tenant_id, school_id, campus_id, campus_section_id, campus_section_code, grade_id, grade_code, class_id,
        student_id, plus_moral_score, minus_moral_score, plus_wisdom_score, minus_wisdom_score, plus_sport_score,
        minus_sport_score, plus_pretty_score, minus_pretty_score, plus_work_score, minus_work_score, plus_total_score,
        minus_total_score, total_score, statistics_time, data_source,school_year,term_name,appraisal_count,plus_other_score,minus_other_score,
        create_by,create_time,update_by,update_time,deleted)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.tenantId},#{item.schoolId},#{item.campusId},#{item.campusSectionId},#{item.campusSectionCode},#{item.gradeId}
            ,#{item.gradeCode},#{item.classId},#{item.studentId},#{item.plusMoralScore},#{item.minusMoralScore},#{item.plusWisdomScore},#{item.minusWisdomScore},#{item.plusSportScore},
             #{item.minusSportScore},#{item.plusPrettyScore},#{item.minusPrettyScore},#{item.plusWorkScore},#{item.minusWorkScore},#{item.plusTotalScore},
            #{item.minusTotalScore},#{item.totalScore},#{item.statisticsTime},#{item.dataSource},#{item.schoolYear},#{item.termName},#{item.appraisalCount},#{item.plusOtherScore},#{item.minusOtherScore}
            ,#{item.createBy},now(),#{item.updateBy},now(),#{item.deleted})
        </foreach>
    </insert>

    <update id="updateBeAppraisalCount">
        <foreach collection="stuBehaviourCountVOS" item="item" separator=";">
            update evaluate_behaviour_student_daily_statistics
            set appraisal_count = #{item.beAppraisalCount}
            where student_id = #{item.studentId} and statistics_time = #{item.submitTime}
        </foreach>
    </update>

    <select id="groupByStu" resultMap="BaseResultMap">
        select student_id, sum(total_score) from evaluate_behaviour_student_daily_statistics
        where campus_id = #{campusId}
        and class_id = #{classId}
        and deleted = 0
        and statistics_time between #{startTime} and #{endTime}
        group by student_id
        order by 2 desc
    </select>
</mapper>
