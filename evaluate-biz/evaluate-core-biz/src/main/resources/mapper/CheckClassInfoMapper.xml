<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.CheckClassInfoMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.CheckClassInfo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
        <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
        <result property="campusSectionId" column="campus_section_id" jdbcType="VARCHAR"/>
        <result property="campusSectionCode" column="campus_section_code" jdbcType="VARCHAR"/>
        <result property="schoolYear" column="school_year" jdbcType="VARCHAR"/>
        <result property="gradeId" column="grade_id" jdbcType="VARCHAR"/>
        <result property="gradeCode" column="grade_code" jdbcType="VARCHAR"/>
        <result property="classId" column="class_id" jdbcType="VARCHAR"/>
        <result property="className" column="class_name" jdbcType="VARCHAR"/>
        <result property="checkDimId" column="check_dim_id" jdbcType="BIGINT"/>
        <result property="checkItemId" column="check_item_id" jdbcType="BIGINT"/>
        <result property="checkTemplateId" column="check_template_id" jdbcType="VARCHAR"/>
        <result property="checkInfoId" column="check_info_id" jdbcType="VARCHAR"/>
        <result property="checkUserName" column="check_user_name" jdbcType="VARCHAR"/>
        <result property="checkRoleCode" column="check_role_code" jdbcType="VARCHAR"/>
        <result property="checkDate" column="check_date" jdbcType="TIMESTAMP"/>
        <result property="checkName" column="check_name" jdbcType="VARCHAR"/>
        <result property="totalScore" column="total_score" jdbcType="DECIMAL"/>
        <result property="dataSource" column="data_source" jdbcType="TINYINT"/>
        <result property="checkStatus" column="check_status" jdbcType="TINYINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="queryCheckClassInfoPage" resultType="com.hailiang.model.entity.CheckClassInfo">
        select
        id,
        school_year,
        class_id,
        class_name,
        check_dim_id,
        total_score,
        check_item_id,
        check_date,
        check_user_name,
        check_role_code,
        check_status,
        appeal_status,
        check_info_id
        from check_class_info
        where
        data_source = 0
        and deleted = 0
        and campus_id = #{param.campusId}
        <if test="param.dimId != null">
            and check_dim_id = #{param.dimId}
        </if>
        <if test="param.checkItemName != null and param.checkItemName != ''">
            and check_item_name like CONCAT('%', #{param.checkItemName}, '%')
        </if>
        <if test="param.schoolYear != null">
            and school_year = #{param.schoolYear}
        </if>
        <if test="param.sectionCode != null">
            and campus_section_code = #{param.sectionCode}
        </if>
        <if test="param.campusSectionId != null">
            and campus_section_Id= #{param.campusSectionId}
        </if>
        <if test="param.classId != null">
            and class_id = #{param.classId}
        </if>
        <if test="param.gradeId != null">
            and grade_id = #{param.gradeId}
        </if>
        <if test="param.mineClassIdList != null and param.mineClassIdList.size() >0">
            and class_id in
            <foreach collection="param.mineClassIdList" item="classId" open="(" separator="," close=")">
                #{classId}
            </foreach>
        </if>
        <if test="param.checkRoleCode != null and param.checkRoleCode != ''">
            and check_role_code = #{param.checkRoleCode}
        </if>
        <if test="param.checkStatus != null">
            and check_status = #{param.checkStatus}
        </if>
        <if test="param.appealStatus != null">
            and appeal_status = #{param.appealStatus}
        </if>
        <if test="param.checkStartTime != null and param.checkEndTime != null">
            and check_date between #{param.checkStartTime} and #{param.checkEndTime}
        </if>
        <if test="param.checkUserName != null and param.checkUserName != ''">
            and check_user_name like CONCAT('%', #{param.checkUserName}, '%')
        </if>
        order by create_time desc, class_id asc, check_item_id asc
    </select>

    <select id="getCheckItemBasicInfo" resultType="com.hailiang.model.dto.check.CheckItemBasicInfoDTO">
        SELECT ci.id     AS itemId,
               ci.`name` AS itemName,
               ci.check_obj_type,
               ci.template_id,
               ci.opt_student,
               ci.icon_url,
               cd.id     AS dimId,
               cd.`name` AS dimName,
               cg.id     AS groupId,
               cg.`name` AS groupName
        FROM check_item ci
                 LEFT JOIN check_dim cd ON cd.id = ci.dim_id
            AND cd.deleted = 0
                 LEFT JOIN check_group cg ON cg.id = ci.group_id
            AND cg.deleted = 0
        WHERE ci.deleted = 0
          AND ci.`status` = 1
          AND ci.id = #{itemId}
          AND ci.tenant_id = #{tenantId}
          AND ci.school_id = #{schoolId}
          AND ci.campus_id = #{campusId}
    </select>

    <select id="listCheckItemBasicInfo" resultType="com.hailiang.model.dto.check.CheckItemBasicInfoDTO">
        SELECT
        ci.id AS itemId,
        ci.`name` AS itemName,
        ci.check_obj_type,
        ci.template_id,
        ci.opt_student,
        ci.icon_url,
        cd.id AS dimId,
        cd.`name` AS dimName,
        cg.id AS groupId,
        cg.`name` AS groupName
        FROM
        check_item ci
        LEFT JOIN check_dim cd ON cd.id = ci.dim_id
        LEFT JOIN check_group cg ON cg.id = ci.group_id
        WHERE
        ci.tenant_id = #{tenantId}
        AND ci.school_id = #{schoolId}
        AND ci.campus_id = #{campusId}
        AND ci.id in
        <foreach item="item" collection="list" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryClassDimScoreList" resultType="com.hailiang.model.vo.CheckClassDimScoreListVO">
        select class_id, class_name, check_dim_id, sum(total_score) totalScore
        from check_class_info
        where data_source = 0
          and deleted = 0
          and check_status in (1, 3)
          and campus_id = #{campusId}
        <if test="campusSectionId != null and campusSectionId != ''">
            and campus_section_id = #{campusSectionId}
        </if>
        <if test="schoolYear != null and schoolYear != ''">
            and school_year = #{schoolYear}
        </if>
          and check_date between #{startTime} and #{endTime}
        group by class_id, check_dim_id
    </select>

    <select id="queryClassScoreList" resultType="com.hailiang.model.vo.CheckClassScoreListVO">
        select class_id, class_name, sum(total_score) totalScore
        from check_class_info
        where data_source = 0
        and deleted = 0
        and check_status in (1, 3)
        and campus_id = #{campusId}
        <if test="sectionCode != null and sectionCode != ''">
            and campus_section_code = #{sectionCode}
        </if>
        <if test="campusSectionId != null and campusSectionId != ''">
            and campus_section_id = #{campusSectionId}
        </if>
        <if test="schoolYear != null and schoolYear != ''">
            and school_year = #{schoolYear}
        </if>
        <if test="gradeId != null">
            and grade_id = #{gradeId}
        </if>
        and check_date between #{startTime} and #{endTime}
        group by class_id
    </select>

    <select id="isExistsAuditRecord" resultType="java.lang.Boolean">
        select exists
        (select 1
        from check_class_info
        where
        class_id in
        <foreach collection="classIds" item="classId" open="(" separator="," close=")">
            #{classId}
        </foreach>
        and check_date between #{startTime} and #{endTime}
        and tenant_id = #{tenantId}
        and school_id = #{schoolId}
        and campus_id = #{campusId}
        and appeal_status = 1
        and data_source = 0
        and deleted = 0)
    </select>
</mapper>
