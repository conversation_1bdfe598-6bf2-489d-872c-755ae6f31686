<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.CheckDimMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.CheckDim">
        <!--@mbg.generated-->
        <!--@Table check_dim-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="campus_id" jdbcType="VARCHAR" property="campusId"/>
        <result column="source_type" jdbcType="CHAR" property="sourceType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="is_default" jdbcType="TINYINT" property="isDefault"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cext" jdbcType="VARCHAR" property="cext"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id, school_id, campus_id, source_type,`name`, is_default, deleted, create_by, update_by,
        create_time, update_time,cext
    </sql>

    <insert id="saveBatch">
        insert into check_dim (id, tenant_id, school_id, campus_id, source_type,`name`,
        is_default, deleted, create_by,update_by, create_time, update_time) values
        <foreach collection="dims" item="dim" separator=",">
            (#{dim.id,jdbcType=BIGINT},#{dim.tenantId,jdbcType=VARCHAR},#{dim.schoolId,jdbcType=VARCHAR},
            #{dim.campusId,jdbcType=VARCHAR},#{dim.sourceType,jdbcType=VARCHAR},#{dim.name,jdbcType=VARCHAR},
            #{dim.isDefault,jdbcType=BOOLEAN},0,#{dim.createBy,jdbcType=VARCHAR},#{dim.updateBy,jdbcType=VARCHAR},
            now(),now())
        </foreach>
    </insert>
</mapper>