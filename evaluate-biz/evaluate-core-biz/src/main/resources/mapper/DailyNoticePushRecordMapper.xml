<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.DailyNoticePushRecordMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.DailyNoticePushRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
            <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
            <result property="classId" column="class_id" jdbcType="VARCHAR"/>
            <result property="studentId" column="student_id" jdbcType="VARCHAR"/>
            <result property="studentCode" column="student_code" jdbcType="VARCHAR"/>
            <result property="studentName" column="student_name" jdbcType="VARCHAR"/>
            <result property="lastRetryPushTime" column="last_retry_push_time" jdbcType="TIMESTAMP"/>
            <result property="retryTimes" column="retry_times" jdbcType="TINYINT"/>
            <result property="pushStatus" column="push_status" jdbcType="TINYINT"/>
            <result property="lastPushFailItem" column="last_push_fail_item" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="requestParam" column="request_param" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,school_id,
        campus_id,class_id,student_id,
        student_code,student_name,last_retry_push_time,
        retry_times,push_status,last_push_fail_item,
        create_by,create_time,update_by,
        update_time,deleted,request_param
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from evaluate_daily_notice_push_record
        where  id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from evaluate_daily_notice_push_record
        where  id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hailiang.model.entity.DailyNoticePushRecord" useGeneratedKeys="true">
        insert into evaluate_daily_notice_push_record
        ( id,tenant_id,school_id
        ,campus_id,class_id,student_id
        ,student_code,student_name,last_retry_push_time
        ,retry_times,push_status,last_push_fail_item
        ,create_by,create_time,update_by
        ,update_time,deleted,request_param)
        values (#{id,jdbcType=BIGINT},#{tenantId,jdbcType=VARCHAR},#{schoolId,jdbcType=VARCHAR}
        ,#{campusId,jdbcType=VARCHAR},#{classId,jdbcType=VARCHAR},#{studentId,jdbcType=VARCHAR}
        ,#{studentCode,jdbcType=VARCHAR},#{studentName,jdbcType=VARCHAR},#{lastRetryPushTime,jdbcType=TIMESTAMP}
        ,#{retryTimes,jdbcType=TINYINT},#{pushStatus,jdbcType=TINYINT},#{lastPushFailItem,jdbcType=VARCHAR}
        ,#{createBy,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR}
        ,#{updateTime,jdbcType=TIMESTAMP},#{deleted,jdbcType=TINYINT},#{requestParam,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.hailiang.model.entity.DailyNoticePushRecord" useGeneratedKeys="true">
        insert into evaluate_daily_notice_push_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="tenantId != null">tenant_id,</if>
                <if test="schoolId != null">school_id,</if>
                <if test="campusId != null">campus_id,</if>
                <if test="classId != null">class_id,</if>
                <if test="studentId != null">student_id,</if>
                <if test="studentCode != null">student_code,</if>
                <if test="studentName != null">student_name,</if>
                <if test="lastRetryPushTime != null">last_retry_push_time,</if>
                <if test="retryTimes != null">retry_times,</if>
                <if test="pushStatus != null">push_status,</if>
                <if test="lastPushFailItem != null">last_push_fail_item,</if>
                <if test="createBy != null">create_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="tenantId != null">#{tenantId,jdbcType=VARCHAR},</if>
                <if test="schoolId != null">#{schoolId,jdbcType=VARCHAR},</if>
                <if test="campusId != null">#{campusId,jdbcType=VARCHAR},</if>
                <if test="classId != null">#{classId,jdbcType=VARCHAR},</if>
                <if test="studentId != null">#{studentId,jdbcType=VARCHAR},</if>
                <if test="studentCode != null">#{studentCode,jdbcType=VARCHAR},</if>
                <if test="studentName != null">#{studentName,jdbcType=VARCHAR},</if>
                <if test="lastRetryPushTime != null">#{lastRetryPushTime,jdbcType=TIMESTAMP},</if>
                <if test="retryTimes != null">#{retryTimes,jdbcType=TINYINT},</if>
                <if test="pushStatus != null">#{pushStatus,jdbcType=TINYINT},</if>
                <if test="lastPushFailItem != null">#{lastPushFailItem,jdbcType=VARCHAR},</if>
                <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.hailiang.model.entity.DailyNoticePushRecord">
        update evaluate_daily_notice_push_record
        <set>
                <if test="tenantId != null">
                    tenant_id = #{tenantId,jdbcType=VARCHAR},
                </if>
                <if test="schoolId != null">
                    school_id = #{schoolId,jdbcType=VARCHAR},
                </if>
                <if test="campusId != null">
                    campus_id = #{campusId,jdbcType=VARCHAR},
                </if>
                <if test="classId != null">
                    class_id = #{classId,jdbcType=VARCHAR},
                </if>
                <if test="studentId != null">
                    student_id = #{studentId,jdbcType=VARCHAR},
                </if>
                <if test="studentCode != null">
                    student_code = #{studentCode,jdbcType=VARCHAR},
                </if>
                <if test="studentName != null">
                    student_name = #{studentName,jdbcType=VARCHAR},
                </if>
                <if test="lastRetryPushTime != null">
                    last_retry_push_time = #{lastRetryPushTime,jdbcType=TIMESTAMP},
                </if>
                <if test="retryTimes != null">
                    retry_times = #{retryTimes,jdbcType=TINYINT},
                </if>
                <if test="pushStatus != null">
                    push_status = #{pushStatus,jdbcType=TINYINT},
                </if>
                <if test="lastPushFailItem != null">
                    last_push_fail_item = #{lastPushFailItem,jdbcType=VARCHAR},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="deleted != null">
                    deleted = #{deleted,jdbcType=TINYINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.hailiang.model.entity.DailyNoticePushRecord">
        update evaluate_daily_notice_push_record
        set
            tenant_id =  #{tenantId,jdbcType=VARCHAR},
            school_id =  #{schoolId,jdbcType=VARCHAR},
            campus_id =  #{campusId,jdbcType=VARCHAR},
            class_id =  #{classId,jdbcType=VARCHAR},
            student_id =  #{studentId,jdbcType=VARCHAR},
            student_code =  #{studentCode,jdbcType=VARCHAR},
            student_name =  #{studentName,jdbcType=VARCHAR},
            last_retry_push_time =  #{lastRetryPushTime,jdbcType=TIMESTAMP},
            retry_times =  #{retryTimes,jdbcType=TINYINT},
            push_status =  #{pushStatus,jdbcType=TINYINT},
            last_push_fail_item =  #{lastPushFailItem,jdbcType=VARCHAR},
            create_by =  #{createBy,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_by =  #{updateBy,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            deleted =  #{deleted,jdbcType=TINYINT}
        where   id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
