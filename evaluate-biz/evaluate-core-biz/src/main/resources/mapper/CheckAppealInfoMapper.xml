<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.CheckAppealInfoMapper">

    <select id="listApprovalRecord" resultType="com.hailiang.model.dto.check.appeal.CheckApprovalBasicDTO">
        SELECT
            cai.submit_user_id,
            cai.submit_user_name,
            cci.id as checkClassInfoId,
            cci.check_item_id,
            cci.check_item_name,
            cci.total_score,
            cci.class_name,
            cci.check_dim_id,
            cci.check_dim_name,
            cci.appeal_status
        FROM
            check_appeal_info cai
                LEFT JOIN check_class_info cci ON cci.id = cai.check_class_info_id AND cci.deleted = 0
          WHERE cai.deleted = 0
            AND cai.tenant_id = #{tenantId}
            AND cai.school_id = #{schoolId}
            AND cai.campus_id = #{campusId}
            AND cai.appeal_status IN
            <foreach item="status" collection="list" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
            <choose>
                <when test="approvalUserId != null and approvalUserId != ''">
                    AND cai.approval_user_id =#{approvalUserId}
                    ORDER BY cai.update_time DESC
                </when>
                <otherwise>
                    ORDER BY cai.id DESC
                </otherwise>
            </choose>



    </select>


</mapper>
