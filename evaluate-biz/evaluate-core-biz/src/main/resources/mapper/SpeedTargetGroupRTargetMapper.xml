<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.SpeedTargetGroupRTargetMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.SpeedTargetGroupRTargetPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="speedTargetGroupId" column="speed_target_group_id" jdbcType="BIGINT"/>
        <result property="targetId" column="target_id" jdbcType="BIGINT"/>
        <result property="optionId" column="option_id" jdbcType="VARCHAR"/>
        <result property="infoType" column="info_type" jdbcType="TINYINT"/>
        <result property="appType" column="app_type" jdbcType="TINYINT"/>
        <result property="sortIndex" column="sort_index" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <select id="listSelectedOptions" resultType="com.hailiang.model.dto.response.speed.SpeedGroupSelectedOptionsDTO">
        select target_id                   as targetId,
               et.target_name              as targetName,
               estgr.option_id             as optionId,
               if(option_id is null, 1, 2) as infoType,
               target_name,
               app_type,
               estgr.sort_index
        from evaluate_speed_target_group estg
                 inner join evaluate_speed_target_group_r_target estgr
                            on estg.id = estgr.speed_target_group_id
                 inner join evaluate_target et on estgr.target_id = et.id
        ${ew.customSqlSegment}
    </select>

    <select id="listSysSelectedOptions" resultType="com.hailiang.model.dto.response.speed.SpeedGroupSelectedOptionsDTO">
        select target_id                   as targetId,
               et.target_name              as targetName,
               estgr.option_id             as optionId,
               if(option_id is null, 1, 2) as infoType,
               target_name,
               app_type,
               estgr.sort_index
        from evaluate_speed_target_group estg
                 inner join evaluate_speed_target_group_r_target estgr
                            on estg.id = estgr.speed_target_group_id
                 inner join sys_target et on estgr.target_id = et.id
        ${ew.customSqlSegment}
    </select>

    <select id="listRemoteSelectedOptions"
            resultType="com.hailiang.model.dto.response.speed.remote.RemoteGroupSelectedOptionsDTO">
        select
        estg.id                     as groupId,
        target_id                   as targetId,
        et.target_name              as targetName,
        estgr.option_id             as optionId,
        if(option_id is null, 1, 2) as infoType,
        target_name,
        app_type,
        estgr.sort_index
        from evaluate_speed_target_group estg
        inner join evaluate_speed_target_group_r_target estgr
        on estg.id = estgr.speed_target_group_id
        inner join evaluate_target et on estgr.target_id = et.id
        ${ew.customSqlSegment}
    </select>
</mapper>
