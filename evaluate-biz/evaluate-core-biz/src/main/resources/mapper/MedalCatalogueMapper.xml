<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.MedalCatalogueMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.MedalCatalogue">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
        <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,tenant_id,school_id,
        campus_id,name,create_by,
        create_time,update_by,update_time,
        deleted
    </sql>
    <select id="listMedalCatalogues" resultType="com.hailiang.model.entity.MedalCatalogue">
        SELECT
        mc.id,
        mc.NAME
        FROM
        medal_info mi
        INNER JOIN medal_catalogue mc ON mc.id = mi.medal_catalogue_id
        WHERE
        mi.tenant_id = #{tenantId}
        AND mi.school_id = #{schoolId}
        AND mi.campus_id = #{campusId}
        and mi.id in
        <foreach item="item" collection="list" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="listMedalCataloguesBySchoolId" resultType="com.hailiang.model.entity.MedalCatalogue">
        SELECT
        mc.id,
        mc.NAME
        FROM
        medal_info mi
        INNER JOIN medal_catalogue mc ON mc.id = mi.medal_catalogue_id
        WHERE
        mi.school_id = #{schoolId}
    </select>
</mapper>
