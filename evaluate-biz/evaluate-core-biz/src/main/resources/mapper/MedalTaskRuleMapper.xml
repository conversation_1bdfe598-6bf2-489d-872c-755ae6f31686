<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.MedalTaskRuleMapper">
    <update id="batchUpdateStatus">
        update medal_task_rule set status = #{status}
            where id in
            <foreach collection="medalTaskRuleIds" open="(" separator="," item="item" close=")">
                #{item}
            </foreach>
    </update>

    <select id="getMedalRuleDetail" resultType="com.hailiang.model.dto.activity.rule.save.MedalRuleDetailDTO">
        SELECT
            mtr.type as ruleType,
            mtrt.submit_name ruleName,
            mtrt.target_value
        FROM
            medal_task_rule mtr
                INNER JOIN medal_task_rule_target mtrt ON mtrt.medal_task_rule_id = mtr.id
                AND mtrt.deleted = 0
        WHERE mtr.deleted = 0
          AND mtr.id = #{taskRuleId}

    </select>

</mapper>

