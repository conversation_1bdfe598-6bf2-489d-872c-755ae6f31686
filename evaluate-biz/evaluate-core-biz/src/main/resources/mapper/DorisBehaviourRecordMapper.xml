<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.doris.DorisBehaviourRecordMapper">

    <select id="listFixPointUse" resultType="com.hailiang.model.entity.BehaviourRecord">
        select main.*
        from evaluate_behaviour_record main
                 inner join point_change_detail t on cast(main.id as char) = t.source_id
                 inner join evaluate_third_data_info third
                            on third.local_record_id = main.id
        where (main.data_source = 6 or main.data_source = 4)
          and main.deleted = 1
          and main.update_time between #{startTime}
            and #{endTime}
          and t.scene_detail_type = 11
          and third.operate_flag = 3;
    </select>




    <select id="getAvgScore" resultType="java.math.BigDecimal">
        SELECT ROUND(AVG(total_score), 2)
        FROM (
        select student_id,
        (sum(total_score)
<!--        <if test="schoolId != null and schoolId != ''-->
<!--        and campusId != null and campusId != ''-->
<!--        and schoolYear != null and schoolYear != ''-->
<!--        and termName != null and termName != ''-->
<!--        and moduleCodes != null and moduleCodes.size() > 0 ">-->
<!--            +(-->
<!--            select-->
<!--            coalesce(sum(initial_score), 0)-->
<!--            from-->
<!--            evaluate_initial_score eis-->
<!--            where-->
<!--            eis.school_id = #{schoolId}-->
<!--            and eis.campus_id = #{campusId}-->
<!--            and eis.school_year =#{schoolYear}-->
<!--            and eis.term_name = #{termName}-->
<!--            and eis.initial_score_type in-->
<!--            <foreach collection="moduleCodes" item="moduleCode" open="(" separator="," close=")">-->
<!--                #{moduleCode}-->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
        ) as total_score
        FROM (
        SELECT student_id, SUM(score) AS total_score
        FROM evaluate_behaviour_record br
        WHERE is_score = 1
        <if test="contrastRange != null and contrastRange == 1">
            AND class_id = #{classId}
        </if>
        <if test="contrastRange != null and contrastRange == 2">
            AND grade_id = #{gradeId}
        </if>
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="unNeedBehaviourRecordIds != null and unNeedBehaviourRecordIds.size() > 0">
            AND id NOT IN
            <foreach collection="unNeedBehaviourRecordIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="submitIds != null and submitIds.size() > 0">
            AND (option_id IN
            <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                #{submitId}
            </foreach>
            OR target_id IN
            <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                #{submitId}
            </foreach>
            )
        </if>
        GROUP BY student_id
        <if test="includeHelpBehaviour">
            union all
            SELECT student_id, SUM(score) AS total_score
            FROM evaluate_help_behaviour_record hbr
            <where>
                <if test="contrastRange != null and contrastRange == 1">
                    AND class_id = #{classId}
                </if>
                <if test="contrastRange != null and contrastRange == 2">
                    AND grade_id = #{gradeId}
                </if>
                <if test="startTime != null">
                    AND submit_time &gt;= #{startTime}
                </if>
                <if test="endTime != null">
                    AND submit_time &lt;= #{endTime}
                </if>
                <if test="unNeedBehaviourRecordIds != null and unNeedBehaviourRecordIds.size() > 0">
                    AND id NOT IN
                    <foreach collection="unNeedBehaviourRecordIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                <if test="submitIds != null and submitIds.size() > 0">
                    AND (option_id IN
                    <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                        #{submitId}
                    </foreach>
                    OR target_id IN
                    <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                        #{submitId}
                    </foreach>
                    )
                </if>
            </where>
            GROUP BY student_id
        </if>
        )as all_view
        group by student_id
        ) AS student_scores
    </select>

    <select id="getStudentScore" resultType="java.math.BigDecimal">
        SELECT ROUND(SUM(score), 2)
        from (
        select SUM(score) as score
        FROM evaluate_behaviour_record br
        WHERE is_score = 1
        AND student_id = #{studentId}
        <if test="contrastRange != null and contrastRange == 1">
            AND class_id = #{classId}
        </if>
        <if test="contrastRange != null and contrastRange == 2">
            AND grade_id = #{gradeId}
        </if>
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="unNeedBehaviourRecordIds != null and unNeedBehaviourRecordIds.size() > 0">
            AND id NOT IN
            <foreach collection="unNeedBehaviourRecordIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="submitIds != null and submitIds.size() > 0">
            AND (option_id IN
            <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                #{submitId}
            </foreach>
            OR target_id IN
            <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                #{submitId}
            </foreach>
            )
        </if>
        <if test="includeHelpBehaviour">
            union all

            SELECT SUM(score) as score
            FROM evaluate_help_behaviour_record hbr
            WHERE
            student_id = #{studentId}
            <if test="contrastRange != null and contrastRange == 1">
                AND class_id = #{classId}
            </if>
            <if test="contrastRange != null and contrastRange == 2">
                AND grade_id = #{gradeId}
            </if>
            <if test="startTime != null">
                AND submit_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND submit_time &lt;= #{endTime}
            </if>
            <if test="submitIds != null and submitIds.size() > 0">
                AND (option_id IN
                <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                    #{submitId}
                </foreach>
                OR target_id IN
                <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                    #{submitId}
                </foreach>
                )
            </if>
        </if>
        ) as final_view
    </select>

    <!-- 使用Doris的窗口函数优化查询 -->
    <select id="getMaxScore" resultType="java.math.BigDecimal">
        SELECT ROUND(MAX(total_score), 2)
        FROM (
        select student_id,
        SUM( total_score ) AS total_score from (
        SELECT student_id, SUM(score) AS total_score
        FROM evaluate_behaviour_record br
        WHERE is_score = 1
        <if test="contrastRange != null and contrastRange == 1">
            AND class_id = #{classId}
        </if>
        <if test="contrastRange != null and contrastRange == 2">
            AND grade_id = #{gradeId}
        </if>
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="unNeedBehaviourRecordIds != null and unNeedBehaviourRecordIds.size() > 0">
            AND id NOT IN
            <foreach collection="unNeedBehaviourRecordIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="submitIds != null and submitIds.size() > 0">
            AND (option_id IN
            <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                #{submitId}
            </foreach>
            OR target_id IN
            <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                #{submitId}
            </foreach>
            )
        </if>
        group by student_id
        <if test="includeHelpBehaviour">
            union all

            SELECT student_id, SUM(score) AS total_score
            FROM evaluate_help_behaviour_record hbr
            <where>
                <if test="contrastRange != null and contrastRange == 1">
                    AND class_id = #{classId}
                </if>
                <if test="contrastRange != null and contrastRange == 2">
                    AND grade_id = #{gradeId}
                </if>
                <if test="startTime != null">
                    AND submit_time &gt;= #{startTime}
                </if>
                <if test="endTime != null">
                    AND submit_time &lt;= #{endTime}
                </if>
                <if test="submitIds != null and submitIds.size() > 0">
                    AND (option_id IN
                    <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                        #{submitId}
                    </foreach>
                    OR target_id IN
                    <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                        #{submitId}
                    </foreach>
                    )
                </if>
            </where>
            group by student_id
        </if>
        ) as all_view
        group by student_id
        ) AS student_scores
    </select>

    <select id="listByTimeAndStudentId" resultType="com.hailiang.portrait.vo.StuBehaviourCountVO">
        select date(submit_time) as submit_time,student_id,count(*) as be_appraisal_count
        from evaluate_behaviour_record
        where
        submit_time between #{startTime} and #{endTime}
        and deleted = 0
        group by date(submit_time),student_id
    </select>

    <sql id="pageCondition">
        <!--固定条件-->
        and campus_id = #{pageProcessRecordParam.campusId}
        and campus_section_id = #{pageProcessRecordParam.campusSectionId}
        <!--筛选条件-->
        <if test="pageProcessRecordParam.currentUserId != null">
            and appraisal_id = #{pageProcessRecordParam.currentUserId}
        </if>
        <if test="pageProcessRecordParam.gradeId != null and  pageProcessRecordParam.gradeId != ''">
            and grade_id = #{pageProcessRecordParam.gradeId}
        </if>
        <if test="pageProcessRecordParam.classId != null and  pageProcessRecordParam.classId != ''">
            and class_id = #{pageProcessRecordParam.classId}
        </if>
        <if test="pageProcessRecordParam.studentIds != null and pageProcessRecordParam.studentIds.size() > 0">
            and student_id in
            <foreach collection="pageProcessRecordParam.studentIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageProcessRecordParam.moduleCodes != null and pageProcessRecordParam.moduleCodes.size() > 0">
            and module_code in
            <foreach collection="pageProcessRecordParam.moduleCodes" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageProcessRecordParam.targetIds != null and pageProcessRecordParam.targetIds.size() > 0">
            and target_id in
            <foreach collection="pageProcessRecordParam.targetIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--时间筛选条件-->
        <if test="pageProcessRecordParam.startTime != null">
            AND submit_time &gt;= #{pageProcessRecordParam.startTime}
        </if>
        <if test="pageProcessRecordParam.endTime != null">
            AND submit_time &lt;= #{pageProcessRecordParam.endTime}
        </if>
        <!--数据权限-->
        <if test="pageProcessRecordParam.dataClassIds != null and pageProcessRecordParam.dataClassIds.size() > 0">
            and class_id in
            <foreach collection="pageProcessRecordParam.dataClassIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--数据来源-->
        <if test="pageProcessRecordParam.dataSources != null and pageProcessRecordParam.dataSources.size() > 0">
            and data_source in
            <foreach collection="pageProcessRecordParam.dataSources" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--筛选指标范围-校级指标、提测、德育产生的点评明细-->
        and (
        data_source in (0,1,2,3,9,15,16,10,11)
        or
        ((data_source in (4,5,6) ) and info_type in (1,2,3,4))
        )
    </sql>

    <select id="pageProcessEvaRecordCountV1" resultType="java.lang.Integer">
        select
        count(*)
        from evaluate_behaviour_record
        where deleted = 0
        <include refid="pageCondition"/>
    </select>

    <select id="pageProcessEvaRecordV1" resultType="com.hailiang.model.processeva.entity.BehaviourRecordEntity">
        select
        id,
        campus_id,
        class_id,
        student_id,
        module_code,
        target_id,
        target_name,
        info_id,
        info_type,
        info_name,
        score_type,
        is_score,
        score,
        score_value,
        data_source,
        submit_time,
        appraisal_name,
        subject_code
        from evaluate_behaviour_record
        where deleted = 0
        <include refid="pageCondition"/>
        <!--按照记录生成时间倒序-->
        order by submit_time desc ,id desc
        limit #{pageProcessRecordParam.pageNum},#{pageProcessRecordParam.pageSize}
    </select>

    <select id="listInfoIdByRecordIds" resultType="com.hailiang.model.processeva.entity.BehaviourIdInfoIdEntity">
        select
        id,
        info_id,
        data_source,
        target_id
        from evaluate_behaviour_record where id in
        <foreach collection="behaviourRecordIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listBehaviourRecordByRecordIds" resultType="com.hailiang.model.processeva.entity.BehaviourRecordEntity">
        select
        id,
        class_id,
        student_id,
        module_code,
        target_id,
        target_name,
        option_id,
        info_id,
        info_type,
        info_name,
        score_type,
        is_score,
        score,
        score_value,
        data_source,
        submit_time,appraisal_name
        from evaluate_behaviour_record where id in
        <foreach collection="behaviourRecordIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by submit_time desc
    </select>

    <select id="listStudentScore" resultType="com.hailiang.model.vo.studentmodel.EvaluateStudentScore">
        SELECT
            student_id AS studentId,ROUND(SUM(score), 2) AS totalScore
        FROM
            evaluate_behaviour_record
        WHERE deleted = 0
          and is_score = 1
          and campus_id = #{campusId}
          and data_source in (0,2,4,5,6,9)
          and info_type not in (5,6)
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="unNeedBehaviourRecordIds != null and unNeedBehaviourRecordIds.size() > 0">
            AND id NOT IN
            <foreach collection="unNeedBehaviourRecordIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="submitIds != null and submitIds.size() > 0">
            AND (option_id IN
            <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                #{submitId}
            </foreach>
            OR target_id IN
            <foreach collection="submitIds" item="submitId" open="(" separator="," close=")">
                #{submitId}
            </foreach>
            )
        </if>
        <!--<if test="targetIds != null and targetIds.size() > 0">
            AND target_id IN
            <foreach collection="targetIds" item="targetId" open="(" separator="," close=")">
                #{targetId}
            </foreach>
        </if>
        <if test="optionIds != null and optionIds.size() > 0">
            AND option_id IN
            <foreach collection="optionIds" item="optionId" open="(" separator="," close=")">
                #{optionId}
            </foreach>
        </if>-->
        GROUP BY
            student_id;
    </select>

    <select id="pageTotalRecordByCondition"
            resultType="com.hailiang.model.datastatistics.dto.BehaviourRecordNewDTO">
        SELECT
            e1.student_id,
            e1.submit_time,
            e1.appraisal_id,
            e1.appraisal_name,
            e1.target_id,
            e1.option_id,
            e1.score,
            e1.subject_code,
            e1.score_value,
            e1.is_score,
            e1.deleted,
            e1.class_id
        FROM evaluate_behaviour_record e1
                 inner JOIN (SELECT id
                             FROM evaluate_behaviour_record br
                                 ${ew.customSqlSegment}
                             limit #{offset},#{pageSize}
        ) e2 using (id)
    </select>

    <select id="countByConditions"
            resultType="java.lang.Long">
        SELECT count(id)
        FROM evaluate_behaviour_record br
            ${ew.customSqlSegment}
    </select>


    <select id="queryClassSubject" resultType="com.hailiang.model.entity.BehaviourRecord">
        select id,
        class_id,
        student_id,
        module_code,
        target_id,
        target_name,
        option_id,
        info_id,
        info_type,
        info_name,
        score_type,
        is_score,
        score,
        score_value,
        data_source,
        submit_time,
        appraisal_name,
        subject_code
        from evaluate_behaviour_record
        where deleted = 0
        and is_score = 1
        and submit_time between #{classSubjectQueryDTO.startTime}
        and #{classSubjectQueryDTO.endTime}
        <if test="classSubjectQueryDTO.classIds != null
            and classSubjectQueryDTO.classIds.size > 0">
            and class_id in
            <foreach collection="classSubjectQueryDTO.classIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="classSubjectQueryDTO.subjectCodes != null
            and classSubjectQueryDTO.subjectCodes.size > 0">
            and subject_code in
            <foreach collection="classSubjectQueryDTO.subjectCodes" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="(classSubjectQueryDTO.targetIds != null and classSubjectQueryDTO.targetIds.size > 0)
                or (classSubjectQueryDTO.optionIds != null and classSubjectQueryDTO.optionIds.size > 0)">
            and (
            <if test="(classSubjectQueryDTO.targetIds != null and classSubjectQueryDTO.targetIds.size > 0)">
                target_id in
                <foreach collection="classSubjectQueryDTO.targetIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="(classSubjectQueryDTO.optionIds != null and classSubjectQueryDTO.optionIds.size > 0)">
                <if test="(classSubjectQueryDTO.targetIds != null and classSubjectQueryDTO.targetIds.size > 0)">
                    or
                </if>
                    option_id in
                    <foreach collection="classSubjectQueryDTO.optionIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
            </if>
            )
        </if>

    </select>

    <select id="listByOptionIds" resultType="com.hailiang.model.entity.BehaviourRecord">
        select * from  evaluate_behaviour_record where submit_time between #{startTime} and #{endTime}
        and deleted = 0
        and campus_section_id = #{campusSectionId}
        and
        option_id in
        <foreach collection="optionIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="listByTargetIds" resultType="com.hailiang.model.entity.BehaviourRecord">
        select * from  evaluate_behaviour_record where submit_time between #{startTime} and #{endTime}
        and deleted = 0
        and campus_section_id = #{campusSectionId}
        and option_id = ''
        and
        evaluate_behaviour_record.target_id in
        <foreach collection="targetIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="listByInfoId" resultType="com.hailiang.model.entity.BehaviourRecord">
        SELECT tenant_id,
               school_id,
               campus_id,
               campus_section_id,
               campus_section_code,
               grade_id,
               grade_code,
               class_id,
               student_id,
               module_code,
               target_id,
               target_name,
               template_id,
               title_id,
               option_id,
               task_id,
               info_id,
               info_type,
               info_name,
               score_type,
               is_score,
               score,
               score_value,
               send_type,
               data_source,
               submit_time,
               appraisal_type,
               appraisal_id,
               appraisal_name,
               subject_code,
               not_part_count
        FROM evaluate_behaviour_record
        WHERE info_id = #{infoId}
    </select>
    <select id="listRecordsByIds" resultType="com.hailiang.model.entity.BehaviourRecord">
        SELECT tenant_id,
        school_id,
        campus_id,
        campus_section_id,
        campus_section_code,
        grade_id,
        grade_code,
        class_id,
        student_id,
        module_code,
        target_id,
        target_name,
        template_id,
        title_id,
        option_id,
        task_id,
        info_id,
        info_type,
        info_name,
        score_type,
        is_score,
        score,
        score_value,
        send_type,
        data_source,
        submit_time,
        appraisal_type,
        appraisal_id,
        appraisal_name,
        subject_code,
        not_part_count
        from evaluate_behaviour_record where id in
        <foreach collection="behaviourRecordIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by submit_time desc
    </select>

    <!--根据年级/班级查询时间段内的行为记录-->
    <select id="listBehaviourRecordNew" resultType="com.hailiang.model.datastatistics.dto.BehaviourRecordDTO">
        SELECT
        id,
        campus_section_id,
        grade_id,
        class_id,
        student_id,
        module_code,
        target_id,
        score_type,
        score_value,
        data_source,
        submit_time,
        appraisal_type,
        appraisal_id
        FROM evaluate_behaviour_record
        WHERE deleted = 0
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="campusId != null and campusId != ''">
            AND campus_id = #{campusId}
        </if>
        <if test="campusSectionId != null and campusSectionId != ''">
            AND campus_section_id = #{campusSectionId}
        </if>
        <if test="gradeId != null and gradeId != ''">
            AND grade_id = #{gradeId}
        </if>
        <if test="classId != null and classId != ''">
            AND class_id = #{classId}
        </if>
        <if test="appraisalType != null">
            AND appraisal_type = #{appraisalType}
        </if>
        <if test="studentIdList != null and studentIdList.size() > 0">
            AND student_id in
            <foreach collection="studentIdList" item="studentId" open="(" separator="," close=")">
                #{studentId}
            </foreach>
        </if>
        <if test="targetId != null">
            AND target_id = #{targetId}
        </if>
    </select>

    <select id="listBehaviourGroupDoris" resultType="com.hailiang.portrait.vo.StuBehaviorOptionVO">
        select
        t1.target_id targetId,
        t1.target_name targetName,
        t1.info_name infoName,
        t1.template_id templateId,
        t1.option_id optionId,
        t1.info_type infoType,
        t1.score_type scoreType,
        t1.info_id infoId,
        t1.id id,
        t1.data_source dataSource,
        date_format(submit_time, '%Y-%m-%d') submitTime,
        create_time create_time,
        appraisal_id,
        appraisal_name,
        appraisal_type,
        t1.score behaviourScore
        from evaluate_behaviour_record t1
        where  t1.deleted = 0
        and t1.campus_id = #{campusId}
        and t1.class_id = #{classId}
        and t1.student_id = #{studentId}
        and t1.submit_time between #{startTime} and #{endTime}
        and t1.not_part_count = 0
        <if test="ids != null  and ids.size > 0">
            AND t1.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="unNeedBehaviourRecordIds != null  and unNeedBehaviourRecordIds.size > 0">
            AND t1.id not in
            <foreach collection="unNeedBehaviourRecordIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="dataSourceList != null and dataSourceList.size() > 0">
            AND (
            <foreach collection="dataSourceList" item="datasource" index="index" separator="OR">
                (t1.data_source = #{datasource}
                <if test="datasource == 6">
                    AND t1.info_type = 4
                </if>
                <if test="datasource == 4">
                    AND t1.info_type = 4
                </if>
                )
            </foreach>
            )
        </if>
    </select>

    <select id="getStudentEvaNumGroup"
    resultType="com.hailiang.model.response.report.ReportEvaStudentNumResponse">
        SELECT
        target_id AS targetId,
        COUNT(DISTINCT student_id) AS studentCount
        FROM evaluate_behaviour_record
        WHERE deleted = 0 and target_id IN
        <foreach collection="targetIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND student_id IN
        <foreach collection="studentIds" item="stduentId" open="(" separator="," close=")">
            #{stduentId}
        </foreach>
        AND submit_time &gt;= #{startTime}
        AND submit_time &lt;= #{endTime}
        GROUP BY target_id

    </select>

    <select id="listSubmitEvaStudentIds" resultType="java.lang.Long">
        SELECT
        DISTINCT ebr.student_id
        FROM evaluate_behaviour_record ebr Inner JOIN evaluate_target et ON  et.id = ebr.target_id
        WHERE ebr.deleted = 0
        and student_id IN
        <foreach collection="studentIds" item="stduentId" open="(" separator="," close=")">
            #{stduentId}
        </foreach>
        AND submit_time &gt;= #{startTime}
        AND submit_time &lt;= #{endTime}
        AND target_id  = #{targetId}
    </select>

    <select id="listTargetInfo"
      resultType="com.hailiang.model.response.report.ReportModuleTargetInfoResponse">
        SELECT
        etg.module_code,
        et.group_id AS groupId,
        et.id AS targetId,
        et.target_name AS targetName
        FROM evaluate_target et left join evaluate_target_group etg on etg.id = et.group_id
        WHERE
        et.campus_id = #{campusId}
        and et.deleted = 0
        and et.data_source != 3
        and etg.deleted = 0
        and et.target_status = 1
        and (module_code IN
        <foreach collection="moduleCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        OR
        et.group_id IN
        <foreach collection="groupIds" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        OR
        et.id IN
        <foreach collection="targetIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>)
    </select>
</mapper>
