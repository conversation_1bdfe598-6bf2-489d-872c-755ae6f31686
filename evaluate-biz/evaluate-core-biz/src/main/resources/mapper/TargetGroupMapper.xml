<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.TargetGroupMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.TargetGroup">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
        <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
        <result property="moduleCode" column="module_code" jdbcType="TINYINT"/>
        <result property="moduleName" column="module_name" jdbcType="VARCHAR"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="sortIndex" column="sort_index" />
        <result property="dataSource" column="data_source" />
        <result property="tenantId" column="tenant_id" />
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="listByConditionsMap" type="com.hailiang.model.vo.TargetGroupListByConditionsVO">
        <id property="groupId" column="groupId" jdbcType="BIGINT"/>
        <result property="groupName" column="groupName" jdbcType="VARCHAR"/>
        <result property="sortIndex" column="sortIndex" jdbcType="INTEGER"/>
        <result property="groupType" column="groupType" jdbcType="TINYINT"/>
        <collection property="targetList" ofType="com.hailiang.model.vo.TargetGroupListByConditionsVOInnerTarget">
            <id property="id" column="targetId" jdbcType="BIGINT"/>
            <result property="targetName" column="targetName"/>
            <result property="targetStatus" column="targetStatus"/>
            <result property="sortIndex" column="sortIndexTarget"/>
            <result property="iconUrl" column="iconUrl"/>
            <result property="submitType" column="submitType"/>
            <result property="submitDate" column="submitDate"/>
            <result property="targetType" column="targetType"/>
            <result property="scoreControlType" column="scoreControlType"/>
            <result property="score" column="score"/>
            <result property="scoreValue" column="scoreValue"/>
            <result property="dataSource" column="dataSource"/>
            <result property="deleted" column="targetDeleted"/>
            <result property="schoolId" column="school_id"/>
            <result property="campusId" column="campus_id"/>
            <result property="tenantId" column="tenant_id"/>
            <collection property="submitUserNameList"
                        ofType="com.hailiang.model.vo.TargetGroupListByConditionsVOInnerTargetUser">
                <result property="submitUserName" column="submitUserName"/>
                <result property="deleted" column="userDeleted"/>
            </collection>
        </collection>
    </resultMap>

    <resultMap id="listAllEvaluateTargetMap" type="com.hailiang.model.vo.ListAllEvaluateTargetVOModule">
        <id property="moduleCode" column="module_code"/>
        <result property="moduleName" column="module_name"/>
        <collection property="targetGroupList" ofType="com.hailiang.model.vo.ListAllEvaluateTargetVOInnerTargetGroup">
            <id property="groupId" column="groupId"/>
            <result property="groupName" column="group_name"/>
            <collection property="targetList" ofType="com.hailiang.model.vo.ListAllEvaluateTargetVOInnerTarget">
                <id property="targetId" column="targetId"/>
                <result property="targetName" column="target_name"/>
                <result property="iconUrl" column="icon_url"/>
            </collection>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        school_id,
        campus_id,
        module_code,
        module_name,
        group_name,
        sort_index,
        data_source,
        tenant_id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted
    </sql>
    <insert id="batchInsert">
        insert into evaluate_target_group (id,school_id,campus_id,module_code,module_name,group_name,
                                           sort_index,data_source,tenant_id,create_by,create_time,update_by,update_time,deleted)
        values
        <foreach collection="targetGroups" item="item" separator=",">
                (#{item.id},#{item.schoolId},#{item.campusId},#{item.moduleCode},#{item.moduleName},
                 #{item.groupName},#{item.sortIndex},#{item.dataSource},#{item.tenantId},#{item.createBy},now(),#{item.updateBy},now(),#{item.deleted})
            </foreach>
    </insert>
    <update id="batchUpdateSortIndex">
        update evaluate_target_group
            SET sort_index = CASE id
                <foreach collection="targetGroupList"  item="item">
                    WHEN #{item.id} THEN #{item.sortIndex}
                </foreach>
            END
        where id in
        <foreach collection="groupIds" open="(" item="item" separator="," close=")">
                #{item}
        </foreach>
    </update>

    <update id="batchDeleteByIds">
        update evaluate_target_group
        set deleted = 1,update_by = #{updator},update_time = now()
        where deleted = 0 and id in
        <foreach collection="groupIds" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="listByConditions" resultMap="listByConditionsMap">
        select t1.id groupId,
        t1.group_name groupName,
        t1.sort_index sortIndex,
        t1.group_type groupType,
        t2.id targetId,
        t2.target_name targetName,
        t2.target_status targetStatus,
        t2.sort_index sortIndexTarget,
        t2.icon_url iconUrl,
        t2.submit_type submitType,
        t2.submit_date submitDate,
        t2.score_control_type scoreControlType,
        t2.score,
        t2.score_value scoreValue,
        t2.data_source dataSource,
        t2.deleted targetDeleted,
        t3.deleted userDeleted,
        t2.school_id,
        t2.campus_id,
        t2.tenant_id,
        t2.target_type targetType,
        t3.submit_user_name submitUserName
        from evaluate_target_group as t1
        left join evaluate_target t2 on t1.id = t2.group_id
        left join evaluate_target_user t3 on t3.target_id = t2.id
        where t1.deleted = 0

        and t1.data_source = 0
        and t1.tenant_id = #{tenantId}
        and t1.school_id = #{schoolId}

        and t1.campus_id = #{campusId}

        and t1.module_code = #{moduleCode}
        order by t1.sort_index asc, t2.sort_index asc;
    </select>

    <select id="listAllEvaluateTarget" resultMap="listAllEvaluateTargetMap">
        select t1.module_code,
        t1.module_name,
        t1.id groupId,
        t1.group_name,
        t2.id targetId,
        t2.target_name,
        t2.icon_url
        from evaluate_target_group t1
        join evaluate_target t2 on t2.group_id = t1.id
        join evaluate_target_user t3 on t3.target_id = t2.id
        where t1.deleted = 0
        and t1.school_id = #{schoolId}
        and t1.campus_id = #{campusId}
        and t1.data_source = 0
        and t2.target_status = 1
        and t2.data_source = 0
        <if test="pictureEvaluateFlag">
            and t2.picture_evaluate_flag = 1
        </if>
        and t2.deleted = 0
        <if test="submitValueList != null and submitValueList.size > 0">
            and t3.submit_value in
            <foreach collection="submitValueList" item="submitValue" open="(" separator="," close=")">
                #{submitValue}
            </foreach>
        </if>
        and t3.submit_type in (1, 2, 3)
        and t3.deleted = 0
        order by t1.module_code asc, t1.sort_index asc, t2.picture_sort_index asc;
    </select>

    <select id="listParentAllEvaluateTarget" resultMap="listAllEvaluateTargetMap">
        select t1.module_code,
        t1.module_name,
        t1.id groupId,
        t1.group_name,
        t2.id targetId,
        t2.target_name,
        t2.icon_url
        from evaluate_target_group t1
        join evaluate_target t2 on t2.group_id = t1.id
        join evaluate_target_user t3 on t3.target_id = t2.id
        where t1.deleted = 0
        and t1.data_source = 0
        and t2.target_status = 1
        and t2.data_source = 0
        and t2.deleted = 0
        <if test="submitValueList != null and submitValueList.size > 0">
            and t3.submit_value in
            <foreach collection="submitValueList" item="submitValue" open="(" separator="," close=")">
                #{submitValue}
            </foreach>
        </if>
        and t3.submit_type in (4, 5, 6, 7, 8, 9, 10)
        and t3.deleted = 0
        order by t1.module_code asc, t1.sort_index asc, t2.sort_index asc;
    </select>

    <select id="getByIdWithOutDeleted" resultType="com.hailiang.model.entity.TargetGroup">
        select *
        from evaluate_target_group
        where id = #{id}
    </select>
    <select id="listWithOutDeleted" resultType="com.hailiang.model.entity.TargetGroup">
        select *
        from evaluate_target_group
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="countWithDelete" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM evaluate_target_group
        WHERE tenant_id = #{tenantId}
        AND school_id = #{schoolId}
        AND campus_id = #{campusId}
        AND data_source = #{code}
        and create_by != "SystemRecommend";
    </select>
    <select id="listTargetByIds" resultType="com.hailiang.model.entity.TargetGroup">
        select *
        from evaluate_target_group
        where id in
        <foreach collection='ids' open='(' close=')' index='index' item='item' separator=','>
            #{item}
        </foreach>
    </select>

    <select id="listTargetAndTargetGroups" resultType="com.hailiang.model.dto.target.TargetAndGroupDTO">
        select etg.id                as groupId,
               etg.group_name        as groupName,
               etg.module_code       as moduleCode,
               et.id                 as targetId,
               et.target_name,
               et.score_control_name as scoreControlName,
               et.score_control_type as scoreControlType,
               et.score              as score
        from evaluate_target_group etg
                 inner join evaluate_target et on etg.id = et.group_id
            ${ew.customSqlSegment}
    </select>

    <select id="listSysTargetAndSysTargetGroups" resultType="com.hailiang.model.dto.target.TargetAndGroupDTO">
        select etg.id                as groupId,
               etg.group_name        as groupName,
               etg.module_code       as moduleCode,
               et.id                 as targetId,
               et.target_name,
               et.score_control_name as scoreControlName,
               et.score_control_type as scoreControlType,
               et.score              as score
        from sys_target_group etg
                 inner join sys_target et on etg.id = et.group_id
            ${ew.customSqlSegment}
    </select>
    <select id="listGroupByConditions" resultType="com.hailiang.model.entity.TargetGroup">
        select * from
            evaluate_target_group
        where deleted = 0
          <if test="condition.tenantId != null">
        and tenant_id = #{condition.tenantId}
          </if>
        <if test="condition.schoolId != null">
              AND school_id = #{condition.schoolId}
        </if>
              AND campus_id = #{condition.campusId}
              AND data_source = #{condition.datasource}
        <if test="condition.moduleCode != null">
            AND module_code = #{condition.moduleCode}
        </if>
        <if test="condition.moduleCodeList != null and condition.moduleCodeList.size > 0">
            AND module_code in
                <foreach collection="condition.moduleCodeList" open="(" item="item" close=")" separator=",">
                    #{item}
                </foreach>
        </if>
        <if test="condition.groupType != null">
            AND group_type = #{condition.groupType}
        </if>
    </select>
    <select id="getMaxSortIndex" resultType="java.lang.Integer">
        select max(sort_index) from evaluate_target_group
        where campus_id = #{campusId} and module_code = #{moduleCode}
    </select>

    <select id="listTargetGroupInfoByTargetIds" resultType="com.hailiang.model.dto.target.TargetGroupInfoResponse">
        select
        t.id as targetId,
        t.target_name,
        tg.module_code,
        tg.module_name,
        tg.id as groupId,
        tg.group_name
        from evaluate_target t
        left join evaluate_target_group tg on tg.id = t.group_id
        where t.id in
        <foreach collection="targetIds" item="targetId" open="(" separator="," close=")">
            #{targetId}
        </foreach>
    </select>

    <select id="listParentAllEvaluateTargetByTargetIds"
      resultMap="listAllEvaluateTargetMap">
        select t1.module_code,
        t1.module_name,
        t1.id groupId,
        t1.group_name,
        t2.id targetId,
        t2.target_name,
        t2.icon_url
        from evaluate_target_group t1
        join evaluate_target t2 on t2.group_id = t1.id
        where t1.deleted = 0
        and t1.data_source = 0
        and t2.target_status = 1
        and t2.data_source = 0
        and t2.deleted = 0
        and t2.id in
        <foreach collection="targetIds" item="targetId" open="(" separator="," close=")">
            #{targetId}
        </foreach>
        order by t1.module_code asc, t1.sort_index asc, t2.sort_index asc;
    </select>

    <select id="listStudentTargetUserInfo" resultType="com.hailiang.model.entity.TargetUserPO">
        select
        *
        from evaluate_target_user etu
        where etu.deleted = 0
        and etu.campus_id = #{campusId}
        and (etu.submit_type in (14,15,16,17,18,19)
        and etu.submit_value in
        <foreach collection="studentOrgIds" item="studentOrgId" open="(" separator="," close=")">
            #{studentOrgId}
        </foreach>)
        or (etu.submit_type = 20 and etu.campus_id = #{campusId} and etu.deleted = 0)
    </select>
</mapper>
