<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.CabinetInfoMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.point.entity.CabinetInfo">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
        <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
        <result property="campusSectionId" column="campus_section_id" jdbcType="VARCHAR"/>
        <result property="campusSectionCode" column="campus_section_code" jdbcType="VARCHAR"/>
        <result property="cabinetCode" column="cabinet_code" jdbcType="VARCHAR"/>
        <result property="cabinetName" column="cabinet_name" jdbcType="VARCHAR"/>
        <result property="factoryId" column="factory_id" jdbcType="VARCHAR"/>
        <result property="cabinetDescription" column="cabinet_description" jdbcType="VARCHAR"/>
        <result property="versionName" column="version_name" jdbcType="VARCHAR"/>
        <result property="enableFlag" column="enable_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,school_id,
        campus_id,campus_section_id,campus_section_code,
        cabinet_code,cabinet_name,factory_id,cabinet_description,version_name,enable_flag,
        create_by,create_time,update_by,
        update_time,deleted
    </sql>
</mapper>
