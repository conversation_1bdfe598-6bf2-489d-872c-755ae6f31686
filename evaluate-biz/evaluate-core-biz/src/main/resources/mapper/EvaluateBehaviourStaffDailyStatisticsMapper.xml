<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.EvaluateBehaviourStaffDailyStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hailiang.portrait.entity.StaffDailyStatisticsPO">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="school_id" property="schoolId" />
        <result column="campus_id" property="campusId" />
        <result column="campus_section_id" property="campusSectionId" />
        <result column="campus_section_code" property="campusSectionCode" />
        <result column="grade_id" property="gradeId" />
        <result column="grade_code" property="gradeCode" />
        <result column="class_id" property="classId" />
        <result column="appraisal_id" property="appraisalId" />
        <result column="module_code" property="moduleCode" />
        <result column="target_id" property="targetId" />
        <result column="plus_total_score" property="plusTotalScore" />
        <result column="minus_total_score" property="minusTotalScore" />
        <result column="total_score" property="totalScore" />
        <result column="statistics_time" property="statisticsTime" />
        <result column="data_source" property="dataSource" />
        <result column="appraisal_count" property="appraisalCount" />
        <result column="school_year" property="schoolYear" />
        <result column="term_name" property="termName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        tenant_id, school_id, campus_id, campus_section_id, campus_section_code,
          grade_id, grade_code, class_id, appraisal_id, module_code, target_id, plus_total_score,
          minus_total_score, total_score, statistics_time, data_source,appraisal_count,school_year,term_name
    </sql>
    <insert id="insertBatch">
        INSERT INTO evaluate_behaviour_staff_daily_statistics (id,tenant_id, school_id, campus_id,campus_section_id,campus_section_code,
        grade_id,grade_code,class_id,appraisal_id, module_code,target_id,plus_total_score,minus_total_score,total_score,statistics_time,data_source,
        appraisal_count,school_year,term_name,create_by,create_time,update_by,update_time,deleted)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.tenantId},#{item.schoolId},#{item.campusId},#{item.campusSectionId},#{item.campusSectionCode},#{item.gradeId}
            ,#{item.gradeCode},#{item.classId},#{item.appraisalId},#{item.moduleCode},#{item.targetId},#{item.plusTotalScore},#{item.minusTotalScore},#{item.totalScore},#{item.statisticsTime},#{item.dataSource},
            #{item.appraisalCount},#{item.schoolYear},#{item.termName},#{item.createBy},now(),#{item.updateBy},now(),#{item.deleted})
        </foreach>
    </insert>

    <select id="listHistoryTeacherIds" resultType="java.lang.String">
        SELECT DISTINCT(appraisal_id)
        FROM evaluate_behaviour_staff_daily_statistics
        WHERE deleted = 0
        <if test="startTime != null">
            AND statistics_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND statistics_time &lt;= #{endTime}
        </if>
        <if test="schoolId != null and schoolId != ''">
            AND school_id = #{schoolId}
        </if>
        <if test="campusId != null and campusId != ''">
            AND campus_id = #{campusId}
        </if>
        <if test="campusSectionId != null and campusSectionId != ''">
            AND campus_section_id = #{campusSectionId}
        </if>
        <if test="schoolYear != null and schoolYear != ''">
            AND school_year = #{schoolYear}
        </if>
        <if test="gradeId != null and gradeId != ''">
            AND grade_id = #{gradeId}
        </if>
        <if test="classId != null and classId != ''">
            AND class_id = #{classId}
        </if>
    </select>

</mapper>
