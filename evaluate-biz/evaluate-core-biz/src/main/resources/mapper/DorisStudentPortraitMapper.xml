<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.doris.DorisStudentPortraitMapper">
    <select id="listStudentPortrait" resultType="com.hailiang.model.vo.BehaviourStudentFileVO">
        WITH ScoreSummary AS (
            SELECT
                student_id studentId,
                ROUND(SUM(score), 2) AS sumScore,
                ROUND(SUM(CASE WHEN score_type = 1 THEN score ELSE 0 END), 2) AS addScore,
                ROUND(SUM(CASE WHEN score_type = 2 THEN score ELSE 0 END), 2) AS subtractScore
            FROM
                evaluate_behaviour_record
            WHERE deleted = 0 AND is_score = 1 AND not_part_count = 0
                <if test="startTime != null">
                    AND submit_time &gt;= #{startTime}
                </if>
                <if test="endTime != null">
                    AND submit_time &lt;= #{endTime}
                </if>
              AND campus_id = #{campusId}
              AND campus_section_id = #{campusSectionId}
            <if test="gradeIdList != null and gradeIdList.size() > 0">
                AND grade_id IN
                <foreach collection="gradeIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="classIdList != null and classIdList.size() > 0">
                AND class_id IN
                <foreach collection="classIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="studentIdList != null and studentIdList.size() > 0">
                AND student_id IN
                <foreach collection="studentIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        <if test="dataSourceList != null and dataSourceList.size() > 0">
            AND (
            <foreach collection="dataSourceList" item="datasource" index="index" separator="OR">
                (data_source = #{datasource}
                <if test="datasource == 6">
                    AND info_type in (1,2,3,4)
                </if>
                <if test="datasource == 4">
                    AND info_type in (1,2,3,4)
                </if>
                )
            </foreach>
            )
        </if>
            <if test="moduleCode != null">
                AND module_code = #{moduleCode}
            </if>
            GROUP BY
                student_id
        ),
             RankedScores AS (
                 SELECT
                    studentId,
                    sumScore,
                    addScore,
                    subtractScore
--                     RANK() OVER (ORDER BY sumScore DESC) AS rank
                 FROM
                     ScoreSummary
             )
        SELECT
        studentId,
        sumScore,
        addScore,
        subtractScore
--         rank
        FROM
            RankedScores;
    </select>
    <select id="listStudentDailyStatistics" resultType="com.hailiang.portrait.entity.StudentDailyStatisticsPO">
        SELECT
            tenant_id,
            school_id,
            campus_id,
            campus_section_id,
            campus_section_code,
            grade_id,
            grade_code,
            class_id,
            student_id,
            SUM(CASE WHEN module_code = 1 AND score_type = 1 THEN score ELSE 0 END) AS plus_moral_score,
            SUM(CASE WHEN module_code = 1 AND score_type = 2 THEN score ELSE 0 END) AS minus_moral_score,
            SUM(CASE WHEN module_code = 2 AND score_type = 1 THEN score ELSE 0 END) AS plus_wisdom_score,
            SUM(CASE WHEN module_code = 2 AND score_type = 2 THEN score ELSE 0 END) AS minus_wisdom_score,
            SUM(CASE WHEN module_code = 3 AND score_type = 1 THEN score ELSE 0 END) AS plus_sport_score,
            SUM(CASE WHEN module_code = 3 AND score_type = 2 THEN score ELSE 0 END) AS minus_sport_score,
            SUM(CASE WHEN module_code = 4 AND score_type = 1 THEN score ELSE 0 END) AS plus_pretty_score,
            SUM(CASE WHEN module_code = 4 AND score_type = 2 THEN score ELSE 0 END) AS minus_pretty_score,
            SUM(CASE WHEN module_code = 5 AND score_type = 1 THEN score ELSE 0 END) AS plus_work_score,
            SUM(CASE WHEN module_code = 5 AND score_type = 2 THEN score ELSE 0 END) AS minus_work_score,
            SUM(CASE WHEN score_type = 1 THEN score ELSE 0 END) AS plus_total_score,
            SUM(CASE WHEN score_type = 2 THEN score ELSE 0 END) AS minus_total_score,
            COALESCE(SUM(score),0) AS total_score,
            DATE(submit_time) AS statistics_time,
            COUNT(*) AS appraisal_count
        FROM
            evaluate_behaviour_record
        WHERE deleted = 0 AND is_score = 1
        AND not_part_count = 0
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="campusId != null and campusId != ''">
            AND campus_id = #{campusId}
        </if>
        <if test="classId != null and classId != ''">
            AND class_id = #{classId}
        </if>
        <if test="needIds != null and needIds.size() > 0">
            AND id IN
            <foreach collection="needIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="dataSourceList != null and dataSourceList.size() > 0">
            AND (
            <foreach collection="dataSourceList" item="datasource" index="index" separator="OR">
                (data_source = #{datasource}
                <if test="datasource == 6">
                    AND info_type in (1,2,3,4)
                </if>
                <if test="datasource == 4">
                    AND info_type in (1,2,3,4)
                </if>
                )
            </foreach>
            )
        </if>
        GROUP BY
            tenant_id,
            school_id,
            campus_id,
            campus_section_id,
            campus_section_code,
            grade_id,
            grade_code,
            class_id,
            student_id,
            DATE(submit_time)
        ORDER BY
            statistics_time desc;
    </select>

    <select id="listStudentPortraitExport" resultType="com.hailiang.model.vo.BehaviourStudentExportVO">
      select
      *
      from
      (
      select
      student_id,
      campus_id,
      sum(case when score >= 0 then score else 0 end) as total_add_score,
      sum(case when score &lt; 0 then score else 0 end) as total_minus_score,
      sum(case when module_code = 1 then score else 0 end) as moral_score,
      sum(case when module_code = 2 then score else 0 end) as intellectual_score,
      sum(case when module_code = 3 then score else 0 end) as physical_score,
      sum(case when module_code = 4 then score else 0 end) as aesthetic_score,
      sum(case when module_code = 5 then score else 0 end) as labor_score,
      sum(case when info_type in (1,2,3,4) then score else 0 end) as school_score,
      sum(case when info_type in (5,6) then score else 0 end) as personal_score
      from evaluate_behaviour_record
      where campus_id=#{campusId} and submit_time &gt;= #{startTime} and submit_time &lt;=
      #{endTime} and deleted = 0 and not_part_count = 0
      <if test="gradeId != null">
        and grade_id = #{gradeId}
      </if>
      <if test="classId != null">
        and class_id = #{classId}
      </if>
      <if test="moduleCode != null">
        and module_code = #{moduleCode}
      </if>
      <if test="includeInternalScoreFlag == null">
        and info_type in (1,2,3,4)
      </if>
      group by student_id,campus_id
      ) ebr
      left join
      (
      SELECT
      campus_id,
      SUM(CASE WHEN initial_score_type = 0 THEN initial_score ELSE 0 END) AS eis_total_score, -- 总分
      SUM(CASE WHEN initial_score_type = 1 THEN initial_score ELSE 0 END) AS eis_moral_score, -- 德育
      SUM(CASE WHEN initial_score_type = 2 THEN initial_score ELSE 0 END) AS eis_intellectual_score,
      -- 智育
      SUM(CASE WHEN initial_score_type = 3 THEN initial_score ELSE 0 END) AS eis_physical_score,
      -- 体育
      SUM(CASE WHEN initial_score_type = 4 THEN initial_score ELSE 0 END) AS eis_aesthetic_score,
      -- 美育
      SUM(CASE WHEN initial_score_type = 5 THEN initial_score ELSE 0 END) AS eis_labor_score -- 劳育
      FROM evaluate_initial_score where school_year = #{schoolYear} and term_name = #{termName}
                                    and deleted = 0
      <if test="moduleCode != null">
        and initial_score_type = #{moduleCode}
      </if>
      group by campus_id
      ) eis on ebr.campus_id = eis.campus_id
      left join
      (
      select
      student_id,
      sum(case when score >= 0 then score else 0 end) as ehbr_total_add_score,
      sum(case when score &lt; 0 then score else 0 end) as ehbr_total_minus_score,
      sum(case when module_code = 1 then score else 0 end) as ehbr_moral_score,
      sum(case when module_code = 2 then score else 0 end) as ehbr_intellectual_score,
      sum(case when module_code = 3 then score else 0 end) as ehbr_physical_score,
      sum(case when module_code = 4 then score else 0 end) as ehbr_aesthetic_score,
      sum(case when module_code = 5 then score else 0 end) as ehbr_labor_score,
      sum(case when data_type in (1,2,3,4) then score else 0 end) as ehbr_school_score,
      sum(case when data_type in (5,6) then score else 0 end) as ehbr_personal_score
      from evaluate_help_behaviour_record
      where campus_id=#{campusId} and submit_time &gt;= #{startTime} and
      submit_time &lt;= #{endTime} and deleted = 0 and not_part_count = 0
      <if test="moduleCode != null">
        and module_code = #{moduleCode}
      </if>
      <if test="includeInternalScoreFlag == null">
        and data_type in (1,2,3,4)
      </if>
      group by student_id
      ) ehbr on ebr.student_id = ehbr.student_id
    </select>

</mapper>
