<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.EvaluateBehaviourImportsRecordDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hailiang.entity.EvaluateBehaviourImportsRecordDetailPO">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="school_id" property="schoolId" />
        <result column="campus_id" property="campusId" />
        <result column="campus_section_id" property="campusSectionId" />
        <result column="campus_section_code" property="campusSectionCode" />
        <result column="grade_id" property="gradeId" />
        <result column="grade_code" property="gradeCode" />
        <result column="class_id" property="classId" />
        <result column="student_id" property="studentId" />
        <result column="student_name" property="studentName" />
        <result column="target_id" property="targetId" />
        <result column="control_id" property="controlId" />
        <result column="control_type" property="controlType" />
        <result column="control_value" property="controlValue" />
        <result column="control_is_score" property="controlIsScore" />
        <result column="option_id" property="optionId" />
        <result column="option_value" property="optionValue" />
        <result column="option_score" property="optionScore" />
        <result column="import_record_id" property="importRecordId" />
        <result column="submit_id" property="submitId" />
        <result column="appraisal_id" property="appraisalId" />
        <result column="appraisal_name" property="appraisalName" />
        <result column="appraisal_type" property="appraisalType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        tenant_id, school_id, campus_id, campus_section_id, campus_section_code, grade_id, grade_code, class_id, class_name, student_id, student_name, target_id, control_id, control_type, control_value, control_is_score, option_id, option_value, option_score, import_record_id, submit_id, appraisal_id, appraisal_name, appraisal_type
    </sql>



</mapper>
