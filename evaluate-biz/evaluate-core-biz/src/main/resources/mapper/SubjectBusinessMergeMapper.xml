<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hailiang.mapper.SubjectBusinessMergeMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.SubjectBusinessMergePO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="school_id" property="schoolId"/>
        <result column="campus_id" property="campusId"/>
        <result column="business_id" property="businessId"/>
        <result column="business_type" property="businessType"/>
        <result column="subject_id" property="subjectId"/>
        <result column="subject_code" property="subjectCode"/>
        <result column="subject_name" property="subjectName"/>
        <result column="weight" property="weight"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <!-- 表字段 -->
    <sql id="baseColumns">
    id, tenant_id, school_id, campus_id, business_id, business_type, subject_id,subject_code,
      subject_name , weight , create_by , create_time , update_by , update_time ,deleted
      </sql>
    <!-- 插入全部字段 -->
    <insert id="insert" parameterType="com.hailiang.model.entity.SubjectBusinessMergePO" keyProperty="id" keyColumn="id"
            useGeneratedKeys="true">
        INSERT INTO subject_business_merge
        <trim prefix="(" suffix=")" suffixOverrides=",">id, tenant_id, school_id, campus_id, business_id, business_type,
            subject_id, subject_code, subject_name, weight, create_by, create_time, update_by, update_time, deleted,
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">#{id}, #{tenantId}, #{schoolId}, #{campusId},
            #{businessId}, #{businessType}, #{subjectId}, #{subjectCode}, #{subjectName}, #{weight}, #{createBy},
            #{createTime}, #{updateBy}, #{updateTime}, #{deleted},
        </trim>
    </insert>
    <insert id="insertBatch">
        INSERT INTO subject_business_merge
        (id, tenant_id, school_id, campus_id, business_id, business_type,
            subject_id, subject_code, subject_name, weight, create_by, create_time, update_by, update_time, deleted)
        values
        <foreach collection="addList" separator="," item="item">
            (#{item.id}, #{item.tenantId}, #{item.schoolId}, #{item.campusId},
            #{item.businessId}, #{item.businessType}, #{item.subjectId}, #{item.subjectCode}, #{item.subjectName}, #{item.weight}, #{item.createBy},
            now(), #{item.updateBy}, now(), 0)
        </foreach>
    </insert>
    <!-- 更新不为NULL的字段 -->
    <update id="updateIgnoreNull" parameterType="com.hailiang.model.entity.SubjectBusinessMergePO">
        UPDATE
        subject_business_merge
        <set>
            <if test="tenantId != null">tenant_id=#{tenantId},</if>
            <if test="schoolId != null">school_id=#{schoolId},</if>
            <if test="campusId != null">campus_id=#{campusId},</if>
            <if test="businessId != null">business_id=#{businessId},</if>
            <if test="businessType != null">business_type=#{businessType},</if>
            <if test="subjectId != null">subject_id=#{subjectId},</if>
            <if test="subjectCode != null">subject_code=#{subjectCode},</if>
            <if test="subjectName != null">subject_name=#{subjectName},</if>
            <if test="weight != null">weight=#{weight},</if>
            <if test="createBy != null">create_by=#{createBy},</if>
            <if test="createTime != null">create_time=#{createTime},</if>
            <if test="updateBy != null">update_by=#{updateBy},</if>
            <if test="updateTime != null">update_time=#{updateTime},</if>
            <if test="deleted != null">deleted=#{deleted},</if>
        </set>
        WHERE id = #{id}
    </update>
    <select id="queryByBusiness" resultMap="BaseResultMap">
        select
            <include refid="baseColumns" />
            from subject_business_merge
        where deleted = 0
        and business_id in
        <foreach collection="businessIdList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and business_type = #{businessType}
    </select>
</mapper>
