<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hailiang.mapper.EvaluateStudentAbilityModelItemConfigMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.EvaluateStudentAbilityModelItemConfigPO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="school_id" property="schoolId"/>
        <result column="campus_id" property="campusId"/>
        <result column="ability_item_id" property="abilityItemId"/>
        <result column="ability_item_type" property="abilityItemType"/>
        <result column="weight" property="weight"/>
        <result column="enabled" property="enabled"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <!-- 表字段 -->
    <sql id="baseColumns">id
    , tenant_id , school_id , campus_id , ability_item_id , ability_item_type  ,
    weight , enabled , create_by , create_time , update_by , update_time , deleted</sql>
    <!-- 插入全部字段 -->
    <insert id="insert" parameterType="com.hailiang.model.entity.EvaluateStudentAbilityModelItemConfigPO" keyProperty="id"
            keyColumn="id" useGeneratedKeys="true">INSERT INTO evaluate_student_ability_model_item_config
        <trim prefix="(" suffix=")" suffixOverrides=",">id
            , tenant_id , school_id , campus_id , ability_item_id , ability_item_type  ,
            weight , enabled , create_by , create_time , update_by , update_time , deleted
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">#{id}, #{tenantId}, #{schoolId}, #{campusId},
            #{abilityItemId}, #{abilityItemType}, #{weight}, #{enabled},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{deleted},
        </trim>
    </insert>
    <insert id="insertBatch">
        INSERT INTO evaluate_student_ability_model_item_config
        (id
        , tenant_id , school_id , campus_id , ability_item_id , ability_item_type ,
        weight , enabled , create_by , create_time , update_by , update_time , deleted
        )
        values
        <foreach collection="addList" item="item" separator=",">
            (#{item.id}, #{item.tenantId}, #{item.schoolId}, #{item.campusId},
            #{item.abilityItemId}, #{item.abilityItemType}, #{item.weight}, #{item.enabled},
            #{item.createBy}, now(), #{item.updateBy}, now(), 0)
        </foreach>
    </insert>
    <!-- 更新不为NULL的字段 -->
    <update id="updateIgnoreNull" parameterType="com.hailiang.model.entity.EvaluateStudentAbilityModelItemConfigPO">
        UPDATE evaluate_student_ability_model_item_config
        <set>
            <if test="tenantId != null">tenant_id=#{tenantId},</if>
            <if test="schoolId != null">school_id=#{schoolId},</if>
            <if test="campusId != null">campus_id=#{campusId},</if>
            <if test="abilityItemId != null">ability_item_id=#{abilityItemId},</if>
            <if test="abilityItemConfigType != null">ability_item_type=#{abilityItemType},</if>
            <if test="weight != null">weight=#{weight},</if>
            <if test="enabled != null">parent_id=#{enabled},</if>
            <if test="createBy != null">create_by=#{createBy},</if>
            <if test="createTime != null">create_time=#{createTime},</if>
            <if test="updateBy != null">update_by=#{updateBy},</if>
            <if test="updateTime != null">update_time=#{updateTime},</if>
            <if test="deleted != null">deleted=#{deleted},</if>
        </set>
        WHERE id = #{id}
    </update>
    <select id="queryItemConfigByItemIdList" resultMap="BaseResultMap">
        select
            <include refid="baseColumns"/>
            from evaluate_student_ability_model_item_config
        where deleted = 0
        and ability_item_id in
            <foreach collection="abilityItemId" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
    </select>
</mapper>
