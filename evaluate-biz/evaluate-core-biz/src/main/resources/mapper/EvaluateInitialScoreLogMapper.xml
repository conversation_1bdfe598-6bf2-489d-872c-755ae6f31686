<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.EvaluateInitialScoreLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.EvaluateInitialScoreLogPO">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="school_id" property="schoolId" />
        <result column="campus_id" property="campusId" />
        <result column="school_year" property="schoolYear" />
        <result column="term_name" property="termName" />
        <result column="before_allocation_score" property="beforeAllocationScore" />
        <result column="allocation_score" property="allocationScore" />
        <result column="score_value_abs" property="scoreValueAbs" />
        <result column="score_type" property="scoreType" />
        <result column="allocation_time" property="allocationTime" />
        <result column="initial_score_type" property="initialScoreType" />
        <result column="allocation_id" property="allocationId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        tenant_id, school_id, campus_id, school_year, term_name, before_allocation_score, allocation_score, score_value_abs, score_type, allocation_time, initial_score_type, allocation_id
    </sql>

</mapper>
