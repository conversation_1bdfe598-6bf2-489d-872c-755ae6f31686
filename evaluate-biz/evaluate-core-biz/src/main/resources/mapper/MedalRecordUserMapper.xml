<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.MedalRecordUserMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.MedalRecordUser">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="medalUserAcquireRecordId" column="medal_user_acquire_record_id" jdbcType="BIGINT"/>
            <result property="parentMobile" column="parent_mobile" jdbcType="VARCHAR"/>
            <result property="readFlag" column="read_flag" jdbcType="TINYINT"/>
            <result property="readTime" column="read_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        message_user_id,medal_user_acquire_record_id,parent_mobile,
        read_flag,read_time,create_by,
        create_time,update_by,update_time,
        deleted
    </sql>
</mapper>
