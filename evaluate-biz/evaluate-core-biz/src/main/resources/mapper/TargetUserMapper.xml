<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.TargetUserMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.TargetUserPO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
            <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
            <result property="targetId" column="target_id" jdbcType="BIGINT"/>
            <result property="submitUserName" column="submit_user_name" />
            <result property="iconUrl" column="icon_url" />
            <result property="submitType" column="submit_type" jdbcType="INTEGER"/>
            <result property="submitValue" column="submit_value" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" />
            <result property="userCount" column="user_count" />
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,school_id,campus_id,
        target_id,submit_user_name,icon_url,submit_type,
        submit_value,tenant_id,user_count,create_by,create_time,
        update_by,update_time,deleted
    </sql>
    <insert id="batchInsert">
        insert into evaluate_target_user(id,school_id,campus_id,
                                         target_id,submit_user_name,icon_url,submit_type,
                                         submit_value,tenant_id,user_count,create_by,create_time,
                                         update_by,update_time,deleted)
        values
            <foreach collection="targetUserPOS" item="item" separator=",">
                (#{item.id},#{item.schoolId},#{item.campusId},#{item.targetId},#{item.submitUserName},
                 #{item.iconUrl},#{item.submitType},#{item.submitValue},
                #{item.tenantId},#{item.userCount},#{item.createBy},now(),#{item.updateBy},now(),#{item.deleted})
            </foreach>
    </insert>
    <update id="batchDeletedByTargetIds">
        update evaluate_target_user
            set deleted = 1,update_by=#{updator},update_time = now()
        where deleted = 0 and id in( select a.id from
        ( select id from evaluate_target_user where target_id in
        <foreach collection="targetIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        ) as a)
    </update>
</mapper>
