<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.DailyNoticePushRuleMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.DailyNoticePushRule">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
            <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
            <result property="pushDay" column="push_day" jdbcType="VARCHAR"/>
            <result property="pushTime" column="push_time" jdbcType="VARCHAR"/>
            <result property="pushWay" column="push_way" jdbcType="TINYINT"/>
            <result property="parentPushFlag" column="parent_push_flag" jdbcType="TINYINT"/>
            <result property="lastPushTime" column="last_push_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,school_id,
        campus_id,push_day,push_time,
        push_way,parent_push_flag,last_push_time,
        create_by,create_time,update_by,
        update_time,deleted
    </sql>


</mapper>
