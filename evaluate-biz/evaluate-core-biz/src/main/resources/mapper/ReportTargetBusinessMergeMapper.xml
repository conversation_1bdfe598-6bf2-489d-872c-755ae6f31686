<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.ReportTargetBusinessMergeMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.model.report.entity.SubjectEvaluationDimTargetBusinessMergePO">
    <!--@mbg.generated-->
    <!--@Table report_target_business_merge-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="school_id" jdbcType="VARCHAR" property="schoolId" />
    <result column="campus_id" jdbcType="VARCHAR" property="campusId" />
    <result column="dim_id" jdbcType="BIGINT" property="dimId"/>
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="dim_name" jdbcType="VARCHAR" property="dimName" />
    <result column="module_code" jdbcType="VARCHAR" property="moduleCode" />
    <result column="target_group_id" jdbcType="BIGINT" property="targetGroupId" />
    <result column="target_id" jdbcType="BIGINT" property="targetId" />
    <result column="option_id" jdbcType="BIGINT" property="optionId" />
    <result column="submit_name" jdbcType="VARCHAR" property="submitName"/>
    <result column="submit_type" jdbcType="TINYINT" property="submitType"/>
    <result column="submit_id" jdbcType="VARCHAR" property="submitId"/>
    <result column="archive_flag" jdbcType="TINYINT" property="archiveFlag" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, school_id, campus_id, dim_id, business_type, business_id, dim_name,
    module_code, target_group_id, target_id, option_id, submit_name, submit_type, submit_id, archive_flag,
    deleted, create_by,update_by, create_time, update_time
  </sql>


  <insert id="saveBatch">
    insert into report_target_business_merge (
    id, tenant_id, school_id, campus_id, dim_id, business_type, business_id, dim_name,
    module_code, target_group_id, target_id, option_id, submit_name, submit_type, submit_id, create_by, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.tenantId,jdbcType=VARCHAR}, #{item.schoolId,jdbcType=VARCHAR},
      #{item.campusId,jdbcType=VARCHAR}, #{item.dimId,jdbcType=BIGINT}, #{item.businessType,jdbcType=INTEGER},
      #{item.businessId,jdbcType=BIGINT},
      #{item.dimName,jdbcType=VARCHAR},
      #{item.moduleCode,jdbcType=VARCHAR}, #{item.targetGroupId,jdbcType=BIGINT},
      #{item.targetId,jdbcType=BIGINT}, #{item.optionId,jdbcType=BIGINT},
      #{item.submitName,jdbcType=VARCHAR},#{item.submitType,jdbcType=INTEGER},
      #{item.submitId,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR},now() )
    </foreach>
  </insert>
</mapper>