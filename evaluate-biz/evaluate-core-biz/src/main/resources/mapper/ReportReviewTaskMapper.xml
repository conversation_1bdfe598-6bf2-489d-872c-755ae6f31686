<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.ReportReviewTaskMapper">

    <select id="pageReportReview" resultType="com.hailiang.model.entity.ReportReviewTask">
        SELECT *
        FROM evaluate_report_review_task errt
        WHERE  errt.need_review = 1
            AND errt.deleted = 0
            AND errt.teacher_staff_id = #{teacherStaffId}
            AND errt.campus_id = #{campusId}
        ORDER BY errt.create_time DESC
    </select>

</mapper>
