<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.EvaluateStudentAbilityModelArchivedMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hailiang.portrait.entity.StudentAbilityModelArchivedPO">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="school_id" property="schoolId"/>
        <result column="campus_id" property="campusId"/>
        <result column="ability_model_name" property="abilityModelName"/>
        <result column="contrast_range" property="contrastRange"/>
        <result column="school_year" property="schoolYear"/>
        <result column="config" property="config"/>
        <result column="term_name" property="termName"/>
        <result column="rule_module_list" property="ruleModuleList"/>
        <result column="exam_source" property="examSource"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        tenant_id, school_id, campus_id, ability_model_name, contrast_range, school_year, config, exam_source
    </sql>

    <select id="getByYear" resultMap="BaseResultMap">
        select *
        from evaluate_student_ability_model_archived
        where campus_id = #{campusId}
          and school_year = #{schoolYear}
        and deleted = 0 order by create_time desc limit 1;
    </select>
</mapper>
