<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.ReportDimConfigMapper">
  <resultMap id="BaseResultMap" type="com.hailiang.model.report.entity.SubjectEvaluationDimConfigPO">
    <!--@mbg.generated-->
    <!--@Table report_dim_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="school_id" jdbcType="VARCHAR" property="schoolId" />
    <result column="campus_id" jdbcType="VARCHAR" property="campusId" />
    <result column="school_year" jdbcType="VARCHAR" property="schoolYear" />
    <result column="term_name" jdbcType="VARCHAR" property="termName" />
    <result column="campus_section_id" jdbcType="VARCHAR" property="campusSectionId" />
    <result column="campus_section_code" jdbcType="VARCHAR" property="campusSectionCode" />
    <result column="archive_flag" jdbcType="TINYINT" property="archiveFlag" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, school_id, campus_id, school_year, term_name, campus_section_id, campus_section_code, 
    archive_flag, deleted, create_by, update_by, create_time, update_time
  </sql>

</mapper>