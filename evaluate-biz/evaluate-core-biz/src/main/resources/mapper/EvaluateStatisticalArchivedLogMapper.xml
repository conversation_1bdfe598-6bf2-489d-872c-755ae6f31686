<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.EvaluateStatisticalArchivedLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hailiang.portrait.entity.EvaluateStatisticalArchivedLogPO">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="biz_type" property="bizType" />
        <result column="daily" property="daily" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        biz_type, daily, status, remark
    </sql>

    <!--通过主键删除 如果是逻辑删除，触发重试或重新计算产生多条删除记录会导致 un_biz_daily_status_IDX 唯一键冲突 -->
    <delete id="deleteById">
        delete from evaluate_statistical_archived_log where id = #{id}
    </delete>

</mapper>
