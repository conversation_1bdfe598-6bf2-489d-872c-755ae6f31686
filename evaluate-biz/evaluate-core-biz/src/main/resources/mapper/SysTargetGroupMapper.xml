<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.SysTargetGroupMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.TargetGroup">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
        <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
        <result property="moduleCode" column="module_code" jdbcType="TINYINT"/>
        <result property="moduleName" column="module_name" jdbcType="VARCHAR"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="sortIndex" column="sort_index" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="listByConditionsMap" type="com.hailiang.model.vo.TargetGroupListByConditionsVO">
        <id property="groupId" column="groupId" jdbcType="BIGINT"/>
        <result property="groupName" column="groupName" jdbcType="VARCHAR"/>
        <result property="sortIndex" column="sortIndex" jdbcType="INTEGER"/>
        <collection property="targetList" ofType="com.hailiang.model.vo.TargetGroupListByConditionsVOInnerTarget">
            <id property="id" column="targetId" jdbcType="BIGINT"/>
            <result property="targetName" column="targetName"/>
            <result property="targetStatus" column="targetStatus"/>
            <result property="sortIndex" column="sortIndexTarget"/>
            <result property="iconUrl" column="iconUrl"/>
            <result property="submitType" column="submitType"/>
            <result property="submitDate" column="submitDate"/>
            <result property="scoreControlType" column="scoreControlType"/>
            <result property="score" column="score"/>
            <result property="scoreValue" column="scoreValue"/>
            <result property="dataSource" column="dataSource"/>
            <result property="deleted" column="targetDeleted"/>
            <result property="schoolId" column="school_id"/>
            <result property="campusId" column="campus_id"/>
            <result property="tenantId" column="tenant_id"/>
            <collection property="submitUserNameList"
                        ofType="com.hailiang.model.vo.TargetGroupListByConditionsVOInnerTargetUser">
                <result property="submitUserName" column="submitUserName"/>
                <result property="deleted" column="userDeleted"/>
            </collection>
        </collection>
    </resultMap>

    <resultMap id="listAllEvaluateTargetMap" type="com.hailiang.model.vo.ListAllEvaluateTargetVOModule">
        <id property="moduleCode" column="module_code"/>
        <result property="moduleName" column="module_name"/>
        <collection property="targetGroupList" ofType="com.hailiang.model.vo.ListAllEvaluateTargetVOInnerTargetGroup">
            <id property="groupId" column="groupId"/>
            <result property="groupName" column="group_name"/>
            <collection property="targetList" ofType="com.hailiang.model.vo.ListAllEvaluateTargetVOInnerTarget">
                <id property="targetId" column="targetId"/>
                <result property="targetName" column="target_name"/>
                <result property="iconUrl" column="icon_url"/>
            </collection>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id,module_code,module_name,
        group_name,sort_index,data_source,
        create_by,create_time,update_by,
        update_time,deleted
    </sql>
    <select id="listByConditions" resultMap="listByConditionsMap">
        select t1.id                 groupId,
               t1.group_name         groupName,
               t1.sort_index         sortIndex,
               t2.id                 targetId,
               t2.target_name        targetName,
               t2.target_status      targetStatus,
               t2.sort_index         sortIndexTarget,
               t2.icon_url           iconUrl,
               t2.submit_type        submitType,
               t2.submit_date        submitDate,
               t2.score_control_type scoreControlType,
               t2.score,
               t2.score_value        scoreValue,
               t2.data_source        dataSource,
               t2.deleted            targetDeleted
        from sys_target_group as t1
                 left join sys_target t2 on t1.id = t2.group_id
        where t1.deleted = 0
          and t1.data_source = 0
          and t1.module_code = #{moduleCode}
          and t1.group_type = #{groupType}
          and t2.target_type = #{targetType}
        order by t1.sort_index asc, t2.sort_index asc;
    </select>
    <select id="listAllEvaluateTarget" resultMap="listAllEvaluateTargetMap">
        select t1.module_code,
        t1.module_name,
        t1.id groupId,
        t1.group_name,
        t2.id targetId,
        t2.target_name,
        t2.icon_url
        from sys_target_group t1
        join sys_target t2 on t2.group_id = t1.id

        where t1.deleted = 0

        and t1.data_source = 0
        and t2.target_status = 1
        and t2.data_source = 0
        and t2.deleted = 0

        and t3.deleted = 0
        order by t1.module_code asc, t1.sort_index asc, t2.sort_index asc;

    </select>
    <select id="listParentAllEvaluateTarget" resultMap="listAllEvaluateTargetMap">
        select t1.module_code,
        t1.module_name,
        t1.id groupId,
        t1.group_name,
        t2.id targetId,
        t2.target_name,
        t2.icon_url
        from sys_target_group t1
        join sys_target t2 on t2.group_id = t1.id

        where t1.deleted = 0
        and t1.data_source = 0
        and t2.target_status = 1
        and t2.data_source = 0
        and t2.deleted = 0
        order by t1.module_code asc, t1.sort_index asc, t2.sort_index asc;
    </select>
</mapper>
