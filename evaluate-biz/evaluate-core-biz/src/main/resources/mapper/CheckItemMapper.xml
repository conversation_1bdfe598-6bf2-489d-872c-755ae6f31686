<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.CheckItemMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.CheckItemPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="campus_id" jdbcType="VARCHAR" property="campusId"/>
        <result column="group_id" jdbcType="VARCHAR" property="groupId"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="check_obj_type" jdbcType="VARCHAR" property="checkObjType"/>
        <result column="opt_student" jdbcType="TINYINT" property="optStudent"/>
        <result column="dim_id" jdbcType="VARCHAR" property="dimId"/>
        <result column="template_id" jdbcType="VARCHAR" property="templateId"/>
        <result column="icon_url" jdbcType="VARCHAR" property="iconUrl"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="score_type" jdbcType="TINYINT" property="scoreType"/>
        <result column="score" jdbcType="DECIMAL" property="score"/>
        <result column="score_value" jdbcType="DECIMAL" property="scoreValue"/>
        <result column="sort_index" jdbcType="BIGINT" property="sortIndex"/>
        <result column="yard_status" jdbcType="TINYINT" property="yardStatus"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cext" jdbcType="VARCHAR" property="cext"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id, school_id, campus_id, group_id, source_type, check_obj_type, opt_student, dim_id, template_id,
        icon_url,
        `name`, `status`, score_type, score, score_value, sort_index, deleted, create_by, update_by, create_time,
        update_time, cext
    </sql>

    <select id="isExists" resultType="java.lang.Boolean">
        select exists(select * from check_item
        where
        group_id=#{groupId} and deleted=0)
    </select>

    <select id="queryItems" resultMap="BaseResultMap">
        select distinct
        i.id,
        i.icon_url,
        i.`name`,
        i.sort_index,
        i.yard_status,
        i.group_id
        from check_item i
        left join
        check_evaluator e
        on i.id = e.check_item_id
        where
        i.group_id in
        <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
            #{groupId}
        </foreach>
        <if test="checkObjType != null and checkObjType != ''">
            and i.check_obj_type = #{checkObjType}
        </if>
        <if test="optStudent != null">
            and i.opt_student = #{optStudent}
        </if>
        <if test="yardStatus != null">
            and i.yard_status = #{yardStatus}
        </if>
        and i.deleted = 0
        and i.`status` = 1
        and e.business_code in
        <foreach collection="businessCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and e.deleted = 0
        order by i.sort_index, i.id
    </select>

    <update id="updateDiy">
        update check_item
        set group_id       =#{item.groupId,jdbcType=VARCHAR},
            check_obj_type = #{item.checkObjType,jdbcType=VARCHAR},
            opt_student    =#{item.optStudent,jdbcType=BOOLEAN},
            dim_id         =#{item.dimId,jdbcType=VARCHAR},
            template_id=#{item.templateId,jdbcType=VARCHAR},
            icon_url=#{item.iconUrl,jdbcType=VARCHAR},
            `name`=#{item.name,jdbcType=VARCHAR},
            score_type=#{item.scoreType,jdbcType=INTEGER},
            score=#{item.score,jdbcType=DECIMAL},
            score_value    =#{item.scoreValue,jdbcType=DECIMAL},
            yard_status    =#{item.yardStatus,jdbcType=BOOLEAN},
            sort_index=#{item.sortIndex,jdbcType=BIGINT},
            update_by=#{item.updateBy,jdbcType=VARCHAR},
            update_time=now(),
            cext=#{item.cext,jdbcType=VARCHAR}
        where id = #{item.id,jdbcType=BIGINT}
    </update>
</mapper>