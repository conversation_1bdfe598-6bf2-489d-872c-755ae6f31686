<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.TargetArchivedMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.TargetArchivedPO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="targetId" column="target_id" jdbcType="BIGINT"/>
        <result property="iconUrl" column="icon_url" jdbcType="VARCHAR"/>
        <result property="targetName" column="target_name" jdbcType="VARCHAR"/>
        <result property="targetStatus" column="target_status" jdbcType="TINYINT"/>
        <result property="groupId" column="group_id" jdbcType="BIGINT"/>
        <result property="templateId" column="template_id" jdbcType="VARCHAR"/>
        <result property="submitType" column="submit_type" jdbcType="TINYINT"/>
        <result property="submitDate" column="submit_date" jdbcType="VARCHAR"/>
        <result property="remindTime" column="remind_time" jdbcType="VARCHAR"/>
        <result property="holidayNoticeFlag" column="holiday_notice_flag" jdbcType="TINYINT"/>
        <result property="mustSubmitFlag" column="must_submit_flag" jdbcType="TINYINT"/>
        <result property="urgeTime" column="urge_time" jdbcType="INTEGER"/>
        <result property="sendParentFlag" column="send_parent_flag" jdbcType="TINYINT"/>
        <result property="sortIndex" column="sort_index" jdbcType="INTEGER"/>
        <result property="schoolYear" column="school_year" jdbcType="VARCHAR"/>
        <result property="termName" column="term_name" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, target_id,icon_url,target_name,
        target_status,group_id,template_id,
        submit_type,submit_date,remind_time,
        holiday_notice_flag,must_submit_flag,urge_time,
        send_parent_flag,sort_index,create_by,school_year,term_name,
        create_time,update_by,update_time,
        deleted
    </sql>

    <select id="listEnableTarget" resultType="com.hailiang.model.datastatistics.dto.TargetDTO">
        SELECT
            eta.*,
            etga.group_name,
            etga.module_code
        FROM
            evaluate_target_archived eta
                INNER JOIN evaluate_target_group_archived etga ON etga.group_id = eta.group_id
        WHERE
            eta.deleted = 0
          AND etga.deleted = 0
          AND eta.target_status = 1
          AND eta.school_id = #{schoolId}
          AND eta.campus_id = #{campusId}
        <if test="schoolYear != null and schoolYear != ''">
            AND eta.school_year = #{schoolYear}
        </if>
    </select>

</mapper>
