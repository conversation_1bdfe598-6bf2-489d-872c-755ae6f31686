<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.BehaviourRecordMapper">



    <!--<select id="pageTotalRecordByCondition"
            resultType="com.hailiang.model.datastatistics.dto.BehaviourRecordNewDTO">
        SELECT
        e1.student_id,
        e1.submit_time,
        e1.appraisal_id,
        e1.appraisal_name,
        e1.target_id,
        e1.option_id,
        e1.score,
        e1.subject_code,
        e1.score_value,
        e1.is_score,
        e1.deleted,
        e1.class_id
        FROM evaluate_behaviour_record e1
        inner JOIN (SELECT id
        FROM evaluate_behaviour_record br
        ${ew.customSqlSegment}
            limit #{offset},#{pageSize}
        ) e2 using (id)
    </select>-->

    <select id="listDateBehaviour" resultType="com.hailiang.model.entity.BehaviourRecord">
        select
        t1.target_id targetId,
        t1.target_name targetName,
        t1.info_name infoName,
        t1.module_code moduleCode,
        t1.template_id templateId,
        t1.option_id optionId,
        t1.info_name infoName,
        t1.info_id infoId,
        t1.info_type,
        t1.id,
        t1.score,
        date_format(t1.submit_time, '%Y-%m-%d') submitTime,
        t1.data_source dataSource,
        t1.create_time,
        t1.appraisal_name,
        t1.appraisal_type,
        t1.score_type scoreType,
        t1.not_part_count
        from evaluate_behaviour_record t1
        where t1.campus_id = #{campusId}
        and t1.class_id = #{classId}
        and t1.student_id = #{studentId}
        and t1.deleted = 0
        and t1.submit_time  <![CDATA[>=]]> #{startTime} and t1.submit_time  <![CDATA[<]]> #{endTime}
        ORDER BY t1.submit_time desc
    </select>

    <!--<select id="getSumScoreByCondition" resultType="java.math.BigDecimal">
        select sum(score) from evaluate_behaviour_record
        where campus_id = #{campusId}
        and class_id = #{classId}
        and student_id = #{studentId}
        and deleted = 0
        and is_score = 1
        <if test="type == 1">
            and score > 0
        </if>
        <if test="type == 2">
            and score &lt; 0
        </if>
        and submit_time between #{startTime} and #{endTime}
    </select>-->

   <!-- <select id="listSendFlag" resultType="com.hailiang.model.entity.BehaviourRecord">
        SELECT
        *
        FROM
        evaluate_behaviour_record ebr
        INNER JOIN evaluate_target et ON et.id = ebr.target_id
        AND et.deleted = 0
        AND ebr.deleted = 0
        AND ebr.send_type = 2
        AND et.send_parent_flag = 1
        WHERE ebr.submit_time BETWEEN #{reportStartTime} AND #{reportEndTime}
        <if test="studentIds != null">
            AND ebr.student_id IN
            <foreach collection="studentIds" item="studentId" open="(" separator="," close=")">
                #{studentId}
            </foreach>
        </if>
        ORDER BY ebr.submit_time DESC
    </select>-->

    <update id="updateScoreInfoIsNull">
        UPDATE evaluate_behaviour_record
        SET score_type = NULL,
        score = NULL,
        score_value = NULL
        WHERE id = #{behaviorRecordId}
    </update>

    <!--根据年级/班级查询时间段内的行为记录-->
    <select id="listBehaviourRecordNew" resultType="com.hailiang.model.datastatistics.dto.BehaviourRecordDTO">
        SELECT
        id,
        campus_section_id,
        grade_id,
        class_id,
        student_id,
        module_code,
        target_id,
        score_type,
        score_value,
        data_source,
        submit_time,
        appraisal_type,
        appraisal_id
        FROM evaluate_behaviour_record
        WHERE deleted = 0
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="campusId != null and campusId != ''">
            AND campus_id = #{campusId}
        </if>
        <if test="campusSectionId != null and campusSectionId != ''">
            AND campus_section_id = #{campusSectionId}
        </if>
        <if test="gradeId != null and gradeId != ''">
            AND grade_id = #{gradeId}
        </if>
        <if test="classId != null and classId != ''">
            AND class_id = #{classId}
        </if>
        <if test="appraisalType != null">
            AND appraisal_type = #{appraisalType}
        </if>
    </select>

    <select id="getRuleBehaviour" resultType="com.hailiang.model.dto.activity.rule.match.RuleBehaviourInfoDTO">
        SELECT
        ebr.id AS behaviourRecordId,
        ebr.module_code,
        ebr.tenant_id,
        ebr.school_id,
        ebr.campus_id,
        ebr.campus_section_id,
        ebr.campus_section_code,
        ebr.grade_id,
        ebr.class_id,
        ebr.student_id,
        ebr.grade_code,
        et.group_id,
        ebr.target_id,
        ebr.option_id,
        ebr.create_by,
        ebr.update_by,
        ebr.deleted,
        et.target_name,
        ebr.data_source,
        ebr.info_type,
        ebr.info_name,
        ebr.score
        FROM
        evaluate_behaviour_record ebr
        INNER JOIN evaluate_target et ON et.id = ebr.target_id
        AND et.deleted = 0
        INNER JOIN evaluate_target_group etg ON etg.id = et.group_id
        AND etg.deleted = 0
        WHERE
        ebr.id = #{behaviourRecordId}
    </select>


    <select id="encapsulationLevel" resultType="com.hailiang.model.dto.activity.rule.save.TargetLevelDTO">
        SELECT
        etg.module_code,
        etg.id as groupId,
        etg.group_name,
        et.id as targetId,
        et.target_name
        FROM
        evaluate_target et
        INNER JOIN evaluate_target_group etg ON etg.id = et.group_id
        WHERE
        et.id = #{targetId}
    </select>

    <select id="getTargetGroup" resultType="com.hailiang.model.entity.TargetGroup">
        SELECT * FROM evaluate_target_group WHERE id = #{groupId}
    </select>

    <select id="listTeacherId" resultType="java.lang.String">
        SELECT DISTINCT(appraisal_id)
        FROM evaluate_behaviour_record
        WHERE deleted = 0
        and appraisal_type = 1
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="schoolId != null and schoolId != ''">
            AND school_id = #{schoolId}
        </if>
        <if test="campusId != null and campusId != ''">
            AND campus_id = #{campusId}
        </if>
        <if test="campusSectionId != null and campusSectionId != ''">
            AND campus_section_id = #{campusSectionId}
        </if>
        <if test="gradeId != null and gradeId != ''">
            AND grade_id = #{gradeId}
        </if>
        <if test="classId != null and classId != ''">
            AND class_id = #{classId}
        </if>
    </select>

    <!--<update id="batchUpdateSubject">
        <foreach collection="list" item="item" separator=";">
            UPDATE evaluate_behaviour_record SET subject_code = #{item.subjectCode} WHERE id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>-->

    <select id="listStuStatics" resultType="com.hailiang.portrait.vo.BehaviourRecordStaticsVO">

        select IFNULL(sum(score), 0), score_type, tenant_id, school_id, campus_id, campus_section_id, grade_id, class_id, student_id, data_source, module_code
        from evaluate_behaviour_record
        where deleted = 0
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="studentId != null">
            AND student_id = #{studentId}
        </if>
            and is_score = 1

        group by score_type, tenant_id, school_id, campus_id, campus_section_id, grade_id, class_id, student_id, data_source, module_code;
    </select>

    <!--<select id="classRank" resultType="com.hailiang.portrait.vo.StuBehaviorOptionVO">
        select student_id, sum(score) from evaluate_behaviour_record
        where campus_id = #{campusId}
        and class_id = #{classId}
        and student_id = #{studentId}
        and deleted = 0
        and is_score = 1
        and submit_time between #{startTime} and #{endTime}
        group by student_id
        orderby 2 desc
    </select>-->

    <sql id="getBehaviourGroupSQL">
        select
        t1.target_id                         targetId,
        t1.target_name                       targetName,
        t1.info_name                         infoName,
        t1.template_id templateId,
        t1.option_id optionId,
        t1.info_type infoType,
        t1.score_type scoreType,
        t1.info_id infoId,
        t1.id,
        t1.data_source dataSource,
        date_format(submit_time, '%Y-%m-%d') submitTime,
        create_time,
        t1.score behaviourScore
        from evaluate_behaviour_record t1
        where  t1.deleted = 0
        and t1.campus_id = #{condition.campusId}
        and t1.class_id = #{condition.classId}
        and t1.student_id = #{condition.studentId}
        and t1.submit_time between #{condition.startTime} and #{condition.endTime}
        <if test="condition.ids != null  and condition.ids.size > 0">
            AND t1.id in
            <foreach collection="condition.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="condition.unNeedBehaviourRecordIds != null  and condition.unNeedBehaviourRecordIds.size > 0">
            AND t1.id not in
            <foreach collection="condition.unNeedBehaviourRecordIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </sql>

    <!--<select id="getBehaviourGroup" resultType="com.hailiang.portrait.vo.StuBehaviorOptionVO">
            <include refid="getBehaviourGroupSQL" />
&lt;!&ndash;        group by  t1.info_id, submitTime, t1.option_id &ndash;&gt;
    </select>-->

    <select id="getBehaviourGroupPage" resultType="com.hailiang.portrait.vo.StuBehaviorOptionVO">
        <include refid="getBehaviourGroupSQL" />
        order by submit_time desc
    </select>
    <select id="listBehaviourGroupDorisOld" resultType="com.hailiang.portrait.vo.StuBehaviorOptionVO">
        select
        MIN(t1.target_id) targetId,
        MIN(t1.target_name) targetName,
        MIN(t1.info_name) infoName,
        MIN(t1.template_id) templateId,
        t1.option_id optionId,
        MIN(t1.info_type) infoType,
        MIN(t1.score_type) scoreType,
        t1.info_id infoId,
        MIN(t1.id) id,
        MIN(t1.data_source) dataSource,
        date_format( submit_time, '%Y-%m-%d' ) submitTime,
        MIN(create_time) create_time,
        MIN(t1.score) behaviourScore
        from evaluate_behaviour_record t1
        where  t1.deleted = 0
        and t1.campus_id = #{campusId}
        and t1.class_id = #{classId}
        and t1.student_id = #{studentId}
        and t1.submit_time between #{startTime} and #{endTime}
        <if test="ids != null  and ids.size > 0">
            AND t1.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="unNeedBehaviourRecordIds != null  and unNeedBehaviourRecordIds.size > 0">
            AND t1.id not in
            <foreach collection="unNeedBehaviourRecordIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by  t1.info_id, submitTime, t1.option_id
    </select>
    <!--<select id="listBehaviourGroupDoris" resultType="com.hailiang.portrait.vo.StuBehaviorOptionVO">
        select
        t1.target_id targetId,
        t1.target_name targetName,
        t1.info_name infoName,
        t1.template_id templateId,
        t1.option_id optionId,
        t1.info_type infoType,
        t1.score_type scoreType,
        t1.info_id infoId,
        t1.id id,
        t1.data_source dataSource,
        date_format(submit_time, '%Y-%m-%d') submitTime,
        create_time create_time,
        appraisal_id,
        appraisal_name,
        appraisal_type,
        t1.score behaviourScore
        from evaluate_behaviour_record t1
        where  t1.deleted = 0
        and t1.campus_id = #{campusId}
        and t1.class_id = #{classId}
        and t1.student_id = #{studentId}
        and t1.submit_time between #{startTime} and #{endTime}
        and t1.not_part_count = 0
        <if test="ids != null  and ids.size > 0">
            AND t1.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="unNeedBehaviourRecordIds != null  and unNeedBehaviourRecordIds.size > 0">
            AND t1.id not in
            <foreach collection="unNeedBehaviourRecordIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="dataSourceList != null and dataSourceList.size() > 0">
            AND (
            <foreach collection="dataSourceList" item="datasource" index="index" separator="OR">
                (t1.data_source = #{datasource}
                <if test="datasource == 6">
                    AND t1.info_type = 4
                </if>
                <if test="datasource == 4">
                    AND t1.info_type = 4
                </if>
                )
            </foreach>
            )
        </if>
    </select>-->
    <!--<update id="updateTargetName">
        update evaluate_behaviour_record r
        set r.target_name = (select t.target_name from evaluate_target t where r.target_id = t.id)
        where r.submit_time between #{startTime} and #{endTime}
    </update>-->

    <select id="listByTimeAndStudentId" resultType="com.hailiang.portrait.vo.StuBehaviourCountVO">
        select date(submit_time) as submit_time,student_id,count(*) as be_appraisal_count
        from evaluate_behaviour_record
        where
        submit_time between #{startTime} and #{endTime}
        and deleted = 0
        group by date(submit_time),student_id
    </select>

    <!--<select id="groupStaffStatics" resultType="com.hailiang.portrait.vo.BehaviourRecordStaticsVO">
        select tenant_id,
               school_id,
               campus_id,
               campus_section_id,
               grade_id,
               class_id,
               appraisal_id,
               target_id,
               module_code,
               data_source,
               score_type,
               sum(nullif(score, 0))
        from evaluate_behaviour_record
        where  deleted = 0
          and is_score = 1
          and submit_time  between #{startTime} and #{endTime}
        <if test="staffId != null">
            AND appraisal_id = #{staffId}
        </if>
        group by tenant_id, school_id, campus_id, campus_section_id, grade_id, class_id, appraisal_id, target_id, module_code,
                 data_source, score_type;
    </select>-->

   <!-- <select id="listBehaviourRecordNewV2" resultType="com.hailiang.model.datastatistics.dto.BehaviourRecordDTO">
        SELECT
        id,
        grade_id,
        class_id,
        student_id,
        module_code,
        target_id,
        score_type,
        score_value,
        data_source,
        submit_time,
        appraisal_type,
        appraisal_id
        FROM evaluate_behaviour_record
        WHERE deleted = 0
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="campusId != null and campusId != ''">
            AND campus_id = #{campusId}
        </if>
        <if test="campusSectionId != null and campusSectionId != ''">
            AND campus_section_id = #{campusSectionId}
        </if>
        <if test="gradeId != null and gradeId != ''">
            AND grade_id = #{gradeId}
        </if>
        <if test="classIds != null">
            AND class_id in
            <foreach collection="classIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="appraisalType != null">
            AND appraisal_type = #{appraisalType}
        </if>
    </select>-->

    <!--<select id="groupStudentStatics" resultType="com.hailiang.portrait.vo.BehaviourRecordStaticsVO">
        select tenant_id,
        school_id,
        campus_id,
        campus_section_id,
        grade_id,
        class_id,
        student_id,
        target_id,
        module_code,
        data_source,
        score_type,
        sum(nullif(score, 0))
        from evaluate_behaviour_record
        where  deleted = 0
        and is_score = 1
        and submit_time  between #{startTime} and #{endTime}
        <if test="staffId != null">
            AND appraisal_id = #{staffId}
        </if>
        group by tenant_id, school_id, campus_id, campus_section_id, grade_id, class_id, student_id, target_id, module_code,
        data_source, score_type;
    </select>-->
    <select id="listDistinctClassId" resultType="java.lang.String">
        select distinct class_id from evaluate_behaviour_record
            where submit_time  between #{conditionQO.startTime} and #{conditionQO.endTime}
        <if test="conditionQO.unusedDatasource != null and conditionQO.unusedDatasource.size > 0">
                and data_source not in
            <foreach collection="conditionQO.unusedDatasource" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <resultMap id="targetstatisticsdtomap" type="com.hailiang.model.datastatistics.dto.BehaviourStudentClassifyStatisticsDTO">
        <result property="tenantId" column="tenant_id" />
        <result property="schoolId" column="school_id" />
        <result property="campusId" column="campus_id" />
        <result property="campusSectionId" column="campus_section_id" />
        <result property="campusSectionCode" column="campus_section_code" />
        <result property="gradeId" column="grade_id" />
        <result property="gradeCode" column="grade_code" />
        <result property="classId" column="class_id" />
        <result property="studentId" column="student_id" />
        <result property="classifyId" column="classify_id" />
        <result property="classifyName" column="classify_name" />
        <result property="recordCount" column="record_count" />
        <result property="scoreType" column="score_type" />
        <result property="score" column="score" />
    </resultMap>
    <select id="listBehaviourRecordByClassIds"
            resultType="com.hailiang.model.datastatistics.dto.BehaviourStudentClassifyStatisticsDTO">
        SELECT
        a.tenant_id,
        a.school_id,
        a.campus_id,
        a.campus_section_id,
        a.campus_section_code,
        a.grade_id,
        a.grade_code,
        a.class_id,
        a.student_id,
        a.score_type,
        if(b.classify_id is null,-1,b.classify_id) as classify_id,
        if(b.classify_name is null,'其他',b.classify_name) as classify_name,
        sum(a.score_value) as score,
        count(*) as record_count
        FROM evaluate_behaviour_record as a
        left join evaluate_behaviour_record_ext as b
        on a.id = b.behaviour_record_id
        WHERE a.deleted = 0
            AND a.submit_time &gt;= #{conditionQO.startTime}
            AND a.submit_time &lt;= #{conditionQO.endTime}
            AND a.not_part_count = 0
            AND a.data_source not in
            <foreach collection="conditionQO.unusedDatasource" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND a.class_id in
        <foreach collection="conditionQO.classIdList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
        <if test="conditionQO.campusId != null">
            AND a.campus_id = #{conditionQO.campusId}
        </if>
        group by a.tenant_id,a.school_id,a.campus_id,a.campus_section_id,a.campus_section_code,a.grade_id,a.grade_code,a.class_id,a.student_id,b.classify_id,b.classify_name,a.score_type
    </select>


    <resultMap id="classOverviewScoreDetailMap" type="com.hailiang.model.datastatistics.dto.ClassOverviewScoreDetailDTO">
        <result property="studentId" column="student_id" />
        <result property="score" column="score" />
        <result property="scoreType" column="score_type" />
        <result property="classifyId" column="classify_id" />
        <result property="classifyName" column="classify_name" />
        <result property="targetId" column="target_id" />
        <result property="targetName" column="target_name" />
        <result property="optionId" column="option_id" />
        <result property="infoName" column="info_name" />
        <result property="dataSource" column="data_source" />
        <result property="moduleCode" column="module_code" />
        <result property="submitTime" column="submit_time" />
        <result property="appraisalName" column="appraisal_name" />
    </resultMap>
    <select id="listClassOverviewScoreDetail"
            resultMap="classOverviewScoreDetailMap">
        select * from (
        SELECT
        a.student_id,
        a.score,
        a.score_type,
        if(b.classify_id is null,-1,b.classify_id) as classify_id,
        if(b.classify_name is null,'其他',b.classify_name) as classify_name,
        a.target_id,
        a.target_name,
        a.option_id,
        a.info_name,
        a.submit_time,
        a.module_code,
        a.data_source,
        a.appraisal_name
        FROM evaluate_behaviour_record as a
        left join evaluate_behaviour_record_ext as b
        on a.id = b.behaviour_record_id
        WHERE a.deleted = 0
        AND a.is_score = 1
        AND a.submit_time &gt;= #{conditionQO.startTime}
        AND a.submit_time &lt;= #{conditionQO.endTime}
        AND a.not_part_count = 0
        AND a.data_source not in
        <foreach collection="conditionQO.unusedDatasource" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and a.campus_id = #{conditionQO.campusId}
        <if test="conditionQO.classId != null">
            and a.class_id = #{conditionQO.classId}
        </if>
        <if test="conditionQO.classIdList != null and conditionQO.classIdList.size > 0">
            AND a.class_id in
            <foreach collection="conditionQO.classIdList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        union all

        SELECT
        a.student_id,
        a.score,
        a.score_type,
        if(a.classify_id is null,-1,a.classify_id) as classify_id,
        if(a.classify_name is null,'其他',a.classify_name) as classify_name,
        a.target_id,
        a.target_name,
        a.option_id,
        a.help_desc as info_name,
        a.submit_time,
        a.module_code,
        -1 as data_source,
        a.appraisal_name
        FROM evaluate_help_behaviour_record as a
        WHERE a.deleted = 0
        AND a.submit_time &gt;= #{conditionQO.startTime}
        AND a.submit_time &lt;= #{conditionQO.endTime}
        <if test="conditionQO.classId != null">
            and a.class_id = #{conditionQO.classId}
        </if>
        <if test="conditionQO.classIdList != null and conditionQO.classIdList.size > 0">
            AND a.class_id in
            <foreach collection="conditionQO.classIdList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) as all_view
        order by all_view.submit_time desc
    </select>
    <select id="selectByCondition"
            resultType="com.hailiang.model.response.EvaluateFixDataResponse">
        SELECT
        sql1.id,
        sql1.student_id,
        sql1.class_id,
        sql1.target_id,
        sql1.target_name,
        sql1.module_code,
        sql1.info_name,
        sql1.subject_code AS recordSubjectCode,
        sql2.submit_value,
        sql2.subject_code AS targetSubjectCode,
        sql2.subject_name AS targetSubjectName
        FROM
        (SELECT
        id,
        target_id,
        student_id,
        class_id,
        target_name,
        subject_code,
        module_code,
        info_name
        FROM
        evaluate_behaviour_record
        WHERE
       campus_id = #{campusId}
        and module_code in(2,4)
        and deleted = 0
        AND submit_time BETWEEN '2025-06-01 00:00:00' AND NOW()) AS sql1
        LEFT JOIN
        (SELECT
        DISTINCT etu.target_id,
        etu.submit_value,
        rsi.subject_code,
        rsi.subject_name
        FROM
        evaluate_target_user etu
        LEFT JOIN
        report_subject_info rsi ON etu.submit_value = rsi.subject_id
        WHERE
        etu.submit_type = 10
        AND etu.deleted = 0
        AND rsi.deleted = 0) AS sql2
        ON
        sql1.target_id = sql2.target_id
        WHERE
        sql1.subject_code != sql2.subject_code or  (sql1.subject_code = "0" and sql2.subject_code is not null);
    </select>


</mapper>
