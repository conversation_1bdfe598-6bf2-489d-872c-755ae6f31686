<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.CheckGroupMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.CheckGroupPO">
        <!--@mbg.generated-->
        <!--@Table check_group-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="campus_id" jdbcType="VARCHAR" property="campusId"/>
        <result column="dim_id" jdbcType="VARCHAR" property="dimId"/>
        <result column="source_type" jdbcType="CHAR" property="sourceType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="sort_index" jdbcType="BIGINT" property="sortIndex"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cext" jdbcType="VARCHAR" property="cext"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id, school_id, campus_id, dim_id, source_type, `name`, sort_index, deleted, create_by,
        update_by, create_time, update_time, cext
    </sql>
</mapper>