<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.doris.DorisMoralMapper">
    <select id="listStudentScoreForCampus" resultType="com.hailiang.model.vo.studentmodel.MoralStudentScore">
        SELECT
        s.student_id studentId,
        p.production_level productionLevel,
        p.activity_id activityId,
        c.rule_value totalScore
        FROM
        moral_activity_task_production p
        JOIN moral_activity_production_r_student s ON p.id = s.production_id
        JOIN moral_activity_r_evaluate_config c ON p.activity_id = c.activity_id
        AND p.production_level = c.rule_code
        AND c.rule_type = 1
        WHERE
        p.activity_id IN
        <foreach collection="activityIds" item="activityId" open="(" separator="," close=")">
            #{activityId}
        </foreach>
        <if test="startTime != null">
            AND p.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND p.create_time &lt;= #{endTime}
        </if>
        AND p.deleted = 0
        AND s.deleted = 0
        AND c.deleted = 0
        ORDER BY
        s.student_id,
        p.activity_id;
    </select>

    <select id="listStudentScoreForOffCampus" resultType="com.hailiang.model.vo.studentmodel.MoralStudentScore">
        SELECT
            t.assignee_id studentId,
            r.production_level productionLevel,
            t.activity_id activityId,
            c.rule_value totalScore
        FROM
            moral_activity_task t
                JOIN moral_activity_task_review r ON t.id = r.committed_task_id
                JOIN moral_activity_r_evaluate_config c ON r.activity_id = c.activity_id
                AND r.production_level = c.rule_code
                AND c.rule_type = 1
        WHERE
            t.assignee_type = 1
          AND t.activity_id IN -- 指定活动ID
        <foreach collection="activityIds" item="activityId" open="(" separator="," close=")">
            #{activityId}
        </foreach>
        <if test="startTime != null">
            AND r.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND r.create_time &lt;= #{endTime}
        </if>
          AND t.deleted = 0
          AND r.deleted = 0
          AND c.deleted = 0
        ORDER BY
            t.assignee_id,
            t.activity_id;
    </select>

    <select id="listStudentScoreForCampusV2" resultType="com.hailiang.model.vo.studentmodel.MoralStudentScore">
        SELECT
        puc.activity_task_id as activityId,
        pucrs.student_id as studentId,
        marec.rule_value as totalScore,
        pur.review_level as productionLevel
        from
        practical_user_composition puc
        JOIN practical_user_composition_r_student pucrs ON puc.id = pucrs.user_composition_id
        JOIN practical_activity pa ON puc.activity_id = pa.id
        JOIN moral_activity_r_evaluate_config marec ON CAST(puc.activity_task_id AS VARCHAR) = marec.activity_id
        JOIN practical_user_review pur ON puc.id = pur.source_id
        AND pur.review_level = marec.rule_code
        AND pur.source_type = 2
        WHERE
        marec.rule_type = 1
        AND pa.score_settlement_status = 3
        AND puc.audit_status = 3
        AND puc.submit_user_type = 2
        AND
        puc.activity_task_id IN
        <foreach collection="activityIds" item="activityId" open="(" separator="," close=")">
            #{activityId}
        </foreach>
        <if test="startTime != null">
            AND puc.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND puc.create_time &lt;= #{endTime}
        </if>
        AND puc.deleted = 0
        AND pucrs.deleted = 0
        AND marec.deleted = 0
        AND pur.deleted = 0
        ORDER BY
        pucrs.student_id,
        puc.activity_task_id;
    </select>

    <select id="listStudentScoreForOffCampusV2" resultType="com.hailiang.model.vo.studentmodel.MoralStudentScore">
        SELECT
        put.user_id as studentId,
        pur.review_level as productionLevel,
        put.activity_task_id as activityId,
        marec.rule_value as totalScore
        FROM
        practical_user_task put
        JOIN practical_user_review pur ON put.id = pur.source_id
        JOIN practical_activity pa ON put.activity_id = pa.id
        JOIN moral_activity_r_evaluate_config marec ON CAST(put.activity_task_id AS VARCHAR) = marec.activity_id
        AND pur.review_level = marec.rule_code
        AND marec.rule_type = 1
        where
        put.user_type = 1
        AND pa.score_settlement_status = 3
        and pur.source_type = 1
        and put.activity_task_id in
        <foreach collection="activityIds" item="activityId" open="(" separator="," close=")">
            #{activityId}
        </foreach>
        <if test="startTime != null">
            AND put.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND put.create_time &lt;= #{endTime}
        </if>
        AND put.deleted = 0
        AND marec.deleted = 0
        AND pur.deleted = 0
        ORDER BY
        put.user_id,
        put.activity_task_id;
    </select>
</mapper>
