<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.ReportVideoFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.ReportVideoFilePO">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="school_id" property="schoolId" />
        <result column="campus_id" property="campusId" />
        <result column="business_id" property="businessId" />
        <result column="file_name" property="fileName" />
        <result column="original_file_url" property="originalFileUrl" />
        <result column="file_url" property="fileUrl" />
        <result column="file_size" property="fileSize" />
        <result column="format" property="format" />
        <result column="transcode_status" property="transcodeStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        tenant_id, school_id, campus_id, business_id, file_name, original_file_url, file_url, file_size, format, transcode_status
    </sql>

</mapper>
