<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.StudentAbilityModelMapper">

    <resultMap id="studentAbilityModelMap" type="com.hailiang.model.entity.StudentAbilityModel">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="schoolId" column="school_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="abilityModelName" column="ability_model_name"/>
        <result property="contrastRange" column="contrast_range"/>
        <result property="enabled" column="enabled"/>
        <result property="submitTime" column="submit_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="examSource" column="exam_source"/>
    </resultMap>

    <sql id="BASE_COLUMN">
        id
        ,tenant_id,school_id,campus_id,ability_model_name,contrast_range,enabled,
		  submit_time,create_by,create_time,update_time,update_by,deleted,exam_source
    </sql>
    <update id="updateEnabledStatus">
        update evaluate_student_ability_model
        set enabled = #{param.enabled}
        where id = #{param.id}
    </update>

    <select id="getStudentAbilityModelByDate" resultType="com.hailiang.model.entity.StudentAbilityModel">
        SELECT
        <include refid="BASE_COLUMN"/>
        FROM evaluate_student_ability_model
        WHERE deleted = 0
            and campus_id = #{campusId}
        <if test="dateTime != null">
            AND create_time &lt;= #{dateTime}
        </if>
        ORDER BY create_time DESC
        LIMIT 1;
    </select>

    <select id="listByTime" resultMap="studentAbilityModelMap">
        SELECT
        <include refid="BASE_COLUMN"/>
        FROM evaluate_student_ability_model
        WHERE deleted = 0
        and campus_id = #{dto.campusId}
        order by create_time desc limit 1;
    </select>
</mapper>