<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.ThirdDataInfoMapper">
    <update id="updateFail">
        update evaluate_third_data_info
        set dispose_flag = 2,
            update_by='system',
            update_time=now(),
            remark=#{remark}
        where id = #{id}
    </update>
    <update id="updateSuccess">
        update evaluate_third_data_info
        set dispose_flag   = 1,
            update_by='system',
            update_time=now(),
            local_record_id=#{localRecordId},
            campus_id=#{campusId}
        where id = #{id}
    </update>

    <!--批量更新第三方数据失败状态与原因-->
    <update id="batchUpdateFail">
        <foreach collection="thirdDataInfoDTOS" item="item" separator=";">
            update evaluate_third_data_info set
            dispose_flag = 2,
            update_by='system',
            update_time=now(),
            remark=#{item.remark}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <!--批量更新第三方数据成功状态与本地落地ID-->
    <update id="batchUpdateSuccess">
        <foreach collection="thirdDataInfoDTOS" item="item" separator=";">
            update evaluate_third_data_info
            set dispose_flag = 1,
            update_by='system',
            update_time=now(),
            local_record_id=#{item.localRecordId},
            campus_id=#{item.campusId}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>