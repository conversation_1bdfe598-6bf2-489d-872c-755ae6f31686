<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.SpeedConfigMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.SpeedConfigPO">
        <!--@Table evaluate_speed_config-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="campus_id" jdbcType="VARCHAR" property="campusId"/>
        <result column="staff_id" jdbcType="VARCHAR" property="staffId"/>
        <result column="class_ids" jdbcType="VARCHAR" property="classIds"/>
        <result column="is_frequent_reviews" jdbcType="TINYINT" property="isFrequentReviews"/>
        <result column="uncommented_time_type" jdbcType="TINYINT" property="uncommentedTimeType"/>
        <result column="remind_weeks" jdbcType="VARCHAR" property="remindWeeks"/>
        <result column="remind_time" jdbcType="CHAR" property="remindTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        tenant_id,
        school_id,
        campus_id,
        staff_id,
        class_ids,
        is_frequent_reviews,
        uncommented_time_type,
        remind_weeks,
        remind_time,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted
    </sql>
</mapper>