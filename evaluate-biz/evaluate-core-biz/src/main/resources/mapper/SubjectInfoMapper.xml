<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.SubjectInfoMapper">

    <select id="listReportSubjectInfo" resultType="com.hailiang.model.report.entity.SubjectInfoPO">
        select * from report_subject_info
        ${ew.customSqlSegment}
    </select>

    <select id="listWithOutDeleted" resultType="com.hailiang.model.report.entity.SubjectInfoPO">
        select * from report_subject_info
        where
        <if test="campusId != null and campusId != ''">
            campus_id = #{campusId}
        </if>
        <if test="subjectCodes != null and subjectCodes.size() > 0">
            and subject_code in
            <foreach collection="subjectCodes" item="subjectCode" open="(" separator="," close=")">
                #{subjectCode}
            </foreach>
        </if>
    </select>
</mapper>