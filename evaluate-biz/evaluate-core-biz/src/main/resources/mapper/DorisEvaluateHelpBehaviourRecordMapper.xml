<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hailiang.mapper.doris.DorisEvaluateHelpBehaviourRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.EvaluateHelpBehaviourRecordPO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="school_id" property="schoolId"/>
        <result column="campus_id" property="campusId"/>
        <result column="campus_section_id" property="campusSectionId"/>
        <result column="campus_section_code" property="campusSectionCode"/>
        <result column="grade_id" property="gradeId"/>
        <result column="grade_code" property="gradeCode"/>
        <result column="class_id" property="classId"/>
        <result column="student_id" property="studentId"/>
        <result column="student_name" property="studentName"/>
        <result column="module_code" property="moduleCode"/>
        <result column="target_id" property="targetId"/>
        <result column="target_name" property="targetName"/>
        <result column="classify_id" property="classifyId"/>
        <result column="classify_name" property="classifyName"/>
        <result column="option_id" property="optionId"/>
        <result column="option_name" property="optionName"/>
        <result column="help_desc" property="helpDesc"/>
        <result column="score_type" property="scoreType"/>
        <result column="score" property="score"/>
        <result column="score_value" property="scoreValue"/>
        <result column="data_type" property="dataType"/>
        <result column="data_channel" property="dataChannel"/>
        <result column="submit_time" property="submitTime"/>
        <result column="appraisal_id" property="appraisalId"/>
        <result column="appraisal_name" property="appraisalName"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <!-- 表字段 -->
    <sql id="baseColumns">
        t
        .
        id
        , t.tenant_id , t.school_id , t.campus_id , t.campus_section_id ,
        t.campus_section_code , t.grade_id , t.grade_code , t.class_id ,
        t.student_id , t.student_name , t.module_code , t.target_id ,
        t.target_name , t.classify_id , t.classify_name , t.option_id ,
        t.option_name , t.help_desc , t.score_type , t.score , t.score_value ,
        t.data_type , t.data_channel , t.submit_time , t.appraisal_id , t.appraisal_name ,
        t.create_by , t.create_time , t.update_by , t.update_time , t.deleted
    </sql>

    <select id="listStudentDailyStatistics" resultType="com.hailiang.portrait.entity.StudentDailyStatisticsPO">
        SELECT
        tenant_id,
        school_id,
        campus_id,
        campus_section_id,
        campus_section_code,
        grade_id,
        grade_code,
        class_id,
        student_id,
        ROUND(SUM(CASE WHEN module_code = 1 AND score_type = 1 THEN score ELSE 0 END),2) AS plus_moral_score,
        ROUND(SUM(CASE WHEN module_code = 1 AND score_type = 2 THEN score ELSE 0 END),2) AS minus_moral_score,
        ROUND(SUM(CASE WHEN module_code = 2 AND score_type = 1 THEN score ELSE 0 END),2) AS plus_wisdom_score,
        ROUND(SUM(CASE WHEN module_code = 2 AND score_type = 2 THEN score ELSE 0 END),2) AS minus_wisdom_score,
        ROUND(SUM(CASE WHEN module_code = 3 AND score_type = 1 THEN score ELSE 0 END),2) AS plus_sport_score,
        ROUND(SUM(CASE WHEN module_code = 3 AND score_type = 2 THEN score ELSE 0 END),2) AS minus_sport_score,
        ROUND(SUM(CASE WHEN module_code = 4 AND score_type = 1 THEN score ELSE 0 END),2) AS plus_pretty_score,
        ROUND(SUM(CASE WHEN module_code = 4 AND score_type = 2 THEN score ELSE 0 END),2) AS minus_pretty_score,
        ROUND(SUM(CASE WHEN module_code = 5 AND score_type = 1 THEN score ELSE 0 END),2) AS plus_work_score,
        ROUND(SUM(CASE WHEN module_code = 5 AND score_type = 2 THEN score ELSE 0 END),2) AS minus_work_score,
        ROUND(SUM(CASE WHEN module_code = 0 AND score_type = 1 THEN score ELSE 0 END),2) AS plus_other_score,
        ROUND(SUM(CASE WHEN module_code = 0 AND score_type = 2 THEN score ELSE 0 END),2) AS minus_other_score,
        ROUND(SUM(CASE WHEN score_type = 1 THEN score ELSE 0 END),2) AS plus_total_score,
        ROUND(SUM(CASE WHEN score_type = 2 THEN score ELSE 0 END),2) AS minus_total_score,
        ROUND(SUM(score),2) AS total_score,
        DATE(submit_time) AS statistics_time,
        COUNT(*) AS appraisal_count
        FROM
        evaluate_help_behaviour_record
        WHERE deleted = 0
        AND not_part_count = 0
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        <if test="campusId != null and campusId != ''">
            AND campus_id = #{campusId}
        </if>
        <if test="classId != null and classId != ''">
            AND class_id = #{classId}
        </if>
        <if test="needIds != null and needIds.size() > 0">
            AND id IN
            <foreach collection="needIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="includeInternalScoreFlag == false">
            AND data_type = 4
        </if>
        GROUP BY
        tenant_id,
        school_id,
        campus_id,
        campus_section_id,
        campus_section_code,
        grade_id,
        grade_code,
        class_id,
        student_id,
        DATE(submit_time)
        ORDER BY
        statistics_time desc;
    </select>


    <select id="listStudentHelpPortrait" resultType="com.hailiang.model.vo.BehaviourStudentFileVO">

        SELECT
        student_id studentId,
        ROUND(SUM(score), 2) AS sumScore,
        ROUND(SUM(CASE WHEN score_type = 1 THEN score ELSE 0 END), 2) AS addScore,
        ROUND(SUM(CASE WHEN score_type = 2 THEN score ELSE 0 END), 2) AS subtractScore
        FROM
        evaluate_help_behaviour_record
        WHERE deleted = 0
        AND not_part_count = 0
        <if test="startTime != null">
            AND submit_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND submit_time &lt;= #{endTime}
        </if>
        AND campus_id = #{campusId}
        AND campus_section_id = #{campusSectionId}
        <if test="gradeIdList != null and gradeIdList.size() > 0">
            AND grade_id IN
            <foreach collection="gradeIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="classIdList != null and classIdList.size() > 0">
            AND class_id IN
            <foreach collection="classIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="studentIdList != null and studentIdList.size() > 0">
            AND student_id IN
            <foreach collection="studentIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="moduleCode != null and moduleCode != ''">
            AND module_code = #{moduleCode}
        </if>
        <if test="includeInternalScoreFlag == false">
            AND data_type = 4
        </if>
        GROUP BY
        student_id
    </select>



    <resultMap id="statisticsCountMap" type="com.hailiang.behaviour.classify.statistics.model.dto.BehaviourClassifyStatisticsCountDTO">
        <result property="classId" column="class_id"/>
        <result property="studentId" column="student_id"/>
        <result property="classifyId" column="classify_id"/>
        <result property="classifyName" column="classify_name"/>
        <result property="recordCount" column="record_count"/>
        <result property="scoreType" column="score_type"/>
        <result property="scoreSum" column="score"/>
    </resultMap>
    <select id="queryStatisticsCount" resultMap="statisticsCountMap">
        select
        class_id,
        student_id,
        classify_id,
        classify_name,
        count(*) as record_count,
        score_type,
        sum(score_value) as score
        from evaluate_help_behaviour_record
        where deleted = 0
        and submit_time >= #{statisticsRecordQO.startTime}
        and submit_time &lt;= #{statisticsRecordQO.endTime}
        and not_part_count = 0
        and class_id = #{statisticsRecordQO.classId}
        <if test="statisticsRecordQO.studentId != null">
            and student_id = #{statisticsRecordQO.studentId}
        </if>
        <if test="statisticsRecordQO.studentIds != null and statisticsRecordQO.studentIds.size > 0">
            and student_id in
            <foreach collection="statisticsRecordQO.studentIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by class_id,student_id,classify_id,classify_name,score_type
    </select>

</mapper>
