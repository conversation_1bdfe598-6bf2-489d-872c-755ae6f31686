<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.SysFactoryMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.point.entity.SysFactory">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="factoryName" column="factory_name" jdbcType="VARCHAR"/>
            <result property="factoryAddress" column="factory_address" jdbcType="VARCHAR"/>
            <result property="contactPhone" column="contact_phone" jdbcType="VARCHAR"/>
            <result property="factoryDescription" column="factory_description" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,factory_name,factory_address,
        contact_phone,factory_description,create_by,
        create_time,update_by,update_time,
        deleted
    </sql>
</mapper>
