<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.ReportCategoryConfigMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.report.entity.SubjectEvaluationDimCategoryConfigPO">
        <!--@mbg.generated-->
        <!--@Table report_category_config-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="campus_id" jdbcType="VARCHAR" property="campusId"/>
        <result column="dim_id" jdbcType="BIGINT" property="dimId"/>
        <result column="subject_id" jdbcType="BIGINT" property="subjectId"/>
        <result column="category_name" jdbcType="VARCHAR" property="categoryName"/>
        <result column="category_type" jdbcType="TINYINT" property="categoryType"/>
        <result column="weight" jdbcType="INTEGER" property="weight"/>
        <result column="enable_flag" jdbcType="TINYINT" property="enableFlag"/>
        <result column="archive_flag" jdbcType="TINYINT" property="archiveFlag"/>
        <result column="rule_detail" jdbcType="LONGVARCHAR" property="ruleDetail"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="level_type" jdbcType="INTEGER" property="levelType"/>
        <result column="level_name" jdbcType="VARCHAR" property="levelName"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, parent_id, tenant_id, school_id, campus_id, dim_id, subject_id, category_name, category_type,
        weight, enable_flag, archive_flag, rule_detail, deleted, create_by, update_by, create_time, update_time
        level_type, level_name
    </sql>
    <insert id="saveBatch">
        insert into report_category_config (id, parent_id, tenant_id,
        school_id, campus_id, dim_id, subject_id,
        category_name, category_type, weight,
        enable_flag,rule_detail,
        create_by, create_time, level_type, level_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.parentId,jdbcType=BIGINT}, #{item.tenantId,jdbcType=VARCHAR},
            #{item.schoolId,jdbcType=VARCHAR}, #{item.campusId,jdbcType=VARCHAR},
            #{item.dimId,jdbcType=BIGINT},#{item.subjectId,jdbcType=BIGINT},
            #{item.categoryName,jdbcType=VARCHAR}, #{item.categoryType,jdbcType=TINYINT},
            #{item.weight,jdbcType=INTEGER}, #{item.enableFlag,jdbcType=TINYINT},
            #{item.ruleDetail,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR}, now(),
            #{item.levelType,jdbcType=INTEGER}, #{item.levelName,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>