<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.doris.DorisSubjectMapper">
    <select id="listStudentScore" resultType="com.hailiang.model.vo.studentmodel.SubjectStudentScore">
        SELECT
        stu_id AS studentId,
        subject_code AS subjectCode,
        subject_name AS subjectName,
        ROUND(AVG(subject_score), 2) AS totalScore
        FROM
        report_subject_score
        WHERE
        campus_id = #{campusId}
        <if test="termStartTime != null">
            AND start_date &gt;= #{termStartTime}
        </if>
        <if test="termEndTime != null">
            AND start_date &lt;= #{termEndTime}
        </if>
        AND exam_type = 3 -- 期末考试
        AND deleted = 0
        AND subject_code IN -- 指定课程
        <foreach collection="subjectCodes" item="subjectCode" open="(" separator="," close=")">
            #{subjectCode}
        </foreach>
        GROUP BY
        stu_id,
        subject_code,subject_name
        ORDER BY
        stu_id,
        subject_code;
    </select>


    <!-- 查询学生成绩 -->
    <select id="listStudentScoresByExamType" parameterType="com.hailiang.model.report.dto.ExamConfigQueryDTO" resultType="com.hailiang.model.vo.studentmodel.SubjectStudentScore">
        SELECT
        stu_id AS studentId,
        subject_code AS subjectCode,
        subject_name AS subjectName,
        <choose>
            <when test="calculateType == 1">
                ROUND(AVG(subject_score), 2) AS totalScore
            </when>
            <when test="calculateType == 2">
                MAX(subject_score) AS totalScore
            </when>
            <otherwise>
                ROUND(AVG(subject_score), 2) AS totalScore <!-- 默认取平均值 -->
            </otherwise>
        </choose>
        FROM
        report_subject_score
        WHERE
        campus_id = #{campusId}
        <if test="termStartTime != null">
            AND start_date &gt;= #{termStartTime}
        </if>
        <if test="termEndTime != null">
            AND start_date &lt;= #{termEndTime}
        </if>
        AND deleted = 0
        AND exam_type = #{examType}
        <if test="!allExamFlag and examIds != null and examIds.size() > 0">
            AND exam_id IN (
            <foreach collection="examIds" item="examId" separator=",">
                #{examId}
            </foreach>
            )
        </if>
        AND subject_code IN (
        <foreach collection="subjectCodes" item="subjectCode" separator=",">
            #{subjectCode}
        </foreach>
        )
        GROUP BY
        stu_id,
        subject_code,
        subject_name
        ORDER BY
        stu_id,
        subject_code
    </select>

</mapper>
