<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.EvaluateBehaviourRecordExtMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hailiang.entity.EvaluateBehaviourRecordExtPO">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="behaviour_record_id" property="behaviourRecordId" />
        <result column="classify_id" property="classifyId" />
        <result column="classify_name" property="classifyName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        behaviour_record_id, classify_id, classify_name
    </sql>

</mapper>
