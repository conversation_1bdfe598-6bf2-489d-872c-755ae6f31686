<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.EvaluateBehaviourImportsRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hailiang.entity.EvaluateBehaviourImportsRecordPO">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="school_id" property="schoolId" />
        <result column="campus_id" property="campusId" />
        <result column="campus_section_id" property="campusSectionId" />
        <result column="campus_section_code" property="campusSectionCode" />
        <result column="grade_id" property="gradeId" />
        <result column="grade_code" property="gradeCode" />
        <result column="class_ids" property="classIds" />
        <result column="appraisal_id" property="appraisalId" />
        <result column="appraisal_name" property="appraisalName" />
        <result column="appraisal_type" property="appraisalType" />
        <result column="module_code" property="moduleCode" />
        <result column="target_group_id" property="targetGroupId" />
        <result column="target_group_name" property="targetGroupName" />
        <result column="target_id" property="targetId" />
        <result column="target_name" property="targetName" />
        <result column="submit_id" property="submitId" />
        <result column="validated_submit_content" property="validatedSubmitContent" />
        <result column="subject_code" property="subjectCode" />
        <result column="subject_name" property="subjectName" />
        <result column="deal_flag" property="dealFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_time,
        update_by,
        update_time,
        deleted,
        tenant_id, school_id, campus_id, campus_section_id, campus_section_code, grade_id, grade_code, class_ids, appraisal_id, appraisal_name, appraisal_type, module_code, target_group_id, target_group_name, target_id, target_name, submit_id, validated_submit_content, subject_code, subject_name, deal_flag
    </sql>

</mapper>
