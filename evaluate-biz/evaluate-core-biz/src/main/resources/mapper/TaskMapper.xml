<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.TaskMapper">


    <select id="listTeacherRemindTask" resultType="com.hailiang.model.entity.TaskPO">
        SELECT DISTINCT
            a.*
        FROM
            evaluate_task a
            LEFT JOIN evaluate_message_log b
        ON a.id = b.business_id
            AND b.deleted = 0 AND b.message_type = 100
        WHERE
            a.deleted = 0
          AND #{now} >= a.remind_time
          AND a.remind_time >= #{beginOfDay}
          AND a.role_type = 1
          AND a.task_status = 1
          AND b.id IS NULL
    </select>

    <select id="listParentRemindTask" resultType="com.hailiang.model.entity.TaskPO">
        SELECT DISTINCT
            a.*
        FROM
            evaluate_task a
            LEFT JOIN evaluate_message_log b
        ON a.id = b.business_id
            AND b.deleted = 0 AND b.message_type = 201
        WHERE
            a.deleted = 0
          AND #{now} >= a.remind_time
          AND a.remind_time >= #{beginOfDay}
          AND a.role_type = 2
          AND a.task_status = 1
          AND a.approval_status is NULL
          AND b.id IS NULL
    </select>

    <select id="listTeacherUrgeTask" resultType="com.hailiang.model.entity.TaskPO">
        SELECT DISTINCT
            a.*
        FROM
            evaluate_task a
            LEFT JOIN evaluate_message_log b
        ON a.id = b.business_id
            AND b.deleted = 0 AND b.message_type =102
        WHERE
            a.deleted = 0
          AND a.urge_time &lt;= #{now}
          AND a.urge_time >= #{beginOfDay}
          AND a.role_type = 1
          AND a.task_status = 1
          AND b.id IS NULL
    </select>

    <select id="listParentUrgeTask" resultType="com.hailiang.model.entity.TaskPO">
        SELECT DISTINCT
            a.*
        FROM
            evaluate_task a
            LEFT JOIN evaluate_message_log b
        ON a.id = b.business_id
            AND b.deleted = 0 AND b.message_type = 202
        WHERE
            a.deleted = 0
          AND a.urge_time &lt;= #{now}
          AND a.urge_time >= #{beginOfDay}
          AND a.role_type = 2
          AND a.task_status = 1
          AND a.approval_status is NULL
          AND b.id IS NULL
    </select>

    <select id="listTeacherTasks" resultType="com.hailiang.model.entity.TaskPO">
        SELECT
            id,
            tenant_id,
            school_id,
            campus_id,
            target_id,
            task_name,
            task_date,
            remind_time,
            urge_time,
            submit_staff_id,
            role_type,
            task_type,
            task_status,
            approval_status,
            submit_time,
            mobile,
            create_by,
            create_time,
            update_time,
            update_by,
            deleted
        FROM
            evaluate_task
        WHERE
            deleted = 0
          AND campus_id = '7014142802895892480'
          AND (submit_staff_id = '7014145369805729792'AND task_status = 1
               OR ( submit_staff_id IN ( 7027846937625260032, 7027846747686203392, 7020609329232900096, 7020609154015850496, 7014232447117733888, 7014232329828216832 ) AND approval_status = 1 AND task_status = 2 ))
        ORDER BY
            create_time DESC;
    </select>

</mapper>
