<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.MedalActivityMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.MedalActivity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
            <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
    </resultMap>

    <select id="listMedalActivityInfo" resultType="com.hailiang.model.dto.activity.detail.MedalActivityInfoVO">
        SELECT
            id as activityId,
           `name` as activityName,
            start_time,
            end_time,
           `status`
        FROM
            medal_activity
        WHERE
            id IN
        <foreach item="item" collection="list" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
