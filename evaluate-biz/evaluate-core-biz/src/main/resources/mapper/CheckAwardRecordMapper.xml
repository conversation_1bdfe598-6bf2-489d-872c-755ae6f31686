<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.CheckAwardRecordMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.CheckAwardRecord">
        <!--@mbg.generated-->
        <!--@Table check_award_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="campus_id" jdbcType="VARCHAR" property="campusId"/>
        <result column="school_year" jdbcType="VARCHAR" property="schoolYear"/>
        <result column="campus_section_id" jdbcType="VARCHAR" property="campusSectionId"/>
        <result column="campus_section_code" jdbcType="VARCHAR" property="campusSectionCode"/>
        <result column="award_type" jdbcType="INTEGER" property="awardType"/>
        <result column="initial_score" jdbcType="INTEGER" property="initialScorePO"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="is_notify_teacher" jdbcType="TINYINT" property="isNotifyTeacher"/>
        <result column="is_notify_class" jdbcType="TINYINT" property="isNotifyClass"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cext" jdbcType="VARCHAR" property="cext"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id, school_id, campus_id, school_year, campus_section_id, campus_section_code, award_type,
        initial_score, start_time, end_time, is_notify_teacher, is_notify_class, deleted, create_by,
        update_by, create_time, update_time, cext
    </sql>
</mapper>