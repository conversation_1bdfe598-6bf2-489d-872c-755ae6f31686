<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.CheckEvaluatorMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.entity.CheckEvaluatorPO">
        <!--@mbg.generated-->
        <!--@Table check_evaluator-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="campus_id" jdbcType="VARCHAR" property="campusId"/>
        <result column="check_item_id" jdbcType="VARCHAR" property="checkItemId"/>
        <result column="source_type" jdbcType="CHAR" property="sourceType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="evaluator_type" jdbcType="VARCHAR" property="evaluatorType"/>
        <result column="business_code" jdbcType="VARCHAR" property="businessCode"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="cext" jdbcType="VARCHAR" property="cext"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,tenant_id, school_id, campus_id, check_item_id, source_type, `name`, evaluator_type, business_code, deleted,
        create_by, update_by, create_time, update_time, cext
    </sql>

    <insert id="saveBatch">
        insert into check_evaluator (id,tenant_id, school_id, campus_id, check_item_id, source_type, `name`,
        evaluator_type, business_code,deleted, create_by, update_by,create_time, update_time,cext) values
        <foreach collection="evaluators" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT},#{item.tenantId,jdbcType=VARCHAR},#{item.schoolId,jdbcType=VARCHAR},#{item.campusId,jdbcType=VARCHAR},
            #{item.checkItemId,jdbcType=VARCHAR},#{item.sourceType,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
            #{item.evaluatorType,jdbcType=VARCHAR}, #{item.businessCode,jdbcType=VARCHAR},
            0,#{item.createBy,jdbcType=VARCHAR},#{item.updateBy,jdbcType=VARCHAR},now(),now(),
            #{item.cext,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>