<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.CabinetLatticeLayoutMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.point.entity.CabinetLatticeLayout">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="cabinetId" column="cabinet_id" jdbcType="VARCHAR"/>
        <result property="cabinetLatticeId" column="cabinet_lattice_id" jdbcType="VARCHAR"/>
        <result property="cabinetLatticeTypeId" column="cabinet_lattice_type_id" jdbcType="BIGINT"/>
        <result property="latticeLayer" column="lattice_layer" jdbcType="INTEGER"/>
        <result property="latticeIndex" column="lattice_index" jdbcType="INTEGER"/>
        <result property="latticeNumber" column="lattice_number" jdbcType="VARCHAR"/>
        <result property="productId" column="product_id" jdbcType="BIGINT"/>
        <result property="latticeCustomNumber" column="lattice_custom_number" jdbcType="VARCHAR"/>
        <result property="productNum" column="product_num" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,cabinet_id,cabinet_lattice_id,
        cabinet_lattice_type_id,lattice_layer,lattice_index,
        lattice_number,product_id,lattice_custom_number,product_num,create_by,
        create_time,update_by,update_time,
        deleted
    </sql>
</mapper>
