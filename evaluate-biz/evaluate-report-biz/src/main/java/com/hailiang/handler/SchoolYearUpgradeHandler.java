package com.hailiang.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hailiang.constant.Constant;
import com.hailiang.convert.StudentAbilityModelConvert;
import com.hailiang.enums.EventCodeEnum;
import com.hailiang.manager.SysArchivedLogManager;
import com.hailiang.manager.TargetArchivedManager;
import com.hailiang.manager.TargetGroupArchivedManager;
import com.hailiang.manager.TargetUserArchivedManager;
import com.hailiang.model.dto.CampusSectionSchoolYearUpgradeDTO;
import com.hailiang.model.dto.SchoolYearUpgradeDTO;
import com.hailiang.model.entity.SysArchivedLogPO;
import com.hailiang.service.StudentAbilityDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ScheduledExecutorService;

/**
 * 学年升级处理事件
 *
 * <AUTHOR>
 * @version 1.6.0
 * @date Created in 2024-07-15
 */
@Slf4j
@Component
public class SchoolYearUpgradeHandler {
    @Resource
    private TargetUserArchivedManager targetUserArchivedManager;
    @Resource
    private TargetArchivedManager targetArchivedManager;
    @Resource
    private TargetGroupArchivedManager targetGroupArchivedManager;
    @Resource
    private ScheduledExecutorService scheduledExecutor;
    @Resource
    private StudentAbilityDataService studentAbilityDataService;
    @Resource
    private SysArchivedLogManager sysArchivedLogManager;
    @Value("${school.year.upgrade.retry.time:1}")
    private Integer retryTime;

    public void doUpgrade(String message) {
        log.info("【升学年事件】- 消费主体对象：{}", message);
        if (StringUtils.isEmpty(message)) return;
        JSONObject decryptJson = JSONUtil.parseObj(message);
        SchoolYearUpgradeDTO query = decryptJson.toBean(SchoolYearUpgradeDTO.class);
        if (StringUtils.isBlank(query.getTaskId()) || CollUtil.isEmpty(query.getContent())
                || !Objects.equals(EventCodeEnum.GRADUATE_SCHOOL_YEAR.getCode(), query.getEventCode())) {
            log.error("【升学年事件】- 参数校验不匹配，直接返回 {}", query);
            return;
        }
        // 异步执行升学年流程：历史数据归档
        int times = 0;
        CompletableFuture.runAsync(() -> this.upgradeSchoolYear(query, times), scheduledExecutor);
    }

    private void upgradeSchoolYear(SchoolYearUpgradeDTO query, int times) {
        List<CampusSectionSchoolYearUpgradeDTO> upgradeList = query.getContent();
        if (CollUtil.isEmpty(upgradeList)){
            log.info("【升学年事件】upgradeList为空，直接返回");
            return;
        }
        try {
            upgradeList.forEach(this::doUpgradeSchoolYear);
        } catch (Exception e) {
            log.error("【升学年事件】- 触发 消息数据:{}，异常第 {}次", JSONUtil.toJsonStr(query), times, e);
            // 抛异常 意味着要自动重试 不建议自动重试 如果逻辑有问题 会一直打印error日志 建议查清楚数据情况后 进行手动补偿（把源数据发送mq）
            if ((++times) <= retryTime) {
                try {
                    // 等2s再重试
                    Thread.sleep(2000);
                    this.upgradeSchoolYear(query, times);
                } catch (InterruptedException ie) {
                    log.error("【升学年事件】重试失败【{}】次，错误信息：", times, ie);
                }
            } else {
                log.error("【升学年事件】- 触发 消息数据:{}，异常 {}次，未成功", JSONUtil.toJsonStr(query), times, e);
            }
        }
    }

    private void doUpgradeSchoolYear(CampusSectionSchoolYearUpgradeDTO upgrade) {
        if (Objects.isNull(upgrade)) {
            return;
        }
        upgrade.setStartTime(DateUtil.beginOfDay(upgrade.getStartTime()));
        upgrade.setEndTime(DateUtil.endOfDay(upgrade.getEndTime()));
        upgrade.setSchoolYear(this.getSchoolYear(upgrade.getUpGradeYear()));
        // 指标填写对象历史学年归档
        targetUserArchivedManager.archive(upgrade);
        // 指标历史学年归档
        targetArchivedManager.archive(upgrade);
        // 指标组历史学年归档
        targetGroupArchivedManager.archive(upgrade);
        // 学生能力模型历史学年配置归档
        studentAbilityDataService.archiveV2(StudentAbilityModelConvert.INSTANCE.toStuAbilityUpgradeQuery(upgrade));
    }

    private List<SysArchivedLogPO> archivedIdempotence(CampusSectionSchoolYearUpgradeDTO upgrade) {
        LambdaQueryWrapper<SysArchivedLogPO> queryWrapper = new LambdaQueryWrapper<SysArchivedLogPO>()
                .eq(Objects.nonNull(upgrade.getSchoolId()), SysArchivedLogPO::getSchoolId, upgrade.getSchoolId())
                .eq(Objects.nonNull(upgrade.getCampusId()), SysArchivedLogPO::getCampusId, upgrade.getCampusId())
                .eq(Objects.nonNull(upgrade.getSchoolYear()), SysArchivedLogPO::getSchoolYear, upgrade.getSchoolYear())
                .eq(SysArchivedLogPO::getDeleted, Constant.ZERO);
        return sysArchivedLogManager.list(queryWrapper);
    }

    private String getSchoolYear(Integer upGradeYear) {
        return String.format("%s-%s", (upGradeYear - 1), upGradeYear);
    }
}
