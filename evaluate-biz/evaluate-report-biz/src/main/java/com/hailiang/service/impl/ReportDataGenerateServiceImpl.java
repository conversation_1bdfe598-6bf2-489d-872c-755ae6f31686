package com.hailiang.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.hailiang.context.ReGenerateHolder;
import com.hailiang.enums.SaasClassTypeEnum;
import com.hailiang.enums.SectionInfoEnum;
import com.hailiang.enums.report.ReportDataBusinessTypeEnum;
import com.hailiang.enums.report.ReportDataGenerateTypeEnum;
import com.hailiang.enums.report.ReportTemplateGradeLevelTypeEnum;
import com.hailiang.exception.BizException;
import com.hailiang.manager.*;
import com.hailiang.model.report.dto.GenerateReportDataDTO;
import com.hailiang.model.report.dto.GenerateReportMqMessageDTO;
import com.hailiang.model.report.entity.*;
import com.hailiang.model.report.query.ReportTemplateGradeQuery;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.dto.educational.EduClassQueryDTO;
import com.hailiang.remote.saas.vo.educational.EduClassInfoVO;
import com.hailiang.saas.SaasHistoryClassCacheManager;
import com.hailiang.saas.SaasHistoryStudentCacheManager;
import com.hailiang.saas.SaasStudentManager;
import com.hailiang.saas.model.dto.StudentPageQueryDTO;
import com.hailiang.saas.model.vo.StudentInfo1VO;
import com.hailiang.saas.model.vo.history.HistoryClassVO;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import com.hailiang.service.ReportDataGenerateService;
import com.hailiang.service.ReportTemplateSnapshotService;
import com.hailiang.util.SnowFlakeIdUtil;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Date：2023-11-08
 * Time：13:52
 * Description：报告单服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReportDataGenerateServiceImpl implements ReportDataGenerateService {

    @Resource
    private BasicInfoRemote basicInfoRemote;
    @Resource
    private SaasStudentManager saasStudentManager;
    @Resource
    private ReportDataManager reportDataManager;
    @Resource
    private ReportDataStudentManager reportDataStudentManager;
    @Resource
    private RocketMQTemplate rocketMQTemplate;
    @Resource
    private SaasHistoryClassCacheManager saasHistoryClassCacheManager;
    @Resource
    private ReportTemplateSnapshotService reportTemplateSnapshotService;
    @Resource
    private ReportTemplateGradeManager reportTemplateGradeManager;
    @Value("${rocketmq.topic.reportData}")
    private String reportDataTopic;

    @Resource
    private ReportDataStudentParentManager reportDataStudentParentManager;

    @Resource
    private SaasHistoryStudentCacheManager saasHistoryStudentCacheManager;

    @Override
    public void reGenerateReportData(GenerateReportDataDTO generateReportDataDTO) {
        Long reportTemplateSnapshotId = ReGenerateHolder.getGenerateReportDataDTO().getReportTemplateSnapshotId();
        Assert.notNull(reportTemplateSnapshotId, "报告单快照ID不能为空");
        ReportTemplateSnapshot reportTemplateSnapshot = reportTemplateSnapshotService.getReportTemplateSnapshotByReportTemplateSnapshotId(reportTemplateSnapshotId);

        // 查询模板快照学生数据
        ReportTemplateGradeQuery reportTemplateGradeQuery = new ReportTemplateGradeQuery();
        reportTemplateGradeQuery.setBusinessId(reportTemplateSnapshot.getId());
        reportTemplateGradeQuery.setBusinessType(ReportDataBusinessTypeEnum.TEMPLATE_SNAPSHOT.getCode());
        List<ReportTemplateGrade> reportTemplateGradeList = reportTemplateGradeManager.getListByCondition(reportTemplateGradeQuery);

        // 年级id
        Set<String> gradeIdSet = reportTemplateGradeList.stream().filter(d -> Objects.equals(d.getLevel(), ReportTemplateGradeLevelTypeEnum.GRADE.getCode())).map(ReportTemplateGrade::getGradeId).collect(Collectors.toSet());
        // 班级id
        Set<String> classIdSet = reportTemplateGradeList.stream().filter(d -> Objects.equals(d.getLevel(), ReportTemplateGradeLevelTypeEnum.CLASS.getCode())).map(ReportTemplateGrade::getClassId).collect(Collectors.toSet());

        // 查询匹配到的班级(历史学期和本学期)
        List<EduClassInfoVO> eduClassInfoVOList = this.querySchoolClass(reportTemplateSnapshot);
        // 查询匹配到的学生(历史学期和本学期)(年级)
        Map<Long, List<StudentInfo1VO>> studentInfo1VOGradeMap = this.queryGradeStudent(reportTemplateSnapshot, gradeIdSet);
        //查询匹配到的学生(班级)
        Map<Long, List<StudentInfo1VO>> studentInfo1VOClassMap = this.queryClassStudent(reportTemplateSnapshot, classIdSet);
        // 生成报告单、报告单学生数据
        ReportData reportData = this.getReportDataAndSetReportDataStudent(reportTemplateSnapshot, reportTemplateGradeList, eduClassInfoVOList, studentInfo1VOGradeMap, studentInfo1VOClassMap);

        List<ReportDataStudentParent> reportDataStudentParents = ReGenerateHolder.getReportDataStudentParents();
//        List<ReportStudentPushRecord> reportStudentPushRecords = ReGenerateHolder.getReportStudentPushRecords();
        List<ReportDataStudent> reportDataStudents = ReGenerateHolder.getNewReportDataStudents();
        Assert.notEmpty(reportDataStudents, () -> new BizException("报告单下无学生数据，无需重新生成"));
        // 入库
        // 保存报告单数据
        reportDataManager.save(reportData);
        log.info("【报告单-重新生成】，保存新报告单，新报告单id：{},学生数量：{}", reportData.getId(), reportDataStudents);
        // 保存报告单学生数据
        if (!CollectionUtils.isEmpty(reportDataStudents)) {
            reportDataStudentManager.saveBatch(reportDataStudents);
        }
        // reportDataStudentParents 需要根据reportDataStudents数据情况设置上reportDataStudentId 先把reportDataStudents转换成map key为报告单id和学生 value是id
        Map<String, Long> reportDataStudentIdMap = new HashMap<>();
        reportDataStudents.forEach(d -> {
            String key = Convert.toStr(d.getReportDataId()) + "_" + d.getStudentId();
            reportDataStudentIdMap.put(key, d.getId());
        });
        reportDataStudentParents.forEach(reportDataStudentParent -> {
            String key = Convert.toStr(reportDataStudentParent.getReportDataId()) + "_" + reportDataStudentParent.getStudentId();
            reportDataStudentParent.setReportDataStudentId(reportDataStudentIdMap.get(key));
        });
        // 保存报告单学生家长数据
        if (!CollectionUtils.isEmpty(reportDataStudentParents)) {
            reportDataStudentParentManager.saveBatch(reportDataStudentParents);
        }


        // 保存报告单学生家长推送数据
        /*if (!CollectionUtils.isEmpty(reportStudentPushRecords)) {
            // 保存推送记录前
            // 修改学生姓名和学号 避免姓名和学号发送变化 影响Hai家校再次推送 后续如果重新优化为根据学生id推送 此段逻辑可同时调整
            // 看是否有新增的学生 也加到records里面
//            this.handleChangeStudent(reportDataStudents, reportStudentPushRecords);

            reportStudentPushRecordManager.saveBatch(reportStudentPushRecords);
        }*/

        GenerateReportMqMessageDTO message = new GenerateReportMqMessageDTO();
        message.setReportDataId(Convert.toStr(reportData.getId()));
        message.setClassAlbumConfigUpdateFlag(generateReportDataDTO.getClassAlbumConfigUpdateFlag());
        message.setTriggerType(generateReportDataDTO.getTriggerType());
        //发送消息到rocketmq
        sendMqTask(message);

        ReGenerateHolder.setNewReportDataId(reportData.getId());
    }

    private Map<Long, List<StudentInfo1VO>> queryClassStudent(ReportTemplateSnapshot reportTemplateSnapshot, Set<String> classIdSet) {
        Map<Long, List<StudentInfo1VO>> result = new HashMap<>(16);
        if (CollectionUtils.isEmpty(classIdSet)) {
            return result;
        }
        // 报告单学年（可能是历史学年）
        ReportData oldReportData = ReGenerateHolder.getOldReportData();
        String schoolYear = oldReportData.getSchoolYear();
        Long schoolId = Long.valueOf(reportTemplateSnapshot.getSchoolId());
        Long campusId = Long.valueOf(reportTemplateSnapshot.getCampusId());
        boolean isHistoryYear = ReGenerateHolder.getIsHistoryYear();

        for (String classId : classIdSet) {
            List<StudentInfo1VO> studentInfo1VOList;
            if (isHistoryYear) {
                List<SassStudentVO> sassStudentVOS =
                        saasHistoryStudentCacheManager
                                .listAllStudentByClassId(
                                        schoolId,
                                        schoolYear,
                                        campusId,
                                        Long.valueOf(classId),
                                        false,
                                        "0",
                                        "0",
                                        null);

                studentInfo1VOList = this.convertStudents(sassStudentVOS);
            } else {
                StudentPageQueryDTO studentPageQueryDTO = new StudentPageQueryDTO();
                studentPageQueryDTO.setSchoolId(schoolId);
                studentPageQueryDTO.setCampusId(campusId);
                studentPageQueryDTO.setCampusSectionId(Long.valueOf(reportTemplateSnapshot.getCampusSectionId()));
                studentPageQueryDTO.setWithClass(Boolean.TRUE);
                studentPageQueryDTO.setClassTypes(Collections.singletonList("0"));
                studentPageQueryDTO.setClassId(Long.valueOf(classId));
                studentInfo1VOList = saasStudentManager.queryStudentPage(studentPageQueryDTO);

                //处理年级、班级属性(一个学生可能存在多个班级)
                if (!CollectionUtils.isEmpty(studentInfo1VOList)) {
                    for (StudentInfo1VO vo : studentInfo1VOList) {
                        if (CollectionUtils.isEmpty(vo.getClassList())) {
                            continue;
                        }
                        List<StudentInfo1VO.ClassInfo> classList = vo.getClassList().stream().filter(element ->
                                SaasClassTypeEnum.XINGZHENG.getCode().equals(element.getClassType())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(classList)) {
                            vo.setClassId(classList.get(0).getClassId());
                            vo.setClassName(classList.get(0).getClassName());
                            vo.setGradeId(classList.get(0).getGradeId());
                            vo.setGradeCode(classList.get(0).getGradeCode());
                            vo.setGradeName(classList.get(0).getGradeName());
                        }
                    }
                }
            }

            result.put(Long.valueOf(classId), studentInfo1VOList);
        }
        return result;
    }
/*
    private void handleChangeStudent(List<ReportDataStudent> reportDataStudents, List<ReportStudentPushRecord> reportStudentPushRecords) {
        ReportStudentPushRecord oldReportStudentPushRecord = reportStudentPushRecords.get(0);
        // reportDataStudents
    Map<String, ReportDataStudent> stringReportDataStudentMap = CollStreamUtil.toIdentityMap(reportDataStudents, ReportDataStudent::getStudentId);
    reportStudentPushRecords.forEach(d -> {
        ReportDataStudent reportDataStudent = stringReportDataStudentMap.get(d.getStudentId());
        if (reportDataStudent != null) {
            d.setStudentName(reportDataStudent.getStudentName());
            d.setStudentNo(reportDataStudent.getStudentNo());
            // 重新推送需要origin_data字段
            // 修改d里面的origin_data，origin_data字段是一个json数据，先判断里面有没有studentName和studentCode，如果有解析并替换里面的studentName和studentCode
            if (StrUtil.isNotBlank(d.getOriginData())) {
                JSONObject jsonObject = JSONObject.parseObject(d.getOriginData());
                if (jsonObject.containsKey("studentName")) {
                    jsonObject.put("studentName", reportDataStudent.getStudentName());
                }
                if (jsonObject.containsKey("studentCode")) {
                    jsonObject.put("studentCode", reportDataStudent.getStudentNo());
                }
                d.setOriginData(jsonObject.toJSONString());
            }
        }
    });
    // 处理新增的学生
    List<String> oldStudentIds = CollStreamUtil.toList(reportStudentPushRecords, ReportStudentPushRecord::getStudentId);
    for (ReportDataStudent d : reportDataStudents){
        if (oldStudentIds.contains(d.getStudentId())){
            continue;
        }
        log.info("【报告单-重新生成】，report_student_push_record表检测到有新加学生，学生id：{},学生姓名：{}", d.getStudentId(), d.getStudentName());
        ReportStudentPushRecord reportStudentPushRecord = new ReportStudentPushRecord();
        BeanUtil.copyProperties(oldReportStudentPushRecord, reportStudentPushRecord);
        reportStudentPushRecord.setId(SnowFlakeIdUtil.nextId());
        reportStudentPushRecord.setStudentNo(d.getStudentNo());
        reportStudentPushRecord.setStudentName(d.getStudentName());
        reportStudentPushRecord.setStudentId(d.getStudentId());
        reportStudentPushRecord.setParentId("0");
        reportStudentPushRecord.setParentName("0");
        reportStudentPushRecord.setMobile("0");
        reportStudentPushRecord.setOriginData(this.handleOriginData(oldReportStudentPushRecord.getOriginData(), d.getStudentId(), d.getStudentNo(), d.getStudentName()));
        reportStudentPushRecord.setPushStatus(false);

        reportStudentPushRecords.add(reportStudentPushRecord);
    }
    }*/

    private String handleOriginData(String originData, String studentId, String studentNo, String studentName) {
        if (StrUtil.isBlank(originData)){
            return StrUtil.EMPTY;
        }
        JSONObject jsonObject = JSONObject.parseObject(originData);
        if (jsonObject.containsKey("studentId")) {
            jsonObject.put("studentId", studentId);
        }
        if (jsonObject.containsKey("studentName")) {
            jsonObject.put("studentName", studentName);
        }
        if (jsonObject.containsKey("studentCode")) {
            jsonObject.put("studentCode", studentNo);
        }
       return jsonObject.toJSONString();
    }

    public ReportData getReportDataAndSetReportDataStudent(ReportTemplateSnapshot reportTemplateSnapshot,
                                                           List<ReportTemplateGrade> reportTemplateGradeList,
                                                           List<EduClassInfoVO> eduClassInfoVOList,
                                                           Map<Long, List<StudentInfo1VO>> studentInfo1VOGradeMap,
                                                           Map<Long, List<StudentInfo1VO>> studentInfo1VOClassMap) {
        //生成报告单数据
        ReportData reportData = new ReportData();
        Date currentTime = new Date();
        //定时任务执行默认用户为模板创建人
        String staffId =  WebUtil.getStaffId();
        Long newReportDataId = SnowFlakeIdUtil.nextId();
        ReportData oldReportData = ReGenerateHolder.getOldReportData();
        List<ReportDataStudentParent> reportDataStudentParents = ReGenerateHolder.getReportDataStudentParents();
//        List<ReportStudentPushRecord> reportStudentPushRecords = ReGenerateHolder.getReportStudentPushRecords();
        List<ReportDataStudent> reportDataStudentList = new ArrayList<>(1024);

        reportData.setId(newReportDataId);
        reportData.setTenantId(reportTemplateSnapshot.getTenantId());
        reportData.setSchoolId(reportTemplateSnapshot.getSchoolId());
        reportData.setCampusId(reportTemplateSnapshot.getCampusId());
        reportData.setCampusSectionId(reportTemplateSnapshot.getCampusSectionId());
        reportData.setCampusSectionCode(reportTemplateSnapshot.getCampusSectionCode());
        reportData.setCampusSectionName(SectionInfoEnum.getNameByCode(reportTemplateSnapshot.getCampusSectionCode()));
        reportData.setSchoolYear(oldReportData.getSchoolYear());
        reportData.setTermName(oldReportData.getTermName());
        reportData.setReportDataNumber(oldReportData.getReportDataNumber());
        reportData.setReportDataName(reportTemplateSnapshot.getReportTemplateSnapshotName());
        reportData.setReportTemplateSnapshotId(reportTemplateSnapshot.getId());
        reportData.setGenerateType(ReportDataGenerateTypeEnum.AGAIN.getCode());
        reportData.setGenerateTime(currentTime);
//        reportData.setAllStudentCount(0);
        // 应该使用已查看的学生数量/最新的总数量
//        reportData.setParentViewRate(oldReportData.getParentViewRate());
//        reportData.setParentUnViewCount(oldReportData.getParentUnViewCount());
        reportData.setPushFlag(oldReportData.getPushFlag());
        reportData.setCreateBy(staffId);
        reportData.setCreateTime(currentTime);
        reportData.setUpdateBy(staffId);
        reportData.setUpdateTime(currentTime);
        reportData.setDeleted(Boolean.FALSE);

        if (CollectionUtils.isEmpty(reportTemplateGradeList) || CollectionUtils.isEmpty(eduClassInfoVOList)) {
            return reportData;
        }

        //每个年级下有多少班级
        Map<Long, List<EduClassInfoVO>> eduClassInfoVOGradeIdMap = eduClassInfoVOList.stream().collect(Collectors.groupingBy(EduClassInfoVO::getGradeId));
        for (ReportTemplateGrade element : reportTemplateGradeList) {
            //年级下的班级信息
            List<EduClassInfoVO> classInfoVOList = eduClassInfoVOGradeIdMap.get(Long.valueOf(element.getGradeId()));
            //年级下的学生信息
            List<StudentInfo1VO> studentInfo1VOList = null;
            if (Objects.equals(element.getLevel(), ReportTemplateGradeLevelTypeEnum.CLASS.getCode())) {
                studentInfo1VOList = studentInfo1VOClassMap.get(Long.valueOf(element.getClassId()));
            } else if (Objects.equals(element.getLevel(), ReportTemplateGradeLevelTypeEnum.GRADE.getCode())){
                studentInfo1VOList = studentInfo1VOGradeMap.get(Long.valueOf(element.getGradeId()));
            }
            if (CollectionUtils.isEmpty(classInfoVOList) || CollectionUtils.isEmpty(studentInfo1VOList)) {
                continue;
            }

            Map<Long, List<StudentInfo1VO>> studentClassMap = studentInfo1VOList.stream().collect(
                    Collectors.groupingBy(StudentInfo1VO::getClassId));
            //班级下的学生信息
            for (EduClassInfoVO eduClassInfoVO : classInfoVOList) {
                List<StudentInfo1VO> studentInfo1VOS = studentClassMap.get(eduClassInfoVO.getId());
                if (CollectionUtils.isEmpty(studentInfo1VOS)) {
                    continue;
                }
                reportDataStudentList.addAll(this.getReportDataStudentList(reportData, eduClassInfoVO, studentInfo1VOS));
            }

        }
        List<String> newStudentIds = CollStreamUtil.toList(reportDataStudentList, ReportDataStudent::getStudentId);
        log.info("【报告单-重新生成】，原报告单家长评价/反馈条数：{}", reportDataStudentParents.size());
        // 过滤家长查看数据
        reportDataStudentParents = reportDataStudentParents.stream().filter(reportDataStudentParent -> newStudentIds.contains(reportDataStudentParent.getStudentId())).collect(Collectors.toList());
        // 过滤推送数据
//        reportStudentPushRecords = reportStudentPushRecords.stream().filter(reportStudentPushRecord -> newStudentIds.contains(reportStudentPushRecord.getStudentId())).collect(Collectors.toList());
        log.info("【报告单-重新生成】，过滤后报告单家长评价/反馈条数：{}", reportDataStudentParents.size());
        // 替换reportId
        reportDataStudentParents.forEach(d -> {
            d.setId(SnowFlakeIdUtil.nextId());
            d.setReportDataId(newReportDataId);
        });
        /*reportStudentPushRecords.forEach(d -> {
            d.setId(SnowFlakeIdUtil.nextId());
            d.setReportDataId(newReportDataId);
        });*/
        // 复制家长查看情况
        List<ReportDataStudent> oldReportDataStudents = ReGenerateHolder.getOldReportDataStudents();
        if (!CollectionUtils.isEmpty(oldReportDataStudents)) {
            reportDataStudentList.forEach(d -> {
                ReportDataStudent oldReportDataStudent = oldReportDataStudents.stream().filter(e -> e.getStudentId().equals(d.getStudentId())).findFirst().orElse(null);
                if (oldReportDataStudent != null) {
                    d.setParentViewFlag(oldReportDataStudent.getParentViewFlag());
                }
            });
        }
        // 回写学生数量
        if (!CollectionUtils.isEmpty(reportDataStudentList)) {
            reportData.setAllStudentCount(reportDataStudentList.size());
            // 未查看家长数=学生总数-上一次报告单中已查看的数量
            reportData.setParentUnViewCount(reportData.getAllStudentCount() - reportDataStudentParents.size());
            // 查看率 = 已查看数量/学生总数
            reportData.setParentViewRate(NumberUtil.div(Convert.toInt(reportDataStudentParents.size()), reportData.getAllStudentCount(), 2).doubleValue());
        }
        ReGenerateHolder.setReportDataStudentParents(reportDataStudentParents);
//        ReGenerateHolder.setReportStudentPushRecords(reportStudentPushRecords);
        ReGenerateHolder.setNewReportDataStudents(reportDataStudentList);

        return reportData;
    }
    private List<ReportDataStudent> getReportDataStudentList(ReportData reportData,
                                                             EduClassInfoVO eduClassInfoVO,
                                                             List<StudentInfo1VO> studentInfo1VOs) {
        List<ReportDataStudent> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(studentInfo1VOs)) {
            return result;
        }
        for (StudentInfo1VO element : studentInfo1VOs) {
            ReportDataStudent reportDataStudent = new ReportDataStudent();
            reportDataStudent.setId(SnowFlakeIdUtil.nextId());
            reportDataStudent.setTenantId(reportData.getTenantId());
            reportDataStudent.setSchoolId(reportData.getSchoolId());
            reportDataStudent.setCampusId(reportData.getCampusId());
            reportDataStudent.setCampusSectionId(reportData.getCampusSectionId());
            reportDataStudent.setCampusSectionCode(reportData.getCampusSectionCode());
            reportDataStudent.setCampusSectionName(reportData.getCampusSectionName());
            reportDataStudent.setGradeId(String.valueOf(eduClassInfoVO.getGradeId()));
            reportDataStudent.setGradeCode(eduClassInfoVO.getGradeCode());
            reportDataStudent.setGradeName(eduClassInfoVO.getGradeName());
            reportDataStudent.setClassId(String.valueOf(eduClassInfoVO.getId()));
            reportDataStudent.setClassName(eduClassInfoVO.getClassName());
            reportDataStudent.setHeaderMasterName(eduClassInfoVO.getHeadMasterName());
            reportDataStudent.setReportDataId(reportData.getId());
            reportDataStudent.setStudentId(String.valueOf(element.getId()));
            reportDataStudent.setStudentName(element.getStudentName());
            reportDataStudent.setStudentNo(element.getStudentNo());
            reportDataStudent.setStatus(1);
            reportDataStudent.setParentViewFlag(0);
            reportDataStudent.setCreateBy(reportData.getCreateBy());
            reportDataStudent.setCreateTime(reportData.getCreateTime());
            reportDataStudent.setUpdateBy(reportData.getCreateBy());
            reportDataStudent.setUpdateTime(reportData.getCreateTime());
            reportDataStudent.setDeleted(Boolean.FALSE);

            result.add(reportDataStudent);
        }
        return result;
    }
    private List<EduClassInfoVO> querySchoolClass(ReportTemplateSnapshot reportTemplateSnapshot) {
        boolean isHistoryTerm = ReGenerateHolder.getIsHistoryYear();
        // 报告单学年（可能是历史学年）
        ReportData oldReportData = ReGenerateHolder.getOldReportData();
        String schoolYear = oldReportData.getSchoolYear();
        String schoolId = oldReportData.getSchoolId();
        String campusId = oldReportData.getCampusId();
        String campusSectionCode = oldReportData.getCampusSectionCode();
        List<EduClassInfoVO> eduClassInfoVOS = new ArrayList<>(1024);
        // 历史学期
        if (isHistoryTerm) {
            List<HistoryClassVO> historyClassVOS = saasHistoryClassCacheManager.listHistoryClassNormal(Convert.toLong(schoolId), Convert.toLong(campusId), campusSectionCode, schoolYear);
            eduClassInfoVOS = this.convertClass(historyClassVOS);
        } else {
            EduClassQueryDTO eduClassQueryDTO = new EduClassQueryDTO();
            eduClassQueryDTO.setSchoolId(Long.valueOf(reportTemplateSnapshot.getSchoolId()));
            eduClassQueryDTO.setCampusId(Long.valueOf(reportTemplateSnapshot.getCampusId()));
            eduClassQueryDTO.setCampusSectionId(Long.valueOf(reportTemplateSnapshot.getCampusSectionId()));
            eduClassQueryDTO.setClassTypes(Collections.singletonList("0"));
            eduClassInfoVOS = basicInfoRemote.queryClassInfoList(eduClassQueryDTO);
        }
        return eduClassInfoVOS;
    }

    private List<EduClassInfoVO> convertClass(List<HistoryClassVO> historyClassVOS) {
        List<EduClassInfoVO> eduClassInfoVOS = new ArrayList<>(32);

        historyClassVOS.forEach(element -> {
            EduClassInfoVO eduClassInfoVO = new EduClassInfoVO();
            eduClassInfoVO.setId(element.getClassId());
            eduClassInfoVO.setClassName(element.getClassName());
            eduClassInfoVO.setGradeId(element.getGradeId());
            eduClassInfoVO.setGradeCode(element.getGradeCode());
            eduClassInfoVO.setGradeName(element.getGradeName());
            eduClassInfoVO.setHeadMasterName(element.getStaffName());
            eduClassInfoVOS.add(eduClassInfoVO);
        });

        return eduClassInfoVOS;
    }

    private Map<Long, List<StudentInfo1VO>> queryGradeStudent(ReportTemplateSnapshot reportTemplateSnapshot,
                                                              Set<String> gradeIds) {
        Map<Long, List<StudentInfo1VO>> result = new HashMap<>(16);
        if (CollectionUtils.isEmpty(gradeIds)) {
            return result;
        }
        // 报告单学年（可能是历史学年）
        ReportData oldReportData = ReGenerateHolder.getOldReportData();
        String schoolYear = oldReportData.getSchoolYear();
        String schoolId = oldReportData.getSchoolId();
        boolean isHistoryTerm = ReGenerateHolder.getIsHistoryYear();

        for (String gradeId : gradeIds) {
            List<StudentInfo1VO> studentInfo1VOList;
            if (isHistoryTerm) {
                List<SassStudentVO> sassStudentVOS =
                        saasHistoryStudentCacheManager
                                .listAllStudentNormalByGradeId(
                                        Convert.toLong(schoolId),
                                        schoolYear,
                                        null, Convert.toLong(gradeId), !isHistoryTerm);

                studentInfo1VOList = this.convertStudents(sassStudentVOS);
            } else {
                StudentPageQueryDTO studentPageQueryDTO = new StudentPageQueryDTO();
                studentPageQueryDTO.setSchoolId(Long.valueOf(reportTemplateSnapshot.getSchoolId()));
                studentPageQueryDTO.setCampusId(Long.valueOf(reportTemplateSnapshot.getCampusId()));
                studentPageQueryDTO.setCampusSectionId(Long.valueOf(reportTemplateSnapshot.getCampusSectionId()));
                studentPageQueryDTO.setWithClass(Boolean.TRUE);
                studentPageQueryDTO.setClassTypes(Collections.singletonList("0"));
                studentPageQueryDTO.setGradeId(Long.valueOf(gradeId));
                studentInfo1VOList = saasStudentManager.queryStudentPage(studentPageQueryDTO);

                //处理年级、班级属性(一个学生可能存在多个班级)
                if (!CollectionUtils.isEmpty(studentInfo1VOList)) {
                    for (StudentInfo1VO vo : studentInfo1VOList) {
                        if (CollectionUtils.isEmpty(vo.getClassList())) {
                            continue;
                        }
                        List<StudentInfo1VO.ClassInfo> classList = vo.getClassList().stream().filter(element ->
                                SaasClassTypeEnum.XINGZHENG.getCode().equals(element.getClassType())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(classList)) {
                            vo.setClassId(classList.get(0).getClassId());
                            vo.setClassName(classList.get(0).getClassName());
                            vo.setGradeId(classList.get(0).getGradeId());
                            vo.setGradeCode(classList.get(0).getGradeCode());
                            vo.setGradeName(classList.get(0).getGradeName());
                        }
                    }
                }
            }

            result.put(Long.valueOf(gradeId), studentInfo1VOList);
        }
        return result;
    }

    private List<StudentInfo1VO> convertStudents(List<SassStudentVO> sassStudentVOS) {
        if (CollectionUtils.isEmpty(sassStudentVOS)) {
            return Collections.emptyList();
        }
        List<StudentInfo1VO> result = new ArrayList<>(1024);
        for (SassStudentVO sassStudentVO : sassStudentVOS) {
            StudentInfo1VO studentInfo1VO = new StudentInfo1VO();
            studentInfo1VO.setId(sassStudentVO.getStudentId());
            studentInfo1VO.setStudentName(sassStudentVO.getStudentName());
            studentInfo1VO.setStudentNo(sassStudentVO.getStudentNo());
            studentInfo1VO.setSex(sassStudentVO.getSex());
            studentInfo1VO.setClassId(sassStudentVO.getClassId());
            studentInfo1VO.setClassName(sassStudentVO.getClassName());
            studentInfo1VO.setGradeId(sassStudentVO.getGradeId());
            studentInfo1VO.setGradeCode(sassStudentVO.getGradeCode());
            studentInfo1VO.setGradeName(sassStudentVO.getGradeName());
            result.add(studentInfo1VO);
        }
        return result;
    }

    private void sendMqTask(GenerateReportMqMessageDTO message) {
        rocketMQTemplate.asyncSend(reportDataTopic, JSONUtil.toJsonStr(message), new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("[报告单数据id发送成功]-[request:{}]", JSONUtil.toJsonStr(message));
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("[报告单数据id发送失败] 请求数据:{}", JSONUtil.toJsonStr(message), throwable);
            }
        });
    }
}
