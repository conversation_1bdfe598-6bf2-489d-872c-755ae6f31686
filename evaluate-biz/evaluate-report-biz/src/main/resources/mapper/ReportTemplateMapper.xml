<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.ReportTemplateMapper">
    <resultMap id="BaseResultMap" type="com.hailiang.model.report.entity.ReportTemplate">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="school_id" jdbcType="VARCHAR" property="schoolId"/>
        <result column="campus_id" jdbcType="VARCHAR" property="campusId"/>
        <result column="campus_section_id" jdbcType="VARCHAR" property="campusSectionId"/>
        <result column="campus_section_code" jdbcType="VARCHAR" property="campusSectionCode"/>
        <result column="report_template_name" jdbcType="VARCHAR" property="reportTemplateName"/>
        <result column="report_template_type" jdbcType="TINYINT" property="reportTemplateType"/>
        <result column="time_type" jdbcType="TINYINT" property="timeType"/>
        <result column="month_number" jdbcType="INTEGER" property="monthNumber"/>
        <result column="day_number" jdbcType="INTEGER" property="dayNumber"/>
        <result column="background_url" jdbcType="VARCHAR" property="backgroundUrl"/>
        <result column="font_color" jdbcType="VARCHAR" property="fontColor"/>
        <result column="enable_flag" jdbcType="INTEGER" property="enableFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, tenant_id, school_id, campus_id, campus_section_id, campus_section_code, report_template_name,
        report_template_type, time_type, month_number, day_number, background_url, font_color,enable_flag,
        create_by, create_time, update_by, update_time, deleted
    </sql>

    <select id="getListByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM report_template
        <where>
            <include refid="where"/>
        </where>
        <choose>
            <when test="null != row.sortCriteria and '' != row.sortCriteria">
                ORDER BY ${row.sortCriteria}
            </when>
            <otherwise>
                ORDER BY create_time DESC
            </otherwise>
        </choose>
        <if test="null != row.offset and null != row.limit">
            LIMIT #{row.offset}, #{row.limit}
        </if>
    </select>

    <select id="getRowByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM report_template
        <where>
            <include refid="where"/>
        </where>
        LIMIT 1
    </select>

    <sql id="where">
        <if test="null != row.id">
            AND id = #{row.id}
        </if>
        <if test="null != row.tenantId and row.tenantId != ''">
            AND tenant_id = #{row.tenantId}
        </if>
        <if test="null != row.schoolId and row.schoolId != ''">
            AND school_id = #{row.schoolId}
        </if>
        <if test="null != row.campusId and row.campusId != ''">
            AND campus_id = #{row.campusId}
        </if>
        <if test="null != row.campusSectionId and row.campusSectionId != ''">
            AND campus_section_id = #{row.campusSectionId}
        </if>
        <if test="null != row.campusSectionCode and row.campusSectionCode != ''">
            AND campus_section_code = #{row.campusSectionCode}
        </if>
        <if test="null != row.reportTemplateType">
            AND report_template_type = #{row.reportTemplateType}
        </if>
        <if test="null != row.timeType">
            AND time_type = #{row.timeType}
        </if>
        <if test="null != row.monthNumber">
            AND month_number = #{row.monthNumber}
        </if>
        <if test="null != row.dayNumber">
            AND day_number = #{row.dayNumber}
        </if>
        <if test="null != row.enableFlag">
            AND enable_flag = #{row.enableFlag}
        </if>
        <if test="null != row.deleted">
            AND deleted = #{row.deleted}
        </if>

    </sql>

</mapper>