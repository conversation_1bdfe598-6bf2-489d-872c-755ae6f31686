<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hailiang.mapper.ReportSubjectScoreMapper">

    <resultMap id="BaseResultMap" type="com.hailiang.model.report.entity.ExamSubjectScorePO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="examId" column="exam_id" jdbcType="BIGINT"/>
            <result property="schoolId" column="school_id" jdbcType="VARCHAR"/>
            <result property="schoolName" column="school_name" jdbcType="VARCHAR"/>
            <result property="campusId" column="campus_id" jdbcType="VARCHAR"/>
            <result property="sectionCode" column="section_code" jdbcType="VARCHAR"/>
            <result property="campusName" column="campus_name" jdbcType="VARCHAR"/>
            <result property="gradeCode" column="grade_code" jdbcType="VARCHAR"/>
            <result property="gradeId" column="grade_id" jdbcType="VARCHAR"/>
            <result property="gradeName" column="grade_name" jdbcType="VARCHAR"/>
            <result property="classId" column="class_id" jdbcType="VARCHAR"/>
            <result property="className" column="class_name" jdbcType="VARCHAR"/>
            <result property="stuId" column="stu_id" jdbcType="VARCHAR"/>
            <result property="stuSno" column="stu_sno" jdbcType="VARCHAR"/>
            <result property="stuName" column="stu_name" jdbcType="VARCHAR"/>
            <result property="subjectCode" column="subject_code" jdbcType="VARCHAR"/>
            <result property="subjectName" column="subject_name" jdbcType="VARCHAR"/>
            <result property="examType" column="exam_type" jdbcType="TINYINT"/>
            <result property="startDate" column="start_date" jdbcType="DATE"/>
            <result property="endDate" column="end_date" jdbcType="DATE"/>
            <result property="isNormal" column="is_normal" jdbcType="TINYINT"/>
            <result property="subjectScore" column="subject_score" jdbcType="DECIMAL"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,exam_id,
        school_id,school_name,campus_id,
        section_code,campus_name,grade_code,
        grade_id,grade_name,class_id,
        class_name,stu_id,stu_sno,
        stu_name,subject_code,subject_name,
        exam_type,start_date,end_date,
        is_normal,subject_score,create_by,
        create_time,update_by,update_time,
        deleted
    </sql>
    <select id="listByCondition" resultType="com.hailiang.model.response.evaluationdim.ExamDetailInfoResponse">
        SELECT
        s.section_code,
        s.subject_code,
        s.subject_name,
        s.exam_type,
        s.exam_id,
        i.exam_name,
        i.id
        FROM
        report_subject_score s
        right JOIN report_exam_info i ON i.source_id = s.exam_id
        AND i.issue_status = 1
        WHERE
        s.deleted = 0
        <if test="tenantId != null and tenantId !=''">
            AND s.tenant_id = #{tenantId}
        </if>
        <if test="schoolId != null and schoolId !=''">
            AND s.school_id = #{schoolId}
        </if>
        <if test="campusId != null and campusId !=''">
            AND s.campus_id = #{campusId}
        </if>
        and s.section_code = #{sectionCode}
        <if test="subjectCodes != null ">
            AND s.subject_code IN
            <foreach collection="subjectCodes" item="subjectCode" open="(" separator="," close=")">
                #{subjectCode}
            </foreach>
        </if>
        GROUP BY
        s.exam_id,
        s.subject_code,
        s.exam_type
    </select>
</mapper>
