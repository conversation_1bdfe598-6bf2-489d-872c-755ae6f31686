package com.hailiang.internal.model.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 点评项详情返回 VO
 *
 * @Description: 点评项详情返回 VO
 * @Author: JJl
 * @Date: Created in 2024-07-02
 * @Version: 2.0.0
 */
@Data
public class PlanCommentListsResponse {
    /**
     * 点评项ID
     */
    private String planCommentId;
    /**
     * 积分板ID
     */
    private Long planId;
    /**
     * 点评内容
     */
    private String content;
    /**
     * 点评分值
     */
    private BigDecimal score;
    /**
     * 应用级别 枚举： general|普通 school|校级
     */
    private String applyLevel;
    /**
     * 五育信息  枚举：  0|无 1|德 2|智 3|体 4|美 5|劳
     */
    private Integer moduleCode;
}
