package com.hailiang.saas;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.saas.constant.SaasUrlConstants;
import com.hailiang.saas.helper.SaasRequestHelper;
import com.hailiang.saas.model.dto.SchoolYearClassReqDTO;
import com.hailiang.saas.model.vo.history.HistoryClassVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * saas历史信息学生维度数据的缓存(缓存包装类)
 * 远程接口
 */
@Slf4j
@Service
public class SaasHistoryClassCacheManager {
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private SaasRequestHelper saasRequestHelper;
    /**
     *  saas远程调用查询学校某一学年（时刻）的班级列表
     *
     * @param schoolId
     * @param campusId
     * @param sectionCode
     * @param schoolYear
     * @return
     */
    public List<HistoryClassVO> listHistoryClassNormal(Long schoolId, Long campusId, String sectionCode , String schoolYear) {
        String key = StrUtil.join(StrPool.COLON, RedisKeyConstants.SAAS_RESULT_HISTORY_CLASS, schoolId, campusId, schoolYear, sectionCode);
        log.info("准备缓存获取数据>>>>>>>>>>>查询某学校某一学年的班级列表>>>>>>>>>>>>>>>，key：{}", key);
        SchoolYearClassReqDTO schoolYearClassReqDTO = new SchoolYearClassReqDTO();
        schoolYearClassReqDTO.setSchoolId(schoolId);
        schoolYearClassReqDTO.setCampusId(campusId);
        schoolYearClassReqDTO.setSchoolYear(schoolYear);
        schoolYearClassReqDTO.setSectionCode(sectionCode);

        List<HistoryClassVO> classList = redisUtil.getOrAdd(key, () -> this.listClass(schoolYearClassReqDTO), CacheConstants.ONE_MINUTE);
        if (CollUtil.isEmpty(classList)) {
            log.info("缓存获取数据>>>>>>>>>>>查询某学校某一学年的班级列表>>>>>>>>>>>>>>>，key：{}，数据为空", key);
            return Collections.emptyList();
        }
        // 过滤classType为0的数据
        return classList.stream().filter(d -> Objects.nonNull(d.getClassType()) && d.getClassType().equals("0")).collect(Collectors.toList());
    }
    /**
     *  saas远程调用查询学校某一学年（时刻）的班级列表
     *  此返回值包括非行政班，禁止直接暴露出去调用
     *
     * @param schoolYearClassReqDTO
     * @return
     */
    private List<HistoryClassVO> listClass(SchoolYearClassReqDTO schoolYearClassReqDTO) {
        JSONArray jsonArray = saasRequestHelper.post(SaasUrlConstants.SAAS_HISTORY_CLASS, JSONUtil.toJsonStr(schoolYearClassReqDTO)).getJSONArray("data");
        List<HistoryClassVO> historyClassVOS = new ArrayList<>();
        if (Objects.nonNull(jsonArray)) {
            historyClassVOS = jsonArray.toList(HistoryClassVO.class);
        }
        return historyClassVOS;
    }

}
