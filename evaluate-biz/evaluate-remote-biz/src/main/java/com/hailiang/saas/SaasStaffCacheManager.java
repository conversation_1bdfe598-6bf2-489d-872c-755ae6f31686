package com.hailiang.saas;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.hailiang.common.cache.constant.CacheConstants;
import com.hailiang.common.cache.constant.RedisKeyConstants;
import com.hailiang.common.cache.util.RedisUtil;
import com.hailiang.saas.model.dto.StudentDTO;
import com.hailiang.saas.model.vo.StudentInfoVO;
import com.hailiang.saas.model.vo.TeacherClassRelationVO;
import com.hailiang.saas.model.vo.history.SassStudentVO;
import com.hailiang.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * saas基础数据(缓存包装类)
 * 远程接口
 */
@Slf4j
@Service
public class SaasStaffCacheManager {
    @Autowired
    private SaasStaffManager saasStaffManager;
    @Resource
    private SaasHistoryTeacherRelationCacheManager saasHistoryTeacherRelationCacheManager;
    @Resource
    private SaasHistoryStudentCacheManager saasHistoryStudentCacheManager;
    @Resource
    private SaasStudentManager saasStudentManager;
    @Autowired
    private RedisUtil redisUtil;
    /**
     * 根据staffId获取学生id的集合
     */
    public List<Long> queryStudentByStaffId(Long schoolId, Long staffId) {
        String key = StrUtil.join(StrPool.UNDERLINE, StrUtil.join(StrPool.UNDERLINE, schoolId, staffId));
        log.info("准备缓存获取数据>>>>>>>>>>>>>>>>>>>>>>>>>>，key：{}", key);
        return redisUtil.getOrAdd(RedisKeyConstants.SAAS_QUERY_STUDENT_BY_STAFFID + key, () -> saasStaffManager.queryStudentByStaffId(schoolId, staffId), CacheConstants.HALF_HOUR);
    }

    /**
     * 查询学生信息（缓存1分钟）
     * @return 权限数据
     */
    public List<StudentInfoVO> studentDetailV2(StudentDTO studentByIdQuery){
        String studentIds = JSON.toJSONString(studentByIdQuery.getStudentIds());
        log.info("SaasStaffCacheManager.studentDetailV2 查询用户的数据，学生ID：{}", studentIds);
        return redisUtil.getOrAdd(StrUtil.format(RedisKeyConstants.SAAS_QUERY_STUDENT_BY_STUDENT_ID, studentIds),
                () -> saasStudentManager.studentDetailV2(studentByIdQuery), CacheConstants.FIVE_MINUTE);
    }

    /**
     * 根据staffId获取学生id的集合
     */
    public List<Long> queryStudentByStaffId(String schoolYear, Long campusSectionId) {
        // 当前老师任教过的班级列表
        Set<Long> studentIds = new HashSet<>();
        Map<Long/*staffId*/, TeacherClassRelationVO> longTeacherClassRelationVOMap = saasHistoryTeacherRelationCacheManager
                .listAllTeacherClassIdsRelation(
                        WebUtil.getSchoolIdLong(),
                        schoolYear,
                        WebUtil.getCampusIdLong(),
                        campusSectionId,
                        null,
                        null);

        if (!longTeacherClassRelationVOMap.containsKey(WebUtil.getStaffIdLong())) {
            return new ArrayList<>();
        }

        TeacherClassRelationVO teacherClassRelationVO = longTeacherClassRelationVOMap.get(WebUtil.getStaffIdLong());

        if (teacherClassRelationVO == null || CollUtil.isEmpty(teacherClassRelationVO.getHisClassList())) {
            return new ArrayList<>();
        }

        teacherClassRelationVO.getHisClassList().stream()
                .filter(item -> Objects.equals(item.getHeadTeacherStaffId(), WebUtil.getStaffIdLong()))
                .forEach(item -> {
                    List<SassStudentVO> hisStudentVOS = saasHistoryStudentCacheManager
                            .listHisStudentByGradeId(
                                    item.getSchoolId(),
                                    schoolYear,
                                    item.getCampusId(),
                                    item.getCampusSectionId(),
                                    item.getGradeId(),
                                    item.getClassId());

                    if (CollUtil.isEmpty(hisStudentVOS)) return;
                    studentIds.addAll(hisStudentVOS.parallelStream().map(SassStudentVO::getStudentId).collect(Collectors.toSet()));
                });
        return new ArrayList<>(studentIds);
    }
}
