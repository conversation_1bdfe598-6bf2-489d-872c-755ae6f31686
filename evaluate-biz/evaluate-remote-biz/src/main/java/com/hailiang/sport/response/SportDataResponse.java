package com.hailiang.sport.response;

import lombok.Data;

/**
 * 运动数据分页响应结果（包含分页信息和数据集）
 */
@Data
public class SportDataResponse {
    /**
     * 接口调用是否成功状态（true表示成功）
     */
    private Boolean success;

    /**
     * 系统返回的提示信息（成功时为空或默认值）
     */
    private String message;

    /**
     * 接口调用状态码（如"200"表示成功，"404"表示资源未找到）
     */
    private String code;

    /**
     * 分页数据对象，包含当前页码、总记录数等信息
     */
    private SportDataPage data;
}