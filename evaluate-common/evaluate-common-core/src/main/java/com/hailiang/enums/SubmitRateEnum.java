package com.hailiang.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

/**
 * 1：工作日每天 2：每周 3：每月 4：每年 枚举类
 */
@RequiredArgsConstructor
public enum SubmitRateEnum {


    WORK_DAY(1, "工作日每天"),
    EVERY_WEEK(2, "每周"),
    EVERY_MONTH(3, "每月"),
    EVERY_YEAR(4, "每年"),
    NO_RATE(5, "不限定频次"),
    TERM(6,"一学期一次"),
    ;
    /**
     * 1：工作日每天 2：每周 3：每月 4：每年 5：不限定频次
     */
    private Integer submitType;
    /**
     * 工作日每天、 每周、 每月 、每年、 不限定频次
     */
    private String submitTypeName;

    private static final List<Integer> NEED_REMINDER_LIST;

    static {
        NEED_REMINDER_LIST = Lists.newArrayList(
                WORK_DAY.getSubmitType(),
                EVERY_WEEK.getSubmitType(),
                EVERY_MONTH.getSubmitType(),
                EVERY_YEAR.getSubmitType());
    }


    public static boolean isRemind(Integer submitType) {
        return NEED_REMINDER_LIST.contains(submitType);
    }


    /**
     * 获取当前枚举的开始时间
     */
    public DateTime getBeginTime(DateTime date) {
        switch (this) {
            case WORK_DAY:
                return DateUtil.beginOfDay(date);
            case EVERY_WEEK:
                return DateUtil.beginOfWeek(date);
            case EVERY_MONTH:
                return DateUtil.beginOfMonth(date);
            case EVERY_YEAR:
                return DateUtil.beginOfYear(date);
            default:
                return null;
        }
    }

    /**
     * 获取当前枚举的结束时间
     */
    public DateTime getEndTime(DateTime date) {
        switch (this) {
            case WORK_DAY:
                return DateUtil.endOfDay(date);
            case EVERY_WEEK:
                return DateUtil.endOfWeek(date);
            case EVERY_MONTH:
                return DateUtil.endOfMonth(date);
            case EVERY_YEAR:
                return DateUtil.endOfYear(date);
            default:
                return null;
        }
    }

    /**
     * 根据 type 获取枚举
     */
    public static SubmitRateEnum getEnumByType(Integer type) {
        if (ObjectUtil.isNull(type)) {
            return null;
        }
        for (SubmitRateEnum rate : values()) {
            if (rate.getSubmitType().equals(type)) {
                return rate;
            }
        }
        return null;
    }

    private static List<InnerSubmitRate> innerSubmitRateList = new ArrayList<>();

    private SubmitRateEnum(int submitType, String submitTypeName) {
        this.submitType = submitType;
        this.submitTypeName = submitTypeName;
    }

    /**
     * 获取提交频率列表
     *
     * @return
     */
    public static List<InnerSubmitRate> getSubmitRateList() {
        if (CollUtil.isEmpty(innerSubmitRateList)) {
            innerSubmitRateList.add(new InnerSubmitRate(WORK_DAY.getSubmitType(), WORK_DAY.getSubmitTypeName()));
            innerSubmitRateList.add(new InnerSubmitRate(EVERY_WEEK.getSubmitType(), EVERY_WEEK.getSubmitTypeName()));
            innerSubmitRateList.add(new InnerSubmitRate(EVERY_MONTH.getSubmitType(), EVERY_MONTH.getSubmitTypeName()));
            innerSubmitRateList.add(new InnerSubmitRate(EVERY_YEAR.getSubmitType(), EVERY_YEAR.getSubmitTypeName()));
            innerSubmitRateList.add(new InnerSubmitRate(NO_RATE.getSubmitType(), NO_RATE.getSubmitTypeName()));
            innerSubmitRateList.add(new InnerSubmitRate(TERM.getSubmitType(), TERM.getSubmitTypeName()));
        }
        return innerSubmitRateList;
    }

    public static String getSubmitRateName(Integer submitType) {
        if (WORK_DAY.getSubmitType().equals(submitType)) {
            return WORK_DAY.getSubmitTypeName();
        } else if (EVERY_WEEK.getSubmitType().equals(submitType)) {
            return EVERY_WEEK.getSubmitTypeName();
        } else if (EVERY_MONTH.getSubmitType().equals(submitType)) {
            return EVERY_MONTH.getSubmitTypeName();
        } else if (EVERY_YEAR.getSubmitType().equals(submitType)) {
            return EVERY_YEAR.getSubmitTypeName();
        } else if (NO_RATE.getSubmitType().equals(submitType)) {
            return NO_RATE.getSubmitTypeName();
        }else if (TERM.getSubmitType().equals(submitType)){
            return TERM.getSubmitTypeName();
        }
        return "";
    }

    public Integer getSubmitType() {
        return submitType;
    }

    public String getSubmitTypeName() {
        return submitTypeName;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InnerSubmitRate {

        /**
         * 1：工作日每天 2：每周 3：每月 4：每年 5:不限定频次
         */
        private Integer submitType;
        /**
         * 工作日每天、 每周、 每月、每年、不限定频次
         */
        private String submitTypeName;
    }
}
