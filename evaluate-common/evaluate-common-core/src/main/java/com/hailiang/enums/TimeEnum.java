package com.hailiang.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 时间枚举
 * 枚举类
 */
@RequiredArgsConstructor
@Getter
public enum TimeEnum{
    YEAR(1, "年"),
    MONTH(2, "月"),
    <PERSON>EE<PERSON>(3,"周"),
    DAY(4,"日"),
    HOUR(5,"时"),
    MINUTE(6,"分"),
    SECOND(7,"秒"),
    TIME_PERIODS(8,"时间段");
    private final Integer code;
    private final String message;

    public static String getValueByCode(Integer code){
        for(TimeEnum timeEnum:TimeEnum.values()){
            if(code.equals(timeEnum.getCode())){
                return timeEnum.getMessage();
            }
        }
        return  null;
    }
}
