package com.hailiang.enums.report;

import lombok.Getter;

/**
 * <AUTHOR> gaoxin
 * @create 2023/9/5 11:01
 */
@Getter
public enum ReportPackageFileTypeEnum {
    ONE_PAGE(1, "一页纸"),
    ALL(2, "全部");

    private final int code;
    private final String description;

    ReportPackageFileTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return "ReportPackageFileTypeEnum{" +
                "code=" + code +
                ", description='" + description + '\'' +
                '}';
    }
}
