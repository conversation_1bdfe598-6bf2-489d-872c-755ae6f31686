/**
 * Hailiang.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.hailiang.enums.report;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报告单类型枚举类
 * <AUTHOR>
 * @version v0.1: ReportTemplateTypeEnum.java, v 0.1 2023年11月09日 11:25  zhousx Exp $
 */
@Getter
@AllArgsConstructor
public enum ReportTemplateTypeEnum {

    DAILY(1, "日常报告"),
    END_OF_TERM(2, "期末报告"),
    ;
    public final Integer code;
    public final String desc;

}