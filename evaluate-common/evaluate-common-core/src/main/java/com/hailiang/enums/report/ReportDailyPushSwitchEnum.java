package com.hailiang.enums.report;

public enum ReportDailyPushSwitchEnum {
    OPEN(1, "打开"),
    CLOSED(0, "关闭");

    /**
     * 枚举编号
     */
    private final Integer code;

    /**
     * 枚举详情
     */
    private final String msg;

    ReportDailyPushSwitchEnum(Integer num, String name) {
        this.code = num;
        this.msg = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static ReportDailyPushSwitchEnum getByCode(Integer code) {
        ReportDailyPushSwitchEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ReportDailyPushSwitchEnum st = var1[var3];
            if (st.code.equals(code)) {
                return st;
            }
        }

        return null;
    }
}
