package com.hailiang.enums.medal;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 争章活动奖章颁发类型
 * 枚举类
 */
@RequiredArgsConstructor
@Getter
public enum MedalAwardTypeEnum{

    AUTO(1, "自动颁发"),
    MANUAL(2, "手动颁发"),
    ;

    private final Integer code;
    private final String message;


    public static String getMessageByCode(Integer code) {
        for (MedalAwardTypeEnum activityTypeEnum : MedalAwardTypeEnum.values()) {
            if (code.equals(activityTypeEnum.getCode())) {
                return activityTypeEnum.getMessage();
            }
        }
        return null;
    }

    public static Integer getCodeByMessage(String message) {
        for (MedalAwardTypeEnum activityTypeEnum : MedalAwardTypeEnum.values()) {
            if (message.equals(activityTypeEnum.getMessage())) {
                return activityTypeEnum.getCode();
            }
        }
        return null;
    }
}
