package com.hailiang.enums.medal;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 争章活动奖章颁发状态
 * 枚举类
 */
@RequiredArgsConstructor
@Getter
public enum PersonalProgressStatusEnum {

    IS_PROCEED(1, "进行中"),
    IS_COMPLETION(2, "已完成"),
    ;

    private final Integer code;
    private final String message;

    public static String getMessageByCode(Integer code) {
        for (PersonalProgressStatusEnum activityTypeEnum : PersonalProgressStatusEnum.values()) {
            if (code.equals(activityTypeEnum.getCode())) {
                return activityTypeEnum.getMessage();
            }
        }
        return null;
    }

    public static Integer getCodeByMessage(String message) {
        for (PersonalProgressStatusEnum activityTypeEnum : PersonalProgressStatusEnum.values()) {
            if (message.equals(activityTypeEnum.getMessage())) {
                return activityTypeEnum.getCode();
            }
        }
        return null;
    }
}
