package com.hailiang.enums.report;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * @description: 报告中行为类型枚举
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024/9/23 17:18
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum ReportBehaviourTypeEnum {
    BEHAVIOUR_RECORD(1, "行为记录表记录"),
    HELP_BEHAVIOUR_RECORD(2, "师徒帮扶表记录"),
    OTHER(null, "-"),
    ;


    private final Integer code;
    private final String desc;

    public static ReportBehaviourTypeEnum getByCode(Integer code) {
        if (ObjectUtil.isNull(code)) {
            return OTHER;
        }else{
            for (ReportBehaviourTypeEnum typeEnum : ReportBehaviourTypeEnum.values()) {
                if (typeEnum.getCode().equals(code)) {
                    return typeEnum;
                }
            }
            return OTHER;
        }
    }
}