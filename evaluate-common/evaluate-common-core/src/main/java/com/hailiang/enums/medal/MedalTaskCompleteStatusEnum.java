package com.hailiang.enums.medal;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 争章活动规则任务完成状态
 * 枚举类
 */
@RequiredArgsConstructor
@Getter
public enum MedalTaskCompleteStatusEnum {

    NO_COMPLETION(0, "未完成"),
    IS_COMPLETE(1, "已完成"),
    IS_FROZEN(2, "已冻结"),
    ARTIFICIAL_WITHDRAW(3, "手动撤回"),


    ;

    private final Integer code;
    private final String message;


    public static String getMessageByCode(Integer code) {
        for (MedalTaskCompleteStatusEnum activityTypeEnum : MedalTaskCompleteStatusEnum.values()) {
            if (code.equals(activityTypeEnum.getCode())) {
                return activityTypeEnum.getMessage();
            }
        }
        return null;
    }

    public static Integer getCodeByMessage(String message) {
        for (MedalTaskCompleteStatusEnum activityTypeEnum : MedalTaskCompleteStatusEnum.values()) {
            if (message.equals(activityTypeEnum.getMessage())) {
                return activityTypeEnum.getCode();
            }
        }
        return null;
    }
}
