package com.hailiang.enums.report;

public enum ReportDailyPushWayEnum {
    HAI_JX(1, "hai家校"),
    DING_TALK(2, "钉钉"),
    FEI_SHU(3, "飞书");

    /**
     * 枚举编号
     */
    private final Integer code;

    /**
     * 枚举详情
     */
    private final String msg;

    ReportDailyPushWayEnum(Integer num, String name) {
        this.code = num;
        this.msg = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static ReportDailyPushWayEnum getByCode(Integer code) {
        ReportDailyPushWayEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ReportDailyPushWayEnum st = var1[var3];
            if (st.code.equals(code)) {
                return st;
            }
        }

        return null;
    }
}
