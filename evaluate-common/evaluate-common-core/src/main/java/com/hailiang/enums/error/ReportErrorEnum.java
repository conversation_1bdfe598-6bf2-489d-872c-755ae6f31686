package com.hailiang.enums.error;

import com.hailiang.exception.ErrorInterface;
import lombok.RequiredArgsConstructor;

/**
 * 课程信息判空校验
 * 枚举类
 */
@RequiredArgsConstructor
public enum ReportErrorEnum implements ErrorInterface {

    EMPTY_TASK_ID(10001, "审核任务ID为空"),
    EMPTY_TASK(10002, "审核任务不存在"),
    EMPTY_TASK_DETAIL_ID(10003, "审核详情ID为空"),
    EMPTY_TASK_DETAIL(10004, "审核详情不存在或已被审核"),
    EMPTY_PUSH_RULE(10005, "推送规则不存在"),
    GET_DETAIL_ERROR(10006, "获取明细失败,规则中不包含明细"),
    PUSH_MODULE_ERROR(10007,"推送模块至少选择一项"),
    ERR_REVIEW_STATUS(10008,"当前学生已推送，请不要重复推送"),
    DUPLICATE_REVIEW_STATUS(10009,"存在已推送过的学生，请不要重复推送"),
    NO_PARENT(10010,"家长信息不存在，请维护后再推送"),
    ;

    private final Integer code;
    private final String name;


    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.name;
    }

}
