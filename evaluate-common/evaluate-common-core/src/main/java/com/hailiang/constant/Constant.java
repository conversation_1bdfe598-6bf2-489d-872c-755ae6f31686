package com.hailiang.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface Constant {

    Integer ZERO = 0;
    Integer ONE = 1;
    Integer NEGATIVE_ONE = -1;
    Integer TWO = 2;
    Integer THREE = 3;
    Integer FOUR = 4;
    Integer SIX = 6;
    Integer BEHAVIOUR_LIST = 4;
    Integer FIVE = 5;
    Integer TEN = 10;
    Integer TWENTY = 20;
    Integer DAY_NUMBER_OF_YEAR = 365;
    Integer THIRTY = 30;
    Integer TWENTY_FOUR = 24;
    Integer THIRTY_ONE = 31;
    Integer SIXTY = 60;
    Integer DATETIME_OFFSET = -999;
    Integer NINETY_NINE = 99;

    Boolean TRUE = true;

    Boolean FALSE = false;

    Integer YES = 1;

    Integer NO = 0;

    Integer HUNDRED = 100;
    Integer TWO_HUNDRED = 200;
    Integer THOUSAND = 1000;
    Integer FIVE_HUNDRED = 500;
    Integer FIVE_THOUSAND = 5000;
    String CHINESE_COMMA = "，";
    String CHINESE_SPOT = "。";
    String ENGLISH_SPOT = "\\.";
    String CHINESE_COLON = "：";
    String ENGLISH_COLON = ":";
    String ENGLISH_COMMA = ",";
    String MINUTER = "分";
    String SCECOND = "秒";
    String AT = "@";
    String PERCENT = "%";
    String REGEX = "#";
    String HYPHEN = "-";
    String CAESURA_SIGN = "、";
    String MINUTE_REGEX = "'";
    String SECOND_REGEX = "\"";
    String SECOND_TWOOBLIQUE = "”";
    String SECOND_OBLIQUE = "’";
    String MIDPOINT = "·";
    String PATTERN_FORMAT = "yyyy-MM-dd";
    String YEAR_MONTH_DAY_FORMAT = "yyyy.M.d";
    String FORMAL_MONTH_DAY_FORMAT = "MM-dd";
    String CHINESE_FORMAT = "M月d日";
    String MONTH_DAY_FORMAT = "M.d";
    String EMPTY_STRING = "";
    String WAVY_LINE = "~";
    //分秒格式
    String MINUTES_SECONDS_REGEX = "^\\d{1,2}'\\d{1,2}\"$";
    //正浮点数
    String POSITIVE_FLOATING_POINT_NUMBER_REGEX = "^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$";
    //非负数
    String NONNEGATIVE_NUMBER_REGEX = "^\\d+(\\.\\d+){0,1}$";
    //数字
    String NUMBER_REGEX = "-?(0|[1-9]\\d*)(\\.\\d+)?";
    //包含字母
    String LETTER_REGEX = ".*[a-zA-Z].*";
    //-1
    Long LONG_NEGATIVE_ONE = -1L;
    //0
    Long LONG_ZERO = 0L;
    //1
    Long LONG_ONE = 1L;
    //一小时
    Long ONE_HOUR = 3600L;
    //12小时
    Long TWELVE_HOUR = 12 * 3600L;
    //一分钟
    Long ONE_MINUTE = 60L;
    //单位为分秒格式的项目
    String MINUTES_SECONDS = "minutesSeconds";
    //格式化两位小数
    String TWO_FLOAT_FORMT = "%02d";
    //校长roleCode
    String HEADMASTER_ROLE_CODE = "xs1001";
    //学生处主任roleCode
    String MANAGER_ROLE_CODE = "xs1010";
    //运营人员roleCode
    String OPERATION_ROLE_CODE = "xs1008";
    //年级组长roleCode
    String GRADE_ROLE_CODE = "xs1003";

    String SYSTEM = "system";

    // 学期查询相关状态码
    // 学期不存在
    String TERM_IS_NULL_CODE = "1000011";
    // 多个当前学期
    String HISTORY_TERM_IS_NULL_CODE = "1000012";
    // 学期正常状态码
    String TERM_DEFAULT_CODE = "1000010";
    String REPORT_TEMPLATE_OPERATE_ADD = "add";
    String REPORT_TEMPLATE_OPERATE_UPDATE = "update";

    String VERIFICATION_CODE = "verificationCode";

    String VERIFICATION_CODE_KEY = "VERIFICATION_CODE";

    String FAMILY_KEY = "FAMILY";

    String LIMIT_VERIFICATION_CODE_KEY = "LIMIT_VERIFICATION_CODE";

    Integer MESSAGE_CODE_LIMIT_CODE = 10000;

    Integer MESSAGE_CODE_EXPIRE_TIME = 15;

    String SUCCESS = "success";
    String STR_ZERO = "0";
    String MINUS_ONE = "-1";

    String ROD = "_";

    String SLASH = "/";

    String PDF_SUFFIX = ".pdf";

    String WORD_SUFFIX = ".docx";

    String HTML_SUFFIX = ".html";

    String ACTION_ADD = "add";
    String ACTION_UPDATE = "edit";

    String DEFAULT_CREATOR = "admin";

    String DEFAULT_VALUE = " ";

    Integer BATCH_INSERT_SIZE = 1000;

    Integer MONGO_BATCH_SIZE = 1200;

    /**
     * 跳转德育活动详情页前缀(老师)
     */
    String MORAL_HAI_TEACHER_URL_PREFIX = "/h5/page/index?appType=moralActivity&entry=3&role=1,2&url=";
    /**
     * 跳转德育活动详情页前缀(家长)
     */
    String MORAL_HAI_PARENT_URL_PREFIX = "/h5/page/index?appType=moralActivity&entry=3&role=0&url=";

    Set<String> IMAGE_EXTENSIONS = new HashSet<>(Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "tiff", "tif","heic","webp","heif"));

    Set<String> VIDEO_EXTENSIONS = new HashSet<>(Arrays.asList("mp4", "avi", "mov", "mkv", "wmv", "flv", "rmvb", "webm"));

    String TEACHER_MOBILE_BLANK = "    ";

    String CREATE_BY = "ArchiveJob";

    String EVALUATE = "evaluate";
}
