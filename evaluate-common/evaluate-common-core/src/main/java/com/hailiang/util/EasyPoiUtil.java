package com.hailiang.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;

/**
 * EasyPoi 工具类
 */
@Slf4j
public class EasyPoiUtil {

    /**
     * excel 导出
     *
     * @param list     数据列表
     * @param fileName 导出时的excel名称
     * @param response
     */
    public static void exportExcel(List<Map<String, Object>> list, String fileName, HttpServletResponse response) throws IOException {
        defaultExport(list, fileName, response);
    }

    /**
     * 默认的 excel 导出
     *
     * @param list     数据列表
     * @param fileName 导出时的excel名称
     * @param response
     */
    private static void defaultExport(List<Map<String, Object>> list, String fileName, HttpServletResponse response) throws IOException {
        //把数据添加到excel表格中
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.HSSF);
        downLoadExcel(fileName, response, workbook);
    }

    /**
     * excel 导出
     *
     * @param list         数据列表
     * @param pojoClass    pojo类型
     * @param fileName     导出时的excel名称
     * @param response
     * @param exportParams 导出参数（标题、sheet名称、是否创建表头，表格类型）
     */
    private static void defaultExport(List<?> list, Class<?> pojoClass, String fileName, HttpServletResponse response, ExportParams exportParams) throws IOException {
        //把数据添加到excel表格中
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        downLoadExcel(fileName, response, workbook);
    }

    /**
     * excel 导出
     *
     * @param list         数据列表
     * @param pojoClass    pojo类型
     * @param fileName     导出时的excel名称
     * @param exportParams 导出参数（标题、sheet名称、是否创建表头，表格类型）
     * @param response
     */
    public static void exportExcel(List<?> list, Class<?> pojoClass, String fileName, ExportParams exportParams, HttpServletResponse response) throws IOException {
        defaultExport(list, pojoClass, fileName, response, exportParams);
    }

    /**
     * excel 导出
     *
     * @param list      数据列表
     * @param title     表格内数据标题
     * @param sheetName sheet名称
     * @param pojoClass pojo类型
     * @param fileName  导出时的excel名称
     * @param response
     */
    public static void exportExcel(String fileName, String title, String sheetName, Class<?> pojoClass, List<?> list, HttpServletResponse response) throws IOException {
        defaultExport(list, pojoClass, fileName, response, new ExportParams(title, sheetName, ExcelType.XSSF));
    }


    /**
     * excel 导出
     *
     * @param list           数据列表
     * @param title          表格内数据标题
     * @param sheetName      sheet名称
     * @param pojoClass      pojo类型
     * @param fileName       导出时的excel名称
     * @param isCreateHeader 是否创建表头
     * @param response
     */
    public static void exportExcel(List<?> list, String title, String sheetName, Class<?> pojoClass, String fileName, boolean isCreateHeader, HttpServletResponse response) throws IOException {
        ExportParams exportParams = new ExportParams(title, sheetName, ExcelType.XSSF);
        exportParams.setCreateHeadRows(isCreateHeader);
        defaultExport(list, pojoClass, fileName, response, exportParams);
    }

    /**
     * excel下载03版本
     *
     * @param fileName 下载时的文件名称
     * @param response
     * @param workbook excel数据
     */
    public static void downLoad03Excel(String fileName, HttpServletResponse response, Workbook workbook) throws IOException {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xls", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }


    /**
     * excel下载
     *
     * @param fileName 下载时的文件名称
     * @param response
     * @param workbook excel数据
     */
    public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) throws IOException {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }

    /**
     * excel下载
     *
     * @param fileName 下载时的文件名称
     * @param response
     * @param workbook excel数据
     */
    public static void downLoadExcelWithFormat(String fileName, HttpServletResponse response, Workbook workbook) throws IOException {
        OutputStream outputStream = null;
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("format","excel");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));

            outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.error("Excel文件下载失败: {}", e.getMessage(), e);
            throw new IOException("Excel文件保存失败: " + e.getMessage(), e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输出流失败", e);
                }
            }
        }
    }

    /**
     * @description: 下载本地
     * @param: workbook, filePath
     * @return: void
     * <AUTHOR>
     * @date: 2022-04-24 16:27
     */
    public static void saveExcelFileOld(Workbook workbook, String filePath) {
        ExcelWriter excelWriter = new ExcelWriter(workbook, null);
        excelWriter.setDestFile(new File(filePath));
        excelWriter.flush();
    }

    @SneakyThrows
    public static void saveExcelFile(Workbook workbook, String filePath) {
        /*File savefile = new File("D:/home/<USER>/");
        if (!savefile.exists()) {
            savefile.mkdirs();
        }*/
        FileOutputStream fos = new FileOutputStream(filePath);
        workbook.write(fos);
        fos.close();
    }
    /**
     * excel 导入
     *
     * @param file      excel文件
     * @param pojoClass pojo类型
     * @param <T>
     * @return
     */
    public static <T> List<T> importExcel(MultipartFile file, Class<T> pojoClass) throws IOException {
        return importExcel(file, 1, 1, pojoClass);
    }

    /**
     * excel 导入
     *
     * @param filePath   excel文件路径
     * @param titleRows  表格内数据标题行
     * @param headerRows 表头行
     * @param pojoClass  pojo类型
     * @param <T>
     * @return
     */
    public static <T> List<T> importExcel(String filePath, Integer titleRows, Integer headerRows, Class<T> pojoClass) throws IOException {
        if (StringUtils.isBlank(filePath)) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setNeedSave(true);
        params.setSaveUrl("/excel/");
        try {
            return ExcelImportUtil.importExcel(new File(filePath), pojoClass, params);
        } catch (NoSuchElementException e) {
            throw new IOException("模板不能为空");
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }


    /**
     * excel 导入
     *
     * @param file       上传的文件
     * @param titleRows  表格内数据标题行
     * @param headerRows 表头行
     * @param pojoClass  pojo类型
     * @param <T>
     * @return
     */
    public static <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows, Class<T> pojoClass) throws IOException {
        if (file == null) {
            return null;
        }
        try {
            return importExcel(file.getInputStream(), titleRows, headerRows, pojoClass);
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }

    /**
     * excel 导入
     *
     * @param inputStream 文件输入流
     * @param titleRows   表格内数据标题行
     * @param headerRows  表头行
     * @param pojoClass   pojo类型
     * @param <T>
     * @return
     */
    public static <T> List<T> importExcel(InputStream inputStream, Integer titleRows, Integer headerRows, Class<T> pojoClass) throws IOException {
        if (inputStream == null) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        params.setSaveUrl("/excel/");
        params.setNeedSave(true);
        try {
            return ExcelImportUtil.importExcel(inputStream, pojoClass, params);
        } catch (NoSuchElementException e) {
            throw new IOException("excel文件不能为空");
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }

    /**
     * 保存在指定目录
     * @param workbook
     * @param filePath
     */
    public static void saveExcel(Workbook workbook, String filePath) {
        ExcelWriter excelWriter = new ExcelWriter(workbook, null);
        excelWriter.setDestFile(new File(filePath));
        excelWriter.flush();
    }

    /**
     * 自适应 adjustStyle(workbook.getSheetAt(0));
     * @param sheet
     */
    public static void adjustStyle(Sheet sheet) {
        sheet.rowIterator()
                .forEachRemaining(EasyPoiUtil::adaptiveRowHeight);
    }

    private static void adaptiveRowHeight(Row row) {
        for (short index = row.getFirstCellNum(); index < row.getLastCellNum(); index++) {


            Cell cell = row.getCell(index);
            if (Objects.isNull(cell) || CellType.BLANK.equals(cell.getCellType())) {
                continue;
            }
            int fontIndexAsInt = cell.getCellStyle().getFontIndexAsInt();
            short cellFontSize = row.getSheet().getWorkbook().getFontAt(fontIndexAsInt).getFontHeightInPoints();


            String cellContent = cell.getStringCellValue();
            int cellWidth = getCellWidth(cell);
            int calCellContentLength = calCellContentLength(cellContent);
            short rows = NumberUtil.div(BigDecimal.valueOf(calCellContentLength), BigDecimal.valueOf(cellWidth))
                    .setScale(0, RoundingMode.UP).shortValue();


            BigDecimal calHeight = BigDecimal.valueOf(cellFontSize * 20)
                    .multiply(BigDecimal.valueOf(rows));


            if (NumberUtil.isGreater(calHeight, BigDecimal.valueOf(row.getHeight()))) {
                row.setHeight(calHeight.setScale(0, RoundingMode.CEILING).shortValue());
            }
        }
    }
    private static int calCellContentLength(String cellContent) {
        int calLength = 0;

        if (Objects.isNull(cellContent)) {
            return calLength;
        }

        for (char c : cellContent.toCharArray()) {
            if (isChineseCharacter(c)) {
                calLength += 2;
            } else {
                calLength += 1;
            }
        }

        return calLength * 512;
    }

    private static boolean isChineseCharacter(char c) {
        return String.valueOf(c).matches("[\u4e00-\u9fa5]");
    }
    /**
     * 获取单元格宽度
     */
    private static int getCellWidth(Cell cell) {
        CellRangeAddress cellAddresses = getCellRangeAddress(cell);

        if (Objects.isNull(cellAddresses)) {
            return cell.getSheet().getColumnWidth(cell.getColumnIndex());
        }

        int cellWidth = 0;
        for (int columnIndex = cellAddresses.getFirstColumn(); columnIndex <= cellAddresses.getLastColumn(); columnIndex++) {
            cellWidth += cell.getSheet().getColumnWidth(cell.getColumnIndex());
        }
        return cellWidth;

    }

    private static CellRangeAddress getCellRangeAddress(Cell cell) {
        return cell.getSheet().getMergedRegions()
                .stream()
                .filter(cellAddresses -> cell.getRowIndex() >= cellAddresses.getFirstRow() && cell.getRowIndex() <= cellAddresses.getLastRow()
                        && cell.getColumnIndex() >= cellAddresses.getFirstColumn() && cell.getColumnIndex() <= cellAddresses.getLastColumn())
                .findFirst()
                .orElse(null);
    }

    /**
     * 删除指定列
     * @param sheet
     * @param columnToDelete
     * @return
     */
    public static Sheet removeColumn(Sheet sheet, int columnToDelete) {
        for (int rId = 0; rId <= sheet.getLastRowNum(); rId++) {
            Row row = sheet.getRow(rId);
            for (int cID = columnToDelete; cID <= row.getLastCellNum(); cID++) {
                Cell cOld = row.getCell(cID);
                if (cOld != null) {
                    row.removeCell(cOld);
                }
                Cell cNext = row.getCell(cID + 1);
                if (cNext != null) {
                    Cell cNew = row.createCell(cID, cNext.getCellType());
                    cloneCell(cNew, cNext);
                    //Set the column width only on the first row.
                    //Other wise the second row will overwrite the original column width set previously.
                    if (rId == 0) {
                        sheet.setColumnWidth(cID, sheet.getColumnWidth(cID + 1));
                    }
                }
            }
        }
        return sheet;
    }

    /**
     * 右边列左移
     *
     * @param cNew
     * @param cOld
     */
    private static void cloneCell(Cell cNew, Cell cOld) {
        cNew.setCellComment(cOld.getCellComment());
        cNew.setCellStyle(cOld.getCellStyle());

        if (CellType.BOOLEAN == cNew.getCellType()) {
            cNew.setCellValue(cOld.getBooleanCellValue());
        } else if (CellType.NUMERIC == cNew.getCellType()) {
            cNew.setCellValue(cOld.getNumericCellValue());
        } else if (CellType.STRING == cNew.getCellType()) {
            cNew.setCellValue(cOld.getStringCellValue());
        } else if (CellType.ERROR == cNew.getCellType()) {
            cNew.setCellValue(cOld.getErrorCellValue());
        } else if (CellType.FORMULA == cNew.getCellType()) {
            cNew.setCellValue(cOld.getCellFormula());
        }
    }
    public static void cloneSheet(Workbook workbook, String srcSheetName, String destSheetName) {
        int index = workbook.getSheetIndex(srcSheetName);
        cloneSheet(workbook, index, destSheetName);
    }

    public static void cloneSheet(File excelFile, String srcSheetName, String destSheetName) {
        Workbook sheets = readExcelFromFile(excelFile);
        int index = sheets.getSheetIndex(srcSheetName);
        cloneSheet(excelFile, index, destSheetName);
    }

    public static void cloneSheet(File excelFile, Integer index, String destSheetName) {
        Workbook sheets = readExcelFromFile(excelFile);
        //克隆一个新的sheet
        Sheet newSheet = sheets.cloneSheet(index);
        int sheetIndex = sheets.getSheetIndex(newSheet);
        sheets.setSheetName(sheetIndex, destSheetName);
        try {
            FileOutputStream out = new FileOutputStream(excelFile);
            out.flush();
            sheets.write(out);
            out.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    public static void cloneSheet(Workbook workbook, Integer index, String destSheetName) {
        //克隆一个新的sheet
        Sheet newSheet = workbook.cloneSheet(index);
        int sheetIndex = workbook.getSheetIndex(newSheet);
        workbook.setSheetName(sheetIndex, destSheetName);
    }
    //读取excel
    public static Workbook readExcelFromFile(File file) {
        if (file == null) {
            throw new IllegalStateException("文件解析失败， file is null");
        }
        try {
            return new XSSFWorkbook(new FileInputStream(file));
        } catch (IOException e) {
            throw new IllegalStateException("文件解析失败，" + file.getAbsolutePath(), e);
        }
    }

    /**
     * 根据模板导出word
     *
     * @param map 数据
     * @param url 模板地址
     * @param tempFile 临时模板文件
     */
    public static void exportWord(Map<String, Object> map, String url, File tempFile) {
        try {
            XWPFDocument doc = WordExportUtil.exportWord07(url, map);
            // 手动处理换行符
            for (XWPFParagraph paragraph : doc.getParagraphs()) {
                // 使用手动管理索引来遍历和修改 XWPFRun 集合
                for (int i = 0; i < paragraph.getRuns().size(); i++) {
                    XWPFRun run = paragraph.getRuns().get(i);
                    String text = run.getText(0);
                    if (text != null && text.contains("\n")) {
                        String[] lines = text.split("\n");
                        run.setText(lines[0], 0);
                        for (int j = 1; j < lines.length; j++) {
                            XWPFRun newRun = paragraph.createRun();
                            newRun.addBreak();
                            newRun.setText(lines[j]);
                        }
                    }
                }
            }

            // 处理表格中的换行符
            for (XWPFTable table : doc.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            for (int i = 0; i < paragraph.getRuns().size(); i++) {
                                XWPFRun run = paragraph.getRuns().get(i);
                                String text = run.getText(0);
                                if (text != null && text.contains("\n")) {
                                    String[] lines = text.split("\n");
                                    run.setText(lines[0], 0);
                                    for (int j = 1; j < lines.length; j++) {
                                        XWPFRun newRun = paragraph.createRun();
                                        newRun.addBreak();
                                        newRun.setText(lines[j]);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            FileOutputStream fos = new FileOutputStream(tempFile);
            doc.write(fos);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
