package com.hailiang.util;


import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;


/**
 * 雪花算法ID生成工具
 *
 * @Description: 雪花算法ID生成工具
 * @Author: Jovi
 * @Date: Created in 2024/4/16
 * @Version: 1.7.0
 */
@Slf4j
public class SnowFlakeIdUtil {

    /**
     * 起始的时间戳（"2010-11-04 09:42:54"）与mybatis-plus的起始时间戳相同
     */
    private final static long START_STMP = 1288834974657L;

    /*==========================每一部分占用的位数==========================*/
    /**
     * 序列号占用的位数
     */
    private final static int SEQUENCE_BIT = 12;
    /**
     * 机器标识占用的位数
     */
    private final static int MACHINE_BIT = 5;
    /**
     * 数据中心标识占用的位数
     */
    private final static int DATA_BIT = 5;

    /**
     * 每一部分的最大值
     */
    private final static int MAX_MACHINE_NUM = ~(-1 << MACHINE_BIT);
    private final static int MAX_DATA_NUM = ~(-1 << DATA_BIT);
    private final static int MAX_SEQUENCE = ~(-1 << SEQUENCE_BIT);

    /**
     * 随机获取数据中心id的参数 32
     */
    private static final int DATA_RANDOM = MAX_DATA_NUM + 1;
    /**
     * 随机获取机器id的参数 32
     */
    private static final int MACHINE_RANDOM = MAX_MACHINE_NUM + 1;


    /**
     * 机器id左移位数 12
     */
    private final static long MACHINE_LEFT = SEQUENCE_BIT;
    /**
     * 数据中心id左移位数 17
     */
    private final static long DATA_LEFT = DATA_BIT + MACHINE_LEFT;
    /**
     * 时间左移位数 22
     */
    private final static long TIMESTMP_LEFT = DATA_LEFT + DATA_BIT;

    /**
     * 序列号
     */
    private static long sequence = 0L;
    /**
     * 上一次时间戳
     */
    private static long lastStmp = -1L;

    /**
     * 机房Id
     */
    private final static long DATA_ID = getDataId();

    /**
     * 机器Id
     */
    private final static long MACHINE_ID = getWorkId();


    /**
     * 产生下一个ID
     *
     * @return
     */
    public synchronized static long nextId() {
        long currStmp = getNewstmp();
        if (currStmp < lastStmp) {
            throw new RuntimeException(String.format("系统时间错误！ %d 毫秒内拒绝生成雪花ID！", START_STMP - currStmp));
        }

        if (currStmp == lastStmp) {
            //相同毫秒内，序列号自增，按位与运算保证始终是在4096这个范围内，避免传递序列号超过4096
            sequence = (sequence + 1) & MAX_SEQUENCE;
            //同一毫秒的序列数已经达到最大
            if (sequence == 0L) {
                currStmp = getNextMill(lastStmp);
            }
        } else {
            //不同毫秒内，序列号置为0
            sequence = 0L;
        }

        //上次生成Id的时间戳
        lastStmp = currStmp;

        /**
         *  这儿就是最核心的二进制位运算操作，生成一个64bit的id
         *  先将当前时间戳左移，放到41 bit那儿；将机房id左移放到5 bit那儿；
         *  将机器id左移放到5 bit那儿；将序号放最后12 bit
         *  最后拼接起来成一个64 bit的二进制数字，转换成10进制就是个long型
         */

        long idFromUtil =
                //时间戳部分
                (currStmp - START_STMP) << TIMESTMP_LEFT
                        //机房标识部分
                        | DATA_ID << DATA_LEFT
                        //机器标识部分
                        | MACHINE_ID << MACHINE_LEFT
                        //序列号部分
                        | sequence;

        log.debug("【雪花算法工具类】-【生成主键ID】-currStmp：【{}】，DATA_ID：【{}】，MACHINE_ID：【{}】，主键ID：【{}】",
                currStmp,
                DATA_ID,
                MACHINE_ID,
                idFromUtil);

        return idFromUtil;
    }

    public static String nextIdStr() {
        return Convert.toStr(nextId());
    }

    /**
     * 阻塞到下一秒，直到获得新的时间戳
     *
     * @param lastTimestamp 上次生成Id的时间戳
     * @return 当前时间戳
     */
    private static long getNextMill(long lastTimestamp) {
        long mill = getNewstmp();
        while (mill <= lastTimestamp) {
            mill = getNewstmp();
        }
        return mill;
    }

    private static long getNewstmp() {
        return System.currentTimeMillis();
    }

    /**
     * 根据IP地址取余，发生异常就取0到31之间的随机数
     *
     * @return
     */
    private static int getWorkId() {
        String workerId = System.getProperty("workerId");
        return Convert.toInt(workerId);
    }

    /**
     * 根据主机名取余，发生异常就取0到31之间的随机数
     *
     * @return
     */
    private static int getDataId() {
        String dataCenterId = System.getProperty("dataCenterId");
        return Convert.toInt(dataCenterId);
    }

}
