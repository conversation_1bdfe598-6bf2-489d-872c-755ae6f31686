package com.hailiang.config;

import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * job执行的切面
 *
 * @Description: job执行的切面
 * @Author: xuqiwei
 * @Date: Created in 2024-03-29
 * @Version: 1.0.0
 */
@Aspect
@Component
@Slf4j
public class XxlJobAspect {
    @Around("within(com.hailiang.job..*) && @annotation(com.xxl.job.core.handler.annotation.XxlJob)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Class<?> clazz = point.getTarget().getClass();
        long start = System.currentTimeMillis();
        Object result;
        String methodName = point.getSignature().getName();
        log.info("xxlJob handler start, {}.{}", clazz.getSimpleName(), methodName);
        XxlJobHelper.log("xxlJob handler start, {}.{}", clazz.getSimpleName(), methodName);
        try {
            result = point.proceed();
            long time = System.currentTimeMillis() - start;
            XxlJobHelper.log("xxlJob handler end: {}, cost-time: {} ms", point.getSignature(), time);
            log.info("xxlJob handler end: {}, cost-time: {} ms", point.getSignature(), time);
        } catch (Throwable e) {
            XxlJobHelper.log("xxlJob handler error: {}.{}, error:{}", clazz.getSimpleName(), methodName, e.getMessage());
            log.error("xxlJob handler error: {}.{}, error:{}", clazz.getSimpleName(), methodName, e.getMessage(), e);
            throw e;
        }
        return result;
    }

}