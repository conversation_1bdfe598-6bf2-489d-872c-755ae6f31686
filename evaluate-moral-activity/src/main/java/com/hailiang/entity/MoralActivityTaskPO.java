package com.hailiang.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hailiang.common.mybatis.base.BaseBusinessEntity;
import com.hailiang.common.mybatis.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 德育活动任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Getter
@Setter
@TableName("moral_activity_task")
public class MoralActivityTaskPO extends BaseBusinessEntity {
    /**
     * 年级id
     */
    @TableField("grade_id")
    private String gradeId;

    /**
     * 年级code
     */
    @TableField("grade_code")
    private String gradeCode;

    /**
     * 活动id
     */
    @TableField("activity_id")
    private Long activityId;

    /**
     * 班级id
     */
    @TableField("class_id")
    private Long classId;

    /**
     * 任务类型 1：校外写实作品提交，2校园作品提交，3：教师点评
     */
    @TableField("task_type")
    private Integer taskType;

    /**
     * 受理对象类型 1:学生对应家长 2:班级对应老师
     */
    @TableField("assignee_type")
    private Integer assigneeType;

    /**
     * 受理对象id 1:student_id; 2:classId
     */
    @TableField("assignee_id")
    private String assigneeId;

    /**
     * 任务状态 1：待办，2：已完成
     */
    @TableField("task_status")
    private Integer taskStatus;

    /**
     * 任务下作品总体状态 0:初始值 1：待审核，2：审核失败，3：审核成功（待评价） 4：评价不通过 5：评价通过
     */
    @TableField("production_status")
    private Integer productionStatus;

    /**
     * 已提交次数
     */
    @TableField("task_submit_num")
    private Integer taskSubmitNum;

    /**
     * tasktype=3的情况下代表任务已提醒次数
     */
    @TableField("task_advice_num")
    private Integer taskAdviceNum;


    public static final String TENANT_ID = "tenant_id";

    public static final String SCHOOL_ID = "school_id";

    public static final String CAMPUS_ID = "campus_id";

    public static final String GRADE_ID = "grade_id";

    public static final String GRADE_CODE = "grade_code";

    public static final String ACTIVITY_ID = "activity_id";

    public static final String CLASS_ID = "class_id";

    public static final String TASK_TYPE = "task_type";

    public static final String ASSIGNEE_TYPE = "assignee_type";

    public static final String ASSIGNEE_ID = "assignee_id";

    public static final String TASK_STATUS = "task_status";

    public static final String PRODUCTION_STATUS = "production_status";

    public static final String TASK_SUBMIT_NUM = "task_submit_num";

    public static final String TASK_ADVICE_NUM = "task_advice_num";

}
