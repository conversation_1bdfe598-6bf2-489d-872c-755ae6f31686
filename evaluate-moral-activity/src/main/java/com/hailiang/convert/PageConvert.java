package com.hailiang.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

public class PageConvert {

    private PageConvert(){

    }

    /**
     * 将IPage转换为page
     *
     * @param page
     * @param <T>
     * @return
     */
    public static <T> Page<T> convertIPageToPage(IPage<T> page) {
        Page<T> res = new Page<>(page.getCurrent(), page.getSize());
        res.setRecords(page.getRecords());
        res.setTotal(page.getTotal());
        res.setCurrent(page.getCurrent());
        res.setPages(page.getPages());
        res.setSize(page.getSize());
        return res;
    }
}
