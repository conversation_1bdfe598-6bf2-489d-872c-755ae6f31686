package com.hailiang.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 活动任务指派类型枚举
 *
 * @Description: 活动任务指派类型枚举
 * @Author: xuqiwei
 * @Date: Created in 2024-03-18
 * @Version: 1.0.0
 */
@RequiredArgsConstructor
@Getter
public enum MoralActivityTaskTypeEnum {
    OFF_CAMPUS_ACTIVITY(1, "校外写实作品提交"),
    CAMPUS_ACTIVITY(2, "校园作品提交"),
    TEACHER_REVIEW(3, "教师点评"),
    ;
    private final Integer code;
    private final String msg;

    public static List<Integer> getCommittedTaskType() {
        return Arrays.asList(OFF_CAMPUS_ACTIVITY.getCode(), CAMPUS_ACTIVITY.getCode());
    }
}
