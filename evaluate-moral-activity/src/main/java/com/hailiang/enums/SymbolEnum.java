package com.hailiang.enums;

import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * 符号
 */
@Getter
@RequiredArgsConstructor
public enum SymbolEnum {
    ADD(1,"+"),
    SUBTRACT(2,"-"),
    ;


    private final Integer code;
    private final String value;

    public static SymbolEnum getByCode(Integer code){
        for (SymbolEnum value : SymbolEnum.values()) {
            if(Objects.equals(value.getCode(),code)){
                return value;
            }
        }
        return null;
    }
}
