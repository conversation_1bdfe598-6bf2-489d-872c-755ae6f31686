package com.hailiang.enums;

import com.hailiang.remote.hai.enmus.HaiPushRoleTypeEnum;
import com.hailiang.remote.hai.enmus.HaiTaskStatusTypeEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 活动任务状态枚举
 *
 * @Description: 活动任务状态枚举
 * @Author: xuqiwei
 * @Date: Created in 2024-03-18
 * @Version: 1.0.0
 */
@RequiredArgsConstructor
@Getter
public enum MoralActivityTaskStatusEnum {
    UNFINISHED(1, "待办"),
    FINISHED(2, "已完成"),
    ;
    private final Integer code;
    private final String msg;

    public static MoralActivityTaskStatusEnum getByCode(Integer code) {
        for (MoralActivityTaskStatusEnum enums : MoralActivityTaskStatusEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        throw new IllegalStateException("code is illegal");
    }

    public HaiTaskStatusTypeEnum getHaiEnum() {
        switch (this) {
            case UNFINISHED:
                return HaiTaskStatusTypeEnum.UNFINISHED;
            case FINISHED:
                return HaiTaskStatusTypeEnum.FINISHED;
            default:
                throw new IllegalStateException("can't convert enum");
        }
    }
}
