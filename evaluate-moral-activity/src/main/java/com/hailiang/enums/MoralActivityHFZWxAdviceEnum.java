package com.hailiang.enums;

import com.fasterxml.jackson.databind.deser.impl.PropertyValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 嗨夫子微信通知模版
 *
 * @Description: 嗨夫子微信通知模版
 * @Author: tanjian
 * @Date: Created in 2025-02-19
 * @Version: 1.0.0
 */
@RequiredArgsConstructor
@Getter
public enum MoralActivityHFZWxAdviceEnum {

    BE_COMMENT("综合实践活动作品点评", "%s", "作品点评", "待点评", "尚有%s个学生作品未点评"),
    BE_COMMIT("综合实践活动作品提交", "%s", "作品提交", "待提交", "请在%s前提交作品"),
    PRODUCTION_ILLEGAL("综合实践活动作品修改", "%s", "作品修改", "待修改", "请在%s前修改作品"),
    ;

    private final String typeName;

    private final String title;

    private final String todoTitle;

    private final String status;

    private final String content;
}
