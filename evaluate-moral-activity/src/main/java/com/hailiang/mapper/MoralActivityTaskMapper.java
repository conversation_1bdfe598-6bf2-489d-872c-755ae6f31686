package com.hailiang.mapper;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.dto.response.MoralActivityTaskResponse;
import org.apache.ibatis.annotations.Param;

import com.hailiang.entity.MoralActivityTaskPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 德育活动任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Mapper
public interface MoralActivityTaskMapper extends BaseMapper<MoralActivityTaskPO> {
    Page<MoralActivityTaskResponse> pageActivityTaskStudent(Page<MoralActivityTaskResponse> page,
                                                             @Param("classId") Long classId,
                                                             @Param("studentId") String studentId,
                                                             @Param("activityType") Integer activityType,
                                                             @Param("activityStatus") Integer activityStatus,
                                                            @Param("activityName") String activityName
    );

    List<String> listStudentIds(@Param("activityId") String activityId);

    List<MoralActivityTaskPO> listClassGradeByActivityId(@Param("activityId") String activityId);

    List<String> listGradeByActivityId(@Param("activityId") String activityId);
}
