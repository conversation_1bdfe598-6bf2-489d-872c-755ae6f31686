package com.hailiang.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hailiang.dto.MoralActivitySubmitCountDTO;
import com.hailiang.dto.MoralActivityTaskProductionCountDTO;
import com.hailiang.dto.MoralActivityTaskStudentCountDTO;
import com.hailiang.entity.MoralActivityTaskProductionPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 德育活动任务-作品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Mapper
public interface MoralActivityTaskProductionMapper extends BaseMapper<MoralActivityTaskProductionPO> {

    @DS("doris")
    List<MoralActivityTaskProductionCountDTO> selectCampusProductionCount(@Param("activityId") String activityId);

    @DS("doris")
    List<MoralActivityTaskProductionCountDTO> selectOffCampusProductionCount(@Param("activityId") String activityId);

    @DS("doris")
    List<MoralActivityTaskStudentCountDTO> selectOffCampusStudentCount(@Param("activityId") String activityId);

    @DS("doris")
    List<MoralActivitySubmitCountDTO> countSubmitNumByCampusId(@Param("campusId") String campusId);

    List<MoralActivityTaskProductionPO> listAllByActivityIds(@Param("activityIds") List<Long> activityIds);
}
