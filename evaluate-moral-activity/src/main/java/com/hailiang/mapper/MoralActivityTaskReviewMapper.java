package com.hailiang.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hailiang.dto.MoralActivityTaskReviewCountDTO;
import com.hailiang.dto.MoralActivityTaskReviewDTO;
import com.hailiang.entity.MoralActivityTaskReviewPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 德育活动任务-点评 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Mapper
public interface MoralActivityTaskReviewMapper extends BaseMapper<MoralActivityTaskReviewPO> {

    @DS("doris")
    List<MoralActivityTaskReviewCountDTO> selectOffCampusCommentCount(@Param("activityId") String activityId);

    @DS("doris")
    List<MoralActivityTaskReviewDTO> selectOffCampusStudentCount(@Param("activityId") String activityId);
}
