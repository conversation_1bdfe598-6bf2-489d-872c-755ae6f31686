package com.hailiang.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.processeva.request.MoralSportDetailRequest;
import com.hailiang.model.processeva.request.ProcessEvaRecordIdsRequest;
import com.hailiang.model.processeva.request.ProcessEvaRecordRequest;
import com.hailiang.model.processeva.request.SpeedInfoDetailRequest;
import com.hailiang.model.processeva.response.ProcessEvaRecordResponse;
import com.hailiang.model.vo.GetEvaluateInfoDetailVO;
import com.hailiang.service.ProcessEvaService;
import com.hailiang.util.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 过程性评价
 * @Author: huyouting
 * @Date: Created in 2024-12-02
 * @Version: v1.2.4
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/processEva")
public class ProcessEvaController {

    private final ProcessEvaService processEvaService;

    /**
     * 分页查询过程性评价明细记录
     *
     * @param request
     * @return
     */
    @PostMapping("/pageProcessEvaRecord")
    public R<Page<ProcessEvaRecordResponse>> pageProcessEvaRecord(@RequestBody ProcessEvaRecordRequest request) {
        return R.ok(processEvaService.pageProcessEvaRecord(request));
    }
}
