package com.hailiang.controller.evaluate;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.client.ProcessEvaClient;
import com.hailiang.model.processeva.request.MoralSportDetailRequest;
import com.hailiang.model.processeva.request.ProcessEvaRecordIdsRequest;
import com.hailiang.model.processeva.request.ProcessEvaRecordRequest;
import com.hailiang.model.processeva.request.SpeedInfoDetailRequest;
import com.hailiang.model.processeva.response.ProcessEvaRecordResponse;
import com.hailiang.model.vo.GetEvaluateInfoDetailVO;
import com.hailiang.service.ProcessEvaService;
import com.hailiang.util.R;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 过程性评价
 * @Author: huyouting
 * @Date: Created in 2024-12-02
 * @Version: v1.2.4
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/processEva")
public class ProcessEvaController {

    private final ProcessEvaService processEvaService;
    private final ProcessEvaClient processEvaClient;

    /**
     * 分页查询过程性评价明细记录
     *
     * @param request
     * @return
     */
    @PostMapping("/pageProcessEvaRecord")
    public R<Page<ProcessEvaRecordResponse>> pageProcessEvaRecord(@RequestBody ProcessEvaRecordRequest request) {
        return processEvaClient.pageProcessEvaRecord(request);
    }

    /**
     * 导出过程性评价明细记录
     *
     * @param request
     * @param response
     */
    @PostMapping("/exportProcessEvaRecord")
    public void exportProcessEvaRecord(@RequestBody @Valid ProcessEvaRecordRequest request, HttpServletResponse response) {
        // 导出限制10000条记录
        request.setPageNum(1);
        request.setPageSize(50001);
        R<Page<ProcessEvaRecordResponse>> pageR = processEvaClient.pageProcessEvaRecord(request);
        Page<ProcessEvaRecordResponse> data = pageR.getData();
        processEvaService.exportProcessEvaRecord(data, response);
    }

    /**
     * 批量删除过程性评价明细记录
     *
     * @param request
     */
    @PostMapping("/batchDeletedProcessEvaRecord")
    public R batchDeletedProcessEvaRecord(@RequestBody ProcessEvaRecordIdsRequest request) {
        processEvaService.batchDeletedProcessEvaRecord(request);
        return R.ok();
    }

    /**
     * 获取极速点评详情(单个学生)
     *
     * @param request
     */
    @PostMapping("/getSpeedInfoDetail")
    public R<GetEvaluateInfoDetailVO> getSpeedInfoDetail(@RequestBody SpeedInfoDetailRequest request) {
        return R.ok(processEvaService.getSpeedInfoDetail(request));
    }

    /**
     * 获取批量导入详情(单个学生)
     *
     * @param request
     */
    @PostMapping("/getBatchImportsInfoDetail")
    public R<GetEvaluateInfoDetailVO> getBatchImportsInfoDetail(@RequestBody SpeedInfoDetailRequest request) {
        return R.ok(processEvaService.getSpeedInfoDetail(request));
    }

    /**
     * 获取德育活动、体测详情
     *
     * @param request
     */
    @PostMapping("/moralSportDetailRequest")
    public R<GetEvaluateInfoDetailVO> getMoralSportInfoDetail(@RequestBody MoralSportDetailRequest request) {
        return R.ok(processEvaService.getMoralSportInfoDetail(request));
    }
}
