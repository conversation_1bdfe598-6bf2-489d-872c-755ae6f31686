package com.hailiang.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.client.DataStatisticsClient;
import com.hailiang.model.datastatistics.query.DataStatisticsQuery;
import com.hailiang.model.datastatistics.query.TeacherEvaluateDataStatisticsQuery;
import com.hailiang.model.datastatistics.vo.*;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.service.DataStatisticsService;
import com.hailiang.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 数据统计，看板数据，综合素质评价v1.1.0
 *
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping("/dataStatistics")
public class DataStatisticsController {

    @Autowired
    private DataStatisticsService dataStatisticsService;
    @Autowired
    private DataStatisticsClient dataStatisticsClient;
    /**
     * 获取学期时间
     *
     * @return
     */
    @GetMapping ("/getTime")
    public R<TermVo> getTime(@RequestParam String campusSectionId) {
        return R.ok(dataStatisticsService.getTime(campusSectionId));
    }

    /**
     * 老师点评统计
     *
     * @param query
     * @return
     */
    @PostMapping("/getTeacherEvaluateStatistics")
    public R<TeacherEvaluateDataStatisticsVO> getTeacherEvaluateStatistics(@RequestBody @Valid TeacherEvaluateDataStatisticsQuery query) {
        return R.ok(dataStatisticsService.getTeacherEvaluateStatistics(query));
    }

    /**
     * 学生点评覆盖率（新）
     *
     * @param dto
     * @return
     */
    @PostMapping("/getStudentEvaluateStatistics")
    public R<DataStatisticsStudentEvaluateRateVO> getStudentEvaluateStatistics(@RequestBody @Valid DataStatisticsQuery dto) {
        return R.ok(dataStatisticsService.getStudentEvaluateStatistics(dto));
    }

    /**
     * 老师点评榜（支持历史学年）
     *
     * @param query
     * @return
     */
    @PostMapping("/pageTeacherEvaluateNew")
    public R<Page<TeacherVO>> pageTeacherEvaluateNew(@RequestBody @Valid TeacherEvaluateDataStatisticsQuery query) {
        return R.ok(dataStatisticsService.pageTeacherEvaluateNew(query));
    }

    /**
     * 老师点评榜下载（支持历史学年）
     *
     * @param query
     */
    @PostMapping("/exportTeacherEvaluateNew")
    public void exportTeacherEvaluateNew(@RequestBody @Valid TeacherEvaluateDataStatisticsQuery query, HttpServletResponse response) {
        dataStatisticsService.exportTeacherEvaluateNew(query, response);
    }

    /**
     * 指标排行榜（支持历史学年）
     *
     * @param query
     * @return
     */
    @PostMapping("/getTargetRankNew")
    public R<Page<TargetRankVO>> getTargetRankNew(@RequestBody @Valid TeacherEvaluateDataStatisticsQuery query) {
        return R.ok(dataStatisticsService.getTargetRankNew(query));
    }

    /**
     * 指标排行榜下载（支持历史学年）
     *
     * @param query
     */
    @PostMapping("/exportTargetRankNew")
    public void exportTargetRankNew(@RequestBody @Valid TeacherEvaluateDataStatisticsQuery query, HttpServletResponse response) {
        dataStatisticsService.exportTargetRankNew(query, response);
    }

//    /**
//     * 老师点评榜下载
//     *
//     * @param dto
//     * @return
//     */
//    @PostMapping("/exportTeacherEvaluate")
//    @TLogAspect(convert = CurrentStaffConvert.class)
//    public void exportTeacherEvaluate(@RequestBody @Valid DataStatisticsQuery dto, HttpServletResponse response) throws IOException {
//        dataStatisticsService.exportTeacherEvaluate(dto,response);
//    }

    /**
     * 未被点评学生名单
     *
     * @param dto
     * @return
     */
    @PostMapping("/pageNoEvaluateStudent")
    public R<Page<StudentVO>> pageNoEvaluateStudent(@RequestBody @Valid DataStatisticsQuery dto) {
        return R.ok(dataStatisticsClient.pageNoEvaluateStudent(dto));
    }

    /**
     * 未被点评学生名单下载
     *
     * @param dto
     * @return
     */
    @PostMapping("/exportNoEvaluateStudent")
    public void exportNoEvaluateStudent(@RequestBody @Valid DataStatisticsQuery dto, HttpServletResponse response) throws IOException {
        dataStatisticsService.exportNoEvaluateStudent(dto,response);
    }

    /**
     * 年级分组（校级看板未被点评学生名单）
     *
     * @param dto
     * @return
     */
    @PostMapping("/listGrade")
    public R<List<GradeInfoVO>> listGrade(@RequestBody @Valid DataStatisticsQuery dto) {
        return R.ok(dataStatisticsClient.listGrade(dto));
    }

//    /**
//     * 获取上一段相同时间
//     *
//     * @param startTime
//     * @param endTime
//     * @return
//     */
//    @GetMapping("/getLastSameTime")
//    public R<LastSameTimeVO> getLastSameTime(Date startTime, Date endTime,String campusSectionId) {
//        return R.ok(dataStatisticsClient.getLastSameTime(startTime, endTime,campusSectionId));
//    }

    //
//    /**
//     * 表扬与待改进
//     *
//     * @param dto
//     * @return
//     */
//    @PostMapping("/getPraiseImproveDetail")
//    public R<PraiseImproveVO> getPraiseImproveDetail(@RequestBody DataStatisticsQuery dto) {
//        return R.ok(dataStatisticsClient.getPraiseImproveDetail(dto));
//    }

//    /**
//     * 五育分布
//     *
//     * @param dto
//     * @return
//     */
//    @PostMapping("/getFiveEducation")
//    public R<FiveEducationVO> getFiveEducation(@RequestBody DataStatisticsQuery dto) {
//        return R.ok(dataStatisticsClient.getFiveEducation(dto));
//    }

//    /**
//     * 指标覆盖率
//     *
//     * @param dto
//     * @return
//     */
//    @PostMapping("/getTargetCoverage")
//    public R<TargetCoverageVO> getTargetCoverage(@RequestBody DataStatisticsQuery dto) {
//        return R.ok(dataStatisticsClient.getTargetCoverage(dto));
//    }

//    /**
//     * 指标覆盖情况
//     *
//     * @param dto
//     * @return
//     */
//    @PostMapping("/getTargetFrequency")
//    public R<List<TargetFrequencyVO>> getTargetFrequency(@RequestBody DataStatisticsQuery dto) {
//        return R.ok(dataStatisticsClient.getTargetFrequency(dto));
//    }

//    /**
//     * 指标排行榜
//     *
//     * @param dto
//     * @return
//     */
//    @PostMapping("/getTargetRank")
//    public R<Page<TargetRankVO>> getTargetRank(@RequestBody DataStatisticsQuery dto) {
//        return R.ok(dataStatisticsClient.getTargetRank(dto));
//    }

//    /**
//     * 导出指标排行榜excel
//     *
//     * @param dto
//     * @return
//     */
//    @PostMapping("/exportTargetRank")
//    public void exportTargetRank(HttpServletResponse response, @RequestBody DataStatisticsQuery dto) {
//        dataStatisticsService.exportTargetRank(response,dto);
//
//    }

//    /**
//     * 获取教职工的权限范围（3：校级，4：年级，5：班级）
//     * @return Integer 3：校级，4：年级，5：班级
//     */
//    @GetMapping("/getRoleType")
//    public R<Integer> getRoleType() {
//        String staffId = WebUtil.getStaffId();
//        return R.ok(dataStatisticsService.getRoleType(staffId));
//    }
}
