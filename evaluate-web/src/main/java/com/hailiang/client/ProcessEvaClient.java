package com.hailiang.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hailiang.model.processeva.request.ProcessEvaRecordRequest;
import com.hailiang.model.processeva.response.ProcessEvaRecordResponse;
import com.hailiang.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 过程性评价明细列表
 * @Author: huyouting
 * @Date: Created in 2024-12-10
 * @Version: v2.1.0
 */
@FeignClient(value = "evaluate-calculated", path = "/evaluate-calculated/processEva")
public interface ProcessEvaClient {

    /**
     * 分页查询过程性评价明细记录
     *
     * @param request
     * @return
     */
    @PostMapping("/pageProcessEvaRecord")
    R<Page<ProcessEvaRecordResponse>> pageProcessEvaRecord(@RequestBody ProcessEvaRecordRequest request);

//    /**
//     * 导出过程性评价明细记录
//     *
//     * @param request
//     * @param response
//     */
//    @PostMapping("/exportProcessEvaRecord")
//    void exportProcessEvaRecord(@RequestBody ProcessEvaRecordRequest request, HttpServletResponse response);

}