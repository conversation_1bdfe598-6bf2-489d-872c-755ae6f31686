package com.hailiang.util;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.hailiang.model.dto.target.TargetBatchOperateSaveRecordDTO;

// 自定义监听器处理读取逻辑
public class TargetBatchOperateSaveRecordDTOListener extends AnalysisEventListener<TargetBatchOperateSaveRecordDTO> {
    @Override
    public void invoke(TargetBatchOperateSaveRecordDTO employee, AnalysisContext context) {
        // 这里可以对读取到的每一条数据进行处理
        System.out.println("读取到一行数据:" + employee);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 所有数据解析完成后会调用此方法
        System.out.println("所有数据解析完成");
    }
}