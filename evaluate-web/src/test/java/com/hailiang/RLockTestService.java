package com.hailiang;

import com.hailiang.common.lock.annotation.RLock;
import com.hailiang.common.lock.annotation.RLockKey;
import com.hailiang.common.lock.model.LockTimeoutStrategy;
import com.hailiang.common.lock.model.LockType;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/18 9:48
 */
@Service
public class RLockTestService {

    @RLock(waitTime = 20, leaseTime = 60, keys = {"#param"}, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public String getValue(String param) throws Exception {
        System.out.println("线程:[" + Thread.currentThread().getName() + "]拿到锁，开始处理业务逻辑-----------" + new Date());
          if ("sleep".equals(param)) {//线程休眠或者断点阻塞，达到一直占用锁的测试效果
            Thread.sleep(1000 * 5);
          } else if ("noSleep".equals(param)){

          }else{
              Thread.sleep(1000 * 5);
          }
        return "success";
    }

    @RLock(name = "getValueByRead", keys = {"#userId"}, waitTime = 20, leaseTime = -1, lockType = LockType.Read)
//    @RLock(name = "getValue", keys = {"#userId"}, waitTime = 20, leaseTime = 10)
    public String getValueByRead(String userId, @RLockKey Integer id)throws Exception{
        System.out.println("线程:[" + Thread.currentThread().getName() + "]拿到锁，开始处理业务逻辑-----------" + new Date());
        Thread.sleep(60 * 1000);

        return "success";
    }
    @RLock(name = "getValueByLock", keys = {"#userId"}, waitTime = 20, leaseTime = -1)
//    @RLock(name = "getValue", keys = {"#userId"}, waitTime = 20, leaseTime = 10)
    public String getValueByLock(String userId, @RLockKey Integer id)throws Exception{
        System.out.println("线程:[" + Thread.currentThread().getName() + "]拿到锁，开始处理业务逻辑-----------" + new Date());
        String result = this.getValueByLock1(userId, id);
        Thread.sleep(60 * 1000);

        return "success";
    }

    @RLock(name = "getValueByLock", keys = {"#userId"}, waitTime = 20, leaseTime = -1)
//    @RLock(name = "getValue", keys = {"#userId"}, waitTime = 20, leaseTime = 10)
    public String getValueByLock1(String userId, @RLockKey Integer id)throws Exception{
        System.out.println("线程:[" + Thread.currentThread().getName() + "]拿到锁，开始处理业务逻辑-----------" + new Date());
        Thread.sleep(30 * 1000);

        return "success";
    }
    /*@RLock(keys = {"#user.name", "#user.id"})
    public String getValue(User user)throws Exception{
        System.out.println("开始处理-----------" + new Date());
        Thread.sleep(60*1000);
        return "success";
    }*/
}
