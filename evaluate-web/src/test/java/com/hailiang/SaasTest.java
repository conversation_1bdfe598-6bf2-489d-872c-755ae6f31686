package com.hailiang;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Range;
import com.hailiang.constant.Constant;
import com.hailiang.enums.*;
import com.hailiang.logic.SaasSubjectLogic;
import com.hailiang.logic.TermLogic;
import com.hailiang.manager.SportProjectManager;
import com.hailiang.manager.SportScoreStandardManager;
import com.hailiang.mapper.SportProjectMapper;
import com.hailiang.model.dto.QueryBehaviourScoreDTO;
import com.hailiang.model.dto.standard.SportScoreLevelDTO;
import com.hailiang.model.dto.standard.SportStandardProjectDTO;
import com.hailiang.model.entity.SportProject;
import com.hailiang.model.query.standard.SportStandardQuery;
import com.hailiang.model.report.vo.ReportModuleScoreStatisticsVO;
import com.hailiang.model.report.vo.ReportModuleScoreVO;
import com.hailiang.model.vo.standard.SportExtraScoreMapVO;
import com.hailiang.model.vo.standard.SportExtraScoreVO;
import com.hailiang.model.vo.standard.SportProjectSexVO;
import com.hailiang.model.vo.standard.SportStandardVO;
import com.hailiang.mp.commonsource.api.PageResult;
import com.hailiang.remote.ding.domain.ChangeOaStatusReq;
import com.hailiang.remote.ding.utils.DingDingMsgUtil;
import com.hailiang.remote.internaldrive.SendTaskModel;
import com.hailiang.remote.saas.BasicInfoRemote;
import com.hailiang.remote.saas.SaaSYardAPI;
import com.hailiang.remote.saas.dto.auth.StaffDataAuthDTO;
import com.hailiang.remote.saas.dto.educational.EduClassQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduOrgQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentClassQueryDTO;
import com.hailiang.remote.saas.dto.educational.EduStudentPageQueryDTO;
import com.hailiang.remote.saas.dto.login.BaseInfoDTO;
import com.hailiang.remote.saas.dto.login.LoginDTO;
import com.hailiang.remote.saas.dto.login.LogoutDTO;
import com.hailiang.remote.saas.dto.role.ResRoleQueryDTO;
import com.hailiang.remote.saas.dto.school.PageSchoolDTO;
import com.hailiang.remote.saas.dto.school.SchoolQueryDTO;
import com.hailiang.remote.saas.dto.staff.*;
import com.hailiang.remote.saas.dto.student.StudentByIdQuery;
import com.hailiang.remote.saas.dto.student.UcStudentClassQuery;
import com.hailiang.remote.saas.dto.term.TermQuery;
import com.hailiang.remote.saas.dto.yard.YardTreeQueryDTO;
import com.hailiang.remote.saas.query.StaffClassQuery;
import com.hailiang.remote.saas.vo.LoginVO;
import com.hailiang.remote.saas.vo.auth.StaffDataAuthVO;
import com.hailiang.remote.saas.vo.campus.TchCampusInfoVO;
import com.hailiang.remote.saas.vo.educational.*;
import com.hailiang.remote.saas.vo.login.BaseInfoVO;
import com.hailiang.remote.saas.vo.role.ResStaffRoleVO;
import com.hailiang.remote.saas.vo.school.PageSchoolVO;
import com.hailiang.remote.saas.vo.school.TchSchoolVO;
import com.hailiang.remote.saas.vo.staff.*;
import com.hailiang.remote.saas.vo.student.StudentClassVO;
import com.hailiang.remote.saas.vo.student.UcStudentClassBffVO;
import com.hailiang.remote.saas.vo.term.TermVo;
import com.hailiang.remote.saas.vo.yard.YardTreeVO;
import com.hailiang.saas.*;
import com.hailiang.saas.common.base.CommonResult;
import com.hailiang.saas.model.dto.ClassSubjectRelationDTO;
import com.hailiang.saas.model.dto.SchoolYearTeacherReqDTO;
import com.hailiang.saas.model.dto.StudentDTO;
import com.hailiang.saas.model.dto.school.AppIdBaseReq;
import com.hailiang.saas.model.dto.school.EduOrgQueryV2DTO;
import com.hailiang.saas.model.dto.school.TchSchoolFullQuery;
import com.hailiang.saas.model.dto.student.StudentNumQueryDTO;
import com.hailiang.saas.model.dto.subject.SaasDisciplineQueryDTO;
import com.hailiang.saas.model.dto.yard.DutyYardRequest;
import com.hailiang.saas.model.vo.ClassSubjectRelationVO;
import com.hailiang.saas.model.vo.HisTeacherRelationVO;
import com.hailiang.saas.model.vo.SaasStudentNumVO;
import com.hailiang.saas.model.vo.StudentInfoVO;
import com.hailiang.saas.model.vo.school.EduOrgTreeV2VO;
import com.hailiang.saas.model.vo.school.SchoolFullInfoVO;
import com.hailiang.saas.model.vo.school.TchSchoolFullVO;
import com.hailiang.saas.model.vo.staff.ClassTeacherInfoVO;
import com.hailiang.saas.model.vo.subject.SaasDisciplineVO;
import com.hailiang.saas.model.vo.yard.DutyYardResponse;
import com.hailiang.service.*;
import com.hailiang.util.RSAUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/11 15:46
 */
@SpringBootTest
@Slf4j
public class SaasTest {

    @Autowired
    private BasicInfoRemote basicInfoRemote;
    @Autowired
    private SportProjectManager sportProjectManager;
    @Autowired
    private TermLogic termLogic;
    @Autowired
    private DingDingMsgUtil dingDingMsgUtil;
    @Autowired
    private BehaviourRecordService behaviourRecordService;
    @Autowired
    private SaasStudentManager saasStudentManager;
    @Resource
    private SportProjectMapper sportProjectMapper;
    @Resource
    private SportScoreStandardManager sportScoreCalculationManager;
    @Resource
    private SportScoreStandardManager sportScoreStandardManager;
    @Resource
    private SportDataService sportDataService;
    @Resource
    private SportStandardExtraService sportStandardExtraService;
    @Resource
    private SportStandardLogicService sportStandardLogicService;
    @Resource
    private SaasSchoolManager saasSchoolManager;
    @Resource
    private SaasStudentCacheManager saasStudentCacheManager;
    @Resource
    private BasicInfoService basicInfoService;
    @Resource
    private SaasSubjectManager saasSubjectManager;
    @Resource
    private ReportDataService reportDataService;
    @Resource
    private SaasSubjectLogic saasSubjectLogic;
    @Resource
    private SaasClassManager saasClassManager;
    @Resource
    private SaasYardManager saasYardManager;
    @Resource
    private SaaSYardAPI smaSYardAPI;


    @Test
    void listSaasDisciplineVO() {
        SaasDisciplineQueryDTO saasDisciplineQueryDTO = new SaasDisciplineQueryDTO();
        saasDisciplineQueryDTO.setSchoolId(Convert.toLong("7004355789694902272"));
        List<SaasDisciplineVO> saasDisciplineVOS = saasSubjectManager.listSaasDisciplineVO(saasDisciplineQueryDTO);
        log.info(JSONUtil.toJsonStr(saasDisciplineVOS));
    }

    @Test
    void getDiffSexProject() {
        List<SportProjectSexVO> sportProjects = sportProjectMapper.listSportProjectSexByGrade("1", "2001");
        List<SportProjectSexVO> projectSexVOS = sportProjects.stream().filter(s -> !SportProjectEnum.HEIGHT.getCode().equals(s.getProjectCode()) && !SportProjectEnum.WEIGHT.getCode().equals(s.getProjectCode())).collect(Collectors.toList());
        if (CollUtil.isEmpty(projectSexVOS)) {
            sportProjects = sportProjectMapper.listSportProjectSexByGrade(Constant.ZERO.toString(), "2001");
        }
        log.info(JSONUtil.toJsonStr(sportProjects));
    }

    @Test
    void querySchoolById() {
        TchSchoolVO tchSchoolVO = basicInfoRemote.querySchoolById(7004355789694902272L);
        log.info(JSONUtil.toJsonStr(tchSchoolVO));
    }

    @Test
    void getExtraScoreByCondition() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<Integer, Map<String, Map<Range<BigDecimal>, BigDecimal>>> standardExtraRange = sportScoreCalculationManager.getStandardExtraRange("330000", "3001");
        stopWatch.stop();
        System.err.println(stopWatch.getLastTaskTimeMillis());
        BigDecimal pullUp = sportScoreStandardManager.getExtraScoreByCondition(standardExtraRange, 1, "pullUp", BigDecimal.valueOf(7));
        System.err.println(pullUp);
    }

    @Test
    void getScoreByCondition() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<Integer, Map<String, Map<Range<BigDecimal>, SportScoreLevelDTO>>> standardRangeMap = sportScoreCalculationManager.getStandardRangeMap("330000", "3001", "7004354983742541824", "7004355789694902272", "7011709917010010112");
        stopWatch.stop();
        System.err.println(stopWatch.getLastTaskTimeMillis());
        BigDecimal pullUp = sportScoreStandardManager.getScoreByCondition(standardRangeMap, 1, "pullUp", BigDecimal.valueOf(7));
        System.err.println(pullUp);
    }


    @Test
    void queryStudentByClassId() {
        StaffClassDTO staffClassDTO = new StaffClassDTO();
        staffClassDTO.setClassIdList(Arrays.asList(7014142753971154965L));
        List<StudentClassVO> studentClassVOS = basicInfoRemote.queryStudentByClassId(staffClassDTO);
        log.info(JSONUtil.toJsonStr(studentClassVOS));
    }

    @Test
    void queryTermList() {
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(7014142802489044992L);
        termQuery.setCampusSectionId(String.valueOf(7014167473038864384L));
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        log.info(JSONUtil.toJsonStr(termVos));
    }

    @Test
    void sportProjectSave() {
        SportProject sportProject = new SportProject();
        sportProject.setProjectCode("111");
        sportProjectManager.save(sportProject);
        log.info(JSONUtil.toJsonStr(sportProject));
    }

    @Test
    void queryInfo() {
//        SportData info = infoDao.findById("6406a6181d39a5494e82ec28");
//        log.info(JSONUtil.toJsonStr(info));
    }

    @Test
    void queryByClassId() {
        StaffSectionOrGradeQueryDTO dto = new StaffSectionOrGradeQueryDTO();
        dto.setClassId(7011709917152559105L);
        List<StaffFullInfoVO> staffFullInfoVOS = basicInfoRemote.queryByClassId(dto);
        log.info(JSONUtil.toJsonStr(staffFullInfoVOS));
    }

    @Test
    void queryByGradeId() {
        StaffSectionOrGradeQueryDTO dto = new StaffSectionOrGradeQueryDTO();
        dto.setGradeId(7011709917152559104L);
        List<StaffFullInfoVO> staffFullInfoVOS = basicInfoRemote.queryByGradeId(dto);
        log.info(JSONUtil.toJsonStr(staffFullInfoVOS));
    }

    @Test
    void queryByCampusSectionId() {
        StaffSectionOrGradeQueryDTO dto = new StaffSectionOrGradeQueryDTO();
        dto.setCampusSectionId(7011709917148364800L);
        List<StaffFullInfoVO> staffFullInfoVOS = basicInfoRemote.queryByCampusSectionId(dto);
        log.info(JSONUtil.toJsonStr(staffFullInfoVOS));
    }

    @Test
    void queryStaffListBySchoolId() {
        SchoolQueryDTO dto = new SchoolQueryDTO();
        dto.setSchoolId(7004355789694902272L);
        dto.setRoleCodes(Arrays.asList("xs1011"));
        List<UcStaffInfoRoleSchoolVO> ucStaffInfoRoleSchoolVOS = basicInfoRemote.queryStaffListBySchoolId(dto);
        dto.setRoleCodes(Arrays.asList("xs1010"));
        List<UcStaffInfoRoleSchoolVO> ucStaffInfoRoleSchoolVOS1 = basicInfoRemote.queryStaffListBySchoolId(dto);
        log.info(JSONUtil.toJsonStr(ucStaffInfoRoleSchoolVOS));
        log.info(JSONUtil.toJsonStr(ucStaffInfoRoleSchoolVOS1));
    }

    @Test
    void queryStaffByGradeId() {
        GradeByIdQuery dto = new GradeByIdQuery();
        dto.setGradeId(7011709917152559123L);
        List<GradeStaffVO> gradeStaffVOS = basicInfoRemote.queryStaffByGradeId(dto);
        log.info(JSONUtil.toJsonStr(gradeStaffVOS));
    }

    @Test
    void queryTeachersByClassId() {
        TeacherInfoQuery teacherInfoQuery = new TeacherInfoQuery();
        teacherInfoQuery.setClassId(7011709917152559128L);
        List<ClassStaffVO> classStaffVOS = basicInfoRemote.queryTeachersByClassId(teacherInfoQuery);
        log.info(JSONUtil.toJsonStr(classStaffVOS));
    }

    @Test
    void queryClassStudentPageByCondition() {
        UcStudentClassQuery ucStudentClassQuery = new UcStudentClassQuery();
        ucStudentClassQuery.setSchoolId(7004355789694902272L);
        ucStudentClassQuery.setPageSize(5000);
        PageResult<UcStudentClassBffVO> ucStudentClassBffVOPageResult = basicInfoRemote.queryClassStudentPageByCondition(ucStudentClassQuery);
        log.info("记录数size:{}",ucStudentClassBffVOPageResult.getList().size());
    }

    @Test
    void listByStudentIds() {
        StudentByIdQuery studentByIdQuery = new StudentByIdQuery();
        ArrayList<Long> longs = new ArrayList<>();
        longs.add(7040523717968302080L);
        longs.add(7040523614062809088L);
        studentByIdQuery.setStudentIds(longs);
        List<UcStudentClassBffVO> ucStudentClassBffVOS = basicInfoRemote.listByStudentIds(studentByIdQuery);
        log.info(JSONUtil.toJsonStr(ucStudentClassBffVOS));
    }

    @Test
    void queryEducationalOrgTree() {
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(7004355789694902272L);
        eduOrgQueryDTO.setCurrentIdType(SaasCurrentIdTypeEnum.SCHOOL.getCode());
        eduOrgQueryDTO.setEndType(5);
        List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        eduOrgQueryDTO.setClassTypes(Arrays.asList(0, 4));
        List<EduOrgTreeVO> eduOrgTreeVOS1 = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        log.info(JSONUtil.toJsonStr(eduOrgTreeVOS));
        log.info(JSONUtil.toJsonStr(eduOrgTreeVOS1));
    }

    @Test
    void queryEducationalOrgTree1() {
        EduOrgQueryDTO eduOrgQueryDTO = new EduOrgQueryDTO();
        eduOrgQueryDTO.setCurrentId(7011709917152559122L);
        eduOrgQueryDTO.setCurrentIdType(SaasClassOrgTypeEnum.SECTION.getCode());
        eduOrgQueryDTO.setEndType(5);
        eduOrgQueryDTO.setIsTree(0);
        List<EduOrgTreeVO> eduOrgTreeVOS = basicInfoRemote.queryEducationalOrgTree(eduOrgQueryDTO);
        log.info(JSONUtil.toJsonStr(eduOrgTreeVOS));
    }

    @Test
    void queryStaffDataAuth() {
        StaffDataAuthDTO staffDataAuthDTO = new StaffDataAuthDTO();
        staffDataAuthDTO.setSchoolId(7004355789694902272L);
        staffDataAuthDTO.setStaffId(7013008617380401152L);
        staffDataAuthDTO.setCampusId(7011709917010010112L);
        StaffDataAuthVO staffDataAuthVO = basicInfoRemote.queryStaffDataAuth(staffDataAuthDTO);
        log.info(JSONUtil.toJsonStr(staffDataAuthVO));
    }


    @Test
    void queryStaffRoleList() {
        ResRoleQueryDTO resRoleQueryDTO = new ResRoleQueryDTO();
        resRoleQueryDTO.setType(2);
        resRoleQueryDTO.setStaffIds(Arrays.asList(7013008617380401152L));
        resRoleQueryDTO.setId(7004355789694902272L);
        List<ResStaffRoleVO> resStaffRoleVOS = basicInfoRemote.queryStaffRoleList(resRoleQueryDTO);
        log.info(JSONUtil.toJsonStr(resStaffRoleVOS));
    }

    @Test
    void login() {
        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCyER5DTxxXNZ/CvPxcxlQ4y3rngcWAFifs7BYHLP20Gl5grW6s6QM6mrZkvtzyjGSLaMbgIRB+Pika8itXgzF3gai7w8RyQslMLc+jPcqeM0jwLartthevgHo2eDtjzdxx8zEifpXptd619qa6VFJuGB5Xyba4lL+8g0eWasquMwIDAQAB";

        LoginDTO loginDTO = new LoginDTO();
        loginDTO.setEndpoint("WEB_BROWSER");
        loginDTO.setChannel("PASSWORD");
        loginDTO.setPassword(RSAUtil.encrypt("123456", publicKey));
        loginDTO.setUserName("13675862176");
        loginDTO.setAppType("pass");

        LoginVO loginVO = basicInfoRemote.doLogin(loginDTO);

        log.info(JSONUtil.toJsonStr(loginVO));
    }

    @Test
    void authCheck() {
//        log.info(haiManager.authCheck("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VySW5mbyI6eyJyb2xlIjowLCJjbGFzc0lkIjo1MzcyLCJzdHVkZW50SWQiOjgxNzA5LCJ3ZWNoYXRVc2VySWQiOjE0ODY3MH0sImlhdCI6MTY4NTYwMzIxM30.TDqhiLSqZycM1iu207w5fJEVeJTMHYII1ncrxvblHBU"));
    }

    @Test
    void termList() {
        TermQuery termQuery = new TermQuery();
        termQuery.setSchoolId(6943844159176384512L);
        termQuery.setCampusId(6948123159369424896L);
//        termQuery.setCampusSectionId("6948122985269448704");
        DateTime dateTime = DateUtil.parseDate(DateUtil.now());
        List<TermVo> termVos = basicInfoRemote.queryTermList(termQuery);
        // 排除开始时间大于当前时间的学期数据
        List<TermVo> currentTermVos = termVos.stream().filter(d -> DateUtil.compare(dateTime, DateUtil.parseDate(d.getStartTime())) >= 0).collect(Collectors.toList());
        // 按照结束时间倒序
        List<TermVo> termVos1 = CollUtil.reverse(CollUtil.sortByProperty(currentTermVos, "endTime"));
        Map<String, List<TermVo>> termMap = new LinkedHashMap<>();
        for (TermVo d : termVos1) {
            termMap.computeIfAbsent(d.getEndTime(), key -> new ArrayList<>()).add(d);
        }
        List<TermVo> termVos3 = termMap.get(termVos1.get(0).getEndTime());
        // 按照开始时间排序
        List<TermVo> termVos4 = CollUtil.sortByProperty(termVos3, "startTime");
        TermVo termVo = termVos4.get(0);
        System.out.println(JSONUtil.toJsonStr(termVo));
    }

    @Test
    void getTermVo() {
        TermVo termVo = termLogic.getTermVoByCampusId(6943844159176384512L, 6948123159369424896L, 6948122985269448704L, DateUtil.date());
        log.info("{}", JSONUtil.toJsonStr(termVo));
    }

    @Test
    void logout() {
        LogoutDTO logoutDTO = new LogoutDTO();
        logoutDTO.setXt("123");
        logoutDTO.setChannel("FACE");
        logoutDTO.setEndpoint("APP");
        Boolean success = basicInfoRemote.logout(logoutDTO);
        log.info("{}", success);
    }

    @Test
    void pageSchool() {
        PageSchoolDTO pageSchoolDTO = new PageSchoolDTO();
        pageSchoolDTO.setPageNum(1);
        pageSchoolDTO.setPageSize(10000);
        List<PageSchoolVO> pageSchoolVOS = basicInfoRemote.pageSchool(pageSchoolDTO);
        log.info("{}", pageSchoolVOS);
    }

    @Test
    void queryBaseInfo() {
        BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
        baseInfoDTO.setTenantId("7004354983742541824");
        baseInfoDTO.setSaasAppId("7004353930909384704");
        baseInfoDTO.setPlatformType("DINGTALK");
        BaseInfoVO baseInfoVO = basicInfoRemote.queryBaseInfo(baseInfoDTO);
        log.info("{}", baseInfoVO);
    }

    @Test
    void updateStatusNew() {
        ChangeOaStatusReq changeOaStatusReq = new ChangeOaStatusReq();
        changeOaStatusReq.setBusinessId(1L);
        changeOaStatusReq.setStatusBg("123");
        changeOaStatusReq.setStatusValue("123");
        changeOaStatusReq.setTenantId("123");

        dingDingMsgUtil.updateStatusNew(changeOaStatusReq);
    }

    @Test
    void queryStaffBaseClass() {
        StaffClassQuery staffClassQuery = new StaffClassQuery();
        staffClassQuery.setStaffId(7004354984896049152L);
        staffClassQuery.setSchoolId(7004355789694902272L);
        staffClassQuery.setCampusId(7011709917010010112L);
        List<TchClassOpenVO> tchClassOpenVOS = basicInfoRemote.queryStaffBaseClass(staffClassQuery);
        log.info("{}", tchClassOpenVOS);
    }

    @Test
    void listUnderByClassIds() {
        List<EduClassInfoLinkVO> eduClassInfoLinkVOS = basicInfoRemote.listUnderByClassIds(Arrays.asList(Convert.toLong("6991685311369007109")));
        System.err.println(JSONUtil.toJsonStr(eduClassInfoLinkVOS));
    }

    @Test
    void queryStudentScore() {
        QueryBehaviourScoreDTO dto = new QueryBehaviourScoreDTO();
        dto.setStartTime(DateUtil.offsetMonth(DateUtil.date(), -3));
        dto.setEndTime(DateUtil.date());
        dto.setBusinessType(0);
        dto.setBusinessIds(Arrays.asList("7087454705126084608"));
        List<ReportModuleScoreVO> pairs = behaviourRecordService.queryStudentScore(dto);
        System.err.println(JSONUtil.toJsonStr(pairs));
    }

    @Test
    void queryStudentScoreStatistics() {
        QueryBehaviourScoreDTO dto = new QueryBehaviourScoreDTO();
        dto.setStartTime(DateUtil.date(1690992000000L));
        dto.setEndTime(DateUtil.date(1706716799999L));
        dto.setBusinessType(0);
        dto.setBusinessIds(Arrays.asList("7173948417179582464",
                "7138108591805190144",
                "7118203251332653056",
                "7118167300984184832",
                "7117865451018166272",
                "7117864825718743040",
                "7117854989538992128",
                "7117853695222915072",
                "7116404645676097536",
                "7116400210589687808",
                "7116399094565085184",
                "7111280995130077184",
                "7110110923283951616",
                "7040523717968302080",
                "7040523614062809088"
        ));
        dto.setSubjectCodes(Arrays.asList("xk1001", "xk1002"));
        List<ReportModuleScoreStatisticsVO> vos = behaviourRecordService.queryStudentScoreStatistics(dto);
        System.err.println(JSONUtil.toJsonStr(vos));
    }

    @Test
    void listHistoryTeacher() {
        SchoolYearTeacherReqDTO schoolYearTeacherReqDTO = new SchoolYearTeacherReqDTO();
//        schoolYearTeacherReqDTO.setGradeId(7011709917152559123L);
        schoolYearTeacherReqDTO.setSchoolId(7004355789694902272L);
        schoolYearTeacherReqDTO.setCampusId(7011709917010010112L);
        schoolYearTeacherReqDTO.setHisClassId(7085947406289125377L);
        schoolYearTeacherReqDTO.setSchoolYear("2022-2023");
        List<HisTeacherRelationVO> hisTeacherRelationVOS = saasStudentManager.listHistoryTeacher(schoolYearTeacherReqDTO);
        log.info(JSONUtil.toJsonStr(hisTeacherRelationVOS));
    }

    @Test
    void listClassTeachRelation() {
        ClassSubjectRelationDTO schoolYearTeacherReqDTO = new ClassSubjectRelationDTO();
        schoolYearTeacherReqDTO.setSchoolId(7004355789694902272L);
        schoolYearTeacherReqDTO.setCampusId(7011709917010010112L);
        schoolYearTeacherReqDTO.setClassIdList(Arrays.asList(7011709917152559124L));
        List<ClassSubjectRelationVO> classSubjectRelationVOS = saasStudentManager.listClassTeachRelation(schoolYearTeacherReqDTO);
        log.info(JSONUtil.toJsonStr(classSubjectRelationVOS));
    }

    /**
     * 批量处理所有标准分和附加分
     */
    @Test
    void handleScore() {
        String r = sportDataService.batchHandleAllScore();
        System.out.println(r);
    }

    @Test
    void getProjectMinOrMaxValue() {
        SportStandardProjectDTO sportStandardProjectDTO = new SportStandardProjectDTO();
        sportStandardProjectDTO.setProjectCode("jump");
        sportStandardProjectDTO.setSex(1);
        sportStandardProjectDTO.setGradeCode("3001");
        SportStandardProjectDTO res = sportProjectManager.getProjectMinOrMaxValue("330000", sportStandardProjectDTO.getProjectCode(), sportStandardProjectDTO.getGradeCode(), sportStandardProjectDTO.getSex());
        BigDecimal extraScore = new BigDecimal(0);
        BigDecimal currentScore = BigDecimal.valueOf(180);
        if (res.getMinValue().equals("0")) {
            BigDecimal extraValue = currentScore.subtract(new BigDecimal(res.getMaxValue()));
            if (extraValue.compareTo(BigDecimal.ZERO) < 0) {
                System.out.println(extraValue);
            }
        }
        if (StrUtil.isEmpty(res.getMaxValue())) {
            BigDecimal extraValue = currentScore.subtract(new BigDecimal(res.getMinValue()));
            if (extraValue.compareTo(BigDecimal.ZERO) > 0) {
                System.out.println(extraValue);
            }
        }
    }

    @Test
    void getLevelByCondition() {
        String level = sportScoreStandardManager.getLevelByCondition("330000", "2001", "7004354983742541824", "7004355789694902272", "7011709917010010112", 1, "sitAndReach", BigDecimal.valueOf(12));
        System.err.println(level);
    }

    @Test
    void querySportExtraScoreHeader() {
        SportStandardQuery query = new SportStandardQuery();
        query.setSex(2);
        query.setProjectCode("sitUp");
        List<SportExtraScoreVO> res = sportStandardExtraService.querySportExtraScoreHeader(query);
        System.out.println(res);
    }

    @Test
    void querySportExtraScoreData() {
        SportStandardQuery query = new SportStandardQuery();
        query.setSex(1);
        query.setProjectCode("skipRope");
        SportExtraScoreMapVO sportExtraScoreMapVO = sportStandardExtraService.querySportExtraScoreData(query);
        System.out.println(sportExtraScoreMapVO);
    }


    @Test
    void querySportStandardData() {
        SportStandardQuery query = new SportStandardQuery();
        query.setSex(1);
        query.setProjectCode("BMI");
        SportStandardVO dataVOS = sportStandardLogicService.querySportStandardData(query);
        System.out.println(dataVOS);
    }


    @Test
    void queryHistoryEducationalOrgTree() {
        EduOrgQueryV2DTO eduOrgQueryV2DTO = new EduOrgQueryV2DTO();
        eduOrgQueryV2DTO.setCurrentId(7004355789694902272L);
        eduOrgQueryV2DTO.setSchoolYear("2022-2023");
        eduOrgQueryV2DTO.setCurrentIdType(SaasCurrentIdTypeEnum.SCHOOL.getCode());
        eduOrgQueryV2DTO.setEndType(SaasEndTypeEnum.CLASS.getCode());
        eduOrgQueryV2DTO.setNeedStudentNum(1);
        List<EduOrgTreeV2VO> eduOrgTreeV2VOS = saasSchoolManager.queryHistoryEducationalOrgTree(eduOrgQueryV2DTO);
        log.info(JSONUtil.toJsonStr(eduOrgTreeV2VOS));
    }

    @Test
    void queryHistoryStudentNum() {
        StudentNumQueryDTO studentNumQueryDTO = new StudentNumQueryDTO();
        studentNumQueryDTO.setSchoolIds(Collections.singletonList(7014142802489044992L));
        studentNumQueryDTO.setSchoolYear("2023-2024");
        studentNumQueryDTO.setStudentStatus(Constant.ZERO.toString());
        List<SaasStudentNumVO> studentNumVOList = saasStudentManager.queryHistoryStudentNum(studentNumQueryDTO);
        log.info(JSONUtil.toJsonStr(studentNumVOList));
    }

    @Test
    void queryCacheHistoryStudentNum() {
        StudentNumQueryDTO studentNumQueryDTO = new StudentNumQueryDTO();
        studentNumQueryDTO.setSchoolIds(Arrays.asList(7004355789694902272L, 143L));
        studentNumQueryDTO.setSchoolYear("2022-2023");
        List<SaasStudentNumVO> studentNumVOList = saasStudentCacheManager.queryHistoryStudentNum(studentNumQueryDTO);
        log.info(JSONUtil.toJsonStr(studentNumVOList));
    }

    @Test
    void querySchoolByAppId() {
        AppIdBaseReq dto = new AppIdBaseReq();
        List<SchoolFullInfoVO> schoolFullInfoVOS = saasSchoolManager.querySchoolByAppId(dto);
        log.info(JSONUtil.toJsonStr(schoolFullInfoVOS));
    }

    @Test
    void queryBySchoolIds() {
        TchSchoolFullQuery dto = new TchSchoolFullQuery();
        dto.setSchoolIds(Collections.singleton(7004355789694902272L));
        PageResult<TchSchoolFullVO> tchSchoolFullVOPage = saasSchoolManager.queryBySchoolIds(dto);
        log.info(JSONUtil.toJsonStr(tchSchoolFullVOPage));
    }

    @Test
    void queryStudentPage() {
        EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
        eduStudentPageQueryDTO.setSchoolId(7014142802489044992L);
        eduStudentPageQueryDTO.setClassId(7139576091151716352L);
//            eduStudentPageQueryDTO.setGradeId(StudentInfoTypeEnum.GRADE.getCode().equals(studentInfo.getType()) ? Long.parseLong(studentInfo.getId()) : null);
//            eduStudentPageQueryDTO.setCampusSectionId(StudentInfoTypeEnum.SECTION.getCode().equals(studentInfo.getType()) ? Long.parseLong(studentInfo.getId()) : null);
        eduStudentPageQueryDTO.setPageSize(Integer.MAX_VALUE);
        List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoService.queryStudentPage(eduStudentPageQueryDTO);
        log.info(JSONUtil.toJsonStr(eduStudentInfoVOS));
    }

    @Test
    void queryCampusListBySchoolId() {
        SchoolQueryDTO schoolQueryDTO = new SchoolQueryDTO();
        schoolQueryDTO.setSchoolId(7014142802489044992L);
        List<TchCampusInfoVO> tchCampusInfoVOList = basicInfoRemote.queryCampusListBySchoolId(schoolQueryDTO);
        log.info(JSONUtil.toJsonStr(tchCampusInfoVOList));
    }


    @Test
    void queryClassListByStudentIds() {
        EduStudentClassQueryDTO eduStudentClassQueryDTO = new EduStudentClassQueryDTO();
        List<String> classTypes = new ArrayList<>();
        classTypes.add(SaasClassTypeEnum.XINGZHENG.getCode());
        eduStudentClassQueryDTO.setClassTypes(classTypes);
        eduStudentClassQueryDTO.setStudentIds(Collections.emptyList());
        eduStudentClassQueryDTO.setGraduationStatus("0");
        List<EduStudentClassVO> eduStudentClassVOList = basicInfoRemote.queryClassListByStudentIds(eduStudentClassQueryDTO);
        System.err.println(eduStudentClassVOList);
    }


    @Test
    void queryClassInfoList() {
        EduClassQueryDTO eduClassQueryDTO = new EduClassQueryDTO();
//        eduClassQueryDTO.setGradeId(7011709917152559123L);
        eduClassQueryDTO.setSchoolId(7004355789694902272L);
        List<EduClassInfoVO> eduClassInfoVOS = basicInfoRemote.queryClassInfoList(eduClassQueryDTO);
        System.err.println(eduClassInfoVOS);
    }


    @Test
    void listUnderByNameNo() {
        List<StaffNOVO> staffNOVOS = basicInfoRemote.listUnderByNameNo("abc1234567", "蛋总");
        System.err.println(staffNOVOS);
    }

    @Test
    void testInit(){
//        reportDataService.initSubjectInfo();
    }


    @Test
    void studentDetailV2() {
        StudentDTO studentDTO = new StudentDTO();
        studentDTO.setStudentIds(Arrays.asList(7072806076147093504L));
        List<StudentInfoVO> studentInfoVOS = saasStudentManager.studentDetailV2(studentDTO);
        log.info(JSONUtil.toJsonStr(studentInfoVOS));
    }

    @Test
    void testSubject(){
        SendTaskModel taskModel = new SendTaskModel();
        taskModel.setContent(JSONUtil.parseArray("[{\n" +
                "\t\"subjectIds\": [\n" +
                "\t\t\"7204841852438491136\",\n" +
                "\t\t\"7204840973937324032\"\n" +
                "\t],\n" +
                "\t\"schoolId\": \"7004355789694902272\",\n" +
                "\t\"tenantId\": \"7004354983742541824\"\n" +
                "}]"));
        taskModel.setTaskId("SAAS1805956112343179264");
        taskModel.setEventCode("saas-subject-delete");
        taskModel.setUpstream("SAAS系统");
        taskModel.setDataType("batchDelete");
        taskModel.setEventType("科目");
        taskModel.setEventName("科目批量删除");
        saasSubjectLogic.subjectDeletedHandle(taskModel);
    }

    @Test
    void testQueryClassTeacher(){
        List<Long> classIds = Collections.singletonList(7087454599077400576L);
        List<ClassTeacherInfoVO> classTeacherInfoVOS = saasClassManager.queryClassTeacherInfo(classIds);
        System.out.println(classTeacherInfoVOS);
    }

    @Test
    void testQueryStudentPage(){
        EduStudentPageQueryDTO eduStudentPageQueryDTO = new EduStudentPageQueryDTO();
        eduStudentPageQueryDTO.setSchoolId(7004355789694902272L);
        eduStudentPageQueryDTO.setStudentStatus(null);
        List<EduStudentInfoVO> eduStudentInfoVOS = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO);
        List<Long> studentIds = eduStudentInfoVOS.stream().map(EduStudentInfoVO::getId).collect(Collectors.toList());
        EduStudentPageQueryDTO eduStudentPageQueryDTO2 = new EduStudentPageQueryDTO();
        eduStudentPageQueryDTO2.setStudentIds(studentIds);
        eduStudentPageQueryDTO2.setSchoolId(7004355789694902272L);
        eduStudentPageQueryDTO2.setNeedPage(true);
        List<EduStudentInfoVO> eduStudentInfoVOS1 = basicInfoRemote.queryStudentPage(eduStudentPageQueryDTO2);
        System.out.println(eduStudentInfoVOS1.size() == eduStudentInfoVOS.size());
        System.out.println(eduStudentInfoVOS.size());
    }

    @Test
    void queryStaffListBySchool(){
        SchoolStaffQueryDTO schoolStaffQueryDTO = new SchoolStaffQueryDTO();
        schoolStaffQueryDTO.setType(2);
        schoolStaffQueryDTO.setSchoolId(7004355789694902272L);
        List<StaffVO> staffVOS = basicInfoRemote.queryStaffListBySchool(schoolStaffQueryDTO);
        System.out.println(staffVOS);
    }


    @Test
    void listDutyYardVOs(){
        DutyYardRequest dutyYardRequest = new DutyYardRequest();
        dutyYardRequest.setIdList(Arrays.asList(7083680304329080837L));
        List<DutyYardResponse> dutyYardResponses = saasYardManager.listDutyYards(dutyYardRequest);
        System.out.println(dutyYardResponses);
    }

    @Test
    void querySchoolYardTree(){
        YardTreeQueryDTO yardTreeQueryDTO = new YardTreeQueryDTO();
        yardTreeQueryDTO.setCurrentId(7011709917010010112L);
        yardTreeQueryDTO.setStartViewType(2);
        yardTreeQueryDTO.setEndViewType(6);
        yardTreeQueryDTO.setBuildingTypes((SaaSBuildingTypeEnum.queryDormitoryTypeList()));
        CommonResult<List<YardTreeVO>> listCommonResult = smaSYardAPI.querySchoolYardTree(yardTreeQueryDTO);
        List<YardTreeVO> yardTreeVOS = listCommonResult.getData();
        System.out.println(yardTreeVOS);
    }
}
