@Library('common-shared-library@evaluate') _

def map = [:]
    // 定义项目构建运行的命名空间
    map.put('NAMESPACE','evaluate')
    // 需要修改此处，定义微服务工程名称
    map.put('PROJECT_NAME','evaluate-h5')
    //模块名
    map.put('MOD_NAME','evaluate-h5')
    // 定义服务配置
    map.put('PROFILES','aliprod')
    // 定义jvm
    map.put('JVM_OPTIONS','-Xmx2g -Xms2g -javaagent:/opt/app/transmittable-thread-local-2.12.2.jar -javaagent:/opt/app/opentelemetry-javaagent.jar -Dfile.encoding=utf-8 -DNACOS_USERNAME=evaluate -DNACOS_PASSWORD=P2VoqfSYiv -DNACOS_HOST=************ -DNACOS_PORT=8848 -DNACOS_NAMESPACE=a1ddb2cc-05b3-43cf-9359-6c9946a3955c -XX:NativeMemoryTracking=summary -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/opt/app/evaluate/logs/evaluate-h5/java_heapdump.hprof')
    // 定义端口
    map.put('PORT','8801')
    // 定义项目git地址
    map.put('SrcUrl','http://*********/edu/evaluate.git')
    // 确定镜像名称 [格式: 项目名/服务名]
    map.put('IMAGES_ORG','hlyz')
    // 配置pod资源分配
    map.put('limitscpu','2')
    map.put('limitsmem','4Gi')
    map.put('requestscpu','100m')
    map.put('requestsmem','4Gi') 
  
deploy_aliack_cluster(map)
